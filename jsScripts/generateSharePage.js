"use strict";
import PdfPrinter from "pdfmake";
import * as fs from 'node:fs';

// 2024-12-24: I don't want to touch this file outside the import statements, so keeping existing naming
const pdfMake = PdfPrinter

var mainInd = process.argv[2].indexOf("vault")
var finalInd = process.argv[2].indexOf("DESCRIPTOR");
var mainDir = process.argv[2].slice(0,mainInd);
var workingDir = process.argv[2].slice(0,finalInd);

var jsonString = fs.readFileSync(process.argv[2]).toString();
var json = JSON.parse(jsonString);

var isPatient = false;
var isProvider = false;
switch(process.argv[3]) {
  case "patient":
    isPatient = true;
    break;
  case "provider":
    isProvider = true;
    break;
  default:
    throw 'Incorrect input for arg 3, should be "patient" or "provider"';
}

var isPrint = false;
var isFax = false;
switch(process.argv[4]) {
  case "print":
    isPrint = true;
    break;
  case "fax":
    isFax = true;
    break;
  default:
    throw 'Incorrect input for arg 4, should be "print" or "fax"';
}

if (isFax) {
  var faxAttn = json.faxAttn;
  if (faxAttn.length > 57) {
    faxAttn = faxAttn.slice(0, 54) + "...";
  }
  var faxSubject = json.faxSubject;
  if (faxSubject.length > 57) {
    faxSubject = faxSubject.slice(0, 54) + "...";
  }
  var faxFrom = json.faxFrom;
  if (faxFrom.length > 57) {
    faxFrom = faxFrom.slice(0, 54) + "...";
  }
}

if (isProvider) {
  var institutionLogoUrl = json.institutionLogoUrl;
  var institutionName = json.institutionName;
  if (institutionName.length > 58) {
    institutionName = institutionName.slice(0, 55) + "...";
  }
}

var isFrench = false;
switch(process.argv[5]) {
  case "en":
    isFrench = false;
    break;
  case "en-US":
    isFrench = false;
    break;
  case "fr-CA":
    isFrench = true;
    break;
  case "fr":
    isFrench = true;
    break;
  case "es-US":
    isFrench = false;
    break;
  case "es":
    isFrench = false;
    break;
  default:
    throw 'Incorrect input for arg 5, should be "en-US", "fr-CA" or "es-US" but got ' + process.argv[5];
}

var patientName = json.patientName;
if (patientName.length > 58) {
  patientName = patientName.slice(0, 55) + "...";
}
var dob = json.dob;
var securityCode = json.securityCode;

var expiryDate = json.expiryDate;
var frenchExpiryDate = new Date(expiryDate).toLocaleDateString('fr-CA', {month: 'short', day: '2-digit', year: 'numeric'})
var expiryTime = json.expiryTime;
var validityPeriod = json.validityPeriod;

var patientPrintContent = [
  {"text":patientName, "style":"textbody", "margin":[115,120,0,0], "alignment":"left"},
  {"text":dob, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[73,158,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[73,37,0,0], "alignment": "left"}
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {"text": expiryDate, "style": "textbody", "bold": "true", "margin":[376,-38,0,0], "alignment": "left"},
  {"text": "This Imaging will be available till " + expiryTime, "style": "small", "margin":[40,370,0,0], "alignment": "left"},
  {"text": "(Option to extend by 9 months after access)", "style": "small", "margin":[40,0,0,0], "alignment": "left"}
]

var patientPrintContentFrench = [
  [
    {"text":patientName, "style":"textbody", "margin":[115,120,0,0], "alignment":"left"},
    {"text":dob, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
    {
      "columns":[
        [
          {"text":securityCode, "style": "bigInfo", "margin":[60,170,0,0], "alignment": "left"},
          {"text":dob, "style": "bigInfo", "margin":[60,37,0,0], "alignment": "left"}
        ],
        [
        ]
      ],
      "alignment": "justify"
    },
    {"text": frenchExpiryDate, "style": "textbody", "bold": "true", "margin":[390,-53,0,0], "alignment": "left"},
    {"text": "Cette imagerie sera disponible jusqu'à " + expiryTime, "style": "small", "margin":[40,370,0,0], "alignment": "left"},
    {"text": "(Possibilité de prolonger de 9 mois après l'accès)", "style": "small", "margin":[40,0,0,0], "alignment": "left"}
  ]
]

var patientFaxContent = [
  {"text":faxAttn, "style":"deliveryInfo", "margin":[95,85,0,0], "alignment":"left"},
  {"text":"Transfer from " + json.patientName, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":faxFrom, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":patientName, "style":"deliveryInfo",  "margin":[130,25,0,0], "alignment":"left"},
  {"text":dob, "style":["deliveryInfo", "institutionBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[73,167,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[73,37,0,0], "alignment": "left"}
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {"text": expiryDate, "style": "textbody", "bold": "true", "margin":[376,-40,0,0], "alignment": "left"},
  {
    "text":
    [
      {"text": "This Imaging will be available till " + expiryTime + " EST", "bold": "true"},
      "(Option to extend by 9 months after access)"
    ],
      "style": "small", "margin":[60,330,0,0], "alignment": "left"
  }
]

var patientFaxContentFrench = [
  {"text":faxAttn, "style":"deliveryInfo", "margin":[95,97,0,0], "alignment":"left"},
  {"text":"Transfer from " + json.patientName, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":faxFrom, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":patientName, "style":"deliveryInfo",  "margin":[108,22,0,0], "alignment":"left"},
  {"text":dob, "style":["deliveryInfo", "institutionBlockNext"], "margin":[108,3,0,0], "alignment":"left"},
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[57,170,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[57,37,0,0], "alignment": "left"}
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {"text": frenchExpiryDate, "style": "textbody", "bold": "true", "margin":[390,-52,0,0], "alignment": "left"},
  {
    "text":
    [
      {"text": "Cette imagerie sera disponible jusqu'à " + expiryTime + " EST", "bold": "true"},
      "(Possibilité de prolonger de 9 mois après l'accès)"
    ],
      "style": "small", "margin":[40,320,0,0], "alignment": "left"
  }
]


var providerPrintContent = [
  {
    "image": institutionLogoUrl,
    "fit":[200, 25]
  },
  {"text":institutionName,"style":"textbody", "margin":[115,55,0,0], "alignment":"left"},
  {"text":patientName, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
  {"text":dob, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[73,228,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[73,37,0,0], "alignment": "left"}
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {"text": expiryDate, "style": "textbody", "bold": "true", "margin":[376,-39,0,0], "alignment": "left"},
  {"text": "This Imaging will be available till " + expiryTime, "style": "small", "bold": "true", "margin":[306,320,0,0], "alignment": "left"},
  {"text": "(Option to extend by 9 months after access)", "style": "small", "margin":[306,0,0,0], "alignment": "left"}
]

var providerPrintContentFrench = [
  {
    "image": institutionLogoUrl,
    "fit":[200, 25]
  },
  {"text":institutionName,"style":"textbody", "margin":[115,70,0,0], "alignment":"left"},
  {"text":patientName, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
  {"text":dob, "style":["textbody", "institutionBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[58,230,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[58,37,0,0], "alignment": "left"}
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {"text": frenchExpiryDate, "style": "textbody", "bold": "true", "margin":[390,-52,0,0], "alignment": "left"},
  {"text": "Cette imagerie sera disponible jusqu'à " + expiryTime, "style": "small", "bold": "true", "margin":[306,315,0,0], "alignment": "left"},
  {"text": "(Possibilité de prolonger de 9 mois après l'accès)", "style": "small", "margin":[306,0,0,0], "alignment": "left"}
]

var providerFaxContent = [
  {
    "image": institutionLogoUrl,
    "fit":[200, 25]
  },
  {"text":faxAttn, "style":"deliveryInfo", "margin":[115,53,0,0], "alignment":"left"},
  {"text":faxSubject, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":faxFrom, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {
          "table": {
            "widths": [150],
            "body": [
              [{"text":institutionName,"style":"small", "alignment":"left"}]
            ]
          },
          "layout": 'noBorders',
          "absolutePosition": {"x": 150, "y": 165}
        },
        {
          "table": {
            "widths": [150],
            "body": [
              [{"text":patientName,"style":"small", "alignment":"left"}]
            ]
          },
          "layout": 'noBorders',
          "absolutePosition": {"x": 150, "y": 192}
        },
        {"text":dob, "style":"small", "alignment":"left", "absolutePosition": {x: 150, y: 222}},
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[73,147,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[73,37,0,0], "alignment": "left"}
      ],
      []
    ],
    "alignment": "justify"
  },
  {"text": expiryDate, "style": "textbody", "bold": "true", "margin":[376,-40,0,0], "alignment": "left"},
  {"text": "This Imaging will be available till " + expiryTime, "style": "small", "bold": "true", "margin":[306,312,0,0], "alignment": "left"},
  {"text": "(Option to extend by 9 months after access)", "style": "small", "margin":[306,0,0,0], "alignment": "left"}
]

var providerFaxContentFrench = [
  {
    "image": institutionLogoUrl,
    "fit":[200, 25]
  },
  {"text":faxAttn, "style":"deliveryInfo", "margin":[115,53,0,0], "alignment":"left"},
  {"text":faxSubject, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {"text":faxFrom, "style":["deliveryInfo", "deliveryBlockNext"], "alignment":"left"},
  {
    "columns":[
      [
        {
          "table": {
            "widths": [150],
            "body": [
              [{"text":institutionName,"style":"small", "alignment":"left"}]
            ]
          },
          "layout": 'noBorders',
          "absolutePosition": {"x": 150, "y": 165}
        },
        {
          "table": {
            "widths": [150],
            "body": [
              [{"text":patientName,"style":"small", "alignment":"left"}]
            ]
          },
          "layout": 'noBorders',
          "absolutePosition": {"x": 150, "y": 192}
        },
        {"text":dob, "style":"small", "alignment":"left", "absolutePosition": {x: 150, y: 224}},
      ],
      [
      ]
    ],
    "alignment": "justify"
  },
  {
    "columns":[
      [
        {"text":securityCode, "style": "bigInfo", "margin":[57,159,0,0], "alignment": "left"},
        {"text":dob, "style": "bigInfo", "margin":[57,37,0,0], "alignment": "left"}
      ],
      []
    ],
    "alignment": "justify"
  },
  {"text": frenchExpiryDate, "style": "textbody", "bold": "true", "margin":[390,-52,0,0], "alignment": "left"},
  {"text": "Cette imagerie sera disponible jusqu'à " + expiryTime, "style": "small", "bold": "true", "margin":[306,312,0,0], "alignment": "left"},
  {"text": "(Possibilité de prolonger de 9 mois après l'accès)", "style": "small", "margin":[306,0,0,0], "alignment": "left"}
]

var recordList;
var recordListFrench;
if (isPatient) {
  if (isPrint) {
    if (isFrench) {
      recordListFrench = patientPrintContentFrench[0][2].columns[1];
    }
    recordList = patientPrintContent[2].columns[1];
  } else if (isFax) {
    if (isFrench) {
      recordListFrench = patientFaxContentFrench[5].columns[1];
    } 
    recordList = patientFaxContent[5].columns[1];
    
  }
} else if (isProvider) {
  if (isPrint) {
    if (isFrench) {
      recordListFrench = providerPrintContentFrench[4].columns[1];
    }
    recordList = providerPrintContent[4].columns[1];
  } else if (isFax) {
    if (isFrench) {
      recordListFrench = providerFaxContentFrench[5].columns[1];
    }
    recordList = providerFaxContent[5].columns[1];
  }
}

for (var i = 0; i < json.examList.length; i++) {
  var record = json.examList[i];
  var recordTitle = record.modality + ": " + record.description
  if (recordTitle.length > 35) {
    recordTitle = recordTitle.slice(0, 32) + "..."
  }
  if (i == 0) {
    recordList.push({"text":"- " + recordTitle, "style":["textbody", "FirstRecordItem"], "alignment": "left"});
    if (isFrench) {
      recordListFrench.push({"text":"- " + recordTitle, "style":["textbody", "FirstRecordItem"], "alignment": "left"});
    }
  } else {
    recordList.push({"text":"- " + recordTitle, "style":"recordTitle", "alignment": "left"});
    if (isFrench) {
      recordListFrench.push({"text":"- " + recordTitle, "style":"recordTitle", "alignment": "left"});
    }
  }
  recordList.push({"text":record.date, "style": "dateSmall", "alignment": "left"});
  if (isFrench) {
    var frenchDate = new Date(record.date).toLocaleDateString('fr-CA', {month: 'short', day: '2-digit', year: 'numeric'})
    recordListFrench.push({"text":frenchDate, "style": "dateSmall", "alignment": "left"});
  }
}
if (json.totalNum > 3) {
  recordList.push({"text":"+ " + (json.totalNum - 3).toString() + " more records", "style": "moreRecords", "alignment": "left"});
  if (isFrench) {
    recordListFrench.push({"text":"+ " + (json.totalNum - 3).toString() + " more records", "style": "moreRecords", "alignment": "left"});
  }
}

//Adding english pages to French Shares
providerFaxContentFrench.push(...providerFaxContent)
providerPrintContentFrench.push(...providerPrintContent)
patientFaxContentFrench.push({ "text": "", "pageBreak": 'before' })
patientFaxContentFrench.push(...patientFaxContent)
patientPrintContentFrench.push({ "text": "", "pageBreak": 'before' })
patientPrintContentFrench.push(...patientPrintContent)

var patientPrintDescriptor = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": patientPrintContent,
  "background": [
    {
      "image":"assets/accessPageBackgrounds/printPdfBgPatients.png",
      "width":612
    }
  ],
  "styles": {
    "defaultStyle":{"columnGap":10},
    "FirstRecordItem":{"margin":[42,75,0,0]},
    "institutionBlockNext":{"margin":[115,3,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var patientPrintDescriptorFrench = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": patientPrintContentFrench,
  "background": function(currentPage) {
    if (`${currentPage}` == 1) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/fr-CA/printPdfBgPatients.png',
              "width":612
          }
      ]
    }
    if (`${currentPage}` == 2) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/printPdfBgPatients.png',
              "width": 612
          }
      ]
    }
  },
  "styles": {
    "defaultStyle":{"columnGap":10},
    "FirstRecordItem":{"margin":[42,75,0,0]},
    "institutionBlockNext":{"margin":[115,3,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var patientFaxDescriptor = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": patientFaxContent,
  "background": [
    {
      "image":"assets/accessPageBackgrounds/faxPdfBgPatients.png",
      "width":612
    }
  ],
  "styles": {
    "defaultStyle":{"columnGap":10},
    "deliveryBlockNext":{"margin":[95,2,0,0]},
    "deliveryInfo":{"fontSize":12, "bold":false},
    "FirstRecordItem":{"margin":[42,81,0,0]},
    "institutionBlockNext":{"margin":[130,2,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var patientFaxDescriptorFrench = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": patientFaxContentFrench,
  "background": function(currentPage) {
    if (`${currentPage}` == 1) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/fr-CA/faxPdfBgPatients.png',
              "width": 612
          }
      ]
    }
    if (`${currentPage}` == 2) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/faxPdfBgPatients.png',
              "width": 612
          }
      ]
    }
  },
  "styles": {
    "defaultStyle":{"columnGap":10},
    "deliveryBlockNext":{"margin":[95,2,0,0]},
    "deliveryInfo":{"fontSize":12, "bold":false},
    "FirstRecordItem":{"margin":[42,81,0,0]},
    "institutionBlockNext":{"margin":[130,2,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var providerPrintDescriptor = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": providerPrintContent,
  "background": [
    {
      "image":"assets/accessPageBackgrounds/printPdfBgProviders.png",
      "width":612
    }
  ],
  "styles": {
    "defaultStyle":{"columnGap":10},
    "FirstRecordItem":{"margin":[42,142,0,0]},
    "institutionBlockNext":{"margin":[115,3,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var providerPrintDescriptorFrench = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": providerPrintContentFrench,
  "background": function(currentPage) {
    if (`${currentPage}` == 1) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/fr-CA/printPdfBgProviders.png',
              "width": 612
          }
      ]
    }
    if (`${currentPage}` == 2) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/printPdfBgProviders.png',
              "width": 612
          }
      ]
    }
  },
  "styles": {
    "defaultStyle":{"columnGap":10},
    "FirstRecordItem":{"margin":[42,142,0,0]},
    "institutionBlockNext":{"margin":[115,3,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var providerFaxDescriptor = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": providerFaxContent,
  "background": [
    {
      "image":"assets/accessPageBackgrounds/faxPdfBgProviders.png",
      "width":612
    }
  ],
  "styles": {
    "defaultStyle":{"columnGap":10},
    "deliveryBlockNext":{"margin":[115,2,0,0]},
    "deliveryInfo":{"fontSize":12, "bold":false},
    "institutionBlockNext":{"margin":[115,2,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "FirstRecordItem":{"margin":[45,60,0,0]},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

var providerFaxDescriptorFrench = {
  "pageSize":"letter",
  "pageMargins":[30,25],
  "content": providerFaxContentFrench,
  "background": function(currentPage) {
    if (`${currentPage}` == 1) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/fr-CA/faxPdfBgProviders.png',
              "width": 612
          }
      ]
    }
    if (`${currentPage}` == 2) { 
      return [
          {
              "image": 'assets/accessPageBackgrounds/faxPdfBgProviders.png',
              "width": 612
          }
      ]
    }
  },
  "styles": {
    "defaultStyle":{"columnGap":10},
    "deliveryBlockNext":{"margin":[115,2,0,0]},
    "deliveryInfo":{"fontSize":12, "bold":false},
    "institutionBlockNext":{"margin":[115,2,0,0]},
    "textbody":{"fontSize":10, "bold":false},
    "small":{"fontSize":8},
    "topheader":{"fontSize":25,"bold":false,"margin":[0,30,0,20],"color":"white"},
    "bigInfo":{"fontSize":16,"bold":false,"margin":[0,0,0,0],"font":"Roboto"},
    "FirstRecordItem":{"margin":[45,60,0,0]},
    "recordTitle":{"fontSize":10, "margin":[45,8,0,0]},
    "dateSmall":{"fontSize":8,"margin":[45,0,0,0]},
    "moreRecords":{"fontSize":8,"margin":[45,8,0,0]}
  }
};

function makeProviderFaxNote(noteContent) {
  note = [{
    "canvas": [
      {
        "type": 'rect',
        "x": 5,
        "y": 8,
        "w": 275,
        "h": 75,
        "r": 4,
        "lineColor": (noteContent) ? "gray" : "white",
        "hLineWidth": 0.1,
        "vLineWidth": 0.1
      },
    ]
  }];
  if (noteContent) {
    note.push({
      "text": "Note",
      "style": {
        "fontSize":6,
        "color": "gray"
      },
      "absolutePosition": {"x": 320, "y": 160}
    });

    if (noteContent.length > 263) {
      noteContent = noteContent.slice(0, 260) + "..."
    }
    note.push({
      "text": noteContent,
      "style": "textbody",
      "absolutePosition": {"x": 320, "y": 168}
    });
    return [note]
  }
}

if (isProvider && isFax) {
  var noteContent = faxNote;
  if (isFrench) {
    //Adding note content to English Page
    providerFaxDescriptorFrench.content[13].columns[1] = makeProviderFaxNote(noteContent)
    //Adding note content to French page
    providerFaxDescriptorFrench.content[4].columns[1] = makeProviderFaxNote(noteContent)
  } else {
    providerFaxDescriptor.content[4].columns[1] = makeProviderFaxNote(noteContent)
  }
}

var fonts = {
    Roboto: {
        normal: mainDir+'assets/fonts/roboto/Roboto-Regular.ttf',
        bold: mainDir+'assets/fonts/roboto/Roboto-Medium.ttf',
        italics: mainDir+'assets/fonts/roboto/Roboto-Italic.ttf',
        bolditalics: mainDir+'assets/fonts/roboto/Roboto-MediumItalic.ttf'
    },
    Opensans: {
        bold: mainDir+'assets/fonts/opensans/open-sans.bold.ttf'
    }
};

var printer = new pdfMake(fonts);
var pdfDoc;
if (isPatient) {
  if (isPrint) {
    if (isFrench) {
      pdfDoc = printer.createPdfKitDocument(patientPrintDescriptorFrench);
    } else {
      pdfDoc = printer.createPdfKitDocument(patientPrintDescriptor);
    }
  } else if (isFax) {
    if (isFrench) {
      pdfDoc = printer.createPdfKitDocument(patientFaxDescriptorFrench);
    } else {
      pdfDoc = printer.createPdfKitDocument(patientFaxDescriptor);
    }
  }
} else if (isProvider) {
  if (isPrint) {
    if (isFrench) {
      pdfDoc = printer.createPdfKitDocument(providerPrintDescriptorFrench);
    } else {
      pdfDoc = printer.createPdfKitDocument(providerPrintDescriptor);
    }
  } else if (isFax) {
    if (isFrench) {
      pdfDoc = printer.createPdfKitDocument(providerFaxDescriptorFrench);
    } else {
      pdfDoc = printer.createPdfKitDocument(providerFaxDescriptor);
    }
  }
}


pdfDoc.pipe(fs.createWriteStream(workingDir+"MAIN.pdf"));
pdfDoc.end();
