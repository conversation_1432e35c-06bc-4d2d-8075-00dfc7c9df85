"use strict";

import PdfPrinter from "pdfmake";
import * as fs from 'node:fs';

// 2024-12-24: I don't want to touch this file outside the import statements, so keeping existing naming
const pdfMake = PdfPrinter

var byteArray = fs.readFileSync(process.argv[2]);
var mainInd = process.argv[2].indexOf("vault")
var finalInd = process.argv[2].indexOf("DESCRIPTOR");
var mainDir = process.argv[2].slice(0,mainInd);
var workingDir = process.argv[2].slice(0,finalInd);

var descriptor = JSON.parse(byteArray);

var fonts = {
    Roboto: {
        normal: mainDir+'assets/fonts/roboto/Roboto-Regular.ttf',
        bold: mainDir+'assets/fonts/roboto/Roboto-Medium.ttf',
        italics: mainDir+'assets/fonts/roboto/Roboto-Italic.ttf',
        bolditalics: mainDir+'assets/fonts/roboto/Roboto-MediumItalic.ttf'                                        
    },
    Opensans: {
        bold: mainDir+'assets/fonts/opensans/open-sans.bold.ttf'
    }
};

var printer = new pdfMake(fonts);

var pdfDoc = printer.createPdfKitDocument(descriptor);
pdfDoc.pipe(fs.createWriteStream(workingDir+"MAIN.pdf"));
pdfDoc.end();
