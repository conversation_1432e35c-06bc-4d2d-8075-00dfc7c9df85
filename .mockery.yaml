with-expecter: true
filename: "mock_{{.InterfaceName | lower}}.go"
dir: 'generated/mocks/{{ replace .InterfaceDirRelative "pkg/" "" 1 }}'
mockname: "Mock{{.InterfaceName}}"
outpkg: "mock{{.PackageName}}"
inpackage: false
issue-845-fix: true
resolve-type-alias: false
packages:
  gitlab.com/pockethealth/coreapi/generated/services/recordservice:
    interfaces:
      Invoker:
        config:
          dir: 'generated/mocks/{{ replace .InterfaceDirRelative "generated/services/" "" 1 }}'
  gitlab.com/pockethealth/coreapi/pkg/services/recordservice:
    interfaces:
      RecordServiceClientInterface:
        config:
          dir: 'generated/mocks/{{ replace .InterfaceDirRelative "pkg/services/" "" 1 }}'
  gitlab.com/pockethealth/coreapi/pkg/coreapi:
    interfaces:
      CheckoutApiServicer:
      ProvidersApiServicer:
      RequestFormConfigurationApiServicer:
      UsersApiServicer:
      V2UsersApiServicer:
      ShortUrlsServicer:
  gitlab.com/pockethealth/coreapi/pkg/recordstreaming:
    interfaces:
      RecordStreamingServicer:
  gitlab.com/pockethealth/coreapi/pkg/audit:
    interfaces:
      Store:
      Service:
  gitlab.com/pockethealth/coreapi/pkg/pubsub:
    interfaces:
      TopicSender:
  gitlab.com/pockethealth/coreapi/pkg/physicianaccount:
    interfaces:
      PhysicianAccountApiServicer:
