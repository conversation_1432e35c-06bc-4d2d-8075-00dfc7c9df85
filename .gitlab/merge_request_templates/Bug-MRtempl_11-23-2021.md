**Notion task:** [link](https://www.notion.so/pockethealth/)
- [ ] This code has been manually rebased (check if true)
## 🐞 Bug Description
(Provide a brief summary of the background and purpose)
<br><br>
## ✨ Root Cause
(What was the root cause of the problem?)
<br><br>
## 📝 Solution
#### 📝 Overview / Description
(What was the technical solution to the problem and how does it resolve the issue caused in the original task?)

(Provide visuals/screenshots if applicable)
<br><br>
#### 📝 Update
(What changes did you work on?)
<br><br>
## 💯 Testing
(Were unit tests/integration tests/apptests added or altered as a part of this bug fix or was only manual testing applicable?)

## 🔐 Security Checklist
(https://www.notion.so/Code-Review-Security-Checklist-b7972c7288114cc5a9854fb88de9097c)
- [ ] The above security items have been checked, and none of them apply (check if true)

(Mention any relevant security items checked, otherwise confirm N/A above)