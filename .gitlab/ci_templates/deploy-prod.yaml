spec:
  inputs:
    namespace:
      default: default
      description: "The namespace the application will run in by default."
    region:
      default: cacentral
    cluster-name:
      default: prod-aks-2
    cluster-resource-group:
      default: pockethealth_backend_prod
    db-hostname:
      default: ph-sql-main3-cactrl-prod.mysql.database.azure.com
    ci-runner-tags:
      default: envprod
    url:
      description: "A test URL for accessing this server once deployed (like /ping)"
---
prod_$[[ inputs.region ]]_env_deploy:
  rules:
    - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
      when: manual
  allow_failure: false
  stage: deploy
  environment:
    name: "Production $[[ inputs.region ]]"
    url: "$[[ inputs.url ]]"
  tags:
    - $[[ inputs.ci-runner-tags ]]
  dependencies:
    - push-to-acr-coreapi
    - qa_env_apptest
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  before_script:
    - echo "Use link below to login; deployment job will block until authenticated (times out in 15 min)."
    - az login --use-device-code
    - echo "Azure AD credentials OK; deployment approver is <$(az ad signed-in-user show --query userPrincipalName -o tsv)>"
    - wget -O /usr/local/bin/goose https://github.com/pressly/goose/releases/latest/download/goose_linux_x86_64
    - chmod +x /usr/local/bin/goose
    - goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "coreapi-prod-deployers:$(az account get-access-token --resource-type oss-rdbms --output tsv --query accessToken)@tcp($[[ inputs.db-hostname ]])/pockethealth?parseTime=true&allowCleartextPasswords=true" up
    - echo "Azure AD credentials OK; deployment approver is <$(az ad signed-in-user show --query userPrincipalName -o tsv)>"
    - az aks install-cli
    - apk add --no-cache git curl
  script:
    - az aks get-credentials --resource-group "$[[ inputs.cluster-resource-group ]]" --name "$[[ inputs.cluster-name ]]"
    - kubelogin convert-kubeconfig -l azurecli
    - kubectl config set-context --current=true --namespace="$[[ inputs.namespace ]]"

    - echo "State before deployment:"
    - kubectl get pods -l app=coreapi
    - echo "Running kubectl kustomize ./deployments/kubernetes/overlays/prod-$[[ inputs.region ]]"
    - echo "namespace":" $[[ inputs.namespace ]]" > ./kustomization.yaml
    - echo "resources:" >> ./kustomization.yaml
    - echo "- ./deployments/kubernetes/overlays/prod-$[[ inputs.region ]]" >> ./kustomization.yaml
    - echo "images:" >> ./kustomization.yaml
    - cat coreapi.kustomize.yaml >> ./kustomization.yaml
    - cat ./kustomization.yaml
    - kubectl diff -k . || true
    - kubectl apply -k .
    - kubectl rollout status deployment coreapi
    - kubectl rollout status deployment coreapi-tesla
    - deployments/release-notes.sh $[[ inputs.region ]]
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=coreapi
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=coreapi --prefix=true
    - kubectl get services
  retry:
    max: 2
    when:
      - runner_system_failure
      - script_failure
