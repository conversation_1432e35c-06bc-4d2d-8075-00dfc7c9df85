s2sauth-test-setup:
  tags:
    - k8s-infra-dm
  stage: preimage
  image: mcr.microsoft.com/azure-cli:2.63.0
  script:
    - mkdir -p tmp
    - az login --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --federated-token "$(cat $AZURE_FEDERATED_TOKEN_FILE)"
    - az aks get-credentials --resource-group pockethealth_test --name qa-aks-2 -f /tmp/config
    - az aks install-cli
    - kubelogin convert-kubeconfig -l spn --kubeconfig /tmp/config
    - cp /tmp/config $CI_PROJECT_DIR/tmp/config
    - cp /usr/local/bin/kubelogin $CI_PROJECT_DIR/kubelogin
  artifacts:
    paths:
      - $CI_PROJECT_DIR/tmp/config
      - $CI_PROJECT_DIR/kubelogin

