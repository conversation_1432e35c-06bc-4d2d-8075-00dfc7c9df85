qa_env_deploy:
  rules:
  - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
    when: always
  - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
    when: always
  allow_failure: false
  stage: deploy
  environment:
      name: "QA"
      url: https://core.qa.pocket.health/ping
  tags:
    - k8s-infra-dm
  needs:
    - push-to-acr-coreapi
  dependencies:
    - push-to-acr-coreapi
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  before_script:
    #install goose and apply any db migrations
    - wget -O /usr/local/bin/goose https://github.com/pressly/goose/releases/latest/download/goose_linux_x86_64
    - chmod +x /usr/local/bin/goose
    - goose -dir migrations mysql "$GOOSE_QA_CONNECTION" up
    - az login --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --federated-token $(cat $AZURE_FEDERATED_TOKEN_FILE)
    - az aks install-cli
  script:
    - az aks get-credentials -g pockethealth_test -n qa-aks-2
    - kubelogin convert-kubeconfig -l azurecli
    - kubectl config set-context --current=true --namespace=coreapi
    - echo "State before deployment:"
    - kubectl get pods -l app=coreapi
    - echo "Running kubectl kustomize ./deployments/kubernetes/overlays/qa"
    - echo "namespace":" coreapi" > ./kustomization.yaml
    - echo "resources:" >> ./kustomization.yaml
    - echo "- ./deployments/kubernetes/overlays/qa" >> ./kustomization.yaml
    - echo "images:" >> ./kustomization.yaml
    - cat coreapi.kustomize.yaml >> ./kustomization.yaml
    - cat ./kustomization.yaml
    - kubectl diff -k . || true
    - kubectl apply -k .
    - kubectl rollout status deployment coreapi
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=coreapi
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=coreapi --prefix=true
  retry:
    max: 2
    when:
      - runner_system_failure
      - script_failure
