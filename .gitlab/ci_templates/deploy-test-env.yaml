spec:
  inputs:
    namespace:
      description: "The namespace the application will run in by default."
    identity-name:
      description: "The name of the identity for this environment. Must already exist."
    federated-credential-prefix:
      description: "The value for this environment's federated credential."
    ingress-url:
      default: server-$CI_COMMIT_REF_SLUG.qa.pocket.health
    service-account-name:
      default: server
    region:
      default: cacentral
    cluster-name:
      default: qa-aks-2
    cluster-resource-group:
      default: pockethealth_test
    identity-resource-group:
      default: MC-qa-aks-2
---

deploy_qa_test:
  rules:
  - if: $CI_MERGE_REQUEST_ID
  stage: deploy
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    url: https://$[[ inputs.ingress-url ]]
    on_stop: teardown_qa_test
    auto_stop_in: 4 days
  tags:
    - k8s-infra-dm
  needs:
    - push-to-acr-coreapi
  dependencies:
    - push-to-acr-coreapi
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  variables:
    NAMESPACE_NAME: "$[[ inputs.namespace ]]-${CI_COMMIT_REF_SLUG}"
    AZURE_CLUSTER_NAME: "$[[ inputs.cluster-name ]]"
    AZURE_CLUSTER_RESOURCE_GROUP: "$[[ inputs.cluster-resource-group ]]"
    IDENTITY_NAME: "$[[ inputs.identity-name ]]"
    FEDERATED_IDENTITY_NAME: "$[[ inputs.federated-credential-prefix ]]-qa-${CI_COMMIT_REF_SLUG}"
    IDENTITY_RESOURCE_GROUP: "$[[ inputs.identity-resource-group ]]"
    SERVICE_ACCOUNT_SUBJECT: "system:serviceaccount:${NAMESPACE_NAME}:$[[ inputs.service-account-name ]]"
  before_script:
    #install goose and apply any db migrations
    - wget -O /usr/local/bin/goose https://github.com/pressly/goose/releases/latest/download/goose_linux_x86_64
    - chmod +x /usr/local/bin/goose
    - goose -dir migrations mysql "$GOOSE_QA_CONNECTION" up
    - az login --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --federated-token $(cat $AZURE_FEDERATED_TOKEN_FILE)
    - export AKS_ISSUER=$(az aks show -n "${AZURE_CLUSTER_NAME}" -g "${AZURE_CLUSTER_RESOURCE_GROUP}" --query "oidcIssuerProfile.issuerUrl" -otsv)
    - az aks install-cli
  script:
    - az aks get-credentials --resource-group "${AZURE_CLUSTER_RESOURCE_GROUP}" --name "${AZURE_CLUSTER_NAME}"
    - kubelogin convert-kubeconfig -l azurecli
    - kubectl create namespace "${NAMESPACE_NAME}" || true
    - kubectl config set-context --current=true --namespace="${NAMESPACE_NAME}"
    - az identity federated-credential create --identity-name "${IDENTITY_NAME}" --name "${FEDERATED_IDENTITY_NAME}" --subject "${SERVICE_ACCOUNT_SUBJECT}" --resource-group "${IDENTITY_RESOURCE_GROUP}" --issuer "${AKS_ISSUER}"
    - echo "State before deployment:"
    - kubectl get pods -l app=coreapi
    - echo "Running kubectl kustomize ./deployments/kubernetes/overlays/test-$[[ inputs.region ]]"
    - |
      cat << EOF > ./kustomization.yaml
      namespace: ${NAMESPACE_NAME}
      resources:
      - "./deployments/kubernetes/overlays/test-$[[ inputs.region ]]"
      replacements:
      - source:
          kind: Certificate
          fieldPath: spec.host
        targets:
        - select:
          commonName: coreapi
          fieldPaths:
          - spec.host[0]
          - spec.host[1]
      patches:
      - target:
          kind: Certificate
          name: coreapi
        patch: |-
          apiVersion: cert-manager.io/v1
          kind: Certificate
          metadata:
            name: coreapi
          spec:
            commonName: $[[ inputs.ingress-url]]
            dnsNames:
            - $[[ inputs.ingress-url]]
            - tesla.$[[ inputs.ingress-url]]
      replacements:
      - source:
          kind: Certificate
          name: coreapi
          fieldPath: spec.commonName
        targets:
        - select:
            kind: Ingress
            name: coreapi
          fieldPaths:
          - spec.rules.0.host
      - source:
          kind: Certificate
          name: coreapi
          fieldPath: spec.dnsNames.1
        targets:
        - select:
            kind: Ingress
            name: coreapi
          fieldPaths:
          - spec.rules.1.host
      images:
      EOF
    - cat coreapi.kustomize.yaml >> ./kustomization.yaml
    - cat ./kustomization.yaml
    - kubectl diff -k . || true
    - kubectl apply -k .
    - kubectl rollout status deployment coreapi
  artifacts:
    paths:
    - ./kustomization.yaml
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=coreapi
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=coreapi --prefix=true

teardown_qa_test:
  rules:
  - if: $CI_MERGE_REQUEST_ID
  when: manual
  stage: teardown
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    url: https://$[[ inputs.ingress-url ]]/ping
    action: stop
  tags:
  - k8s-infra-dm
  needs:
  - deploy_qa_test
  dependencies:
  - deploy_qa_test
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  variables:
    NAMESPACE_NAME: "$[[ inputs.namespace ]]-${CI_COMMIT_REF_SLUG}"
    AZURE_CLUSTER_NAME: "$[[ inputs.cluster-name ]]"
    AZURE_CLUSTER_RESOURCE_GROUP: "$[[ inputs.cluster-resource-group ]]"
    IDENTITY_NAME: "$[[ inputs.identity-name ]]"
    FEDERATED_IDENTITY_NAME: "$[[ inputs.federated-credential-prefix ]]-qa-${CI_COMMIT_REF_SLUG}"
    IDENTITY_RESOURCE_GROUP: "$[[ inputs.identity-resource-group ]]"
  before_script:
  - az login --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --federated-token $(cat $AZURE_FEDERATED_TOKEN_FILE)
  script:
  - az aks get-credentials --resource-group "${AZURE_CLUSTER_RESOURCE_GROUP}" --name "${AZURE_CLUSTER_NAME}"
  - az aks install-cli
  - kubelogin convert-kubeconfig -l azurecli
  # run kustomize to help cleanup
  - kubectl delete -k . --all --dry-run=server || true
  - kubectl delete -k . --all
  - az identity federated-credential delete --identity-name "${IDENTITY_NAME}" --name "${FEDERATED_IDENTITY_NAME}"  --resource-group "${IDENTITY_RESOURCE_GROUP}" --yes
  artifacts:
    paths:
    - ./kustomization.yaml
  after_script:
  - kubectl delete namespace "${NAMESPACE_NAME}" || true
