{"keyvault_name": "phprodkvcacentral0", "jwt_secret_sec_name": "ca-jwt-sec", "email_token_sec_name": "ca-email-token-sec", "mysql_connection_string_sec_name": "ca-sql-conn-str", "azure_storage_account": "phcentralcabloblrs", "azure_objs_storage_container": "prod", "azure_requests_storage_container": "requests", "azure_consents_storage_container": "consents", "azure_sharemetadata_storage_container": "sharemetadata", "azure_reportinsight_storage_container": "reportinsightsdata", "azure_pprof_autoprofiler_storage_container": "coreapi-profiles", "ssl_certs": [{"kind": "file", "key": "/etc/coreapi/tls/tls.key", "chain": "/etc/coreapi/tls/tls.crt"}], "hostname": "core.pocket.health", "allowed_origins": ["https://www.pocket.health", "https://pockethealth.com", "https://www.pockethealth.com", "https://medstar.pocket.health", "https://medstar-staging.pocket.health"], "allowed_headers": ["Accept", "Authorization", "Enctype", "Content-type", "upload-session-id", "X-Image-Token", "UTM-Source", "UTM-Medium", "UTM-Campaign", "Client-Id", "App-Location", "Deferred", "Language"], "exposed_headers": ["Content-disposition", "cache-control", "content-length", "expires", "pragma", "Pockethealth-Api-Result"], "frontend_host": "pocket.health", "log_level": "INFO", "shutdown_timeout_seconds": 30, "prov_svc": {"url": "providers.pocket.health", "user": "<PERSON><PERSON><PERSON>", "pw_sec_name": "ca-prov-svc-pw"}, "fax_svc": {"url": "fax.pocket.health", "name": "ca-prod", "api_key_sec_name": "ca-fs-api-key"}, "hlth_rec_svc": {"url": "hrs.cactrl.prod.pocket.health", "api_key_sec_name": "ca-hrs-key"}, "payment_svc": {"url": "pmts.global.prod.pocket.health", "api_key_sec_name": "ca-pmts-rw-api-key"}, "rec_svc": {"url": "https://recsvc.cactrl.prod.pocket.health", "name": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-recsvc-key"}, "rp_client": {"url": "http://reportprocessor.grapefruit.svc.cluster.local:7443", "name": "core", "api_key_sec_name": "ca-rp-api-key"}, "ri_client": {"url": "http://report-insights-service.reportinsights.svc.cluster.local:4000"}, "account_svc": {"url": "https://account.global.prod.pocket.health", "name": "core-cactrl-prod", "api_key_sec_name": "ca-acctsvc-api-key"}, "org_svc": {"url": "https://orgs.global.prod.pocket.health", "name": "core-prod", "api_key_sec_name": "ca-orgsvc-api-key"}, "plan_svc": {"url": "https://plansvc.global.prod.pocket.health", "name": "core-cactrl-prod", "api_key_sec_name": "ca-plansvc-api-key"}, "exam_insights_svc": {"url": "https://examinsights.cactrl.prod.pocket.health", "name": "core-prod", "api_key_sec_name": "ca-sosvc-api-key"}, "record_retrieval_svc_url": "https://recordretrievalsvc.cactrl.prod.pocket.health", "cache_expiry_lengths_mins": {"report_delay": 480, "origin_id_name": 480, "clinic_id_org_id": 480, "provsvc_auth_token": 720}, "rr_pubkey_path": "configs/rrpubkey.prod.pem", "rr_base_url": "https://rr.global.prod.pocket.health", "rr_api_key_sec_name": "ca-rr-api-key", "region_id": 1, "as_pubkey_path": "configs/aspubkey.prod.pem", "as_jwt_pubkey_path": "configs/asjwtpubkey.prod.pem", "email_throttle_per_day": 5, "max_idle_sql_conns": 20, "max_sql_conn_mins": 5, "max_open_sql_conns": 70, "send_emails": {"delegate": true}, "roi_svc": {"api_url": "https://roi.cactrl.prod.pocket.health", "api_user": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-roi-key-CA"}, "amplitude_api_key_sec_name": "amplitude-api-key", "amplitude_backend_deployment_key_sec_name": "amplitude-backend-deployment-key", "cio_site_id_sec_name": "cio-site-id", "cio_api_key_sec_name": "cio-api-key", "trigger_tracking_events": true, "appgw_private_cidr": "10.242.1.0/24", "appgw_cidrs": ["10.242.1.0/24", "20.151.112.140/32"], "certreloader_cadence_sec": 1800, "bad_login_delay_ms": 80, "cio_transactional_email_ids": {"request_confirmation": "34", "continue_to_account": "47", "duplicate_request": "36", "pvr_px": "50", "delegate_review": "55", "request_rejected": "53", "already_enrolled": "56", "friend_referral": "57", "patient_share": "59", "share_email_failed": "63", "share_failed": "62", "share_fax_failed": "64", "share_email_confirm": "68", "share_paper_confirm": "66", "share_fax_confirm": "67"}, "cio_email_api_key_sec_name": "cio-app-api-key", "region": "CA", "service_bus_connection_key_sec_name": "service-bus-conn-str", "pocket_health_app_url": "https://pocket.health", "service_bus_name": "sb-ph-prod", "account_request_created_queue": "accountrequestcreated", "request_status_queue": "requeststatus", "reports_topic": "reports", "support_email_address": "<EMAIL>", "organviz_topic_trigger": {"topicName": "study_state_change", "subscriptionName": "coreapi_organviz_trigger"}, "gateway_auth_url": "https://gatewayauth.global.prod.pocket.health", "open_telemetry_collector": {"host": "telemetry.cactrl.prod.pocket.health", "auth_token_sec_name": "coreapi-open-telemetry-collector-auth-token"}}