{"keyvault_name": "phtestkvcacentral0", "jwt_secret_sec_name": "ca-jwt-sec", "email_token_sec_name": "ca-email-token-sec", "mysql_connection_string_sec_name": "ca-sql-conn-str-qa", "azure_storage_account": "phdevcentralcabloblrs", "azure_objs_storage_container": "prod", "azure_requests_storage_container": "requests", "azure_consents_storage_container": "consents", "azure_sharemetadata_storage_container": "sharemetadata", "azure_reportinsight_storage_container": "reportinsightsdata", "azure_pprof_autoprofiler_storage_container": "coreapi-profiles", "ssl_certs": [{"kind": "file", "key": "/etc/coreapi/tls/tls.key", "chain": "/etc/coreapi/tls/tls.crt"}], "hostname": "core.qa.pocket.health", "allowed_origins": ["https://qa.pocket.health", "https://localhost", "http://localhost:4200", "https://localhost:4200", "http://local.pocket.health:4200", "https://devtest.global.qa.pocket.health", "https://127.0.0.1:4200", "http://127.0.0.1:4200", "https://127.0.0.1", "https://localhost/v1", "https://frontend.cactrl.qa.pocket.health", "https://medstar.qa.pocket.health", "https://woodbine.qa.pocket.health"], "allowed_headers": ["Accept", "Authorization", "Enctype", "Content-type", "upload-session-id", "X-Image-Token", "Access-Control-Allow-Credentials", "Access-Control-Allow-Origin", "set-cookie", "Origin", "UTM-Source", "UTM-Medium", "UTM-Campaign", "Client-Id", "App-Location", "Deferred", "Language"], "exposed_headers": ["Content-disposition", "cache-control", "content-length", "expires", "pragma", "Pockethealth-Api-Result"], "frontend_host": "qa.pocket.health", "log_level": "DEBUG", "shutdown_timeout_seconds": 30, "prov_svc": {"url": "providersservice.qa.pocket.health", "user": "<PERSON><PERSON><PERSON>", "pw_sec_name": "ca-prov-svc-pw"}, "fax_svc": {"url": "fax.qa.pocket.health", "name": "ca-qa", "api_key_sec_name": "ca-fs-api-key"}, "hlth_rec_svc": {"url": "hrs.cactrl.qa.pocket.health", "api_key_sec_name": "ca-hrs-key"}, "payment_svc": {"url": "pmts.global.qa.pocket.health", "api_key_sec_name": "ca-pmts-rw-api-key"}, "rec_svc": {"url": "https://recsvc.cactrl.qa.pocket.health", "name": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-recsvc-key"}, "rp_client": {"url": "http://reportprocessor.default.svc.cluster.local:7443", "name": "core", "api_key": "MJRYSdabkZp22zHL6gCnoSf4R18jmQ0teEdj57RbnkUyzkxmgXWxqV2rXLOOikPj"}, "ri_client": {"url": "https://ri.cactrl.qa.pocket.health"}, "account_svc": {"url": "https://account.global.qa.pocket.health", "name": "core-qa", "api_key_sec_name": "ca-acctsvc-api-key"}, "org_svc": {"url": "https://orgs.global.qa.pocket.health", "name": "core-dev", "api_key_sec_name": "ca-orgsvc-api-key"}, "plan_svc": {"url": "https://plansvc.global.qa.pocket.health", "name": "core-qa", "api_key_sec_name": "ca-plansvc-api-key"}, "exam_insights_svc": {"url": "https://examinsights.cactrl.qa.pocket.health", "name": "core-qa", "api_key_sec_name": "ca-sosvc-api-key"}, "record_retrieval_svc_url": "https://recordretrievalsvc.cactrl.qa.pocket.health", "record_retrieval_allow_cross_site": true, "cache_expiry_lengths_mins": {"report_delay": 480, "origin_id_name": 480, "clinic_id_org_id": 480, "provsvc_auth_token": 720}, "rr_pubkey_path": "configs/rrpubkey.qa.pem", "rr_base_url": "https://rr.global.qa.pocket.health", "rr_api_key_sec_name": "ca-rr-api-key", "region_id": 1, "as_pubkey_path": "configs/aspubkey.qa.pem", "as_jwt_pubkey_path": "configs/asjwtpubkey.qa.pem", "email_throttle_per_day": 5, "max_idle_sql_conns": 5, "max_sql_conn_mins": 5, "max_open_sql_conns": 100, "send_emails": {"delegate": false}, "roi_svc": {"api_url": "https://roi.cactrl.qa.pocket.health", "api_user": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-roi-key-CA"}, "amplitude_api_key_sec_name": "amplitude-api-key", "amplitude_backend_deployment_key_sec_name": "amplitude-backend-deployment-key", "cio_site_id_sec_name": "cio-site-id", "cio_api_key_sec_name": "cio-api-key", "trigger_tracking_events": true, "appgw_cidrs": ["**********/24", "************/32", "**********/16"], "open_telemetry_collector": {"host": "telemetry.cactrl.qa.pocket.health", "auth_token_sec_name": "coreapi-open-telemetry-collector-auth-token"}, "certreloader_cadence_sec": 21600, "bad_login_delay_ms": 92, "cio_transactional_email_ids": {"request_confirmation": "26", "continue_to_account": "47", "duplicate_request": "27", "pvr_px": "58", "delegate_review": "64", "request_rejected": "61", "already_enrolled": "65", "friend_referral": "60", "patient_share": "66", "share_email_failed": "71", "share_failed": "72", "share_fax_failed": "73", "share_email_confirm": "70", "share_paper_confirm": "75", "share_fax_confirm": "74"}, "cio_email_api_key_sec_name": "phu-cioweb-api-key", "region": "CA", "service_bus_connection_key_sec_name": "service-bus-conn-str-qa", "pocket_health_app_url": "https://qa.pocket.health", "service_bus_name": "sb-ph-qa", "account_request_created_queue": "accountrequestcreated", "request_status_queue": "requeststatus-ca", "reports_topic": "reports", "support_email_address": "<EMAIL>", "organviz_topic_trigger": {"topicName": "study_state_change", "subscriptionName": "coreapi_organviz_trigger"}, "gateway_auth_url": "https://gatewayauth.global.qa.pocket.health"}