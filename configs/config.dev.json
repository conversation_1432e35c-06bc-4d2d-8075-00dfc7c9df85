{"keyvault_name": "phtestkvcacentral0", "jwt_secret": "here-is-a-secret", "email_token_sec_name": "ca-email-token-sec", "mysql_connection_string": "devuser:devpw@tcp(apptest_mysql_1:3306)/pockethealth?parseTime=true", "azure_storage_account": "phdevcentralcabloblrs", "azure_objs_storage_container": "prod", "azure_requests_storage_container": "requests", "azure_consents_storage_container": "consents", "azure_sharemetadata_storage_container": "sharemetadata", "azure_reportinsight_storage_container": "reportinsightsdata", "azure_pprof_autoprofiler_storage_container": "coreapi-profiles", "ssl_certs": [{"kind": "file", "chain": "test_only_cert/fullchain.pem", "key": "test_only_cert/privkey.pem"}], "hostname": "localhost", "allowed_origins": ["https://qa.pocket.health", "https://localhost", "http://localhost:4200", "https://localhost:4200", "http://local.pocket.health:4200", "https://devtest.global.qa.pocket.health", "https://127.0.0.1:4200", "http://127.0.0.1:4200", "https://127.0.0.1", "https://localhost/v1", "https://frontend.cactrl.qa.pocket.health", "https://medstar.qa.pocket.health", "https://woodbine.qa.pocket.health"], "allowed_headers": ["Accept", "Authorization", "Enctype", "Content-type", "upload-session-id", "X-Image-Token", "UTM-Source", "UTM-Medium", "UTM-Campaign", "Client-Id", "App-Location", "Deferred", "Language"], "exposed_headers": ["Content-disposition", "cache-control", "content-length", "expires", "pragma", "Pockethealth-Api-Result"], "frontend_host": "localhost", "log_level": "DEBUG", "shutdown_timeout_seconds": 30, "prov_svc": {"url": "appgateway.cactrl.qa.pocket.health/provsvc", "user": "<PERSON><PERSON><PERSON>", "pw": "pw12345"}, "fax_svc": {"url": "appgateway.cactrl.qa.pocket.health/faxsvc", "name": "ca-dev", "api_key": "BnyKRapTbfgnuIkVGQFrfvzW3SWObwjHAAnhr3NCDyvd82to2YbJYk411RltagmK"}, "hlth_rec_svc": {"url": "appgateway.cactrl.qa.pocket.health/hrs", "api_key": "MJRYSdabkZp22zHL6gCnoSf4R18jmQ0teEdj57RbnkUyzkxmgXWxqV2rXLOOikPj"}, "payment_svc": {"url": "appgateway.cactrl.qa.pocket.health/pmts", "api_key": "HrpWNut7NNmaglsARUOWO6RNtSUdsu8Tm12IcM46i97OtaGMMAnxgIMnHQRb0elI"}, "rec_svc": {"url": "https://appgateway.cactrl.qa.pocket.health/recsvc", "name": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-recsvc-key"}, "rp_client": {"url": "http://apptest_rp_1:7443", "name": "core", "api_key": "MJRYSdabkZp22zHL6gCnoSf4R18jmQ0teEdj57RbnkUyzkxmgXWxqV2rXLOOikPj"}, "ri_client": {"url": "http://host.docker.internal:4000"}, "account_svc": {"url": "https://appgateway.cactrl.qa.pocket.health/acctsvc", "name": "core-dev", "api_key": "o5h2VvckcTZxX7rAaAMpEOdhpShC7071XFkESUD4KklgJXNnD3ZWjPC8ZEdnm3hp"}, "org_svc": {"url": "https://appgateway.cactrl.qa.pocket.health/orgsvc", "name": "core-dev", "api_key": "NQrfbT7uYvv5lNkwv3D87w1yfv7ccYNfwZySsmdkeB6gIhkOkOuGty0gMr2T739y"}, "plan_svc": {"url": "https://appgateway.cactrl.qa.pocket.health/plansvc", "name": "core-qa", "api_key": "oB7Ud0WOsXH2UgZ+0S/CvoBRk8gWtGXNeDfC7ez0e4w="}, "exam_insights_svc": {"url": "https://examinsights.cactrl.qa.pocket.health", "name": "core-dev", "api_key": "2Muk2LMwr2Vfz2Ua7HDwr8LPyr9Yxt6VMru7HKbi5QMvu6Tzq6ZWqm5Cvd3S"}, "record_retrieval_svc_url": "https://recordretrievalsvc.cactrl.qa.pocket.health", "record_retrieval_allow_cross_site": true, "cache_expiry_lengths_mins": {"report_delay": 480, "origin_id_name": 480, "clinic_id_org_id": 480, "provsvc_auth_token": 720}, "rr_pubkey_path": "configs/rrpubkey.dev.pem", "rr_base_url": "https://appgateway.cactrl.qa.pocket.health/regionrouter", "rr_api_key": "RPUvzrjqHvDrzy1fhfLQ7cNcEzjSGnGNjU3ux7EepwDlF9e2/j9p6/9oQ916KLC4HGhFe42qC+EjrclDrXDLvw==", "region_id": 1, "as_pubkey_path": "configs/aspubkey.dev.pem", "as_jwt_pubkey_path": "configs/asjwtpubkey.dev.pem", "email_throttle_per_day": 5, "max_idle_sql_conns": 5, "max_sql_conn_mins": 5, "max_open_sql_conns": 10, "send_emails": {"delegate": false}, "roi_svc": {"api_url": "https://appgateway.cactrl.qa.pocket.health/roi", "api_user": "<PERSON><PERSON><PERSON>", "api_key_sec_name": "coreapi-roi-key-CA"}, "amplitude_api_key_sec_name": "amplitude-api-key", "amplitude_backend_deployment_key_sec_name": "amplitude-backend-deployment-key", "cio_site_id_sec_name": "cio-site-id", "cio_api_key_sec_name": "cio-api-key", "trigger_tracking_events": false, "appgw_cidrs": ["**********/24", "************/32"], "open_telemetry_collector": {"host": "telemetry.cactrl.qa.pocket.health", "auth_token_sec_name": "coreapi-open-telemetry-collector-auth-token"}, "bad_login_delay_ms": 94, "cio_transactional_email_ids": {"request_confirmation": "26", "continue_to_account": "47", "duplicate_request": "27", "pvr_px": "58", "delegate_review": "64", "request_rejected": "61", "already_enrolled": "65", "friend_referral": "60", "patient_share": "66", "share_email_failed": "71", "share_failed": "72", "share_fax_failed": "73", "share_email_confirm": "70", "share_paper_confirm": "75", "share_fax_confirm": "74"}, "cio_email_api_key_sec_name": "phu-cioweb-api-key", "region": "CA", "service_bus_connection_key_sec_name": "service-bus-conn-str-qa", "pocket_health_app_url": "https://qa.pocket.health", "service_bus_name": "sb-ph-qa", "account_request_created_queue": "accountrequestcreated", "request_status_queue": "requeststatus-ca", "reports_topic": "reports", "support_email_address": "<EMAIL>", "organviz_topic_trigger": {"topicName": "study_state_change", "subscriptionName": "coreapi_organviz_trigger"}, "gateway_auth_url": "https://gatewayauth.global.qa.pocket.health"}