main
__debug_bin
pkg/linux_amd64/github.com/go-sql-driver/mysql.a
vault
dist
assets/pdfTemplates/testOutput/*
apptest/tmp
apptest/out
apptest/mongodump/test
apptest/sql/Dump.sql
.DS_Store
coreapi
node_modules
scripts/backfill_bambora_exam_orders/backfillexamorders
scripts/backfill_grace_period_orders/backfillparentexams
scripts/backfill_*/examorders.csv
# don't ignore coreapi folder
!coreapi/
test_only_cert/local-root.pem
coverage/*
!/coverage/README.md