<html>
  <head>
    <style>
      /* General */
      p,
      h1,
      h2 {
        margin-block-start: 0;
        margin-block-end: 0;
      }

      body {
        font-family: sans-serif;
        font: 400 18px/1.5;
      }

      .label {
        font-size: 10px;
        line-height: 1.2;
        color: #475569;
        white-space: nowrap;
      }

      .info {
        font-size: 14px;
      }

      .title {
        font-size: 24px;
        line-height: 1.2;
      }

      .main-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin: -12px 0;
      }

      .main-content > * {
        margin: 12px 0;
      }

      .divider {
        border-bottom: 1px solid;
        border-color: #cbd5e1;
      }

      .section-heading {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0;
        margin-bottom: 16px;
      }

      .bullet-points {
        padding-inline-start: 16px;
      }

      .bullet-points > li:not(:last-child) {
        margin: 0 0 8px;
      }

      .gap4 {
        margin-bottom: 4px;
      }

      /* Request Section */

      .request-section {
        display: -webkit-box;
        width: 100%;
      }

      .request-subsection {
        margin-right: 32px;
      }

      .request-subsection-email {
        -webkit-box-flex: 1;
      }

      .request-subsection-email .info {
        white-space: normal;
        word-wrap: break-word;
      }

      .request-data {
        margin-top: 0;
        margin-bottom: 0;
      }

      /* Provider Section */

      .provider-section {
        border: 1px solid;
        border-color: #cbd5e1;
        min-height: 88px;
        border-radius: 12px;
        display: -webkit-box;
      }

      .provider-name-subsection {
        display: -webkit-box;
        width: 66.66%;
        -webkit-box-orient: vertical;
        -webkit-box-pack: center;
      }

      .provider-label {
        margin: 16px 24px;
      }

      .provider-logo-subsection {
        width: 33.33%;
        border-left: 1px solid;
        border-color: #cbd5e1;
        -webkit-box-align: center;
        -webkit-box-pack: center;
        display: -webkit-box;
      }

      .provider-name {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        display: block;
      }

      .logo-container {
        margin-bottom: 40px;
      }

      .provider-logo {
        margin: auto;
        max-width: 180px;
        max-height: 40px;
      }

      .requesting-from {
        margin-bottom: 4px;
      }

      /* Patient Details Section */

      .patient-section {
        display: -webkit-box;
        width: 100%;
      }

      .dob {
        padding-left: 2px;
        font-size: 8px;
        color: #475569;
      }

      .delegate-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
      }

      .patient-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-right: 16px;
      }

      .info-table {
        border-spacing: 16px 8px;
        margin: -8px -16px;
        width: 100%;
      }

      .info-table .label {
        width: 1%;
        vertical-align: top;
        padding: 3.5px 0;
      }

      /* Recent Exam Section */

      .recent-exam-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-left: 16px;
      }

      .exam {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .exam-list {
        margin-top: 0;
        margin-bottom: -12px;
        padding-left: 24px;
      }

      /* Consent Section */

      .consent-section {
        margin-bottom: 16px;
      }

      .consent-text {
        line-height: 1.5;
      }

      /* Signature Section */

      .signature {
        width: 200px;
        height: 120px;
        border-bottom: 1px solid;
        border-color: #cbd5e1;
        margin-bottom: 8px;
        padding-bottom: 0;
      }

      /* Extra */

      .zero-top {
        margin-top: 0;
      }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body>
    <div class="logo-container">
      <a href="http://pocket.health">
        <img height="28px" src="data:image/png;base64,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" />
      </a>
    </div>
    <div class="main-content">
      <h1 class="title">Demande de dossiers médicaux</h1>
      <div class="request-section">
        <div class="request-subsection">
          <p class="label gap4">ID de la demande</p>
          <p class="request-data info">90210</p>
        </div>
        <div class="request-subsection">
          <p class="label gap4">Date de création</p>
          <p class="request-data info">31/12/2023</p>
        </div>
        <div class="request-subsection-email">
          <p class="label gap4">Compte</p>
          <p class="request-data info wrap"><EMAIL></p>
        </div>
      </div>
      <div class="provider-section">
        <div class="provider-name-subsection">
          <div class="provider-label">
            <div class="requesting-from label">Demandant de</div>
            <div class="provider-name">Uncle Hubey&#39;s Healthcare</div>
          </div>
        </div>
        <div class="provider-logo-subsection">
          <img class="provider-logo" src="data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="patient-section">
        <div class="patient-details-section">
          <h2 class="section-heading padding-bot-12">
            Informations sur le patient
          </h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Nom</td>
                <td class="info">James Shen</td>
              </tr>
              <tr>
                <td class="label">Date de naissance</td>
                <td class="info">
                  1900/01/01<span class="dob">AAAA/MM/JJ</span>
                </td>
              </tr>
              
              <tr>
                <td class="label">Téléphone</td>
                <td class="info">**********</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="recent-exam-section">
          <h2 class="section-heading padding-bot-12">Examens récents</h2>
          <ul class="exam-list bullet-points">
            
            <li class="exam">X-Ray - Jan, 1990</li>
            
            <li class="exam">CT - May, 2010</li>
            
            <li class="exam">Ultrasound - Dec, 2020</li>
            
          </ul>
        </div>
        
      </div>
      
      <div class="divider"></div>
      <div class="consent-section">
        <h2 class="section-heading">Consentement et reconnaissance</h2>
        <div class="consent-text info">
            <p>
                Moi, <b>James Shen</b> renonce par la présente à toute réclamation à l’encontre <b>Uncle Hubey&#39;s Healthcare</b> de ses médecins, employés et agents à quelque fin que ce soit en rapport avec ladite communication et la divulgation d’informations dans lesdits dossiers.
            </p>
            <p>
                Je comprends que mes dossiers seront mis à ma disposition par l’intermédiaire d’une plateforme de stockage de dossiers sécurisée d’un tiers, PocketHealth, par laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion.
            </p>
            <p> 
                Je comprends que les futurs dossiers, s'ils sont créés, seront également accessibles et pourront être ajoutés à mon compte, à ma discrétion.
            </p>
            <p>
                Je comprends que je pourrai me retirer de cet accès en ligne à tout moment.
            </p>
            <p> 
                J’autorise <b>Uncle Hubey&#39;s Healthcare</b> la communication de mes dossiers aux personnes que j’ai spécifiée dans ce formulaire
            </p>
          
        </div>
        <img class="signature" src="data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" />
        <p class="info zero-top">
          Signature
          du patient
        </p>
      </div>
    </div>
  </body>
</html>
