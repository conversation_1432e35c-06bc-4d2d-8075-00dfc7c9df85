# Backend Infrastructure

Welcome to CoreAPI Infrastructure-as-Code (IaC).  This directory contains automation for bringing up infrastructure for Providersservice in the form of Azure Resource Manager (ARM) templates, scripts, and documentation.  It assumes that the [Pockethealth Backend Infrastructure](https://gitlab.com/pockethealth/pho/-/tree/backend-no-aks/backend) has already been set up.  

Directory structure:

- `tmpl/` - ARM templates for backend infrastructure
- `param/` - Parameters for ARM templates
- `helpers/` - Helper scripts

The organization of ARM templates follows the patterns and naming conventions from PocketHealth Backend Infrastructure. 

# Prerequisite

The following sections presume that:

1. You have Azure CLI client (`az`), logged in with permissions for creating resources and running ARM templates
1. Pockethealth Backend Infrastructure and the `legacy-scripts` have been run (needed for `pockethealth-web` MySQL account and tables/indices)
1. You have some basic understanding of ARM templates (what they are, how to run them, basics of how to read them / get help for them) -- see References at bottom of doc for further ARM info.
1. Providersservice is already set up in the stack that you are deploying to.  (TODO: Need some way to express shared IaC)

# Usage

For a new stack, need the following:

1. Managed Identity
1. Public IP and DNS (2x, one for normal CoreAPI, one for CoreAPI-Tesla)
1. Keyvault
   1. New Secrets
   1. Copy 3rd-party dependencies settings ( Stripe, etc)
1. AKS agentpool
1. TLS Certificate (2x, one for normal CoreAPI, one for CoreAPI-Tesla)

## Preamble

The rest of the doc describes the steps to create and configure the infrastructure for CoreAPI.  You will need the following info from the backend stack to which you want to deploy the CoreAPI infrastructure:

- `${ENV}` - environment: prod or qa
- `${NAME}` - name of the stack; usually named after a geographical region, i.e. uswest, or cactrl.
- `${RG}` - Resource Group containing the backend infra
- `${MC_RG}` - The Resource Group – used by the AKS cluster for the stack (name usually begins with `MC-`)

## Provisioning script

The `provision.sh` script will run all of the template and helper scripts needed to set up everything except the TLS certificate.

```
Usage: provision.sh paramdir rg mc-rg mysqlhost mysqluser destkv

  Provision coreapi service keyvault, sql, and dns (subdomain).  Requires that
  az client be installed and logged in with resource creation privileges in the given
  resource groups.  You will be prompted for mysql password for sql provisioning.

  paramdir: path to stack parameters
  rg: resource group of stack
  mc-rg: MC resource group of stack's AKS
  mysql: mysql hostname 
  mysqluser: mysql user
  destkv: destination keyvault

  Example: ./provision.sh qa-uswest rg_phbackend_qa_uswestv2 MC-aks-backend-qa-uswestv2 ph-sql-main-qa-uswest obama@ph-sql-main-qa-uswest ph-kv-qa-uswestv2
```

During execution of the script, you will be prompted for some information.  You should be ready to provide the following information: XXX

- SQL connection string: this is the connection string for `pockethealth-web`.  It is given in the output of the [pho](https://gitlab.com/pockethealth/pho) repository's legacy MySQL setup script, `pho/backend/legacy-setup/setup-az-mysql-common.sh`.
- Faxservice API Key: the Faxservice API key in Base64.  This was created during the infrastructure config of Faxservice.
- Access keys of Storage account: can get from Portal or use the command `az storage account keys list --account-name <name>`
  - `blobrs`: usually has the name  "phstblobrs${ENV}${NAME}"
- MySQL admin password: The password for the admin user given in the CLI parameters.
- JWT secret: a secret that will be used for JWT token generation.  You can set this and forget it - a helper script `helpers/gen-jwt-secret.sh` can be used for this.

The script is safe to re-run if it fails or is canceled partway through.

## TLS Certificate

For creating a TLS certificate, use the script and instructions from https://gitlab.com/pockethealth/pho/-/tree/master/tls.  Note that you need one for the normal CoreAPI endpoint and another one for the Tesla CoreAPI endpoint.  See the templates for dns to get the hostname.

## Configure aad-pod-identity

Because the IaC creates a new node pool (`agentpool4`) with a taint, pod-aad-identity needs the corresponding tolerance, otherwise the Managed Identity will not be properly assigned to the nodes from that node pool.

Using the [helm upgrade](https://helm.sh/docs/helm/helm_upgrade/) command, you need to set the aad-pod-identity's [configuration](https://github.com/Azure/aad-pod-identity/tree/master/charts/aad-pod-identity#configuration) value `nmi.tolerations`.

Example: `helm upgrade aad-pod-identity aad-pod-identity/aad-pod-identity --set nmi.tolerations[0].key=vmsize,nmi.tolerations[0].operator=Equal,nmi.tolerations[0].value=bigmem,tolerations[0].effect=NoSchedule --set nmi.allowNetworkPluginKubenet=true`

Note that there may already be other settings and tolerations already configured, you need to set them again in the CLI, otherwise they'll be removed.  Use `helm get values` to check existing values.

# Next Steps

The infrastructure for CoreAPI should now be set up, but the service itself still needs to be deployed into the stack.  To do this, the following is needed:

1. CoreAPI config in new environment
  1. Make a copy of an existing prod config from `configs` directory in the root of CoreAPI project.  Use the naming convention `config.${ENV}-${NAME}.json`.  The following settings need to be updated to reflect the new stack:
     1. `keyvault_name`,
     1. `azure_storage_account`,
     1. `fax_hostname`,
     1. `prov_svc.url`
1. Kubernetes configuration
   1. Make a copy of an existing prod config from `deployments` directory.  Use the naming convention `k8s.${ENV}-${NAME}.json`.  The following sections need to be updated to reflect the new stack:
      1. AzureIdentity (`aadpodidentity.k8s.io/v1`): update the resourceID and clientID to reflect the Managed Identity used in this stack (can find it in ${RG}, it is the Identity with fax in its name)
      1. Deployments (`apps/v1`) `coreapi` and `coreapi-tesla`: Update the `command` so the config file for the new stack is used
      1. Service (`v1`) `coreapi` and `coreapi-tesla`: Update the IPs to the Public IP for CoreAPI/CoreAPI-Tesla in the new stack 
1. CICD configuration
   1. Create a new job in `.gitlab-ci.yml` for deploying to the new stack.  You can copy an existing prod deployment job.  In the new job:
      1. Use a Gitlab environment name to one that reflects the new stack's name.
      1. Use the K8s .yml file you created in the above steps.
      1. Use the name of the AKS cluster in the new stack.
   1. Register the Gitlab project in the GitlabRunner for the new stack with the new Gitlab environment name


# Apptest

The apptest is not automated enough to be run on a new stack out-of-the-box.  It was done once during IaC testing by basically copying the original QA DB into the new stack and modifying the configurations to point to it.  
