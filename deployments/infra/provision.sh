#!/bin/bash
set -e
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
if [[ "$1" == "-h" || $# -ne 6 ]]; then
    echo "Usage: provision.sh paramdir rg mc-rg mysqlhost mysqluser destkv"
    echo 
    echo "  Provision coreapi service keyvault, sql, and dns (subdomain).  Requires that"
    echo "  az client be installed and logged in with resource creation privileges in the given"
    echo "  resource groups.  You will be prompted for mysql password for sql provisioning."
    echo 
    echo "  paramdir: path to stack parameters"
    echo "  rg: resource group of stack"
    echo "  mc-rg: MC resource group of stack's AKS"
    echo "  mysql: mysql hostname "
    echo "  mysqluser: mysql user"
    echo "  destkv: destination keyvault"
    echo
    echo "  Example: ./provision.sh qa-uswest rg_phbackend_qa_uswestv2 MC-aks-backend-qa-uswestv2 ph-sql-main-qa-uswest obama@ph-sql-main-qa-uswest ph-kv-qa-uswestv2"
    exit 1
fi

paramdir=$1
rg=$2
mcrg=$3
mysqlhost="$4.mysql.database.azure.com"
mysqluser=$5
kvdest=$6

# run arm templates
az deployment group create \
	--name provision-coreapi-id \
	--resource-group "$mcrg" \
	--template-file tmpl/id.json \
	--parameter "params/$paramdir/id.param.json"
az deployment group create \
	--name provision-coreapi-dns \
	--resource-group "$mcrg" \
	--template-file tmpl/dns.json \
	--parameter "params/$paramdir/dns.param.json"
az deployment group create \
	--name provision-coreapi-secrets \
	--resource-group $rg \
	--template-file tmpl/keyvault_secrets.json \
	--parameter "params/$paramdir/keyvault_secrets.param.json"
az deployment group create \
	--name provision-coreapi-id \
	--resource-group "$rg" \
	--template-file tmpl/aks-agentpool.json \
	--parameter "params/$paramdir/aks-agentpool.param.json"

# copy common keyvault values
./copy-kv.sh ca-mgem-pw2 phprodkvcacentral0 "$kvdest"
./copy-kv.sh ca-pmts-rw-api-key phprodkvcacentral0 "$kvdest"
./copy-kv.sh ca-rr-api-key phprodkvcacentral0 "$kvdest"
