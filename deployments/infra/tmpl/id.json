{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ph_env": {"type": "string", "metadata": {"description": "PocketHealth environment (qa or prod)"}}, "ph_stack_name": {"type": "string", "metadata": {"description": "PocketHealth stack name (i.e. uswest)"}}, "stack_rg_name": {"type": "string", "defaultValue": "[concat('rg-phbackend-', parameters('ph_env'), '-', parameters('ph_stack_name'))]"}, "keyVaultName": {"type": "string", "defaultValue": "[concat('ph-kv-', parameters('ph_env'), '-', parameters('ph_stack_name'))]"}, "tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]"}, "id_name": {"type": "string", "defaultValue": "[concat('id-coreapi-', parameters('ph_env'), '-', parameters('ph_stack_name'))]"}, "secretsPermissions": {"type": "array", "defaultValue": ["list", "get"]}, "certsPermissions": {"type": "array", "defaultValue": ["list", "get"]}}, "variables": {}, "resources": [{"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "name": "[parameters('id_name')]", "apiVersion": "2018-11-30", "location": "[resourceGroup().location]"}, {"name": "grant-kv-to-coreapi-id", "type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "resourceGroup": "[parameters('stack_rg_name')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"type": "Microsoft.KeyVault/vaults/accessPolicies", "name": "[concat(parameters('keyVaultName'), '/add')]", "apiVersion": "2019-09-01", "properties": {"accessPolicies": [{"tenantId": "[parameters('tenantId')]", "objectId": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('id_name'))).principalId]", "permissions": {"secrets": "[parameters('secretsPermissions')]", "certificates": "[parameters('certsPermissions')]"}}]}}]}}, "dependsOn": ["[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', parameters('id_name'))]"]}]}