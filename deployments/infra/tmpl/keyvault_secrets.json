{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ph_env": {"type": "string", "metadata": {"description": "PocketHealth environment (qa or prod)"}}, "ph_stack_name": {"type": "string", "metadata": {"description": "PocketHealth stack name (i.e. uswest)"}}, "keyVaultName": {"type": "string", "defaultValue": "[concat('ph-kv-', parameters('ph_env'), '-', parameters('ph_stack_name'))]", "metadata": {"description": "Specifies the name of the key vault."}}, "sql-conn-string": {"type": "securestring", "metadata": {"description": "Specifies the value of the secret that you want to create."}}, "jwt-secret": {"type": "securestring", "metadata": {"description": "JWT secret (Base64)"}}, "azstore-key-blobrs": {"type": "securestring", "metadata": {"description": "Key for 'backend' azure storage account"}}, "fax-api-key": {"type": "securestring", "metadata": {"description": "API Key for faxservice (Base64)."}}, "provsvc-pw": {"type": "securestring", "metadata": {"description": "Password for Providersservice."}}, "ca-queue-azstore-key": {"type": "securestring", "metadata": {"description": "Key for backend storage account with queues"}}, "ca-hrs-key": {"type": "securestring", "metadata": {"description": "Health Records Service API Key"}}, "ca-sosvc-api-key": {"type": "securestring", "metadata": {"description": "Exam Insights Service API Key"}}}, "resources": [{"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-sql-conn-str')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('sql-conn-string')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-jwt-sec')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('jwt-secret')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-azstore-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('azstore-key-blobrs')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-fax-api-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('fax-api-key')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-prov-svc-pw')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('provsvc-pw')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-queue-azstore-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('ca-queue-azstore-key')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-hrs-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('ca-hrs-key')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'ca-ers-api-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('ca-ers-api-key')]"}}]}