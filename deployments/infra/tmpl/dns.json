{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ph_env": {"type": "string", "metadata": {"description": "PocketHealth environment (qa or prod)"}}, "ph_stack_name": {"type": "string", "metadata": {"description": "PocketHealth stack name (i.e. uswest)"}}, "stack_rg_name": {"type": "String", "defaultValue": "[concat('rg-phbackend-', parameters('ph_env'), '-', parameters('ph_stack_name'))]", "metadata": {"description": "Resource Group name containing the stack (not the MC RG)"}}, "coreapi_dns_prefix": {"defaultValue": "core", "type": "String"}, "coreapi_tesla_dns_prefix": {"defaultValue": "tesla.core", "type": "String"}, "parent_dns_zone": {"type": "String", "defaultValue": "[concat(parameters('ph_stack_name'), '.', parameters('ph_env'), '.pocket.health' )]"}, "coreapi_lb_pip_name": {"defaultValue": "[concat('pip-coreapi-', parameters('ph_env'), '-', parameters('ph_stack_name'), '-001')]", "type": "String"}, "coreapi_tesla_lb_pip_name": {"defaultValue": "[concat('pip-coreapitesla-', parameters('ph_env'), '-', parameters('ph_stack_name'), '-001')]", "type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.Network/publicIPAddresses", "apiVersion": "2020-08-01", "name": "[parameters('coreapi_lb_pip_name')]", "location": "[resourceGroup().location]", "sku": {"name": "Standard", "tier": "Regional"}, "properties": {"publicIPAddressVersion": "IPv4", "publicIPAllocationMethod": "Static", "idleTimeoutInMinutes": 4, "ipTags": []}}, {"type": "Microsoft.Network/publicIPAddresses", "apiVersion": "2020-08-01", "name": "[parameters('coreapi_tesla_lb_pip_name')]", "location": "[resourceGroup().location]", "sku": {"name": "Standard", "tier": "Regional"}, "properties": {"publicIPAddressVersion": "IPv4", "publicIPAllocationMethod": "Static", "idleTimeoutInMinutes": 4, "ipTags": []}}, {"name": "setup-coreapi-dns-a-record", "type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "resourceGroup": "[parameters('stack_rg_name')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"type": "Microsoft.Network/dnszones/A", "apiVersion": "2018-05-01", "name": "[concat(parameters('parent_dns_zone'), '/', parameters('coreapi_dns_prefix'))]", "properties": {"TTL": 3600, "ARecords": [{"ipv4Address": "[reference(resourceId('Microsoft.Network/publicIPAddresses', parameters('coreapi_lb_pip_name'))).ipAddress]"}], "targetResource": {}}}, {"type": "Microsoft.Network/dnszones/A", "apiVersion": "2018-05-01", "name": "[concat(parameters('parent_dns_zone'), '/', parameters('coreapi_tesla_dns_prefix'))]", "properties": {"TTL": 3600, "ARecords": [{"ipv4Address": "[reference(resourceId('Microsoft.Network/publicIPAddresses', parameters('coreapi_tesla_lb_pip_name'))).ipAddress]"}], "targetResource": {}}}]}}}]}