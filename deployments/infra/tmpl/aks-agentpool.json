{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ph_env": {"type": "string", "metadata": {"description": "PocketHealth environment (qa or prod)"}}, "ph_stack_name": {"type": "string", "metadata": {"description": "PocketHealth stack name (i.e. uswest)"}}, "cluster_name": {"defaultValue": "[concat('aks-backend-', parameters('ph_env'), '-', parameters('ph_stack_name'))]", "type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.ContainerService/managedClusters/agentPools", "apiVersion": "2020-12-01", "name": "[concat(parameters('cluster_name'), '/agentpool3')]", "properties": {"count": 1, "vmSize": "Standard_F16s", "osDiskSizeGB": 128, "osDiskType": "Managed", "maxPods": 110, "type": "VirtualMachineScaleSets", "enableAutoScaling": true, "maxCount": 6, "minCount": 1, "orchestratorVersion": "1.21.2", "enableNodePublicIP": false, "nodeTaints": ["vmsize=fastio:NoSchedule"], "mode": "User", "osType": "Linux", "upgradeSettings": {}}}]}