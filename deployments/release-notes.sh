#!/bin/bash

if [[ $# -ne 1 ]]; then
  printf "usage: ./release-notes {region}
        {region} can be cacentral or uswest\n"
  exit 1
fi
REGION=$1

#get current and second-most current tag
CURR_TAG="$(git describe --tags `git rev-list --tags --max-count=1`)"
LAST_TAG="$(git describe --abbrev=0 --tags $CURR_TAG^)"
#get commit messages between those two tags and link to the associated Jira tasks
REL_NOTES="$(git log --pretty='%s' $LAST_TAG..$CURR_TAG | sed -E 's/"/\\\"/g')"
#format into nice slack message
JSON_MESSAGE="{\"type\":\"mrkdwn\", \"text\":\"*Core API $REGION release $CURR_TAG*\n\n\n$REL_NOTES\n\n\n<https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/f3a25801-f1db-426d-b0a6-a207227920c5|🏥 Core API Health Metrics>\n<https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/8782eb95-4c17-43ab-904c-0d2e1b68d9c0|🏥 Core API Metrics>\n<https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/891e5490-3912-4b0a-8e08-5ad224fc1742|🏥 Core API US West Metrics>\"}"
curl -X POST -H 'Content-type: application/json' --data "$JSON_MESSAGE" *******************************************************************************