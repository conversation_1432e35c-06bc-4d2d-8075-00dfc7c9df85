apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi
  annotations:
    nginx.ingress.kubernetes.io/enable-modsecurity: "false"
spec:
  rules:
  - host: core.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - core.pocket.health
    secretName: coreapi-tls