---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: coreapi-tesla
spec:
  maxReplicas: 3
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: coreapi-tesla # deployment name
  metrics:
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 85