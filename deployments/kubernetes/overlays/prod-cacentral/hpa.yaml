---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: coreapi
spec:
  maxReplicas: 25
  minReplicas: 4
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: coreapi # deployment name
  metrics:
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 65
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 65