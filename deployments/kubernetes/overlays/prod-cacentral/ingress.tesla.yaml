apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi-tesla
  annotations:
    nginx.ingress.kubernetes.io/enable-modsecurity: "false"
spec:
  rules:
  - host: tesla.core.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi-tesla
            port:
              number: 443
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - tesla.core.pocket.health
    secretName: coreapi-tesla-tls