---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi
  annotations:
    $patch: replace
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-write-timeout: "300"
spec:
  ingressClassName: nginx-internal
  rules:
  - host: core.cactrl.qa.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix
  - host: tesla.cactrl.qa.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix