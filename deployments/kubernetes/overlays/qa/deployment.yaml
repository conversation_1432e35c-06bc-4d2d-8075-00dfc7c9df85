---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coreapi
spec:
  replicas: 1
  template:
    metadata:
      name: fs
    spec:
      containers:
      - name: coreapi
        command: [ "./docker-entrypoint.sh", "qa" ]
        readinessProbe:
          periodSeconds: 4       
        resources:	
          requests:
            cpu: "0.1"
            memory: 768Mi
          limits:
            cpu: "0.5"
            memory: 2048Mi
        env:
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "90"]
      terminationGracePeriodSeconds: 101     
