apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://qa.pocket.health, https://localhost, http://localhost:4200, https://localhost:4200, http://local.pocket.health:4200, https://devtest.global.qa.pocket.health, https://127.0.0.1:4200, http://127.0.0.1:4200, https://127.0.0.1, https://localhost, https://frontend.cactrl.qa.pocket.health, https://medstar.qa.pocket.health"
    nginx.ingress.kubernetes.io/enable-modsecurity: "false"
spec:
  rules:
  - host: core.qa.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - core.qa.pocket.health
    secretName: coreapi-tls