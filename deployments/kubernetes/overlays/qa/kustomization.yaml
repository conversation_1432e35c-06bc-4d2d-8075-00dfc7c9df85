namespace: coreapi

resources:
- ./../../base

patches:
- path: ./deployment.yaml
  target:
    name: coreapi
    kind: Deployment
- path: ./certificate.yaml
  target:
    name: coreapi
    kind: Certificate
- path: ./serviceaccount.yaml
  target:
    name: sa-coreapi
    kind: ServiceAccount
- path: ./exclude-deployment.tesla.yaml
- path: ./ingress.yaml
  target:
    name: coreapi
    kind: Ingress
# we don't have a separate resources for tesla in QA
- patch: |-
    $patch: delete
    apiVersion: networking.k8s.io/v1
    kind: Ingress
    metadata:
      name: coreapi-tesla
- patch: |-
    $patch: delete
    apiVersion: v1
    kind: Service
    metadata:
      name: coreapi-tesla