# This is service added as a workaround to external ingress upload speed limitations.
# TODO: remove this service and redirect tesla traffic to ingress once upload issues resolved
apiVersion: v1
kind: Service
metadata:
  name: coreapi-tesla-lb
  namespace: coreapi
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-resource-group: MC-aks-backend-prod-uswest
    service.beta.kubernetes.io/azure-pip-name: pip-hrs-prod-uswest
spec:
  selector:
    app: coreapi-tesla
  type: LoadBalancer
  ports:
    - name: https
      port: 443
      protocol: TCP
      targetPort: 20443
