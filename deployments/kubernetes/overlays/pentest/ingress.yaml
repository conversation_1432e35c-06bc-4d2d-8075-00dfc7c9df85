---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://pentest.qa.pocket.health"
spec:
  ingressClassName: nginx-internal
  rules:
  - host: core.pentest.qa.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix
  - host: tesla.core.pentest.qa.pocket.health
    http:
      paths:
      - backend:
          service:
            name: coreapi
            port:
              number: 443
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - tesla.core.pentest.qa.pocket.health
    - core.pentest.qa.pocket.health
    secretName: coreapi-tls