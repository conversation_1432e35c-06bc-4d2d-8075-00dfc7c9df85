namespace: pentest-2024
resources:
- ./../../base

patches:
- path: ./deployment.yaml
  target:
    name: coreapi
    kind: Deployment
- path: ./certificate.yaml
  target:
    name: coreapi
    kind: Certificate
- path: ./serviceaccount.yaml
  target:
    name: sa-coreapi
    kind: ServiceAccount
- path: ./exclude-deployment.tesla.yaml
- path: ./ingress.yaml
  target:
    name: coreapi
    kind: Ingress
# we don't have a separate resources for tesla in pentest
- patch: |-
    $patch: delete
    apiVersion: networking.k8s.io/v1
    kind: Ingress
    metadata:
      name: coreapi-tesla
- patch: |-
    $patch: delete
    apiVersion: v1
    kind: Service
    metadata:
      name: coreapi-tesla

images:
- name: phcrcacentral0.azurecr.io/coreapi@__DOCKER_DIGEST_PLACEHOLDER__
  digest: sha256:41cccd503cc9a7881341e5a18873b0935df2760919600d76303633675ed609b4
  newName: phcrcacentral0.azurecr.io/coreapi
