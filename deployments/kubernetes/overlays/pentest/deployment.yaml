---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coreapi
spec:
  replicas: 1
  template:
    metadata:
      name: fs
    spec:
      containers:
      - name: coreapi
        image: phcrcacentral0.azurecr.io/coreapi@__DOCKER_DIGEST_PLACEHOLDER__
        command: [ "./docker-entrypoint.sh", "pentest" ]
        readinessProbe:
          periodSeconds: 4       
        resources:
          requests:
            cpu: "0.1"
            memory: 256Mi
          limits:
            cpu: "0.5"
            memory: 512Mi
        env:
        lifecycle:
          preStop:
            exec:
              command: ["sleep", "90"]
      terminationGracePeriodSeconds: 101     