---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coreapi
spec:
  progressDeadlineSeconds: 600 
  selector:
    matchLabels:
      app: coreapi
  template:
    metadata:
      labels:
        app: coreapi
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: sa-coreapi
      securityContext:
        runAsUser: 21000
        runAsGroup: 21000
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: coreapi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - NET_RAW
        image: phcrcacentral0.azurecr.io/coreapi@__DOCKER_DIGEST_PLACEHOLDER__
        imagePullPolicy: Always
        readinessProbe:
          httpGet:
            path: /ping
            port: 20443
            scheme: HTTPS
          timeoutSeconds: 2
          failureThreshold: 5
        ports:
        - containerPort: 20443
        volumeMounts:
        - mountPath: /etc/coreapi/tls
          name: coreapi-tls
          readOnly: true
      volumes:
      - secret:
          secretName: coreapi-tls
        name: coreapi-tls