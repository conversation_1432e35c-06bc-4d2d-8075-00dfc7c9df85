---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coreapi-tesla
spec:
  progressDeadlineSeconds: 600 
  selector:
    matchLabels:
      app: coreapi-tesla
  template:
    metadata:
      name: ca-tesla
      labels:
        app: coreapi-tesla
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: sa-coreapi
      securityContext:
          runAsUser: 21000
          runAsGroup: 21000
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: coreapi
        securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - NET_RAW
        image: phcrcacentral0.azurecr.io/coreapi@__DOCKER_DIGEST_PLACEHOLDER__
        imagePullPolicy: Always
        readinessProbe:
          httpGet:
            path: /ping
            port: 20443
            scheme: HTTPS
          timeoutSeconds: 2
          failureThreshold: 5
        resources:
          requests:
            cpu: "4"
            memory: 8192Mi
          limits:
            cpu: "7"
            memory: 14336Mi 
        ports:
        - containerPort: 20443
        volumeMounts:
        - mountPath: /etc/coreapi/tls
          name: coreapi-tesla-tls
          readOnly: true
      volumes:
      - secret:
          secretName: coreapi-tesla-tls
        name: coreapi-tesla-tls
      tolerations:
      - key: "vmsize"
        operator: "Equal"
        value: "fastio"
        effect: "NoSchedule"