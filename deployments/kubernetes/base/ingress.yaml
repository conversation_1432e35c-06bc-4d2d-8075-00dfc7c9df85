apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coreapi
  annotations:
    $patch: replace
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Enctype, Content-type, upload-session-id, X-Image-Token, UTM-Source, UTM-Medium, UTM-Campaign, Client-Id, App-Location, Deferred, Language, sentry-trace, baggage"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "Content-disposition, cache-control, content-length, expires, pragma, Pockethealth-Api-Result"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://www.pocket.health, https://pockethealth.com, https://www.pockethealth.com, https://pockethealtstg.wpengine.com"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/proxy-read-timeout: 600
    nginx.ingress.kubernetes.io/proxy-write-timeout: 600
    nginx.ingress.kubernetes.io/service-upstream: true
    nginx.ingress.kubernetes.io/proxy-connect-timeout: 30
spec:
  ingressClassName: nginx-external
