---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: coreapi
spec:
  # Secret names are always required. A secret in the same namespace with
  # the following name will be created.
  secretName: coreapi-tls

  duration: 2160h # The certificate will be valid for 90 days
  renewBefore: 720h # The certificate should be renewed 30 days before it expires
  # The use of the common name field has been deprecated since 2000 and is
  # discouraged from being used.
  isCA: false
  privateKey:
    algorithm: RSA
    size: 2048
  usages:
  - server auth
  issuerRef:
    name: letsencrypt
    kind: ClusterIssuer
    group: cert-manager.io