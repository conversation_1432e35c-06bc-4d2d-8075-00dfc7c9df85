services:
  coreapi:
    image: phcrcacentral0.azurecr.io/coreapi:latest
    platform: linux/x86_64
    container_name: coreapi
    restart: always
    user: "21000:21000"
    ports:
      - 443:20443
    environment:
      AZURE_SUBSCRIPTION_ID: ${AZURE_SUBSCRIPTION_ID}
      AZURE_TENANT_ID: ${AZURE_TENANT_ID}
      AZURE_CLIENT_ID: ${AZURE_CLIENT_ID}
      AZURE_CLIENT_SECRET: ${AZURE_CLIENT_SECRET}
    command: ${PH_ENV}
    networks:
      - local_dev
  mysql:
    image: phcrcacentral0.azurecr.io/mysql:8.0.35
    platform: linux/x86_64
    container_name: apptest_mysql_1
    restart: always
    environment:
      MYSQL_DATABASE: pockethealth
      MYSQL_ROOT_PASSWORD: sqlrootpw
      MYSQL_USER: devuser
      MYSQL_PASSWORD: devpw
    ports:
      - 3306:3306
    networks:
      - local_dev

networks:
  local_dev:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "127.0.0.1"
