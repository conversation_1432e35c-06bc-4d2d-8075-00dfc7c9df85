<html>
  <head>
    <style>
      /* General */
      p,
      h1,
      h2 {
        margin-block-start: 0;
        margin-block-end: 0;
      }

      body {
        font-family: sans-serif;
        font: 400 18px/1.5;
      }

      .label {
        font-size: 10px;
        line-height: 1.2;
        color: #475569;
        white-space: nowrap;
      }

      .info {
        font-size: 14px;
      }

      .title {
        font-size: 24px;
        line-height: 1.2;
      }

      .main-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin: -12px 0;
      }

      .main-content > * {
        margin: 12px 0;
      }

      .divider {
        border-bottom: 1px solid;
        border-color: #cbd5e1;
      }

      .section-heading {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0;
        margin-bottom: 16px;
      }

      .bullet-points {
        padding-inline-start: 16px;
      }

      .bullet-points > li:not(:last-child) {
        margin: 0 0 8px;
      }

      .gap4 {
        margin-bottom: 4px;
      }

      /* Request Section */

      .request-section {
        display: -webkit-box;
        width: 100%;
      }

      .request-subsection {
        margin-right: 32px;
      }

      .request-subsection-email {
        -webkit-box-flex: 1;
      }

      .request-subsection-email .info {
        white-space: normal;
        word-wrap: break-word;
      }

      .request-data {
        margin-top: 0;
        margin-bottom: 0;
      }

      /* Provider Section */

      .provider-section {
        border: 1px solid;
        border-color: #cbd5e1;
        min-height: 88px;
        border-radius: 12px;
        display: -webkit-box;
      }

      .provider-name-subsection {
        display: -webkit-box;
        width: 66.66%;
        -webkit-box-orient: vertical;
        -webkit-box-pack: center;
      }

      .provider-label {
        margin: 16px 24px;
      }

      .provider-logo-subsection {
        width: 33.33%;
        border-left: 1px solid;
        border-color: #cbd5e1;
        -webkit-box-align: center;
        -webkit-box-pack: center;
        display: -webkit-box;
      }

      .provider-name {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        display: block;
      }

      .logo-container {
        margin-bottom: 40px;
      }

      .provider-logo {
        margin: auto;
        max-width: 180px;
        max-height: 40px;
      }

      .requesting-from {
        margin-bottom: 4px;
      }

      /* Patient Details Section */

      .patient-section {
        display: -webkit-box;
        width: 100%;
      }

      .dob {
        padding-left: 2px;
        font-size: 8px;
        color: #475569;
      }

      .delegate-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
      }

      .patient-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-right: 16px;
      }

      .info-table {
        border-spacing: 16px 8px;
        margin: -8px -16px;
        width: 100%;
      }

      .info-table .label {
        width: 1%;
        vertical-align: top;
        padding: 3.5px 0;
      }

      /* Recent Exam Section */

      .recent-exam-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-left: 16px;
      }

      .exam {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .exam-list {
        margin-top: 0;
        margin-bottom: -12px;
        padding-left: 24px;
      }

      /* Consent Section */

      .consent-section {
        margin-bottom: 16px;
      }

      .consent-text {
        line-height: 1.5;
      }

      /* Signature Section */

      .signature {
        width: 200px;
        height: 120px;
        border-bottom: 1px solid;
        border-color: #cbd5e1;
        margin-bottom: 8px;
        padding-bottom: 0;
      }

      /* Extra */

      .zero-top {
        margin-top: 0;
      }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body>
    <div class="logo-container">
      <a href="http://pocket.health">
        <img height="28px" src="{{PocketHealthLogo}}" />
      </a>
    </div>
    <div class="main-content">
      <h1 class="title">Demande de dossiers médicaux</h1>
      <div class="request-section">
        <div class="request-subsection">
          <p class="label gap4">ID de la demande</p>
          <p class="request-data info">{{RequestId}}</p>
        </div>
        <div class="request-subsection">
          <p class="label gap4">Date de création</p>
          <p class="request-data info">{{Date}}</p>
        </div>
        <div class="request-subsection-email">
          <p class="label gap4">Compte</p>
          <p class="request-data info wrap">{{Email}}</p>
        </div>
      </div>
      <div class="provider-section">
        <div class="provider-name-subsection">
          <div class="provider-label">
            <div class="requesting-from label">Demandant de</div>
            <div class="provider-name">{{ProviderName}}</div>
          </div>
        </div>
        <div class="provider-logo-subsection">
          <img class="provider-logo" src="{{ProviderLogo}}" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="patient-section">
        <div class="patient-details-section">
          <h2 class="section-heading padding-bot-12">
            Informations sur le patient
          </h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Nom</td>
                <td class="info">{{FullName}}</td>
              </tr>
              <tr>
                <td class="label">Date de naissance</td>
                <td class="info">
                  {{DateOfBirth}}<span class="dob">AAAA/MM/JJ</span>
                </td>
              </tr>
              {% if HasHealthId %} {% if HealthId.OHIP != "" %}
              <tr>
                <td class="label">OHIP</td>
                <td class="info">{{HealthId.OHIP}}</td>
              </tr>
              {% endif %} {% if HealthId.MRN != ""%}
              <tr>
                <td class="label">MRN</td>
                <td class="info">{{HealthId.MRN}}</td>
              </tr>
              {% endif %} {% if HealthId.BCPHN != "" %}
              <tr>
                <td class="label">BCPHN</td>
                <td class="info">{{HealthId.BCPHN}}</td>
              </tr>
              {% endif %} {% if HealthId.IPN != "" %}
              <tr>
                <td class="label">IPN</td>
                <td class="info">{{HealthId.IPN}}</td>
              </tr>
              {% endif %} {% if HealthId.SSN != "" %}
              <tr>
                <td class="label">SSN</td>
                <td class="info">{{HealthId.SSN}}</td>
              </tr>
              {% endif %} {% if HealthId.AlternativeHealthId != "" %}
              <tr>
                <td class="label">ID alternatif</td>
                <td class="info">{{HealthId.AlternativeHealthId}}</td>
              </tr>
              {% endif %} {% endif %}
              <tr>
                <td class="label">Téléphone</td>
                <td class="info">{{PhoneNumber}}</td>
              </tr>
            </tbody>
          </table>
        </div>
        {% if ExamInformation and ExamInformation.size > 0 %}
        <div class="recent-exam-section">
          <h2 class="section-heading padding-bot-12">Examens récents</h2>
          <ul class="exam-list bullet-points">
            {% for exam in ExamInformation %}
              {% if exam.HasMonth %}
                <li class="exam">{{ exam.ExamType}} - {{exam.ExamMonth}}, {{exam.ExamYear }}</li>
              {% else %}
                <li class="exam">{{ exam.ExamType}} - {{exam.ExamYear }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </div>
        {% endif %}
      </div>
      {% if DelegateFullName != "" %}
      <div class="divider"></div>
      <div class="delegate-details-section">
        <div>
          <h2 class="section-heading padding-bot-12">
            Informations sur les délégués
          </h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Nom</td>
                <td class="info">{{DelegateFullName}}</td>
              </tr>
              <tr>
                <td class="label">Relations</td>
                <td class="info">{{DelegateRelationToPatient}}</td>
              </tr>
              <tr>
                <td class="label">Téléphone</td>
                <td class="info">{{DelegatePhoneNumber}}</td>
              </tr>
              <tr>
                <td class="label">Adresse</td>
                <td class="info">{{DelegateAddress}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {% endif %}
      <div class="divider"></div>
      <div class="consent-section">
        <h2 class="section-heading">
          {% if MinorSignature != "" %}
            Consentement et reconnaissance du délégué
          {% else %}  
            Consentement et reconnaissance
          {% endif %}
        </h2>
        <div class="consent-text info">
            {% if ConsentText != "" %} {{ConsentText}} {% else %}
            <p>
                {% if DelegateFullName != "" %}
                    I, {{DelegateFullName}}, confirme que je suis légalement autorisé à demander et à recevoir l'imagerie et les dossiers médicaux de <b>{{FullName}}</b>. Pour le compte de <b>{{FullName}}</b>, je renonce par la présente à toute réclamation contre <b>{{ProviderName}}</b>, ses médecins, employés et agents à toutes fins utiles en rapport avec ladite communication et la divulgation d'informations contenues dans ledit dossier.
                {% else %}
                    Moi, <b>{{FullName}}</b> renonce par la présente à toute réclamation à l’encontre <b>{{ProviderName}}</b> de ses médecins, employés et agents à quelque fin que ce soit en rapport avec ladite communication et la divulgation d’informations dans lesdits dossiers.
                {% endif %}
            </p>
            <p>
                {% if DelegateFullName != "" %}
                Je comprends que les dossiers du patient seront mis à ma disposition par le biais d'une plateforme de stockage de dossiers sécurisée d'un tiers - à savoir PocketHealth - à travers laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma guise.
                {% else %}
                Je comprends que mes dossiers seront mis à ma disposition par l’intermédiaire d’une plateforme de stockage de dossiers sécurisée d’un tiers, PocketHealth, par laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion.
                {% endif %}
            </p>
            <p> 
                Je comprends que les futurs dossiers, s'ils sont créés, seront également accessibles et pourront être ajoutés à mon compte, à ma discrétion.
            </p>
            <p>
                Je comprends que je pourrai me retirer de cet accès en ligne à tout moment.
            </p>
            <p> 
                {% if DelegateFullName != "" %}
                J’autorise <b>{{ProviderName}}</b> la communication des dossiers du patient aux personnes que j’ai spécifiée dans ce formulaire.
                {% else %}
                J’autorise <b>{{ProviderName}}</b> la communication de mes dossiers aux personnes que j’ai spécifiée dans ce formulaire
                {% endif %}
            </p>
            {% if DelegateFullName != "" %}
                <p>En soumettant cette demande, je confirme que les informations que j'ai fournies ci-dessus sont correctes et exhaustives et que je suis légalement autorisé à faire cette demande au nom du patient.</p>
            {% endif %}
        {% endif %}
        </div>
        <img class="signature" src="{{Signature}}" />
        <p class="info zero-top">
          {% if DelegateFullName != "" %}Signature du délégué{% else %}Signature
          du patient{% endif %}
        </p>
        {% if MinorSignature != "" %}
          <br><br><br>
          <h2 class="section-heading">Consentement et reconnaissance du patient</h2>
          <div class="consent-text info">
            {% if MinorConsentText != "" %} {{MinorConsentText}} {% else %}
              <p>
                Moi, <b>{{FullName}}</b> renonce par la présente à toute réclamation à l’encontre <b>{{ProviderName}}</b> de ses médecins, employés et agents à quelque fin que ce soit en rapport avec ladite communication et la divulgation d’informations dans lesdits dossiers.
              </p>
              <p>
                Je comprends que mes dossiers seront mis à ma disposition par l’intermédiaire d’une plateforme de stockage de dossiers sécurisée d’un tiers, PocketHealth, par laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion.
              </p>
              <p>
                Je comprends que les futurs dossiers, s'ils sont créés, seront également accessibles et pourront être ajoutés à mon compte, à ma discrétion.
              </p>
              <p>
                Je comprends que je pourrai me retirer de cet accès en ligne à tout moment.
              </p>
              <p>
                J’autorise <b>{{ProviderName}}</b> la communication de mes dossiers aux personnes que j’ai spécifiée dans ce formulaire
              </p>
            {% endif %}
          </div>
          <img class="signature" src="{{MinorSignature}}" />
          <p class="info zero-top">Signature du patient</p>
        {% endif %}
      </div>
    </div>
  </body>
</html>
