<html>
  <head>
    <style>
    /* General */
    :root {
        color: #0F172A;
    }
    p,
    h1,
    h2,
    h3 {
        margin-block-start: 0;
        margin-block-end: 0;
        margin: 0;
    }

    body {
        font-family: sans-serif;
        font: 400 18px/1.5;
        letter-spacing: normal;
    }

    html
    body,
    div,
    span,
    section,
    p {
        margin: 0;
        padding: 0;
    }
    
    .txt-grey-600 {
        color: #475569;
    }
      
    .font-size-12 {
        font-size: 12px;
    }
      
    .font-size-14 {
        font-size: 14px;
    }
      
    .font-size-20 {
        font-size: 20px;
    }
      
    .font-weight-500 {
        font-weight: 500;
    }

    .font-weight-700 {
        font-weight: 700;
    }

    .info {
        font-size: 14px;
    }

    .title {
        font-size: 28px;
        line-height: 1.2;
    }
    .content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
    }

    .content > * {
        margin-bottom: 40px; /* Equivalent to gap: 40px; for vertical layout */
    }

    .content > :last-child {
        margin-bottom: 0;
    }

    .divider {
        border-bottom: 1px solid #E2E8F0;
        margin: 0;
        margin-bottom: 24px;
    }

    .bullet-points {
        padding-inline-start: 16px;
    }

    .logo-container {
        margin-bottom: 40px;
    }
    header > :first-child {
        margin-bottom: 24px;
    }

    .receipt-metadata {
        display: -webkit-box;
    }

    .receipt-metadata > * + * {
        margin-left: 32px;
    }

    .receipt-metadata-item {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
    }

    .receipt-metadata-item > :last-child {
        margin-top: 8px;
    }

    .receipt-details {
        padding: 24px;
        border: 1px solid;
        border-color: #cbd5e1;
        min-height: 88px;
        border-radius: 24px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
    }

    .receipt-details > * + * {
        margin-top: 24px;
    }

    .receipt-details-header > :nth-child(2) {
        margin-top: 8px;
    }

    .receipt-details-header > :last-child {
        margin-top: 16px;
    }

    .description {
        line-height: 21px; 
    }

    .payment-detail-row {
        display: -webkit-box;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        font-size: 18px;
        width: 100%;
        -webkit-box-pack: justify;
    }

    .payment-detail-row > span {
        display: -webkit-box;
    }

    .payment-detail-row > .discount-amount {
        color: #007A90;
    }

    .payment-detail-row > b {
        display: -webkit-box;
    }

    .total {
        position: absolute;
        right: 24px;
    }
      
    .charge-details {
        display: -webkit-box;
    }

    .charge-details > * + * {
        margin-left: 16px; /* Equivalent to gap: 16px; */
    }

    .charge-detail-row {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: calc(100% - 56px);
    }

    .charge-detail-row > * + * {
        margin-top: 8px; /* Equivalent to gap: 8px; */
    }

    .masked-card-num {
        letter-spacing: 2px;
        margin-left: 8px;
        margin-right: 4px;
    }

    .card-logo {
        height: 24px;
        width: 40px;
    }

    .footer {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
    }

    .footer > * + * {
        margin-top: 8px; /* Equivalent to gap: 8px; */
    }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body>
    <main class="content">
      <!-- Logo -->
      <div class="logo-container">
        <a href="http://pocket.health">
          <img height="28px" src="{{PocketHealthLogo}}" />
        </a>
      </div>
      
      <header>
          <h1 class="title">Reçu</h1>
          <section class="receipt-metadata">
            <div class="receipt-metadata-item">
              <div class="txt-grey-600 font-size-12">Numéro de commande</div>
              <div class="font-size-14">{{OrderId}}</div>
            </div>
            <div class="receipt-metadata-item">
              <div class="txt-grey-600 font-size-12">Date</div>
              <div class="font-size-14">{{Date}}</div>
            </div>
          </section>
      </header>
      
        <section class="receipt-details">
            <div class="receipt-details-header">
                <h2 class="font-size-20">{% if Plan == "Imaging request" %}Frais de transfert{% else %}Abonnement PocketHealth {{Plan}}{% endif %}</h2>
                <div class="font-size-20 font-weight-500">
                    {{StartDate}}
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.55977 11.5L5.5102 10.4507L9.15452 6.76635H0V5.23366H9.15452L5.5102 1.5552L6.55977 0.5L12 6L6.55977 11.5Z" fill="#0F172A"/>
                    </svg>
                    {{EndDate}}
                </div>
                <div class="description">{% if Plan == "Imaging request" %}Les frais couvrent toutes les images disponibles de votre historique chez le fournisseur demandé, ainsi que toute imagerie supplémentaire effectuée chez le fournisseur demandé au cours des 2 prochaines semaines.{% else %}L'abonnement couvre {% case Plan %}{% when 'Flex' %}un mois de {% when 'Core', 'Unlimited' %}un an de {% endcase %}récupération et stockage illimités de vos dossiers d'imagerie médicale.{% endif %}</div>
            </div>
    
            <div class="divider"></div>
        
        <span class="font-size-14 txt-grey-600">Détails du prix</span>
        
        <div class="payment-detail-row">
          <span>Sous-total</span>
          <span>{{Subtotal}}</span>
        </div>
        {% if DiscountName != "" %}
            <div class="payment-detail-row">
                <span>Rabais - {{DiscountName}}</span>
                <span class="discount-amount">-{{DiscountAmount}}</span>
            </div>
        {% endif %}
        
        <div class="payment-detail-row">
          <span>Taxe</span>
          <span>{{Tax}}</span>
        </div>
        
        <div class="payment-detail-row">
          <b>Total</b>
          <b>{{Total}}</b>
        </div>
        
        <div class="divider"></div>
        
        <span class="font-size-14 txt-grey-600">Paiement</span>
        
        <div class="charge-details">
          <img class="card-logo" src="{{CardLogo}}" />
          <div class="charge-detail-row">
            <div class="payment-detail-row">
              <span>{{CardType}}</span>
              <span class="masked-card-num">••••</span>
              <span>{{LastFour}}</span>
              <span class="font-weight-700 total">{{Total}}</span>
            </div>
            <span class="font-size-14 txt-grey-600">Payé le : {{StartDate}}</span>
          </div>
        </div>
      </section>
  
      <footer class="footer">
        <b class="font-size-12 txt-grey-600">PocketHealth Inc.</b>
          <div class="font-size-12 txt-grey-600">
            Visitez <a class="txt-grey-600" href="https://www.pockethealth.com/fr/">www.pockethealth.com</a> pour en savoir plus.
          </div>
      </footer>
    </main>
  </body>
</html>
