<html>
  <head>
    <style>
      /* General */
      p,
      h1,
      h2 {
        margin-block-start: 0;
        margin-block-end: 0;
      }

      body {
        font-family: sans-serif;
        font: 400 18px/1.5;
      }

      .label {
        font-size: 10px;
        line-height: 1.2;
        color: #475569;
        white-space: nowrap;
      }

      .info {
        font-size: 14px;
      }

      .title {
        font-size: 24px;
        line-height: 1.2;
      }

      .main-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin: -12px 0;
      }

      .main-content > * {
        margin: 12px 0;
      }

      .divider {
        border-bottom: 1px solid;
        border-color: #cbd5e1;
      }

      .section-heading {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0;
        margin-bottom: 16px;
      }

      .bullet-points {
        padding-inline-start: 16px;
      }

      .bullet-points > li:not(:last-child) {
        margin: 0 0 8px;
      }

      .gap4 {
        margin-bottom: 4px;
      }

      /* Request Section */

      .request-section {
        display: -webkit-box;
        width: 100%;
      }

      .request-subsection {
        margin-right: 32px;
      }

      .request-subsection-email {
        -webkit-box-flex: 1;
      }

      .request-subsection-email .info {
        white-space: normal;
        word-wrap: break-word;
      }

      .request-data {
        margin-top: 0;
        margin-bottom: 0;
      }

      /* Provider Section */

      .provider-section {
        border: 1px solid;
        border-color: #cbd5e1;
        min-height: 88px;
        border-radius: 12px;
        display: -webkit-box;
      }

      .provider-name-subsection {
        display: -webkit-box;
        width: 66.66%;
        -webkit-box-orient: vertical;
        -webkit-box-pack: center;
      }

      .provider-label {
        margin: 16px 24px;
      }

      .provider-logo-subsection {
        width: 33.33%;
        border-left: 1px solid;
        border-color: #cbd5e1;
        -webkit-box-align: center;
        -webkit-box-pack: center;
        display: -webkit-box;
      }

      .provider-name {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        display: block;
      }

      .logo-container {
        margin-bottom: 40px;
      }

      .provider-logo {
        margin: auto;
        max-width: 180px;
        max-height: 40px;
      }

      .requesting-from {
        margin-bottom: 4px;
      }

      /* Patient Details Section */

      .patient-section {
        display: -webkit-box;
        width: 100%;
      }

      .dob {
        padding-left: 2px;
        font-size: 8px;
        color: #475569;
      }

      .delegate-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
      }

      .patient-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-right: 16px;
      }

      .info-table {
        border-spacing: 16px 8px;
        margin: -8px -16px;
        width: 100%;
      }

      .info-table .label {
        width: 1%;
        vertical-align: top;
        padding: 3.5px 0;
      }

      /* Recent Exam Section */

      .recent-exam-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-left: 16px;
      }

      .exam {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .exam-list {
        margin-top: 0;
        margin-bottom: -12px;
        padding-left: 24px;
      }

      /* Consent Section */

      .consent-section {
        margin-bottom: 16px;
      }

      .consent-text {
        line-height: 1.5;
      }

      /* Signature Section */

      .signature {
        width: 200px;
        height: 120px;
        border-bottom: 1px solid;
        border-color: #cbd5e1;
        margin-bottom: 8px;
        padding-bottom: 0;
      }

      /* Extra */

      .zero-top {
        margin-top: 0;
      }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body>
    <div class="logo-container">
      <a href="http://pocket.health">
        <img height="28px" src="{{PocketHealthLogo}}" />
      </a>
    </div>
    <div class="main-content">
      <h1 class="title">Medical records request</h1>
      <div class="request-section">
        <div class="request-subsection">
          <p class="label gap4">Request ID</p>
          <p class="request-data info">{{RequestId}}</p>
        </div>
        <div class="request-subsection">
          <p class="label gap4">Created date</p>
          <p class="request-data info">{{Date}}</p>
        </div>
        <div class="request-subsection-email">
          <p class="label gap4">Account</p>
          <p class="request-data info wrap">{{Email}}</p>
        </div>
      </div>
      <div class="provider-section">
        <div class="provider-name-subsection">
          <div class="provider-label">
            <div class="requesting-from label">Requesting from</div>
            <div class="provider-name">{{ProviderName}}</div>
          </div>
        </div>
        <div class="provider-logo-subsection">
          <img class="provider-logo" src="{{ProviderLogo}}" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="patient-section">
        <div class="patient-details-section">
          <h2 class="section-heading padding-bot-12">Patient details</h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Name</td>
                <td class="info">{{FullName}}</td>
              </tr>
              <tr>
                <td class="label">Date of birth</td>
                <td class="info">
                  {{DateOfBirth}}<span class="dob">YYYY/MM/DD</span>
                </td>
              </tr>
              {% if HasHealthId %} {% if HealthId.OHIP != "" %}
              <tr>
                <td class="label">OHIP</td>
                <td class="info">{{HealthId.OHIP}}</td>
              </tr>
              {% endif %} {% if HealthId.MRN != ""%}
              <tr>
                <td class="label">MRN</td>
                <td class="info">{{HealthId.MRN}}</td>
              </tr>
              {% endif %} {% if HealthId.BCPHN != "" %}
              <tr>
                <td class="label">BCPHN</td>
                <td class="info">{{HealthId.BCPHN}}</td>
              </tr>
              {% endif %} {% if HealthId.IPN != "" %}
              <tr>
                <td class="label">IPN</td>
                <td class="info">{{HealthId.IPN}}</td>
              </tr>
              {% endif %} {% if HealthId.SSN != "" %}
              <tr>
                <td class="label">SSN</td>
                <td class="info">{{HealthId.SSN}}</td>
              </tr>
              {% endif %} {% if HealthId.AlternativeHealthId != "" %}
              <tr>
                <td class="label">Alt ID</td>
                <td class="info">{{HealthId.AlternativeHealthId}}</td>
              </tr>
              {% endif %} {% endif %}
              <tr>
                <td class="label">Phone</td>
                <td class="info">{{PhoneNumber}}</td>
              </tr>
            </tbody>
          </table>
        </div>
        {% if ExamInformation and ExamInformation.size > 0 %}
        <div class="recent-exam-section">
          <h2 class="section-heading padding-bot-12">Recent exams</h2>
          <ul class="exam-list bullet-points">
            {% for exam in ExamInformation %}
                {% if exam.HasMonth %}
                  <li class="exam">{{ exam.ExamType}} - {{exam.ExamMonth}}, {{exam.ExamYear }}</li>
            {% else %}
            <li class="exam">{{ exam.ExamType}} - {{exam.ExamYear }}</li>
                {% endif %}
            {% endfor %}
          </ul>
        </div>
        {% endif %}
      </div>
      {% if DelegateFullName != "" %}
      <div class="divider"></div>
      <div class="delegate-details-section">
        <div>
          <h2 class="section-heading padding-bot-12">Delegate details</h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Name</td>
                <td class="info">{{DelegateFullName}}</td>
              </tr>
              <tr>
                <td class="label">Relations</td>
                <td class="info">{{DelegateRelationToPatient}}</td>
              </tr>
              <tr>
                <td class="label">Phone</td>
                <td class="info">{{DelegatePhoneNumber}}</td>
              </tr>
              <tr>
                <td class="label">Address</td>
                <td class="info">{{DelegateAddress}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {% endif %}
      <div class="divider"></div>
      <div class="consent-section">
        <h2 class="section-heading">
          {% if MinorSignature != "" %}
            Delegate consent and acknowledgement
          {% else %}  
            Consent and acknowledgement
          {% endif %}
        </h2>
        <div class="consent-text info">
          {% if ConsentText != "" %} {{ConsentText}} {% else %}
            <p>
                  {% if DelegateFullName != "" %}
                      I, {{DelegateFullName}}, confirm that I am legally authorized to request and receive the medical imaging and records of <b>{{FullName}}</b>. On behalf of <b>{{FullName}}</b>, I hereby waive all claims against <b>{{ProviderName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.
                  {% else %}
                      I, <b>{{FullName}}</b>, hereby waive all claims against <b>{{ProviderName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.
                  {% endif %}
            </p>
            <p>
                  I understand that {% if DelegateFullName != "" %}the patient's{% else %}my{% endif %} records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.
            </p>
            <p>
                  I understand that future records, if/when they are created will also be made available to access and added to my account, at my discretion.
            </p>
            <p>
                  I understand that I will be able to opt out of this online access at any time.
            </p>
            <p>
                  I authorize <b>{{ProviderName}}</b> to release {% if DelegateFullName != "" %}the patient's{% else %}my{% endif %} records to me as I have specified within this form.
            </p>
            {% if DelegateFullName != "" %}
            <p>
                      By submitting this request, I confirm that the information I have provided above is correct and complete and that I am legally authorized to make this request on behalf of the patient.
            </p>
          {% endif %}
          {% endif %}
        </div>
        <img class="signature" src="{{Signature}}" />
        <p class="info zero-top">
          {% if DelegateFullName != "" %}Delegate signature{% else %}Patient
          signature{% endif %}
        </p>
        {% if MinorSignature != "" %}
          <br><br><br>
        <h2 class="section-heading">Patient consent and acknowledgement</h2>
        <div class="consent-text info">
          {% if MinorConsentText != "" %} {{MinorConsentText}} {% else %}
            <p>
                    I, <b>{{FullName}}</b>, hereby waive all claims against <b>{{ProviderName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.
            </p>
            <p>
                    I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.
            </p>
            <p>
                    I understand that future records, if/when they are created will also be made available to access and added to my account, at my discretion.
            </p>
            <p>
                    I understand that I will be able to opt out of this online access at any time.
            </p>
            <p>
                    I authorize <b>{{ProviderName}}</b> to release my records to me as I have specified within this form.
            </p>
          {% endif %}
        </div>
        <img class="signature" src="{{MinorSignature}}" />
        <p class="info zero-top">Patient signature</p>
        {% endif %}
      </div>
    </div>
  </body>
</html>
