<html>
  <head>
    <style>
      /* General */
      p,
      h1,
      h2 {
        margin-block-start: 0;
        margin-block-end: 0;
      }

      body {
        font-family: sans-serif;
        font: 400 18px/1.5;
      }

      .label {
        font-size: 10px;
        line-height: 1.2;
        color: #475569;
        white-space: nowrap;
      }

      .info {
        font-size: 14px;
      }

      .title {
        font-size: 24px;
        line-height: 1.2;
      }

      .main-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin: -12px 0;
      }

      .main-content > * {
        margin: 12px 0;
      }

      .divider {
        border-bottom: 1px solid;
        border-color: #cbd5e1;
      }

      .section-heading {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0;
        margin-bottom: 16px;
      }

      .bullet-points {
        padding-inline-start: 16px;
      }

      .bullet-points > li:not(:last-child) {
        margin: 0 0 8px;
      }

      .gap4 {
        margin-bottom: 4px;
      }

      /* Request Section */

      .request-section {
        display: -webkit-box;
        width: 100%;
      }

      .request-subsection {
        margin-right: 32px;
      }

      .request-subsection-email {
        -webkit-box-flex: 1;
      }

      .request-subsection-email .info {
        white-space: normal;
        word-wrap: break-word;
      }

      .request-data {
        margin-top: 0;
        margin-bottom: 0;
      }

      /* Provider Section */

      .provider-section {
        border: 1px solid;
        border-color: #cbd5e1;
        min-height: 88px;
        border-radius: 12px;
        display: -webkit-box;
      }

      .provider-name-subsection {
        display: -webkit-box;
        width: 66.66%;
        -webkit-box-orient: vertical;
        -webkit-box-pack: center;
      }

      .provider-label {
        margin: 16px 24px;
      }

      .provider-logo-subsection {
        width: 33.33%;
        border-left: 1px solid;
        border-color: #cbd5e1;
        -webkit-box-align: center;
        -webkit-box-pack: center;
        display: -webkit-box;
      }

      .provider-name {
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
        display: block;
      }

      .logo-container {
        margin-bottom: 40px;
      }

      .provider-logo {
        margin: auto;
        max-width: 180px;
        max-height: 40px;
      }

      .requesting-from {
        margin-bottom: 4px;
      }

      /* Patient Details Section */

      .patient-section {
        display: -webkit-box;
        width: 100%;
      }

      .dob {
        padding-left: 2px;
        font-size: 8px;
        color: #475569;
      }

      .delegate-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
      }

      .patient-details-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-right: 16px;
      }

      .info-table {
        border-spacing: 16px 8px;
        margin: -8px -16px;
        width: 100%;
      }

      .info-table .label {
        width: 1%;
        vertical-align: top;
        padding: 3.5px 0;
      }

      /* Recent Exam Section */

      .recent-exam-section {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        width: 50%;
        box-sizing: border-box;
        padding-left: 16px;
      }

      .exam {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .exam-list {
        margin-top: 0;
        margin-bottom: -12px;
        padding-left: 24px;
      }

      /* Consent Section */

      .consent-section {
        margin-bottom: 16px;
      }

      .consent-text {
        line-height: 1.5;
      }

      /* Signature Section */

      .signature {
        width: 200px;
        height: 120px;
        border-bottom: 1px solid;
        border-color: #cbd5e1;
        margin-bottom: 8px;
        padding-bottom: 0;
      }

      /* Extra */

      .zero-top {
        margin-top: 0;
      }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body>
    <div class="logo-container">
      <a href="http://pocket.health">
        <img height="28px" src="{{PocketHealthLogo}}" />
      </a>
    </div>
    <div class="main-content">
      <h1 class="title">Solicitud de Registros Médicos</h1>
      <div class="request-section">
        <div class="request-subsection">
          <p class="label gap4">ID de solicitud</p>
          <p class="request-data info">{{RequestId}}</p>
        </div>
        <div class="request-subsection">
          <p class="label gap4">Fecha de Creación</p>
          <p class="request-data info">{{Date}}</p>
        </div>
        <div class="request-subsection-email">
          <p class="label gap4">Cuenta</p>
          <p class="request-data info wrap">{{Email}}</p>
        </div>
      </div>
      <div class="provider-section">
        <div class="provider-name-subsection">
          <div class="provider-label">
            <div class="requesting-from label">Solicitando de</div>
            <div class="provider-name">{{ProviderName}}</div>
          </div>
        </div>
        <div class="provider-logo-subsection">
          <img class="provider-logo" src="{{ProviderLogo}}" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="patient-section">
        <div class="patient-details-section">
          <h2 class="section-heading padding-bot-12">Datos del paciente</h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Nombre</td>
                <td class="info">{{FullName}}</td>
              </tr>
              <tr>
                <td class="label">Fecha de nacimiento</td>
                <td class="info">
                  {{DateOfBirth}}<span class="dob">AAAA/MM/DD</span>
                </td>
              </tr>
              {% if HasHealthId %} {% if HealthId.OHIP != "" %}
              <tr>
                <td class="label">OHIP</td>
                <td class="info">{{HealthId.OHIP}}</td>
              </tr>
              {% endif %} {% if HealthId.MRN != ""%}
              <tr>
                <td class="label">MRN</td>
                <td class="info">{{HealthId.MRN}}</td>
              </tr>
              {% endif %} {% if HealthId.BCPHN != "" %}
              <tr>
                <td class="label">BCPHN</td>
                <td class="info">{{HealthId.BCPHN}}</td>
              </tr>
              {% endif %} {% if HealthId.IPN != "" %}
              <tr>
                <td class="label">IPN</td>
                <td class="info">{{HealthId.IPN}}</td>
              </tr>
              {% endif %} {% if HealthId.SSN != "" %}
              <tr>
                <td class="label">SSN</td>
                <td class="info">{{HealthId.SSN}}</td>
              </tr>
              {% endif %} {% if HealthId.AlternativeHealthId != "" %}
              <tr>
                <td class="label">ID alternativo</td>
                <td class="info">{{HealthId.AlternativeHealthId}}</td>
              </tr>
              {% endif %} {% endif %}
              <tr>
                <td class="label">Teléfono</td>
                <td class="info">{{PhoneNumber}}</td>
              </tr>
            </tbody>
          </table>
        </div>
        {% if ExamInformation and ExamInformation.size > 0 %}
        <div class="recent-exam-section">
          <h2 class="section-heading padding-bot-12">Exámenes recientes</h2>
          <ul class="exam-list bullet-points">
            {% for exam in ExamInformation %}
              {% if exam.HasMonth %}
                <li class="exam">{{ exam.ExamType}} - {{exam.ExamMonth}}, {{exam.ExamYear }}</li>
              {% else %}
                <li class="exam">{{ exam.ExamType}} - {{exam.ExamYear }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </div>
        {% endif %}
      </div>
      {% if DelegateFullName != "" %}
      <div class="divider"></div>
      <div class="delegate-details-section">
        <div>
          <h2 class="section-heading padding-bot-12">Datos del delegado</h2>
          <table class="info-table">
            <tbody>
              <tr>
                <td class="label">Nombre</td>
                <td class="info">{{DelegateFullName}}</td>
              </tr>
              <tr>
                <td class="label">Relaciones</td>
                <td class="info">{{DelegateRelationToPatient}}</td>
              </tr>
              <tr>
                <td class="label">Teléfono</td>
                <td class="info">{{DelegatePhoneNumber}}</td>
              </tr>
              <tr>
                <td class="label">Dirección</td>
                <td class="info">{{DelegateAddress}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {% endif %}
      <div class="divider"></div>
      <div class="consent-section">
        <h2 class="section-heading">
          {% if MinorSignature != "" %}
          Consentimiento y Reconocimiento del delegado
          {% else %}  
          Consentimiento y Reconocimiento
          {% endif %}
        </h2>
        <div class="consent-text info">
          {% if ConsentText != "" %} {{ConsentText}} {% else %}
            <p>
                {% if DelegateFullName != "" %}
                    I, {{DelegateFullName}}, confirmo que estoy legalmente autorizado para solicitar y recibir imágenes médicas y registros de <b>{{FullName}}</b>. En nombre de <b>{{FullName}}</b> , por la presente renuncio todas las reclamaciones contra <b>{{ProviderName}}</b>, sus médicos, empleados y agentes para cualquier propósito en relación con dicha comunicación y divulgación de información en dicho registros.
                {% else %}
                    Yo, <b>{{FullName}}</b>, por la presente renuncia a todos los reclamos contra <b>{{ProviderName}}</b>, sus médicos, empleados y agentes a todos los efectos en relación con dicha comunicación y divulgación de información en dichos registros.
                {% endif %}
            </p>
            <p>
                {% if DelegateFullName != "" %}
                    Entiendo que los registros del paciente estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción.
                {% else %}
                    Entiendo que mis registros estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción.
                {% endif %}
            </p>
            <p>
                Entiendo que los registros futuros, si / cuando se crean, también serán disponible para acceder y agregar a mi cuenta, a mi discreción.
            </p>
            <p>
                Entiendo que podré rechazar este acceso en línea en cualquier momento.
            </p>
            <p> 
                {% if DelegateFullName != "" %}
                    Autorizo a <b>{{ProviderName}}</b> para entregarme los registros del paciente como lo he especificado en este formulario.
                {% else %}
                    Autorizo a <b>{{ProviderName}}</b> para entregarme mis registros como he especificado en este formulario.
                {% endif %}
            </p>
            {% if DelegateFullName != "" %}
                <p>
                    Al enviar esta solicitud, confirmo que la información que he facilitado anteriormente es correcta y completa y que estoy legalmente autorizado para realizar esta solicitud en nombre del paciente.
                </p>
            {% endif %}
          {% endif %}
        </div>
        <img class="signature" src="{{Signature}}" />
        <p class="info zero-top">
          {% if DelegateFullName != "" %}Firma del delegado{% else %}Firma del
          paciente{% endif %}
        </p>
        {% if MinorSignature != "" %}
          <br><br><br>
          <h2 class="section-heading">Consentimiento y Reconocimiento del paciente</h2>
          <div class="consent-text info">
            {% if MinorConsentText != "" %} {{MinorConsentText}} {% else %}
              <p>
                Yo, <b>{{FullName}}</b>, por la presente renuncia a todos los reclamos contra <b>{{ProviderName}}</b>, sus médicos, empleados y agentes a todos los efectos en relación con dicha comunicación y divulgación de información en dichos registros.
              </p>
              <p>
                Entiendo que mis registros estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción.
              </p>
              <p>
                Entiendo que los registros futuros, si / cuando se crean, también serán disponible para acceder y agregar a mi cuenta, a mi discreción.
              </p>
              <p>
                Entiendo que podré rechazar este acceso en línea en cualquier momento.
              </p>
              <p>
                Autorizo a <b>{{ProviderName}}</b> para entregarme mis registros como he especificado en este formulario.
              </p>
            {% endif %}
          </div>
          <img class="signature" src="{{MinorSignature}}" />
          <p class="info zero-top">Firma del paciente</p>
        {% endif %}
      </div>
    </div>
  </body>
</html>
