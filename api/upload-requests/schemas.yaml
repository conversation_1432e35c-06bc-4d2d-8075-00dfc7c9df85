components:
  schemas:
    UploadRequestDestinationSetBy:
      type: string
      description: Specifies whether the destination was set by the provider or the uploader.
      enum:
        - PROVIDER
        - UPLOADER
    UploadRequestUploaderType:
      type: string
      description: Identifies whether the uploader is a provider or a patient.
      enum:
        - PROVIDER
        - PATIENT
    UploadRequestType:
      type: string
      description: Identifies whether the upload request is a solicited or unsolicited request.
      enum:
        - SOLICITED
        - UNSOLICITED
    UploadRequestUploaderContactMethod:
      type: string
      enum:
        - PHONE_NUMBER
        - EMAIL
    UploadRequestState:
      type: string
      description: Represents the current state of the upload request.
      enum:
        - CANCELLED
        - COMPLETED
        - CREATED
        - DECLINED
        - FULFILLING
        - FULFILLMENT_EXPIRED
        - NOTIFICATION_FAILED
        - NOTIFIED
    UploadRequestStudy:
      type: object
      description: A study that is part of an Upload Request
      required:
        - id
        - study_instance_uid
        - instance_count
      properties:
        id:
          type: integer
          format: int64
          description: The identifier of the upload request study (this is not dicom study uid)
        study_instance_uid:
          $ref: "../common/schemas.yaml#/components/schemas/DicomUID"
          description: The DICOM study instance uid tag value
        study_description:
          type: string
          description: The DICOM study description tag value
        patient_name:
          type: string
          description: The DICOM patient name tag value
        patient_birth_date:
          type: string
          description: The DICOM patient birth date tag value
        modality:
          type: string
          description: The DICOM modality tag value
        accession_number:
          type: string
          description: The DICOM accession number tag value
        study_date:
          type: string
          description: The DICOM study date tag value.
        uploader_notes:
          type: string
          description: The notes attached by the uploader
          maxLength: 50000
        instance_count:
          type: integer
          format: int64
          description: The number of instances associated with this upload request study.
    GetProviderDetailsResponse:
      type: object
      description: Response object containing provider details
      required:
        - id
        - name
        - logo
        - destinations
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        logo:
          $ref: "#/components/schemas/ProviderLogo"
        destinations:
          type: array
          items:
            $ref: "#/components/schemas/ProviderDestination"
    UploadRequestStudyResponse:
      type: object
      description: Response object containing an upload request study.
      required:
        - study
      properties:
        study:
          $ref: "#/components/schemas/UploadRequestStudy"
    UploadRequestStudiesResponse:
      type: object
      description: Response object containing a list of upload request studies.
      required:
        - studies
      properties:
        studies:
          type: array
          items:
            $ref: "#/components/schemas/UploadRequestStudy"
    AuthenticateUploadRequestBody:
      type: object
      description: The fields required to identify and authenticate a solicited upload request
      required:
        - security_code
        - birth_date
      properties:
        security_code:
          type: string
          pattern: "^[a-zA-Z0-9]{9}$" # 9 character alphanumeric string
        birth_date:
          type: string
          format: date
    CreateUploadRequestBody:
      type: object
      description: The fields required to create an unsolicited upload request
      required:
        - provider_id
        - provider_destination_id
        - patient_name
        - patient_birth_date
        - uploader_type
        - contact_name
        - contact_method
        - contact_information
      properties:
        provider_id:
          type: integer
          format: int64
        provider_destination_id:
          type: integer
          format: int64
        patient_name:
          type: string
        patient_birth_date:
          type: string
          format: date
        uploader_type:
          $ref: "#/components/schemas/UploadRequestUploaderType"
        contact_name:
          type: string
        contact_method:
          $ref: "#/components/schemas/UploadRequestUploaderContactMethod"
        contact_information:
          type: string
    DeclineUploadRequestBody:
      type: object
      description: The fields for declining an upload request
      required:
        - security_code
        - birth_date
        - decline_reason
      properties:
        security_code:
          type: string
          pattern: "^[a-zA-Z0-9]{9}$" # 9 character alphanumeric string
        birth_date:
          type: string
          format: date # e.g. 1994-03-19
        decline_reason:
          type: string
          minLength: 1
    SubmitStudyNotesBody:
      type: object
      description: The upload request study properties to update.
      required:
        - uploader_notes
      properties:
        uploader_notes:
          type: string
          description: Updates the study's uploader notes if not null. If empty string, clears the uploader notes.
          nullable: true
          maxLength: 50000
    GetUploadRequestResponse:
      type: object
      description: The Upload Request details that an uploader can retrieve.
      required:
        - request_type
        - state
        - reference_code
        - patient_name
        - patient_birth_date
      properties:
        provider_destination_id:
          type: integer
          format: int64
        provider_destination_set_by:
          $ref: "#/components/schemas/UploadRequestDestinationSetBy"
        request_type:
          $ref: "#/components/schemas/UploadRequestType"
        state:
          $ref: "#/components/schemas/UploadRequestState"
        reference_code:
          type: string
        patient_name:
          type: string
        patient_birth_date:
          type: string
          format: date
        uploader_type:
          $ref: "#/components/schemas/UploadRequestUploaderType"
        uploader_contact_name:
          type: string
        uploader_contact_method:
          $ref: "#/components/schemas/UploadRequestUploaderContactMethod"
        uploader_contact_information:
          type: string
    ProviderDestination:
      type: object
      description: A Provider Destination object representing a destination within a provider's environment
      required:
        - id
        - label
      properties:
        id:
          type: integer
          format: int64
          description: The identifier for this provider destination
        label:
          type: string
          description: The local address book label from the provider
    ProviderLogo:
      type: object
      description: Details needed to render a provider logo
      required:
        - path
        - width
        - height
      properties:
        path:
          type: string
        width:
          type: integer
          format: int64
        height:
          type: integer
          format: int64
    UpdateUploadRequestBody:
      type: object
      description: The fields for updating upload request contact details or provider destination.
      properties:
        provider_destination_id:
          type: integer
          format: int64
          description: The id of the provider destination to update the upload request to.
        uploader_contact_name:
          type: string
          description: The contact name of the uploader.
        uploader_contact_method:
          $ref: "#/components/schemas/UploadRequestUploaderContactMethod"
          description: An enum describing the method of contact in "uploader_contact_information"
        uploader_contact_information:
          type: string
          description: The contact information of the format described in "uploader_contact_method"
    ErrorResponse:
      type: object
      required:
        - message
        - code
      properties:
        message:
          type: string
        code:
          type: string
