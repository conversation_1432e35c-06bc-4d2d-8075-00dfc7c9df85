paths:
  /v1/upload-request/providers/{subdomain}:
    get:
      description: Get record retrieval metadata for providers with record retrieval configured.
      operationId: get-provider-details-v1
      tags:
        - record-retrieval
      parameters:
        - name: subdomain
          in: path
          description: The subdomain associated with the provider.
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully got the list of studies for this upload request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/GetProviderDetailsResponse"
        "404":
          description: The provider was not found or does not have record retrieval enabled.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/authenticate:
    post:
      description:
        Authenticate a security code and birth date for a solicited upload request
        and create a session JWT for this request.
      operationId: authenticate-upload-request-v1
      tags:
        - record-retrieval
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas.yaml#/components/schemas/AuthenticateUploadRequestBody"
      responses:
        "200":
          description: Successfully authenticated upload request.
          content:
            application/json:
              schema:
                type: object
        "404":
          description: No valid request found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request:
    get:
      description: Get details about the upload request (limited to information relevant to the uploader). ID is retrieved from the JWT.
      operationId: get-upload-request-v1
      tags:
        - record-retrieval
      responses:
        "200":
          description: Successfully got the upload request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/GetUploadRequestResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
    post:
      description: Create an unsolicited upload request and create a session JWT for this request.
      operationId: create-upload-request-v1
      tags:
        - record-retrieval
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas.yaml#/components/schemas/CreateUploadRequestBody"
      responses:
        "200":
          description: Successfully created a new upload request.
          content:
            application/json:
              schema:
                type: object
        "400":
          description: Bad Request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
    patch:
      description: Update an upload request's contact details and/or provider destination id.
      operationId: update-upload-request-v1
      tags:
        - record-retrieval
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas.yaml#/components/schemas/UpdateUploadRequestBody"
      responses:
        "200":
          description: Successfully updated the Upload Request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/GetUploadRequestResponse"
        "400":
          description: Invalid request body.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "403":
          description: Destination set by provider and cannot be changed.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The Upload Request is not in a state where it can be changed.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/decline:
    post:
      description: Decline a solicited upload request with a reason.
      operationId: decline-upload-request-v1
      tags:
        - record-retrieval
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas.yaml#/components/schemas/DeclineUploadRequestBody"
      responses:
        "200":
          description: Successfully declined the upload request.
          content:
            application/json:
              schema:
                type: object
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The Upload Request is not in a state where it can be declined.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/studies:
    get:
      description: Get a list of studies associated with this upload request.
      operationId: list-upload-request-studies-v1
      tags:
        - record-retrieval
      responses:
        "200":
          description: Successfully got the list of studies for this upload request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/UploadRequestStudiesResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/studies/{id}:
    get:
      description: Get a study by id from this upload request.
      operationId: get-upload-request-study-by-id-v1
      tags:
        - record-retrieval
      parameters:
        - name: id
          in: path
          description: The id of the study to get from this upload request.
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully got the study from its id.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/UploadRequestStudyResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request or Study not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
    delete:
      description: Delete a study from an upload request.
      operationId: delete-upload-request-study-v1
      tags:
        - record-retrieval
      parameters:
        - name: id
          in: path
          description: The id of the study to delete from this upload request.
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Successfully deleted the study from the upload request.
        "400":
          description: The study is not associated with this upload request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request or Study not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The Upload Request is not in a state where study notes can be changed.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
    patch:
      description: Update study details in an upload request.
      operationId: update-upload-request-study-v1
      tags:
        - record-retrieval
      parameters:
        - name: id
          in: path
          description: The id of the study to update within this upload request.
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./schemas.yaml#/components/schemas/SubmitStudyNotesBody"
      responses:
        "200":
          description: Successfully updated the study.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/UploadRequestStudyResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request or Study not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The Upload Request is not in a state where studies can be changed.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/instances:
    post:
      description: Uploads an instance for this upload request.
      operationId: upload-request-upload-instance-v1
      tags:
        - record-retrieval
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: Successfully uploaded the instance.
          content:
            application/json:
              schema:
                type: object
        "400":
          description: Bad Request.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The upload request is not in a state where instances can be uploaded.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
  /v1/upload-request/submit:
    post:
      description: Submit the upload request to the provider.
      operationId: submit-upload-request-v1
      tags:
        - record-retrieval
      responses:
        "200":
          description: Successfully submitted the upload request.
          content:
            application/json:
              schema:
                type: object
        "401":
          description: Invalid Authentication.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "404":
          description: Upload Request not found.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
        "409":
          description: The upload request is not in a state where it can be submitted.
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/ErrorResponse"
