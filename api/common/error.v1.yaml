# Common error response schema for all PocketHealth APIs.
# See https://www.notion.so/pockethealth/Nicer-service-errors-1cb0d4c6794380ada8a8c4c7bd8c6ba3?pvs=4
components:
  schemas:
    Error:
      type: object
      title: Common error response schema used across PocketHealth APIs
      properties:
        code:
          type: string
          description: >-
            Machine-readable, service-specific error code (enum)

            A service-specific string constant (enum) representing the type of
            error that has been encountered. Generally automatically
            interpreted by the caller (as opposed to displayed to the user).
            The convention used for these enum values is typically an all-caps,
            snake-cased string. See the provided examples.

            Be careful when adding new codes so as to not leak information that
            attackers could use to compromise our services.
          examples:
            create-failed:
              value: CREATE_FAILED
              summary: Creation of an entity failed
            invalid-request:
              value: INVALID_REQUEST
              summary: Failed to parse the request body
            validation-failed:
              value: VALIDATION_FAILED
              summary: Validation of user-entered data failed

        message:
          type: string
          description: >-
            Human-readable error message

            A human-readable error message explaining the error code.

            When crafting human-readable error messages, please stick to the
            following guidelines: https://www.notion.so/pockethealth/User-facing-error-message-style-guide-1e00d4c6794380af96a4d99f2270d49e?pvs=4

            Be careful when providing detail in these error messages so as to
            not leak information that attackers could use to compromise our
            services.
          examples:
            create-failed:
              value: Creation of an entity failed
            invalid-request:
              value: Failed to parse the request body
            validation-failed:
              value: Validation of user-entered data failed

        validation_errors:
          type: array
          items:
            summary: Errors encountered when validating user-entered data
            $ref: "#/components/schemas/ValidationError"
        
        # See https://swagger.io/docs/specification/v3_0/data-models/data-types/#any-type
        additional_details:
          nullable: true
          summary: Arbitrary error-related details whose schema depends on the specific error code
        
        context:
          $ref: "#/components/schemas/ErrorContext"

      required:
        - code
        - correlation_id

    ValidationError:
      type: object
      description: An error encountered when validating user-entered data
      properties:
        field:
          type: string
          description: Machine-readable identifier for the field containing the error
        
        code:
          type: string
          description: Machine-readable validation error code (enum)

        message:
          type: string
          description: Human-readable validation error message

        additional_details:
          nullable: true
          summary: Arbitrary error-related details whose schema depends on the specific validation error code

      required:
        - field
        - code

    ErrorContext:
      type: object
      description: Context relating to a request/response to facilitate tracing/debugging
      properties:
        trace_id:
          type: string
          description: >-
            Trace/correlation ID for use in tracing/debugging

            A trace/correlation ID used in tracing the causes of specific
            errors.

            If a trace/correlation ID is supplied as part of the initial
            request, most PocketHealth services should use that same
            correlation ID throughout the lifecycle of handling that request -
            including returning the same correlation ID. If one was not
            supplied, services' middleware should automatically generate one.

            See https://opentelemetry.io/docs/concepts/signals/traces/

        span_id:
          type: string
          description: >-
            Span ID for use in tracing/debugging

            A span ID used in tracing the causes of specific errors.

            A span usually relates to a specific unit of work. Spans are
            composed together to form traces.

            See https://opentelemetry.io/docs/concepts/signals/traces/#spans

      required:
        - trace_id
