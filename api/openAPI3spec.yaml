openapi: 3.0.0
info:
  title: Core API
  version: "1.0"
  description: Core API for PocketHealth
  contact:
    name: <PERSON>
    email: <EMAIL>
servers:
  - url: "http://localhost:3000"
tags:
  - name: appointmentreminders
  - name: features
  - name: v2healthrecords
  - name: images
  - name: internal
  - name: meddream
  - name: payment
  - name: plans
  - name: providers
  - name: refer
  - name: reports
  - name: requests
  - name: rho
  - name: secondopinion
  - name: shares
  - name: subscriptions
  - name: transfers
  - name: users
  - name: v1 physician accounts
  - name: v2shares
  - name: v2transfers
  - name: recordstreaming
  - name: record-retrieval
  - name: upload-requests
paths:
  /v1/providers:
    get:
      summary: Get providers
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Provider"
        "500":
          description: Internal Server Error
      operationId: get-providers
      parameters:
        - schema:
            type: string
          in: query
          name: searchTerm
      description: "Get list of participating providers, filter by searchTerm query parameter."
    parameters: []
  /v1/providers/{providerId}/formConfig:
    get:
      summary: Get request form config
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RequestFormConfig"
        "404":
          description: Provider with id not found.
        "500":
          description: Internal Server Error
      operationId: get-provider-formConfig
      description: Get request form field options to build a request form for this provider.
    parameters:
      - schema:
          type: integer
        name: providerId
        in: path
        required: true
  /v1/providers/providerConfig:
    get:
      summary: Get provider level configs.
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProviderConfig"
        "500":
          description: Internal Server Error
      operationId: get-provider-config
      description: Get provider level settings to enable/disable various settings in the frontend.
  /v1/requests:
    post:
      summary: Create a new request
      operationId: post-requests
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: Signed request form
                properties:
                  requestId:
                    type: string
                  consentPdf:
                    type: string
                    format: binary
                  registerPin:
                    type: string
                    deprecated: true
        "400":
          description: Bad Request
        "402":
          description: Payment Required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentError"
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: |-
        Initiate a new request.
        Returns PDF form including signature.
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/NewRequest"
    get:
      summary: Get pending requests
      operationId: get-requests
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PendingRequest"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: Get requests for the authenticated user
      security:
        - jwtBearer: []
  /v1/requests/uph:
    post:
      summary: Create a new UPH request
      operationId: post-requests-uph
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: Signed request form
                properties:
                  requestId:
                    type: string
                  consentPdf:
                    type: string
                    format: binary
                  registerPin:
                    type: string
                    deprecated: true
        "400":
          description: Bad Request
        "402":
          description: Payment Required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentError"
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: |-
        Initiate a new UPH request.
        Returns PDF form including signature.
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/NewUPHRequest"
  /v1/shares:
    get:
      summary: Get shares for a user
      tags:
        - shares
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Share"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "500":
          description: Internal Server Error
      operationId: get-shares
      description: "As a logged in patient, get a list of all shares made."
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: integer
          in: query
          name: limit
          description: For pagination
        - schema:
            type: integer
          in: query
          name: offset
          description: For pagination
    post:
      summary: Create a new share
      operationId: post-shares
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "500":
          description: Internal Server Error
      description: Create a new share for a logged in user
      security:
        - jwtBearer: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Share"
      tags:
        - shares
  /v1/images/{imageID}:
    parameters:
      - schema:
          type: string
        name: imageID
        in: path
        required: true
    get:
      summary: Get image
      tags:
        - images
      responses:
        "200":
          description: OK
          content:
            image/png:
              schema:
                type: string
                format: binary
            application/dicom:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Image with id <imageId> not found.
        "500":
          description: Internal Server Error
      operationId: get-image-byID
      description: |-
        Get image by image ID.
        Both logged in users and validated share viewers can access.
        To retrieve image as PNG, set Accept header to image/png.
        To retrieve image as DICOM, set Accept header to application/dicom.
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
            enum:
              - application/dicom
              - image/png
          in: header
          name: Accept
          description: Tell the API what format to return the image
          required: true
        - schema:
            type: string
          in: header
          name: X-Image-Token
          description: Authentication token for image access
  /v1/reports/{reportId}:
    parameters:
      - schema:
          type: string
        name: reportId
        in: path
        required: true
    get:
      summary: Get report
      tags:
        - reports
      responses:
        "200":
          description: OK
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Report with id <reportId> not found or has no permission to access report
        "500":
          description: Internal Server Error
      operationId: get-report-byId
      description: "Get report by report ID. When Accept header set to application/pdf it will be returned as a PDF. When set to image/png, returned as image."
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
            enum:
              - application/pdf
              - image/png
              - text/html
          in: header
          name: Accept
          description: Tell the API what format to return the report
          required: true
  /v1/reports/{reportId}/views:
    parameters:
      - schema:
          type: string
        name: reportId
        in: path
        required: true
    post:
      summary: Post report views
      tags:
        - reports
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: post-report-views
      description: "Post that report has been viewed."
  /v1/users:
    parameters: []
    post:
      summary: Create a user
      operationId: post-users-userId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Create a new user
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterData"
        description: ""
      tags:
        - users
      parameters: []
  /v1/users/notifications:
    parameters:
      - schema:
          type: boolean
          default: false
        name: include_read
        in: query
        required: false
    get:
      summary: Get user notifications
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Notifications"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-user-notifications
      description: Get a list of all user unread notifications.
    post:
      summary: Create a new user notification
      operationId: post-user-notification
      tags:
        - users
      responses:
        "200":
          description: OK
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Notification"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /v1/users/notifications/{notificationId}:
    parameters:
      - schema:
          type: string
        name: notificationId
        in: path
        required: true
    put:
      summary: Mark notification read
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NotificationRead"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: put-notification-read-byId
      description: Mark notification read with the given ID
    delete:
      summary: Delete the current notification
      tags:
        - users
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /v1/users/exams:
    parameters: []
    get:
      summary: Get exams
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Exam"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-user-exams
      description: |-
        Get a list of all uploaded exams for the current user.
        Use query parameter to filter by activated or locked exams. Default is activated.
      security:
        - jwtBearer: []
        - phSignature: []
      parameters:
        - schema:
            type: boolean
            default: true
          in: query
          name: activated
          description: filter exams by whether or not they are activated
        - name: include_reports
          schema:
            type: boolean
          in: query
          description: Include reports in response
        - name: uid
          in: query
          description: Filter by UID
          style: form
          explode: false
          schema:
            type: string
        - name: account_id
          schema:
            type: string
          in: query
          description: Filter by account ID
  /v1/users/exams/{examId}:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    get:
      summary: Get exam
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Exam"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-user-exam-byId
      description: "Get exam by ID, including image and report ID lists"
      security:
        - jwtBearer: []
  /v1/users/exams/{examId}/organviz:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    get:
      summary: Get organ visualizations for exam
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  organs:
                    type: array
                    items:
                      $ref: "#/components/schemas/OrganVisualization"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-organ-visualization-by-exam-id
      description: "Get organ visualizations for all reports in an exam"
      security:
        - jwtBearer: []
  /v1/users/exams/{examId}/organviz/interactive:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    get:
      summary: Get interactive organ visualizations for exam
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InteractiveOrganVisualization"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-interactive-organ-visualization-by-exam-id
      description: "Get organ visualizations for all reports in an exam"
      security:
        - jwtBearer: []
  /v1/users/exams/{examuuid}/thumbnail:
    parameters:
      - schema:
          type: string
        name: examuuid
        in: path
        required: true
    get:
      summary: Get exam thumbnail
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/png:
              schema:
                type: string
                format: binary
        "401":
          description: Unauthorized
        "404":
          description: Not Found
      description: "For a logged in user with a specific exam uuid or an authenticated share viewer, returns a PNG thumbnail of the first file for said exam"
      security:
        - jwtBearer: []
  /v1/short-urls/{slug}:
    parameters:
      - schema:
          type: string
        name: slug
        in: path
        required: true
    get:
      summary: Get the original URL for the provided slug
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShortUrl"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-short-urls
      description: |-
        Get the original URL for the provided slug
  /v1/short-urls:
    parameters: []
    post:
      summary: Generate a slug for the provided URL
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                url:
                  type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShortUrl"
        "401":
          description: Unauthorized
        "400":
          description: Bad Request
        "422":
          description: Unprocessable Entity
        "500":
          description: Internal Server Error
      operationId: post-short-urls
      description: |-
        Generate a slug for the provided URL
      security:
        - phSignature: []
  /v1/shares/{shareId}/revoke:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    put:
      summary: Revoke share permissions
      operationId: put-revoke-share
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - shares
      description: Revoke share permissions (performed by a logged in patient).
      security:
        - jwtBearer: []
  /v1/shares/{shareId}/reshare:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    post:
      summary: Reshare
      operationId: post-reshare
      responses:
        "200":
          description: OK
          content:
            application/pdf:
              schema:
                type: string
                format: binary
                description: response will only be populated for print shares
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: "Regenerate the print page, refax, or re-email an existing share (performed by logged in patient)"
      tags:
        - shares
      security:
        - jwtBearer: []
  /v1/shares/validate:
    post:
      summary: Validate share viewer
      operationId: post-shares-validate
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: |
                  token
                properties:
                  token:
                    type: string
                  requiresNameAuth:
                    type: boolean
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      description: "Validate a share viewer - For email viewers: with shareID and pin; For print viewers: with viewcode and DOB; For extended print viewers, with lastName and token in Authorization header. 200 response returns a token."
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShareCredentials"
      tags:
        - shares
  /v1/users/logout:
    delete:
      summary: Logout
      operationId: delete-users-logout
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
      tags:
        - users
      description: Log out the current user
      security:
        - jwtBearer: []
    parameters: []
  /v1/images/{imageid}/metadata:
    parameters:
      - schema:
          type: string
        name: imageid
        in: path
        required: true
    get:
      summary: Get image metadata
      tags:
        - images
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ImageMetadata"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Image with id <imageID> not found
        "500":
          description: Internal Server Error
      operationId: get-image-metadata-byID
      description: Get image metadata
      security:
        - jwtBearer: []
  /v2/patients:
    post:
      summary: Add a patient
      operationId: post-patient-profiles
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2patients
      description: Add a patient to an account
      security:
        - jwtBearer: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: "#/components/schemas/Patient"
    get:
      summary: Get patient profiles
      operationId: get-patient-profiles
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Patient"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2patients
      description: Get list of patient profiles for an account
      security:
        - jwtBearer: []
  /v2/patients/valid:
    get:
      summary: Check if all patients in an account have a valid profile
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  $ref: "#/components/schemas/PatientProfileValidation"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /v2/patients/incomplete/notification:
    post:
      summary: Create notifications for an account that has incomplete patients
      responses:
        "200":
          description: OK
        "201":
          description: Created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /v2/patients/{patientId}:
    get:
      summary: Get patient profile by id
      operationId: get-patient-profiles-id
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Patient"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2patients
      description: Get list of patient profiles for account
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          name: patientId
          in: path
          required: true
    patch:
      summary: Update patient profile by id
      operationId: patch-patient-profile
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2patients
      description: Update a patient profile by id
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          name: patientId
          in: path
          required: true
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: "#/components/schemas/Patient"
    delete:
      summary: Delete patient profile by id
      operationId: delete-patient-profile
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2patients
      description: Delete a patient profile by id
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          name: patientId
          in: path
          required: true
  /v2/patients/{patient_id}/valid:
    parameters:
      - schema:
          type: string
        name: patient_id
        in: path
        required: true
    get:
      summary: Check if a patient has a valid profile
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PatientProfileValidation"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /v1/shares/{shareId}:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    get:
      summary: Get share files
      tags:
        - shares
      responses:
        "200":
          description: OK
          content:
            application/zip:
              schema:
                type: string
                format: binary
            application/json:
              schema:
                $ref: "#/components/schemas/Share"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-shares-byId
      description: |-
        3 options:
        - Set Accept header to application/zip to download zip file.
        - Set Accept header to application/octet-stream to download iso file.
        - Set Accept header to application/json to get json body response (ie, eUnity token, exam list)
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: query
          name: eids
          description: comma separated list of exam ids to download. ignored for json option.
  /v1/shares/{shareId}/healthrecords:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    get:
      summary: Get shares healthrecord files as a single zip
      tags:
        - shares
      responses:
        "200":
          description: OK
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-shares-healthrecords-byId
      description: Retrieve a zipped folder (organized by record) of all the records for given FHIR ID's.
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: query
          name: hrids
          description: the FHIR ID's to get data for in a comma separated list
  /v1/users/consents:
    parameters: []
    get:
      summary: Get consent pdf(s)
      responses:
        "200":
          description: OK
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-user-request-consent
      tags:
        - users
      description: |-
        Retrieve the consent pdf associated with a records request, transfer, or provider.
        Use only one of the query parameters to retrieve a consent PDF, or use none, and retrieve all a user's consent forms.
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: query
          name: requestID
        - schema:
            type: integer
          in: query
          name: providerID
        - schema:
            type: string
          in: query
          name: transferID
  /v2/shares/{shareId}/exams:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    get:
      summary: Get exams and health records within a share
      tags:
        - v2shares
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  exams:
                    type: array
                    items:
                      $ref: "#/components/schemas/Exam"
                  hrs:
                    type: array
                    items:
                      $ref: "#/components/schemas/Record"
                  hrPatient:
                    $ref: "#/components/schemas/HRPatient"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-share-exams-byshareId-v2
      description: Get a list of exams and health records associated with a share for a validated share viewer
  /v1/providers/{providerId}:
    parameters:
      - schema:
          type: integer
        name: providerId
        in: path
        required: true
    get:
      summary: Get a provider
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Provider"
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-provider-byId
      description: Get a single provider
  /v1/transfers/{transferId}/purchase:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      deprecated: true
      summary: Pay for transfer
      operationId: post-transfer-payment
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "402":
          description: Payment Required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentError"
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
      description: |-
        Pay for self uploaded exam or enrolled transfer (in portal purchase).
        Two types of authorization:
        - [DEPRECATED] for self uploaded exams, expects normal user authentication with a valid uploadSessionId header (retrieved from POST /transfers)
        - otherwise, a transfer token received from doing a successful transfer challenge (GET/POST /trabsfers/{id}/challenge)
        - paymentProvider name in PurchaseToken is required to know actual transaction took place and not just transfer activation
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentToken"
      tags:
        - transfers
  /v2/users/email/update/init:
    post:
      summary: ""
      operationId: post-v2users-updateEmail
      responses:
        "200":
          description: OK
      description: "Update user account email. Backend will enforce token age, so should get new authentication token just prior to making this request."
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/V2EmailUpdate"
      security:
        - jwtBearer: []
      tags:
        - v2users
  /v1/users/rollout:
    parameters:
      - name: flag
        in: query
        required: true
        description: "flag of rollout, either 'resetpw' or 'login'"
        schema:
          type: string
    get:
      summary: Get users ep rollout
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: boolean
        "500":
          description: Internal Server Error
      operationId: get-users-rollout-flag
      description: |-
        get launch flag value probabilistically for rollout flag (intended to be used by
        front-end to decide to call RR or coreapi (then forward to acctsvc).
  /v1/transfers/{transferId}/challenge:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      deprecated: true
      summary: Answer Transfer Challenge
      operationId: post-transfers-transferId-challenge
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChallengeToken"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: |-
        User challenge answer to verify their identity for a new exam that's available.
        Returns a token that can be used to a) register a new user (for out of portal challenge) or b) purchase the records (in portal challenge)
      requestBody:
        content:
          application/json:
            schema:
              type: string
              description: challenge question answer
        description: Challenge answer
      tags:
        - transfers
    get:
      deprecated: true
      summary: Get transfer challenge question
      operationId: get-transfers-transferId-challenge
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChallengeQuestion"
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Get the challenge question for a transfer.
      tags:
        - transfers
  /v1/transfers/{transferId}/reactivate:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      summary: Reactivate the expired Transfer
      operationId: post-transfers-transferId-reactivate
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
      tags:
        - transfers
      deprecated: true
  /v1/transfers/{transferId}:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    get:
      deprecated: true
      summary: Get transfer info
      tags:
        - transfers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Transfer"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-transfers-transferId
      description: Get the details for the transfer - whether it requires purchasing & the fee details. Requires authentication via the transfers challenge
      security:
        - jwtBearer: []
    delete:
      summary: Remove a transfer
      operationId: delete-transfers-transferId
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: |-
        Removes a transfer and refunds the patient, if they've paid. This is ONLY possible for self-uploaded transfers DURING the self uploading process (it's a fallback in case there are errors in the multi-step process that require earlier steps to be rolled back.)
        Requires the uploadSessionID header.
      security:
        - jwtBearer: []
      tags:
        - transfers
      parameters:
        - schema:
            type: string
          in: header
          name: uploadSessionId
      deprecated: true
  /v1/users/settings:
    put:
      summary: Update user settings
      tags:
        - users
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reportNotification:
                  type: boolean
                task:
                  $ref: "#/components/schemas/OnboardingTask"
                language:
                  type: string
      operationId: update-user-settings
      description: "update users report-notification setting, onboarding experience task completion, or language setting"
      security:
        - jwtBearer: []
    get:
      summary: Get user settings
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  reportNotification:
                    type: boolean
                  showOnboarding:
                    type: boolean
                  onboardingTasks:
                    $ref: "#/components/schemas/OnboardingTaskCompletion"
                  language:
                    type: string
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-users-settings
      description: Get user settings
      security:
        - jwtBearer: []
  /v1/users/exams/size:
    get:
      summary: Get all exam files' size
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: number
                description: size of all images/reports
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-users-exams-size
      description: Get the total size of all images and reports in a users account
      security:
        - jwtBearer: []
  /v1/users/exams/lookup:
    get:
      deprecated: true
      summary: Lookup an exam by SSO token and accession
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
                description: transferId
        "401":
          description: Unauthorized
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-users-exams-lookup
      description: "Royal portal integration: Lookup an exam via sso token and accession, returns transferId if exam is found, otherwise 404."
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: query
          name: token
          description: sso token
        - schema:
            type: string
          in: query
          name: accession
          description: accession number
    parameters: []
  /v1/users/exams/eligible-insights:
    get:
      summary: Get exam insight eligibility statuses for patient
      tags:
        - users
      parameters:
        - schema:
            type: string
          in: query
          name: patient_id
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ExamInsightEligibilityResponse"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
      description: |-
        Get an object describing all the eligible exams insights for the given account, or patient.
  /v1/users/reportinsights:
    get:
      summary: Get available/generated report insight in account, or patient
      tags:
        - users
      parameters:
        - schema:
            type: string
          in: query
          name: patient_id
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InsightsResponse"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
      description: |-
        Get an object describing all the available/generated report insights for the given account, or patient.
  /v1/requests/{id}/rejectVerify:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    post:
      summary: Send patient verified email
      operationId: post-requests-id-rejectVerify
      responses:
        "200":
          description: OK
      tags:
        - requests
      description: Send an email to PX after a rejected request has had details verified by the patient
      security:
        - jwtBearer: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectVerifyDetails"
  /v1/users/subscription/toggleautorenew:
    parameters: []
    put:
      summary: Toggle auto-renew
      operationId: put-users-subscription-toggleautorenew
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Toggle auto-renew on a subscription.
      security:
        - jwtBearer: []
      tags:
        - subscriptions
  /v1/requests/{id}/resubmit:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    put:
      summary: Resubmit request
      operationId: post-requests-id-resubmit
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  consentPdf:
                    type: string
                  requestId:
                    type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: Update details of request and resubmit. Same request body and response body of POST /requests except there's no payment token in the request.
      security:
        - jwtBearer: []
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                requestBody:
                  $ref: "#/components/schemas/Request"
                signatureImg:
                  type: string
                  format: base64
                minorSignatureImg:
                  type: string
                  format: base64
                delegForm:
                  type: string
                delegatePhotoId:
                  type: string
  /v1/requests/{id}/cancel:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    patch:
      summary: Cancel request
      operationId: patch-requests-id-cancel
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: Updates request status to VOID which cancels the request.
      security:
        - jwtBearer: []
  /v1/providers/consents/{consentId}/type:
    parameters:
      - schema:
          type: string
        name: consentId
        in: path
        required: true
    get:
      summary: Get consent type
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsentType"
      operationId: get-providers-consents-consentid-type
      description: Get consent type (verified or unverified)
  /v1/providers/consents/{consentid}:
    parameters:
      - schema:
          type: string
        name: consentid
        in: path
        required: true
    get:
      summary: Get consent form data
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsentFormData"
      operationId: get-providers-consents-consentid
      description: Get the data for the consent form for a given consent id
    post:
      summary: Post consent
      operationId: post-providers-consents-consentid
      responses:
        "200":
          description: OK
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - providers
      description: "Post consent form details "
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Consent"
  /v1/providers/consents/{consentid}/unverified:
    parameters:
      - schema:
          type: string
        name: consentid
        in: path
        required: true
    get:
      summary: Get consent form data
      tags:
        - providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsentFormData"
      operationId: get-providers-consents-consentid-unverified
      description: Get consent form data for a given consent id without date of birth verification required
    post:
      summary: Post consent
      operationId: post-providers-consents-consentid-unverified
      responses:
        "200":
          description: OK
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - providers
      description: "Post consent form details "
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Consent"
  /v1/providers/consents/{consentid}/email-verification:
    parameters:
      - schema:
          type: string
        name: consentid
        in: path
        required: true
    post:
      summary: Post consent email verification
      operationId: post-providers-consents-email-verification
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsentEmailVerification"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - providers
      description: "Post consent email verification"
      requestBody:
        content:
          application/json:
            schema:
              type: string
              description: email
  /v1/users/login/sso:
    post:
      summary: Login via SSO
      operationId: post-users-login-sso
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  uuid:
                    type: string
                  userExists:
                    type: boolean
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "500":
          description: Internal Server Error
      tags:
        - users
      description: Login with an SSO token
      requestBody:
        content:
          application/json:
            schema:
              type: string
              description: sso token
  /v1/users/referral:
    post:
      summary: Refer a friend
      operationId: post-users-referral
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
      tags:
        - users
      description: Refer a friend to join PocketHealth
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Referral"
      security:
        - jwtBearer: []
  /v2/users/setup:
    post:
      summary: Set user password and verify email
      operationId: post-users-setup
      responses:
        "200":
          description: OK
        "400":
          description: Bad Password or invalid request body
      tags:
        - v2users
      description: Take the given verification token and password and call /v1/accounts/verify in acctsvc to set the password and verify the email
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Setup"
  /v2/users/verify:
    post:
      summary: Verify user email
      operationId: post-users-verify
      responses:
        "200":
          description: OK
        "400":
          description: Bad request ( token absent in body )
      tags:
        - v2users
      description: Take the given verification token and call /v1/accounts/verify in acctsvc to verify the email
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Verify"
  /v2/users/lockAccount:
    post:
      summary: Lock User account upon email update
      operationId: post-users-lock-account
      responses:
        "200":
          description: OK
        "400":
          description: Bad request ( token absent in body )
        "401":
          description: Unauthorized
        "404":
          description: Token not found
        "500":
          description: Internal Server Error
      tags:
        - v2users
      description: Take the lock token and call /v1/accounts/lock in acctsvc to lock the account after account email update
      requestBody:
        content:
          application/json:
            schema:
              type: string
              description: lock token
  /v2/users/deactivate:
    patch:
      tags:
        - v2users
      summary: Deactivate account
      description: Put account in an inaccessible frozen state to be picked up for deletion later
      operationId: patch-users-deactivate-account
      responses:
        "200":
          description: OK
        "400":
          description: Bad request ( token absent in body )
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v2/users/owner:
    patch:
      tags:
        - v2users
      summary: Set Account Owner
      description: Set Account Owner to Patient Id specified in request body
      operationId: patch-users-set-account-owner
      responses:
        "200":
          description: OK
        "400":
          description: Bad request ( token absent in body )
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SetAccountOwnerRequest"
      security:
        - jwtBearer: []
  /v2/users/email/update:
    put:
      summary: Update user's email
      operationId: put-users-update-email
      responses:
        "200":
          description: OK
        "400":
          description: Bad request ( token absent in body )
        "500":
          description: Internal Server Error
      tags:
        - v2users
      description: Take the given token and call PATCH /v1/accounts in acctsvc to update the email
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Verify"
  /v2/users/resetpassword/init:
    post:
      summary: Init reset password
      operationId: post-v2-users-resetpassword-init
      responses:
        "200":
          description: OK
      tags:
        - v2users
      description: Initiate the reset password process to send an email to the user with a link to change their password.
      requestBody:
        content:
          application/json:
            schema:
              description: email to send reset instructions to
              type: string
  /v2/users/resetpassword:
    post:
      summary: Reset password
      tags:
        - v2users
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      operationId: post-v2-users-resetpassword
      description: Reset the password using a token and security code
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PasswordResetInfo"
  /v2/users/email-verification:
    post:
      summary: Post email verification
      tags:
        - v2users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmailVerification"
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      operationId: post-email-verification
      description: Check if email is verified using the given token
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: string
              description: token
  /v2/users/verify/dob:
    post:
      summary: Post verify DOB
      tags:
        - v2users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: boolean
                description: Whether DOB is correct
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      operationId: post-verify-dob
      description: Verify DOB answered is correct
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DOBVerification"
  /v2/users/state:
    get:
      summary: get account info and state
      operationId: get-account-state
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AccountState"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      tags:
        - v2users
      description: get account state
      security:
        - jwtBearer: []
  /v2/users/exams/lookup:
    get:
      summary: Lookup an exam by SSO token and accession
      tags:
        - users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
                description: uuid
        "401":
          description: Unauthorized
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-v2-users-exams-lookup
      description: "Royal portal integration: Lookup an exam via sso token and accession, returns uuid if exam is found, otherwise 404."
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: query
          name: token
          description: sso token
        - schema:
            type: string
          in: query
          name: accession
          description: accession number
    parameters: []
  /v1/shares/{shareId}/extend:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    put:
      summary: Extend the expiry date
      operationId: put-share-extend-byshareId
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      tags:
        - shares
      description: Extend the expiry date for the share by the given share id
      security:
        - jwtBearer: []
  /v1/reports:
    get:
      summary: Get unassociated reports
      tags:
        - reports
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Report"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-reports
      description: Get reports not associated with any exam
      security:
        - jwtBearer: []
  /v1/reports/{reportId}/metadata:
    parameters:
      - schema:
          type: string
        name: reportId
        in: path
        required: true
    get:
      summary: Get report metadata
      tags:
        - reports
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Report"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-reports-reportId-metadata
      description: Get report metadata
      security:
        - jwtBearer: []
        - phSignature: []
  /v2/transfers:
    post:
      summary: Initialize a V2 upload
      operationId: post-v2-transfers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UploadInitResponse"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Initialize a V2 upload with the list of images to upload. Returns the transfer and upload session IDs.
      security:
        - jwtBearer: []
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/UploadImage"
        description: ""
      tags:
        - v2transfers
  /v2/transfers/{transferId}/images:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      summary: Upload images to transfer
      operationId: post-v2-transfers-transferId-file
      responses:
        "200":
          description: OK - Files accepted for processing.
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Upload images to transfer.
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: header
          name: upload-session-id
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: array
                  items:
                    type: string
                    format: binary
        description: Attached files
      tags:
        - v2transfers
  /v2/transfers/{transferId}/finalize:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      summary: Finalize upload
      operationId: post-v2-transfers-transferId-finalize
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: "Finalize upload - polls provider service until complete/timeout, then finalizes transfer. Ends upload session."
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: header
          name: upload-session-id
      tags:
        - v2transfers
  /v2/transfers/{transferId}:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    delete:
      summary: V2 upload rollback
      operationId: delete-v2-transfers-transferId
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Refunds payment if made and calls providers service to roll back the upload. Ends upload session.
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: header
          name: upload-session-id
      tags:
        - v2transfers
  /v2/transfers/{transferId}/reportdcm:
    parameters:
      - schema:
          type: string
        name: transferId
        in: path
        required: true
    post:
      summary: Upload dcm report to transfer
      operationId: post-v2-transfers-transferId-reportdcm
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Upload a single dcm report to a transfer
      security:
        - jwtBearer: []
      parameters:
        - schema:
            type: string
          in: header
          name: upload-session-id
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                metadata:
                  $ref: "#/components/schemas/UploadFileMetadata"
      tags:
        - v2transfers
  /v1/promo/{code}:
    parameters:
      - schema:
          type: string
        name: code
        in: path
        required: true
    get:
      deprecated: true
      summary: Check promo code
      tags: []
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                required:
                  - valid
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-v1-promo-code
      description: Check if a promo code is valid before submitting a transfer or request payment.
  /v1/requests/incomplete:
    post:
      summary: Create an Incomplete Request
      operationId: post-v1-requests-incomplete
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IncompleteRequestInitResponse"
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: Create a new incomplete request to save request data.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IncompleteRequest"
  /v1/shares/{shareId}/dltoken:
    parameters:
      - schema:
          type: string
        name: shareId
        in: path
        required: true
    post:
      summary: POST share download auth
      operationId: post-v1-shares-shareId-dltoken
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DLToken"
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - shares
      description: Get a limited token that authorizes the bearer to download a share. Endpoint must be authenticated.
      security:
        - jwtBearer: []
  /v1/requests/incomplete/{incompleteRequestId}:
    parameters:
      - schema:
          type: string
        name: incompleteRequestId
        in: path
        required: true
    get:
      summary: Get Metadata of the Incomplete Request
      tags:
        - requests
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IncompleteRequestMetadata"
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-v1-requests-incomplete-incompleteRequestId
      description: Get metadata of a specific incomplete request.
    put:
      summary: Update an Incomplete Request
      operationId: patch-v1-requests-incomplete-incompleteRequestId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Update an incomplete request with updated request data
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IncompleteRequest"
      tags:
        - requests
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          description: "Auth token returned from the incomplete/:id/verify endpoint"
  /v1/requests/incomplete/{incompleteRequestId}/verify:
    parameters:
      - schema:
          type: string
        name: incompleteRequestId
        in: path
        required: true
    post:
      summary: Authenticate Access to Incomplete Request
      operationId: post-v1-requests-incomplete-incompleteRequestId-verify
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IncompleteRequestResponse"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      description: Authenticate a patient's access to incomplete request data by verifying their input DOB against the saved request data.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IncompleteRequestVerify"
      tags:
        - requests
  /v1/requests/incomplete/{incompleteRequestId}/email:
    parameters:
      - schema:
          type: string
        name: incompleteRequestId
        in: path
        required: true
    get:
      summary: Get Incomplete Request Data for an Email
      tags:
        - requests
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      incomplete_request_id:
                        type: string
                      clinic_name:
                        type: string
                required:
                  - status
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-v1-requests-incomplete-incompleteRequestId-email
      description: Get and format Incomplete Request data for usage in an email template.
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          description: JWT Token added to the Email Queue message
  /v1/requests/incomplete/{incompleteRequestId}/email/status:
    parameters:
      - schema:
          type: string
        name: incompleteRequestId
        in: path
        required: true
    post:
      summary: Callback to notify of a successful/failed email send
      operationId: post-v1-requests-incomplete-incompleteRequestId-email-status
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Callback endpoint to notify Core API of whether an email send was successful or not.
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmailStatusCallback"
      tags:
        - requests
  /v1/requests/verification/config:
    get:
      summary: Get all request verification configurations
      tags:
        - requests
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/VerificationConfig"
        "500":
          description: Internal Server Error
      operationId: get-v1-requests-verification-configuration
      description: Get all request verification configurations
  /v1/transactions/providers:
    get:
      summary: Get payment providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                description: ""
                minItems: 1
                uniqueItems: true
                x-examples:
                  example-1:
                    - moneris
                items:
                  type: string
              examples:
                example:
                  value:
                    - moneris
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-v1-expertreview-paymentproviders
      parameters:
        - schema:
            type: string
            pattern: CA|US
          in: query
          name: country
          description: Available providers in the country
      description: Get a list of available providers for a country
      tags:
        - payment
    parameters: []
  /refer/{token}:
    parameters:
      - schema:
          type: string
        name: token
        in: path
        required: true
    get:
      summary: Get Email Refer Throttle By Token
      tags:
        - refer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicReferEmailThrottle"
              examples:
                could send 4 more:
                  value:
                    email_throttle_number: 5
                    available_number: 4
                cannot send anymore:
                  value:
                    email_throttle_number: 5
                    available_number: 0
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      operationId: get-refer-token
    post:
      summary: Post an email friends refer
      tags:
        - refer
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      operationId: post-refer-token
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/PublicReferral"
  /v1/reports/{id}/taggedhtml:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: Get tagged report with definitions
      tags:
        - reports
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TaggedHTML"
        "401":
          description: Unauthorized
        "404":
          description: Report not found or account has no permission to access the report
        "500":
          description: Internal Server Error
      operationId: get-v1-reports-id-taggedhtml
      description: "Returns the report identified by id in html, with defined terms tagged with the css class phdefinition. Also returns a dictionary of terms and definitions"
      security:
        - jwtBearer: []
  /v1/internal/patients/{patient_id}/phiprofiles:
    parameters:
      - schema:
          type: string
        name: patient_id
        in: path
        required: true
    delete:
      summary: Delete associated phi_profiles for a patient_id
      tags:
        - internal
      operationId: delete-internal-patient-profiles
      responses:
        "200":
          description: OK
        "401":
          description: Invalid Signature
        "500":
          description: Internal Server Error
      security:
        - phSignature: []
  /v1/meddream/generate/{exam_uuid}:
    get:
      summary: Generate a token for MedDream
      parameters:
        - in: path
          name: exam_uuid
          schema:
            type: string
          required: true
      tags:
        - meddream
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v2/providers/{id}/plans:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: Get provider plans
      tags:
        - v2providers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Plan"
        "500":
          description: Internal Server Error
      operationId: get-v2-providers
      description: Get list of plan objs supported by a provider
  /v2/plans:
    get:
      summary: Get a List of Available Plans
      tags:
        - plans
      responses:
        "200":
          description: OK
      operationId: get-v1-plans
      description: "Get a list of available plans filtered by `recurring`, which defaults to true."
      parameters:
        - schema:
            type: boolean
            default: true
          in: query
          name: recurring
      security:
        - jwtBearer: []
  /v2/features/{id}/authorize:
    parameters:
      - schema:
          type: integer
        name: id
        in: path
        required: true
    get:
      summary: Authorize a Feature
      tags:
        - features
      responses:
        "200":
          description: OK
      operationId: get-v1-features-id-authorize
      description: One query param must be provided. Auth token gets validated if `user_id` query param provided.
      parameters:
        - schema:
            type: number
          in: query
          name: user_id
        - schema:
            type: number
          in: query
          name: org_id
  /v2/orders:
    post:
      summary: Create order
      operationId: post-v2-orders
      parameters:
        - schema:
            type: string
          in: query
          name: reason
          description: A reason the new subscription is being created; may be useful for analytics
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrderResponse"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2orders
      description: Create an order where the customer is charged
      security:
        - jwtBearer: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrderRequest"
          application/xml:
            schema:
              type: object
              properties: {}
        description: ""
    get:
      summary: Get account orders
      operationId: get-v2-orders
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Order"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      tags:
        - v2orders
      description: |-
        Get active recurring order for an account, if any. Use account from JWT. Returns empty list if no orders.
        TODO: when needed, can open up this EP to allow filtering on the active/recurring parameters of orders. That's why return body is an array.
      security:
        - jwtBearer: []
  /v2/orders/paymentDetails:
    put:
      summary: Update payment details
      operationId: put-v2-orders-paymentDetails
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Update payment details of auto renewal for current session order
      tags:
        - v2orders
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentToken"
        description: Stripe token
      parameters: []
      security:
        - jwtBearer: []
  /v2/secondopinion/eligible/{examId}:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    get:
      summary: Second Opinion Exam Eligibility
      tags:
        - secondopinion
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SOEligibility"
      operationId: get-v1-secondopinion-eligible-examId
      description: Get the eligibility of an exam for second opinion from examinsights
  /v2/secondopinion/review/{examid}:
    post:
      summary: Initiate new SO review request
      tags:
        - secondopinion
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties: {}
              examples:
                example-1:
                  value: {}
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties: {}
        "500":
          description: Internal Server Error
      operationId: post-v1-secondopinion-reviews
      description: Initiate a new Second Opinion review request. Returns back the review id created.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SOReviewRequest"
            examples:
              example-1:
                value:
                  exam_uuid: exam_id
                  request_patient_detail:
                    patient_name: abc
                    dob: aaa
                    ohip: asda
                    ohip_version_code: ab
                    ohip_expiration_date: adfsa
                    phone: "**********"
                  request_referring_detail:
                    referring_physician:
                      cspo: 1231asda1231
        description: ""
      parameters: []
      security:
        - jwtBearer: []
    parameters:
      - schema:
          type: string
        name: examid
        in: path
        required: true
  /v2/secondopinion/doctors/search:
    get:
      summary: Search for CPSO doctors from second opinion svc
      tags:
        - secondopinion
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SOCPSODoctor"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: get-v2-secondopiniont-doctors-search
      parameters:
        - schema:
            type: string
          in: query
          name: query
          description: search term
    parameters: []
  /v2/secondopinion/eligible/priors:
    post:
      summary: Check if list of priors are eligible
      operationId: post-v2-secondopinion-eligible-priors
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                examUuids:
                  type: array
                  items:
                    type: string
      description: Check if list of prior exams are eligible for second opinion base on ruleset we have in backend.
      tags:
        - secondopinion
  /v2/secondopinion/patient_eligibility/{programName}:
    post:
      summary: Create new patient eligibility programs for an account
      tags:
        - secondopinion
      parameters:
        - in: path
          name: programName
          schema:
            type: string
          required: true
          description: program name of the patient eligibility to be created
      responses:
        "200":
          description: OK
        "201":
          description: Created
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
  /v2/secondopinion/patient_eligibility/{patientId}/{programName}:
    parameters:
      - in: path
        name: patientId
        schema:
          type: string
        required: true
      - in: path
        name: programName
        schema:
          type: string
        required: true
    put:
      tags:
        - secondopinion
      summary: Update uncompleted Patient Eligibility Programs for patient
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                is_completed:
                  type: boolean
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not found
        "500":
          description: Internal Server Error
  /v2/secondopinion/patient_eligibility:
    post:
      summary: Create a complete / incomplete PEP for a specific patient
      tags:
        - secondopinion
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePatientEligibilityProgramRequest"
      responses:
        "201":
          description: Created
        "400":
          description: Bad Request
        "401":
          description: Not Authorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /v2/healthrecords/{patientId}:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
    get:
      summary: Get Health Records
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  lastModified:
                    type: string
                  acctId:
                    type: string
                  phPatientId:
                    type: string
                  record:
                    $ref: "#/components/schemas/Record"
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      operationId: get-v2-patientId
      parameters:
        - schema:
            type: boolean
          in: query
          name: covidVaccine
          description: "If set to true, an email is sent to the user prompting them to refer others to upload their vaccine record."
      description: "Get health records metadata for a signle patient "
      tags:
        - v2healthrecords
    post:
      summary: post health records
      operationId: post-v2-healthrecords-patientId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Upload health records for a single patient
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                records:
                  type: array
                  items: {}
                file:
                  type: string
        description: |-
          The request body should have

          1. The records field
          2. For each record, a file whose name matches a filename in records.
      tags:
        - v2healthrecords
  /v2/healthrecords/{patientId}/questionnaire/gail:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
    post:
      summary: post health records questionnaire response
      operationId: post-v2-healthrecords-patientId-gail
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GailRiskScore"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "422":
          description: Unprocessable Entity
        "500":
          description: Internal Server Error
      description: Get Gail model results for patient
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GailQuestionnaireResponse"
      tags:
        - v2healthrecords
  /v2/healthrecords/{patientId}/upload:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
    get:
      summary: Get upload session token
      parameters:
        - in: path
          name: patientId
          schema:
            type: string
          required: true
          description: PH Patient ID of health record being uploaded
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  uploadSessionToken:
                    type: string
                  regionID:
                    type: number
        "401":
          description: Unauthorized
      operationId: get-v2-healthrecords-patientId-upload
      description: "For a logged in user eligible for health records feature, returns a health records upload session token for a specific profile"
      tags:
        - v2healthrecords
  /v2/healthrecords/{patientId}/records/{recordId}:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
      - schema:
          type: string
        name: recordId
        in: path
        required: true
    get:
      summary: Get a zip of all files for a health record
      tags:
        - v2healthrecords
      responses:
        "200":
          description: OK
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "401":
          description: Unauthorized
      operationId: get-v2-healthrecords-patientId-records-recordId
      description: "For a logged in user with a specific record ID or an authenticated share viewer, returns all files as 1 zip for said record"
    put:
      summary: update an existing health record
      operationId: put-v2-healthrecords-patientId-records-recordId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: "For a logged in user with a specific record ID, update said record"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                records:
                  $ref: "#/components/schemas/Record"
                file:
                  type: string
        description: ""
      tags:
        - v2healthrecords
    delete:
      summary: delete health record
      operationId: delete-v2-healthrecords-patientId-records-recordId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      tags:
        - v2healthrecords
  /v2/healthrecords/{patientId}/records/{recordId}/thumbnail:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
      - schema:
          type: string
        name: recordId
        in: path
        required: true
    get:
      summary: get thumbnail of the first attachment of a health record
      tags:
        - v2healthrecords
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
      operationId: get-v2-healthrecords-patientId-records-recordId-thumbnail
      description: "For a logged in user with a specific record ID or an authenticated share viewer, returns a PNG thumbnail of the first file for said record"
  /v2/healthrecords/{patientId}/record/type/{recordType}:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
      - schema:
          type: string
        name: recordType
        in: path
        required: true
      - schema:
          type: string
        in: query
        name: limit
        description: max amount of results to return
    get:
      summary: get records belonging to a patient of a specific fhir type
      tags:
        - v2healthrecords
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Record"
        "401":
          description: Unauthorized
      operationId: get-v2-healthrecords-patientId-records-recordType
      description: "Return the health records of a specific fhir resource type, belonging to a patient"
  /v2/healthrecords/verify:
    post:
      summary: ""
      operationId: post-v2-healthrecords-verify
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
        "401":
          description: Unauthorized
      description: verify that an upload token is valid
      tags:
        - v2healthrecords
  /v2/healthrecords/{patientId}/integrations/mychart:
    parameters:
      - schema:
          type: string
        name: patientId
        in: path
        required: true
    post:
      summary: post new records integration with mychart
      operationId: post-v2-healthrecords-mychart-patientId
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: Connect an account to MyChart for the patient identified by patientId
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MyChartIntegrationRequest"
      tags:
        - v2healthrecords
  /v2/healthrecords/mychart/search:
    parameters:
      - schema:
          type: string
        name: query
        in: query
        required: true
    get:
      summary: search mychart org name
      operationId: get-v2-healthrecords-mychart-search
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OrgInfo"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "500":
          description: Internal Server Error
      description: search for a mychart org based on its name
      tags:
        - v2healthrecords
  /v1/reports/{reportId}/insights/followup:
    parameters:
      - schema:
          type: string
        name: reportId
        in: path
        required: true
    get:
      summary: Get v2 follow up
      tags:
        - reports
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Report not found or account has no permission to access the report
        "500":
          description: Internal Server Error
      operationId: get-v1-reports-reportId-insights-followup
      description: |-
        Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container \
        Then, call report insights to generate followup information
  /v1/reports/{reportId}/insights/questions:
    parameters:
      - schema:
          type: string
        name: reportId
        in: path
        required: true
    get:
      summary: Get questions for your doctor
      tags:
        - reports
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "404":
          description: Report not found or account has no permission to access the report
        "500":
          description: Internal Server Error
      operationId: get-reports-reportId-insights-questions
      description: |-
        Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container \
        Then, call report insights to generate questions for your doctor

  /v1/appointments:
    $ref: "./appointments/paths.yaml#/paths/~1v1~1appointments"
  /v1/appointments/patientstatus:
    $ref: "./appointments/paths.yaml#/paths/~1v1~1appointments~1patientstatus"
  /v2/requests/create:
    post:
      summary: Create a new request
      responses:
        "201":
          description: created
          content:
            application/json:
              schema:
                type: object
                description: New request identifier and consent file
                properties:
                  requestId:
                    type: string
                  consentPdf:
                    type: string
                    format: binary
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "402":
          description: Payment Required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentError"
        "500":
          description: Internal Server Error
      tags:
        - requests
      description: |-
        Create a new request.
        Returns PDF form including signature.
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/CreateRequest"
  /v2/rho/eligible/{examId}:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    get:
      summary: Rho Exam Eligibility
      tags:
        - rho
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnalysisEligibility"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "422":
          description: Unprocessable Entity
        "500":
          description: Internal Server Error
      operationId: get-v2-rho-eligible-examId
      description: Get the eligibility of an exam for rho from examinsights
  /v2/rho/{examId}:
    parameters:
      - schema:
          type: string
        name: examId
        in: path
        required: true
    post:
      summary: Initiate Rho Request
      tags:
        - rho
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      operationId: post-v2-rho-request
      description: Submit new rho request for an exam
  /v1/physician_accounts:
    post:
      tags:
        - v1 physician accounts
      summary: Create Physician Account
      description: Create a new Physician Account
      operationId: post-physician-accounts
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterData"
      responses:
        "200":
          description: OK. Returns ID of newly created physician account. Sends the 'Verify Email' email for physician accounts.
          content:
            application/json:
              schema:
                type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "406":
          description: Not Accceptable. Bad Password provided.
        "500":
          description: Internal Server Error
    patch:
      tags:
        - v1 physician accounts
      summary: Patch Physician Account Password
      description: Update an physician account's password based on recovery token and code and new password.
      operationId: patch-physician-accounts
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PasswordResetInfo"
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "406":
          description: Not Accceptable. Bad Password provided.
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
    get:
      tags:
        - v1 physician accounts
      summary: Get Physician Account
      description: Returns physician account for given account_id
      operationId: get-physician-account
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhysicianAccount"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/login:
    post:
      tags:
        - v1 physician accounts
      summary: Login
      description: "Login a physician with email and password. Returns jwt and refresh token on success, error response otherwise."
      operationId: post-physician-accounts-login
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: token
                properties:
                  token:
                    type: string
                  uuid:
                    type: string
                  betas:
                    type: array
                    items:
                      type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden (account locked)
        "500":
          description: Internal Server Error
  /v1/physician_accounts/logout:
    post:
      tags:
        - v1 physician accounts
      summary: Logout
      description: Logout a physician by blacklisting the given auth token.
      operationId: post-physician-accounts-logout
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/patients:
    get:
      tags:
        - v1 physician accounts
      summary: Get Physician Patients
      description: Returns the patients of a physician
      operationId: get-physician-account-patients
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PhysicianPatientSummary"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/patients/{patient_id}:
    get:
      tags:
        - v1 physician accounts
      summary: Get Physician Patients Exams
      description: Returns the exams of a patient of a physician
      operationId: get-physician-account-patients-exams
      parameters:
        - schema:
            type: string
          name: patient_id
          in: path
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  hrs:
                    type: array
                    items:
                      $ref: "#/components/schemas/Record"
                  exams:
                    type: array
                    items:
                      $ref: "#/components/schemas/Exam"
                  examShareInfoMap:
                    type: object
                    additionalProperties:
                      type: string
                    properties:
                      "":
                        $ref: "#/components/schemas/PhysicianShareInfo"
                  hrShareInfoMap:
                    type: object
                    properties:
                      "":
                        $ref: "#/components/schemas/PhysicianShareInfo"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
    parameters:
      - schema:
          type: string
        name: patient_id
        in: path
        required: true
  /v1/physician_accounts/physicians/{physician_id}/licenses:
    post:
      tags:
        - v1 physician accounts
      summary: Add Physician License to Physician
      description: Add a new Physician License to an existing Physician.
      operationId: post-physician-license
      parameters:
        - schema:
            type: string
          name: physician_id
          in: path
          required: true
      requestBody:
        $ref: "#/components/requestBodies/PhysicianLicenseRequest"
      responses:
        "200":
          description: OK. Returns ID of newly created physician license.
          content:
            application/json:
              schema:
                type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/physicians/{physician_id}/verify:
    post:
      tags:
        - v1 physician accounts
      summary: Verify Physician Notification Method
      description: Verify a fax or email for Physician using a verification code.
      operationId: verify-physician-notification-method
      parameters:
        - schema:
            type: string
          name: physician_id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                notification_method:
                  $ref: "#/components/schemas/NotificationMethod"
                value:
                  type: string
              required:
                - notification_method
                - value
      responses:
        "200":
          description: OK.
          content:
            application/json:
              schema:
                type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/reset:
    post:
      tags:
        - v1 physician accounts
      summary: Reset Physician Account Password
      description: |-
        Send password reset instructions to the email if physician account exists and password is set.
        Otherwise send a different email depending on the state of the account.
      operationId: post-physician-accounts-reset
      requestBody:
        content:
          application/json:
            schema:
              type: string
              format: email
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found.
        "500":
          description: Internal Server Error
  /v1/physician_accounts/search:
    post:
      tags:
        - v1 physician accounts
      summary: Search Shares for Physician Account
      description: |-
        Sends a request to an azure topic for record search based on given search criteria
        if physician has required permissions to query provider's system.
        Returns a queryId for fetching provider search results async.
      operationId: post-physician-accounts-search
      requestBody:
        $ref: "#/components/requestBodies/PhysicianSearchRequest"
      responses:
        "200":
          description: OK
          content:
            text/plain:
              schema:
                type: string
                example: queryId
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found.
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/shares/request:
    post:
      tags:
        - v1 physician accounts
      summary: Request Shares from a provider for a Physician Account
      description: |-
        Sends a request to an azure topic for access to a list of records
        if physician has required permissions to query provider's system.
      operationId: post-physician-accounts-request
      requestBody:
        $ref: "#/components/requestBodies/PhysicianRecordRequest"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PhysicianRecordResponse"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/shares/{share_id}/extend:
    parameters:
      - schema:
          type: string
        name: share_id
        in: path
        required: true
    put:
      summary: Put Physician Extend Share
      operationId: put-physician-accounts-share-extend
      responses:
        "200":
          description: OK
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      tags:
        - v1 physician accounts
      description: Extend a physician's share's expiry
      security:
        - jwtBearer: []
  /v1/physician_accounts/shares/{share_id}/token:
    get:
      tags:
        - v1 physician accounts
      summary: Get Physician E-Unity Token
      description: Creates and returns a eunity token for a share for physician account access
      operationId: get-physician-account-eunity-token
      parameters:
        - schema:
            type: string
          name: share_id
          in: path
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
    parameters:
      - schema:
          type: string
        name: share_id
        in: path
        required: true
  /v1/physician_accounts/studies:
    get:
      tags:
        - v1 physician accounts
      summary: Get physician studies with upload state
      description: Get list of record streaming studies with upload status that physician has access to
      operationId: get-physician-account-study-states
      responses:
        "200":
          description: list of upload statuses for record streaming studies the physician has access to
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PhysicianRecordUploadStatus"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/studies/verify-access:
    post:
      tags:
        - v1 physician accounts
      summary: Posts a study access request
      description: Sends a request for accessing a study.
        Physician access is verified and an audit event for the access is logged if access was granted via record streaming.
        No audit logging is done for patient- or physician-initiated shares.
        Returns status code 200 if access was verified and, in case of record streaming studies, audit logging was successful.
      operationId: post-physician-account-study-access
      requestBody:
        $ref: "#/components/requestBodies/PhysicianAccessVerificationRequest"
      responses:
        "200":
          description: list of upload statuses for record streaming studies the physician has access to
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      security:
        - jwtBearer: []
  /v1/physician_accounts/verify:
    post:
      tags:
        - v1 physician accounts
      summary: Verify Physician Account Email
      description: Verify email after physician account creation.
      operationId: post-physician-accounts-verify
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                ip:
                  type: string
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /v2/requests/{rid}/status/history:
    post:
      summary: Get all request status history records
      parameters:
        - in: path
          name: rid
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RequestStatusHistory"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
      tags:
        - requests
      description: |-
        Create a new request.
        Returns PDF form including signature.
  /v1/records/upload-status:
    get:
      summary: Get record upload status list for a patient.
        Currently only considers studies that have been uploaded via record streaming.
      tags:
        - recordstreaming
      parameters:
        - schema:
            type: string
          in: query
          name: account_id
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RecordUploadStatus"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
      description: |-
        Get a list of study upload status objects for all studies
        that have been uploaded via record streaming
        that a patient has access to.
        Upload status contains study metadata, provider data and the upload percentage of the study.
  /v1/upload-request:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request"
  /v1/upload-request/authenticate:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1authenticate"
  /v1/upload-request/decline:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1decline"
  /v1/upload-request/studies:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1studies"
  /v1/upload-request/studies/{id}:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1studies~1%7Bid%7D"
  /v1/upload-request/instances:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1instances"
  /v1/upload-request/providers/{subdomain}:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1providers~1%7Bsubdomain%7D"
  /v1/upload-request/submit:
    $ref: "./upload-requests/paths.yaml#/paths/~1v1~1upload-request~1submit"
components:
  requestBodies:
    PhysicianLicenseRequest:
      content:
        application/json:
          schema:
            title: PhysicianLicenseRequest
            type: object
            properties:
              licenseType:
                type: string
              licenseNo:
                type: string
              stateOrProvince:
                type: string
              expiryDate:
                type: string
                format: date-time
    PhysicianSearchRequest:
      content:
        application/json:
          schema:
            type: object
            required:
              - providerId
              - query
            properties:
              providerId:
                type: integer
              query:
                $ref: "#/components/schemas/PhysicianSearchQuery"
    PhysicianRecordRequest:
      content:
        application/json:
          schema:
            type: object
            required:
              - providerId
              - studyRequests
            properties:
              providerId:
                type: integer
              studyRequests:
                type: array
                items:
                  type: object
                  required:
                    - studyUID
                    - patientId
                  properties:
                    studyUID:
                      type: string
                      description: DICOM UID of study that is being requestd
                    patientId:
                      type: string
                      description: MRN of patient associated with study
                    issuer:
                      type: string
                      description: issuer of patient MRN
                    patientFirstName:
                      type: string
                      description: first name of patient
                    patientLastName:
                      type: string
                      description: last name of patient
                    patientBirthDate:
                      type: string
                      description: date of birth name of patient
    PhysicianAccessVerificationRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            description:
              contains information on a study that a physician is trying to access.
              This can be either a shared study or a study uploaded via record streaming.
              Record streaming access verification happens based on studyUID and providerID.
              Share access verification happens based on shareID.
            required:
              - studyUID
              - providerID
            properties:
              studyUID:
                type: string
                description: DICOM UID of study
              providerID:
                type: integer
                format: int64
                description: legacy provider id, also known as orgID
              shareID:
                type: string
                description: id of a share
                  - for record streaming studies, studyUID is used as shareID to ensure backwards-compatibility of some workflows
  schemas:
    Provider:
      title: Provider
      type: object
      x-tags:
        - providers
      properties:
        providerId:
          type: integer
        orgId:
          type: integer
        name:
          type: string
        address:
          type: string
        feeAmount:
          type: number
          description: Before tax
        taxName:
          type: string
        taxPercent:
          type: string
        region:
          type: string
        urlName:
          type: string
    ProviderConfig:
      title: ProviderConfig
      type: object
      x-tags:
        - providerConfig
      properties:
        settings:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ProviderConfigSetting"
    ProviderConfigSetting:
      title: ProviderConfigSetting
      type: object
      x-tags:
        - providerConfigSetting
      properties:
        showUpsell:
          type: boolean
        showCoreUpsell:
          type: boolean
        disabledFeatures:
          type: array
          items:
            type: number
        recordStreaming:
          type: boolean
    Share:
      title: Share
      type: object
      x-tags:
        - shares
      description: Object to describe a share.
      properties:
        shareId:
          type: string
          description: This should not be populated on POST
        status:
          $ref: "#/components/schemas/FaxStatus"
        examList:
          type: array
          items:
            $ref: "#/components/schemas/Exam"
        recipient:
          type: string
          description: Populate with fax number for fax shares and email address for email shares. Blank for print shares.
        method:
          $ref: "#/components/schemas/ShareMethod"
        mode:
          $ref: "#/components/schemas/ShareMode"
        expiry:
          type: string
          description: Not populated on POST
        active:
          type: boolean
          description: Not populated on POST
        date:
          type: string
        eUnityToken:
          type: string
        ccUser:
          type: boolean
        extendedExpiry:
          type: string
        healthRecords:
          type: array
          items:
            $ref: "#/components/schemas/Record"
        hrPatient:
          $ref: "#/components/schemas/HRPatient"
        containsDeletedExam:
          type: boolean
    NotificationRead:
      title: NotificationRead
      type: object
      x-tags:
        - users
      properties:
        read:
          type: boolean
    Notifications:
      title: Notifications
      type: object
      x-tags:
        - users
      properties:
        notifications:
          type: array
          items:
            $ref: "#/components/schemas/Notification"
    Notification:
      title: Notification
      type: object
      x-tags:
        - users
      properties:
        id:
          type: string
        type:
          type: string
        read:
          type: boolean
        patient_id:
          type: string
        created_date:
          type: string
          format: date-time
    Exam:
      title: Exam
      type: object
      x-tags:
        - users
        - shares
      properties:
        series:
          type: array
          items:
            $ref: "#/components/schemas/Series"
        reports:
          type: array
          items:
            $ref: "#/components/schemas/Report"
        examId:
          type: string
          description: Not populated on POST
        patientName:
          $ref: "#/components/schemas/PatientName"
        examType:
          type: string
        provider:
          type: string
        examDate:
          type: string
        activated:
          type: boolean
          description: Not populated on POST
        description:
          type: string
        dob:
          type: string
        sex:
          type: string
        transferId:
          type: string
        transferStatus:
          type: string
        orgId:
          type: integer
        bodyPart:
          type: string
        reportDelay:
          type: integer
          description: "When reportDelay is -1, the organization does not send reports."
        uuid:
          type: string
        referringPhysician:
          type: string
        size:
          type: integer
        modality:
          type: string
          description: "unparsed, abbreviated exam modality"
    Referral:
      title: Referral
      type: object
      x-tags:
        - users
      properties:
        email:
          type: string
    Setup:
      title: Setup
      type: object
      x-tags:
        - v2users
      properties:
        token:
          type: string
        password:
          type: string
    Verify:
      title: Verify
      type: object
      x-tags:
        - v2users
      properties:
        token:
          type: string
    PaymentCard:
      title: PaymentCard
      type: object
      x-tags:
        - subscriptions
      properties:
        lastFour:
          type: string
        brand:
          type: string
        expiryYear:
          type: number
        expiryMonth:
          type: number
    ShortUrl:
      title: ShortUrl
      type: object
      properties:
        slug:
          type: string
        original_url:
          type: string
    ImageMetadata:
      title: ImageMetadata
      type: object
      x-tags:
        - images
      properties:
        referringPhysician:
          type: string
        type:
          type: string
        bodyPart:
          type: string
        description:
          type: string
        clinicName:
          type: string
        date:
          type: string
        clinicId:
          type: integer
        activated:
          type: boolean
        examId:
          type: string
    RequestFormConfig:
      title: RequestFormConfig
      type: object
      x-tags:
        - providers
      properties:
        singleDate:
          type: boolean
        recentAndComment:
          type: boolean
        mrn:
          type: string
        delegateConsent:
          type: string
        examType:
          type: string
        patientConsent:
          type: string
        examSite:
          type: string
        examSiteMrnPrefix:
          type: string
        modalTypes:
          type: array
          items:
            type: string
        provider:
          $ref: "#/components/schemas/Provider"
        payment:
          type: boolean
        altID:
          type: string
        multilocations:
          type: boolean
        delegate:
          type: boolean
        reports:
          type: boolean
        recordText:
          type: string
        paymentText:
          type: string
        enrollConsent:
          type: boolean
        legal:
          type: boolean
        legalSurcharge:
          type: string
        ohip:
          type: boolean
        "ipn ":
          type: boolean
        ssn:
          type: boolean
        bcphn:
          type: boolean
        affiliatedText:
          type: string
        minConsentAge:
          type: integer
          format: int32
        examDate:
          type: boolean
        patientAddress:
          type: boolean
        enrollOpt:
          type: boolean
        base64Logo:
          type: string
        surveyId:
          type: integer
        showLoginLink:
          type: boolean
        showZohoLiveChat:
          type: boolean
        requireDelegateReview:
          type: boolean
    ShareMethod:
      type: string
      title: ShareMethod
      enum:
        - Email
        - AccessPagePrint
        - AccessPageFax
        - ZIP
        - ISO
      description: "String enum for share method. AccessPagePrint, AccessPageFax, or Email"
      x-tags:
        - shares
    Request:
      title: Request
      type: object
      x-tags:
        - requests
      description: ""
      properties:
        requestId:
          type: integer
          description: Not populated on POST
        firstName:
          type: string
        lastName:
          type: string
        altLastName:
          type: string
        ohip:
          type: string
        ohipvc:
          type: string
        mrn:
          type: string
        otherId:
          type: string
        dob:
          type: string
        tel:
          type: string
        email:
          type: string
        contents:
          $ref: "#/components/schemas/StudyRequest"
        ssn:
          type: string
        ipn:
          type: string
        orgId:
          type: integer
        providerId:
          type: integer
        altId:
          type: string
        comment:
          type: string
        bcphn:
          type: string
        patientId:
          type: string
    StudyRequest:
      title: StudyRequest
      type: object
      x-tags:
        - requests
      properties:
        mode:
          type: string
          enum:
            - details
            - daterange
        allStudies:
          type: boolean
        details:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        enrollment_consent:
          type: boolean
        recent_exam_details:
          type: object
          properties:
            exam_type:
              type: string
            exam_site:
              type: string
            exam_date:
              type: string
              description: Year+Month
            address:
              type: object
              properties:
                street_number:
                  type: string
                street_name:
                  type: string
                postal_code:
                  type: string
        delegate:
          type: object
          properties:
            firstName:
              type: string
            lastName:
              type: string
            relation:
              type: string
            address:
              type: string
            phone:
              type: string
            relationType:
              type: string
            dob:
              type: string
              example: "01/02/2006"
        patientAddress:
          type: string
        providerAddress:
          type: string
    PaymentToken:
      title: PurchaseToken
      type: object
      properties:
        token:
          type: string
          description: used if paymentProvider is Stripe
        paymentProvider:
          type: string
          enum:
            - Stripe
          description: "Stripe"
        planId:
          type: integer
          description: for order creation
    V2EmailUpdate:
      title: V2EmailUpdate
      type: object
      properties:
        new_email:
          type: string
    ShareCredentials:
      title: ShareCredentials
      type: object
      properties:
        shareId:
          type: string
        pin:
          type: string
        viewcode:
          type: string
        dob:
          type: string
        lastName:
          type: string
      description: "Credentials for validating a share view - only one pair should be used: shareId and pin or viewcode and dob or viewcode and dob and lastName."
      x-tags:
        - shares
    LoginCredentials:
      title: LoginCredentials
      type: object
      properties:
        email:
          type: string
        password:
          type: string
      x-tags:
        - users
    EmailVerification:
      title: EmailVerification
      type: object
      properties:
        accountId:
          type: string
        isVerified:
          type: boolean
      x-tags:
        - users
    DOBVerification:
      title: DOBVerification
      type: object
      properties:
        accountId:
          type: string
        dateOfBirth:
          type: string
      x-tags:
        - users
    PasswordResetInfo:
      title: PasswordResetInfo
      type: object
      properties:
        token:
          type: string
        securityCode:
          type: string
        newPassword:
          type: string
      x-tags:
        - users
    ConsentEmailVerification:
      title: ConsentEmailVerification
      type: object
      properties:
        token:
          type: string
        email:
          type: string
      x-tags:
        - providers
    AccountState:
      title: AccountState
      type: object
      properties:
        created_at:
          type: string
        language:
          type: string
      x-tags:
        - users
    NewRequest:
      title: NewRequest
      type: object
      x-tags:
        - requests
      properties:
        requestBody:
          $ref: "#/components/schemas/Request"
        paymentToken:
          $ref: "#/components/schemas/PaymentToken"
        signatureImg:
          type: string
          format: base64
          description: signature image base64
        delegForm:
          type: string
          description: delegate supporting evidence file
        delegatePhotoId:
          type: string
          description: delegate supporting photo id ( only 1 file allowed to be uploaded)
        subscribeToConnect:
          type: boolean
          description: Whether to use the payment details to subscribe the user to Connect
    NewUPHRequest:
      title: NewRequest
      type: object
      x-tags:
        - requests
      properties:
        requestBody:
          $ref: "#/components/schemas/Request"
        paymentToken:
          $ref: "#/components/schemas/PaymentToken"
        signatureImg:
          type: string
          format: base64
          description: signature image base64
        subscribeToConnect:
          type: boolean
          description: Whether to use the payment details to subscribe the user to Connect
    Series:
      title: Series
      type: object
      properties:
        seriesId:
          type: string
        description:
          type: string
        instances:
          type: array
          items:
            $ref: "#/components/schemas/Image"
      x-tags:
        - users
    Image:
      title: Image
      type: object
      properties:
        ImageId:
          type: string
        Token:
          type: string
    Report:
      title: Report
      type: object
      x-tags:
        - reports
      properties:
        reportId:
          type: string
        clinicId:
          type: integer
        clinicName:
          type: string
        parsedTime:
          type: string
        uploadTime:
          type: string
        activated:
          type: boolean
        referringPhysician:
          type: string
        size:
          type: integer
        inactivatedTransferId:
          type: string
        patientName:
          $ref: "#/components/schemas/PatientName"
        dob:
          type: string
        sex:
          type: string
        protocol:
          type: string
        definitions:
          type: boolean
    PatientName:
      title: PatientName
      type: object
      properties:
        dicomName:
          type: string
        firstAndMiddleName:
          type: string
        lastName:
          type: string
      x-tags:
        - users
    PendingRequest:
      title: PendingRequest
      type: object
      x-tags:
        - requests
      properties:
        firstName:
          type: string
        lastName:
          type: string
        altLastName:
          type: string
        patientId:
          type: string
        clinicId:
          type: integer
        orgId:
          type: integer
        timestamp:
          type: string
        clinicName:
          type: string
        date:
          type: string
        requestId:
          type: integer
        editable:
          type: boolean
        status:
          type: string
        dob:
          type: string
        ohip:
          type: string
        ohipVc:
          type: string
        isBcphn:
          type: boolean
        mrn:
          type: string
        ssn:
          type: string
        ipn:
          type: string
        altId:
          type: string
        contents:
          $ref: "#/components/schemas/StudyRequest"
        providerName:
          type: string
        providerLogo:
          type: string
    ShareMode:
      type: string
      title: ShareMode
      enum:
        - all
        - multiple
      x-tags:
        - shares
    FaxStatus:
      type: string
      title: FaxStatus
      enum:
        - delivered
        - failed
        - no-answer
        - busy
        - canceled
      description: Fax Status enum
      x-tags:
        - shares
    PaymentStatus:
      type: string
      title: PaymentStatus
      enum:
        - prepaid
        - purchased
        - gracePeriod
        - unpaid
        - premium
    FeeInfo:
      title: FeeInfo
      type: object
      properties:
        feeAmount:
          deprecated: true
          type: number
        taxName:
          deprecated: true
          type: string
        taxPercent:
          deprecated: true
          type: number
          format: float
        region:
          type: string
    TransferExamSummary:
      title: TransferExamSummary
      type: object
      properties:
        uuid:
          type: string
        type:
          type: string
        examDate:
          type: string
        description:
          type: string
        provider:
          type: string
        referringPhysician:
          type: string
        reportIncluded:
          type: boolean
    Transfer:
      title: Transfer
      type: object
      x-tags:
        - transfers
      properties:
        paymentStatus:
          $ref: "#/components/schemas/PaymentStatus"
        feeInfo:
          $ref: "#/components/schemas/FeeInfo"
        providerName:
          type: string
        providerId:
          type: integer
        exams:
          $ref: "#/components/schemas/TransferExamSummary"
    RejectVerifyDetails:
      title: RejectVerifyDetails
      type: object
      properties:
        recentExamDate:
          type: string
        hasOlderExams:
          type: boolean
        providerConfirmed:
          type: boolean
        providerId:
          type: number
        providerText:
          type: string
        examTypes:
          type: array
          items:
            type: string
        otherExamType:
          type: string
        comments:
          type: string
      required:
        - recentExamDate
        - hasOlderExams
        - providerConfirmed
    ChallengeQuestion:
      title: ChallengeQuestion
      type: object
      properties:
        question:
          type: string
        userExists:
          type: boolean
        activated:
          type: boolean
        examId:
          type: string
        questionType:
          type: string
    ChallengeToken:
      title: ChallengeToken
      type: object
      properties:
        token:
          type: string
        userExists:
          type: boolean
        userEmail:
          type: string
        examId:
          type: string
    ConsentType:
      title: ConsentType
      type: object
      x-tags:
        - providers
      description: "The consent form type (verified or unverified)"
      properties:
        verified:
          type: boolean
    ConsentFormData:
      title: ConsentFormData
      type: object
      x-tags:
        - providers
      description: ""
      properties:
        providerName:
          type: string
        new:
          type: boolean
        opt:
          type: string
        consentText:
          type: string
        understandItems:
          type: array
          description: ""
          items:
            type: string
        appointmentReminderId:
          type: string
        consentSource:
          type: string
    Consent:
      title: Consent
      type: object
      properties:
        opt:
          type: string
        fullName:
          type: string
        signatureImg:
          type: string
          format: base64
          description: base64 encoded signature image
    UploadFileMetadata:
      title: UploadFileMetadata
      type: object
      properties:
        instanceNumber:
          type: string
        studyDate:
          type: string
        protocolName:
          type: string
        modality:
          type: string
        reportType:
          $ref: "#/components/schemas/UploadReportType"
    UploadReportType:
      type: string
      title: UploadReportType
      enum:
        - MHT
        - PDF
    UploadImage:
      title: UploadImage
      type: object
      description: V2 model for images to upload
      properties:
        partname:
          type: string
        study_id:
          type: string
        series_id:
          type: string
        instance_id:
          type: string
        sha1:
          type: string
      required:
        - partname
        - study_id
        - series_id
        - instance_id
        - sha1
      x-tags:
        - transfers
        - v2uploads
    UploadInitResponse:
      title: UploadInitResponse
      type: object
      properties:
        transferId:
          type: string
        uploadSessionId:
          type: string
      x-tags:
        - transfers
        - v2uploads
    OnboardingTask:
      title: OnboardingTask
      type: integer
      format: uint
      description: "TaskVideo=1, TaskRequest=2, TaskUpload=4, TaskConnect=8"
      enum:
        - 1
        - 2
        - 4
        - 8
    OnboardingTaskCompletion:
      title: OnboardingTaskCompletion
      type: object
      properties:
        completeVideo:
          type: boolean
        completeRequest:
          type: boolean
        completeUpload:
          type: boolean
        completeConnect:
          type: boolean
    PaymentError:
      title: PaymentError
      type: string
      enum:
        - Declined
        - Expired
        - CardTypeNotAccepted
        - IncorrectCVC
        - IncorrectCardNumber
        - GenericError
    DLToken:
      title: DLToken
      type: object
      description: ""
      properties:
        token:
          type: string
    UploadRecordMetadata:
      title: UploadRecordMetadata
      type: object
      properties:
        filename:
          type: string
        source:
          $ref: "#/components/schemas/RecordSource"
        description:
          type: string
        createdDate:
          type: string
        tag:
          type: string
    RecordSource:
      title: RecordSource
      type: object
      properties:
        type:
          type: string
        description:
          type: string
      x-examples:
        example-1:
          value:
            type: string
            description: string
    RequestFormStep:
      type: string
      title: RequestFormStep
      enum:
        - PATIENT_INFO
        - RECENT_EXAM_INFO
        - FUTURE_EXAM_NOTIFICATIONS
        - CONSENT
        - PAYMENT
        - COMPLETED
        - PATIENT_INFO_CONFIRM
      x-tags:
        - requests
      description: Steps in the Request Form.
    IncompleteRequest:
      title: IncompleteRequest
      type: object
      properties:
        requestData:
          $ref: "#/components/schemas/Request"
        lastCompletedStep:
          $ref: "#/components/schemas/RequestFormStep"
      required:
        - requestData
        - lastCompletedStep
    IncompleteRequestMetadata:
      title: IncompleteRequestMetadata
      type: object
      properties:
        clinicName:
          type: string
        status:
          type: string
          enum:
            - INCOMPLETE
            - COMPLETE
            - EXPIRED
        emailStep:
          type: string
        clinicId:
          type: number
      required:
        - clinicName
        - status
        - clinicId
        - emailStep
    IncompleteRequestInitResponse:
      title: IncompleteRequestInitResponse
      type: object
      properties:
        incompleteRequestId:
          type: string
        authToken:
          type: string
      required:
        - incompleteRequestId
        - authToken
    IncompleteRequestVerify:
      title: IncompleteRequestVerify
      type: object
      properties:
        dob:
          type: string
          description: Date of birth of patient associated with request.
        data:
          type: string
          description: JWE Token with request data
      required:
        - dob
        - data
    EmailStatusCallback:
      title: EmailStatusCallback
      type: object
      properties:
        is_success:
          type: boolean
        error:
          type: string
          description: error message for failed email
      required:
        - is_success
    IncompleteRequestResponse:
      title: IncompleteRequestResponse
      type: object
      properties:
        requestData:
          $ref: "#/components/schemas/Request"
        lastCompletedStep:
          $ref: "#/components/schemas/RequestFormStep"
        authToken:
          type: string
      required:
        - requestData
        - lastCompletedStep
        - authToken
    Record:
      title: Record
      type: object
      x-tags:
        - v2healthrecords
      properties:
        id:
          type: string
        name:
          type: string
        source:
          $ref: "#/components/schemas/RecordSource"
        recordDate:
          type: string
        description:
          type: string
        typeCode:
          $ref: "#/components/schemas/RecordTypeCode"
        createdDate:
          type: string
        filenames:
          type: string
        tag:
          type: string
          pattern: "^[A-Za-z0-9_]*$"
          maxLength: 50
    RecordTypeCode:
      type: integer
      title: RecordTypeCode
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 10
      x-enum-varnames:
        - ALLERGY_CODE
        - CONDITION_CODE
        - VACCINATION_CODE
        - LAB_RESULT_CODE
        - MEDICAL_TEST_CODE
        - PRESCRIPTION_CODE
        - PROCEDURE_CODE
        - REFERRAL_CODE
        - TREATMENT_PLAN_CODE
        - OTHER_CODE
      x-tags:
        - v2healthrecords
    HRPatient:
      title: Patient
      type: object
      properties:
        phUserId:
          type: integer
        phProfileId:
          type: integer
        firstAndMiddle:
          type: string
        lastName:
          type: string
      description: Patient for HRs
    RegisterData:
      title: RegisterData
      type: object
      properties:
        email:
          type: string
        password:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        dob:
          type: string
          format: date-time
          description: ISO8601 format
          example: "2021-08-18T00:00:00+07:00"
        redirect_url:
          type: string
        share_id:
          type: string
      required:
        - email
        - password
    ERSubmitRequest:
      description: Object that forms a valid expert review request
      type: object
      x-examples:
        example-1:
          exam_uuid: 1udkrprolW847Q3BP2cTxz1dTM9
          payment_provider: moneris
          payment_token: ed5mMcUwumsP6Ug00p5Ig0Cj4
          amount: 14900
          zip: M5T1R4
          country: CA
      properties:
        exam_uuid:
          type: string
          minLength: 1
        payment_provider:
          type: string
          minLength: 1
        payment_token:
          type: string
          minLength: 1
        amount:
          type: number
          minimum: 0
          exclusiveMinimum: true
        zip:
          type: string
          minLength: 1
        country:
          type: string
          minLength: 1
      required:
        - exam_uuid
        - payment_provider
        - payment_token
        - amount
        - zip
        - country
    EREligibility:
      title: EREligibility
      type: object
      description: Object that describes a response for the eligibility of an exam for an expert review
      properties:
        eligible:
          type: boolean
        review_status:
          type: string
          pattern: ^$|^pending$|^completed$
    PublicReferEmailThrottle:
      title: PublicReferEmailThrottle
      type: object
      properties:
        email_throttle_number:
          type: number
        available_number:
          type: number
    PublicReferral:
      title: PublicReferral
      type: object
      properties:
        emails:
          type: array
          items:
            type: string
    TaggedHTML:
      title: TaggedHTML
      type: object
      properties:
        defs:
          type: object
        report:
          type: string
      x-examples:
        example-1:
          defs:
            term1: def1
            term2: def2
          report: <div>Report with <span class=phdefinition>term1</span>
    Plan:
      title: Plan
      x-stoplight:
        id: exx5987zqrbn6
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        display_name:
          type: string
        created_at:
          type: string
        is_active:
          type: boolean
        is_recurring:
          type: boolean
        amount:
          type: integer
        period_unit:
          type: string
        period_length:
          type: integer
        region_id:
          type: integer
        charge_tax:
          type: boolean
        is_collapsed:
          type: boolean
    OrderRequest:
      title: OrderRequest
      x-stoplight:
        id: yx24fb96aneu6
      type: object
      properties:
        plan_id:
          type: integer
        token:
          type: string
      x-tags:
        - v2orders
    CreateOrderResponse:
      title: CreateOrderResponse
      x-stoplight:
        id: a4zb09ergdkf3
      type: object
      properties:
        order_id:
          type: string
        provider_sub_id:
          type: string
        provider_cust_id:
          type: string
        provider_txn_id:
          type: string
      x-tags:
        - v2orders
    Order:
      title: Order
      x-stoplight:
        id: 5roir9o5t0q1g
      type: object
      description: ""
      properties:
        order_id:
          type: string
        plan_id:
          type: integer
        is_auto_renewing:
          type: boolean
        created_at:
          type: string
        expires_at:
          type: string
        payment_method:
          $ref: "#/components/schemas/PaymentCard"
    SOEligibility:
      title: SOEligibility
      type: object
      properties:
        eligible:
          type: boolean
        review_status:
          type: string
      description: Object that describes a response for the eligibility of an exam for second opinion
    SOReviewRequest:
      title: SOReviewRequest
      x-stoplight:
        id: a058c26d227e9
      type: object
      description: Object that contains all the patient entered details for requesting a second opinion.
      properties:
        prior_exams:
          type: array
          items:
            type: string
        request_patient_detail:
          type: object
          properties:
            patient_name:
              type: string
            dob:
              type: string
            ohip:
              type: string
            ohip_version_code:
              type: string
            ohip_expiration_date:
              type: string
            phone:
              type: string
        request_referring_detail:
          type: object
          properties:
            reason:
              type: string
            questions:
              type: array
              items:
                type: string
            referring_physician:
              type: object
              properties:
                cpso:
                  type: integer
                first_name:
                  type: string
                last_name:
                  type: string
                address:
                  type: string
                fax:
                  type: string
                phone:
                  type: string
                phone_ext:
                  type: string
    SOCPSODoctor:
      title: SOCPSODoctor
      x-stoplight:
        id: 3m5y3axr85nn1
      type: object
      description: second opinion service doctor
      properties:
        id:
          type: integer
        first_name:
          type: string
        last_name:
          type: string
        phone:
          type: string
        phone_ext:
          type: string
        fax:
          type: string
        address_1:
          type: string
        address_2:
          type: string
        address_3:
          type: string
        address_4:
          type: string
        address_5:
          type: string
        cpso:
          type: integer
        additional_locations:
          type: string
        specialization:
          type: string
    VerificationConfig:
      title: VerificationConfig
      type: object
      properties:
        org_id:
          type: string
        active:
          type: boolean
        created_at:
          type: string
          format: date-time
        idv_link:
          type: string
    SetAccountOwnerRequest:
      title: SetAccountOwnerRequest
      type: object
      x-tags:
        - v2users
      properties:
        patient_id:
          type: string
    Patient:
      title: Patient
      type: object
      x-tags:
        - v2patients
      properties:
        patientId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        altLastName:
          type: string
        DOB:
          type: string
        ohip:
          type: string
        ohipvc:
          type: string
        bcphn:
          type: string
        altId:
          type: string
        phone:
          type: string
        email:
          type: string
        ipn:
          type: string
        ssn:
          type: string
        sex:
          type: string
        country:
          type: string
        subdivision:
          type: string
          description: essentially state/province
        postalCode:
          type: string
        isAccountOwner:
          type: boolean
    PatientProfileValidation:
      title: PatientProfileValidation
      type: object
      properties:
        valid:
          type: boolean
        first_name:
          type: boolean
        last_name:
          type: boolean
        dob:
          type: boolean
        sex:
          type: boolean
        location:
          type: boolean
    CreateRequest:
      title: CreateRequest
      type: object
      x-tags:
        - requests
      properties:
        requestDetails:
          $ref: "#/components/schemas/Request"
        paymentToken:
          $ref: "#/components/schemas/PaymentToken"
        signatureImg:
          type: string
          format: base64
          description: signature image base64
        minorSignatureImg:
          type: string
          format: base64
          description: delegate patient signature image base64
        delegateForm:
          type: string
          description: delegate supporting evidence file
        delegatePhotoId:
          type: string
          description: delegate supporting photo id ( only 1 file allowed to be uploaded)
        subscribeToConnect:
          type: boolean
          description: Whether to use the payment details to subscribe the user to Connect
          deprecated: true
    AnalysisEligibility:
      title: AnalysisEligibility
      type: object
      x-tags:
        - rho
      properties:
        eligible:
          type: boolean
          description: exam eligible to rho
        status:
          type: string
          enum:
            - not started
            - submitted
            - in progress
            - rejected
            - completed
            - blocked
            - error
            - unknown status
        request_id:
          type: string
          description: rho request id if exam has existing rho request
    PhysicianAccount:
      title: PhysicianAccount
      type: object
      required:
        - id
      properties:
        id:
          type: string
        email:
          type: string
        mainRegion:
          type: integer
        region:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        physicians:
          type: array
          items:
            $ref: "#/components/schemas/Physician"
        permissionsMap:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: "#/components/schemas/PhysicianPermission"
    Physician:
      title: Physician
      type: object
      required:
        - id
        - accountId
      properties:
        id:
          type: string
        accountId:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        isEmailVerified:
          type: boolean
        fax:
          type: string
        isFaxVerified:
          type: boolean
        phone:
          type: string
        defaultNotificationMethod:
          $ref: "#/components/schemas/NotificationMethod"
        address:
          type: string
        otherInfo:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        physicianLicenses:
          type: array
          items:
            $ref: "#/components/schemas/PhysicianLicense"
    PhysicianLicense:
      title: PhysicianLicense
      type: object
      required:
        - id
        - physicianId
      properties:
        id:
          type: string
        physicianId:
          type: string
        licenseType:
          type: string
        licenseNumber:
          type: string
        stateOrProvince:
          type: string
        createdAt:
          type: string
          format: date-time
        expiryDate:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    NotificationMethod:
      type: string
      title: NotificationMethod
      enum:
        - Fax
        - Email
      description: Notification method for physician accounts
    PhysicianPatientSummary:
      title: PhysicianPatientSummary
      x-stoplight:
        id: tg28gbkrrt3s7
      type: object
      description: patient info for a physician
      properties:
        id:
          type: string
          description: identifier of the patient summary,
            based on either patientId (for patient shares)
            OR patient name, dob and legacy provider id (for provider shares)
        patientId:
          type: string
          description: id of pockethealth account that shared records,
            only set for patient shares
        providerId:
          type: integer
          description: legacy provider id of provider who shared records,
            only set for provider shares
        orgName:
          type: string
          description: name of provider who shared records,
            only set for provider shares
        shareIds:
          type: array
          items:
            type: string
            description:
              ids of shares (or record streaming studies) shared by provider,
              only set for provider shares
        firstName:
          type: string
          description: first name of patient
        lastName:
          type: string
          description: last name of patient
        patientName:
          $ref: "#/components/schemas/PatientName"
          description:
            name of the patient - in DICOM format as stored in study's DICOM tags
            and split up into first+middle and last name for display
        dob:
          type: string
          description: date of birth of patient
        recordNum:
          type: integer
          description: number of records shared for this patient
        referringPhysicians:
          type: array
          items:
            $ref: "#/components/schemas/PatientName"
            description: List of unique names of referring physicians for records shared for this patient
        physicianAccountId:
          type: string
          description: account ID of the physician that has permissions on the study
        permissionGroups:
          type: array
          items:
            $ref: "#/components/schemas/PhysicianPermissionGroup"
            description: list of permission groups that are associated with the study
    PhysicianPermissionGroup:
      title: PhysicianPermissionGroup
      type: object
      required:
        - groupId
      properties:
        groupId:
          type: integer
          format: int64
        groupName:
          type: string
    PhysicianShareInfo:
      title: PhysicianShareInfo
      x-stoplight:
        id: a99dcde65d71d
      type: object
      description: share info of an exam -
        containing shareId and expiry if access to exam can expire,
        extendedExpiry if access to exam can expire and has been extended, and
        eunityToken needed to view exam if access is not expired
      required:
        - shareId
      properties:
        shareId:
          type: string
        expiry:
          type: string
        extendedExpiry:
          type: string
        eunityToken:
          type: string
    PhysicianPermission:
      title: PhysicianPermission
      type: object
      description: permission for a physician account
      required:
        - permissionId
        - permissionName
      properties:
        permissionId:
          type: integer
        permissionName:
          type: string
        permissionDescription:
          type: string
    PhysicianSearchQuery:
      type: object
      required:
        - firstName
        - lastName
        - dateOfBirth
      properties:
        firstName:
          type: string
        lastName:
          type: string
        dateOfBirth:
          type: string
          format: date
          description: date of birth in format yyyy-mm-dd
        studyStartDate:
          type: string
          format: date
          description: earliest study date in format yyyy-mm-dd
        studyEndDate:
          type: string
          format: date
          description: latest study date in format yyyy-mm-dd
    PhysicianStudyRequest:
      type: object
      required:
        - studyUID
      properties:
        studyUID:
          type: string
          description: DICOM UUID of study
        patientId:
          type: string
          description: MRN of patient associated with study
        issuer:
          type: string
          description: issuer of MRN
    RequestStatusHistory:
      title: RequestStatusHistory
      type: object
      properties:
        requestId:
          type: string
        trackingState:
          type: string
          enum:
            - Action Needed
            - Processing
            - Transferring
            - Ready
        status:
          type: string
          enum:
            - UNFINALIZED
            - UNDER_REVIEW
            - FULFILLED
            - SUPPRESS
            - REJECTED
            - VOID
            - DELEGATE_PENDING_AUTH
            - VERIFICATION_PENDING
            - APPROVAL_REQUIRED
        createdAt:
          type: string
          format: date-time
      description: ""
    MyChartIntegrationRequest:
      title: MyChartIntegrationRequest
      type: object
      properties:
        code:
          type: string
          description: access code from mychart oauth flow
      required:
        - code
      description: MyChart Health Records integration request
      x-tags:
        - v2healthrecords
    GailQuestionnaireResponse:
      title: Gail Questionnaire Responses
      type: object
      properties:
        eligibility_check:
          $ref: "#/components/schemas/RiskEligibility"
        ethnicity:
          type: string
          enum:
            - Wh
            - AA
            - H
            - NA
            - A
          description: >
            Ethnicity:
             * `Wh` - White
             * `AA` - African American
             * `H` - Hispanic
             * `NA` - Native American or Unknown
             * `A` - Asian
        sub_ethnicity:
          type: string
          enum:
            - HU
            - HF
            - Ch
            - Ja
            - Fi
            - Hw
            - oP
            - oA
          description: >
            Ethnicity:
             * `HU` - Hispanic American
             * `HF` - hispanic american foreign born
             * `Ch` - chinese american
             * `Ja` - japanese american
             * `Fi` - filipino american
             * `Hw` - hawaiian american
             * `oP` - other pacific islander
             * `oA` - other asian
        age_men:
          type: number
          description: age of first menstruation
        age_birth:
          type: number
          description: current age
        first_degree_relatives:
          $ref: "#/components/schemas/OneOrMore"
        breast_biopsy:
          $ref: "#/components/schemas/BreastBiopsyFactor"
      required:
        - eligibility_check
        - ethnicity
        - sub_ethnicity
        - age_men
        - age_birth
        - first_degree_relatives
        - breast_biopsy
      x-tags:
        - v2healthrecords
    BreastBiopsyFactor:
      title: BreastBiopsyFactor
      type: object
      properties:
        exists:
          $ref: "#/components/schemas/YesNoUnknown"
        benign_biopsies:
          $ref: "#/components/schemas/OneOrMore"
        atypical_hyperplasia:
          $ref: "#/components/schemas/OneOrMore"
    RiskEligibility:
      title: Breast Risk Scoring Eligibility
      type: object
      properties:
        breast_cancer_history:
          $ref: "#/components/schemas/YesNoUnknown"
        has_hodgkin_lymphoma:
          $ref: "#/components/schemas/YesNoUnknown"
        has_gene_mutations:
          $ref: "#/components/schemas/YesNoUnknown"
        genetic_risk_breast_cancer:
          $ref: "#/components/schemas/YesNoUnknown"
        has_current_condition:
          $ref: "#/components/schemas/CurrentCondition"
    CurrentCondition:
      type: string
      enum:
        - "DCIS"
        - "LCIS"
        - "0"
    OneOrMore:
      type: number
      enum:
        - 0
        - 1
        - 2
        - 99
      description: >
        OneOrMore:
          * `0` - none
          * `1` - one
          * `2` - more than one
          * `99` - unknown
    YesNoUnknown:
      type: number
      enum:
        - 0
        - 1
        - 99
      description: >
        YesNoUnknown:
          * `0` - no
          * `1` - yes
          * `99` - unknown
    GailRiskScore:
      type: object
      properties:
        risk_five_years:
          type: number
        risk_lifetime:
          type: number
    OrgInfo:
      title: MychartOrgInfo
      type: object
      properties:
        id:
          type: string
          description: Id of the Org
        display_name:
          type: string
          description: name of the Org
        api_url:
          type: string
          description: url required to make a mychart connection with the org
    OrganVisualizationRequest:
      title: OrganVisualizationRequest
      type: object
      required:
        - exam_uuid
        - body_parts
      properties:
        body_parts:
          type: array
          items:
            type: string
          description: list of body parts that we want to get segmentations on
        series_uid:
          type: string
          description: series_uid that we want to pick the image slice from
        object_id:
          type: string
          description: object_id that we want to use as for the segmentation
    OrganVisualization:
      type: object
      properties:
        body_part:
          type: string
          description: body_part of the segmentation
        segmentation:
          type: string
          description: segmentation image, as base64 string
        status:
          type: string
          description: status of the segmentation inference, IN_PROGRESS, COMPLETED, ERROR
    InteractiveOrganVisualization:
      type: object
      properties:
        svg:
          type: string
          description: svg file with all segmentations
        preview:
          type: string
          description: a preview of the svg
        status:
          type: string
          description: status of the segmentation inference, IN_PROGRESS, COMPLETED, ERROR
    ExamInsightFollowup:
      type: object
      properties:
        report_id:
          type: string
        has_followup:
          type: boolean
    ExamInsightEligibility:
      type: object
      properties:
        rho:
          type: boolean
        second_opinion:
          type: boolean
        report:
          type: boolean
        followups:
          type: array
          items:
            type: string
    ExamInsightEligibilityResponse:
      type: object
      additionalProperties:
        $ref: "#/components/schemas/ExamInsightEligibility"
      example:
        examuuid1: { "rho": true, "second_opinion": false, "followups": {} }
        examuuid2:
          {
            "rho": false,
            "second_opinion": false,
            "followups":
              [
                {
                  "report_id": "123abc",
                  "has_followup": true,
                  "occurrences": [{ "context": "some follow-up for user" }],
                },
              ],
          }
    CreatePatientEligibilityProgramRequest:
      type: object
      properties:
        patient_id:
          type: string
        program_name:
          type: string
        is_completed:
          type: boolean
          description: The newly created PEP should be marked as completed
        with_notification:
          type: boolean
          description: If an uncompleted PEP then create a notification for it.
    RecordUploadStatus:
      type: object
      required:
        - orgId
        - examId
        - progressPercent
      properties:
        orgId:
          type: integer
          description: legacy provider id
        examId:
          type: string
          description: DICOM UID of study
        uuid:
          type: string
          description: ksuid of exam object created during study upload, if available
        progressPercent:
          type: integer
          description: upload percentage of study -
            determined by how many images have been processed
            compared to total number of images in the study
        modality:
          type: string
          description: modality of the study - determined by the modality of the first series within the study
        examType:
          type: string
          description: type of exam - based on modality
        date:
          type: string
          format: date
          description: date of exam, in format 'yyyy/MM/dd'
        description:
          type: string
          description: description of study
        orgName:
          type: string
          description: name of provider, 'Unknown' if no name is available
        patientId:
          type: string
          description: id of patient within pockethealth patient account that owns this study
        unlockStatus:
          type: string
          description: availability of a certain study to a patient, e.g. locked
    PhysicianRecordUploadStatus:
      type: object
      required:
        - studyUID
        - providerID
        - uuid
        - hasReport
        - progressPercent
      properties:
        studyUID:
          type: string
          description: DICOM UID of study
        providerID:
          type: integer
          format: int64
          description: legacy provider id, also known as orgID
        uuid:
          type: string
          description: unique ksuid of DICOM study object created during study upload
        hasReport:
          type: boolean
          description: boolean describing if study has uploaded report, false if there is no report or a report has not been uploaded yet
        progressPercent:
          type: integer
          description: upload percentage of study -
            determined by how many images have been processed
            compared to total number of images in the study
    PhysicianRecordResponse:
      type: object
      description: response to a record streaming retrieval request made by a physician
      required:
        - studyUID
        - success
        - alreadyUploaded
      properties:
        studyUID:
          type: string
          description: DICOM identifier of study
        success:
          type: boolean
          description: indicates if the retrieval request for this study was successful
        alreadyUploaded:
          type: boolean
          description: indicates if the requested study is already fully available
        hasReport:
          type: boolean
          description: indicates if there is an uploaded report for the study
    InsightsResponse:
      type: object
      properties:
        reports:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ReportInsight"
        organviz_count:
          $ref: "#/components/schemas/TotalOrganViz"
    ReportInsight:
      type: object
      properties:
        organviz:
          $ref: "#/components/schemas/OrganViz"
        has_explanation:
          type: boolean
    OrganViz:
      type: object
      properties:
        num_masks_generated:
          type: integer
        model:
          type: string
    TotalOrganViz:
      type: object
      properties:
        xray_chest:
          type: integer
        ct_abd:
          type: integer
  securitySchemes:
    jwtBearer:
      type: http
      scheme: bearer
    phSignature:
      name: X-PH-Signature
      type: apiKey
      in: header
      description: authentication used for requests sent from RegionRouter. It is a signature of the form ES256(SHA256(requestBody))
