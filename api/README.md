# OpenAPI Schema

The `coreapi` OpenAPI spec is split across multiple domains for readability. The main `coreapi` spec is
provided in [`openAPI3spec.yaml`](openAPI3spec.yaml) with other domains in separate folders added via `$ref`, such as the [upload-requests](upload-requests/paths.yaml) domain.

The full `coreapi` spec is amalgamated into
[`/generated/spec/openapi.yaml`](../generated/spec/openapi.yaml). Consider it to
be the authoritative spec for what this service offers. Other services needing
to call `coreapi` endpoints should use clients generated off this spec.
