components:
  schemas:
    Appointment:
      title: Appointment
      description: An appointment with metadata
      type: object
      properties:
        id:
          type: integer
        description:
          type: string
        time:
          type: string
        prep_instruction_text:
          type: string
        prep_instruction_urls:
          type: array
          items:
            type: string
        modality:
          type: string
        procedure:
          type: string
      required:
        - modality
        - time
        - procedure
        - description
      x-tags:
        - appointmentreminders
    AppointmentDetails:
      title: AppointmentDetails
      description: A set of appointments for a patient. They should all be in the same clinic on the same day
      type: object
      properties:
        reminder_id:
          type: string
        patient_name:
          type: string
        appointment_date:
          format: date
          type: string
        patient_status:
          $ref: "#/components/schemas/PatientStatus"
        clinic:
          $ref: "#/components/schemas/ClinicInfo"
        appointments:
          type: array
          items:
            $ref: "#/components/schemas/Appointment"
      required:
        - reminder_id
        - patient_name
        - appointment_date
        - patient_status
        - clinic
        - appointments
      x-tags:
        - appointmentreminders
    AppointmentReminderPatientStatusUpdate:
      title: AppointmentReminderPatientStatusUpdate
      type: object
      properties:
        reminder_id:
          type: string
        patient_status:
          $ref: "#/components/schemas/PatientStatus"
      required:
        - reminder_id
        - patient_status
      description: Object that contains the id of an appointment reminder and the patient status it should be updated to
      x-tags:
        - appointmentreminders
    ClinicInfo:
      title: ClinicInfo
      type: object
      properties:
        name:
          type: string
        clinic_name_short:
          type: string
        address:
          type: string
        address_details:
          type: string
        address_short:
          type: string
        phone:
          type: string
        email:
          type: string
        url:
          type: string
      description: Clinic information for appointments
      x-tags:
        - appointmentreminders
    PatientStatus:
      type: string
      enum:
        - cancelled
        - cancel-initiated
        - confirmed
        - opted-out
        - to-reschedule
      x-tags:
        - appointmentreminders
