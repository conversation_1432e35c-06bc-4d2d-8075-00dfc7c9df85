paths:
  /v1/appointments:
    get:
      summary: ""
      operationId: get-v1-appointments
      tags:
        - appointmentreminders
      parameters:
        - name: provider_id
          description: Legacy ID of provider
          required: true
          schema:
            type: integer
            format: int64
          in: query
        - name: consent_id
          description: Consent ID
          required: true
          schema:
            type: string
          in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "./schemas.yaml#/components/schemas/AppointmentDetails"
          description: OK
        "400":
          content:
            application/json:
              schema:
                $ref: "../common/error.v1.yaml#/components/schemas/Error"
          description: Bad Request
        "401":
          content:
            application/json:
              schema:
                $ref: "../common/error.v1.yaml#/components/schemas/Error"
          description: Unauthorized
        "500":
          content:
            application/json:
              schema:
                $ref: "../common/error.v1.yaml#/components/schemas/Error"
          description: Internal Server Error
  /v1/appointments/patientstatus:
    post:
      summary: ""
      operationId: post-v1-appointments-patientstatus
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                records:
                  $ref: "./schemas.yaml#/components/schemas/AppointmentReminderPatientStatusUpdate"
                file:
                  type: string
        description: ""
      description: "Updates patient status for an appointment reminder with a given id, if status was not set yet. Returns Bad Request error if status was already set, Unauthorized on failed authentication and Not Found if no matching reminder exists."
      tags:
        - appointmentreminders
