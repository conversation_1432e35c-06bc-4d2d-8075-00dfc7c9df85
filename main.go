/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"

	amplitudeExperiment "github.com/amplitude/experiment-go-server/pkg/experiment/remote"
	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/appointmentreminders"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/checkout"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/download"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/forms"
	"gitlab.com/pockethealth/coreapi/pkg/images"
	"gitlab.com/pockethealth/coreapi/pkg/images/converter"
	"gitlab.com/pockethealth/coreapi/pkg/incompleterequests"
	"gitlab.com/pockethealth/coreapi/pkg/internalsvc"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/meddream"
	sqlOrgs "gitlab.com/pockethealth/coreapi/pkg/mysql/organizations"
	sqlProviders "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	"gitlab.com/pockethealth/coreapi/pkg/payment"
	"gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/providers"
	"gitlab.com/pockethealth/coreapi/pkg/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/recorduploadstatus"
	"gitlab.com/pockethealth/coreapi/pkg/refer"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/reports"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz"
	organviztopicapi "gitlab.com/pockethealth/coreapi/pkg/reports/organviz/topicapi"
	"gitlab.com/pockethealth/coreapi/pkg/requests"
	"gitlab.com/pockethealth/coreapi/pkg/secondopinion"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/pmts"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/setup"
	"gitlab.com/pockethealth/coreapi/pkg/shares"
	"gitlab.com/pockethealth/coreapi/pkg/short_urls"
	"gitlab.com/pockethealth/coreapi/pkg/subscriptions"
	"gitlab.com/pockethealth/coreapi/pkg/uploadrequests"
	"gitlab.com/pockethealth/coreapi/pkg/v2healthrecords"
	"gitlab.com/pockethealth/coreapi/pkg/v2patients"
	"gitlab.com/pockethealth/recordretrievalservice"

	auditservice "gitlab.com/pockethealth/coreapi/pkg/audit"
	"gitlab.com/pockethealth/coreapi/pkg/users"
	"gitlab.com/pockethealth/coreapi/pkg/util/emailToken"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	v2providers "gitlab.com/pockethealth/coreapi/pkg/v2Providers"
	"gitlab.com/pockethealth/coreapi/pkg/v2features"
	"gitlab.com/pockethealth/coreapi/pkg/v2orders"
	"gitlab.com/pockethealth/coreapi/pkg/v2plans"
	"gitlab.com/pockethealth/coreapi/pkg/v2rho"
	"gitlab.com/pockethealth/coreapi/pkg/v2shares"
	"gitlab.com/pockethealth/coreapi/pkg/v2transfers"
	"gitlab.com/pockethealth/coreapi/pkg/v2users"
	"gitlab.com/pockethealth/phutils/v10/pkg/audit"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth/service"
	"gitlab.com/pockethealth/phutils/v10/pkg/autoprof"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"
	phconfig "gitlab.com/pockethealth/phutils/v10/pkg/config"
	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"
	cioEvents "gitlab.com/pockethealth/phutils/v10/pkg/events"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpserver"
	phkeyvault "gitlab.com/pockethealth/phutils/v10/pkg/keyvault"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/repository"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	amplitudeEvent "github.com/amplitude/analytics-go/amplitude"
	_ "github.com/go-sql-driver/mysql"
	"github.com/gorilla/mux"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/amplitude"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"

	"gitlab.com/pockethealth/phutils/v10/pkg/servicebus"
	"golang.org/x/oauth2"
	"golang.org/x/text/language"
)

type EnvironmentConfig struct {
	KeyvaultName     string `json:"keyvault_name"`
	JwtSecretSecName string `json:"jwt_secret_sec_name"`
	JwtSecret        string `json:"jwt_secret"`

	EmailTokenSecName      string `json:"email_token_sec_name"`
	MySQLConnString        string `json:"mysql_connection_string"`
	MySQLConnStringSecName string `json:"mysql_connection_string_sec_name"`
	AzureStorageAccount    string `json:"azure_storage_account"`
	SSLCerts               []struct {
		Kind     string `json:"kind"`
		Key      string `json:"key"`
		Chain    string `json:"chain"`
		CertName string `json:"cert_name"`
	} `json:"ssl_certs"`
	HostName                      string                       `json:"hostname"`
	AllowedOrigins                []string                     `json:"allowed_origins"`
	AllowedHeaders                []string                     `json:"allowed_headers"`
	ExposedHeaders                []string                     `json:"exposed_headers"`
	FrontendHost                  string                       `json:"frontend_host"`
	LogLevel                      string                       `json:"log_level"`
	ShutdownTimeoutSeconds        int                          `json:"shutdown_timeout_seconds"`
	ProvidersServiceUser          providersservice.ProvSvcUser `json:"prov_svc"`
	FaxServiceUser                fax.FaxSvcUser               `json:"fax_svc"`
	HealthRecordsServiceUser      hrs.HlthRecSvcUser           `json:"hlth_rec_svc"`
	PaymentServiceUser            pmts.PaymentSvcUser          `json:"payment_svc"`
	RecordServiceClient           recordservice.RecordServiceClientInterface
	RPClient                      reportprocessor.RPClient            `json:"rp_client"`
	ReportInsightsClient          reportinsights.InsightsClient       `json:"ri_client"`
	AccountServiceClient          accountservice.AccountServiceClient `json:"account_svc"`
	OrgServiceClient              orgs.OrgServiceClient               `json:"org_svc"`
	PlanServiceClient             planservice.PlanServiceClient       `json:"plan_svc"`
	ExamInsightsServiceUser       examinsights.EIServiceUser          `json:"exam_insights_svc"`
	RecordRetrievalServiceURL     string                              `json:"record_retrieval_svc_url"`
	RecordRetrievalAllowCrossSite bool                                `json:"record_retrieval_allow_cross_site"`
	CacheLengthsMins              map[string]int                      `json:"cache_expiry_lengths_mins"`
	LoadBalancerIP                string                              `json:"load_balancer_ip"`
	RegionRouterPubKeyPath        string                              `json:"rr_pubkey_path"`
	RegionRouterBaseUrl           string                              `json:"rr_base_url"`
	RegionRouterAPIKey            string                              `json:"rr_api_key"`
	RegionID                      uint16                              `json:"region_id"`
	AcctSvcPubKeyPath             string                              `json:"as_pubkey_path"`
	AcctSvcJWTPubKeyPath          string                              `json:"as_jwt_pubkey_path"`
	EmailThrottlePerDay           int                                 `json:"email_throttle_per_day"`
	MaxIdleSQLConns               int                                 `json:"max_idle_sql_conns"`
	MaxSQLConnMins                int                                 `json:"max_sql_conn_mins"`
	MaxOpenSQLConns               int                                 `json:"max_open_sql_conns"`
	AmplitudeAPIKey               string                              `json:"amplitude_api_key"`
	RoiServiceClient              roiservice.RoiServiceClient         `json:"roi_svc"`
	AmplitudeBackendDeploymentKey string                              `json:"amplitude_backend_deployment_key"`
	AppGatewayCIDRs               []string                            `json:"appgw_cidrs"`
	BadLoginDelayMs               int                                 `json:"bad_login_delay_ms"`
	Region                        string                              `json:"region"`
	PockethealthAppUrl            string                              `json:"pocket_health_app_url"`
	ServiceBusName                string                              `json:"service_bus_name"`
	AccountRequestCreatedQueue    string                              `json:"account_request_created_queue"`
	RequestStatusQueue            string                              `json:"request_status_queue"`
	ReportsTopic                  string                              `json:"reports_topic"`
	SupportEmailAddress           string                              `json:"support_email_address"`
	OrganvizTopicTrigger          struct {
		TopicName        string `json:"topicName"`
		SubscriptionName string `json:"subscriptionName"`
	} `json:"organviz_topic_trigger"`
}

var envConfig EnvironmentConfig

var db *sql.DB

var containerClient azureUtils.ContainerClient

var lt lockouttracker.DBLockoutTracker

var emailTokenKey string

var waitGroup *sync.WaitGroup

// this bool is only used for the rollout of using soft deletes for: objects,exams,requests,shares
// anything else that needs to use soft deletes should use its own var or ensure no side effects
var excludeSoftDeletedData bool

var i18nBundle *i18n.Bundle

var languageTagProviders languageproviders.LanguageTagProviders

var supportedLanguages map[string]string

var (
	amplitudeEventClient      interfaces.AmplitudeEventClient
	amplitudeExperimentClient interfaces.AmplitudeExperimentClient
	ampCookieHeader           string
)

var (
	cioEventProducer cioEvents.CIOProducer
	cioEmailer       cio_email.CIOEMailer
)

var svcBusConnStr string

var autoProfiler *autoprof.Autoprof

func selectConfig(env string) EnvironmentConfig {
	if strings.ContainsAny(env, "./") {
		log.Fatal("Environment name can't contain dot or slash")
		return EnvironmentConfig{}
	}

	var ec EnvironmentConfig
	filename := "configs/config." + env + ".json"
	configJSON, err := ioutil.ReadFile(filename) // #nosec G304
	if err != nil {
		log.Fatal("Unable to read config: ", err)
	}

	log.Println("Using environment", env)

	err = json.Unmarshal(configJSON, &ec)
	if err != nil {
		log.Fatal("Unable to read config: ", err)
	}

	return ec
}

func usageAndExit() {
	fmt.Println("usage: ./coreapi --env [dev | qa | prod]")
	os.Exit(1)
}

// setupAZContainers creates and sets all containers used
func setupAZContainers(
	ctx context.Context,
	kv *phkeyvault.Keyvault,
	confMap map[string]json.RawMessage,
) {
	// use azure credential to get blob credential
	accountName := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"azure_storage_account",
		"azure_storage_account_sec_name",
	)

	objsContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_objs_storage_container",
		"",
	)
	smContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_sharemetadata_storage_container",
		"",
	)
	requestsContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_requests_storage_container",
		"",
	)
	consentsContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_consents_storage_container",
		"",
	)
	reportInsightContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_reportinsight_storage_container",
		"",
	)
	pprofContainerName := phconfig.ValFromConfOrKeyvault(
		nil,
		confMap,
		"azure_pprof_autoprofiler_storage_container",
		"",
	)

	generalRetry := &policy.RetryOptions{
		TryTimeout: 5 * time.Second,
	}

	// single images can be quite large
	obsRetry := &policy.RetryOptions{
		TryTimeout: 180 * time.Second,
	}

	objsContainerURL, errO := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		objsContainerName,
		obsRetry,
	)
	smContainerURL, errSM := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		smContainerName,
		generalRetry,
	)
	consentsContainerURL, errC := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		consentsContainerName,
		generalRetry,
	)
	requestsContainerURL, errR := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		requestsContainerName,
		generalRetry,
	)
	reportInsigntContainerURL, errRI := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		reportInsightContainerName,
		generalRetry,
	)
	pprofContainerURL, errMD := azstorageauth.GetContainerClient(
		ctx,
		accountName,
		pprofContainerName,
		generalRetry,
	)
	if errO != nil || errSM != nil || errC != nil || errR != nil || errRI != nil || errMD != nil {
		log.Fatalf(
			"could not setup storage containers::\nobjs err: %v\nSM err: %v\nconsents err: %v\nrequests err: %v\nreportinsights err: %v\nmemory dump err: %v\n",
			errO,
			errSM,
			errC,
			errR,
			errRI,
			errMD,
		)
	}

	// assign container clients to global variable
	containerClient = azureUtils.ContainerClient{
		Objects:       objsContainerURL,
		ShareMetadata: smContainerURL,
		Consents:      consentsContainerURL,
		Requests:      requestsContainerURL,
		ReportInsight: reportInsigntContainerURL,
		Pprof:         pprofContainerURL,
	}
}

func setupMysqlConnection(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	// open sql connection
	var err error
	sqlConnStr := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"mysql_connection_string",
		"mysql_connection_string_sec_name",
	)

	db, err = sql.Open("mysql", sqlConnStr)
	if err != nil {
		log.Fatal("unable to perform sql.Open")
	}
	db.SetMaxIdleConns(envConfig.MaxIdleSQLConns)
	db.SetConnMaxLifetime(time.Minute * time.Duration(envConfig.MaxSQLConnMins))
	db.SetMaxOpenConns(envConfig.MaxOpenSQLConns)

	go func() {
		for {
			logrus.WithField("stats", db.Stats()).Info("db stats")
			time.Sleep(time.Minute * 10)
		}
	}()
}

func setupEmailToken(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	emailTokenKey = phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"email_token_secret",
		"email_token_sec_name",
	)
}

func setupProvSvcUser(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var provUser map[string]json.RawMessage
	err := json.Unmarshal(confMap["prov_svc"], &provUser)
	if err != nil {
		log.Fatalf("can't read prov_svc json %+v.  error: %+v\n", confMap["prov_svc"], err)
	}
	envConfig.ProvidersServiceUser.PW = phconfig.ValFromConfOrKeyvault(
		kv,
		provUser,
		"pw",
		"pw_sec_name",
	)
	envConfig.ProvidersServiceUser.Client = &http.Client{}

	envConfig.ProvidersServiceUser.AuthTokenCache.SetCacheLength(
		time.Duration(envConfig.CacheLengthsMins["provsvc_auth_token"]) * time.Minute)
	envConfig.ProvidersServiceUser.DoRequestWithCtx = requestwithcontext.DoRequestWithCtx
}

func setupFaxSvcUser(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var faxUser map[string]json.RawMessage
	err := json.Unmarshal(confMap["fax_svc"], &faxUser)
	if err != nil {
		log.Fatalf("can't read fax_svc json %+v.  error: %+v\n", confMap["fax_svc"], err)
	}
	envConfig.FaxServiceUser.ApiKey = phconfig.ValFromConfOrKeyvault(
		kv,
		faxUser,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.FaxServiceUser.Client = &http.Client{}
	envConfig.FaxServiceUser.SetDoRequest(nil)
}

func setupHlthRecSvcUser(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var hrsUser map[string]json.RawMessage
	err := json.Unmarshal(confMap["hlth_rec_svc"], &hrsUser)
	if err != nil {
		log.Fatalf("can't read hlth_rec_svc json %+v.  error: %+v\n", confMap["hlth_rec_svc"], err)
	}
	envConfig.HealthRecordsServiceUser.ApiKey = phconfig.ValFromConfOrKeyvault(
		kv,
		hrsUser,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.HealthRecordsServiceUser.Client = &http.Client{}
	envConfig.HealthRecordsServiceUser.SetDoRequest(nil)
	envConfig.HealthRecordsServiceUser.SetUploadLimits(
		hrs.DefaultMaxRecordNumber,
		hrs.DefaultMaxFileNumber,
		hrs.DefaultMaxJsonSize,
		hrs.DefaultMaxFileSize,
	)
}

func setupSecondOpSvcUser(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var sosUser map[string]json.RawMessage
	err := json.Unmarshal(confMap["exam_insights_svc"], &sosUser)
	if err != nil {
		log.Fatalf(
			"can't read exam_insights_svc json %+v.  error: %+v\n",
			confMap["exam_insights_svc"],
			err,
		)
	}
	envConfig.ExamInsightsServiceUser.ApiKey = phconfig.ValFromConfOrKeyvault(
		kv,
		sosUser,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.ExamInsightsServiceUser.HttpClient = &http.Client{}
	envConfig.ExamInsightsServiceUser.SetDoRequest(nil)
}

func setupRPClient(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var rpClient map[string]json.RawMessage
	err := json.Unmarshal(confMap["rp_client"], &rpClient)
	if err != nil {
		log.Fatalf("can't read rp_client json %+v.  error: %+v\n", confMap["rp_client"], err)
	}
	apikey := phconfig.ValFromConfOrKeyvault(
		kv,
		rpClient,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.RPClient = reportprocessor.NewClient(
		envConfig.RPClient.URL,
		envConfig.RPClient.User,
		apikey,
	)
}

func setupInsightServiceS2S(
	ctx context.Context,
	kv *phkeyvault.Keyvault,
	cfg map[string]json.RawMessage,
) {
	var riConfig struct {
		URL string `json:"url"`
	}
	if err := json.Unmarshal(cfg["ri_client"], &riConfig); err != nil {
		log.Fatalf("can't read ri_client config: %v", err)
	}

	insightsURL := riConfig.URL

	authUrl := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"gateway_auth_url",
		"",
	)

	// Setup token source for S2S auth
	tokenSrc := service.NewTokenSource(
		"/var/run/secrets/kubernetes.io/serviceaccount/token",
		authUrl,
		"reportinsights",
		ctx,
	)
	if tokenSrc == nil {
		log.Fatal("unable to create s2s auth token for reportinsights")
	}
	// Create base http client with oauth transport
	baseClient := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &oauth2.Transport{
			Source: oauth2.ReuseTokenSource(nil, tokenSrc),
			Base:   http.DefaultTransport,
		},
	}
	retryParams := &httpclient.RetryParameters{
		MaxNumRetries: 3,
		StartBase:     1.0,
		BaseDuration:  10 * time.Second, // max wait time
	}

	client := httpclient.NewHTTPClient(baseClient, retryParams)

	envConfig.ReportInsightsClient = *reportinsights.NewInsightsClient(insightsURL, client)
}

func setupRoiServiceClient(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var roisvc map[string]json.RawMessage
	err := json.Unmarshal(confMap["roi_svc"], &roisvc)
	if err != nil {
		log.Fatalf("can't read roi_svc json %+v.  error: %+v\n", confMap["roi_svc"], err)
	}

	envConfig.RoiServiceClient.RoiServiceUrl = phconfig.ValFromConfOrKeyvault(
		kv,
		roisvc,
		"api_url",
		"api_url_sec_name",
	)
	envConfig.RoiServiceClient.RoiServiceUser = phconfig.ValFromConfOrKeyvault(
		kv,
		roisvc,
		"api_user",
		"api_user_sec_name",
	)
	envConfig.RoiServiceClient.RoiServiceAPIKey = phconfig.ValFromConfOrKeyvault(
		kv,
		roisvc,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.RoiServiceClient.HttpClient = httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
}

func setupAccountServiceClient(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var acctSvc map[string]json.RawMessage
	err := json.Unmarshal(confMap["account_svc"], &acctSvc)
	if err != nil {
		log.Fatalf("can't read account_svc json %+v.  error: %+v\n", confMap["account_scv"], err)
	}
	envConfig.AccountServiceClient.AccountServiceAPIKey = phconfig.ValFromConfOrKeyvault(
		kv,
		acctSvc,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.AccountServiceClient.HttpClient = httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
}

func setupOrgSvcClient(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var orgSvc map[string]json.RawMessage
	err := json.Unmarshal(confMap["org_svc"], &orgSvc)
	if err != nil {
		log.Fatalf("can't read org_svc json %+v.  error: %+v\n", confMap["org_svc"], err)
	}
	envConfig.OrgServiceClient.OrgServiceAPIKey = phconfig.ValFromConfOrKeyvault(
		kv,
		orgSvc,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.OrgServiceClient.HttpClient = httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	cache, err := orgs.NewOrgsvcCache()
	if err != nil {
		log.Fatal("unable to set up orgsvc cache")
	}
	envConfig.OrgServiceClient.OrgsvcCache = cache
}

func setupPlanServiceClient(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var planSvc map[string]json.RawMessage
	err := json.Unmarshal(confMap["plan_svc"], &planSvc)
	if err != nil {
		log.Fatalf("can't read plan_svc json %+v.  error: %+v\n", confMap["plan_svc"], err)
	}
	envConfig.PlanServiceClient = *planservice.NewHTTPPlanServiceClient(
		envConfig.PlanServiceClient.PlanServiceUrl,
		envConfig.PlanServiceClient.PlanServiceUser,
		phconfig.ValFromConfOrKeyvault(kv, planSvc, "api_key", "api_key_sec_name"),
		&http.Client{},
	)
}

func setupPaymentSvcUser(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	var pmtsUser map[string]json.RawMessage
	err := json.Unmarshal(confMap["payment_svc"], &pmtsUser)
	if err != nil {
		log.Fatalf("can't read payment_svc json %+v.  error: %+v\n", confMap["payment_svc"], err)
	}
	envConfig.PaymentServiceUser.ApiKey = phconfig.ValFromConfOrKeyvault(
		kv,
		pmtsUser,
		"api_key",
		"api_key_sec_name",
	)
	envConfig.PaymentServiceUser.Client = &http.Client{}
	envConfig.PaymentServiceUser.SetDoRequest(nil)
}

func setupCaches(confMap map[string]json.RawMessage) {
	err := json.Unmarshal(confMap["cache_expiry_lengths_mins"], &envConfig.CacheLengthsMins)
	if err != nil {
		log.Fatalf(
			"can't read cache_expiry_lengths_mins json %+v.  error: %+v\n",
			confMap["cache_expiry_lengths_mins"],
			err,
		)
	}
	log.Printf("setting up cache expiry in mins: %s", confMap["cache_expiry_lengths_mins"])
	sqlOrgs.SetupCacheLength(envConfig.CacheLengthsMins["report_delay"])
	sqlProviders.SetupCacheLengths(
		envConfig.CacheLengthsMins["origin_id_name"],
		envConfig.CacheLengthsMins["clinic_id_org_id"],
	)
}

func setupRRApiKey(kv *phkeyvault.Keyvault, confMap map[string]json.RawMessage) {
	rrAPIKey := phconfig.ValFromConfOrKeyvault(kv, confMap, "rr_api_key", "rr_api_key_sec_name")
	envConfig.RegionRouterAPIKey = rrAPIKey
}

func setupCIOEMailer(
	kv *phkeyvault.Keyvault,
	cfg map[string]json.RawMessage,
) cio_email.CIOEMailer {
	// set up transcation email id for customer.io
	var emailIds map[string]string
	err := json.Unmarshal(cfg["cio_transactional_email_ids"], &emailIds)
	if err != nil {
		logrus.Fatal("can't parse email ids section of config file")
	}
	cioMailApiKey := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"cio_email_api_key",
		"cio_email_api_key_sec_name",
	)
	emailer := cioEmail.New(cioMailApiKey)
	return cio_email.ConfigureMail(emailer, emailIds)
}

func getServiceBusConnStr(
	kv *phkeyvault.Keyvault,
	cfg map[string]json.RawMessage,
) string {
	return phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"service_bus_connection_key",
		"service_bus_connection_key_sec_name",
	)
}

func createRouter(
	organvizService organviz.Service,
	examService exams.ExamServiceInterface,
) *mux.Router {
	lt.Init(db)

	regions.SetRegionID(envConfig.RegionID)
	regionRouterClient := regions.NewRegionRouterClient(
		envConfig.RegionRouterBaseUrl,
		envConfig.RegionRouterAPIKey,
		&http.Client{},
	)

	imagesApiService := images.NewImagesApiService(
		db,
		containerClient,
		waitGroup,
		&envConfig.OrgServiceClient,
	)
	privateImagesApiController := images.NewPrivateImagesApiController(imagesApiService)

	providersApiService := providers.NewProvidersApiService(
		db,
		containerClient,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
		&envConfig.AccountServiceClient,
		&envConfig.PlanServiceClient,
		&envConfig.OrgServiceClient,
		amplitudeEventClient,
		amplitudeExperimentClient,
		&cioEventProducer,
		svcBusConnStr,
		envConfig.ServiceBusName,
		&envConfig.ProvidersServiceUser,
	)
	publicProvidersApiController := providers.NewPublicProvidersApiController(
		providersApiService,
		ampCookieHeader,
	)
	privateProvidersApiController := providers.NewPrivateProvidersApiController(
		providersApiService,
		ampCookieHeader,
	)

	privateMeddreamApiController := meddream.NewPrivateMeddreamController(
		meddream.NewAPIService(db, envConfig.RecordServiceClient, examService),
	)

	repo, err := repository.NewFromDB(db, repository.WithTxlessFallback())
	if err != nil {
		log.Fatalf("failure to initialize repository: %s", err.Error())
	}
	auditService := auditservice.NewService(auditservice.NewStore(repo))

	reportsApiService := reports.NewReportsApiService(
		db,
		&containerClient,
		envConfig.RPClient,
		&envConfig.OrgServiceClient,
		organvizService,
		auditService,
		envConfig.RecordServiceClient,
		excludeSoftDeletedData,
		examService,
	)

	reportTopicSender, err := servicebus.GetSender(
		envConfig.ServiceBusName,
		envConfig.ReportsTopic,
		servicebus.DefaultClientOptions,
	)
	if err != nil {
		logrus.Fatalf(
			"can't create client for sending messages to topic '%s'",
			envConfig.ReportsTopic,
		)
	}
	privateReportMetdataController := reports.NewPrivateReportMetadataController(reportsApiService)
	privateReportsApiController := reports.NewPrivateReportsApiController(
		reportsApiService,
		reportTopicSender,
	)

	requestsApiService := requests.NewRequestsApiService(
		db,
		containerClient,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
		envConfig.HostName,
		envConfig.FrontendHost,
		&envConfig.AccountServiceClient,
		&envConfig.PlanServiceClient,
		&envConfig.OrgServiceClient,
		amplitudeEventClient,
		amplitudeExperimentClient,
		&cioEventProducer,
		waitGroup,
		&envConfig.RoiServiceClient,
		envConfig.RecordServiceClient,
		cioEmailer,
		svcBusConnStr,
		envConfig.ServiceBusName,
		envConfig.AccountRequestCreatedQueue,
		envConfig.Region,
		excludeSoftDeletedData,
		envConfig.PockethealthAppUrl,
		envConfig.RequestStatusQueue,
		envConfig.SupportEmailAddress,
	)
	privateRequestsApiController := requests.NewPrivateRequestsApiController(requestsApiService)
	publicRequestsApiController := requests.NewPublicRequestsApiController(
		requestsApiService,
		&lt,
		ampCookieHeader,
	)
	publicRequestsApiControllerV2 := requests.NewPublicRequestsApiControllerV2(
		requestsApiService,
		&lt,
		ampCookieHeader,
	)

	recordStreamingService := recordstreaming.NewRecordStreamingService(
		db,
		&envConfig.OrgServiceClient,
		envConfig.RecordServiceClient,
	)

	sharesApiService := shares.NewSharesApiService(
		db,
		containerClient,
		envConfig.FaxServiceUser,
		envConfig.FrontendHost,
		waitGroup,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
		regionRouterClient,
		envConfig.HealthRecordsServiceUser,
		&envConfig.AccountServiceClient,
		&envConfig.OrgServiceClient,
		excludeSoftDeletedData,
		amplitudeExperimentClient,
		amplitudeEventClient,
		download.Prepare,
		cioEmailer,
		examService,
		recordStreamingService,
	)
	publicSharesApiController := shares.NewPublicSharesApiController(sharesApiService, &lt)
	privateSharesApiController := shares.NewPrivateSharesApiController(sharesApiService)
	dlSharesApiController := shares.NewDLSharesApiController(
		sharesApiService,
		recordStreamingService,
	)

	v2SharesApiService := v2shares.NewV2SharesApiService(
		db,
		envConfig.HealthRecordsServiceUser,
		&envConfig.AccountServiceClient,
		&envConfig.OrgServiceClient,
		excludeSoftDeletedData,
	)
	privateV2SharesApiController := v2shares.NewPrivateV2SharesApiController(v2SharesApiService)

	subscriptionsApiService := subscriptions.NewSubscriptionsApiService(
		db,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
		&envConfig.AccountServiceClient,
	)
	privateSubscriptionsApiController := subscriptions.NewPrivateSubscriptionsApiController(
		subscriptionsApiService,
	)

	v2PatientsApiService := v2patients.NewV2PatientsApiService(
		db,
		&envConfig.AccountServiceClient,
	)
	v2PrivatePatientsApiController := v2patients.NewV2PrivatePatientsApiController(
		v2PatientsApiService,
	)

	shortUrlsApiService := short_urls.NewShortUrlsApiService(db)
	privateShortUrlsApiController := short_urls.NewPrivateShortUrlsApiController(
		shortUrlsApiService,
	)

	publicShortUrlsApiController := short_urls.NewPublicShortUrlsApiController(
		shortUrlsApiService,
	)

	usersApiService := users.NewUsersApiService(
		db,
		containerClient,
		cioEmailer,
		envConfig.FrontendHost,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
		&envConfig.AccountServiceClient,
		&envConfig.OrgServiceClient,
		&envConfig.PlanServiceClient,
		waitGroup,
		excludeSoftDeletedData,
		amplitudeExperimentClient,
		organvizService,
		&envConfig.ProvidersServiceUser,
		converter.ConvertImage,
		&envConfig.ExamInsightsServiceUser,
		&envConfig.RPClient,
		examService,
		reportsApiService,
		&envConfig.ReportInsightsClient,
		&envConfig.RoiServiceClient,
	)
	privateUsersApiController := users.NewPrivateUsersApiController(
		usersApiService,
		envConfig.FrontendHost,
	)
	privateUsersExamsApiController := users.NewPrivateUsersExamsApiController(usersApiService)
	publicUsersApiController := users.NewPublicUsersApiController(
		usersApiService,
		&lt,
		envConfig.FrontendHost,
		i18nBundle,
		languageTagProviders,
		supportedLanguages,
	)

	v2UsersApiService := v2users.NewV2UsersApiService(
		&envConfig.AccountServiceClient,
		&envConfig.OrgServiceClient,
		&lt,
		db,
		amplitudeExperimentClient,
		amplitudeEventClient,
		&envConfig.RoiServiceClient,
	)
	publicV2UsersApiController := v2users.NewPublicV2UsersApiController(
		v2UsersApiService,
		envConfig.FrontendHost,
		envConfig.BadLoginDelayMs,
	)
	privateV2UsersApiController := v2users.NewPrivateV2UsersApiController(v2UsersApiService)

	v2TransfersApiService := v2transfers.NewV2TransfersApiService(
		db,
		envConfig.ProvidersServiceUser,
		envConfig.FrontendHost,
		&envConfig.AccountServiceClient,
	)
	privateV2TransfersApiController := v2transfers.NewPrivateV2TransfersApiController(
		v2TransfersApiService,
	)

	v2HealthRecordsApiService := v2healthrecords.NewV2HealthRecordsApiService(
		db,
		envConfig.HealthRecordsServiceUser,
		&envConfig.AccountServiceClient,
		examService,
	)
	privateV2HealthRecordsApiController := v2healthrecords.NewPrivateV2HealthRecordsApiController(
		v2HealthRecordsApiService,
	)
	v2UploadHealthRecordsApiController := v2healthrecords.NewV2UploadHealthRecordsApiController(
		v2HealthRecordsApiService,
	)

	referApiService := refer.NewReferApiService(
		db,
		usersApiService,
		&envConfig.AccountServiceClient,
		envConfig.EmailThrottlePerDay,
		emailToken.EmailToken{Key: emailTokenKey},
	)
	publicReferApiController := refer.NewPublicReferApiController(referApiService)

	internalApiService := internalsvc.NewInternalApiService(
		db,
		waitGroup,
		&envConfig.AccountServiceClient,
	)
	privateInternalApiController := internalsvc.NewPrivateInternalApiController(internalApiService)

	v2ProvidersService := v2providers.NewV2ProvidersApiService(
		&envConfig.OrgServiceClient,
		&envConfig.PlanServiceClient,
		amplitudeExperimentClient,
		amplitudeEventClient,
		&envConfig.AccountServiceClient,
	)
	publicV2ProviderController := v2providers.NewV2PublicProvidersApiController(
		v2ProvidersService,
		ampCookieHeader,
	)

	v2OrdersService := v2orders.NewV2OrdersApiService(
		db,
		&envConfig.AccountServiceClient,
		amplitudeEventClient,
		&envConfig.PlanServiceClient,
	)
	privateV2OrdersController := v2orders.NewPrivateV2OrdersController(
		v2OrdersService,
		ampCookieHeader,
	)

	planApiService := v2plans.NewPlansApiService(
		&envConfig.PlanServiceClient,
		amplitudeExperimentClient,
		amplitudeEventClient,
	)
	publicPlanApiController := v2plans.NewPublicPlansApiController(planApiService)

	featureApiService := v2features.NewFeaturesApiService(
		&envConfig.PlanServiceClient,
		&envConfig.AccountServiceClient,
	)
	publicFeatureApiController := v2features.NewPublicFeaturesApiController(featureApiService)

	requestFormConfigurationApiService := forms.NewRequestFormConfigurationApiService(
		&envConfig.OrgServiceClient,
		db,
		amplitudeEventClient,
		amplitudeExperimentClient,
	)
	requestFormConfigurationApiController := forms.NewRequestFormConfigurationApiController(
		requestFormConfigurationApiService,
		ampCookieHeader,
	)

	appointmentRemindersApiService := appointmentreminders.NewAppointmentRemindersApiService(
		envConfig.ProvidersServiceUser,
	)
	publicAppointmentRemindersApiController := appointmentreminders.NewPublicAppointmentRemindersApiController(
		appointmentRemindersApiService,
	)
	privateAppointmentsApiController := appointmentreminders.NewPrivateAppointmentsApiController(
		appointmentRemindersApiService,
	)

	rhoApiService := v2rho.NewRhoApiService(
		db,
		envConfig.ExamInsightsServiceUser,
		examService,
	)
	privateRhoApiController := v2rho.NewPrivateRhoApiController(
		rhoApiService,
	)

	checkoutApiService := checkout.NewCheckoutApiService(
		db,
		&envConfig.AccountServiceClient,
		&envConfig.PlanServiceClient,
		&envConfig.OrgServiceClient,
		envConfig.RecordServiceClient,
		&cioEventProducer,
		amplitudeEventClient,
	)

	publicCheckoutApiController := checkout.NewPublicCheckoutApiController(
		checkoutApiService,
		ampCookieHeader,
		&lt,
	)

	rrsClient, err := recordretrievalservice.NewClientWithResponses(
		envConfig.RecordRetrievalServiceURL,
		recordretrievalservice.WithHTTPClient(
			httpclient.WrapClient(
				http.DefaultClient,
				httpclient.NewCorrelationForwardingTransport(),
			),
		),
	)
	if err != nil {
		logutils.Fatalx(
			context.Background(),
			"failed to create client for record retrieval service",
			err,
		)
	}

	publicUploadRequestsController := uploadrequests.PublicUploadRequestsAPIController{
		RecordRetrievalService: rrsClient,
		AllowCrossSite:         envConfig.RecordRetrievalAllowCrossSite,
	}

	// Payment service routes
	paymentApiService := payment.NewPaymentApiService(
		db,
		envConfig.PaymentServiceUser,
		&envConfig.AccountServiceClient,
		&envConfig.OrgServiceClient,
		amplitudeEventClient,
		&envConfig.PlanServiceClient,
	)
	publicPaymentApiController := payment.NewPublicPaymentApiController(paymentApiService)

	privatePaymentApiController := payment.NewPrivatePaymentApiController(paymentApiService)

	// Second Opinion routes
	secondOpinionApiService := secondopinion.NewSecondOpinionApiService(
		db,
		envConfig.ExamInsightsServiceUser,
		envConfig.AccountServiceClient,
		examService,
	)
	privateSecondOpinionApiController := secondopinion.NewPrivateSecondOpinionApiController(
		secondOpinionApiService,
	)
	// Study upload status routes
	recordUploadStatusApiService := recorduploadstatus.NewRecordUploadStatusApiService(
		&envConfig.ProvidersServiceUser,
		&envConfig.AccountServiceClient,
		envConfig.RecordServiceClient,
		exams.NewMigrationHelper(db),
	)
	privateRecordUploadStatusApiController := recorduploadstatus.NewPrivateRecordUploadStatusApiController(
		recordUploadStatusApiService,
	)

	// physician account
	recordsSearchTopic, err := servicebus.GetSender(
		envConfig.ServiceBusName,
		"pacs_search",
		servicebus.DefaultClientOptions,
	)
	if err != nil {
		logrus.Fatalf("can't create client for sending messages to topic '%s'", "pacs_search")
	}
	studyUploadTopic, err := servicebus.GetSender(
		envConfig.ServiceBusName,
		"study_upload",
		servicebus.DefaultClientOptions,
	)
	if err != nil {
		logrus.Fatalf("can't create client for sending messages to topic '%s'", "study_upload")
	}

	physicianAccountApiService := physicianaccount.NewPhysicianAccountApiService(
		db,
		&envConfig.AccountServiceClient,
		envConfig.FaxServiceUser,
		envConfig.HealthRecordsServiceUser,
		&envConfig.OrgServiceClient,
		envConfig.RecordServiceClient,
		auditService,
		&envConfig.RoiServiceClient,
		recordsSearchTopic,
		studyUploadTopic,
		envConfig.Region,
		excludeSoftDeletedData,
	)

	privatePhysicianAccountApiController := physicianaccount.NewPrivatePhysicianAccountApiController(
		physicianAccountApiService,
		&envConfig.AccountServiceClient,
		recordStreamingService,
		audit.NewAuditLoggerService(svcBusConnStr),
	)
	publicPhysicianAccountApiController := physicianaccount.NewPublicPhysicianAccountApiController(
		physicianAccountApiService,
		envConfig.FrontendHost,
		envConfig.BadLoginDelayMs,
	)

	routers := []coreapi.Router{
		privateImagesApiController, publicProvidersApiController, privateProvidersApiController, privateReportMetdataController,
		privateReportsApiController, privateRequestsApiController, publicRequestsApiController, publicRequestsApiControllerV2, publicSharesApiController,
		privateSharesApiController, dlSharesApiController, privateSubscriptionsApiController, v2PrivatePatientsApiController,
		privateUsersExamsApiController, privateShortUrlsApiController, publicShortUrlsApiController, privateUsersApiController, publicUsersApiController, privateV2TransfersApiController,
		publicV2UsersApiController, privateV2HealthRecordsApiController, v2UploadHealthRecordsApiController, privateV2SharesApiController,
		publicReferApiController, privateInternalApiController, publicV2ProviderController, privateV2OrdersController,
		publicPlanApiController, publicFeatureApiController, privateV2UsersApiController, requestFormConfigurationApiController,
		publicAppointmentRemindersApiController, privateAppointmentsApiController, privateRhoApiController, privateMeddreamApiController, publicCheckoutApiController,
		&publicUploadRequestsController, publicPaymentApiController, privatePaymentApiController, privateSecondOpinionApiController,
		privateRecordUploadStatusApiController, privatePhysicianAccountApiController, publicPhysicianAccountApiController,
	}

	var router *mux.Router
	router, err = coreapi.NewRouter(routers...)
	if err != nil {
		log.Fatal("Could not create router: ", err)
	}

	return router
}

func setupTmpDirs() {
	// create tmp directory for temporary files during file conversion
	if _, err := os.Stat("vault"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault", 0o700)
		if mkdirErr != nil {
			log.Fatalf("failed to set up temp dirs: %v", mkdirErr)
		}
	}
	if _, err := os.Stat("vault/tmp"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault/tmp", 0o700)
		if mkdirErr != nil {
			log.Fatalf("failed to set up temp dirs: %v", mkdirErr)
		}
	}
	if _, err := os.Stat("vault/viewer"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault/viewer", 0o700)
		if mkdirErr != nil {
			log.Fatalf("failed to set up temp dirs: %v", mkdirErr)
		}
	}
}

func setupAuth(
	kv *phkeyvault.Keyvault,
	confMap map[string]json.RawMessage,
	envConfig EnvironmentConfig,
) {
	// NOTE: the confKey argument (i.e jwt_secret here) needs to be consistent with the
	// EnvironmentConfig's field tag (i.e. the `json:"jwt_secret"`), which is annoying.
	// TODO: Make more sane EnvironmentConfig - https://mypockethealth.atlassian.net/browse/IS-50
	jwtSecretKey := []byte(
		phconfig.ValFromConfOrKeyvault(kv, confMap, "jwt_secret", "jwt_secret_sec_name"),
	)
	auth.SetSecretKey([]byte(jwtSecretKey))
	auth.InitializeBlacklist(db)

	rrPublicKey, err := ioutil.ReadFile(envConfig.RegionRouterPubKeyPath) // #nosec G304
	if err != nil {
		log.Fatal("can't read regionrouter pub key:", err)
	}
	err = auth.SetRegionRouterPublicKey(rrPublicKey)
	if err != nil {
		log.Fatal("error parsing regionrouter pub key:", err)
	}

	auth.SetRegionID(envConfig.RegionID)

	asPublicKey, err := ioutil.ReadFile(envConfig.AcctSvcPubKeyPath) // #nosec G304
	if err != nil {
		log.Fatal("can't read acct svc pub key:", err)
	}
	err = auth.SetAcctSvcPublicKey(asPublicKey)
	if err != nil {
		log.Fatal("error parsing acct svc pub key:", err)
	}

	asJwtPublicKey, err := ioutil.ReadFile(envConfig.AcctSvcJWTPubKeyPath) // #nosec G304
	if err != nil {
		log.Fatal("can't read acct svc jwt pub key:", err)
	}
	err = auth.SetAcctSvcJWTPublicKey(asJwtPublicKey)
	if err != nil {
		log.Fatal("error parsing acct svc jwt pub key:", err)
	}
}

func setupLanguageTools() {
	i18nBundle = i18n.NewBundle(language.English)
	i18nBundle.RegisterUnmarshalFunc("json", json.Unmarshal)

	// get all supported languages
	languages := make(map[string]string)
	rows, err := db.Query("SELECT code, language FROM languages")
	if err != nil {
		logrus.WithError(err).
			Error("could not retrieve supported languages, setting english default")
		languages["en"] = "English (United States)"
	} else {
		defer rows.Close()
		var code, language string
		for rows.Next() {
			err = rows.Scan(&code, &language)
			if err != nil {
				logrus.WithError(err).Error("failed to retrieve language")
			} else {
				languages[code] = language
				// skip en becuase we have that by default
				if code != "en" && code != "en-US" {
					_, err := i18nBundle.LoadMessageFile("locales/active." + code + ".json")
					if err != nil {
						log.Fatalf("unable to load translation for %s, %q", code, err)
					}
				}
			}
		}
	}
	supportedLanguages = languages

	languageTagProviders = languageproviders.LanguageTagProviders{
		AccountId: accounts.GetAccountIdLanguageTagProvider(&envConfig.AccountServiceClient),
		ClinicId:  sqlProviders.GetClinicLanguageTagProvider(&envConfig.OrgServiceClient),
		OrgId:     sqlOrgs.GetOrgIdLanguageTagProvider(&envConfig.OrgServiceClient),
	}
}

func setupAmplitude(
	confMap map[string]json.RawMessage,
	kv *phkeyvault.Keyvault,
) {
	amplitudeAPIKey := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"amplitude_api_key",
		"amplitude_api_key_sec_name",
	)
	amplitudeBackendDeploymentKey := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"amplitude_backend_deployment_key",
		"amplitude_backend_deployment_key_sec_name",
	)

	// Create a Config struct
	config := amplitudeEvent.NewConfig(amplitudeAPIKey)

	// Pass a Config struct to client
	amplitudeEventClient = amplitude.NewAmplitudeEventClient(amplitudeEvent.NewClient(config))
	// Amplitude cookie header is AMP_ and the first ten digit of api key
	// e.g. AMP_06c2865596
	ampCookieHeader = "AMP_" + amplitudeAPIKey[:10]

	amplitudeExperimentClient = amplitude.NewAmplitudeExperimentClient(
		amplitudeExperiment.Initialize(
			amplitudeBackendDeploymentKey,
			nil,
		),
	)
}

func setupCIOProducer(
	confMap map[string]json.RawMessage,
	kv *phkeyvault.Keyvault,
) {
	cioSiteTrackId := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"cio_site_id",
		"cio_site_id_sec_name",
	)
	cioTrackApiKey := phconfig.ValFromConfOrKeyvault(
		kv,
		confMap,
		"cio_api_key",
		"cio_api_key_sec_name",
	)
	var triggerEvent bool
	err := json.Unmarshal(confMap["trigger_tracking_events"], &triggerEvent)
	if err != nil {
		log.Fatalf("can't read json %+v.  error: %+v\n", confMap["trigger_tracking_events"], err)
	}
	cioEventProducer = cioEvents.New(cioSiteTrackId, cioTrackApiKey, "coreapi", triggerEvent)
}

// setupTracing - checks if config line for trace export is present and attempts to set up trace provider. If config is missing, print warning and proceed
func getTracingCollectorAddr(traceCollectorAddressJson json.RawMessage) string {
	if traceCollectorAddressJson == nil {
		logrus.Warn("no trace collector address specified, tracing is disabled")
		return ""
	}

	var traceCollectorAddress string
	if err := json.Unmarshal(traceCollectorAddressJson, &traceCollectorAddress); err != nil {
		logrus.WithError(err).Warn("could not load trace collector address, tracing is disabled")
		return ""
	}
	return traceCollectorAddress
}

func kickoffSQLCleanupWorkers(ctx context.Context, db *sql.DB, orgSvcClient orgs.OrgServiceClient) {
	go incompleterequests.IncompleteRequestDeletionWorker(ctx, containerClient.Requests, db)
	go requests.BadInputsDeletionWorker(ctx, db)
}

func main() {
	args := os.Args[1:]
	if len(args) != 2 || args[0] != "--env" {
		usageAndExit()
	}

	var err error
	var confMap map[string]json.RawMessage
	envConfig = selectConfig(args[1])
	confMap = phconfig.SelectConfig("config", args[1])

	kv, err := phkeyvault.NewKeyvault(envConfig.KeyvaultName)
	if err != nil {
		log.Printf(
			"Unable to create Keyvault client: %s.  You will not be able to use keyvault secret names in configuration.\n",
			err,
		)
	}
	waitGroup = &sync.WaitGroup{}

	// This context propagates app shutdown to all the service bus listeners that use it
	appCtx, cancelAppCtx := context.WithCancel(context.Background())
	go func() {
		waitSigterm := make(chan os.Signal, 1)
		signal.Notify(waitSigterm, os.Interrupt, syscall.SIGTERM)
		<-waitSigterm
		logrus.Printf("stopping listeners...")
		cancelAppCtx()
	}()

	// setup all db connections and configs
	setupMysqlConnection(kv, confMap)
	err = db.Ping()
	if err != nil {
		log.Fatalf("unable to ping db, %q", err)
	}

	// start mem and goroutine usage logger
	go func() {
		for {
			logRuntimeUsage()
			time.Sleep(time.Minute * 5)
		}
	}()

	var uswSoftDeletes int
	err = db.QueryRow("SELECT pct FROM rollout WHERE name='usw-soft-deletes'").Scan(&uswSoftDeletes)
	if err != nil {
		logrus.WithError(err).
			Error("could not retrieve rollout pct, setting excludeSoftDeletedData to false")
		excludeSoftDeletedData = false
	}
	excludeSoftDeletedData = uswSoftDeletes == 100 // this is binary not a % so only mark as true iff 100% rolled out

	// obtain azure credential to generate access token
	_, err = azidentity.NewDefaultAzureCredential(nil)
	if err != nil {
		logrus.WithError(err).Fatal("error creating default az cred")
	}

	setupAZContainers(context.TODO(), kv, confMap)
	setupEmailToken(kv, confMap)
	setupAuth(kv, confMap, envConfig)
	setupTmpDirs()
	setupProvSvcUser(kv, confMap)
	setupFaxSvcUser(kv, confMap)
	setupHlthRecSvcUser(kv, confMap)
	setupPaymentSvcUser(kv, confMap)
	envConfig.RecordServiceClient = setup.SetupRecordServiceClient(kv, confMap)
	setupRPClient(kv, confMap)
	setupInsightServiceS2S(context.Background(), kv, confMap)
	setupAccountServiceClient(kv, confMap)
	setupOrgSvcClient(kv, confMap)
	setupPlanServiceClient(kv, confMap)
	setupCaches(confMap)
	setupLanguageTools()
	setupRRApiKey(kv, confMap)
	setupAmplitude(confMap, kv)
	setupCIOProducer(confMap, kv)
	cioEmailer = setupCIOEMailer(kv, confMap)
	svcBusConnStr = getServiceBusConnStr(kv, confMap)
	setupRoiServiceClient(kv, confMap)

	organvizService := organviz.NewService(&containerClient,
		db,
		converter.ConvertImage,
		waitGroup,
		&envConfig.ReportInsightsClient,
	)
	examService := exams.NewExamService(
		db,
		&envConfig.OrgServiceClient,
		envConfig.RecordServiceClient,
		&envConfig.PlanServiceClient,
		&envConfig.AccountServiceClient,
		&envConfig.ProvidersServiceUser,
		exams.NewMigrationHelper(db),
	)
	organviztopicapi.StartTopicListener(
		appCtx,
		envConfig.ServiceBusName,
		envConfig.OrganvizTopicTrigger.TopicName,
		envConfig.OrganvizTopicTrigger.SubscriptionName,
		db,
		organvizService,
		examService,
	)

	coreapi.SetProxyIPRanges(envConfig.AppGatewayCIDRs)

	setupSecondOpSvcUser(kv, confMap)

	// async cleanup mysql db
	go kickoffSQLCleanupWorkers(context.Background(), db, envConfig.OrgServiceClient)

	// configure router
	router := createRouter(organvizService, examService)

	// configure regular logging
	if level, err := logrus.ParseLevel(envConfig.LogLevel); err != nil {
		logrus.SetLevel(logrus.InfoLevel)
	} else {
		logrus.SetLevel(level)
	}
	logrus.SetFormatter(&logrus.JSONFormatter{})

	router.Use(httpserver.NewHandleClientCancelMiddleware())

	tlsconfig := httpserver.SetupTLS(confMap, kv)
	allowedMethods := []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}

	s := httpserver.New(
		"coreapi",
		router,
		tlsconfig,
		waitGroup,
		httpserver.WithAllowedHeaders(envConfig.AllowedHeaders),
		httpserver.WithAllowedMethods(allowedMethods),
		httpserver.WithAllowedOrigins(envConfig.AllowedOrigins),
		httpserver.WithExposedHeaders(envConfig.ExposedHeaders),
		httpserver.WithAmplitudeCookieHeader(ampCookieHeader),
		httpserver.WithProfiling(containerClient.Pprof),
		httpserver.WithInstrumentation(kv, confMap),
	)
	s.SetupAndServe()
}

// PrintUsage outputs the current, total and OS memory being used. As well as the number
// of garbage collection cycles completed and goroutines that currently exist
func logRuntimeUsage() {
	var m runtime.MemStats

	// For info on each, see: https://golang.org/pkg/runtime/#MemStats
	runtime.ReadMemStats(&m)
	logrus.WithFields(logrus.Fields{
		"alloc":        bToMb(m.Alloc),
		"total_alloc":  bToMb(m.TotalAlloc),
		"sys_mem":      bToMb(m.Sys),
		"num_gc":       m.NumGC,
		"num_routines": runtime.NumGoroutine(),
	}).Info("runtime usage")
}

func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
