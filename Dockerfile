FROM phcrcacentral0.azurecr.io/node:22-bullseye AS ca
# -- 22 is lts node, bullseye is lts debian, as of like... christmas 2024. If you're reading this, check if this is
# -- still true :)
EXPOSE 20443
WORKDIR /coreapi

ENTRYPOINT [ "./docker-entrypoint.sh" ]
CMD ["dev"]

# -- setup dcmtk and other file conversion utilities
RUN apt-get -qq update && apt-get -q -y install \
    zip \
    genisoimage \
    ghostscript \
    xpdf \
    curl

RUN curl -o dcmtk-3.6.2-linux-x86_64.tar.bz2 -L 'https://phstinfra.blob.core.windows.net/public/dcmtk-3.6.2-linux-x86_64.tar.bz2?sp=r&st=2025-01-20T06:00:00Z&se=2026-01-13T04:55:51Z&spr=https&sv=2022-11-02&sr=b&sig=HJP%2FceeblxpqXSGfaBCwaLbQfbRhZI%2BZnCKzi3t69IQ%3D' \
    && tar xjf dcmtk-3.6.2-linux-x86_64.tar.bz2

RUN curl -o libpng12-0_1.2.54-1ubuntu1.1_amd64.deb -L http://security.ubuntu.com/ubuntu/pool/main/libp/libpng/libpng12-0_1.2.54-1ubuntu1.1_amd64.deb \
    && dpkg -i libpng12-0_1.2.54-1ubuntu1.1_amd64.deb

RUN wget https://github.com/malaterre/GDCM/releases/download/v3.0.8/GDCM-3.0.8-Linux-x86_64.tar.bz2 \
    && tar xjf GDCM-3.0.8-Linux-x86_64.tar.bz2 \
    && cp GDCM-3.0.8-Linux-x86_64/* /usr/ -r

RUN wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz && \
    tar xvf wkhtmltox-0.12.4_linux-generic-amd64.tar.xz && \
    mv wkhtmltox/bin/wkhtmltopdf /usr/bin && \
    mv wkhtmltox/bin/wkhtmltoimage /usr/bin && \
    rm wkhtmltox-0.12.4_linux-generic-amd64.tar.xz  && rm -rf wkhtmltox

ENV PATH="/coreapi/dcmtk-3.6.2-linux-x86_64/bin:${PATH}"
ENV DCMDICTPATH="/coreapi/dcmtk-3.6.2-linux-x86_64/share/dcmtk/dicom.dic"

COPY package.json package-lock.json ./
RUN npm ci
RUN chown 21000:21000 .

#set imagemagick height limit higher to facilitate pdf conversions
RUN sed -i 's/name="height" value="16KP"/name="height" value="32KP"/g' /etc/ImageMagick-6/policy.xml

#prevent SVG parsing since it can contain a remote link that the convert command will follow
RUN sed -i 's/<\/policymap>/<policy domain="coder" rights="none" pattern="MVG" \/>\n<\/policymap>/' /etc/ImageMagick-6/policy.xml

# Enable permissions required to build consent pdfs
RUN sed -i 's/<policy domain="coder" rights="none" pattern="PDF" \/>/<policy domain="coder" rights="read | write" pattern="PDF" \/>/g' /etc/ImageMagick-6/policy.xml

COPY --chown=21000:21000 dist .
