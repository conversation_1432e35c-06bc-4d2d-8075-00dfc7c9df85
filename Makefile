.PHONY: build unit coreapi clean ca_linux dist clean_bin cmd_tools_test integ apptest
.PHONY: start_dc stop_dc kill_dev_deps run_docker_dev_nt run_docker_dev run_docker_qa_nt run_docker_qa local_dev docker_tail
.PHONY: generate clean-gen all-gen svc-apis recordservice-apis lint fetch-openapi-specs
default: help

build: generate lint coreapi ## Build coreapi binary.

# Can be overridden via environment variables, e.g.:
#   GOLANGCI_LINT_VERSION=v1.63.4 make lint
GOLANGCI_LINT_VERSION?=v1.64.8

lint: ## Lint go code for any issues with formatting or performance.
	go run github.com/golangci/golangci-lint/cmd/golangci-lint@$(GOLANGCI_LINT_VERSION) run ./...

coreapi:
	go mod tidy
	go env -w GOPRIVATE=gitlab.com/pockethealth/*
	go build
	# go vet checks common errors, see go tool vet help for all options.
	# exclude composites, which checks for unkeyed literal struct declarations which we use often.
	go vet -composites=false ./...

# compile for Linux
ca_linux: export GOOS=linux
ca_linux: coreapi

# colourize tests with gotest if it exists
GOTESTBIN := gotest
ifeq (, $(shell which gotest))
GOTESTBIN = go test
endif

OPENAPI_FILES := api/openAPI3spec.yaml $(wildcard api/*/*.yaml)

# Packages to be tested. By default, all packages will be tested.
PKGS?=./...

unit: ## run unit tests skip apptest, which is integration testing.
	$(GOTESTBIN) `go list $(PKGS) | grep -v /apptest`

cmd_tools_test: ## run tests that require specific command line tools and/or environment in order to execute
	$(GOTESTBIN) `go list $(PKGS) | grep -v /apptest` -tags=cmd_tools_test -v

# set default test env
# (do not copy this if making a new service, follow regionrouter example instead)
PH_IT_TEST_ENV ?= dev-qa# for integ test
PH_TEST_ENV ?= dev# for apptest

integ: ## run integ tests using devtestreader account (intended for execution on dev machines; i.e. won't have an azure managed identity)
integ: export AZURE_SUBSCRIPTION_ID=************************************
integ: export AZURE_TENANT_ID=************************************
integ: export AZURE_CLIENT_ID=************************************
integ: export AZURE_CLIENT_SECRET=****************************************
integ: export KUBE_CONF_FILE=$(CURDIR)/tmp/config
integ: export IT_CONF=$(CURDIR)/apptest/gotests/config.$(PH_IT_TEST_ENV).json
integ:
# Check if the 'integ' target is invoked in a CI/CD pipeline job environment. $(CI_PROJECT_DIR) is set by the pipeline.
ifeq ($(CI_PROJECT_DIR),)
	az login --service-principal -u $(AZURE_CLIENT_ID) -t $(AZURE_TENANT_ID) -p $(AZURE_CLIENT_SECRET)
	az aks get-credentials --resource-group pockethealth_test --name qa-aks-2 -f  $(KUBE_CONF_FILE)
	kubelogin convert-kubeconfig -l azurecli --kubeconfig $(KUBE_CONF_FILE)
endif
	go clean -testcache
	$(GOTESTBIN) -covermode=count `go list $(PKGS) | grep -v /apptest` -tags=integration -coverpkg=$(PKGS)  -coverprofile=coverage/integration_unit.cov $(PKGS)
	cat coverage/integration_unit.cov | grep -v "mock" | grep -v "gitlab.com/pockethealth/coreapi/generated/" > coverage/integration_unit_filtered.cov

coverage_html: ## view coverage information in html form
	go tool cover -html coverage/integration_unit_filtered.cov

# run application tests (needs port 443 to be open).  note: if this fails, use
# `make kill_dev_deps` to tidy up.  if you started the dependencies already
# using setup_dev_deps, you need to stop them before running this.
apptest: build ## Run app tests
	cd apptest && $(MAKE)

apptest_qa_local: build ## run application tests with a local instance connected to qa services and dbs
	cd apptest && $(MAKE) apptest_qa_local

start_dc: ## runs local dependencies for local testing/dev purposes
	cd apptest && $(MAKE) setup_dev_deps

stop_dc: ## stop local dependencies
	cd apptest && $(MAKE) clean

kill_dc: ## kill local dependencies incl. running containers
	cd apptest && $(MAKE) kill_dev_deps

# makes distribution suitable to be put into Docker image
# also runs unit tests
dist: generate unit ca_linux
	mkdir -p dist
	echo -n 'dev build - last commit was ' > dist/build_info.txt
	git rev-parse --verify HEAD >> dist/build_info.txt
	cp -r coreapi docker-entrypoint.sh configs jsScripts assets locales tmpl test_only_cert dist
	chmod -R go-rwx dist/

NAME   := phcrcacentral0.azurecr.io/coreapi
TAG    := $$(git rev-parse --verify HEAD --short)
IMG    := ${NAME}:${TAG}
LATEST := ${NAME}:latest
USERID := 21000
GROUPID:= 21000

i18n_extract:
	$$(go env GOPATH)/bin/goi18n extract -outdir locales -format json
	$$(go env GOPATH)/bin/goi18n merge -outdir locales -format json locales/active.*.json

docker: clean_bin dist ## build docker image
	az acr login --name phcrcacentral0
	docker build -t ${IMG} --platform linux/amd64 --target ca .
	docker tag ${IMG} ${LATEST}

DOCKER_COMPOSE_FILE = docker-compose.yml
DOCKER_COMPOSE_CMD = docker compose

local_dev: ## Run local dev coreapi
	@$(DOCKER_COMPOSE_CMD) -f $(DOCKER_COMPOSE_FILE) up -d coreapi

docker_tail: ## Tail the logs on the running coreapi instance
	@$(DOCKER_COMPOSE_CMD) logs -f coreapi

#build into docker image and run it. This image hooks into the apptest network (local mysql spun up)
# This directive used to not be used for apptests. During a partial refactor I made apptests use this,
# but due to the flow of apptests I therefore needed this to not be daemonized
# nt = no tail
run_docker_dev_nt: export PH_ENV=dev
run_docker_dev_nt: export AZURE_SUBSCRIPTION_ID=************************************
run_docker_dev_nt: export AZURE_TENANT_ID=************************************
run_docker_dev_nt: export AZURE_CLIENT_ID=************************************
run_docker_dev_nt: export AZURE_CLIENT_SECRET=****************************************
run_docker_dev_nt: stop_dc start_dc docker local_dev

run_docker_dev: run_docker_dev_nt ## build into docker image and run it.
	@$(DOCKER_COMPOSE_CMD) logs -f coreapi

#build into docker image and run it, connecting to QA dbs
run_docker_qa_nt: export PH_ENV=dev-qa
run_docker_qa_nt: export AZURE_SUBSCRIPTION_ID=************************************
run_docker_qa_nt: export AZURE_TENANT_ID=************************************
run_docker_qa_nt: export AZURE_CLIENT_ID=************************************
run_docker_qa_nt: export AZURE_CLIENT_SECRET=****************************************
run_docker_qa_nt: docker local_dev

run_docker_qa: run_docker_qa_nt ## build into docker image and run it, connecting to QA dbs
	@$(DOCKER_COMPOSE_CMD) logs -f coreapi

# Ensure the api/common directory exists
api/common:
	mkdir -p $@

fetch-openapi-specs: api/common
	git archive --remote=**************:pockethealth/phutils.git main api/error.v1.yaml | tar xO > api/common/error.v1.yaml

all-gen: svc-apis generate

svc-apis: recordservice-apis

recordservice-apis:
	git archive --remote=**************:pockethealth/recordservice.git HEAD generated/spec/openapi.yaml | tar xO > pkg/services/recordservice/openapi.yaml

generate: generated/spec/openapi.yaml
	go generate $(PKGS)

generated/spec:
	mkdir -p $@

generated/spec/openapi.yaml: $(OPENAPI_FILES) | generated/spec
	REDOCLY_TELEMETRY=off redocly bundle api/openAPI3spec.yaml --output $@

clean-gen:
	rm -rf generated

clean_bin:
	rm -rf dist coreapi vault

clean: clean_bin clean-gen ## Clean up all build and generated artifacts.
	go clean -testcache
	cd apptest && $(MAKE) clean
	rm -rf $(CURDIR)/tmp
	@$(DOCKER_COMPOSE_CMD) down && @$(DOCKER_COMPOSE_CMD) rm

DB_USER ?= devuser
DB_PASS ?= devpw
DB_PORT ?= 3306
DB_NAME ?= pockethealth
CONNSTR := $(DB_USER):$(DB_PASS)@tcp(localhost:$(DB_PORT))/$(DB_NAME)?parseTime=true

migration:
	goose -dir migrations create ${NAME} sql

db_up:
	goose -dir migrations mysql '$(CONNSTR)' up

db_status:
	goose -dir migrations mysql '$(CONNSTR)' status

db_down:
	goose -dir migrations mysql '$(CONNSTR)' down

#certfile is required to access the prod mysqls. This cert is from Azure.
db_up_prod:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" up

db_status_prod:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" status

db_down_prod:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" down

db_up_prod_uswest:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" up

db_status_prod_uswest:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" status

db_down_prod_uswest:
	@goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "$(CONNSTR)" down

help: ## Show this help.
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+%?:.*?## / {sub("\\\\n",sprintf("\n%22c"," "), $$2);printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
