#!/bin/bash

# This is a hack to make the test TestPostIncompleteRequestsV2/should_encrypt_token_correctly
# work.  The test runs requests.generateConsentPDFs func, which wants to run the "convert" 
# program from ImageMagick.  The test at one point relied on setting the wrong content-type 
# in its request so that that code path for "convert" didn't get run.  As part of a security
# fix, needed to ensure content-type is set properly, so that "cheat" no longer works.  The
# CICD build job does not have "convert", so the test fails.  This file is part of a temp
# workaround to ensure a program named "convert" exists in the PATH when the test gets run 
# in CI, so it does not fail.

exit 0
