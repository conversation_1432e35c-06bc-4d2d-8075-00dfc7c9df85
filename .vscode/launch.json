{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}",
      "env": {
        "AZURE_SUBSCRIPTION_ID": "d12085a7-7fc3-449f-859f-40cefd7dd709",
        "AZURE_TENANT_ID": "ad8e19d0-8460-4b06-86a3-fe948041ccd2",
        "AZURE_CLIENT_ID": "95467f8b-80ae-4c92-a58e-461aad32d945",
        "AZURE_CLIENT_SECRET": "****************************************"
      },
      "args": ["--env", "dev"],
      "trace": "verbose"
    }
  ]
}
