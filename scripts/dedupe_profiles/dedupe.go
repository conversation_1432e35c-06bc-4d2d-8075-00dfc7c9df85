package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
)

type Profile struct {
	Id        int
	UserId    int
	Mrn       string
	Ohip      string
	OhipVc    string
	AltHId    string
	Ipn       string
	Ssn       string
	Dob       string
	AcctOwner int
	Updated   time.Time
}

func main() {
	args := os.Args[1:]

	sqlConnStr := args[0] //"devuser:devpw@tcp(127.0.0.1:3306)/pockethealth?parseTime=true"
	hrsConnStr := args[1] //"hrs:w.AZz1M_gLz(;hMUN3*6U!Pq@tcp(phqa0.mysql.database.azure.com:3306)/healthrecords?parseTime=true&tls=skip-verify&allowNativePasswords=true"

	phdb, err := sql.Open("mysql", sqlConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to perform sql.Open")
	}
	err = phdb.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping db")
	}

	hrdb, err := sql.Open("mysql", hrsConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to perform sql.Open for hr")
	}
	err = hrdb.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping db for hr")
	}

	//just a get list of all profiles in hrs, there's ~17k

	rows, err := hrdb.Query("SELECT ph_profile_id FROM user_mappings")
	if err != nil {
		log.Fatalf("couldn't get hr profiles: %v", err)
	}

	defer rows.Close()
	hrProfs := map[int]bool{} //only have ids, but make it a map to make it a quick check
	for rows.Next() {
		var pId int
		err := rows.Scan(&pId)
		if err != nil {
			log.Fatalf("couldn't read hr profile id %v", err)
		}
		hrProfs[pId] = true
	}

	batchSize := int(50000)
	minUserId := int(0)
	endOfResults := false
	for i := 0; !endOfResults; i++ {
		ps := map[int][]Profile{}
		log.Printf("min id: %d", minUserId)
		rows, err := phdb.Query(
			`select user_id, id, mrn, ohip, ohip_vc, ipn, ssn, alt_h_id, dob, is_account_owner, updated
		FROM profiles 
		WHERE user_id < ?
		AND user_id >= ?
		ORDER BY user_id asc`,
			(minUserId*i)+batchSize,
			minUserId*i,
		)
		if err != nil {
			log.Fatalf("could not get profiles at min id %d: %v", minUserId, err)
		}

		defer rows.Close()

		for rows.Next() {
			newP := Profile{}
			var nmrn, nohip, nohipvc, nipn, nssn, nalthid sql.NullString
			err := rows.Scan(
				&newP.UserId,
				&newP.Id,
				&nmrn,
				&nohip,
				&nohipvc,
				&nipn,
				&nssn,
				&nalthid,
				&newP.Dob,
				&newP.AcctOwner,
				&newP.Updated,
			)
			if err != nil {
				log.Fatalf("cannot scan rows: %v", err)
			}

			if l, ok := ps[newP.UserId]; ok {
				ps[newP.UserId] = append(l, newP)
			} else {
				ps[newP.UserId] = []Profile{newP}
				minUserId = newP.UserId
			}
		}
		log.Printf("profiles queried: %d", len(ps))

		if len(ps) == 0 {
			endOfResults = true
		}

		//dedupe for each user
		for uid, profs := range ps {
			if len(profs) > 1 {
				idList := getDupes(profs, hrProfs)
				if len(idList) > 0 {
					log.Printf("dupes for user %d: %v", uid, idList)
					idStr := strings.Trim(
						strings.Join(strings.Fields(fmt.Sprint(idList)), ","),
						"[]",
					)
					_, err := phdb.Exec(fmt.Sprintf("DELETE FROM profiles where id IN (%s)", idStr))
					if err != nil {
						log.Fatalf("error deleting dup profiles: %v", err)
					}
				}
			}
		}

	}

}

//for each user,
// do they have more than one profile?
//if so, are any duplicates (ie, health id and dob matches)
//if so, are any of the duplicates not in the hrs db
//if yes, delete that one.
//if no, if one is the account owner and others are not, keep the account owner
//else keep the most recent

func getDupes(ps []Profile, hrProfs map[int]bool) []int {
	uniques := []Profile{}
	dupIds := []int{}
	for i, pToCheck := range ps {
		if i == 0 {
			uniques = append(uniques, pToCheck)
			continue
		}
		added := false
		duplicate := false
		for j, up := range uniques {
			if profilesDuplicate(pToCheck, up) {
				duplicate = true
				_, toCheckYes := hrProfs[pToCheck.Id]
				_, upYes := hrProfs[up.Id]

				//if both have hrs, need to keep both despite duplication
				if toCheckYes && upYes {
					break
				}

				//if one has hrs, and the other doesn't, keep the hr one.
				if !toCheckYes && upYes {
					dupIds = append(dupIds, pToCheck.Id)
					break
				} else if toCheckYes && !upYes {
					dupIds = append(dupIds, uniques[j].Id)
					uniques[j] = pToCheck
					added = true
					break
				}

				//if the account owner status is different, take the owner
				if pToCheck.AcctOwner > up.AcctOwner {
					dupIds = append(dupIds, uniques[j].Id)
					uniques[j] = pToCheck
					added = true
					break
				}
				//otherwise take the most recent
				if up.Updated.Before(pToCheck.Updated) {
					dupIds = append(dupIds, uniques[j].Id)
					uniques[j] = pToCheck
					added = true
					break
				} else {
					dupIds = append(dupIds, pToCheck.Id)
				}
			}
		}
		if !added && !duplicate {
			uniques = append(uniques, pToCheck)
		}
	}
	return dupIds
}

func profilesDuplicate(p1 Profile, p2 Profile) bool {
	return p1.Dob == p2.Dob &&
		(nonBlankMatch(p1.Ohip, p2.Ohip) || nonBlankMatch(p1.Mrn, p2.Mrn) || nonBlankMatch(p1.AltHId, p2.AltHId) || nonBlankMatch(p1.Ipn, p2.Ipn) || nonBlankMatch(p1.Ssn, p2.Ssn) || (allBlankHealthIds(p1) && allBlankHealthIds(p2)))
}

func nonBlankMatch(s1 string, s2 string) bool {
	return s1 != "" && s2 != "" && s1 == s2
}

func allBlankHealthIds(p Profile) bool {
	return p.Ohip == "" && p.Mrn == "" && p.AltHId == "" && p.Ipn == "" && p.Ssn == ""
}
