# Dedupe profiles script
## Purpose
This script is in preparation of the profiles -> patients migration. Due to bugs at various times, or user error, many accounts have duplicate profiles. This script removes dupes with the awareness of links to health records and preserving an account owner. 

## To run

`go build`
`./dedupe <phsqlconnectionstring> <hrsqlconnectionstring>`

EG for testing,

`./dedupe 'devuser:devpw@tcp(127.0.0.1:3306)/pockethealth?parseTime=true' 'hrs:w.AZz1M_gLz(;hMUN3*6U!Pq@tcp(phqa0.mysql.database.azure.com:3306)/healthrecords?parseTime=true&tls=skip-verify&allowNativePasswords=true'`