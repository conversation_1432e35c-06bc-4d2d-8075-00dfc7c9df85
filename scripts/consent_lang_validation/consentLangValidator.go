package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"index/suffixarray"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
	phconfig "gitlab.com/pockethealth/phutils/v10/pkg/config"
	phkeyvault "gitlab.com/pockethealth/phutils/v10/pkg/keyvault"
	"golang.org/x/net/html"
)

func main() {
	config, err := SetupConfigurations()
	if err != nil {
		return
	}
	validateConsents(config)
}

type DatabaseConfigurations struct {
	SourceCA *sql.DB
	// SourceUS    *sql.DB
	Destination *sql.DB
}

type ConsentStruct struct {
	ProviderId     int
	PatientConsent string
	DelegConsent   string
}

func usageAndExit(arg string) {
	fmt.Printf("usage: %s <env>", arg)
	os.Exit(1)
}

func SetupConfigurations() (DatabaseConfigurations, error) {
	// CLI args
	args := os.Args[1:]
	logrus.Infof("args %d", len(args))
	if len(args) < 1 {
		usageAndExit(os.Args[0])
	}
	environment := args[0]

	cfg := phconfig.SelectConfig("config", environment)

	// setup keyvault so we can load config values from it
	var keyvaultName string
	if err := json.Unmarshal(cfg["keyvault_name"], &keyvaultName); err != nil {
		logrus.Info("Couldn't load keyvault name")
		keyvaultName = ""
	}

	kv, err := phkeyvault.NewKeyvault(keyvaultName)
	if err != nil {
		logrus.Infof(
			"Unable to create Keyvault client: %s.  You will not be able to use keyvault secret names in configuration.\n",
			err,
		)
	}

	// uDB := setupMySQL(cfg, kv, "coreapi_us_mysql_connection_string")
	cDB := setupMySQL(cfg, kv, "coreapi_ca_mysql_connection_string")
	pDB := setupMySQL(cfg, kv, "orgsvc_mysql_connection_string")

	deps := DatabaseConfigurations{
		SourceCA: cDB,
		// SourceUS:    uDB,
		Destination: pDB,
	}

	return deps, nil
}

func setupMySQL(cfg map[string]json.RawMessage, kv *phkeyvault.Keyvault, keyName string) *sql.DB {
	sqlConnStr := phconfig.ValFromConfOrKeyvault(kv, cfg, keyName, keyName+"_sec_name")
	sqlMaxIdleCfg, _ := cfg["mysql_idle_conns"].MarshalJSON()
	sqlMaxOpenCfg, _ := cfg["mysql_open_conns"].MarshalJSON()
	sqlConnLifetimeCfg, _ := cfg["mysql_conn_lifetime_mins"].MarshalJSON()

	db, err := sql.Open("mysql", sqlConnStr)
	if err != nil {
		logrus.WithError(err).Fatalf("unable to perform sql.Open for DB with key: `%s`", keyName)
	}
	err = db.Ping()
	if err != nil {
		logrus.WithError(err).Fatalf("unable to ping DB with key: `%s`", keyName)
	}

	sqlMaxIdle, err := strconv.Atoi(string(sqlMaxIdleCfg))
	if err != nil {
		logrus.Info("assigning default max idle sql conns")
		sqlMaxIdle = 10
	}
	sqlMaxOpen, err := strconv.Atoi(string(sqlMaxOpenCfg))
	if err != nil {
		logrus.Info("assigning default max open sql conns")
		sqlMaxOpen = 10
	}
	sqlConnLifetime, err := strconv.Atoi(string(sqlConnLifetimeCfg))
	if err != nil {
		logrus.Info("assigning default sql conn lifetime")
		sqlConnLifetime = 5
	}

	db.SetMaxIdleConns(sqlMaxIdle)
	db.SetMaxOpenConns(sqlMaxOpen)
	db.SetConnMaxLifetime(time.Minute * time.Duration(sqlConnLifetime))
	return db
}

func getAllConsentLangs(ctx context.Context, db *sql.DB) ([]ConsentStruct, error) {
	var err error
	var ConsentLangs []ConsentStruct

	pullTwo, err := db.Query(`SELECT organization_id, patient_consent_lang, delegate_consent_lang from pockethealth.forms`)
	if err != nil {
		return []ConsentStruct{}, err
	}
	defer pullTwo.Close()

	for pullTwo.Next() {
		temp := ConsentStruct{}
		var delegConsentLang sql.NullString
		var patientConsentLang sql.NullString
		var providerId int

		err = pullTwo.Scan(
			&providerId,
			&patientConsentLang,
			&delegConsentLang,
		)
		if err != nil {
			return []ConsentStruct{}, err
		}
		if delegConsentLang.Valid {
			temp.DelegConsent = delegConsentLang.String
		} else {
			temp.DelegConsent = ""
		}

		if patientConsentLang.Valid {
			temp.PatientConsent = patientConsentLang.String
		} else {
			temp.PatientConsent = ""
		}
		temp.ProviderId = providerId
		ConsentLangs = append(ConsentLangs, temp)
	}

	return ConsentLangs, nil
}

// func consentValidate(configs DatabaseConfigurations) {
// 	ctx := context.Background()
// 	fmt.Println(ctx)
// }

func validateConsents(configs DatabaseConfigurations) {
	ctx := context.Background()

	ConsentLangsCA, err := getAllConsentLangs(ctx, configs.SourceCA)
	if err != nil {
		logrus.Error("failed to get consent langs CA", err.Error())
		return
	}
	// ConsentLangsUS, err := getAllConsentLangs(ctx, configs.SourceUS)
	// if err != nil {
	// 	logrus.Error("failed to get consent langs US", err.Error())
	// 	return
	// }
	// ConsentLangs := append(ConsentLangsCA, ConsentLangsUS...)
	ConsentLangs := ConsentLangsCA

	// go through the consent langs and run our validator both
	// after the json function too?

	for _, consentLang := range ConsentLangs {
		resultPatient, err := ConsentLangValidator(consentLang.PatientConsent)
		if err != nil {
			logrus.Error("Provider "+strconv.Itoa(consentLang.ProviderId)+" failed validation of patient consent ", err.Error())
		}
		resultDeleg, err := ConsentLangValidator(consentLang.DelegConsent)
		if err != nil {
			logrus.Error("Provider "+strconv.Itoa(consentLang.ProviderId)+" failed validation of deleg consent ", err.Error())
		}
		fmt.Println("Patient Validation Pass: "+strconv.FormatBool(resultPatient), "Delegate Validation Pass: "+strconv.FormatBool(resultDeleg))
	}

}

func ConsentLangValidator(consentText string) (bool, error) {
	/*
		check if every html tag is either as simple bold, line break, div or span ??
		check if the text itself is valid through other metrics
		check if string only contains alphanumeric characters allowing the needed
	*/
	consentText = strings.ReplaceAll(consentText, "<br>", `\n`)
	re := regexp.MustCompile("(<[a-z]*>)|(</[a-z]*>)")
	htmlTags := re.FindAllString(consentText, -1)
	splitText := re.Split(consentText, -1)
	if len(htmlTags) > 1 {
		for _, tag := range htmlTags {
			if !(strings.Contains(tag, "<b>") || strings.Contains(tag, "</b>")) &&
				!(strings.Contains(tag, "<span>") || strings.Contains(tag, "</span>")) &&
				!(strings.Contains(tag, "<div>") || strings.Contains(tag, "</div>")) &&
				!(strings.Contains(tag, "<ul>") || strings.Contains(tag, "</ul>")) &&
				!(strings.Contains(tag, "<li>") || strings.Contains(tag, "</li>")) &&
				!(strings.Contains(tag, "<li>")) {
				return false, errors.New("there exists a html tag in the consent lang that will be printed in plain text in the pdf")
			}
		}
	}
	_, err := html.Parse(strings.NewReader(consentText))
	if err != nil {
		return false, errors.New("invalid html")
	}
	if len(splitText) > 1 {
		for _, sect := range splitText {
			// text in between html tags should be strictly alphanumeric
			re = regexp.MustCompile(`([a-zA-z0-9\p{P}<>\\\/\ ]*)`)
			innerSplit := re.Split(sect, -1)
			if len(innerSplit) > 1 && innerSplit[0] != "" {
				return false, errors.New("there exists a some undesired characters that are not alphanum in consentlang")
			}
		}
	}
	openingTags := regexp.MustCompile("(<[a-z]*>)")
	closingTags := regexp.MustCompile("(</[a-z]*>)")
	listItemRE := regexp.MustCompile("(<li>)")
	closingListItemRE := regexp.MustCompile("(</li>)")
	temp := suffixarray.New([]byte(consentText))
	resultsOpening := temp.FindAllIndex(openingTags, -1)
	resultsClosing := temp.FindAllIndex(closingTags, -1)
	listItems := temp.FindAllIndex(listItemRE, -1)
	closingListItems := temp.FindAllIndex(closingListItemRE, -1)
	if len(resultsOpening) != len(resultsClosing)+(len(listItems)-len(closingListItems)) {
		return false, errors.New("there exists a HTML tag that was not closed")
	}
	return true, nil
}

func ConsentJSONValidator(consentJSON string) (bool, error) {
	/*
		check that there are no remaining html tags
		make sure the text is only alphanumeric but allow punc, and apostrophies
		check if the text itself is valid idk how tho
		check if the json is valid
	*/
	re := regexp.MustCompile("(<[a-z]*>)|(</[a-z]*>)")
	splitText := re.Split(consentJSON, -1)
	if len(splitText) > 1 {
		return false, errors.New("there exists a html tag in the json consent lang that will be printed in plain text in the pdf")
	}
	re = regexp.MustCompile(`([a-zA-z0-9\p{P}<>\/\ ]*)`)
	splitText = re.Split(consentJSON, -1)
	if len(splitText) > 1 && splitText[0] != "" {
		return false, errors.New("there exists a some undesired characters that are not alphanum in json consent")
	}
	if !json.Valid([]byte(consentJSON)) {
		return false, errors.New("the json produced by consent lang is not valid")
	}
	return true, nil
}
