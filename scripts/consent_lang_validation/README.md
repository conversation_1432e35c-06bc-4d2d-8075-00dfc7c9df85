# Validate all ConsentLangs in the databse

## Build

`go build`

## to build and run

---

```
go build ./consent_lang_validator
./consent_lang_validator <env>
```

for example, for testing using QA db run

```
go build ./consent_lang_validator
./consent_lang_validator qa
```

To run on prod, create a config.prod.json in the configs folder and add the mysql connection strings (from keyvault) and rebuild:
for example, for testing using QA db run

```
go build ./seed-request-form-configurations
./seed-request-form-configurations prod
```
