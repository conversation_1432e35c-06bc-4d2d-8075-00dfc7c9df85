{"keyvault_name": "phtestkvcacentral0", "orgsvc_mysql_connection_string": "orgsvc:&UFmvA^Nge&B71jnb0eLBum6@tcp(phqa0.mysql.database.azure.com:3306)/providersvc?parseTime=true&tls=skip-verify&allowNativePasswords=true", "coreapi_ca_mysql_connection_string": "pockethealth-web:hZ1NSfeCkgG1!@tcp(phqa0.mysql.database.azure.com:3306)/pockethealth?parseTime=true&tls=skip-verify&allowNativePasswords=true", "coreapi_us_mysql_connection_string": "pockethealth-web@ph-sql-main-qa-uswestv2:JwjUu>)N03f6-&A~Yhv>y&9Q@tcp(ph-sql-main-qa-uswestv2.mysql.database.azure.com)/pockethealth?parseTime=true&tls=skip-verify&allowNativePasswords=true", "max_idle_sql_conns": 10, "max_sql_conn_mins": 10, "max_open_sql_conns": 1000}