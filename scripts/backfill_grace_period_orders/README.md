# Backfill `exam_orders` for exams that were covered by a grace period

## Build
`go build`

## Run
Running the executable will create a csv, `examorders.csv` that can imported into `exam_orders` directly for purchases between two dates (inclusive)

`./backfillexamorders <mysqlpassword> 2022-01-01 2022-01-31`

## Import
In the mysql cli, run the following
 ```
   LOAD DATA LOCAL INFILE 'examorders.csv' INTO TABLE exam_orders
   FIELDS TERMINATED BY ','
   LINES TERMINATED BY '\n';
   ```