package main

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	args := os.Args[1:]
	mysqlpw := args[0]
	//pockethealth db
	phconnStr := fmt.Sprintf(
		"admins-phprod0@phprod0:%s@tcp(phprod0.mysql.database.azure.com:3306)/pockethealth?parseTime=true&tls=skip-verify&allowNativePasswords=true&allowCleartextPasswords=1",
		mysqlpw,
	)
	phdb, err := sql.Open("mysql", phconnStr)
	if err != nil {
		log.Fatalf("unable to perform sql.Open for pockethealth: %v", err)
	}
	err = phdb.Ping()
	if err != nil {
		log.Fatalf("unable to ping phdb, %q", err)
	}

	start, err := time.Parse("2006-01-02", args[1])
	if err != nil {
		log.Fatalf("unable to get startdate: %s", args[1])
	}

	end, err := time.Parse("2006-01-02", args[2])
	if err != nil {
		log.Fatalf("unable to get enddate: %s", args[2])
	}

	csvFileName := "examorders.csv"
	f, err := os.Create(csvFileName)
	if err != nil {
		log.Fatalf("could not open file:%v", err)
	}
	csvf := csv.NewWriter(f)

	tIds := getTransfersIds(phdb, start, end)
	chunkSize := 200
	for i := 0; i < len(tIds); i += chunkSize {
		lix := i + chunkSize + 1
		if len(tIds) < lix {
			lix = len(tIds)
		}

		log.Printf("processing %d-%d", i, lix-1)
		eomap := getExamsAndOrderInGroup(phdb, tIds[i:lix])

		for e, b := range eomap {
			if e != "" && b != "" {
				err := csvf.Write([]string{e, b})
				if err != nil {
					log.Printf("error writing to csv: %v", err)
				}
			}
		}
		csvf.Flush()
	}
	csvf.Flush()

}

func getTransfersIds(db *sql.DB, start time.Time, end time.Time) []string {
	rows, err := db.Query(`select scan_id from purchases p
	join exams e on p.scan_id = e.transfer_id
	left join exam_orders eo on e.uuid = eo.exam_uuid 
	 where eo.order_id is null and stripe_charge_id = 'GRACE_PERIOD' and purchased >= ? and purchased <= ?`, start, end)
	if err != nil {
		log.Fatalf("could not fetch transfer_ids for %v, %v", start, err)
	}
	defer rows.Close()
	tIdList := []string{}
	for rows.Next() {
		var tId string
		err := rows.Scan(&tId)
		if err != nil {
			continue
		}
		tIdList = append(tIdList, tId)
	}
	log.Printf("got %d transfers", len(tIdList))
	return tIdList
}

func getExamsAndOrderInGroup(db *sql.DB, tIds []string) map[string]string {

	mapping := map[string]string{}

	var connectionCount = 5
	connPool := make(chan bool, connectionCount)
	var swg sync.WaitGroup

	var lock = sync.RWMutex{}
	for i := range tIds {

		swg.Add(1)
		go func(tid string) {
			connPool <- true
			rows, err := db.Query(`select e.uuid, eo.order_id
	from exams e join 
	(select transfer_id from transfer_group 
		where transfer_id = ? or parent_transfer_id = ?
		or parent_transfer_id in (select parent_transfer_id from transfer_group where transfer_id = ?)
		or transfer_id in (select parent_transfer_id from transfer_group where transfer_id = ?)
		) g
	on e.transfer_id = g.transfer_id
	left join exam_orders eo on e.uuid = eo.exam_uuid`, tid, tid, tid, tid)

			if err != nil {
				log.Fatalf("could not get exams and orders in group: %v", err)
				<-connPool
				return
			}
			defer rows.Close()
			exams := []string{}
			orderId := ""
			ocount := 0
			for rows.Next() {
				var euuid string
				var oId sql.NullString
				err = rows.Scan(&euuid, &oId)
				if err != nil {
					log.Print("euuid scan error")
					continue
				}
				exams = append(exams, euuid)
				if oId.Valid {
					if orderId != "" && oId.String != orderId {
						ocount++
					} else {
						orderId = oId.String
					}
				}
			}
			if ocount > 1 {
				log.Printf("more than one order for this %s group: %d", tIds[i], ocount)

				swg.Done()
				<-connPool
				return
			}
			if orderId != "" {
				for j := range exams {
					lock.Lock()
					mapping[exams[j]] = orderId
					lock.Unlock()
				}
			}

			swg.Done()
			<-connPool
		}(tIds[i])
	}
	swg.Wait()
	log.Printf("got %d exam mappings", len(mapping))
	return mapping
}
