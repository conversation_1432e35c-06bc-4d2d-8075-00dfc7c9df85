# backfill account id script
this script is part of the profiles -> patients migration. to link various different entities to accounts instead of a regional user which willbe deprecated eventually, backfilling new `account_id` columns of tables that need to be updated.
>note: this should be ran once in each region

## to run
---
```
go build
./backfill_acctId <accts_conn_str> <ph_conn_str> <region_id>
```

for example, for testing using QA `accounts` and a local `pockethealth` db
```
./backfill_acctid "acctsvc:&l,jD0CI#I/Br2*BgYpDh(pO@tcp(phqa0.mysql.database.azure.com:3306)/accounts?parseTime=true&tls=skip-verify&allowNativePasswords=true" "devuser:devpw@tcp(127.0.0.1:3306)/pockethealth?parseTime=true" 1
```