package main

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strconv"

	"github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
)

func main() {
	args := os.Args[1:]

	// check args / print usage
	if len(args) < 3 {
		fmt.Fprintf(
			os.<PERSON>derr,
			"usage: %s <accts_conn_str> <ph_conn_str> <region_id>",
			os.Args[0],
		)
		return
	}
	regId, err := strconv.Atoi(args[2])
	if err != nil || (regId != 1 && regId != 2) {
		logrus.Fatalf("invalid region id, got %s", args[2])
		return
	}

	// connect to mysql
	acctConnStr := args[0]
	sqlConnStr := args[1]

	asdb, err := sql.Open("mysql", acctConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to sql.Open")
	}
	err = asdb.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping db")
	}

	phdb, err := sql.Open("mysql", sqlConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to sql.Open")
	}
	err = phdb.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping db")
	}

	// prep csv file
	f, err := os.Create("idmap.csv")
	if err != nil {
		logrus.WithError(err).Fatal("failed to create file")
	}
	w := csv.NewWriter(f)

	var data [][]string
	tables := [3]string{"user_notifications", "shares", "share_analytics"} // tables to be updated

	// get all id mappings
	rows, err := asdb.Query(
		`select id, user_id from 
			(select id, main_region_user_id as user_id  from accounts.accounts where main_region_id=?) as a 
		UNION
			(select id, user_id from accounts.account_other_regions where region=?)`,
		regId,
		regId,
	)
	if err != nil {
		logrus.WithError(err).Fatal("failed to get id mapping")
	}
	defer rows.Close()
	for rows.Next() {
		var aId string
		var uId sql.NullInt64
		err := rows.Scan(&aId, &uId)
		if err != nil {
			logrus.WithError(err).Fatal("failed to read account and user id mapping")
		}
		// prep data for writing to csv
		if uId.Valid {
			rec := []string{strconv.Itoa(int(uId.Int64)), aId}
			data = append(data, rec)
		}
	}

	// write to csv
	err = w.WriteAll(data)
	if err != nil {
		logrus.WithError(err).Fatal("error writing csv")
	}
	err = f.Close()
	if err != nil {
		logrus.WithError(err).Fatal("failed to close csv file")
	}

	// create temp table
	_, err = phdb.Exec(`create temporary table temp_map (
		account_id char(28),
		user_id bigint(10) unique
	)`)
	if err != nil {
		logrus.Fatalf("failed to create temp table: %v", err)
	}

	// populate table with csv
	// use mysql readerhandler
	mysql.RegisterReaderHandler("data", func() io.Reader {
		var csvReader io.Reader
		csvReader, err := os.Open("idmap.csv")
		if err != nil {
			logrus.Fatalf("failed to open idmap.csv: %v", err)
		}
		return csvReader
	})
	_, err = phdb.Exec(
		"load data local infile 'Reader::data' into table temp_map fields terminated by ',' (user_id, account_id)",
	)
	if err != nil {
		logrus.Fatalf("failed to load data into temp table: %v", err)
	}

	// update join on temp table and tables to-be-updated
	for _, t := range tables {
		_, err := phdb.Exec(fmt.Sprintf(`update %s ut
			inner join temp_map tt on ut.user_id = tt.user_id
			set ut.account_id = tt.account_id
		`, t))
		if err != nil {
			logrus.Fatalf("failed to update join on table %s: %v", t, err)
		}
	}

	// drop temp table
	_, err = phdb.Exec("drop temporary table if exists temp_map")
	if err != nil {
		logrus.Fatalf("failed to drop temp table: %v", err)
	}
}
