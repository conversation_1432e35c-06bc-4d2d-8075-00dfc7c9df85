#!/bin/bash

DB_USER="admins-phprod0@phprod0"
DB_NAME="pockethealth"
DB_HOST="phprod0.mysql.database.azure.com"
DB_PORT="3306"

MYSQL_PWD="$1"
PROVIDER_ID="$2"

if [[ -z "$MYSQL_PWD" || -z "$PROVIDER_ID" ]]; then
    echo "Usage: $0 <db_password> <provider_id>"
    exit 1
fi

SQL=$(sed -e "s/{{PROVIDER_ID}}/$PROVIDER_ID/g" fill_enrollment_hash.sql)
export MYSQL_PWD=$MYSQL_PWD
mysql -u "$DB_USER" -h "$DB_HOST" -P "$DB_PORT" -D "$DB_NAME" -e "$SQL"
