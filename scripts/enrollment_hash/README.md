# Filling Enrollment Hash Migration

Tool to gradually fill the `lookup_hash` in the `enrollments` table. The column is null by default, however, we're adding a feature that will use the column to avoid adding duplicate enrollments to the table. As we roll this out to more providers, we will be using this to migrate the provider's enrollment records.

## Run

1. Change the `DB_USER` and `DB_HOST` in the script
2. Run the following command in bash: `./run.sh [db password] [providerID]`

## Testing

To test that the script is working as expected, insert the following records in the db:

```sql
INSERT INTO enrollments
  (id, org_id, mrn, issuer, email, active, audit_user, timestamp, empi, patient_id)
  VALUES (11201, 101, '15672', 'pacs1', '<EMAIL>', 1, '', 2005-01-01, '', ''); -- should have same lookup_hash
INSERT INTO enrollments
  (id, org_id, mrn, issuer, email, active, audit_user, timestamp, empi, patient_id)
  VALUES (11202, 101, '15672', 'pacs1', '<EMAIL>', 1, '', 2005-01-01, '', ''); -- should have same lookup_hash
INSERT INTO enrollments
  (id, org_id, mrn, issuer, email, active, audit_user, timestamp, empi, patient_id)
  VALUES (11203, 101, '15672', 'pacs1', '<EMAIL>', 1, '', 2005-01-01, '', ''); -- should have same lookup_hash
INSERT INTO enrollments
  (id, org_id, mrn, issuer, email, active, audit_user, timestamp, empi, patient_id)
  VALUES (11204, 102, '15672', 'pacs2', '<EMAIL>', 1, '', 2005-01-01, '', ''); -- should have empty lookup_hash
INSERT INTO enrollments
  (id, org_id, mrn, issuer, email, active, audit_user, timestamp, empi, patient_id)
  VALUES (11205, 102, '15672', 'pacs2', '<EMAIL>', 1, '', 2005-01-01, '', ''); -- should have empty lookup_hash
SELECT id, org_id, mrn, issuer, lookup_hash FROM enrollments WHERE org_id = 101 OR org_id = 102;
```

This represents a patient that has 5 records: 3 images in provider 101 and 2 images in provider 102. Running the script should give the same `lookup_hash` for the 3 records in provider 101 and leave the 102-related records intact.
