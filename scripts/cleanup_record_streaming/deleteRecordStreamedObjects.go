package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// Set up connection to db
	// replace CONNECTION_STRING with `mysql_connection_string` value from config*.json file
	phDBConnectionString := "CONNECTION_STRING"
	db, err := sql.Open("mysql", phDBConnectionString)
	if err != nil {
		log.Fatalf("unable to perform sql.Open for pockethealth: %v", err)
	}
	err = db.Ping()
	if err != nil {
		log.Fatalf("unable to ping pockethealth db, %q", err)
	}
	fmt.Println("Database ping - success")

	// 1. Get all record streaming exam IDs
	var examIDs []string
	// unique_study_index is a table only used with record streaming, so we use it as a starting point
	// to get all associated objects
	rows, err := db.Query("SELECT exam_id FROM unique_study_index")
	if err != nil {
		fmt.Println("failed to retrieve examIDs")
	} else {
		defer rows.Close()
		for rows.Next() {
			var examID string
			err = rows.Scan(&examID)
			if err != nil {
				fmt.Println("failed to retrieve examID")
			} else {
				examIDs = append(examIDs, examID)
			}
		}
	}
	examIDsSql := `("` + strings.Join(examIDs, `", "`) + `")`

	if len(examIDs) == 0 {
		fmt.Println("No exam IDs found in unique_study_index - nothing to clean up")
		return
	}

	// 2. Get object IDs
	var objectIDs []string
	rows, err = db.Query(
		fmt.Sprintf("SELECT object_id FROM object_mappings where exam_uuid in %s", examIDsSql),
	)
	if err != nil {
		fmt.Println("failed to retrieve objectIDs")
	} else {
		defer rows.Close()
		for rows.Next() {
			var objectID string
			err = rows.Scan(&objectID)
			if err != nil {
				fmt.Println("failed to retrieve objectID")
			} else {
				objectIDs = append(objectIDs, objectID)
			}
		}
	}
	objectIDsSql := `("` + strings.Join(objectIDs, `", "`) + `")`

	// 3. Delete Series
	_, err = db.Exec(fmt.Sprintf("DELETE FROM series where exam_uuid in %s", examIDsSql))
	if err != nil {
		fmt.Println("failed to delete series")
	}

	// 4. Delete Objects
	_, err = db.Exec(fmt.Sprintf("DELETE FROM objects where object_id in %s", objectIDsSql))
	if err != nil {
		fmt.Println("failed to delete objects: ", err)
		fmt.Println(objectIDsSql)
	}

	// 5. Delete Object mappings
	_, err = db.Exec(fmt.Sprintf("DELETE FROM object_mappings where exam_uuid in %s", examIDsSql))
	if err != nil {
		fmt.Println("failed to delete object mappings")
	}

	// 6. Delete exams
	_, err = db.Exec(fmt.Sprintf("DELETE FROM exams where uuid in %s", examIDsSql))
	if err != nil {
		fmt.Println("failed to delete exams")
	}

	// The following tables are only used with record streaming, so we can delete all rows

	// 7. Delete study index
	_, err = db.Exec("DELETE FROM unique_study_index")
	if err != nil {
		fmt.Println("failed to delete study index")
	}

	// 8. Delete instance index
	_, err = db.Exec("DELETE FROM unique_instance_index")
	if err != nil {
		fmt.Println("failed to delete instance index")
	}

	// 9. Delete report index
	_, err = db.Exec("DELETE FROM unique_report_index")
	if err != nil {
		fmt.Println("failed to delete report index")
	}

	// 10. Delete history log
	_, err = db.Exec("DELETE FROM upload_status_log")
	if err != nil {
		fmt.Println("failed to delete status log")
	}

	// 11. Delete physician study permissions
	_, err = db.Exec("DELETE FROM pockethealth.physician_study_permissions")
	if err != nil {
		fmt.Println("failed to delete physician study permissions")
	}

	// 12. Delete study instance upload status
	_, err = db.Exec("DELETE FROM pockethealth.study_instance_upload_status")
	if err != nil {
		fmt.Println("failed to delete study instance upload status")
	}

	// 13. Delete study report upload status
	_, err = db.Exec("DELETE FROM pockethealth.study_report_upload_status")
	if err != nil {
		fmt.Println("failed to delete study report upload status")
	}

	// 14. Delete study preview metadata
	_, err = db.Exec("DELETE FROM pockethealth.study_preview_metadata")
	if err != nil {
		fmt.Println("failed to delete study preview metadata")
	}

	// 15. Delete patient account study permissions
	_, err = db.Exec("DELETE FROM pockethealth.patient_account_study_permissions")
	if err != nil {
		fmt.Println("failed to delete patient account study permissions")
	}

	fmt.Println("deleted objects for all examIDs ", examIDs)
}
