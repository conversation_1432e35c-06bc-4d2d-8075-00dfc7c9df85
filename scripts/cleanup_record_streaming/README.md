# Delete objects produced by record streaming

## Overview

Use this script if you want to test fetching exams / studies from a physician account, but you already downloaded the exams to that account. 

NOTE: This script removes *all* objects created via record streaming from a specified database.

This is usually fine because it's easy to just re-fetch studies for a physician account, but it will affect any live testing happening at the time the script is run. 

## Usage

1. In line 15 of the script, replace CONNECTION_STRING with a connection string value from a config*.json file:

`phDBConnectionString := "CONNECTION_STRING"`

2. From terminal in this directory, run `go run deleteRecordStreamedObjects.go`