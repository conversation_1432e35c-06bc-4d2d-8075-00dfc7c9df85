package main

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	args := os.Args[1:]
	mysqlpw := args[0]
	//pockethealth db

	phconnStr := fmt.Sprintf(
		"admins-phprod0@phprod0:%s@tcp(phprod0.mysql.database.azure.com:3306)/pockethealth?parseTime=true&tls=skip-verify&allowNativePasswords=true&allowCleartextPasswords=1",
		mysqlpw,
	)
	phdb, err := sql.Open("mysql", phconnStr)
	if err != nil {
		log.Fatalf("unable to perform sql.Open for pockethealth: %v", err)
	}
	err = phdb.Ping()
	if err != nil {
		log.Fatalf("unable to ping phdb, %q", err)
	}

	//payments db
	pmtsconnStr := fmt.Sprintf(
		"admins-phprod0@phprod0:%s@tcp(phprod0.mysql.database.azure.com:3306)/paymentservice?parseTime=true&tls=skip-verify&allowNativePasswords=true&allowCleartextPasswords=1",
		mysqlpw,
	)
	pmtsdb, err := sql.Open("mysql", pmtsconnStr)
	if err != nil {
		log.Fatal("unable to perform sql.Open for pmts")
	}
	err = pmtsdb.Ping()
	if err != nil {
		log.Fatalf("unable to ping pmtsdb, %q", err)
	}

	start, err := time.Parse("2006-01-02", args[1])
	if err != nil {
		log.Fatalf("unable to get startdate: %s", args[1])
	}

	end, err := time.Parse("2006-01-02", args[2])
	if err != nil {
		log.Fatalf("unable to get enddate: %s", args[2])
	}

	csvFileName := "examorders.csv"
	f, err := os.Create(csvFileName)
	if err != nil {
		log.Fatalf("could not open file:%v", err)
	}
	csvf := csv.NewWriter(f)

	//one day at a time, fetch the list of exams with bambora ids
	for d := end; d.Before(start) == false; d = d.AddDate(0, 0, -1) {
		log.Printf("processing %s", d.Format("2006-01-02"))

		ebmap, brmap := getBamboraPaidExams(phdb, d)
		bomap := getOrders(pmtsdb, brmap)
		ct := 0
		for e, b := range ebmap {
			if e != "" && bomap[b] != "" {
				err := csvf.Write([]string{e, bomap[b]})
				if err != nil {
					log.Printf("error writing to csv: %v", err)
				}
				ct++
				if ct%10 == 0 {
					csvf.Flush()
				}
			}
		}
		csvf.Flush()
	}

}

func getBamboraPaidExams(phdb *sql.DB, date time.Time) (map[string]string, map[string]string) {
	rows, err := phdb.Query(`SELECT e.uuid, p.stripe_charge_id, o.region 
	FROM exams e 
	LEFT JOIN exam_orders eo on e.uuid = eo.exam_uuid
	JOIN scans s on s.scan_id = e.transfer_id
	JOIN purchases p on p.scan_id = s.scan_id
	JOIN users u on s.origin_id = u.user_id
	JOIN clinics c on c.id = u.clinic_id
	JOIN organizations o on o.id = c.organization_id
	WHERE DATE(p.purchased) = DATE(?) AND eo.exam_uuid IS NULL AND e.activated`, date)
	if err != nil {
		log.Fatalf("could not fetch exams for %v, %v", date, err)
	}
	defer func() {
		if err := rows.Close(); err != nil {
			log.Printf("error closing sql rows: %v", err)
		}
	}()

	//map exams to bambora ids
	ebmap := map[string]string{}
	//map bambora ids to region
	brmap := map[string]string{}

	for rows.Next() {
		var uuid, bamboraId, region string
		err = rows.Scan(&uuid, &bamboraId, &region)
		if err != nil {
			log.Printf("could not scan exam row %v", err)
			continue
		}
		if len(bamboraId) < 8 || bamboraId[0:8] != "bambora_" {
			continue
		}
		//parse out actual bambora id
		bId := bamboraId[8:]
		ebmap[uuid] = bId
		brmap[bId] = region
	}
	log.Printf("%d exams", len(ebmap))
	return ebmap, brmap
}

func getOrders(pmtsdb *sql.DB, brmap map[string]string) map[string]string {
	//map bambora id to order id
	bomap := map[string]string{}
	log.Printf("looking up %d bambora ids", len(brmap))
	//iterate thru bambora ids.
	var connectionCount = 15
	connPool := make(chan bool, connectionCount)
	var swg sync.WaitGroup

	var lock = sync.RWMutex{}
	for b, r := range brmap {
		swg.Add(1)
		go func(bid string, region string) {
			connPool <- true
			var oId string
			err := pmtsdb.QueryRow(`SELECT order_id FROM transactions
		WHERE provider_txn_id = ? AND country = ?`, bid, region).Scan(&oId)
			if err != nil {
				log.Printf("could not find order for id %s %s", bid, region)
				swg.Done()
				<-connPool
				return
			}
			lock.Lock()
			bomap[bid] = oId
			lock.Unlock()
			swg.Done()
			<-connPool
		}(b, r)
	}

	swg.Wait()
	return bomap

}
