package load

import (
	"encoding/csv"
	"fmt"
	"os"
)

type Profile struct {
	PatientId string
	Ohip	  string
	OhipVc    string
	AltHId	  string
	Ssn       string
	Ipn       string
}

func openAndLoadCSVFile(filename string) ([][]string, error) {
	openFile, err := os.Open(filename) // #nosec G304
	if err != nil {
		return nil, err
	}

	defer func() {
		err = openFile.Close()
		if err != nil {
			fmt.Println("error closing file:", err)
		}
	}()

	reader := csv.NewReader(openFile)

	return reader.ReadAll()
}

func LoadProfiles(filename string) ([]Profile, error){
	csv, err := openAndLoadCSVFile(filename)
	if err != nil {
		return nil, err
	}

	// skip first row because first row is column names
	csv = csv[1:]

	var data []Profile

	for _, col := range csv {
		c := Profile{
			PatientId: col[0],
			Ohip: col[1],
			OhipVc: col[2],
			AltHId: col[3],
			Ssn: col[4],
			Ipn: col[5],
		}

		data = append(data, c)
	}

	return data, nil
}