package export

import (
	"encoding/csv"
	"log"
	"os"
	"path/filepath"

	"migrate_phi_profiles/pkg/transform"
)

func exportTsvData(data [][]string, filename string) error {
	file, err := os.Create(filepath.Clean(filename))
	if err != nil {
		return err
	}

	defer func() {
		if err := file.Close(); err != nil { 
			 log.Printf("Error closing file: %s\n", err)
		}
	}()

	writer := csv.NewWriter(file)
	writer.Comma = '\t'
	defer writer.Flush()

	for _, value := range data {
		err := writer.Write(value)
		if err != nil {
			return err
		}
	}

	return nil
}

func ExportPHIProfilesToTsv(phiProfiles []transform.PhiProfile, filename string) error {
	data := make([][]string, 0)
	for _, p := range phiProfiles {
		data = append(data, []string{
			p.Id,
			p.<PERSON>d,
			p.PhiType.String(),
			p.<PERSON>al<PERSON>,
		})
	}

	return exportTsvData(data, filename)
}
