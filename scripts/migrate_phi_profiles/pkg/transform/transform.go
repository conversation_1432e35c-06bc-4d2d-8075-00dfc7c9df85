package transform

import (
	"github.com/segmentio/ksuid"
	"migrate_phi_profiles/pkg/load"
)

type PhiType string
// List of PhiTypes
const (
	OHIP     PhiType = "OHIP"
	OHIP_VC  PhiType = "OHIP_VC"
	SSN      PhiType = "SSN"
	IPN      PhiType = "IPN"
	ALT_H_ID PhiType = "ALT_H_ID"
)


type PhiProfile struct {
	Id 			string
	PatientId	string
	PhiType		PhiType
	PhiValue	string
}

func (t PhiType) String() string {
	return string(t)
}

func TransformPhiProfie(profiles []load.Profile) ([]PhiProfile) {
	phiProfiles := make([]PhiProfile, 0)
	for _, p := range profiles {
		if p.Ohip != "" {
			phiProfile := PhiProfile{
				Id:        ksuid.New().String(),
				PatientId: p.PatientId,
				PhiType:   OHIP,
				PhiValue:  p.Ohip,
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
		if p.OhipVc != "" {
			phiProfile := PhiProfile{
				Id:        ksuid.New().String(),
				PatientId: p.PatientId,
				PhiType:   OHIP_VC,
				PhiValue:  p.OhipVc,
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
	
		if p.Ssn != "" {
			phiProfile := PhiProfile{
				Id:        ksuid.New().String(),
				PatientId: p.PatientId,
				PhiType:   SSN,
				PhiValue:  p.Ssn,
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
		if p.Ipn != "" {
			phiProfile := PhiProfile{
				Id:        ksuid.New().String(),
				PatientId: p.PatientId,
				PhiType:   IPN,
				PhiValue:  p.Ipn,
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
		if p.AltHId != "" {
			phiProfile := PhiProfile{
				Id:        ksuid.New().String(),
				PatientId: p.PatientId,
				PhiType:   ALT_H_ID,
				PhiValue:  p.AltHId,
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
	}
	return phiProfiles
}