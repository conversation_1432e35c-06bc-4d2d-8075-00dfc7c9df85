package main

import (
	"database/sql"
	"fmt"
	"io"
	"os"
	"sync"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/joho/sqltocsv"
	"github.com/sirupsen/logrus"
	"migrate_phi_profiles/pkg/export"
	"migrate_phi_profiles/pkg/load"
	"migrate_phi_profiles/pkg/transform"
)

var region string

func main(){
	t0 := time.Now()
	args := os.Args[1:]

	// check args / print usage
	if len(args) < 2 {
		fmt.Fprintf(
			os.Stderr,
			"usage: %s <region>(CA or US) <phsql_connstring>",
			os.Args[0],
		)
		return
	}
	region = args[0]

	phConnStr := args[1] 
	//connect to db
	db, err := sql.Open(
		"mysql",
		phConnStr,
	)
	if err != nil {
		logrus.Fatalf("unable to perform sql.Open pockethealth: %v", err)
	}

	err = db.Ping()
	if err != nil {
		logrus.Fatalf("unable to ping pockethealth db, %q", err)
	}

	logrus.Info("fetching profiles")
	// fetch + load profiles for mapping
	// profile created by script doesnt have any phi
	profilesRow, err := db.Query(
		`SELECT patient_id, COALESCE(ohip,''), COALESCE(ohip_vc,''), COALESCE(alt_h_id,''), COALESCE(ssn,''), COALESCE(ipn,'') 
		FROM profiles 
		WHERE patient_id IS NOT NULL
		and (ohip is not null or 
			ohip_vc is not null or
			ipn is not null or 
			ssn is not null or 
			alt_h_id is not null)`)
	if err != nil {
		logrus.Fatalf("failed to fetch profiles: %v", err)
	}
	csvConverter := sqltocsv.New(profilesRow)
	err = csvConverter.WriteFile(fmt.Sprintf("profiles_%s.csv", region))
	if err != nil {
		logrus.Fatalf("failed to write to profiles csv: %v", err)
	}

	profiles, err := load.LoadProfiles(fmt.Sprintf("profiles_%s.csv", region))
	if err != nil {
		logrus.Fatalf("failed to load profiles: %v", err)
	} 

	batchSize := 1000

	var connectionCount = 8
	connPool := make(chan bool, connectionCount)
	var swg sync.WaitGroup
	var lock = sync.RWMutex{}

	phiProfiles := []transform.PhiProfile{}
	for lower := 0; lower < len(profiles); lower += batchSize {
		swg.Add(1)
		upper := lower + batchSize + 1
		if len(profiles) < upper {
			upper = len(profiles)
		}
		logrus.Infof("processing %d-%d", lower, upper-1)
		go func (ps []load.Profile){
			connPool <- true
			pp := transform.TransformPhiProfie(ps)

			lock.Lock()
			phiProfiles = append(phiProfiles, pp...)
			lock.Unlock()

			swg.Done()
			<-connPool
		} (profiles[lower:upper])
	}
	swg.Wait()

	err = export.ExportPHIProfilesToTsv(phiProfiles, fmt.Sprintf("phi_profiles_%s.tsv", region))
	if err != nil {
		logrus.Fatalf("failed to export phi profiles to tsv: %v", err)
	}

	logrus.Debug("finished generating phi profiles and exported to tsv, loading to phi_profiles table")
	// read phi profile tsv
	mysql.RegisterReaderHandler("data", func() io.Reader {
		var tsvReader io.Reader
		tsvReader, err := os.Open(fmt.Sprintf("phi_profiles_%s.tsv", region))
		if err != nil {
			logrus.Fatalf("failed to open %s: %v", fmt.Sprintf("phi_profiles_%s.tsv", region), err)
		}
		return tsvReader
	})

	// load patients table
	_, err = db.Exec(
		`LOAD DATA LOCAL INFILE 'Reader::data' INTO TABLE phi_profiles
		FIELDS TERMINATED BY '\t'
		OPTIONALLY ENCLOSED BY '"'
		LINES TERMINATED BY '\n'	  
		(id, patient_id, phi_type, phi_value)`)
	if err != nil {
		logrus.Fatalf("failed to load data into phi_profiles table: %v", err)
	}

	mysql.DeregisterReaderHandler("data")

	logrus.New().WithFields(logrus.Fields{
		"took": time.Since(t0),
	}).Info("finish script")
}