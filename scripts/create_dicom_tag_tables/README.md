# Create DICOM Tag Table Scripts

## extract_dicom_tags

This script takes a file path of DICOM conformance statement PDFs and extracts all
DICOM tag hex codes that are referenced in each document. It then compiles a
CSV that shows the tags and counts of how many times that hex code was
referenced. This provides a high level overview of how often DICOM tags are
used among different PACS vendors.

### External dependencies

- [pdfgrep](https://pdfgrep.org/)

### Usage

```sh
go run cmd/extract_dicom_tags/main.go -path "~/Downloads/dicom-conformance-statements/*.pdf" -out "<optional-output-csv-file-path>"
```

## generate_dicom_tag_tables

After the output CSV from `extract_dicom_tags` is curated, this script takes
the resulting CSV and generates a MySQL database migration to create a `dicoms`
table to store these DICOM tags as JSON along with specific columns as
specified by the CSV.

### Usage


For initial creation script

```sh
go run cmd/generate_dicom_tag_tables/main.go -csv dicom-tags.csv -outdir ../../migrations -create
```

For follow up rename script

```sh
go run cmd/generate_dicom_tag_tables/main.go -csv dicom-tags.csv -outdir ../../migrations -rename
```
