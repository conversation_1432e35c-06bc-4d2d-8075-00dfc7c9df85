package main

import (
	"encoding/csv"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strconv"
	"strings"

	"github.com/suyashkumar/dicom/pkg/tag"
)

const (
	defaultOutputCSVFilePath = "dicom-tag-overview.csv" // default output CSV file path
)

var (
	tagInfoMap   = map[string]*tag.Info{}
	tagMapCount  = map[string]int{}
	tagMapByPACS = map[string]map[string]bool{}
)

// This script takes a file path of DICOM conformance statement PDFs and extracts
// all DICOM tag hex codes that are referenced in each document. It then
// compiles a CSV that shows the tags and counts of how many times that hex
// code was referenced. This provides a high level overview of how often DICOM
// tags are used among different PACS vendors.
//
// External dependencies:
// - pdfgrep
//
// Usage:
// ```
// go run cmd/extract_dicom_tags/main.go -path "~/Downloads/dicom-conformance-statements/*.pdf" -out "<optional-output-csv-file-path>"
// ```
func main() {
	filePattern := flag.String("path", "", "File path with optional glob pattern (e.g., './*.txt')")
	outPath := flag.String("out", "", "Output CSV file path")
	flag.Parse()

	outputCSVFilePath := defaultOutputCSVFilePath
	if len(*outPath) > 0 {
		outputCSVFilePath = *outPath
	}

	// Parse file path (with possible glob pattern) from command-line arguments.
	files := getPDFFileNames(filePattern)
	log.Printf("Using pdfgrep to find dicom tags in files \n%s", files)

	// Iterate through files to extract tags with pdfgrep
	for _, filename := range files {
		// run pdfgrep to get list of tags
		cmd := fmt.Sprintf(
			`pdfgrep -oP "[0-9A-Fa-f]{4},[0-9A-Fa-f]{4}" %s | tr '[:lower:]' '[:upper:]' | sort | uniq`,
			filename,
		)
		out, err := exec.Command("bash", "-c", cmd).Output()
		if err != nil {
			log.Fatalf("Could not run pdfgrep: %s", err)
		}

		// loop through list of tags
		outTrim := strings.Trim(string(out), "\n")
		tagList := strings.Split(outTrim, "\n")
		for _, t := range tagList {
			// parse out group and element
			tagTrim := strings.Trim(t, "()")
			tagSplit := strings.Split(tagTrim, ",")
			if len(tagSplit) == 2 {
				group, element := tagSplit[0], tagSplit[1]

				// get tag info
				tagInfo, err := getTagInfo(group, element)
				if err == nil {
					tagInfoMap[tagInfo.Tag.String()] = tagInfo
					tagMapCount[tagInfo.Tag.String()]++

					// add to tag map
					if tagMapByPACS[filename] == nil {
						tagMapByPACS[filename] = map[string]bool{}
					}
					tagMapByPACS[filename][tagInfo.Tag.String()] = true
				}
			} else {
				log.Fatalf("tag malformed: %s", tagSplit)
			}
		}

	}

	// sort tagMapCount by count
	keys := make([]string, 0, len(tagMapCount))
	for key := range tagMapCount {
		keys = append(keys, key)
	}
	sort.SliceStable(keys, func(i, j int) bool {
		return tagMapCount[keys[i]] > tagMapCount[keys[j]]
	})

	// output csv from tag map
	log.Printf("Output DICOM tag CSV to %s", outputCSVFilePath)
	file, err := os.Create(outputCSVFilePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	writer := csv.NewWriter(file)
	defer writer.Flush()

	header := []string{"tag", "VR", "VM", "tag name", "count"}
	for _, filename := range files {
		header = append(header, filename)
	}
	writer.Write(header)

	// fill rows of CSV with gathered tag information
	for _, key := range keys {
		count := tagMapCount[key]
		countStr := strconv.Itoa(count)
		tagInfo := tagInfoMap[key]
		row := []string{fmt.Sprintf("%q", key), tagInfo.VR, tagInfo.VM, tagInfo.Name, countStr}
		for _, filename := range files {
			exist := tagMapByPACS[filename][key]
			if exist {
				row = append(row, "Y")
			} else {
				row = append(row, "")
			}
		}
		writer.Write(row)
	}
}

// getPDFFileNames takes a filePattern and expands it as a glob pattern to
// return a list of PDF files
func getPDFFileNames(filePattern *string) []string {
	if *filePattern == "" {
		log.Fatalf("Error: File path is required.")
	}

	path := *filePattern
	if strings.HasPrefix(*filePattern, "~/") {
		dirname, err := os.UserHomeDir()
		if err != nil {
		}
		path = filepath.Join(dirname, path[2:])
	}

	// Resolve the glob pattern to matching files.
	files, err := filepath.Glob(path)
	if err != nil {
		log.Fatalf("Error: Invalid file pattern: %v", err)
	}

	if len(files) == 0 {
		log.Fatalf("No files matched the pattern: %s\n", *filePattern)
	}
	return files
}

// getTagInfo returns tag info from a DICOM group and element code
func getTagInfo(group, element string) (*tag.Info, error) {
	groupUint, err := strconv.ParseUint(group, 16, 16)
	if err != nil {
		return nil, err
	}
	elementUint, err := strconv.ParseUint(element, 16, 16)
	if err != nil {
		return nil, err
	}
	t := tag.Tag{Group: uint16(groupUint), Element: uint16(elementUint)}
	info, err := tag.Find(t)
	if err != nil {
		return nil, err
	}
	return &info, err
}
