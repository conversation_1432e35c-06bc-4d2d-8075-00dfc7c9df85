package main

import (
	"bytes"
	"encoding/csv"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"time"

	"github.com/jszwec/csvutil"
	"gitlab.com/pockethealth/phutils/v10/pkg/dicomtag"
)

const (
	dicomsTableName = "dicoms"
)

// DICOMTag represents a row from the input CSV of tags
type DICOMTag struct {
	TagHexCode          string            `csv:"tag"`
	ValueRepresentation string            `csv:"VR"`
	ValueMultiplicity   string            `csv:"VM"`
	TagName             string            `csv:"tag name"`
	Count               string            `csv:"count"`
	OtherData           map[string]string `csv:"-"`
}

func (dt DICOMTag) HumanReadableName() (string, error) {
	tagInfo, err := dicomtag.FindByHexString(strings.Trim(dt.TagHexCode, `"`))
	if err != nil {
		return "", err
	}
	return tagInfo.Keyword, nil
}

func (dt DICOMTag) GetNotNull() string {
	switch dt.TagName {
	case "StudyInstanceUID", "SeriesInstanceUID", "SOPInstanceUID":
		return "NOT NULL"
	}
	return "NULL"
}

// GetMySQLType converts a DICOM value representation and value multiplicity to a SQL type.
func (dt DICOMTag) GetMySQLType() string {
	// If value multiplicity is not 1 then store as JSON (array or object)
	if dt.ValueMultiplicity != "1" {
		return "JSON"
	}

	// For any tag with 1 value only, store with SQL type
	switch dt.ValueRepresentation {
	case "AE":
		return "VARCHAR(16)"
	case "AS":
		return "CHAR(4)"
	case "AT":
		return "CHAR(4)"
	case "CS":
		return "VARCHAR(16)"
	case "DA":
		return "CHAR(8)"
	case "DS":
		return "VARCHAR(16)"
	case "DT":
		return "DATETIME"
	case "FL":
		return "FLOAT"
	case "FD":
		return "DOUBLE"
	case "IS":
		return "INT"
	case "LO":
		return "VARCHAR(64)"
	case "LT":
		return "TEXT"
	case "OB":
		return "BLOB"
	case "OF":
		return "BLOB"
	case "OW":
		return "BLOB"
	case "PN":
		return "VARCHAR(64)"
	case "SH":
		return "VARCHAR(16)"
	case "SL":
		return "INT"
	case "SQ":
		return "TEXT"
	case "SS":
		return "SMALLINT"
	case "ST":
		return "TEXT"
	case "TM":
		return "TIME"
	case "UI":
		return "VARCHAR(64)"
	case "UL":
		return "INT"
	case "UN":
		return "BLOB"
	case "US":
		return "SMALLINT"
	case "UT":
		return "LONGTEXT"
	default:
		log.Printf("Value Representation %q not found", dt.ValueRepresentation)
	}

	// store JSON as fallback for unknown types
	return "JSON"
}

// GetDefaultValue returns a sensible default value based on the value representation (VR) of the tag.
func (dt DICOMTag) GetDefaultValue() string {
	switch dt.ValueRepresentation {
	case "AE", "AS", "CS", "LO", "PN", "SH", "ST", "UI", "UT":
		return `""` // Empty string for these types
	case "DA", "DT", "TM":
		return `"0000-00-00"` // Default date/time value (MySQL supports this format)
	case "DS", "FL", "FD":
		return "0.0" // Default float/double value
	case "IS", "SL", "SS", "UL", "US":
		return "0" // Default integer value
	case "OB", "OF", "OW", "UN":
		return `"[]"` // Default for binary/blob-like data (empty JSON array)
	case "SQ":
		return `"[]"` // Default for sequences (empty JSON array)
	default:
		// Unknown or unsupported types, return empty string by default
		return `NULL`
	}
}

type MigrationData struct {
	DICOMTags []DICOMTag
	TableName string
}

// After the output CSV from `extract_dicom_tags` is curated, this script takes
// the resulting CSV and generates a MySQL database migration to create a `dicoms`
// table to store these DICOM tags as JSON along with specific columns as
// specified by the CSV.
func main() {
	filePattern := flag.String("path", "", "DICOM tag CSV file path')")
	outDir := flag.String("outdir", "", "Output directory for sql migration file")
	create := flag.Bool("create", false, "Create dicoms table")
	rename := flag.Bool("rename", false, "Rename existing dicoms table")
	flag.Parse()

	// Open CSV file
	filePath := getCSVFileName(filePattern)
	f, err := os.Open(filePath)
	if err != nil {
		log.Fatal("Unable to read input file "+filePath, err)
	}
	defer f.Close()

	// Read CSV
	log.Printf("Reading CSV of DICOM tags from %s", filePath)
	csvReader := csv.NewReader(f)
	dec, err := csvutil.NewDecoder(csvReader)
	if err != nil {
		log.Fatal(err)
	}

	// Unmarshal CSV to DICOMTag structs
	dicomTags := []DICOMTag{}
	for {
		dicomTag := DICOMTag{}
		if err := dec.Decode(&dicomTag); err == io.EOF {
			break
		} else if err != nil {
			log.Fatal(err)
		}
		dicomTags = append(dicomTags, dicomTag)
	}

	// Build MySQL migration from DICOM tags
	var mySQLMigration bytes.Buffer
	var outFileName string
	if *create {
		log.Printf("Building MySQL migration to create %s table", dicomsTableName)

		// define template
		templ := `-- This SQL migration was generated by the generate_dicom_tag_tables script
-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS ` + "`{{ .TableName }}`" + ` (
` + "`id`" + ` CHAR(27) NOT NULL UNIQUE PRIMARY KEY COMMENT "ksuid",
` + "`provider_id`" + ` BIGINT NOT NULL COMMENT "legacy provider ID, also known as org_id",
` + "`tags`" + ` JSON NOT NULL COMMENT "human readable dicom json tags",
{{ range $dt := .DICOMTags -}}
` + "`{{ $dt.TagName }}`" + ` {{ $dt.GetMySQLType }} GENERATED ALWAYS AS (COALESCE(tags->>'$.{{ $dt.TagName }}', {{ $dt.GetDefaultValue }})) STORED {{ $dt.GetNotNull }} COMMENT {{ $dt.TagHexCode }},
{{ end -}}
INDEX idx_provider_study (provider_id, StudyInstanceUID),
INDEX idx_provider_series (provider_id, SeriesInstanceUID),
UNIQUE INDEX idx_provider_study_instance (provider_id, StudyInstanceUID, SOPInstanceUID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS ` + "`{{ .TableName }}`" + `;
-- +goose StatementEnd`

		// Set up template data and execute
		migrationData := MigrationData{
			DICOMTags: dicomTags,
			TableName: dicomsTableName,
		}
		t := template.Must(template.New("create").Parse(templ))
		err := t.Execute(&mySQLMigration, migrationData)
		if err != nil {
			log.Fatalf("failed to template migration: %s", err.Error())
		}

		// Set SQL migration output filename
		outFileName = fmt.Sprintf(
			"%s_create_%s_table.sql",
			time.Now().Format("20060102150405"),
			dicomsTableName,
		)
	} else if *rename {
		log.Printf("Building MySQL migration to rename columns in %s table", dicomsTableName)

		// define template
		templ := `-- This SQL migration was generated by the generate_dicom_tag_tables script
-- +goose Up
-- +goose StatementBegin
ALTER TABLE ` + "`{{ .TableName }}`" + `
MODIFY COLUMN ` + "`id`" + ` VARBINARY(27),
{{ range $index, $dt := .DICOMTags }}{{ if $index -}},
{{ end -}}
CHANGE COLUMN` + "`{{ $dt.TagName }}` `{{ $dt.HumanReadableName }}`" + ` {{ $dt.GetMySQLType }} GENERATED ALWAYS AS (COALESCE(tags->>'$.{{ $dt.HumanReadableName }}', {{ $dt.GetDefaultValue }})) STORED {{ $dt.GetNotNull }} COMMENT {{ $dt.TagHexCode }}
{{- end }};
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE ` + "`{{ .TableName }}`" + `
{{ range $index, $dt := .DICOMTags }}{{ if $index -}},
{{ end -}}
CHANGE COLUMN` + "`{{ $dt.HumanReadableName }}` `{{ $dt.TagName }}`" + ` {{ $dt.GetMySQLType }} GENERATED ALWAYS AS (tags->>'$.{{ $dt.TagName }}') STORED {{ $dt.GetNotNull }} COMMENT {{ $dt.TagHexCode }}
{{- end }};
-- +goose StatementEnd`

		// Set up template data and execute
		migrationData := MigrationData{
			DICOMTags: dicomTags,
			TableName: dicomsTableName,
		}
		t := template.Must(template.New("rename").Parse(templ))
		err := t.Execute(&mySQLMigration, migrationData)
		if err != nil {
			log.Fatalf("failed to template migration: %s", err.Error())
		}

		// Set SQL migration output filename
		outFileName = fmt.Sprintf(
			"%s_update_%s_table.sql",
			time.Now().Format("20060102150405"),
			dicomsTableName,
		)
	} else {
		log.Fatalf("Use -create to create a new migration or -rename to rename the existing migration")
	}

	// Output SQL migration to file
	outPath := outFileName
	if len(*outDir) > 0 {
		outPath = filepath.Join(*outDir, outFileName)
	}
	log.Printf("Output MySQL migration to %s", outPath)
	err = os.WriteFile(outPath, mySQLMigration.Bytes(), 0644)
	if err != nil {
		log.Fatalf(err.Error())
	}
}

// getCSVFileName retrieves the filename from a filePattern
func getCSVFileName(filePattern *string) string {
	if *filePattern == "" {
		log.Fatalf("Error: File path is required.")
	}

	filePath := *filePattern
	if strings.HasPrefix(*filePattern, "~/") {
		dirname, err := os.UserHomeDir()
		if err != nil {
		}
		filePath = filepath.Join(dirname, filePath[2:])
	}
	return filePath
}
