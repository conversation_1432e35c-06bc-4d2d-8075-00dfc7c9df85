package main

import (
	"database/sql"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"map_patients/pkg/export"
	"map_patients/pkg/extract"
	"map_patients/pkg/load"
	"map_patients/pkg/models"
	"map_patients/pkg/transform"

	"github.com/go-sql-driver/mysql"
)

var region string

func main() {
	t0 := time.Now()
	args := os.Args[1:]
	log.SetFormatter(&log.JSONFormatter{})
	// check args / print usage
	if len(args) < 3 {
		log.Fatalf(
			"usage: %s <region>(CA or US) <phsql_connstring> <acctsql_connstring>",
			os.Args[0],
		)
		return
	}
	region = args[0]
	phConnStr := args[1]
	acctConnStr := args[2]
	//connect to db
	db, err := sql.Open(
		"mysql",
		phConnStr,
	)
	if err != nil {
		log.Fatalf("unable to perform sql.Open pockethealth: %v", err)
	}

	err = db.Ping()
	if err != nil {
		log.Fatalf("unable to ping pockethealth db, %q", err)
	}

	acctDb, err := sql.Open(
		"mysql",
		acctConnStr,
	)
	if err != nil {
		log.Fatalf("unable to perform sql.Open accounts: %v", err)
	}

	err = acctDb.Ping()
	if err != nil {
		log.Fatalf("unable to ping accounts db, %q", err)
	}

	filename := fmt.Sprintf("accounts_users_mapping_%s.tsv", region)
	aums, err := load.LoadAccountUserIDMapping(filename)
	if err != nil {
		log.Fatalf("unable to load account userId Mapping in %s, %q", region, err)
	}

	batchSize := 5000

	for lower := 0; lower < len(aums); lower += batchSize {
		upper := lower + batchSize + 1
		if len(aums) < upper {
			upper = len(aums)
		}
		log.Infof("processing %d-%d", lower, upper-1)

		examFName := fmt.Sprintf("exams_mapping_%s_%d_%d.tsv", region, lower, upper)
		rFName := fmt.Sprintf("requests_mapping_%s_%d_%d.tsv", region, lower, upper)
		eFName := fmt.Sprintf("enrolments_mapping_%s_%d_%d.tsv", region, lower, upper)
		cFName := fmt.Sprintf("consents_mapping_%s_%d_%d.tsv", region, lower, upper)
		filenames := []string{examFName, rFName, eFName, cFName}

		examf, examWriter, err := export.OpenExportTsvFile(examFName)
		if err != nil {
			log.Fatalf("failed to open exam tsv file: %v", err)
		}
		defer examf.Close()
		reqf, reqWriter, err := export.OpenExportTsvFile(rFName)
		if err != nil {
			log.Fatalf("failed to open request tsv file: %v", err)
		}
		defer reqf.Close()
		consentf, consentWriter, err := export.OpenExportTsvFile(cFName)
		if err != nil {
			log.Fatalf("failed to open consent tsv file: %v", err)
		}
		defer consentf.Close()
		enrolf, enrolWriter, err := export.OpenExportTsvFile(eFName)
		if err != nil {
			log.Fatalf("failed to open enrolment tsv file: %v", err)
		}
		defer enrolf.Close()

		exams, reqs, enrols, consents := mapping(db, acctDb, aums[lower:upper])
		err1 := export.ExportExamsToTsv(exams, examWriter)
		if err1 != nil {
			log.Errorf("failed to export to exam tsv file: %v", err)
		}
		export.ExportRequestsToTsv(reqs, reqWriter)
		if err1 != nil {
			log.Errorf("failed to export to request tsv file: %v", err)
		}
		export.ExportEnrolmentsToTsv(enrols, enrolWriter)
		if err1 != nil {
			log.Errorf("failed to export to enrolment tsv file: %v", err)
		}
		export.ExportConsentsToTsv(consents, consentWriter)
		if err1 != nil {
			log.Errorf("failed to export to consents tsv file: %v", err)
		}
		for _, filename := range filenames {
			var tableName, idString, ttColName, colName string
			if strings.HasPrefix(filename, "exams") {
				tableName = "exams"
				ttColName = "uuid"
				colName = "uuid"
				idString = "uuid varchar(100)"
			} else if strings.HasPrefix(filename, "requests") {
				tableName = "requests"
				ttColName = "request_id"
				colName = "id"
				idString = "request_id bigint(20)"
			} else if strings.HasPrefix(filename, "enrolments") {
				tableName = "enrollments"
				ttColName = "enrolment_id"
				colName = "id"
				idString = "enrolment_id bigint(20)"
			} else if strings.HasPrefix(filename, "consents") {
				tableName = "consents"
				ttColName = "consent_id"
				colName = "id"
				idString = "consent_id varchar(255)"
			}
			query := fmt.Sprintf(`create temporary table %s_map (
				account_id char(28),
				patient_id char(28),
				%s
			)`, tableName, idString)
			// create mapping temp table
			_, err = db.Exec(query)
			if err != nil {
				log.Fatalf("failed to create %s map temp table: %v", tableName, err)
			}

			// populate table with csv
			// use mysql readerhandler
			startLoadingTemp := time.Now()
			mysql.RegisterReaderHandler("data", func() io.Reader {
				var tsvReader io.Reader
				tsvReader, err := os.Open(filepath.Clean(filename))
				if err != nil {
					log.Fatalf("failed to open %s: %v", filename, err)
				}
				return tsvReader
			})
			_, err = db.Exec(
				fmt.Sprintf(`LOAD DATA LOCAL INFILE 'Reader::data' INTO TABLE %s_map 
				FIELDS TERMINATED BY '\t'
				OPTIONALLY ENCLOSED BY '"'
				LINES TERMINATED BY '\n'
				(account_id, patient_id, %s)`,
					tableName, ttColName))
			if err != nil {
				log.Fatalf("failed to load data into temp table: %v", err)
			}
			log.WithFields(log.Fields{
				"took": time.Since(startLoadingTemp),
			}).Infof("finished loading into %s temp table", tableName)

			// update join
			startUpdateJoin := time.Now()
			for i := 0; i < 3; i++ {
				_, err := db.Exec(fmt.Sprintf(`update %s ut
			inner join %s_map tt on ut.%s = tt.%s
			set ut.account_id = tt.account_id, ut.patient_id = tt.patient_id
			`, tableName, tableName, colName, ttColName))
				if err != nil {
					l := fmt.Sprintf("failed to update join try :%d on table %s: %v", i, tableName, err)

					if i == 2 {
						log.Fatal(l)
					} else {
						log.Error(l)
					}
					time.Sleep(time.Second)
				} else {
					break
				}
			}

			log.WithFields(log.Fields{
				"took": time.Since(startUpdateJoin),
			}).Infof("finished update join on %s table", tableName)

			// drop temp table
			_, err = db.Exec(fmt.Sprintf("drop temporary table if exists %s_map", tableName))
			if err != nil {
				log.Fatalf("failed to drop temp table %s_map: %v", tableName, err)
			}
		}
	}

	log.WithFields(log.Fields{
		"took": time.Since(t0),
	}).Info("finished script")
}

func mapping(db *sql.DB, acctDb *sql.DB, aums []models.Account) ([]models.Exam, []models.Request, []models.Enrol, []models.Consent) {
	allExams := make([]models.Exam, 0)
	allRequests := make([]models.Request, 0)
	allEnrolments := make([]models.Enrol, 0)
	allConsents := make([]models.Consent, 0)

	var connectionCount = 64
	connPool := make(chan bool, connectionCount)
	var swg sync.WaitGroup
	var exlock = sync.RWMutex{}
	var reqlock = sync.RWMutex{}
	var enrollock = sync.RWMutex{}
	var conlock = sync.RWMutex{}

	for i := 0; i < len(aums); i++ {
		swg.Add(1)
		go func(aum models.Account) {
			connPool <- true
			exams := extract.GetExams(db, aum.UserId)
			consents := extract.GetConsents(db, aum.UserId, aum.Email)
			enrols := extract.GetEnrolments(db, aum.UserId, aum.Email)
			reqs := extract.GetRequests(db, aum.UserId, aum.Email)
			patients := extract.GetPatients(acctDb, aum.AccountId)

			_ = transform.MapExams(patients, exams, aum.AccountId)
			transform.MapRequests(patients, reqs, aum.AccountId)
			transform.MapEnrolments(patients, enrols, reqs, exams, aum.AccountId)
			transform.MapConsents(patients, consents, enrols, aum.AccountId)

			exlock.Lock()
			allExams = append(allExams, exams...)
			exlock.Unlock()
			reqlock.Lock()
			allRequests = append(allRequests, reqs...)
			reqlock.Unlock()
			enrollock.Lock()
			allEnrolments = append(allEnrolments, enrols...)
			enrollock.Unlock()
			conlock.Lock()
			allConsents = append(allConsents, consents...)
			conlock.Unlock()

			swg.Done()
			<-connPool
		}(aums[i])
	}
	swg.Wait()
	return allExams, allRequests, allEnrolments, allConsents
}
