#!/bin/bash

# Extracts the table into a TSV file to later be processed
# to match user_id in each each with exams/requests/enrollments/consents from each and backfill 
# Result will be in accounts_users_mapping_CA.tsv and accounts_users_mapping_US.tsv
#
# Ex: SQLHOST=************* SQLUSER=pockethealth-web SQLPW='hZ1NSfeCkgG1!' ./fetch_account_user_mapping.sh
#
# accounts.tsv will be lines containing:  <account_id>\t<email>\t<user_id>\n

if [[ -z "$SQLHOST" ]] || [[ -z "$SQLUSER" ]] || [[ -z "$SQLPW" ]]; then
    echo "Variables SQLHOST, SQLUSER, and SQLPW must be set"
    exit
fi

# CA
rm -f accounts_users_mapping_CA.tsv
touch accounts_users_mapping_CA.tsv

mysql --database accounts -N -B \
-u "$SQLUSER" \
--host "$SQLHOST" \
-p"$SQLPW" \
-e "select id, email, user_id
    from 
    (select id, email, main_region_user_id as user_id
    from accounts.accounts where main_region_id = 1) as a
    UNION 
    (select ao.id, ac.email, user_id
    from accounts.account_other_regions ao JOIN accounts.accounts ac ON ac.id = ao.id
    where region = 1);" > accounts_users_mapping_CA.tsv

# US
rm -f accounts_users_mapping_US.tsv
touch accounts_users_mapping_US.tsv

mysql --database accounts -N -B \
-u "$SQLUSER" \
--host "$SQLHOST" \
-p"$SQLPW" \
-e "select id, email, user_id
    from 
    (select id, email, main_region_user_id as user_id
    from accounts.accounts where main_region_id = 2) as a
    UNION 
    (select ao.id, ac.email, user_id
    from accounts.account_other_regions ao JOIN accounts.accounts ac ON ac.id = ao.id
    where region = 2);" > accounts_users_mapping_US.tsv
