# map patients
This script is part of the profiles -> patients migration script. This script tries to map exams, requests, enrolments and consents to a patient.

>note: this should be ran once in each region

## Usage
---
1. Run the fetch_account_user_mapping.sh, setting env variables for SQL connection to accounts db; e.g. 
   
   ```
    SQLHOST=************* SQLUSER=pockethealth-web SQLPW='hZ1NSfeCkgG1!' ./fetch_account_user_mapping.sh
   ``` 
    This will create the files: `accounts_users_mapping_CA.tsv` and `accounts_users_mapping_US.tsv`, TSVs file that can be used in the mapping script
1. To run the script
    ```
    go build
    ./map_patient <region> (CA or US) <ph_db_conn_str> <acct_db_conn_str>
    ```
    with Region CA will be using `accounts_users_mapping_CA.tsv`, with Region US `accounts_users_mapping_US.tsv`.