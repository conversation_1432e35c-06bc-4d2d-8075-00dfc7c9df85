package load

import (
	"encoding/csv"
	"fmt"
	"os"
	"strconv"

	"map_patients/pkg/models"
)

// inspired by https://www.socketloop.com/tutorials/golang-read-tab-delimited-file-with-encoding-csv-package
func openAndLoadTSVFile(filename string) ([][]string, error) {
	openFile, err := os.Open(filename) // #nosec G304
	if err != nil {
		return nil, err
	}

	defer func() {
		err = openFile.Close()
		if err != nil {
			fmt.Println("error closing file:", err)
		}
	}()

	reader := csv.NewReader(openFile)

	reader.Comma = '\t' // Use tab-delimited instead of comma

	reader.FieldsPerRecord = -1

	return reader.ReadAll()
}

func LoadAccountUserIDMapping(filename string) ([]models.Account, error) {
	tsv, err := openAndLoadTSVFile(filename)
	if err != nil {
		return nil, err
	}

	var accounts []models.Account

	for _, col := range tsv {
		if col[2] != "NULL"{
			var userId int64
			userId, err = strconv.ParseInt(col[2], 10, 64)
			if err != nil {
				fmt.Printf(
					"unable to parse main region acct user_id: %s, err: %q\n",
					col[2],
					err.Error(),
				)
				return nil, err
			}

			accounts = append(accounts, models.Account{
				AccountId:  col[0],
				UserId:  	userId,
				Email:		col[1],
			})
		}
	}

	return accounts, nil

}
