package extract

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	"map_patients/pkg/models"
)

const filter = ` and ((patient_id is null or patient_id = "") or (account_id is null or account_id = ""))`

func GetRequests(db *sql.DB, userId int64, email string) []models.Request {
	var rows *sql.Rows
	var err error
	if email == "" {
		rows, err = db.Query(
			`select r.id, dob, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')), mrn, organization_id, user_id, email
			FROM requests r 
			JOIN clinics c ON r.clinic_id = c.id 
			WHERE user_id = ?`+filter,
			userId,
		)
	} else {
		rows, err = db.Query(
			`select r.id, dob, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')), mrn, organization_id, user_id, email
			FROM requests r 
			JOIN clinics c ON r.clinic_id = c.id 
			WHERE email = ?`+filter,
			email,
		)
	}
	if err != nil {
		log.Fatalf("email blank: %v, %v", email == "", err)
	}
	defer rows.Close()
	reqs := []models.Request{}
	for rows.Next() {
		var r models.Request
		var nullUID sql.NullInt64
		var nullMrn, nullDob sql.NullString

		err := rows.Scan(&r.Id, &nullDob, &r.Name, &nullMrn, &r.OrgId, &nullUID, &r.Email)
		if err != nil {
			log.Fatalf("req scan %v", err)
		}
		if nullUID.Valid {
			r.UserId = nullUID.Int64
		}

		if nullMrn.Valid {
			r.Mrn = nullMrn.String
		}

		if nullDob.Valid && nullDob.String != "" && len(nullDob.String) >= 10 {
			r.Dob = fmt.Sprintf("%s%s%s", nullDob.String[6:], nullDob.String[0:2], nullDob.String[3:5])
		} else {
			r.Dob = nullDob.String
		}
		reqs = append(reqs, r)
	}
	return reqs
}

func GetEnrolments(db *sql.DB, userId int64, email string) []models.Enrol {
	var rows *sql.Rows
	var err error
	if email == "" {
		rows, err = db.Query(
			`select id, org_id, user_id, mrn, email, request_id, patient_dob
			FROM enrollments 
			WHERE user_id = ?`+filter,
			userId,
		)
	} else {
		rows, err = db.Query(
			`select id, org_id, user_id, mrn, email, request_id, patient_dob
			FROM enrollments 
			WHERE email = ?`+filter,
			email,
		)
	}

	if err != nil {
		log.Fatalf("enrol email blank: %v, %v", email == "", err)
	}
	defer rows.Close()
	enrols := []models.Enrol{}
	for rows.Next() {
		var e models.Enrol
		var nullUID, nullRID sql.NullInt64
		var nullDOB sql.NullString

		err := rows.Scan(&e.Id, &e.OrgId, &nullUID, &e.Mrn, &e.Email, &nullRID, &nullDOB)
		if err != nil {
			log.Fatalf("enrol scan %v", err)
		}
		if nullUID.Valid {
			e.UserId = nullUID.Int64
		}
		if nullRID.Valid {
			e.ReqId = int(nullRID.Int64)
		}
		if nullDOB.Valid {
			e.Dob = nullDOB.String
		}
		enrols = append(enrols, e)
	}
	return enrols
}

func GetConsents(db *sql.DB, userId int64, email string) []models.Consent {
	var rows *sql.Rows
	var err error
	if email == "" {
		rows, err = db.Query(`select id, org_id, user_id, mrn, email FROM consents WHERE user_id = ?`+filter, userId)
	} else {
		rows, err = db.Query(`select id, org_id, user_id, mrn, email FROM consents WHERE email = ?`+filter, email)
	}
	if err != nil {
		log.Fatalf("consent: %v", err)
	}
	defer rows.Close()
	consents := []models.Consent{}
	for rows.Next() {
		var c models.Consent
		var nullUID sql.NullInt64
		err := rows.Scan(&c.Id, &c.OrgId, &nullUID, &c.Mrn, &c.Email)
		if err != nil {
			log.Fatalf("consent scan  %v", err)
		}
		if nullUID.Valid {
			c.UserId = nullUID.Int64
		}
		consents = append(consents, c)
	}
	return consents
}

func GetPatients(db *sql.DB, accountId string) []models.Patient {
	rows, err := db.Query(
		"select id, account_id, COALESCE(first_name, ''), COALESCE(last_name, ''), COALESCE(dob, '') FROM patients WHERE account_id = ?",
		accountId,
	)
	if err != nil {
		log.Fatalf("getpts %v", err)
	}
	defer rows.Close()
	patients := []models.Patient{}
	for rows.Next() {
		var p models.Patient
		err := rows.Scan(
			&p.PatientId,
			&p.AccountId,
			&p.FirstName,
			&p.LastName,
			&p.Dob,
		)
		if err != nil {
			log.Fatalf("patient scan %v", err)
		}
		if p.Dob != "" {
			p.Dob = strings.Replace(p.Dob, "-", "", -1)
		}

		patients = append(patients, p)
	}
	return patients
}

func GetExams(db *sql.DB, userId int64) []models.Exam {
	rows, err := db.Query(
		`select uuid, COALESCE(e.patient_name, ''), COALESCE(dob, ''), patient_mrn, COALESCE(transfer_id, ''), organization_id
		FROM exams e 
		JOIN scans s on e.transfer_id = s.scan_id
		LEFT JOIN users u on u.user_id = s.origin_id
		LEFT JOIN clinics c on u.clinic_id = c.id
		WHERE e.user_id = ? and 
		((e.patient_id is null or e.patient_id = "") or (e.account_id is null or e.account_id = ""))`,
		userId,
	)
	if err != nil {
		log.Fatalf("getexams %v", err)
	}
	defer rows.Close()
	var nullMrn sql.NullString
	var nullOrgId sql.NullInt64
	exams := []models.Exam{}
	for rows.Next() {
		var e models.Exam
		err := rows.Scan(&e.Uuid, &e.Name, &e.Dob, &nullMrn, &e.TransferId, &nullOrgId)
		if err != nil {
			log.Fatalf("exam scan %v", err)
		}
		if nullMrn.Valid {
			e.Mrn = nullMrn.String
		}
		if nullOrgId.Valid {
			e.OrgId = int(nullOrgId.Int64)
		}
		exams = append(exams, e)
	}
	return exams
}
