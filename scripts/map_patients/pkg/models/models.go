package models

import (
	"time"
)

type Patient struct {
	AccountId		string
	PatientId		string
	FirstName	 	string
	LastName	 	string
	Email		 	string
	Telephone	 	string
	Dob				string
	IsAccountOwner  bool
	CreatedAt		time.Time
	UpdatedAt		time.Time
}

type Exam struct {
	Uuid       string
	Dob        string
	Name       string
	Mrn        string
	TransferId string
	OrgId      int
	PatientId  string
	AccountId  string
	Uploaded   time.Time
}

type Request struct {
	Id        int
	Dob       string
	Name      string
	Mrn       string
	UserId    int64
	OrgId     int
	Email     string
	PatientId string
	AccountId string
}

type Enrol struct {
	Id        int
	Mrn       string
	Dob       string
	ReqId     int
	UserId    int64
	OrgId     int
	Email     string
	PatientId string
	AccountId string
}

type Consent struct {
	Id        string
	OrgId     int
	UserId    int64
	Mrn       string
	Email     string
	PatientId string
	AccountId string
}

type Account struct {
	AccountId	string
	UserId		int64
	Email		string
}

// return map of dob to profile id and number of unique dobs
func GetDOBMap(ps []Patient) map[string]Patient {
	dobmap := map[string]Patient{}
	for _, p := range ps {
		if _, ok := dobmap[p.Dob]; !ok {
			dobmap[p.Dob] = p
		}
	}
	return dobmap
}
