package transform

import (
	"map_patients/pkg/models"
)

func MapRequests(pts []models.Patient, requests []models.Request, accountId string) {
	// check if only 1 patient, if so, all belong to this patient
	if len(pts) == 1 {
		for i, r := range requests {
			r.PatientId = pts[0].PatientId
			r.AccountId = pts[0].AccountId
			requests[i] = r
		}
	} else {
		dobMap := models.GetDOBMap(pts)
		for i, r := range requests {
			r.AccountId = accountId
			requests[i] = r
			if p, ok := dobMap[requests[i].Dob]; ok {
				r.PatientId = p.PatientId
				requests[i] = r
			}
		}
	}
	
}
