package transform

import (
	"map_patients/pkg/models"
)

func MapConsents(
	pts []models.Patient,
	consents []models.Consent,
	mappedEnrols []models.Enrol,
	accountId string,
) {
	// check if only 1 patient, if so, all belong to this patient
	if len(pts) == 1 {
		for i, c := range consents {
			c.PatientId = pts[0].PatientId
			c.AccountId = pts[0].AccountId
			consents[i] = c
		}
	}

	//consents don't have a dob directly associated with them.
	//try to map them based on an enrolment that has been mapped already
	for i, c := range consents {
		c.AccountId = accountId
		consents[i] = c
		//check if any enrolments match on org, and mrn
		for _, e := range mappedEnrols {
			if e.Mrn == c.Mrn && e.OrgId == c.OrgId {
				c.PatientId = e.PatientId
				consents[i] = c
			}
		}
	}
}
