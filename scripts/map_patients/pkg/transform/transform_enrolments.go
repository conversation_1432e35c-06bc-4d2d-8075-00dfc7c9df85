package transform

import (
	"map_patients/pkg/models"
)

func MapEnrolments(
	pts []models.Patient,
	enrols []models.Enrol,
	mappedReqs []models.Request,
	mappedExams []models.Exam,
	accountId string,
) {

	//if there only is one patient, can assume enrols are also for that patient
	if len(pts) == 1 {
		for i, e := range enrols {
			e.PatientId = pts[0].PatientId
			e.AccountId = pts[0].AccountId
			enrols[i] = e
		}
	}

	dobMap := models.GetDOBMap(pts)
	for i, e := range enrols {
		e.AccountId = accountId
		enrols[i] = e

		if enrols[i].Dob != "" {
			if p, ok := dobMap[enrols[i].Dob]; ok {
				e.PatientId = p.PatientId
				enrols[i] = e
			}
		} else if enrols[i].ReqId != 0 {
			//grab the same patient as the mapped request
			for j := range mappedReqs {
				if mappedReqs[j].Id == enrols[i].ReqId {
					e.PatientId = mappedReqs[j].PatientId
					enrols[i] = e
				}
			}
		} else if enrols[i].Mrn != "" && enrols[i].OrgId != 0 {
			//grab the same patient as the mapped exam
			for j := range mappedExams {
				if mappedExams[j].Mrn == enrols[i].Mrn &&
					mappedExams[j].OrgId == enrols[i].OrgId {
					e.PatientId = mappedExams[j].PatientId
					enrols[i] = e
				}
			}
		}
	}
}

