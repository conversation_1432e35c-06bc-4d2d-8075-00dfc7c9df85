package transform

import (
	"strings"
	"time"

	"github.com/lithammer/fuzzysearch/fuzzy"
	"map_patients/pkg/models"
)

func MapExams(ps []models.Patient, exams []models.Exam, accountId string) map[string]models.Patient {
	// all exams in a single transfer should belong to the same patient
	// fmt.Println("------dobmap")
	// fmt.Println(dobMap)
	transferMap := map[string]models.Patient{}
	if len(ps) == 1 {
		for i, e := range exams {
			e.PatientId = ps[0].PatientId
			e.AccountId = ps[0].AccountId
			exams[i] = e
			if _, ok := transferMap[e.TransferId]; !ok {
				transferMap[e.TransferId] = ps[0]
			}
		}
	} else {
		dobMap := models.GetDOBMap(ps)
		for i, e := range exams {
			e.AccountId = accountId
			exams[i] = e
			if p, ok := transferMap[e.TransferId]; ok {
				e.PatientId = p.PatientId
				e.AccountId = p.AccountId
				exams[i] = e
				continue
			}
			// fmt.Println("----------------hiiiiiii")
			// fmt.Println(exams[i].Dob)
			if exams[i].Dob == "" {
				t := time.Time{}
				t, err := time.Parse("********", exams[i].Dob)
				if err != nil || t.IsZero() {
					for _, e2 := range exams {
						if e2.Name == e.Name && e2.Dob != "" {
							e.Dob = e2.Dob
							exams[i] = e
						}
					}
				}
			}
			if p, ok := dobMap[exams[i].Dob]; ok {
				e.PatientId = p.PatientId
				e.AccountId = p.AccountId
				exams[i] = e
				transferMap[e.TransferId] = p
			} else {
				// cannot map by dob, try with name
				mappedE := mapByName(e, ps)
				exams[i] = mappedE
			}
		}
	}

	return transferMap
}

func mapByName(e models.Exam, ps []models.Patient) (models.Exam) {
	var examFirstName, examLastName string
	//handle edge case dicom names like 'LAST,^FIRST' or 'LAST,First^Middle' or 'Last^,^First^Middle' etc
	if strings.Contains(e.Name, ",^") || (strings.Contains(e.Name, "^") && strings.Contains(e.Name, ",")){
		examFirstName, examLastName = parseFirstLastNameWithCaretComma(e.Name)
	} else if strings.Contains(e.Name, "^") {
		examFirstName, examLastName = parseFirstLastName(e.Name, "^")
	} else if strings.Contains(e.Name, ",") {
		examFirstName, examLastName = parseFirstLastName(e.Name, ",")
	} else if strings.Contains(e.Name, " ") {
		examFirstName, examLastName = parseFirstLastName(e.Name, " ")
	} 

	for _, p := range ps {
		if (strings.Contains(p.FirstName, "^") || strings.Contains(p.LastName, "^")) && e.Name != "" {
			// bad profile name that is actually a unparsed dicom name
			// Profiles that are created before and around July 2021 had bad first name and last name
			// Dicom name are split by space not caret, with first name and last name swap
			pNames := []string{p.FirstName, p.LastName}
			profileDicomName := strings.Join(pNames, " ")
			if bidirectionMatch(profileDicomName, e.Name) {
				e.PatientId = p.PatientId
			}
			
		} else if p.FirstName != "" && p.LastName != "" && e.Name != ""{
			if bidirectionMatch(p.FirstName, examFirstName) &&
				bidirectionMatch(p.LastName, examLastName) {
					e.PatientId = p.PatientId
				}
		} else if (p.FirstName == "" || p.LastName == "") && !(p.FirstName == "" && p.LastName == "") {
			// some profile only has one of last name / first name
			// because patient_name in dicom doesnt have caret --> unable to identify a last name or first name
			// or because we had bad logic when creating profile (?)
			pNames := []string{p.FirstName, p.LastName}
			pName := strings.Join(pNames, " ")
			if bidirectionMatch(pName, e.Name) {
				e.PatientId = p.PatientId
			}
		}
	}
	return e
}

func parseFirstLastName(name string, deliminator string) (first string, last string) {
	s := strings.Split(name, deliminator)
	first = strings.Join(s[1:], " ")
	last = s[0]

	first = strings.TrimSpace(strings.Title(strings.ToLower(first)))
	last = strings.TrimSpace(strings.Title(strings.ToLower(last)))

	return first, last
}

func parseFirstLastNameWithCaretComma(name string) (first string, last string) {
	s := strings.Replace(name, ",", "", -1)
	first, last = parseFirstLastName(s, "^")
	return first, last
}

func bidirectionMatch(s1 string, s2 string) (bool) {
	return fuzzy.MatchFold(s1, s2) || fuzzy.MatchFold(s2, s1) 
}

