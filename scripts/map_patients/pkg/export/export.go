package export

import (
	"encoding/csv"
	"fmt"
	"os"

	"map_patients/pkg/models"
	log "github.com/sirupsen/logrus"
)

type convertFn func([]models.Request) [][]string

type ExamMapping struct {
	AccountId	string
	PatientId	string
	TransferId	string
}

func exportTsvData(data [][]string, writer *csv.Writer) error {
	defer writer.Flush()

	for _, value := range data {
		err := writer.Write(value)
		if err != nil {
			return err
		}
	}

	return nil
}

func OpenExportTsvFile(filename string) (*os.File, *csv.Writer, error) {
	file, err := os.Create(filename) // #nosec G304
	if err != nil {
		return nil, nil, err
	}
	writer := csv.NewWriter(file)
	writer.Comma = '\t'

	return file, writer, nil
}

func ExportConsentsToTsv(consents []models.Consent, writer *csv.Writer) error {
	data := make([][]string, 0)
	for _, c := range consents {
		data = append(data, []string{
			c.AccountId,
			c.<PERSON>d,
			c.Id,
		})
	}

	err := exportTsvData(data, writer)
	if err != nil {
		return err
	}

	return nil
}


func ExportRequestsToTsv(requests []models.Request, writer *csv.Writer) error {
	data := make([][]string, 0)
	for _, r := range requests {
		data = append(data, []string{
			r.AccountId,
			r.PatientId,
			fmt.Sprint(r.Id),
		})
	}

	err := exportTsvData(data, writer)
	if err != nil {
		return err
	}

	return nil
}

func ExportEnrolmentsToTsv(enrolments []models.Enrol, writer *csv.Writer) error {
	data := make([][]string, 0)
	for _, e := range enrolments {
		data = append(data, []string{
			e.AccountId,
			e.PatientId,
			fmt.Sprint(e.Id),
		})
	}
	err := exportTsvData(data, writer)
	if err != nil {
		return err
	}

	return nil
}

func ExportExamsToTsv(exams []models.Exam, writer *csv.Writer) error {
	data := make([][]string, 0)
	for _, e := range exams {
		data = append(data, []string{
			e.AccountId,
			e.PatientId,
			e.Uuid,
		})
	}

	log.Info("export to tsv")
	err := exportTsvData(data, writer)
	if err != nil {
		return err
	}

	return nil
}


