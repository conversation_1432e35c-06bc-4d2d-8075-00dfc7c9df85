run:
  tests: true
  build-tags:
    - integration

linters:
  disable-all: true
  enable:
    - gocritic       # Provides diagnostics that check for bugs, performance and style issues.
    - gosec          # Inspects source code for security problems.
    - sqlclosecheck  # Checks that sql.Rows, sql.Stmt, sqlx.NamedStmt, pgx.Query are closed.

linters-settings:
  gocritic:
    disable-all: true
    enabled-checks:
      - sqlQuery  # Detects issue in Query() and Exec() calls. https://go-critic.com/overview.html#sqlquery

issues:
  # See https://golangci-lint.run/usage/false-positives/#default-exclusions
  exclude-use-default: false
  exclude-rules:
    # Disable gosec entirely for tests
    - path: '(.+)_test\.go'
      linters:
        - gosec