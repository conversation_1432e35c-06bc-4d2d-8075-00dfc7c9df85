{"ChallengeDOBCA": {"hash": "sha1-df787dcb5027af8c56332e06c4dba797e5de80f5", "other": "¿Cuál es tu fecha de nacimiento? Indíquelo como AAAAMMDD. Si nació el 14 de marzo de 1988, ingresaría 19880314"}, "ChallengeDOBUS": {"hash": "sha1-5df9c3f9b9f5366fd776ed6589c03ab46b001661", "other": "¿Cuál es tu fecha de nacimiento? Indíquelo como MMDDAAAA. Si nació el 14 de marzo de 1988, ingresaría 03141988"}, "ChallengeHealthCardNum": {"hash": "sha1-54e76abe672f9c6fa4fa4b38cd8d62ff185afa8c", "other": "¿<PERSON><PERSON><PERSON>les son los últimos 4 números (no letras) del número de su tarjeta de salud?"}, "ConsentItem0": {"hash": "sha1-92579e37f03ebceffe75bc1562d28ac619ca2703", "other": "Mis registros estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción."}, "ConsentItem1": {"hash": "sha1-0ab28e51c40dbd9ce08729406a95e57aa6b64e75", "other": "Mis registros futuros, si / cuando se creen, también estarán disponibles para acceder y agregar a mi cuenta, a mi discreción."}, "ConsentItem2": {"hash": "sha1-c382295d5366fe841791016e967a2d2181433c2f", "other": "Podré optar por no participar en este acceso en línea en cualquier momento."}, "ConsentText": {"hash": "sha1-2aa94974cd92b011a3b33d5b9153b4a47ab4d9d9", "other": "Por la presente renuncio a todos los reclamos contra {{.ProviderName}} , sus médicos, empleados y agentes para todos los propósitos en relación con dicha comunicación y divulgación de información en dichos registros."}, "RequestConsentEnrollment": {"hash": "sha1-6740cffb3a9e57322dca231e0a544a92ea035110", "other": "Yo, <b> _PAT_NAME_ </b> presente renuncio a todos los reclamos contra {{.OrgName}} , sus médicos, empleados y agentes para todos los propósitos en relación con dicha comunicación y divulgación de información en dichos registros.\n Entiendo que mis registros estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción.\n Entiendo que podré optar por no participar en este acceso en línea en cualquier momento. Autorizo a <b> {{.OrgName}} </b> a entregarme mis registros como lo he especificado en este formulario."}, "RequestConsentNoEnroll": {"hash": "sha1-aa07a23b6a7183191ae75d3a3e5631cc736867cf", "other": "Yo, <b> _PAT_NAME_ </b> , por la presente renuncio a todos los reclamos contra <b> {{.OrgName}} </b> , sus médicos, empleados y agentes para todos los propósitos en relación con dicha comunicación y divulgación de información en dichos registros. Entiendo que mis registros estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción. Autorizo a <b> {{.OrgName}} </b> a entregarme mis registros como lo he especificado en este formulario."}, "BCPHNNumberLabel": {"hash": "sha1-53980cdc5866970f93d78af680a0b7630222ebaa", "other": "BC PHN"}, "ConsentTitle": {"hash": "sha1-31c142ca5f469bd8acf346f978013a7b3677a3c3", "other": "Solicitud de imágenes de pacientes en línea"}, "DelegateConsentEnroll": {"hash": "sha1-d7c7efb1c4397a4b142a637cb737e1d98dcfce7a", "other": "Soy un encargado sustituto de la toma de decisiones / custodio de la información médica de <b> </b> . En nombre de <b> _PAT_NAME_ </b> , por la presente renuncio a todos los reclamos contra <b> {{.OrgName}} </b> , sus médicos, empleados y agentes para todos los propósitos en relación con dicha comunicación y divulgación de información en dichos registros. Entiendo que los registros del paciente estarán disponibles para mí a través de una plataforma segura de almacenamiento de registros de terceros, PocketHealth, a través de la cual podré acceder, ver, descargar y compartir estos registros a mi discreción. Autorizo a <b> {{.OrgName}} </b> a entregarme los registros del paciente como lo he especificado en este formulario."}, "DelegateInfoConsentAddr": {"hash": "sha1-d70f93df5e8f9b55be44fbeee9d203972e3383d4", "other": "Dirección"}, "DelegateInfoConsentFirst": {"hash": "sha1-b6ea992aab4668311bb94778e056dd0285f27621", "other": "Nombre"}, "DelegateInfoConsentHeader": {"hash": "sha1-50770bcbf6919da93e9b3ed929d54962253f7f1a", "other": "Información del delegado"}, "DelegateInfoConsentLast": {"hash": "sha1-863cb39fbe7d70597076af1960b7ae4618d9e1bc", "other": "Apellido"}, "DelegateInfoConsentPhone": {"hash": "sha1-ab25d61bb12492027e2633997eea946c0f65a6b7", "other": "Número de teléfono"}, "DelegateInfoConsentRelation": {"hash": "sha1-19df0151ca64a1d609a9f3d34cdb46be9852597b", "other": "Relación con el paciente"}, "DontNotifyMeText": {"hash": "sha1-b3edb34eca3e9878b8079907cc20f2982bc3fddf", "other": "No deseo tener acceso a exámenes futuros. Entiendo que tendré que enviar una nueva solicitud para cualquier imagen nueva."}, "EnrollConsentPDFHeader": {"hash": "sha1-cecb289e0362d1be3d04346b776f7af86ff502d3", "other": "Consentimiento para la divulgación de registros"}, "EnrollUnderstandIntroText": {"hash": "sha1-10331464afaabadc34736498af01bf07cdf9dfda", "other": ", comprenda lo siguiente:\n"}, "ExamAddrConsentRow": {"hash": "sha1-0d42cec26dbc762aafe43e05a50f969d0b7505d7", "other": "Dirección"}, "ExamDateConsentRow": {"hash": "sha1-afa4c0ae1f2ab2a4c5c447df5dd1c2dcaaa547ac", "other": "<PERSON><PERSON> exam<PERSON>"}, "ExamInfoTitle": {"hash": "sha1-4045db7f5e32c6d97b3e36854dcb382c42c5bf49", "other": "Información de exámenes recientes"}, "ExamSiteConsentRow": {"hash": "sha1-febc6215a01228470537e398e5292e705ef89277", "other": "Sitio de examen"}, "ExamTypeConsentRow": {"hash": "sha1-8ed8c59d472197512254825d87eb86d910653eff", "other": "<PERSON><PERSON><PERSON> de examen"}, "FutureExamHeader": {"hash": "sha1-db56687c576710d6ec6601d8ed1b4d1725675114", "other": "Notificaciones de exámenes futuros"}, "I": {"hash": "sha1-ca73ab65568cd125c2d27a22bbd9e863c10b675d", "other": "Yo"}, "IPNLabel": {"hash": "sha1-09227bf33ed3b1dc22efc2156b89f5443a276715", "other": "Número de póliza de seguro"}, "NotifyMeText": {"hash": "sha1-02b7bac3e05489c084872e2c204abfd58205c087", "other": "Notifíqueme automáticamente cuando haya nuevos exámenes disponibles para agregar a mi cuenta."}, "OhipNumberLabel": {"hash": "sha1-d6fd5edefa6a361dbf81d839efddea624a567e80", "other": "Número de OHIP"}, "PatientDOBConsent": {"hash": "sha1-c9afc0f38f798bc1d76095b336d608c4c39bea45", "other": "<PERSON><PERSON> de nacimiento (mm/dd/aaaa)"}, "PatientEmailConsent": {"hash": "sha1-84add5b2952787581cb9a8851eef63d1ec75d22b", "other": "Correo electrónico"}, "PatientInfoConsentHeader": {"hash": "sha1-e403fdec6c80165c9c8e006d2f848d4e3edfc4c7", "other": "Información del paciente"}, "PatientNameConsent": {"hash": "sha1-64346b483c0adcfb6e6d4c2e4ce19f1cd2cde025", "other": "Nombre completo"}, "PatientPhoneConsent": {"hash": "sha1-ab25d61bb12492027e2633997eea946c0f65a6b7", "other": "Número de teléfono"}, "SSNLabel": {"hash": "sha1-6e02f8b18bfbcb044843f7c6e323a4d70cf1e4e8", "other": "Número de Seguro Social"}, "altIDLabel": {"hash": "sha1-c68c86f184904260bd9d716033561689431f313e", "other": "Otro ID de salud"}, "mrnLabel": {"hash": "sha1-4f63f4044a5fc54b528a4d9f75b2dfe528d7f9a7", "other": "MRN"}, "DelegateConfirmation": {"hash": "sha1-3e9f5f98fc23f564d1c00aef9e1c37252531dedf", "other": "Al enviar esta solicitud, confirmo que la información que proporcioné anteriormente es correcta y completa y que estoy legalmente autorizado para realizar esta solicitud en nombre del paciente."}}