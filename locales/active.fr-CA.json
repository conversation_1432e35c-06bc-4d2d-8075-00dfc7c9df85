{"RefundConfirmation": {"hash": "sha1-6c8e6df5032c4fc4990e26201ae48146af782ce6", "other": "Confirmation de remboursement – Identifiant {{PurchaseId}}"}, "ChallengeDOBCA": {"hash": "sha1-df787dcb5027af8c56332e06c4dba797e5de80f5", "other": "Quelle est votre date de naissance? Veuillez l'indiquer sous la forme AAAAMMJJ. Si vous êtes né le 14 mars 1988, vous devez entrer 19880314."}, "ChallengeDOBUS": {"hash": "sha1-5df9c3f9b9f5366fd776ed6589c03ab46b001661", "other": "Quelle est votre date de naissance? Veuillez l'indiquer sous la forme MMJJAAAA. Si vous êtes né le 14 mars 1988, vous devez entrer 03141988."}, "ChallengeHealthCardNum": {"hash": "sha1-54e76abe672f9c6fa4fa4b38cd8d62ff185afa8c", "other": "Quels sont les 4 derniers chiffres (pas de lettres) du numéro de votre carte d'assurance maladie?"}, "ConsentItem0": {"hash": "sha1-92579e37f03ebceffe75bc1562d28ac619ca2703", "other": "Mes dossiers seront mis à ma disposition par l'intermédiaire d'une plateforme de stockage de dossiers tierce partie sécurisée, PocketHealth, gr<PERSON><PERSON> à laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion."}, "ConsentItem1": {"hash": "sha1-0ab28e51c40dbd9ce08729406a95e57aa6b64e75", "other": "Mes futurs dossiers, s'ils sont créés, pourront également être consultés et ajoutés à mon compte, à ma discrétion."}, "ConsentItem2": {"hash": "sha1-c382295d5366fe841791016e967a2d2181433c2f", "other": "Je pourrai me désinscrire de cet accès en ligne à tout moment."}, "ConsentText": {"hash": "sha1-2aa94974cd92b011a3b33d5b9153b4a47ab4d9d9", "other": "Je renonce par la présente à tout recours contre {{.ProviderName}}, ses médecins, employés et agents à toutes fins utiles en rapport avec ladite communication et la divulgation d'informations dans lesdits dossiers."}, "RequestConsentEnrollment": {"hash": "sha1-6740cffb3a9e57322dca231e0a544a92ea035110", "other": "Je, <b>_PAT_NAME_</b> renonce par la présente à toute réclamation à l'encontre de <b>{{.OrgName}}</b>, de ses médecins, employés et agents à toutes fins utiles en rapport avec ladite communication et la divulgation des informations contenues dans lesdits dossiers.\\nJe comprends que mes dossiers seront mis à ma disposition via une plateforme de stockage de dossiers tiers sécurisée, PocketHealth, par laquelle je pourrai accéder, visualiser, télécharger et partager ces dossiers à ma discrétion.\\nJe comprends que je pourrai me retirer de cet accès en ligne à tout moment. J'autorise <b>{{.OrgName}}</b> à me communiquer mes dossiers comme je l'ai spécifié dans ce formulaire."}, "RequestConsentNoEnroll": {"hash": "sha1-aa07a23b6a7183191ae75d3a3e5631cc736867cf", "other": "Je, <b>_PAT_NAME_</b>, renonce par la présente à toute réclamation contre <b>{{.OrgName}}</b>, ses médecins, employés et agents à toutes fins utiles en rapport avec ladite communication et la divulgation d'informations dans lesdits dossiers. Je comprends que mes dossiers seront mis à ma disposition via une plateforme de stockage de dossiers tiers sécurisée, PocketHealth, grâce à laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion. J'autorise <b>{{.OrgName}}</b> à me communiquer mes dossiers comme je l'ai spécifié dans ce formulaire. "}, "BCPHNNumberLabel": {"hash": "sha1-53980cdc5866970f93d78af680a0b7630222ebaa", "other": "BC PHN"}, "ConsentTitle": {"hash": "sha1-31c142ca5f469bd8acf346f978013a7b3677a3c3", "other": "Demande d'imagerie en ligne pour les patients"}, "DelegateConsentEnroll": {"hash": "sha1-d7c7efb1c4397a4b142a637cb737e1d98dcfce7a", "other": "Je suis un Décideur Substitut/Dépositaire des informations de santé de  <b>_PAT_NAME_</b>. Au nom de   <b>_PAT_NAME_</b>, je renonce par la présente à toute réclamation contre <b>{{.OrgName}}</b>, ses médecins, employés et agents à toutes fins utiles en rapport avec ladite communication et la divulgation d'informations dans lesdits dossiers. Je comprends que mes dossiers seront mis à ma disposition via une plateforme de stockage de dossiers tiers sécurisée, PocketHealth, grâce à laquelle je pourrai accéder à ces dossiers, les consulter, les télécharger et les partager à ma discrétion. J'autorise <b>{{.OrgName}}</b> à me communiquer les dossiers du patient comme je l'ai spécifié dans ce formulaire. "}, "DelegateInfoConsentAddr": {"hash": "sha1-d70f93df5e8f9b55be44fbeee9d203972e3383d4", "other": "<PERSON><PERSON><PERSON>"}, "DelegateInfoConsentFirst": {"hash": "sha1-b6ea992aab4668311bb94778e056dd0285f27621", "other": "Prénom"}, "DelegateInfoConsentHeader": {"hash": "sha1-50770bcbf6919da93e9b3ed929d54962253f7f1a", "other": "Informations au sujet du délégué"}, "DelegateInfoConsentLast": {"hash": "sha1-863cb39fbe7d70597076af1960b7ae4618d9e1bc", "other": "Nom de famille"}, "DelegateInfoConsentPhone": {"hash": "sha1-ab25d61bb12492027e2633997eea946c0f65a6b7", "other": "Numéro de téléphone"}, "DelegateInfoConsentRelation": {"hash": "sha1-19df0151ca64a1d609a9f3d34cdb46be9852597b", "other": "Lien avec le patient"}, "DontNotifyMeText": {"hash": "sha1-b3edb34eca3e9878b8079907cc20f2982bc3fddf", "other": "Je ne souhaite pas recevoir l’accès à de futurs examens. Je comprends que je devrai soumettre une nouvelle demande pour toute nouvelle imagerie."}, "EnrollConsentPDFHeader": {"hash": "sha1-cecb289e0362d1be3d04346b776f7af86ff502d3", "other": "Consentement pour la divulgation des dossiers"}, "EnrollUnderstandIntroText": {"hash": "sha1-10331464afaabadc34736498af01bf07cdf9dfda", "other": ", comprends ce qui suit :"}, "ExamAddrConsentRow": {"hash": "sha1-0d42cec26dbc762aafe43e05a50f969d0b7505d7", "other": "<PERSON><PERSON><PERSON>"}, "ExamDateConsentRow": {"hash": "sha1-afa4c0ae1f2ab2a4c5c447df5dd1c2dcaaa547ac", "other": "Date de l’examen"}, "ExamInfoTitle": {"hash": "sha1-4045db7f5e32c6d97b3e36854dcb382c42c5bf49", "other": "Informations sur les examens récents"}, "ExamSiteConsentRow": {"hash": "sha1-febc6215a01228470537e398e5292e705ef89277", "other": "Lieu de l’examen"}, "ExamTypeConsentRow": {"hash": "sha1-8ed8c59d472197512254825d87eb86d910653eff", "other": "Type d’examen"}, "FutureExamHeader": {"hash": "sha1-db56687c576710d6ec6601d8ed1b4d1725675114", "other": "Notifications d’examens futurs"}, "I": {"hash": "sha1-ca73ab65568cd125c2d27a22bbd9e863c10b675d", "other": "<PERSON> so<PERSON>,"}, "IPNLabel": {"hash": "sha1-09227bf33ed3b1dc22efc2156b89f5443a276715", "other": "Numéro de police d'assurance"}, "NotifyMeText": {"hash": "sha1-02b7bac3e05489c084872e2c204abfd58205c087", "other": "Veuillez m'informer automatiquement lorsque de nouveaux examens sont disponibles pour être ajoutés à mon compte."}, "OhipNumberLabel": {"hash": "sha1-d6fd5edefa6a361dbf81d839efddea624a567e80", "other": "Numéro OHIP"}, "PatientDOBConsent": {"hash": "sha1-c9afc0f38f798bc1d76095b336d608c4c39bea45", "other": "Date de naissance (mm/jj/aaaa)"}, "PatientEmailConsent": {"hash": "sha1-84add5b2952787581cb9a8851eef63d1ec75d22b", "other": "<PERSON><PERSON><PERSON>"}, "PatientInfoConsentHeader": {"hash": "sha1-e403fdec6c80165c9c8e006d2f848d4e3edfc4c7", "other": "Informations sur le patient"}, "PatientNameConsent": {"hash": "sha1-64346b483c0adcfb6e6d4c2e4ce19f1cd2cde025", "other": "Nom complet"}, "PatientPhoneConsent": {"hash": "sha1-ab25d61bb12492027e2633997eea946c0f65a6b7", "other": "Numéro de téléphone"}, "SSNLabel": {"hash": "sha1-6e02f8b18bfbcb044843f7c6e323a4d70cf1e4e8", "other": "Numéro de sécurité sociale"}, "altIDLabel": {"hash": "sha1-c68c86f184904260bd9d716033561689431f313e", "other": "Autre identifiant de santé"}, "mrnLabel": {"hash": "sha1-4f63f4044a5fc54b528a4d9f75b2dfe528d7f9a7", "other": "NDM"}, "DelegateConfirmation": {"hash": "sha1-3e9f5f98fc23f564d1c00aef9e1c37252531dedf", "other": "En soumettant cette demande, je confirme que les informations que j'ai fournies ci-dessus sont correctes et complètes et que je suis légalement autorisé à faire cette demande au nom du patient."}}