{"email_username": "<EMAIL>", "email_password": "wf2s7p0PN8q5", "mysql_connection_string": "pockethealth-web:hZ1NSfeCkgG1!@tcp(10.0.2.4:3306)/pockethealth?parseTime=true", "azure_storage_account": "phdevcentralcabloblrs", "azure_storage_key": "****************************************************************************************", "ssl_certs": [{"key": "/home/<USER>/pockethealth/chain.pem", "chain": "/home/<USER>/pockethealth/privkey.pem"}], "payment_config": {"CA": {"bambora_merchantid": "*********", "bambora_paymentapikey": "CBB898Cb2F9c42CC8A3Ac365d100fce9", "bambora_reportingapikey": "d604e9eb2f154209A3F6edeCb855BB7D"}, "US": {"bambora_merchantid": "*********", "bambora_paymentapikey": "756A1bB2523a4997ae853AF4D5b4a760", "bambora_reportingapikey": "b7844D26b9D743A9B64Ccc8E8175dC30"}}, "fax_hostname": "", "fax_port": ""}