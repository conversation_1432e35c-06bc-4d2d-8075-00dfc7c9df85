// Code generated by ogen, DO NOT EDIT.

package api

import (
	"context"
	"net/http"

	"github.com/go-faster/errors"
)

// SecuritySource is provider of security values (tokens, passwords, etc.).
type SecuritySource interface {
	// JwtBearer provides jwtBearer security value.
	JwtBearer(ctx context.Context, operationName string) (JwtBearer, error)
	// PhSignature provides phSignature security value.
	// Authentication used for requests sent from RegionRouter. It is a signature of the form
	// ES256(SHA256(requestBody)).
	PhSignature(ctx context.Context, operationName string) (PhSignature, error)
}

func (s *Client) securityJwtBearer(ctx context.Context, operationName string, req *http.Request) error {
	t, err := s.sec.JwtBearer(ctx, operationName)
	if err != nil {
		return errors.Wrap(err, "security source \"JwtBearer\"")
	}
	req.Header.Set("Authorization", "Bearer "+t.<PERSON>)
	return nil
}
func (s *Client) securityPhSignature(ctx context.Context, operationName string, req *http.Request) error {
	t, err := s.sec.PhSignature(ctx, operationName)
	if err != nil {
		return errors.Wrap(err, "security source \"PhSignature\"")
	}
	req.Header.Set("X-PH-Signature", t.APIKey)
	return nil
}
