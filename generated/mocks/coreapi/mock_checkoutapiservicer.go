// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	models "gitlab.com/pockethealth/coreapi/pkg/models"
)

// MockCheckoutApiServicer is an autogenerated mock type for the CheckoutApiServicer type
type MockCheckoutApiServicer struct {
	mock.Mock
}

type MockCheckoutApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCheckoutApiServicer) EXPECT() *MockCheckoutApiServicer_Expecter {
	return &MockCheckoutApiServicer_Expecter{mock: &_m.Mock}
}

// GetMetadata provides a mock function with given fields: _a0, _a1
func (_m *MockCheckoutApiServicer) GetMetadata(_a0 context.Context, _a1 int64) (*models.CheckoutMetadata, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetMetadata")
	}

	var r0 *models.CheckoutMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*models.CheckoutMetadata, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *models.CheckoutMetadata); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CheckoutMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCheckoutApiServicer_GetMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMetadata'
type MockCheckoutApiServicer_GetMetadata_Call struct {
	*mock.Call
}

// GetMetadata is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 int64
func (_e *MockCheckoutApiServicer_Expecter) GetMetadata(_a0 interface{}, _a1 interface{}) *MockCheckoutApiServicer_GetMetadata_Call {
	return &MockCheckoutApiServicer_GetMetadata_Call{Call: _e.mock.On("GetMetadata", _a0, _a1)}
}

func (_c *MockCheckoutApiServicer_GetMetadata_Call) Run(run func(_a0 context.Context, _a1 int64)) *MockCheckoutApiServicer_GetMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *MockCheckoutApiServicer_GetMetadata_Call) Return(_a0 *models.CheckoutMetadata, _a1 error) *MockCheckoutApiServicer_GetMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCheckoutApiServicer_GetMetadata_Call) RunAndReturn(run func(context.Context, int64) (*models.CheckoutMetadata, error)) *MockCheckoutApiServicer_GetMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// PostActivateStudies provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockCheckoutApiServicer) PostActivateStudies(_a0 context.Context, _a1 string, _a2 coreapi.ActivationKey, _a3 string, _a4 string) (coreapi.ActivateStudyResponse, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for PostActivateStudies")
	}

	var r0 coreapi.ActivateStudyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.ActivationKey, string, string) (coreapi.ActivateStudyResponse, error)); ok {
		return rf(_a0, _a1, _a2, _a3, _a4)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.ActivationKey, string, string) coreapi.ActivateStudyResponse); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Get(0).(coreapi.ActivateStudyResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, coreapi.ActivationKey, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCheckoutApiServicer_PostActivateStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostActivateStudies'
type MockCheckoutApiServicer_PostActivateStudies_Call struct {
	*mock.Call
}

// PostActivateStudies is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 coreapi.ActivationKey
//   - _a3 string
//   - _a4 string
func (_e *MockCheckoutApiServicer_Expecter) PostActivateStudies(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}, _a4 interface{}) *MockCheckoutApiServicer_PostActivateStudies_Call {
	return &MockCheckoutApiServicer_PostActivateStudies_Call{Call: _e.mock.On("PostActivateStudies", _a0, _a1, _a2, _a3, _a4)}
}

func (_c *MockCheckoutApiServicer_PostActivateStudies_Call) Run(run func(_a0 context.Context, _a1 string, _a2 coreapi.ActivationKey, _a3 string, _a4 string)) *MockCheckoutApiServicer_PostActivateStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(coreapi.ActivationKey), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockCheckoutApiServicer_PostActivateStudies_Call) Return(_a0 coreapi.ActivateStudyResponse, _a1 error) *MockCheckoutApiServicer_PostActivateStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCheckoutApiServicer_PostActivateStudies_Call) RunAndReturn(run func(context.Context, string, coreapi.ActivationKey, string, string) (coreapi.ActivateStudyResponse, error)) *MockCheckoutApiServicer_PostActivateStudies_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCheckoutApiServicer creates a new instance of MockCheckoutApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCheckoutApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCheckoutApiServicer {
	mock := &MockCheckoutApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
