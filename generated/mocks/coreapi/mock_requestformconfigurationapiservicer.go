// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	orgs "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
)

// MockRequestFormConfigurationApiServicer is an autogenerated mock type for the RequestFormConfigurationApiServicer type
type MockRequestFormConfigurationApiServicer struct {
	mock.Mock
}

type MockRequestFormConfigurationApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRequestFormConfigurationApiServicer) EXPECT() *MockRequestFormConfigurationApiServicer_Expecter {
	return &MockRequestFormConfigurationApiServicer_Expecter{mock: &_m.Mock}
}

// GetFormById provides a mock function with given fields: ctx, formId, inApp, language
func (_m *MockRequestFormConfigurationApiServicer) GetFormById(ctx context.Context, formId string, inApp bool, language string) (orgs.FormResponse, error) {
	ret := _m.Called(ctx, formId, inApp, language)

	if len(ret) == 0 {
		panic("no return value specified for GetFormById")
	}

	var r0 orgs.FormResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, string) (orgs.FormResponse, error)); ok {
		return rf(ctx, formId, inApp, language)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, string) orgs.FormResponse); ok {
		r0 = rf(ctx, formId, inApp, language)
	} else {
		r0 = ret.Get(0).(orgs.FormResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool, string) error); ok {
		r1 = rf(ctx, formId, inApp, language)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRequestFormConfigurationApiServicer_GetFormById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFormById'
type MockRequestFormConfigurationApiServicer_GetFormById_Call struct {
	*mock.Call
}

// GetFormById is a helper method to define mock.On call
//   - ctx context.Context
//   - formId string
//   - inApp bool
//   - language string
func (_e *MockRequestFormConfigurationApiServicer_Expecter) GetFormById(ctx interface{}, formId interface{}, inApp interface{}, language interface{}) *MockRequestFormConfigurationApiServicer_GetFormById_Call {
	return &MockRequestFormConfigurationApiServicer_GetFormById_Call{Call: _e.mock.On("GetFormById", ctx, formId, inApp, language)}
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormById_Call) Run(run func(ctx context.Context, formId string, inApp bool, language string)) *MockRequestFormConfigurationApiServicer_GetFormById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool), args[3].(string))
	})
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormById_Call) Return(_a0 orgs.FormResponse, _a1 error) *MockRequestFormConfigurationApiServicer_GetFormById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormById_Call) RunAndReturn(run func(context.Context, string, bool, string) (orgs.FormResponse, error)) *MockRequestFormConfigurationApiServicer_GetFormById_Call {
	_c.Call.Return(run)
	return _c
}

// GetFormByProviderId provides a mock function with given fields: ctx, providerId, inApp, deviceId, language
func (_m *MockRequestFormConfigurationApiServicer) GetFormByProviderId(ctx context.Context, providerId int64, inApp bool, deviceId string, language string) (orgs.FormResponse, error) {
	ret := _m.Called(ctx, providerId, inApp, deviceId, language)

	if len(ret) == 0 {
		panic("no return value specified for GetFormByProviderId")
	}

	var r0 orgs.FormResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, bool, string, string) (orgs.FormResponse, error)); ok {
		return rf(ctx, providerId, inApp, deviceId, language)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, bool, string, string) orgs.FormResponse); ok {
		r0 = rf(ctx, providerId, inApp, deviceId, language)
	} else {
		r0 = ret.Get(0).(orgs.FormResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, bool, string, string) error); ok {
		r1 = rf(ctx, providerId, inApp, deviceId, language)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFormByProviderId'
type MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call struct {
	*mock.Call
}

// GetFormByProviderId is a helper method to define mock.On call
//   - ctx context.Context
//   - providerId int64
//   - inApp bool
//   - deviceId string
//   - language string
func (_e *MockRequestFormConfigurationApiServicer_Expecter) GetFormByProviderId(ctx interface{}, providerId interface{}, inApp interface{}, deviceId interface{}, language interface{}) *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call {
	return &MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call{Call: _e.mock.On("GetFormByProviderId", ctx, providerId, inApp, deviceId, language)}
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call) Run(run func(ctx context.Context, providerId int64, inApp bool, deviceId string, language string)) *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(bool), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call) Return(_a0 orgs.FormResponse, _a1 error) *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call) RunAndReturn(run func(context.Context, int64, bool, string, string) (orgs.FormResponse, error)) *MockRequestFormConfigurationApiServicer_GetFormByProviderId_Call {
	_c.Call.Return(run)
	return _c
}

// GetFormByProviderUrl provides a mock function with given fields: ctx, providerUrl, inApp, deviceId, language
func (_m *MockRequestFormConfigurationApiServicer) GetFormByProviderUrl(ctx context.Context, providerUrl string, inApp bool, deviceId string, language string) (orgs.FormResponse, error) {
	ret := _m.Called(ctx, providerUrl, inApp, deviceId, language)

	if len(ret) == 0 {
		panic("no return value specified for GetFormByProviderUrl")
	}

	var r0 orgs.FormResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, string, string) (orgs.FormResponse, error)); ok {
		return rf(ctx, providerUrl, inApp, deviceId, language)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, string, string) orgs.FormResponse); ok {
		r0 = rf(ctx, providerUrl, inApp, deviceId, language)
	} else {
		r0 = ret.Get(0).(orgs.FormResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool, string, string) error); ok {
		r1 = rf(ctx, providerUrl, inApp, deviceId, language)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFormByProviderUrl'
type MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call struct {
	*mock.Call
}

// GetFormByProviderUrl is a helper method to define mock.On call
//   - ctx context.Context
//   - providerUrl string
//   - inApp bool
//   - deviceId string
//   - language string
func (_e *MockRequestFormConfigurationApiServicer_Expecter) GetFormByProviderUrl(ctx interface{}, providerUrl interface{}, inApp interface{}, deviceId interface{}, language interface{}) *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call {
	return &MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call{Call: _e.mock.On("GetFormByProviderUrl", ctx, providerUrl, inApp, deviceId, language)}
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call) Run(run func(ctx context.Context, providerUrl string, inApp bool, deviceId string, language string)) *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call) Return(_a0 orgs.FormResponse, _a1 error) *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call) RunAndReturn(run func(context.Context, string, bool, string, string) (orgs.FormResponse, error)) *MockRequestFormConfigurationApiServicer_GetFormByProviderUrl_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRequestFormConfigurationApiServicer creates a new instance of MockRequestFormConfigurationApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRequestFormConfigurationApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRequestFormConfigurationApiServicer {
	mock := &MockRequestFormConfigurationApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
