// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	accountservice "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"

	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	mock "github.com/stretchr/testify/mock"

	models "gitlab.com/pockethealth/coreapi/pkg/models"
)

// MockV2UsersApiServicer is an autogenerated mock type for the V2UsersApiServicer type
type MockV2UsersApiServicer struct {
	mock.Mock
}

type MockV2UsersApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockV2UsersApiServicer) EXPECT() *MockV2UsersApiServicer_Expecter {
	return &MockV2UsersApiServicer_Expecter{mock: &_m.Mock}
}

// GetAccountEnrolmentProviders provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) GetAccountEnrolmentProviders(_a0 context.Context, _a1 string) ([]int64, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountEnrolmentProviders")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]int64, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []int64); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAccountEnrolmentProviders'
type MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call struct {
	*mock.Call
}

// GetAccountEnrolmentProviders is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) GetAccountEnrolmentProviders(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call {
	return &MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call{Call: _e.mock.On("GetAccountEnrolmentProviders", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call) Return(_a0 []int64, _a1 error) *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call) RunAndReturn(run func(context.Context, string) ([]int64, error)) *MockV2UsersApiServicer_GetAccountEnrolmentProviders_Call {
	_c.Call.Return(run)
	return _c
}

// GetAccountState provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) GetAccountState(_a0 context.Context, _a1 string) (coreapi.AccountState, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountState")
	}

	var r0 coreapi.AccountState
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (coreapi.AccountState, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) coreapi.AccountState); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(coreapi.AccountState)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_GetAccountState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAccountState'
type MockV2UsersApiServicer_GetAccountState_Call struct {
	*mock.Call
}

// GetAccountState is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) GetAccountState(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_GetAccountState_Call {
	return &MockV2UsersApiServicer_GetAccountState_Call{Call: _e.mock.On("GetAccountState", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_GetAccountState_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_GetAccountState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_GetAccountState_Call) Return(_a0 coreapi.AccountState, _a1 error) *MockV2UsersApiServicer_GetAccountState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_GetAccountState_Call) RunAndReturn(run func(context.Context, string) (coreapi.AccountState, error)) *MockV2UsersApiServicer_GetAccountState_Call {
	_c.Call.Return(run)
	return _c
}

// GetEnrolmentByAccountId provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) GetEnrolmentByAccountId(_a0 context.Context, _a1 string) ([]coreapi.Enrollment, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetEnrolmentByAccountId")
	}

	var r0 []coreapi.Enrollment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]coreapi.Enrollment, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []coreapi.Enrollment); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coreapi.Enrollment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_GetEnrolmentByAccountId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEnrolmentByAccountId'
type MockV2UsersApiServicer_GetEnrolmentByAccountId_Call struct {
	*mock.Call
}

// GetEnrolmentByAccountId is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) GetEnrolmentByAccountId(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call {
	return &MockV2UsersApiServicer_GetEnrolmentByAccountId_Call{Call: _e.mock.On("GetEnrolmentByAccountId", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call) Return(_a0 []coreapi.Enrollment, _a1 error) *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call) RunAndReturn(run func(context.Context, string) ([]coreapi.Enrollment, error)) *MockV2UsersApiServicer_GetEnrolmentByAccountId_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserExamLookup provides a mock function with given fields: ctx, ssoToken, acctId, accession
func (_m *MockV2UsersApiServicer) GetUserExamLookup(ctx context.Context, ssoToken string, acctId string, accession string) (string, error) {
	ret := _m.Called(ctx, ssoToken, acctId, accession)

	if len(ret) == 0 {
		panic("no return value specified for GetUserExamLookup")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (string, error)); ok {
		return rf(ctx, ssoToken, acctId, accession)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) string); ok {
		r0 = rf(ctx, ssoToken, acctId, accession)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, ssoToken, acctId, accession)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_GetUserExamLookup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserExamLookup'
type MockV2UsersApiServicer_GetUserExamLookup_Call struct {
	*mock.Call
}

// GetUserExamLookup is a helper method to define mock.On call
//   - ctx context.Context
//   - ssoToken string
//   - acctId string
//   - accession string
func (_e *MockV2UsersApiServicer_Expecter) GetUserExamLookup(ctx interface{}, ssoToken interface{}, acctId interface{}, accession interface{}) *MockV2UsersApiServicer_GetUserExamLookup_Call {
	return &MockV2UsersApiServicer_GetUserExamLookup_Call{Call: _e.mock.On("GetUserExamLookup", ctx, ssoToken, acctId, accession)}
}

func (_c *MockV2UsersApiServicer_GetUserExamLookup_Call) Run(run func(ctx context.Context, ssoToken string, acctId string, accession string)) *MockV2UsersApiServicer_GetUserExamLookup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_GetUserExamLookup_Call) Return(_a0 string, _a1 error) *MockV2UsersApiServicer_GetUserExamLookup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_GetUserExamLookup_Call) RunAndReturn(run func(context.Context, string, string, string) (string, error)) *MockV2UsersApiServicer_GetUserExamLookup_Call {
	_c.Call.Return(run)
	return _c
}

// PatchDeactivateAccount provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockV2UsersApiServicer) PatchDeactivateAccount(_a0 context.Context, _a1 string, _a2 string) error {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PatchDeactivateAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PatchDeactivateAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchDeactivateAccount'
type MockV2UsersApiServicer_PatchDeactivateAccount_Call struct {
	*mock.Call
}

// PatchDeactivateAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockV2UsersApiServicer_Expecter) PatchDeactivateAccount(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockV2UsersApiServicer_PatchDeactivateAccount_Call {
	return &MockV2UsersApiServicer_PatchDeactivateAccount_Call{Call: _e.mock.On("PatchDeactivateAccount", _a0, _a1, _a2)}
}

func (_c *MockV2UsersApiServicer_PatchDeactivateAccount_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockV2UsersApiServicer_PatchDeactivateAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PatchDeactivateAccount_Call) Return(_a0 error) *MockV2UsersApiServicer_PatchDeactivateAccount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PatchDeactivateAccount_Call) RunAndReturn(run func(context.Context, string, string) error) *MockV2UsersApiServicer_PatchDeactivateAccount_Call {
	_c.Call.Return(run)
	return _c
}

// PatchSetAccountOwner provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockV2UsersApiServicer) PatchSetAccountOwner(_a0 context.Context, _a1 string, _a2 models.SetAccountOwnerRequest) error {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PatchSetAccountOwner")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, models.SetAccountOwnerRequest) error); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PatchSetAccountOwner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchSetAccountOwner'
type MockV2UsersApiServicer_PatchSetAccountOwner_Call struct {
	*mock.Call
}

// PatchSetAccountOwner is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 models.SetAccountOwnerRequest
func (_e *MockV2UsersApiServicer_Expecter) PatchSetAccountOwner(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockV2UsersApiServicer_PatchSetAccountOwner_Call {
	return &MockV2UsersApiServicer_PatchSetAccountOwner_Call{Call: _e.mock.On("PatchSetAccountOwner", _a0, _a1, _a2)}
}

func (_c *MockV2UsersApiServicer_PatchSetAccountOwner_Call) Run(run func(_a0 context.Context, _a1 string, _a2 models.SetAccountOwnerRequest)) *MockV2UsersApiServicer_PatchSetAccountOwner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(models.SetAccountOwnerRequest))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PatchSetAccountOwner_Call) Return(_a0 error) *MockV2UsersApiServicer_PatchSetAccountOwner_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PatchSetAccountOwner_Call) RunAndReturn(run func(context.Context, string, models.SetAccountOwnerRequest) error) *MockV2UsersApiServicer_PatchSetAccountOwner_Call {
	_c.Call.Return(run)
	return _c
}

// PostEmailVerification provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostEmailVerification(_a0 context.Context, _a1 string) (accountservice.EmailVerification, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostEmailVerification")
	}

	var r0 accountservice.EmailVerification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (accountservice.EmailVerification, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) accountservice.EmailVerification); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(accountservice.EmailVerification)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostEmailVerification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostEmailVerification'
type MockV2UsersApiServicer_PostEmailVerification_Call struct {
	*mock.Call
}

// PostEmailVerification is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) PostEmailVerification(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostEmailVerification_Call {
	return &MockV2UsersApiServicer_PostEmailVerification_Call{Call: _e.mock.On("PostEmailVerification", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostEmailVerification_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_PostEmailVerification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostEmailVerification_Call) Return(_a0 accountservice.EmailVerification, _a1 error) *MockV2UsersApiServicer_PostEmailVerification_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostEmailVerification_Call) RunAndReturn(run func(context.Context, string) (accountservice.EmailVerification, error)) *MockV2UsersApiServicer_PostEmailVerification_Call {
	_c.Call.Return(run)
	return _c
}

// PostLockAccount provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostLockAccount(_a0 context.Context, _a1 string) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostLockAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PostLockAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostLockAccount'
type MockV2UsersApiServicer_PostLockAccount_Call struct {
	*mock.Call
}

// PostLockAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) PostLockAccount(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostLockAccount_Call {
	return &MockV2UsersApiServicer_PostLockAccount_Call{Call: _e.mock.On("PostLockAccount", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostLockAccount_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_PostLockAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostLockAccount_Call) Return(_a0 error) *MockV2UsersApiServicer_PostLockAccount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PostLockAccount_Call) RunAndReturn(run func(context.Context, string) error) *MockV2UsersApiServicer_PostLockAccount_Call {
	_c.Call.Return(run)
	return _c
}

// PostUserPasswordReset provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostUserPasswordReset(_a0 context.Context, _a1 coreapi.PasswordResetInfo) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostUserPasswordReset")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.PasswordResetInfo) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PostUserPasswordReset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUserPasswordReset'
type MockV2UsersApiServicer_PostUserPasswordReset_Call struct {
	*mock.Call
}

// PostUserPasswordReset is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 coreapi.PasswordResetInfo
func (_e *MockV2UsersApiServicer_Expecter) PostUserPasswordReset(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostUserPasswordReset_Call {
	return &MockV2UsersApiServicer_PostUserPasswordReset_Call{Call: _e.mock.On("PostUserPasswordReset", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostUserPasswordReset_Call) Run(run func(_a0 context.Context, _a1 coreapi.PasswordResetInfo)) *MockV2UsersApiServicer_PostUserPasswordReset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(coreapi.PasswordResetInfo))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUserPasswordReset_Call) Return(_a0 error) *MockV2UsersApiServicer_PostUserPasswordReset_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUserPasswordReset_Call) RunAndReturn(run func(context.Context, coreapi.PasswordResetInfo) error) *MockV2UsersApiServicer_PostUserPasswordReset_Call {
	_c.Call.Return(run)
	return _c
}

// PostUserResetPasswordInit provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostUserResetPasswordInit(_a0 context.Context, _a1 string) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostUserResetPasswordInit")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PostUserResetPasswordInit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUserResetPasswordInit'
type MockV2UsersApiServicer_PostUserResetPasswordInit_Call struct {
	*mock.Call
}

// PostUserResetPasswordInit is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockV2UsersApiServicer_Expecter) PostUserResetPasswordInit(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostUserResetPasswordInit_Call {
	return &MockV2UsersApiServicer_PostUserResetPasswordInit_Call{Call: _e.mock.On("PostUserResetPasswordInit", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostUserResetPasswordInit_Call) Run(run func(_a0 context.Context, _a1 string)) *MockV2UsersApiServicer_PostUserResetPasswordInit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUserResetPasswordInit_Call) Return(_a0 error) *MockV2UsersApiServicer_PostUserResetPasswordInit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUserResetPasswordInit_Call) RunAndReturn(run func(context.Context, string) error) *MockV2UsersApiServicer_PostUserResetPasswordInit_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersEmailUpdateInit provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockV2UsersApiServicer) PostUsersEmailUpdateInit(_a0 context.Context, _a1 string, _a2 coreapi.EmailUpdate) error {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersEmailUpdateInit")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.EmailUpdate) error); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersEmailUpdateInit'
type MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call struct {
	*mock.Call
}

// PostUsersEmailUpdateInit is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 coreapi.EmailUpdate
func (_e *MockV2UsersApiServicer_Expecter) PostUsersEmailUpdateInit(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call {
	return &MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call{Call: _e.mock.On("PostUsersEmailUpdateInit", _a0, _a1, _a2)}
}

func (_c *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call) Run(run func(_a0 context.Context, _a1 string, _a2 coreapi.EmailUpdate)) *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(coreapi.EmailUpdate))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call) Return(_a0 error) *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call) RunAndReturn(run func(context.Context, string, coreapi.EmailUpdate) error) *MockV2UsersApiServicer_PostUsersEmailUpdateInit_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersLogin provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockV2UsersApiServicer) PostUsersLogin(_a0 context.Context, _a1 string, _a2 string, _a3 string) (interface{}, string, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersLogin")
	}

	var r0 interface{}
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (interface{}, string, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) interface{}); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) string); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, string) error); ok {
		r2 = rf(_a0, _a1, _a2, _a3)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockV2UsersApiServicer_PostUsersLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersLogin'
type MockV2UsersApiServicer_PostUsersLogin_Call struct {
	*mock.Call
}

// PostUsersLogin is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
//   - _a3 string
func (_e *MockV2UsersApiServicer_Expecter) PostUsersLogin(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockV2UsersApiServicer_PostUsersLogin_Call {
	return &MockV2UsersApiServicer_PostUsersLogin_Call{Call: _e.mock.On("PostUsersLogin", _a0, _a1, _a2, _a3)}
}

func (_c *MockV2UsersApiServicer_PostUsersLogin_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string, _a3 string)) *MockV2UsersApiServicer_PostUsersLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersLogin_Call) Return(_a0 interface{}, _a1 string, _a2 error) *MockV2UsersApiServicer_PostUsersLogin_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersLogin_Call) RunAndReturn(run func(context.Context, string, string, string) (interface{}, string, error)) *MockV2UsersApiServicer_PostUsersLogin_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersRefresh provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockV2UsersApiServicer) PostUsersRefresh(_a0 context.Context, _a1 string, _a2 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersRefresh")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (interface{}, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) interface{}); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostUsersRefresh_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersRefresh'
type MockV2UsersApiServicer_PostUsersRefresh_Call struct {
	*mock.Call
}

// PostUsersRefresh is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockV2UsersApiServicer_Expecter) PostUsersRefresh(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockV2UsersApiServicer_PostUsersRefresh_Call {
	return &MockV2UsersApiServicer_PostUsersRefresh_Call{Call: _e.mock.On("PostUsersRefresh", _a0, _a1, _a2)}
}

func (_c *MockV2UsersApiServicer_PostUsersRefresh_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockV2UsersApiServicer_PostUsersRefresh_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersRefresh_Call) Return(_a0 interface{}, _a1 error) *MockV2UsersApiServicer_PostUsersRefresh_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersRefresh_Call) RunAndReturn(run func(context.Context, string, string) (interface{}, error)) *MockV2UsersApiServicer_PostUsersRefresh_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersSetup provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostUsersSetup(_a0 context.Context, _a1 accountservice.Verification) (accountservice.PasswordSetup, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersSetup")
	}

	var r0 accountservice.PasswordSetup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.Verification) (accountservice.PasswordSetup, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.Verification) accountservice.PasswordSetup); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(accountservice.PasswordSetup)
	}

	if rf, ok := ret.Get(1).(func(context.Context, accountservice.Verification) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostUsersSetup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersSetup'
type MockV2UsersApiServicer_PostUsersSetup_Call struct {
	*mock.Call
}

// PostUsersSetup is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.Verification
func (_e *MockV2UsersApiServicer_Expecter) PostUsersSetup(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostUsersSetup_Call {
	return &MockV2UsersApiServicer_PostUsersSetup_Call{Call: _e.mock.On("PostUsersSetup", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostUsersSetup_Call) Run(run func(_a0 context.Context, _a1 accountservice.Verification)) *MockV2UsersApiServicer_PostUsersSetup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.Verification))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersSetup_Call) Return(_a0 accountservice.PasswordSetup, _a1 error) *MockV2UsersApiServicer_PostUsersSetup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersSetup_Call) RunAndReturn(run func(context.Context, accountservice.Verification) (accountservice.PasswordSetup, error)) *MockV2UsersApiServicer_PostUsersSetup_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersVerify provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostUsersVerify(_a0 context.Context, _a1 accountservice.Verification) (accountservice.VerifyEmailResponse, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersVerify")
	}

	var r0 accountservice.VerifyEmailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.Verification) (accountservice.VerifyEmailResponse, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.Verification) accountservice.VerifyEmailResponse); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(accountservice.VerifyEmailResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, accountservice.Verification) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostUsersVerify_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersVerify'
type MockV2UsersApiServicer_PostUsersVerify_Call struct {
	*mock.Call
}

// PostUsersVerify is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.Verification
func (_e *MockV2UsersApiServicer_Expecter) PostUsersVerify(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostUsersVerify_Call {
	return &MockV2UsersApiServicer_PostUsersVerify_Call{Call: _e.mock.On("PostUsersVerify", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostUsersVerify_Call) Run(run func(_a0 context.Context, _a1 accountservice.Verification)) *MockV2UsersApiServicer_PostUsersVerify_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.Verification))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersVerify_Call) Return(_a0 accountservice.VerifyEmailResponse, _a1 error) *MockV2UsersApiServicer_PostUsersVerify_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostUsersVerify_Call) RunAndReturn(run func(context.Context, accountservice.Verification) (accountservice.VerifyEmailResponse, error)) *MockV2UsersApiServicer_PostUsersVerify_Call {
	_c.Call.Return(run)
	return _c
}

// PostVerificationCode provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockV2UsersApiServicer) PostVerificationCode(_a0 context.Context, _a1 accountservice.VerificationCode, _a2 string) (string, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostVerificationCode")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.VerificationCode, string) (string, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.VerificationCode, string) string); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, accountservice.VerificationCode, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostVerificationCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostVerificationCode'
type MockV2UsersApiServicer_PostVerificationCode_Call struct {
	*mock.Call
}

// PostVerificationCode is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.VerificationCode
//   - _a2 string
func (_e *MockV2UsersApiServicer_Expecter) PostVerificationCode(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockV2UsersApiServicer_PostVerificationCode_Call {
	return &MockV2UsersApiServicer_PostVerificationCode_Call{Call: _e.mock.On("PostVerificationCode", _a0, _a1, _a2)}
}

func (_c *MockV2UsersApiServicer_PostVerificationCode_Call) Run(run func(_a0 context.Context, _a1 accountservice.VerificationCode, _a2 string)) *MockV2UsersApiServicer_PostVerificationCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.VerificationCode), args[2].(string))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostVerificationCode_Call) Return(_a0 string, _a1 error) *MockV2UsersApiServicer_PostVerificationCode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostVerificationCode_Call) RunAndReturn(run func(context.Context, accountservice.VerificationCode, string) (string, error)) *MockV2UsersApiServicer_PostVerificationCode_Call {
	_c.Call.Return(run)
	return _c
}

// PostVerifyDateOfBirth provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PostVerifyDateOfBirth(_a0 context.Context, _a1 accountservice.DateOfBirthVerification) (bool, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostVerifyDateOfBirth")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.DateOfBirthVerification) (bool, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.DateOfBirthVerification) bool); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, accountservice.DateOfBirthVerification) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockV2UsersApiServicer_PostVerifyDateOfBirth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostVerifyDateOfBirth'
type MockV2UsersApiServicer_PostVerifyDateOfBirth_Call struct {
	*mock.Call
}

// PostVerifyDateOfBirth is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.DateOfBirthVerification
func (_e *MockV2UsersApiServicer_Expecter) PostVerifyDateOfBirth(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call {
	return &MockV2UsersApiServicer_PostVerifyDateOfBirth_Call{Call: _e.mock.On("PostVerifyDateOfBirth", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call) Run(run func(_a0 context.Context, _a1 accountservice.DateOfBirthVerification)) *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.DateOfBirthVerification))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call) Return(_a0 bool, _a1 error) *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call) RunAndReturn(run func(context.Context, accountservice.DateOfBirthVerification) (bool, error)) *MockV2UsersApiServicer_PostVerifyDateOfBirth_Call {
	_c.Call.Return(run)
	return _c
}

// PutUsersEmail provides a mock function with given fields: _a0, _a1
func (_m *MockV2UsersApiServicer) PutUsersEmail(_a0 context.Context, _a1 accountservice.UsersEmailUpdate) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PutUsersEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.UsersEmailUpdate) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockV2UsersApiServicer_PutUsersEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutUsersEmail'
type MockV2UsersApiServicer_PutUsersEmail_Call struct {
	*mock.Call
}

// PutUsersEmail is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.UsersEmailUpdate
func (_e *MockV2UsersApiServicer_Expecter) PutUsersEmail(_a0 interface{}, _a1 interface{}) *MockV2UsersApiServicer_PutUsersEmail_Call {
	return &MockV2UsersApiServicer_PutUsersEmail_Call{Call: _e.mock.On("PutUsersEmail", _a0, _a1)}
}

func (_c *MockV2UsersApiServicer_PutUsersEmail_Call) Run(run func(_a0 context.Context, _a1 accountservice.UsersEmailUpdate)) *MockV2UsersApiServicer_PutUsersEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.UsersEmailUpdate))
	})
	return _c
}

func (_c *MockV2UsersApiServicer_PutUsersEmail_Call) Return(_a0 error) *MockV2UsersApiServicer_PutUsersEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockV2UsersApiServicer_PutUsersEmail_Call) RunAndReturn(run func(context.Context, accountservice.UsersEmailUpdate) error) *MockV2UsersApiServicer_PutUsersEmail_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockV2UsersApiServicer creates a new instance of MockV2UsersApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockV2UsersApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockV2UsersApiServicer {
	mock := &MockV2UsersApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
