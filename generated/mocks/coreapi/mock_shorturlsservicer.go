// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockShortUrlsServicer is an autogenerated mock type for the ShortUrlsServicer type
type MockShortUrlsServicer struct {
	mock.Mock
}

type MockShortUrlsServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShortUrlsServicer) EXPECT() *MockShortUrlsServicer_Expecter {
	return &MockShortUrlsServicer_Expecter{mock: &_m.Mock}
}

// GetOriginalUrl provides a mock function with given fields: _a0, _a1
func (_m *MockShortUrlsServicer) GetOriginalUrl(_a0 context.Context, _a1 string) (string, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetOriginalUrl")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockShortUrlsServicer_GetOriginalUrl_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOriginalUrl'
type MockShortUrlsServicer_GetOriginalUrl_Call struct {
	*mock.Call
}

// GetOriginalUrl is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockShortUrlsServicer_Expecter) GetOriginalUrl(_a0 interface{}, _a1 interface{}) *MockShortUrlsServicer_GetOriginalUrl_Call {
	return &MockShortUrlsServicer_GetOriginalUrl_Call{Call: _e.mock.On("GetOriginalUrl", _a0, _a1)}
}

func (_c *MockShortUrlsServicer_GetOriginalUrl_Call) Run(run func(_a0 context.Context, _a1 string)) *MockShortUrlsServicer_GetOriginalUrl_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockShortUrlsServicer_GetOriginalUrl_Call) Return(_a0 string, _a1 error) *MockShortUrlsServicer_GetOriginalUrl_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockShortUrlsServicer_GetOriginalUrl_Call) RunAndReturn(run func(context.Context, string) (string, error)) *MockShortUrlsServicer_GetOriginalUrl_Call {
	_c.Call.Return(run)
	return _c
}

// PostShortUrl provides a mock function with given fields: _a0, _a1
func (_m *MockShortUrlsServicer) PostShortUrl(_a0 context.Context, _a1 string) (string, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostShortUrl")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockShortUrlsServicer_PostShortUrl_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostShortUrl'
type MockShortUrlsServicer_PostShortUrl_Call struct {
	*mock.Call
}

// PostShortUrl is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockShortUrlsServicer_Expecter) PostShortUrl(_a0 interface{}, _a1 interface{}) *MockShortUrlsServicer_PostShortUrl_Call {
	return &MockShortUrlsServicer_PostShortUrl_Call{Call: _e.mock.On("PostShortUrl", _a0, _a1)}
}

func (_c *MockShortUrlsServicer_PostShortUrl_Call) Run(run func(_a0 context.Context, _a1 string)) *MockShortUrlsServicer_PostShortUrl_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockShortUrlsServicer_PostShortUrl_Call) Return(_a0 string, _a1 error) *MockShortUrlsServicer_PostShortUrl_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockShortUrlsServicer_PostShortUrl_Call) RunAndReturn(run func(context.Context, string) (string, error)) *MockShortUrlsServicer_PostShortUrl_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockShortUrlsServicer creates a new instance of MockShortUrlsServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShortUrlsServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShortUrlsServicer {
	mock := &MockShortUrlsServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
