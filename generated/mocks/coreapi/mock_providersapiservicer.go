// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	time "time"
)

// MockProvidersApiServicer is an autogenerated mock type for the ProvidersApiServicer type
type MockProvidersApiServicer struct {
	mock.Mock
}

type MockProvidersApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockProvidersApiServicer) EXPECT() *MockProvidersApiServicer_Expecter {
	return &MockProvidersApiServicer_Expecter{mock: &_m.Mock}
}

// GetProviderById provides a mock function with given fields: _a0, _a1
func (_m *MockProvidersApiServicer) GetProviderById(_a0 context.Context, _a1 int64) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderById")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (interface{}, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviderById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderById'
type MockProvidersApiServicer_GetProviderById_Call struct {
	*mock.Call
}

// GetProviderById is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 int64
func (_e *MockProvidersApiServicer_Expecter) GetProviderById(_a0 interface{}, _a1 interface{}) *MockProvidersApiServicer_GetProviderById_Call {
	return &MockProvidersApiServicer_GetProviderById_Call{Call: _e.mock.On("GetProviderById", _a0, _a1)}
}

func (_c *MockProvidersApiServicer_GetProviderById_Call) Run(run func(_a0 context.Context, _a1 int64)) *MockProvidersApiServicer_GetProviderById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderById_Call) Return(_a0 interface{}, _a1 error) *MockProvidersApiServicer_GetProviderById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderById_Call) RunAndReturn(run func(context.Context, int64) (interface{}, error)) *MockProvidersApiServicer_GetProviderById_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderByUrl provides a mock function with given fields: _a0, _a1
func (_m *MockProvidersApiServicer) GetProviderByUrl(_a0 context.Context, _a1 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderByUrl")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (interface{}, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviderByUrl_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderByUrl'
type MockProvidersApiServicer_GetProviderByUrl_Call struct {
	*mock.Call
}

// GetProviderByUrl is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockProvidersApiServicer_Expecter) GetProviderByUrl(_a0 interface{}, _a1 interface{}) *MockProvidersApiServicer_GetProviderByUrl_Call {
	return &MockProvidersApiServicer_GetProviderByUrl_Call{Call: _e.mock.On("GetProviderByUrl", _a0, _a1)}
}

func (_c *MockProvidersApiServicer_GetProviderByUrl_Call) Run(run func(_a0 context.Context, _a1 string)) *MockProvidersApiServicer_GetProviderByUrl_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderByUrl_Call) Return(_a0 interface{}, _a1 error) *MockProvidersApiServicer_GetProviderByUrl_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderByUrl_Call) RunAndReturn(run func(context.Context, string) (interface{}, error)) *MockProvidersApiServicer_GetProviderByUrl_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderConfig provides a mock function with given fields: _a0
func (_m *MockProvidersApiServicer) GetProviderConfig(_a0 context.Context) (coreapi.ProviderConfig, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderConfig")
	}

	var r0 coreapi.ProviderConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (coreapi.ProviderConfig, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(context.Context) coreapi.ProviderConfig); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(coreapi.ProviderConfig)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviderConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderConfig'
type MockProvidersApiServicer_GetProviderConfig_Call struct {
	*mock.Call
}

// GetProviderConfig is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *MockProvidersApiServicer_Expecter) GetProviderConfig(_a0 interface{}) *MockProvidersApiServicer_GetProviderConfig_Call {
	return &MockProvidersApiServicer_GetProviderConfig_Call{Call: _e.mock.On("GetProviderConfig", _a0)}
}

func (_c *MockProvidersApiServicer_GetProviderConfig_Call) Run(run func(_a0 context.Context)) *MockProvidersApiServicer_GetProviderConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderConfig_Call) Return(_a0 coreapi.ProviderConfig, _a1 error) *MockProvidersApiServicer_GetProviderConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderConfig_Call) RunAndReturn(run func(context.Context) (coreapi.ProviderConfig, error)) *MockProvidersApiServicer_GetProviderConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderConsentData provides a mock function with given fields: _a0, _a1
func (_m *MockProvidersApiServicer) GetProviderConsentData(_a0 context.Context, _a1 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderConsentData")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (interface{}, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviderConsentData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderConsentData'
type MockProvidersApiServicer_GetProviderConsentData_Call struct {
	*mock.Call
}

// GetProviderConsentData is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockProvidersApiServicer_Expecter) GetProviderConsentData(_a0 interface{}, _a1 interface{}) *MockProvidersApiServicer_GetProviderConsentData_Call {
	return &MockProvidersApiServicer_GetProviderConsentData_Call{Call: _e.mock.On("GetProviderConsentData", _a0, _a1)}
}

func (_c *MockProvidersApiServicer_GetProviderConsentData_Call) Run(run func(_a0 context.Context, _a1 string)) *MockProvidersApiServicer_GetProviderConsentData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderConsentData_Call) Return(_a0 interface{}, _a1 error) *MockProvidersApiServicer_GetProviderConsentData_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderConsentData_Call) RunAndReturn(run func(context.Context, string) (interface{}, error)) *MockProvidersApiServicer_GetProviderConsentData_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviderFormConfig provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockProvidersApiServicer) GetProviderFormConfig(_a0 context.Context, _a1 int64, _a2 bool, _a3 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for GetProviderFormConfig")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, bool, string) (interface{}, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, bool, string) interface{}); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, bool, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviderFormConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviderFormConfig'
type MockProvidersApiServicer_GetProviderFormConfig_Call struct {
	*mock.Call
}

// GetProviderFormConfig is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 int64
//   - _a2 bool
//   - _a3 string
func (_e *MockProvidersApiServicer_Expecter) GetProviderFormConfig(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockProvidersApiServicer_GetProviderFormConfig_Call {
	return &MockProvidersApiServicer_GetProviderFormConfig_Call{Call: _e.mock.On("GetProviderFormConfig", _a0, _a1, _a2, _a3)}
}

func (_c *MockProvidersApiServicer_GetProviderFormConfig_Call) Run(run func(_a0 context.Context, _a1 int64, _a2 bool, _a3 string)) *MockProvidersApiServicer_GetProviderFormConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(bool), args[3].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderFormConfig_Call) Return(_a0 interface{}, _a1 error) *MockProvidersApiServicer_GetProviderFormConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviderFormConfig_Call) RunAndReturn(run func(context.Context, int64, bool, string) (interface{}, error)) *MockProvidersApiServicer_GetProviderFormConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetProviders provides a mock function with given fields: _a0, _a1
func (_m *MockProvidersApiServicer) GetProviders(_a0 context.Context, _a1 string) ([]coreapi.Provider, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetProviders")
	}

	var r0 []coreapi.Provider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]coreapi.Provider, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []coreapi.Provider); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coreapi.Provider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_GetProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProviders'
type MockProvidersApiServicer_GetProviders_Call struct {
	*mock.Call
}

// GetProviders is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockProvidersApiServicer_Expecter) GetProviders(_a0 interface{}, _a1 interface{}) *MockProvidersApiServicer_GetProviders_Call {
	return &MockProvidersApiServicer_GetProviders_Call{Call: _e.mock.On("GetProviders", _a0, _a1)}
}

func (_c *MockProvidersApiServicer_GetProviders_Call) Run(run func(_a0 context.Context, _a1 string)) *MockProvidersApiServicer_GetProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_GetProviders_Call) Return(_a0 []coreapi.Provider, _a1 error) *MockProvidersApiServicer_GetProviders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_GetProviders_Call) RunAndReturn(run func(context.Context, string) ([]coreapi.Provider, error)) *MockProvidersApiServicer_GetProviders_Call {
	_c.Call.Return(run)
	return _c
}

// IsAppointmentReminder provides a mock function with given fields: _a0, _a1
func (_m *MockProvidersApiServicer) IsAppointmentReminder(_a0 context.Context, _a1 string) (bool, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for IsAppointmentReminder")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_IsAppointmentReminder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAppointmentReminder'
type MockProvidersApiServicer_IsAppointmentReminder_Call struct {
	*mock.Call
}

// IsAppointmentReminder is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockProvidersApiServicer_Expecter) IsAppointmentReminder(_a0 interface{}, _a1 interface{}) *MockProvidersApiServicer_IsAppointmentReminder_Call {
	return &MockProvidersApiServicer_IsAppointmentReminder_Call{Call: _e.mock.On("IsAppointmentReminder", _a0, _a1)}
}

func (_c *MockProvidersApiServicer_IsAppointmentReminder_Call) Run(run func(_a0 context.Context, _a1 string)) *MockProvidersApiServicer_IsAppointmentReminder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_IsAppointmentReminder_Call) Return(_a0 bool, _a1 error) *MockProvidersApiServicer_IsAppointmentReminder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_IsAppointmentReminder_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *MockProvidersApiServicer_IsAppointmentReminder_Call {
	_c.Call.Return(run)
	return _c
}

// PostProviderConsent provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockProvidersApiServicer) PostProviderConsent(_a0 context.Context, _a1 string, _a2 coreapi.Consent, _a3 string) ([]byte, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for PostProviderConsent")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.Consent, string) ([]byte, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.Consent, string) []byte); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, coreapi.Consent, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_PostProviderConsent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostProviderConsent'
type MockProvidersApiServicer_PostProviderConsent_Call struct {
	*mock.Call
}

// PostProviderConsent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 coreapi.Consent
//   - _a3 string
func (_e *MockProvidersApiServicer_Expecter) PostProviderConsent(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockProvidersApiServicer_PostProviderConsent_Call {
	return &MockProvidersApiServicer_PostProviderConsent_Call{Call: _e.mock.On("PostProviderConsent", _a0, _a1, _a2, _a3)}
}

func (_c *MockProvidersApiServicer_PostProviderConsent_Call) Run(run func(_a0 context.Context, _a1 string, _a2 coreapi.Consent, _a3 string)) *MockProvidersApiServicer_PostProviderConsent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(coreapi.Consent), args[3].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_PostProviderConsent_Call) Return(_a0 []byte, _a1 error) *MockProvidersApiServicer_PostProviderConsent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_PostProviderConsent_Call) RunAndReturn(run func(context.Context, string, coreapi.Consent, string) ([]byte, error)) *MockProvidersApiServicer_PostProviderConsent_Call {
	_c.Call.Return(run)
	return _c
}

// PostProviderConsentVerification provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockProvidersApiServicer) PostProviderConsentVerification(_a0 context.Context, _a1 string, _a2 string) (coreapi.ConsentEmailVerification, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostProviderConsentVerification")
	}

	var r0 coreapi.ConsentEmailVerification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (coreapi.ConsentEmailVerification, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) coreapi.ConsentEmailVerification); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(coreapi.ConsentEmailVerification)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_PostProviderConsentVerification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostProviderConsentVerification'
type MockProvidersApiServicer_PostProviderConsentVerification_Call struct {
	*mock.Call
}

// PostProviderConsentVerification is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockProvidersApiServicer_Expecter) PostProviderConsentVerification(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockProvidersApiServicer_PostProviderConsentVerification_Call {
	return &MockProvidersApiServicer_PostProviderConsentVerification_Call{Call: _e.mock.On("PostProviderConsentVerification", _a0, _a1, _a2)}
}

func (_c *MockProvidersApiServicer_PostProviderConsentVerification_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockProvidersApiServicer_PostProviderConsentVerification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_PostProviderConsentVerification_Call) Return(_a0 coreapi.ConsentEmailVerification, _a1 error) *MockProvidersApiServicer_PostProviderConsentVerification_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_PostProviderConsentVerification_Call) RunAndReturn(run func(context.Context, string, string) (coreapi.ConsentEmailVerification, error)) *MockProvidersApiServicer_PostProviderConsentVerification_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyProviderConsent provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockProvidersApiServicer) VerifyProviderConsent(_a0 context.Context, _a1 string, _a2 time.Time, _a3 string) (*coreapi.VerifiedConsent, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for VerifyProviderConsent")
	}

	var r0 *coreapi.VerifiedConsent
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, string) (*coreapi.VerifiedConsent, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, string) *coreapi.VerifiedConsent); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coreapi.VerifiedConsent)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, time.Time, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvidersApiServicer_VerifyProviderConsent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyProviderConsent'
type MockProvidersApiServicer_VerifyProviderConsent_Call struct {
	*mock.Call
}

// VerifyProviderConsent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 time.Time
//   - _a3 string
func (_e *MockProvidersApiServicer_Expecter) VerifyProviderConsent(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockProvidersApiServicer_VerifyProviderConsent_Call {
	return &MockProvidersApiServicer_VerifyProviderConsent_Call{Call: _e.mock.On("VerifyProviderConsent", _a0, _a1, _a2, _a3)}
}

func (_c *MockProvidersApiServicer_VerifyProviderConsent_Call) Run(run func(_a0 context.Context, _a1 string, _a2 time.Time, _a3 string)) *MockProvidersApiServicer_VerifyProviderConsent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Time), args[3].(string))
	})
	return _c
}

func (_c *MockProvidersApiServicer_VerifyProviderConsent_Call) Return(_a0 *coreapi.VerifiedConsent, _a1 error) *MockProvidersApiServicer_VerifyProviderConsent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvidersApiServicer_VerifyProviderConsent_Call) RunAndReturn(run func(context.Context, string, time.Time, string) (*coreapi.VerifiedConsent, error)) *MockProvidersApiServicer_VerifyProviderConsent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockProvidersApiServicer creates a new instance of MockProvidersApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockProvidersApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProvidersApiServicer {
	mock := &MockProvidersApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
