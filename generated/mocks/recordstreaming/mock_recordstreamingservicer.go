// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockrecordstreaming

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// MockRecordStreamingServicer is an autogenerated mock type for the RecordStreamingServicer type
type MockRecordStreamingServicer struct {
	mock.Mock
}

type MockRecordStreamingServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRecordStreamingServicer) EXPECT() *MockRecordStreamingServicer_Expecter {
	return &MockRecordStreamingServicer_Expecter{mock: &_m.Mock}
}

// CreateEUnityAccessTokenForStudy provides a mock function with given fields: ctx, physicianAccountID, studyUID, providerID
func (_m *MockRecordStreamingServicer) CreateEUnityAccessTokenForStudy(ctx context.Context, physicianAccountID string, studyUID string, providerID int64) (string, error) {
	ret := _m.Called(ctx, physicianAccountID, studyUID, providerID)

	if len(ret) == 0 {
		panic("no return value specified for CreateEUnityAccessTokenForStudy")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) (string, error)); ok {
		return rf(ctx, physicianAccountID, studyUID, providerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) string); ok {
		r0 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEUnityAccessTokenForStudy'
type MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call struct {
	*mock.Call
}

// CreateEUnityAccessTokenForStudy is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
//   - studyUID string
//   - providerID int64
func (_e *MockRecordStreamingServicer_Expecter) CreateEUnityAccessTokenForStudy(ctx interface{}, physicianAccountID interface{}, studyUID interface{}, providerID interface{}) *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call {
	return &MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call{Call: _e.mock.On("CreateEUnityAccessTokenForStudy", ctx, physicianAccountID, studyUID, providerID)}
}

func (_c *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call) Run(run func(ctx context.Context, physicianAccountID string, studyUID string, providerID int64)) *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call) Return(_a0 string, _a1 error) *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call) RunAndReturn(run func(context.Context, string, string, int64) (string, error)) *MockRecordStreamingServicer_CreateEUnityAccessTokenForStudy_Call {
	_c.Call.Return(run)
	return _c
}

// GetShareForRecordStreamingStudiesForPhysician provides a mock function with given fields: ctx, physicianAccountID, studyUID, providerID
func (_m *MockRecordStreamingServicer) GetShareForRecordStreamingStudiesForPhysician(ctx context.Context, physicianAccountID string, studyUID string, providerID int64) (coreapi.Share, error) {
	ret := _m.Called(ctx, physicianAccountID, studyUID, providerID)

	if len(ret) == 0 {
		panic("no return value specified for GetShareForRecordStreamingStudiesForPhysician")
	}

	var r0 coreapi.Share
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) (coreapi.Share, error)); ok {
		return rf(ctx, physicianAccountID, studyUID, providerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) coreapi.Share); ok {
		r0 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r0 = ret.Get(0).(coreapi.Share)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShareForRecordStreamingStudiesForPhysician'
type MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call struct {
	*mock.Call
}

// GetShareForRecordStreamingStudiesForPhysician is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
//   - studyUID string
//   - providerID int64
func (_e *MockRecordStreamingServicer_Expecter) GetShareForRecordStreamingStudiesForPhysician(ctx interface{}, physicianAccountID interface{}, studyUID interface{}, providerID interface{}) *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call {
	return &MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call{Call: _e.mock.On("GetShareForRecordStreamingStudiesForPhysician", ctx, physicianAccountID, studyUID, providerID)}
}

func (_c *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call) Run(run func(ctx context.Context, physicianAccountID string, studyUID string, providerID int64)) *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call) Return(_a0 coreapi.Share, _a1 error) *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call) RunAndReturn(run func(context.Context, string, string, int64) (coreapi.Share, error)) *MockRecordStreamingServicer_GetShareForRecordStreamingStudiesForPhysician_Call {
	_c.Call.Return(run)
	return _c
}

// GetShareMetadataForRecordStreamingStudies provides a mock function with given fields: ctx, physicianAccountID
func (_m *MockRecordStreamingServicer) GetShareMetadataForRecordStreamingStudies(ctx context.Context, physicianAccountID string) ([]coreapi.ShareMetadata, error) {
	ret := _m.Called(ctx, physicianAccountID)

	if len(ret) == 0 {
		panic("no return value specified for GetShareMetadataForRecordStreamingStudies")
	}

	var r0 []coreapi.ShareMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]coreapi.ShareMetadata, error)); ok {
		return rf(ctx, physicianAccountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []coreapi.ShareMetadata); ok {
		r0 = rf(ctx, physicianAccountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coreapi.ShareMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, physicianAccountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShareMetadataForRecordStreamingStudies'
type MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call struct {
	*mock.Call
}

// GetShareMetadataForRecordStreamingStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
func (_e *MockRecordStreamingServicer_Expecter) GetShareMetadataForRecordStreamingStudies(ctx interface{}, physicianAccountID interface{}) *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call {
	return &MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call{Call: _e.mock.On("GetShareMetadataForRecordStreamingStudies", ctx, physicianAccountID)}
}

func (_c *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call) Run(run func(ctx context.Context, physicianAccountID string)) *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call) Return(_a0 []coreapi.ShareMetadata, _a1 error) *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call) RunAndReturn(run func(context.Context, string) ([]coreapi.ShareMetadata, error)) *MockRecordStreamingServicer_GetShareMetadataForRecordStreamingStudies_Call {
	_c.Call.Return(run)
	return _c
}

// PhysicianCanAccessStudy provides a mock function with given fields: ctx, physicianAccountID, studyUID, providerID
func (_m *MockRecordStreamingServicer) PhysicianCanAccessStudy(ctx context.Context, physicianAccountID string, studyUID string, providerID int64) (bool, error) {
	ret := _m.Called(ctx, physicianAccountID, studyUID, providerID)

	if len(ret) == 0 {
		panic("no return value specified for PhysicianCanAccessStudy")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) (bool, error)); ok {
		return rf(ctx, physicianAccountID, studyUID, providerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64) bool); ok {
		r0 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = rf(ctx, physicianAccountID, studyUID, providerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordStreamingServicer_PhysicianCanAccessStudy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PhysicianCanAccessStudy'
type MockRecordStreamingServicer_PhysicianCanAccessStudy_Call struct {
	*mock.Call
}

// PhysicianCanAccessStudy is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
//   - studyUID string
//   - providerID int64
func (_e *MockRecordStreamingServicer_Expecter) PhysicianCanAccessStudy(ctx interface{}, physicianAccountID interface{}, studyUID interface{}, providerID interface{}) *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call {
	return &MockRecordStreamingServicer_PhysicianCanAccessStudy_Call{Call: _e.mock.On("PhysicianCanAccessStudy", ctx, physicianAccountID, studyUID, providerID)}
}

func (_c *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call) Run(run func(ctx context.Context, physicianAccountID string, studyUID string, providerID int64)) *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call) Return(_a0 bool, _a1 error) *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call) RunAndReturn(run func(context.Context, string, string, int64) (bool, error)) *MockRecordStreamingServicer_PhysicianCanAccessStudy_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRecordStreamingServicer creates a new instance of MockRecordStreamingServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRecordStreamingServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRecordStreamingServicer {
	mock := &MockRecordStreamingServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
