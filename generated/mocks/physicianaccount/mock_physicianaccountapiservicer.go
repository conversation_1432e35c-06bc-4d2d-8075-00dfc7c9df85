// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockphysicianaccount

import (
	api "gitlab.com/pockethealth/coreapi/generated/api"
	accountservice "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"

	context "context"

	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	mock "github.com/stretchr/testify/mock"

	modelphysicianaccount "gitlab.com/pockethealth/coreapi/pkg/models/modelphysicianaccount"

	physicianaccount "gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
)

// MockPhysicianAccountApiServicer is an autogenerated mock type for the PhysicianAccountApiServicer type
type MockPhysicianAccountApiServicer struct {
	mock.Mock
}

type MockPhysicianAccountApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPhysicianAccountApiServicer) EXPECT() *MockPhysicianAccountApiServicer_Expecter {
	return &MockPhysicianAccountApiServicer_Expecter{mock: &_m.Mock}
}

// GetEUnityTokenForShare provides a mock function with given fields: ctx, acctId, shareId
func (_m *MockPhysicianAccountApiServicer) GetEUnityTokenForShare(ctx context.Context, acctId string, shareId string) (string, error) {
	ret := _m.Called(ctx, acctId, shareId)

	if len(ret) == 0 {
		panic("no return value specified for GetEUnityTokenForShare")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (string, error)); ok {
		return rf(ctx, acctId, shareId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) string); ok {
		r0 = rf(ctx, acctId, shareId)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, acctId, shareId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEUnityTokenForShare'
type MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call struct {
	*mock.Call
}

// GetEUnityTokenForShare is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - shareId string
func (_e *MockPhysicianAccountApiServicer_Expecter) GetEUnityTokenForShare(ctx interface{}, acctId interface{}, shareId interface{}) *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call {
	return &MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call{Call: _e.mock.On("GetEUnityTokenForShare", ctx, acctId, shareId)}
}

func (_c *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call) Run(run func(ctx context.Context, acctId string, shareId string)) *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call) Return(_a0 string, _a1 error) *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call) RunAndReturn(run func(context.Context, string, string) (string, error)) *MockPhysicianAccountApiServicer_GetEUnityTokenForShare_Call {
	_c.Call.Return(run)
	return _c
}

// GetPatientSharedExams provides a mock function with given fields: ctx, acctId, patientId
func (_m *MockPhysicianAccountApiServicer) GetPatientSharedExams(ctx context.Context, acctId string, patientId string) (interface{}, error) {
	ret := _m.Called(ctx, acctId, patientId)

	if len(ret) == 0 {
		panic("no return value specified for GetPatientSharedExams")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (interface{}, error)); ok {
		return rf(ctx, acctId, patientId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) interface{}); ok {
		r0 = rf(ctx, acctId, patientId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, acctId, patientId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_GetPatientSharedExams_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPatientSharedExams'
type MockPhysicianAccountApiServicer_GetPatientSharedExams_Call struct {
	*mock.Call
}

// GetPatientSharedExams is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - patientId string
func (_e *MockPhysicianAccountApiServicer_Expecter) GetPatientSharedExams(ctx interface{}, acctId interface{}, patientId interface{}) *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call {
	return &MockPhysicianAccountApiServicer_GetPatientSharedExams_Call{Call: _e.mock.On("GetPatientSharedExams", ctx, acctId, patientId)}
}

func (_c *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call) Run(run func(ctx context.Context, acctId string, patientId string)) *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call) Return(_a0 interface{}, _a1 error) *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call) RunAndReturn(run func(context.Context, string, string) (interface{}, error)) *MockPhysicianAccountApiServicer_GetPatientSharedExams_Call {
	_c.Call.Return(run)
	return _c
}

// GetPhysicianAccount provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) GetPhysicianAccount(_a0 context.Context, _a1 string) (accountservice.PhysicianAccount, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetPhysicianAccount")
	}

	var r0 accountservice.PhysicianAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (accountservice.PhysicianAccount, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) accountservice.PhysicianAccount); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(accountservice.PhysicianAccount)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_GetPhysicianAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPhysicianAccount'
type MockPhysicianAccountApiServicer_GetPhysicianAccount_Call struct {
	*mock.Call
}

// GetPhysicianAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockPhysicianAccountApiServicer_Expecter) GetPhysicianAccount(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call {
	return &MockPhysicianAccountApiServicer_GetPhysicianAccount_Call{Call: _e.mock.On("GetPhysicianAccount", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call) Run(run func(_a0 context.Context, _a1 string)) *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call) Return(_a0 accountservice.PhysicianAccount, _a1 error) *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call) RunAndReturn(run func(context.Context, string) (accountservice.PhysicianAccount, error)) *MockPhysicianAccountApiServicer_GetPhysicianAccount_Call {
	_c.Call.Return(run)
	return _c
}

// GetRecordStreamingStudiesWithUploadStatus provides a mock function with given fields: ctx, physicianAccountID
func (_m *MockPhysicianAccountApiServicer) GetRecordStreamingStudiesWithUploadStatus(ctx context.Context, physicianAccountID string) ([]api.PhysicianRecordUploadStatus, error) {
	ret := _m.Called(ctx, physicianAccountID)

	if len(ret) == 0 {
		panic("no return value specified for GetRecordStreamingStudiesWithUploadStatus")
	}

	var r0 []api.PhysicianRecordUploadStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]api.PhysicianRecordUploadStatus, error)); ok {
		return rf(ctx, physicianAccountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []api.PhysicianRecordUploadStatus); ok {
		r0 = rf(ctx, physicianAccountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]api.PhysicianRecordUploadStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, physicianAccountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecordStreamingStudiesWithUploadStatus'
type MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call struct {
	*mock.Call
}

// GetRecordStreamingStudiesWithUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
func (_e *MockPhysicianAccountApiServicer_Expecter) GetRecordStreamingStudiesWithUploadStatus(ctx interface{}, physicianAccountID interface{}) *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call {
	return &MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call{Call: _e.mock.On("GetRecordStreamingStudiesWithUploadStatus", ctx, physicianAccountID)}
}

func (_c *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call) Run(run func(ctx context.Context, physicianAccountID string)) *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call) Return(_a0 []api.PhysicianRecordUploadStatus, _a1 error) *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call) RunAndReturn(run func(context.Context, string) ([]api.PhysicianRecordUploadStatus, error)) *MockPhysicianAccountApiServicer_GetRecordStreamingStudiesWithUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetShareMetadata provides a mock function with given fields: ctx, acctId
func (_m *MockPhysicianAccountApiServicer) GetShareMetadata(ctx context.Context, acctId string) ([]coreapi.ShareMetadata, error) {
	ret := _m.Called(ctx, acctId)

	if len(ret) == 0 {
		panic("no return value specified for GetShareMetadata")
	}

	var r0 []coreapi.ShareMetadata
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]coreapi.ShareMetadata, error)); ok {
		return rf(ctx, acctId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []coreapi.ShareMetadata); ok {
		r0 = rf(ctx, acctId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coreapi.ShareMetadata)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, acctId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_GetShareMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShareMetadata'
type MockPhysicianAccountApiServicer_GetShareMetadata_Call struct {
	*mock.Call
}

// GetShareMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
func (_e *MockPhysicianAccountApiServicer_Expecter) GetShareMetadata(ctx interface{}, acctId interface{}) *MockPhysicianAccountApiServicer_GetShareMetadata_Call {
	return &MockPhysicianAccountApiServicer_GetShareMetadata_Call{Call: _e.mock.On("GetShareMetadata", ctx, acctId)}
}

func (_c *MockPhysicianAccountApiServicer_GetShareMetadata_Call) Run(run func(ctx context.Context, acctId string)) *MockPhysicianAccountApiServicer_GetShareMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetShareMetadata_Call) Return(_a0 []coreapi.ShareMetadata, _a1 error) *MockPhysicianAccountApiServicer_GetShareMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_GetShareMetadata_Call) RunAndReturn(run func(context.Context, string) ([]coreapi.ShareMetadata, error)) *MockPhysicianAccountApiServicer_GetShareMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// PatchPhysician provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockPhysicianAccountApiServicer) PatchPhysician(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianRequest) error {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for PatchPhysician")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, accountservice.PhysicianRequest) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PatchPhysician_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchPhysician'
type MockPhysicianAccountApiServicer_PatchPhysician_Call struct {
	*mock.Call
}

// PatchPhysician is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
//   - _a3 accountservice.PhysicianRequest
func (_e *MockPhysicianAccountApiServicer_Expecter) PatchPhysician(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockPhysicianAccountApiServicer_PatchPhysician_Call {
	return &MockPhysicianAccountApiServicer_PatchPhysician_Call{Call: _e.mock.On("PatchPhysician", _a0, _a1, _a2, _a3)}
}

func (_c *MockPhysicianAccountApiServicer_PatchPhysician_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianRequest)) *MockPhysicianAccountApiServicer_PatchPhysician_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(accountservice.PhysicianRequest))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PatchPhysician_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PatchPhysician_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PatchPhysician_Call) RunAndReturn(run func(context.Context, string, string, accountservice.PhysicianRequest) error) *MockPhysicianAccountApiServicer_PatchPhysician_Call {
	_c.Call.Return(run)
	return _c
}

// PostCreateAccount provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) PostCreateAccount(_a0 context.Context, _a1 coreapi.RegisterData) (string, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostCreateAccount")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.RegisterData) (string, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.RegisterData) string); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, coreapi.RegisterData) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_PostCreateAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostCreateAccount'
type MockPhysicianAccountApiServicer_PostCreateAccount_Call struct {
	*mock.Call
}

// PostCreateAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 coreapi.RegisterData
func (_e *MockPhysicianAccountApiServicer_Expecter) PostCreateAccount(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_PostCreateAccount_Call {
	return &MockPhysicianAccountApiServicer_PostCreateAccount_Call{Call: _e.mock.On("PostCreateAccount", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_PostCreateAccount_Call) Run(run func(_a0 context.Context, _a1 coreapi.RegisterData)) *MockPhysicianAccountApiServicer_PostCreateAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(coreapi.RegisterData))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostCreateAccount_Call) Return(_a0 string, _a1 error) *MockPhysicianAccountApiServicer_PostCreateAccount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostCreateAccount_Call) RunAndReturn(run func(context.Context, coreapi.RegisterData) (string, error)) *MockPhysicianAccountApiServicer_PostCreateAccount_Call {
	_c.Call.Return(run)
	return _c
}

// PostLogin provides a mock function with given fields: ctx, email, password, ip
func (_m *MockPhysicianAccountApiServicer) PostLogin(ctx context.Context, email string, password string, ip string) (interface{}, string, error) {
	ret := _m.Called(ctx, email, password, ip)

	if len(ret) == 0 {
		panic("no return value specified for PostLogin")
	}

	var r0 interface{}
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (interface{}, string, error)); ok {
		return rf(ctx, email, password, ip)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) interface{}); ok {
		r0 = rf(ctx, email, password, ip)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) string); ok {
		r1 = rf(ctx, email, password, ip)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, string) error); ok {
		r2 = rf(ctx, email, password, ip)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockPhysicianAccountApiServicer_PostLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostLogin'
type MockPhysicianAccountApiServicer_PostLogin_Call struct {
	*mock.Call
}

// PostLogin is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
//   - password string
//   - ip string
func (_e *MockPhysicianAccountApiServicer_Expecter) PostLogin(ctx interface{}, email interface{}, password interface{}, ip interface{}) *MockPhysicianAccountApiServicer_PostLogin_Call {
	return &MockPhysicianAccountApiServicer_PostLogin_Call{Call: _e.mock.On("PostLogin", ctx, email, password, ip)}
}

func (_c *MockPhysicianAccountApiServicer_PostLogin_Call) Run(run func(ctx context.Context, email string, password string, ip string)) *MockPhysicianAccountApiServicer_PostLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostLogin_Call) Return(_a0 interface{}, _a1 string, _a2 error) *MockPhysicianAccountApiServicer_PostLogin_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostLogin_Call) RunAndReturn(run func(context.Context, string, string, string) (interface{}, string, error)) *MockPhysicianAccountApiServicer_PostLogin_Call {
	_c.Call.Return(run)
	return _c
}

// PostLogout provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) PostLogout(_a0 context.Context, _a1 string) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostLogout")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostLogout_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostLogout'
type MockPhysicianAccountApiServicer_PostLogout_Call struct {
	*mock.Call
}

// PostLogout is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockPhysicianAccountApiServicer_Expecter) PostLogout(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_PostLogout_Call {
	return &MockPhysicianAccountApiServicer_PostLogout_Call{Call: _e.mock.On("PostLogout", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_PostLogout_Call) Run(run func(_a0 context.Context, _a1 string)) *MockPhysicianAccountApiServicer_PostLogout_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostLogout_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostLogout_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostLogout_Call) RunAndReturn(run func(context.Context, string) error) *MockPhysicianAccountApiServicer_PostLogout_Call {
	_c.Call.Return(run)
	return _c
}

// PostPhysicianLicense provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockPhysicianAccountApiServicer) PostPhysicianLicense(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianLicenceRequest) error {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for PostPhysicianLicense")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, accountservice.PhysicianLicenceRequest) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostPhysicianLicense_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostPhysicianLicense'
type MockPhysicianAccountApiServicer_PostPhysicianLicense_Call struct {
	*mock.Call
}

// PostPhysicianLicense is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
//   - _a3 accountservice.PhysicianLicenceRequest
func (_e *MockPhysicianAccountApiServicer_Expecter) PostPhysicianLicense(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call {
	return &MockPhysicianAccountApiServicer_PostPhysicianLicense_Call{Call: _e.mock.On("PostPhysicianLicense", _a0, _a1, _a2, _a3)}
}

func (_c *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianLicenceRequest)) *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(accountservice.PhysicianLicenceRequest))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call) RunAndReturn(run func(context.Context, string, string, accountservice.PhysicianLicenceRequest) error) *MockPhysicianAccountApiServicer_PostPhysicianLicense_Call {
	_c.Call.Return(run)
	return _c
}

// PostResetPassword provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) PostResetPassword(_a0 context.Context, _a1 coreapi.PasswordResetInfo) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostResetPassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.PasswordResetInfo) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostResetPassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostResetPassword'
type MockPhysicianAccountApiServicer_PostResetPassword_Call struct {
	*mock.Call
}

// PostResetPassword is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 coreapi.PasswordResetInfo
func (_e *MockPhysicianAccountApiServicer_Expecter) PostResetPassword(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_PostResetPassword_Call {
	return &MockPhysicianAccountApiServicer_PostResetPassword_Call{Call: _e.mock.On("PostResetPassword", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_PostResetPassword_Call) Run(run func(_a0 context.Context, _a1 coreapi.PasswordResetInfo)) *MockPhysicianAccountApiServicer_PostResetPassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(coreapi.PasswordResetInfo))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostResetPassword_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostResetPassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostResetPassword_Call) RunAndReturn(run func(context.Context, coreapi.PasswordResetInfo) error) *MockPhysicianAccountApiServicer_PostResetPassword_Call {
	_c.Call.Return(run)
	return _c
}

// PostResetPasswordInit provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) PostResetPasswordInit(_a0 context.Context, _a1 string) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostResetPasswordInit")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostResetPasswordInit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostResetPasswordInit'
type MockPhysicianAccountApiServicer_PostResetPasswordInit_Call struct {
	*mock.Call
}

// PostResetPasswordInit is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockPhysicianAccountApiServicer_Expecter) PostResetPasswordInit(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call {
	return &MockPhysicianAccountApiServicer_PostResetPasswordInit_Call{Call: _e.mock.On("PostResetPasswordInit", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call) Run(run func(_a0 context.Context, _a1 string)) *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call) RunAndReturn(run func(context.Context, string) error) *MockPhysicianAccountApiServicer_PostResetPasswordInit_Call {
	_c.Call.Return(run)
	return _c
}

// PostVerifyAccount provides a mock function with given fields: _a0, _a1
func (_m *MockPhysicianAccountApiServicer) PostVerifyAccount(_a0 context.Context, _a1 accountservice.Verification) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PostVerifyAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, accountservice.Verification) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostVerifyAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostVerifyAccount'
type MockPhysicianAccountApiServicer_PostVerifyAccount_Call struct {
	*mock.Call
}

// PostVerifyAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 accountservice.Verification
func (_e *MockPhysicianAccountApiServicer_Expecter) PostVerifyAccount(_a0 interface{}, _a1 interface{}) *MockPhysicianAccountApiServicer_PostVerifyAccount_Call {
	return &MockPhysicianAccountApiServicer_PostVerifyAccount_Call{Call: _e.mock.On("PostVerifyAccount", _a0, _a1)}
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyAccount_Call) Run(run func(_a0 context.Context, _a1 accountservice.Verification)) *MockPhysicianAccountApiServicer_PostVerifyAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(accountservice.Verification))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyAccount_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostVerifyAccount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyAccount_Call) RunAndReturn(run func(context.Context, accountservice.Verification) error) *MockPhysicianAccountApiServicer_PostVerifyAccount_Call {
	_c.Call.Return(run)
	return _c
}

// PostVerifyPhysicianNotificationMethod provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockPhysicianAccountApiServicer) PostVerifyPhysicianNotificationMethod(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianNotificationRequest) error {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for PostVerifyPhysicianNotificationMethod")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, accountservice.PhysicianNotificationRequest) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostVerifyPhysicianNotificationMethod'
type MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call struct {
	*mock.Call
}

// PostVerifyPhysicianNotificationMethod is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
//   - _a3 accountservice.PhysicianNotificationRequest
func (_e *MockPhysicianAccountApiServicer_Expecter) PostVerifyPhysicianNotificationMethod(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call {
	return &MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call{Call: _e.mock.On("PostVerifyPhysicianNotificationMethod", _a0, _a1, _a2, _a3)}
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string, _a3 accountservice.PhysicianNotificationRequest)) *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(accountservice.PhysicianNotificationRequest))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call) RunAndReturn(run func(context.Context, string, string, accountservice.PhysicianNotificationRequest) error) *MockPhysicianAccountApiServicer_PostVerifyPhysicianNotificationMethod_Call {
	_c.Call.Return(run)
	return _c
}

// PutExtendShare provides a mock function with given fields: ctx, acctId, shareId
func (_m *MockPhysicianAccountApiServicer) PutExtendShare(ctx context.Context, acctId string, shareId string) error {
	ret := _m.Called(ctx, acctId, shareId)

	if len(ret) == 0 {
		panic("no return value specified for PutExtendShare")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, acctId, shareId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_PutExtendShare_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutExtendShare'
type MockPhysicianAccountApiServicer_PutExtendShare_Call struct {
	*mock.Call
}

// PutExtendShare is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - shareId string
func (_e *MockPhysicianAccountApiServicer_Expecter) PutExtendShare(ctx interface{}, acctId interface{}, shareId interface{}) *MockPhysicianAccountApiServicer_PutExtendShare_Call {
	return &MockPhysicianAccountApiServicer_PutExtendShare_Call{Call: _e.mock.On("PutExtendShare", ctx, acctId, shareId)}
}

func (_c *MockPhysicianAccountApiServicer_PutExtendShare_Call) Run(run func(ctx context.Context, acctId string, shareId string)) *MockPhysicianAccountApiServicer_PutExtendShare_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PutExtendShare_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_PutExtendShare_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_PutExtendShare_Call) RunAndReturn(run func(context.Context, string, string) error) *MockPhysicianAccountApiServicer_PutExtendShare_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRecords provides a mock function with given fields: ctx, acctId, request
func (_m *MockPhysicianAccountApiServicer) RequestRecords(ctx context.Context, acctId string, request physicianaccount.PhysicianRecordRequest) ([]modelphysicianaccount.StudyUploadResponse, error) {
	ret := _m.Called(ctx, acctId, request)

	if len(ret) == 0 {
		panic("no return value specified for RequestRecords")
	}

	var r0 []modelphysicianaccount.StudyUploadResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, physicianaccount.PhysicianRecordRequest) ([]modelphysicianaccount.StudyUploadResponse, error)); ok {
		return rf(ctx, acctId, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, physicianaccount.PhysicianRecordRequest) []modelphysicianaccount.StudyUploadResponse); ok {
		r0 = rf(ctx, acctId, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modelphysicianaccount.StudyUploadResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, physicianaccount.PhysicianRecordRequest) error); ok {
		r1 = rf(ctx, acctId, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_RequestRecords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRecords'
type MockPhysicianAccountApiServicer_RequestRecords_Call struct {
	*mock.Call
}

// RequestRecords is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - request physicianaccount.PhysicianRecordRequest
func (_e *MockPhysicianAccountApiServicer_Expecter) RequestRecords(ctx interface{}, acctId interface{}, request interface{}) *MockPhysicianAccountApiServicer_RequestRecords_Call {
	return &MockPhysicianAccountApiServicer_RequestRecords_Call{Call: _e.mock.On("RequestRecords", ctx, acctId, request)}
}

func (_c *MockPhysicianAccountApiServicer_RequestRecords_Call) Run(run func(ctx context.Context, acctId string, request physicianaccount.PhysicianRecordRequest)) *MockPhysicianAccountApiServicer_RequestRecords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(physicianaccount.PhysicianRecordRequest))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_RequestRecords_Call) Return(_a0 []modelphysicianaccount.StudyUploadResponse, _a1 error) *MockPhysicianAccountApiServicer_RequestRecords_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_RequestRecords_Call) RunAndReturn(run func(context.Context, string, physicianaccount.PhysicianRecordRequest) ([]modelphysicianaccount.StudyUploadResponse, error)) *MockPhysicianAccountApiServicer_RequestRecords_Call {
	_c.Call.Return(run)
	return _c
}

// SearchRecords provides a mock function with given fields: ctx, acctId, searchParams
func (_m *MockPhysicianAccountApiServicer) SearchRecords(ctx context.Context, acctId string, searchParams physicianaccount.PhysicianRecordSearchParameters) (string, error) {
	ret := _m.Called(ctx, acctId, searchParams)

	if len(ret) == 0 {
		panic("no return value specified for SearchRecords")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, physicianaccount.PhysicianRecordSearchParameters) (string, error)); ok {
		return rf(ctx, acctId, searchParams)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, physicianaccount.PhysicianRecordSearchParameters) string); ok {
		r0 = rf(ctx, acctId, searchParams)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, physicianaccount.PhysicianRecordSearchParameters) error); ok {
		r1 = rf(ctx, acctId, searchParams)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPhysicianAccountApiServicer_SearchRecords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchRecords'
type MockPhysicianAccountApiServicer_SearchRecords_Call struct {
	*mock.Call
}

// SearchRecords is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - searchParams physicianaccount.PhysicianRecordSearchParameters
func (_e *MockPhysicianAccountApiServicer_Expecter) SearchRecords(ctx interface{}, acctId interface{}, searchParams interface{}) *MockPhysicianAccountApiServicer_SearchRecords_Call {
	return &MockPhysicianAccountApiServicer_SearchRecords_Call{Call: _e.mock.On("SearchRecords", ctx, acctId, searchParams)}
}

func (_c *MockPhysicianAccountApiServicer_SearchRecords_Call) Run(run func(ctx context.Context, acctId string, searchParams physicianaccount.PhysicianRecordSearchParameters)) *MockPhysicianAccountApiServicer_SearchRecords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(physicianaccount.PhysicianRecordSearchParameters))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_SearchRecords_Call) Return(_a0 string, _a1 error) *MockPhysicianAccountApiServicer_SearchRecords_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_SearchRecords_Call) RunAndReturn(run func(context.Context, string, physicianaccount.PhysicianRecordSearchParameters) (string, error)) *MockPhysicianAccountApiServicer_SearchRecords_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitStudyAccessVerification provides a mock function with given fields: ctx, physicianAccountID, studyUID, providerID, shareID
func (_m *MockPhysicianAccountApiServicer) SubmitStudyAccessVerification(ctx context.Context, physicianAccountID string, studyUID string, providerID int64, shareID string) error {
	ret := _m.Called(ctx, physicianAccountID, studyUID, providerID, shareID)

	if len(ret) == 0 {
		panic("no return value specified for SubmitStudyAccessVerification")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int64, string) error); ok {
		r0 = rf(ctx, physicianAccountID, studyUID, providerID, shareID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitStudyAccessVerification'
type MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call struct {
	*mock.Call
}

// SubmitStudyAccessVerification is a helper method to define mock.On call
//   - ctx context.Context
//   - physicianAccountID string
//   - studyUID string
//   - providerID int64
//   - shareID string
func (_e *MockPhysicianAccountApiServicer_Expecter) SubmitStudyAccessVerification(ctx interface{}, physicianAccountID interface{}, studyUID interface{}, providerID interface{}, shareID interface{}) *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call {
	return &MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call{Call: _e.mock.On("SubmitStudyAccessVerification", ctx, physicianAccountID, studyUID, providerID, shareID)}
}

func (_c *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call) Run(run func(ctx context.Context, physicianAccountID string, studyUID string, providerID int64, shareID string)) *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64), args[4].(string))
	})
	return _c
}

func (_c *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call) Return(_a0 error) *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call) RunAndReturn(run func(context.Context, string, string, int64, string) error) *MockPhysicianAccountApiServicer_SubmitStudyAccessVerification_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPhysicianAccountApiServicer creates a new instance of MockPhysicianAccountApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPhysicianAccountApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPhysicianAccountApiServicer {
	mock := &MockPhysicianAccountApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
