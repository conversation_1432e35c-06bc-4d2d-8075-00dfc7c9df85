// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockpubsub

import (
	context "context"

	azservicebus "github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"

	mock "github.com/stretchr/testify/mock"
)

// MockTopicSender is an autogenerated mock type for the TopicSender type
type MockTopicSender struct {
	mock.Mock
}

type MockTopicSender_Expecter struct {
	mock *mock.Mock
}

func (_m *MockTopicSender) EXPECT() *MockTopicSender_Expecter {
	return &MockTopicSender_Expecter{mock: &_m.Mock}
}

// SendMessage provides a mock function with given fields: ctx, message, options
func (_m *MockTopicSender) SendMessage(ctx context.Context, message *azservicebus.Message, options *azservicebus.SendMessageOptions) error {
	ret := _m.Called(ctx, message, options)

	if len(ret) == 0 {
		panic("no return value specified for SendMessage")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *azservicebus.Message, *azservicebus.SendMessageOptions) error); ok {
		r0 = rf(ctx, message, options)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockTopicSender_SendMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMessage'
type MockTopicSender_SendMessage_Call struct {
	*mock.Call
}

// SendMessage is a helper method to define mock.On call
//   - ctx context.Context
//   - message *azservicebus.Message
//   - options *azservicebus.SendMessageOptions
func (_e *MockTopicSender_Expecter) SendMessage(ctx interface{}, message interface{}, options interface{}) *MockTopicSender_SendMessage_Call {
	return &MockTopicSender_SendMessage_Call{Call: _e.mock.On("SendMessage", ctx, message, options)}
}

func (_c *MockTopicSender_SendMessage_Call) Run(run func(ctx context.Context, message *azservicebus.Message, options *azservicebus.SendMessageOptions)) *MockTopicSender_SendMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*azservicebus.Message), args[2].(*azservicebus.SendMessageOptions))
	})
	return _c
}

func (_c *MockTopicSender_SendMessage_Call) Return(_a0 error) *MockTopicSender_SendMessage_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockTopicSender_SendMessage_Call) RunAndReturn(run func(context.Context, *azservicebus.Message, *azservicebus.SendMessageOptions) error) *MockTopicSender_SendMessage_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockTopicSender creates a new instance of MockTopicSender. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockTopicSender(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockTopicSender {
	mock := &MockTopicSender{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
