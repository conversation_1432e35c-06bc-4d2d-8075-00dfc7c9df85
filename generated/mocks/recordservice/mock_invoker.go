// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockrecordservice

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	recordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
)

// MockInvoker is an autogenerated mock type for the Invoker type
type MockInvoker struct {
	mock.Mock
}

type MockInvoker_Expecter struct {
	mock *mock.Mock
}

func (_m *MockInvoker) EXPECT() *MockInvoker_Expecter {
	return &MockInvoker_Expecter{mock: &_m.Mock}
}

// DeletePatientStudies provides a mock function with given fields: ctx, params
func (_m *MockInvoker) DeletePatientStudies(ctx context.Context, params recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DeletePatientStudies")
	}

	var r0 recordservice.DeletePatientStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.DeletePatientStudiesParams) recordservice.DeletePatientStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.DeletePatientStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.DeletePatientStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_DeletePatientStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePatientStudies'
type MockInvoker_DeletePatientStudies_Call struct {
	*mock.Call
}

// DeletePatientStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.DeletePatientStudiesParams
func (_e *MockInvoker_Expecter) DeletePatientStudies(ctx interface{}, params interface{}) *MockInvoker_DeletePatientStudies_Call {
	return &MockInvoker_DeletePatientStudies_Call{Call: _e.mock.On("DeletePatientStudies", ctx, params)}
}

func (_c *MockInvoker_DeletePatientStudies_Call) Run(run func(ctx context.Context, params recordservice.DeletePatientStudiesParams)) *MockInvoker_DeletePatientStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.DeletePatientStudiesParams))
	})
	return _c
}

func (_c *MockInvoker_DeletePatientStudies_Call) Return(_a0 recordservice.DeletePatientStudiesRes, _a1 error) *MockInvoker_DeletePatientStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_DeletePatientStudies_Call) RunAndReturn(run func(context.Context, recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error)) *MockInvoker_DeletePatientStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllPatientStudies provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetAllPatientStudies(ctx context.Context, params recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPatientStudies")
	}

	var r0 recordservice.GetAllPatientStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetAllPatientStudiesParams) recordservice.GetAllPatientStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetAllPatientStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetAllPatientStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetAllPatientStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllPatientStudies'
type MockInvoker_GetAllPatientStudies_Call struct {
	*mock.Call
}

// GetAllPatientStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetAllPatientStudiesParams
func (_e *MockInvoker_Expecter) GetAllPatientStudies(ctx interface{}, params interface{}) *MockInvoker_GetAllPatientStudies_Call {
	return &MockInvoker_GetAllPatientStudies_Call{Call: _e.mock.On("GetAllPatientStudies", ctx, params)}
}

func (_c *MockInvoker_GetAllPatientStudies_Call) Run(run func(ctx context.Context, params recordservice.GetAllPatientStudiesParams)) *MockInvoker_GetAllPatientStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetAllPatientStudiesParams))
	})
	return _c
}

func (_c *MockInvoker_GetAllPatientStudies_Call) Return(_a0 recordservice.GetAllPatientStudiesRes, _a1 error) *MockInvoker_GetAllPatientStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetAllPatientStudies_Call) RunAndReturn(run func(context.Context, recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error)) *MockInvoker_GetAllPatientStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetImaging provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetImaging(ctx context.Context, params recordservice.GetImagingParams) (recordservice.GetImagingRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetImaging")
	}

	var r0 recordservice.GetImagingRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetImagingParams) (recordservice.GetImagingRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetImagingParams) recordservice.GetImagingRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetImagingRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetImagingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetImaging_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetImaging'
type MockInvoker_GetImaging_Call struct {
	*mock.Call
}

// GetImaging is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetImagingParams
func (_e *MockInvoker_Expecter) GetImaging(ctx interface{}, params interface{}) *MockInvoker_GetImaging_Call {
	return &MockInvoker_GetImaging_Call{Call: _e.mock.On("GetImaging", ctx, params)}
}

func (_c *MockInvoker_GetImaging_Call) Run(run func(ctx context.Context, params recordservice.GetImagingParams)) *MockInvoker_GetImaging_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetImagingParams))
	})
	return _c
}

func (_c *MockInvoker_GetImaging_Call) Return(_a0 recordservice.GetImagingRes, _a1 error) *MockInvoker_GetImaging_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetImaging_Call) RunAndReturn(run func(context.Context, recordservice.GetImagingParams) (recordservice.GetImagingRes, error)) *MockInvoker_GetImaging_Call {
	_c.Call.Return(run)
	return _c
}

// GetRecentStudyUploadMetadata provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetRecentStudyUploadMetadata(ctx context.Context, params recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetRecentStudyUploadMetadata")
	}

	var r0 recordservice.GetRecentStudyUploadMetadataRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) recordservice.GetRecentStudyUploadMetadataRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetRecentStudyUploadMetadataRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetRecentStudyUploadMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecentStudyUploadMetadata'
type MockInvoker_GetRecentStudyUploadMetadata_Call struct {
	*mock.Call
}

// GetRecentStudyUploadMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetRecentStudyUploadMetadataParams
func (_e *MockInvoker_Expecter) GetRecentStudyUploadMetadata(ctx interface{}, params interface{}) *MockInvoker_GetRecentStudyUploadMetadata_Call {
	return &MockInvoker_GetRecentStudyUploadMetadata_Call{Call: _e.mock.On("GetRecentStudyUploadMetadata", ctx, params)}
}

func (_c *MockInvoker_GetRecentStudyUploadMetadata_Call) Run(run func(ctx context.Context, params recordservice.GetRecentStudyUploadMetadataParams)) *MockInvoker_GetRecentStudyUploadMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetRecentStudyUploadMetadataParams))
	})
	return _c
}

func (_c *MockInvoker_GetRecentStudyUploadMetadata_Call) Return(_a0 recordservice.GetRecentStudyUploadMetadataRes, _a1 error) *MockInvoker_GetRecentStudyUploadMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetRecentStudyUploadMetadata_Call) RunAndReturn(run func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error)) *MockInvoker_GetRecentStudyUploadMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// GetUploadOverview provides a mock function with given fields: ctx
func (_m *MockInvoker) GetUploadOverview(ctx context.Context) (recordservice.GetUploadOverviewRes, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUploadOverview")
	}

	var r0 recordservice.GetUploadOverviewRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (recordservice.GetUploadOverviewRes, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) recordservice.GetUploadOverviewRes); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetUploadOverviewRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetUploadOverview_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUploadOverview'
type MockInvoker_GetUploadOverview_Call struct {
	*mock.Call
}

// GetUploadOverview is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockInvoker_Expecter) GetUploadOverview(ctx interface{}) *MockInvoker_GetUploadOverview_Call {
	return &MockInvoker_GetUploadOverview_Call{Call: _e.mock.On("GetUploadOverview", ctx)}
}

func (_c *MockInvoker_GetUploadOverview_Call) Run(run func(ctx context.Context)) *MockInvoker_GetUploadOverview_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockInvoker_GetUploadOverview_Call) Return(_a0 recordservice.GetUploadOverviewRes, _a1 error) *MockInvoker_GetUploadOverview_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetUploadOverview_Call) RunAndReturn(run func(context.Context) (recordservice.GetUploadOverviewRes, error)) *MockInvoker_GetUploadOverview_Call {
	_c.Call.Return(run)
	return _c
}

// GetV0ImagingTransfers provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV0ImagingTransfers(ctx context.Context, params recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV0ImagingTransfers")
	}

	var r0 recordservice.GetV0ImagingTransfersRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV0ImagingTransfersParams) recordservice.GetV0ImagingTransfersRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV0ImagingTransfersRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV0ImagingTransfersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV0ImagingTransfers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV0ImagingTransfers'
type MockInvoker_GetV0ImagingTransfers_Call struct {
	*mock.Call
}

// GetV0ImagingTransfers is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV0ImagingTransfersParams
func (_e *MockInvoker_Expecter) GetV0ImagingTransfers(ctx interface{}, params interface{}) *MockInvoker_GetV0ImagingTransfers_Call {
	return &MockInvoker_GetV0ImagingTransfers_Call{Call: _e.mock.On("GetV0ImagingTransfers", ctx, params)}
}

func (_c *MockInvoker_GetV0ImagingTransfers_Call) Run(run func(ctx context.Context, params recordservice.GetV0ImagingTransfersParams)) *MockInvoker_GetV0ImagingTransfers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV0ImagingTransfersParams))
	})
	return _c
}

func (_c *MockInvoker_GetV0ImagingTransfers_Call) Return(_a0 recordservice.GetV0ImagingTransfersRes, _a1 error) *MockInvoker_GetV0ImagingTransfers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV0ImagingTransfers_Call) RunAndReturn(run func(context.Context, recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error)) *MockInvoker_GetV0ImagingTransfers_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1MeddreamGenerate provides a mock function with given fields: ctx, request, params
func (_m *MockInvoker) GetV1MeddreamGenerate(ctx context.Context, request recordservice.OptGenerateMedreamToken, params recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1MeddreamGenerate")
	}

	var r0 recordservice.GetV1MeddreamGenerateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) recordservice.GetV1MeddreamGenerateRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1MeddreamGenerateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1MeddreamGenerate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1MeddreamGenerate'
type MockInvoker_GetV1MeddreamGenerate_Call struct {
	*mock.Call
}

// GetV1MeddreamGenerate is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptGenerateMedreamToken
//   - params recordservice.GetV1MeddreamGenerateParams
func (_e *MockInvoker_Expecter) GetV1MeddreamGenerate(ctx interface{}, request interface{}, params interface{}) *MockInvoker_GetV1MeddreamGenerate_Call {
	return &MockInvoker_GetV1MeddreamGenerate_Call{Call: _e.mock.On("GetV1MeddreamGenerate", ctx, request, params)}
}

func (_c *MockInvoker_GetV1MeddreamGenerate_Call) Run(run func(ctx context.Context, request recordservice.OptGenerateMedreamToken, params recordservice.GetV1MeddreamGenerateParams)) *MockInvoker_GetV1MeddreamGenerate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptGenerateMedreamToken), args[2].(recordservice.GetV1MeddreamGenerateParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1MeddreamGenerate_Call) Return(_a0 recordservice.GetV1MeddreamGenerateRes, _a1 error) *MockInvoker_GetV1MeddreamGenerate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1MeddreamGenerate_Call) RunAndReturn(run func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error)) *MockInvoker_GetV1MeddreamGenerate_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1PatientsUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV1PatientsUploadStatus(ctx context.Context, params recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1PatientsUploadStatus")
	}

	var r0 recordservice.GetV1PatientsUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) recordservice.GetV1PatientsUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1PatientsUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1PatientsUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1PatientsUploadStatus'
type MockInvoker_GetV1PatientsUploadStatus_Call struct {
	*mock.Call
}

// GetV1PatientsUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1PatientsUploadStatusParams
func (_e *MockInvoker_Expecter) GetV1PatientsUploadStatus(ctx interface{}, params interface{}) *MockInvoker_GetV1PatientsUploadStatus_Call {
	return &MockInvoker_GetV1PatientsUploadStatus_Call{Call: _e.mock.On("GetV1PatientsUploadStatus", ctx, params)}
}

func (_c *MockInvoker_GetV1PatientsUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1PatientsUploadStatusParams)) *MockInvoker_GetV1PatientsUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1PatientsUploadStatusParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1PatientsUploadStatus_Call) Return(_a0 recordservice.GetV1PatientsUploadStatusRes, _a1 error) *MockInvoker_GetV1PatientsUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1PatientsUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error)) *MockInvoker_GetV1PatientsUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1PhysicianStudies provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV1PhysicianStudies(ctx context.Context, params recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1PhysicianStudies")
	}

	var r0 recordservice.GetV1PhysicianStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) recordservice.GetV1PhysicianStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1PhysicianStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1PhysicianStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1PhysicianStudies'
type MockInvoker_GetV1PhysicianStudies_Call struct {
	*mock.Call
}

// GetV1PhysicianStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1PhysicianStudiesParams
func (_e *MockInvoker_Expecter) GetV1PhysicianStudies(ctx interface{}, params interface{}) *MockInvoker_GetV1PhysicianStudies_Call {
	return &MockInvoker_GetV1PhysicianStudies_Call{Call: _e.mock.On("GetV1PhysicianStudies", ctx, params)}
}

func (_c *MockInvoker_GetV1PhysicianStudies_Call) Run(run func(ctx context.Context, params recordservice.GetV1PhysicianStudiesParams)) *MockInvoker_GetV1PhysicianStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1PhysicianStudiesParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1PhysicianStudies_Call) Return(_a0 recordservice.GetV1PhysicianStudiesRes, _a1 error) *MockInvoker_GetV1PhysicianStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1PhysicianStudies_Call) RunAndReturn(run func(context.Context, recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error)) *MockInvoker_GetV1PhysicianStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1StudiesPermissions provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV1StudiesPermissions(ctx context.Context, params recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1StudiesPermissions")
	}

	var r0 recordservice.GetV1StudiesPermissionsRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) recordservice.GetV1StudiesPermissionsRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1StudiesPermissionsRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1StudiesPermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1StudiesPermissions'
type MockInvoker_GetV1StudiesPermissions_Call struct {
	*mock.Call
}

// GetV1StudiesPermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1StudiesPermissionsParams
func (_e *MockInvoker_Expecter) GetV1StudiesPermissions(ctx interface{}, params interface{}) *MockInvoker_GetV1StudiesPermissions_Call {
	return &MockInvoker_GetV1StudiesPermissions_Call{Call: _e.mock.On("GetV1StudiesPermissions", ctx, params)}
}

func (_c *MockInvoker_GetV1StudiesPermissions_Call) Run(run func(ctx context.Context, params recordservice.GetV1StudiesPermissionsParams)) *MockInvoker_GetV1StudiesPermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1StudiesPermissionsParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1StudiesPermissions_Call) Return(_a0 recordservice.GetV1StudiesPermissionsRes, _a1 error) *MockInvoker_GetV1StudiesPermissions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1StudiesPermissions_Call) RunAndReturn(run func(context.Context, recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error)) *MockInvoker_GetV1StudiesPermissions_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1StudiesUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV1StudiesUploadStatus(ctx context.Context, params recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1StudiesUploadStatus")
	}

	var r0 recordservice.GetV1StudiesUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) recordservice.GetV1StudiesUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1StudiesUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1StudiesUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1StudiesUploadStatus'
type MockInvoker_GetV1StudiesUploadStatus_Call struct {
	*mock.Call
}

// GetV1StudiesUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1StudiesUploadStatusParams
func (_e *MockInvoker_Expecter) GetV1StudiesUploadStatus(ctx interface{}, params interface{}) *MockInvoker_GetV1StudiesUploadStatus_Call {
	return &MockInvoker_GetV1StudiesUploadStatus_Call{Call: _e.mock.On("GetV1StudiesUploadStatus", ctx, params)}
}

func (_c *MockInvoker_GetV1StudiesUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1StudiesUploadStatusParams)) *MockInvoker_GetV1StudiesUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1StudiesUploadStatusParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1StudiesUploadStatus_Call) Return(_a0 recordservice.GetV1StudiesUploadStatusRes, _a1 error) *MockInvoker_GetV1StudiesUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1StudiesUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error)) *MockInvoker_GetV1StudiesUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1SupportPatientsUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockInvoker) GetV1SupportPatientsUploadStatus(ctx context.Context, params recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1SupportPatientsUploadStatus")
	}

	var r0 recordservice.GetV1SupportPatientsUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) recordservice.GetV1SupportPatientsUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1SupportPatientsUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_GetV1SupportPatientsUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1SupportPatientsUploadStatus'
type MockInvoker_GetV1SupportPatientsUploadStatus_Call struct {
	*mock.Call
}

// GetV1SupportPatientsUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1SupportPatientsUploadStatusParams
func (_e *MockInvoker_Expecter) GetV1SupportPatientsUploadStatus(ctx interface{}, params interface{}) *MockInvoker_GetV1SupportPatientsUploadStatus_Call {
	return &MockInvoker_GetV1SupportPatientsUploadStatus_Call{Call: _e.mock.On("GetV1SupportPatientsUploadStatus", ctx, params)}
}

func (_c *MockInvoker_GetV1SupportPatientsUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1SupportPatientsUploadStatusParams)) *MockInvoker_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1SupportPatientsUploadStatusParams))
	})
	return _c
}

func (_c *MockInvoker_GetV1SupportPatientsUploadStatus_Call) Return(_a0 recordservice.GetV1SupportPatientsUploadStatusRes, _a1 error) *MockInvoker_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_GetV1SupportPatientsUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error)) *MockInvoker_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke")
	}

	var r0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchSupportV1PatientsAccountIDStudiesUUIDRevoke'
type MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call struct {
	*mock.Call
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams
func (_e *MockInvoker_Expecter) PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx interface{}, params interface{}) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	return &MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call{Call: _e.mock.On("PatchSupportV1PatientsAccountIDStudiesUUIDRevoke", ctx, params)}
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams)) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) Return(_a0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, _a1 error) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error)) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke")
	}

	var r0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke'
type MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call struct {
	*mock.Call
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams
func (_e *MockInvoker_Expecter) PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx interface{}, params interface{}) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	return &MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call{Call: _e.mock.On("PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke", ctx, params)}
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams)) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) Return(_a0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, _a1 error) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error)) *MockInvoker_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingTransfersIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchV0ImagingTransfersIDRevoke(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingTransfersIDRevoke")
	}

	var r0 recordservice.PatchV0ImagingTransfersIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) recordservice.PatchV0ImagingTransfersIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingTransfersIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchV0ImagingTransfersIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingTransfersIDRevoke'
type MockInvoker_PatchV0ImagingTransfersIDRevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingTransfersIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingTransfersIDRevokeParams
func (_e *MockInvoker_Expecter) PatchV0ImagingTransfersIDRevoke(ctx interface{}, params interface{}) *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call {
	return &MockInvoker_PatchV0ImagingTransfersIDRevoke_Call{Call: _e.mock.On("PatchV0ImagingTransfersIDRevoke", ctx, params)}
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDRevokeParams)) *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingTransfersIDRevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call) Return(_a0 recordservice.PatchV0ImagingTransfersIDRevokeRes, _a1 error) *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error)) *MockInvoker_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingTransfersIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchV0ImagingTransfersIDUnrevoke(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingTransfersIDUnrevoke")
	}

	var r0 recordservice.PatchV0ImagingTransfersIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) recordservice.PatchV0ImagingTransfersIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingTransfersIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingTransfersIDUnrevoke'
type MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingTransfersIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingTransfersIDUnrevokeParams
func (_e *MockInvoker_Expecter) PatchV0ImagingTransfersIDUnrevoke(ctx interface{}, params interface{}) *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call {
	return &MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call{Call: _e.mock.On("PatchV0ImagingTransfersIDUnrevoke", ctx, params)}
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDUnrevokeParams)) *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingTransfersIDUnrevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call) Return(_a0 recordservice.PatchV0ImagingTransfersIDUnrevokeRes, _a1 error) *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error)) *MockInvoker_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingUUIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchV0ImagingUUIDRevoke(ctx context.Context, params recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingUUIDRevoke")
	}

	var r0 recordservice.PatchV0ImagingUUIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) recordservice.PatchV0ImagingUUIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingUUIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchV0ImagingUUIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingUUIDRevoke'
type MockInvoker_PatchV0ImagingUUIDRevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingUUIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingUUIDRevokeParams
func (_e *MockInvoker_Expecter) PatchV0ImagingUUIDRevoke(ctx interface{}, params interface{}) *MockInvoker_PatchV0ImagingUUIDRevoke_Call {
	return &MockInvoker_PatchV0ImagingUUIDRevoke_Call{Call: _e.mock.On("PatchV0ImagingUUIDRevoke", ctx, params)}
}

func (_c *MockInvoker_PatchV0ImagingUUIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingUUIDRevokeParams)) *MockInvoker_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingUUIDRevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchV0ImagingUUIDRevoke_Call) Return(_a0 recordservice.PatchV0ImagingUUIDRevokeRes, _a1 error) *MockInvoker_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchV0ImagingUUIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error)) *MockInvoker_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingUUIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PatchV0ImagingUUIDUnrevoke(ctx context.Context, params recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingUUIDUnrevoke")
	}

	var r0 recordservice.PatchV0ImagingUUIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) recordservice.PatchV0ImagingUUIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingUUIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PatchV0ImagingUUIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingUUIDUnrevoke'
type MockInvoker_PatchV0ImagingUUIDUnrevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingUUIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingUUIDUnrevokeParams
func (_e *MockInvoker_Expecter) PatchV0ImagingUUIDUnrevoke(ctx interface{}, params interface{}) *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call {
	return &MockInvoker_PatchV0ImagingUUIDUnrevoke_Call{Call: _e.mock.On("PatchV0ImagingUUIDUnrevoke", ctx, params)}
}

func (_c *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingUUIDUnrevokeParams)) *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingUUIDUnrevokeParams))
	})
	return _c
}

func (_c *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call) Return(_a0 recordservice.PatchV0ImagingUUIDUnrevokeRes, _a1 error) *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error)) *MockInvoker_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PostV0SharesProviderSearch provides a mock function with given fields: ctx, request
func (_m *MockInvoker) PostV0SharesProviderSearch(ctx context.Context, request recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for PostV0SharesProviderSearch")
	}

	var r0 []recordservice.ProviderShare
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptProviderShareSearch) []recordservice.ProviderShare); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]recordservice.ProviderShare)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptProviderShareSearch) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PostV0SharesProviderSearch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV0SharesProviderSearch'
type MockInvoker_PostV0SharesProviderSearch_Call struct {
	*mock.Call
}

// PostV0SharesProviderSearch is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptProviderShareSearch
func (_e *MockInvoker_Expecter) PostV0SharesProviderSearch(ctx interface{}, request interface{}) *MockInvoker_PostV0SharesProviderSearch_Call {
	return &MockInvoker_PostV0SharesProviderSearch_Call{Call: _e.mock.On("PostV0SharesProviderSearch", ctx, request)}
}

func (_c *MockInvoker_PostV0SharesProviderSearch_Call) Run(run func(ctx context.Context, request recordservice.OptProviderShareSearch)) *MockInvoker_PostV0SharesProviderSearch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptProviderShareSearch))
	})
	return _c
}

func (_c *MockInvoker_PostV0SharesProviderSearch_Call) Return(_a0 []recordservice.ProviderShare, _a1 error) *MockInvoker_PostV0SharesProviderSearch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PostV0SharesProviderSearch_Call) RunAndReturn(run func(context.Context, recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error)) *MockInvoker_PostV0SharesProviderSearch_Call {
	_c.Call.Return(run)
	return _c
}

// PostV1BusinessRulesetsEvaluate provides a mock function with given fields: ctx, request
func (_m *MockInvoker) PostV1BusinessRulesetsEvaluate(ctx context.Context, request recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for PostV1BusinessRulesetsEvaluate")
	}

	var r0 recordservice.PostV1BusinessRulesetsEvaluateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) recordservice.PostV1BusinessRulesetsEvaluateRes); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PostV1BusinessRulesetsEvaluateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PostV1BusinessRulesetsEvaluate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV1BusinessRulesetsEvaluate'
type MockInvoker_PostV1BusinessRulesetsEvaluate_Call struct {
	*mock.Call
}

// PostV1BusinessRulesetsEvaluate is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptPostV1BusinessRulesetsEvaluateReq
func (_e *MockInvoker_Expecter) PostV1BusinessRulesetsEvaluate(ctx interface{}, request interface{}) *MockInvoker_PostV1BusinessRulesetsEvaluate_Call {
	return &MockInvoker_PostV1BusinessRulesetsEvaluate_Call{Call: _e.mock.On("PostV1BusinessRulesetsEvaluate", ctx, request)}
}

func (_c *MockInvoker_PostV1BusinessRulesetsEvaluate_Call) Run(run func(ctx context.Context, request recordservice.OptPostV1BusinessRulesetsEvaluateReq)) *MockInvoker_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptPostV1BusinessRulesetsEvaluateReq))
	})
	return _c
}

func (_c *MockInvoker_PostV1BusinessRulesetsEvaluate_Call) Return(_a0 recordservice.PostV1BusinessRulesetsEvaluateRes, _a1 error) *MockInvoker_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PostV1BusinessRulesetsEvaluate_Call) RunAndReturn(run func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error)) *MockInvoker_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Return(run)
	return _c
}

// PostV1MeddreamValidate provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PostV1MeddreamValidate(ctx context.Context, params recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PostV1MeddreamValidate")
	}

	var r0 recordservice.PostV1MeddreamValidateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PostV1MeddreamValidateParams) recordservice.PostV1MeddreamValidateRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PostV1MeddreamValidateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PostV1MeddreamValidateParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PostV1MeddreamValidate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV1MeddreamValidate'
type MockInvoker_PostV1MeddreamValidate_Call struct {
	*mock.Call
}

// PostV1MeddreamValidate is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PostV1MeddreamValidateParams
func (_e *MockInvoker_Expecter) PostV1MeddreamValidate(ctx interface{}, params interface{}) *MockInvoker_PostV1MeddreamValidate_Call {
	return &MockInvoker_PostV1MeddreamValidate_Call{Call: _e.mock.On("PostV1MeddreamValidate", ctx, params)}
}

func (_c *MockInvoker_PostV1MeddreamValidate_Call) Run(run func(ctx context.Context, params recordservice.PostV1MeddreamValidateParams)) *MockInvoker_PostV1MeddreamValidate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PostV1MeddreamValidateParams))
	})
	return _c
}

func (_c *MockInvoker_PostV1MeddreamValidate_Call) Return(_a0 recordservice.PostV1MeddreamValidateRes, _a1 error) *MockInvoker_PostV1MeddreamValidate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PostV1MeddreamValidate_Call) RunAndReturn(run func(context.Context, recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error)) *MockInvoker_PostV1MeddreamValidate_Call {
	_c.Call.Return(run)
	return _c
}

// PutV0SharesShareIdExtend provides a mock function with given fields: ctx, params
func (_m *MockInvoker) PutV0SharesShareIdExtend(ctx context.Context, params recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PutV0SharesShareIdExtend")
	}

	var r0 recordservice.PutV0SharesShareIdExtendRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) recordservice.PutV0SharesShareIdExtendRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PutV0SharesShareIdExtendRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_PutV0SharesShareIdExtend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutV0SharesShareIdExtend'
type MockInvoker_PutV0SharesShareIdExtend_Call struct {
	*mock.Call
}

// PutV0SharesShareIdExtend is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PutV0SharesShareIdExtendParams
func (_e *MockInvoker_Expecter) PutV0SharesShareIdExtend(ctx interface{}, params interface{}) *MockInvoker_PutV0SharesShareIdExtend_Call {
	return &MockInvoker_PutV0SharesShareIdExtend_Call{Call: _e.mock.On("PutV0SharesShareIdExtend", ctx, params)}
}

func (_c *MockInvoker_PutV0SharesShareIdExtend_Call) Run(run func(ctx context.Context, params recordservice.PutV0SharesShareIdExtendParams)) *MockInvoker_PutV0SharesShareIdExtend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PutV0SharesShareIdExtendParams))
	})
	return _c
}

func (_c *MockInvoker_PutV0SharesShareIdExtend_Call) Return(_a0 recordservice.PutV0SharesShareIdExtendRes, _a1 error) *MockInvoker_PutV0SharesShareIdExtend_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_PutV0SharesShareIdExtend_Call) RunAndReturn(run func(context.Context, recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error)) *MockInvoker_PutV0SharesShareIdExtend_Call {
	_c.Call.Return(run)
	return _c
}

// V1AccountsAccountIDStudiesAttributePost provides a mock function with given fields: ctx, request, params
func (_m *MockInvoker) V1AccountsAccountIDStudiesAttributePost(ctx context.Context, request recordservice.OptStudyAttribution, params recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for V1AccountsAccountIDStudiesAttributePost")
	}

	var r0 recordservice.V1AccountsAccountIDStudiesAttributePostRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) recordservice.V1AccountsAccountIDStudiesAttributePostRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1AccountsAccountIDStudiesAttributePostRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1AccountsAccountIDStudiesAttributePost'
type MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call struct {
	*mock.Call
}

// V1AccountsAccountIDStudiesAttributePost is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptStudyAttribution
//   - params recordservice.V1AccountsAccountIDStudiesAttributePostParams
func (_e *MockInvoker_Expecter) V1AccountsAccountIDStudiesAttributePost(ctx interface{}, request interface{}, params interface{}) *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call {
	return &MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call{Call: _e.mock.On("V1AccountsAccountIDStudiesAttributePost", ctx, request, params)}
}

func (_c *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call) Run(run func(ctx context.Context, request recordservice.OptStudyAttribution, params recordservice.V1AccountsAccountIDStudiesAttributePostParams)) *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptStudyAttribution), args[2].(recordservice.V1AccountsAccountIDStudiesAttributePostParams))
	})
	return _c
}

func (_c *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call) Return(_a0 recordservice.V1AccountsAccountIDStudiesAttributePostRes, _a1 error) *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call) RunAndReturn(run func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error)) *MockInvoker_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebQidoSearchStudy provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1DicomwebQidoSearchStudy(ctx context.Context, params recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebQidoSearchStudy")
	}

	var r0 recordservice.V1DicomwebQidoSearchStudyRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) recordservice.V1DicomwebQidoSearchStudyRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebQidoSearchStudyRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1DicomwebQidoSearchStudy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebQidoSearchStudy'
type MockInvoker_V1DicomwebQidoSearchStudy_Call struct {
	*mock.Call
}

// V1DicomwebQidoSearchStudy is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebQidoSearchStudyParams
func (_e *MockInvoker_Expecter) V1DicomwebQidoSearchStudy(ctx interface{}, params interface{}) *MockInvoker_V1DicomwebQidoSearchStudy_Call {
	return &MockInvoker_V1DicomwebQidoSearchStudy_Call{Call: _e.mock.On("V1DicomwebQidoSearchStudy", ctx, params)}
}

func (_c *MockInvoker_V1DicomwebQidoSearchStudy_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebQidoSearchStudyParams)) *MockInvoker_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebQidoSearchStudyParams))
	})
	return _c
}

func (_c *MockInvoker_V1DicomwebQidoSearchStudy_Call) Return(_a0 recordservice.V1DicomwebQidoSearchStudyRes, _a1 error) *MockInvoker_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1DicomwebQidoSearchStudy_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error)) *MockInvoker_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebRetrieveInstance provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1DicomwebRetrieveInstance(ctx context.Context, params recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebRetrieveInstance")
	}

	var r0 recordservice.V1DicomwebRetrieveInstanceRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) recordservice.V1DicomwebRetrieveInstanceRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebRetrieveInstanceRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1DicomwebRetrieveInstance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebRetrieveInstance'
type MockInvoker_V1DicomwebRetrieveInstance_Call struct {
	*mock.Call
}

// V1DicomwebRetrieveInstance is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebRetrieveInstanceParams
func (_e *MockInvoker_Expecter) V1DicomwebRetrieveInstance(ctx interface{}, params interface{}) *MockInvoker_V1DicomwebRetrieveInstance_Call {
	return &MockInvoker_V1DicomwebRetrieveInstance_Call{Call: _e.mock.On("V1DicomwebRetrieveInstance", ctx, params)}
}

func (_c *MockInvoker_V1DicomwebRetrieveInstance_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebRetrieveInstanceParams)) *MockInvoker_V1DicomwebRetrieveInstance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebRetrieveInstanceParams))
	})
	return _c
}

func (_c *MockInvoker_V1DicomwebRetrieveInstance_Call) Return(_a0 recordservice.V1DicomwebRetrieveInstanceRes, _a1 error) *MockInvoker_V1DicomwebRetrieveInstance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1DicomwebRetrieveInstance_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error)) *MockInvoker_V1DicomwebRetrieveInstance_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebRetrieveMetadata provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1DicomwebRetrieveMetadata(ctx context.Context, params recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebRetrieveMetadata")
	}

	var r0 recordservice.V1DicomwebRetrieveMetadataRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) recordservice.V1DicomwebRetrieveMetadataRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebRetrieveMetadataRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1DicomwebRetrieveMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebRetrieveMetadata'
type MockInvoker_V1DicomwebRetrieveMetadata_Call struct {
	*mock.Call
}

// V1DicomwebRetrieveMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebRetrieveMetadataParams
func (_e *MockInvoker_Expecter) V1DicomwebRetrieveMetadata(ctx interface{}, params interface{}) *MockInvoker_V1DicomwebRetrieveMetadata_Call {
	return &MockInvoker_V1DicomwebRetrieveMetadata_Call{Call: _e.mock.On("V1DicomwebRetrieveMetadata", ctx, params)}
}

func (_c *MockInvoker_V1DicomwebRetrieveMetadata_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebRetrieveMetadataParams)) *MockInvoker_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebRetrieveMetadataParams))
	})
	return _c
}

func (_c *MockInvoker_V1DicomwebRetrieveMetadata_Call) Return(_a0 recordservice.V1DicomwebRetrieveMetadataRes, _a1 error) *MockInvoker_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1DicomwebRetrieveMetadata_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error)) *MockInvoker_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesActivatePost provides a mock function with given fields: ctx, request, params
func (_m *MockInvoker) V1PatientsAccountIDStudiesActivatePost(ctx context.Context, request recordservice.OptActivateStudy, params recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesActivatePost")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesActivatePostRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) recordservice.V1PatientsAccountIDStudiesActivatePostRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesActivatePostRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesActivatePost'
type MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesActivatePost is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptActivateStudy
//   - params recordservice.V1PatientsAccountIDStudiesActivatePostParams
func (_e *MockInvoker_Expecter) V1PatientsAccountIDStudiesActivatePost(ctx interface{}, request interface{}, params interface{}) *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call {
	return &MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesActivatePost", ctx, request, params)}
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call) Run(run func(ctx context.Context, request recordservice.OptActivateStudy, params recordservice.V1PatientsAccountIDStudiesActivatePostParams)) *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptActivateStudy), args[2].(recordservice.V1PatientsAccountIDStudiesActivatePostParams))
	})
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesActivatePostRes, _a1 error) *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call) RunAndReturn(run func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error)) *MockInvoker_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesGet provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1PatientsAccountIDStudiesGet(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesGet")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) recordservice.V1PatientsAccountIDStudiesGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1PatientsAccountIDStudiesGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesGet'
type MockInvoker_V1PatientsAccountIDStudiesGet_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PatientsAccountIDStudiesGetParams
func (_e *MockInvoker_Expecter) V1PatientsAccountIDStudiesGet(ctx interface{}, params interface{}) *MockInvoker_V1PatientsAccountIDStudiesGet_Call {
	return &MockInvoker_V1PatientsAccountIDStudiesGet_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesGet", ctx, params)}
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesGet_Call) Run(run func(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesGetParams)) *MockInvoker_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PatientsAccountIDStudiesGetParams))
	})
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesGet_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesGetRes, _a1 error) *MockInvoker_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error)) *MockInvoker_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesMatchGet provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1PatientsAccountIDStudiesMatchGet(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesMatchGet")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesMatchGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) recordservice.V1PatientsAccountIDStudiesMatchGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesMatchGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesMatchGet'
type MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesMatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PatientsAccountIDStudiesMatchGetParams
func (_e *MockInvoker_Expecter) V1PatientsAccountIDStudiesMatchGet(ctx interface{}, params interface{}) *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call {
	return &MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesMatchGet", ctx, params)}
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call) Run(run func(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesMatchGetParams)) *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PatientsAccountIDStudiesMatchGetParams))
	})
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesMatchGetRes, _a1 error) *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error)) *MockInvoker_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// V1PhysiciansAccountIDStudiesMatchGet provides a mock function with given fields: ctx, params
func (_m *MockInvoker) V1PhysiciansAccountIDStudiesMatchGet(ctx context.Context, params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PhysiciansAccountIDStudiesMatchGet")
	}

	var r0 recordservice.V1PhysiciansAccountIDStudiesMatchGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) recordservice.V1PhysiciansAccountIDStudiesMatchGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PhysiciansAccountIDStudiesMatchGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PhysiciansAccountIDStudiesMatchGet'
type MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call struct {
	*mock.Call
}

// V1PhysiciansAccountIDStudiesMatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams
func (_e *MockInvoker_Expecter) V1PhysiciansAccountIDStudiesMatchGet(ctx interface{}, params interface{}) *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call {
	return &MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call{Call: _e.mock.On("V1PhysiciansAccountIDStudiesMatchGet", ctx, params)}
}

func (_c *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call) Run(run func(ctx context.Context, params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams)) *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PhysiciansAccountIDStudiesMatchGetParams))
	})
	return _c
}

func (_c *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call) Return(_a0 recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, _a1 error) *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error)) *MockInvoker_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockInvoker creates a new instance of MockInvoker. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockInvoker(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockInvoker {
	mock := &MockInvoker{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
