// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockrecordservice

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	models "gitlab.com/pockethealth/coreapi/pkg/models"

	recordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"

	servicesrecordservice "gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
)

// MockRecordServiceClientInterface is an autogenerated mock type for the RecordServiceClientInterface type
type MockRecordServiceClientInterface struct {
	mock.Mock
}

type MockRecordServiceClientInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRecordServiceClientInterface) EXPECT() *MockRecordServiceClientInterface_Expecter {
	return &MockRecordServiceClientInterface_Expecter{mock: &_m.<PERSON>}
}

// DeletePatientStudies provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) DeletePatientStudies(ctx context.Context, params recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DeletePatientStudies")
	}

	var r0 recordservice.DeletePatientStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.DeletePatientStudiesParams) recordservice.DeletePatientStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.DeletePatientStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.DeletePatientStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_DeletePatientStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePatientStudies'
type MockRecordServiceClientInterface_DeletePatientStudies_Call struct {
	*mock.Call
}

// DeletePatientStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.DeletePatientStudiesParams
func (_e *MockRecordServiceClientInterface_Expecter) DeletePatientStudies(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_DeletePatientStudies_Call {
	return &MockRecordServiceClientInterface_DeletePatientStudies_Call{Call: _e.mock.On("DeletePatientStudies", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_DeletePatientStudies_Call) Run(run func(ctx context.Context, params recordservice.DeletePatientStudiesParams)) *MockRecordServiceClientInterface_DeletePatientStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.DeletePatientStudiesParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_DeletePatientStudies_Call) Return(_a0 recordservice.DeletePatientStudiesRes, _a1 error) *MockRecordServiceClientInterface_DeletePatientStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_DeletePatientStudies_Call) RunAndReturn(run func(context.Context, recordservice.DeletePatientStudiesParams) (recordservice.DeletePatientStudiesRes, error)) *MockRecordServiceClientInterface_DeletePatientStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllPatientStudies provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetAllPatientStudies(ctx context.Context, params recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPatientStudies")
	}

	var r0 recordservice.GetAllPatientStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetAllPatientStudiesParams) recordservice.GetAllPatientStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetAllPatientStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetAllPatientStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetAllPatientStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllPatientStudies'
type MockRecordServiceClientInterface_GetAllPatientStudies_Call struct {
	*mock.Call
}

// GetAllPatientStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetAllPatientStudiesParams
func (_e *MockRecordServiceClientInterface_Expecter) GetAllPatientStudies(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetAllPatientStudies_Call {
	return &MockRecordServiceClientInterface_GetAllPatientStudies_Call{Call: _e.mock.On("GetAllPatientStudies", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetAllPatientStudies_Call) Run(run func(ctx context.Context, params recordservice.GetAllPatientStudiesParams)) *MockRecordServiceClientInterface_GetAllPatientStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetAllPatientStudiesParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetAllPatientStudies_Call) Return(_a0 recordservice.GetAllPatientStudiesRes, _a1 error) *MockRecordServiceClientInterface_GetAllPatientStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetAllPatientStudies_Call) RunAndReturn(run func(context.Context, recordservice.GetAllPatientStudiesParams) (recordservice.GetAllPatientStudiesRes, error)) *MockRecordServiceClientInterface_GetAllPatientStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetImaging provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetImaging(ctx context.Context, params recordservice.GetImagingParams) (recordservice.GetImagingRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetImaging")
	}

	var r0 recordservice.GetImagingRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetImagingParams) (recordservice.GetImagingRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetImagingParams) recordservice.GetImagingRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetImagingRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetImagingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetImaging_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetImaging'
type MockRecordServiceClientInterface_GetImaging_Call struct {
	*mock.Call
}

// GetImaging is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetImagingParams
func (_e *MockRecordServiceClientInterface_Expecter) GetImaging(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetImaging_Call {
	return &MockRecordServiceClientInterface_GetImaging_Call{Call: _e.mock.On("GetImaging", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetImaging_Call) Run(run func(ctx context.Context, params recordservice.GetImagingParams)) *MockRecordServiceClientInterface_GetImaging_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetImagingParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetImaging_Call) Return(_a0 recordservice.GetImagingRes, _a1 error) *MockRecordServiceClientInterface_GetImaging_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetImaging_Call) RunAndReturn(run func(context.Context, recordservice.GetImagingParams) (recordservice.GetImagingRes, error)) *MockRecordServiceClientInterface_GetImaging_Call {
	_c.Call.Return(run)
	return _c
}

// GetMeddreamToken provides a mock function with given fields: ctx, tokenRequest
func (_m *MockRecordServiceClientInterface) GetMeddreamToken(ctx context.Context, tokenRequest servicesrecordservice.TokenRequest) (string, error) {
	ret := _m.Called(ctx, tokenRequest)

	if len(ret) == 0 {
		panic("no return value specified for GetMeddreamToken")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, servicesrecordservice.TokenRequest) (string, error)); ok {
		return rf(ctx, tokenRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, servicesrecordservice.TokenRequest) string); ok {
		r0 = rf(ctx, tokenRequest)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, servicesrecordservice.TokenRequest) error); ok {
		r1 = rf(ctx, tokenRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetMeddreamToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMeddreamToken'
type MockRecordServiceClientInterface_GetMeddreamToken_Call struct {
	*mock.Call
}

// GetMeddreamToken is a helper method to define mock.On call
//   - ctx context.Context
//   - tokenRequest servicesrecordservice.TokenRequest
func (_e *MockRecordServiceClientInterface_Expecter) GetMeddreamToken(ctx interface{}, tokenRequest interface{}) *MockRecordServiceClientInterface_GetMeddreamToken_Call {
	return &MockRecordServiceClientInterface_GetMeddreamToken_Call{Call: _e.mock.On("GetMeddreamToken", ctx, tokenRequest)}
}

func (_c *MockRecordServiceClientInterface_GetMeddreamToken_Call) Run(run func(ctx context.Context, tokenRequest servicesrecordservice.TokenRequest)) *MockRecordServiceClientInterface_GetMeddreamToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(servicesrecordservice.TokenRequest))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetMeddreamToken_Call) Return(_a0 string, _a1 error) *MockRecordServiceClientInterface_GetMeddreamToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetMeddreamToken_Call) RunAndReturn(run func(context.Context, servicesrecordservice.TokenRequest) (string, error)) *MockRecordServiceClientInterface_GetMeddreamToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetPatientStudiesWithUploadStatus provides a mock function with given fields: ctx, accountID
func (_m *MockRecordServiceClientInterface) GetPatientStudiesWithUploadStatus(ctx context.Context, accountID string) (models.RecordUploadStatuses, error) {
	ret := _m.Called(ctx, accountID)

	if len(ret) == 0 {
		panic("no return value specified for GetPatientStudiesWithUploadStatus")
	}

	var r0 models.RecordUploadStatuses
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (models.RecordUploadStatuses, error)); ok {
		return rf(ctx, accountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) models.RecordUploadStatuses); ok {
		r0 = rf(ctx, accountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(models.RecordUploadStatuses)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPatientStudiesWithUploadStatus'
type MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call struct {
	*mock.Call
}

// GetPatientStudiesWithUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - accountID string
func (_e *MockRecordServiceClientInterface_Expecter) GetPatientStudiesWithUploadStatus(ctx interface{}, accountID interface{}) *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call {
	return &MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call{Call: _e.mock.On("GetPatientStudiesWithUploadStatus", ctx, accountID)}
}

func (_c *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call) Run(run func(ctx context.Context, accountID string)) *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call) Return(_a0 models.RecordUploadStatuses, _a1 error) *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call) RunAndReturn(run func(context.Context, string) (models.RecordUploadStatuses, error)) *MockRecordServiceClientInterface_GetPatientStudiesWithUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetRecentStudyUploadMetadata provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetRecentStudyUploadMetadata(ctx context.Context, params recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetRecentStudyUploadMetadata")
	}

	var r0 recordservice.GetRecentStudyUploadMetadataRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) recordservice.GetRecentStudyUploadMetadataRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetRecentStudyUploadMetadataRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecentStudyUploadMetadata'
type MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call struct {
	*mock.Call
}

// GetRecentStudyUploadMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetRecentStudyUploadMetadataParams
func (_e *MockRecordServiceClientInterface_Expecter) GetRecentStudyUploadMetadata(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call {
	return &MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call{Call: _e.mock.On("GetRecentStudyUploadMetadata", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call) Run(run func(ctx context.Context, params recordservice.GetRecentStudyUploadMetadataParams)) *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetRecentStudyUploadMetadataParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call) Return(_a0 recordservice.GetRecentStudyUploadMetadataRes, _a1 error) *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call) RunAndReturn(run func(context.Context, recordservice.GetRecentStudyUploadMetadataParams) (recordservice.GetRecentStudyUploadMetadataRes, error)) *MockRecordServiceClientInterface_GetRecentStudyUploadMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// GetStudies provides a mock function with given fields: ctx, accountID, includeReports, includeInstances, activated, UUIDs
func (_m *MockRecordServiceClientInterface) GetStudies(ctx context.Context, accountID string, includeReports bool, includeInstances bool, activated *bool, UUIDs []string) ([]servicesrecordservice.PatientStudy, error) {
	ret := _m.Called(ctx, accountID, includeReports, includeInstances, activated, UUIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetStudies")
	}

	var r0 []servicesrecordservice.PatientStudy
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, bool, *bool, []string) ([]servicesrecordservice.PatientStudy, error)); ok {
		return rf(ctx, accountID, includeReports, includeInstances, activated, UUIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, bool, *bool, []string) []servicesrecordservice.PatientStudy); ok {
		r0 = rf(ctx, accountID, includeReports, includeInstances, activated, UUIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]servicesrecordservice.PatientStudy)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool, bool, *bool, []string) error); ok {
		r1 = rf(ctx, accountID, includeReports, includeInstances, activated, UUIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStudies'
type MockRecordServiceClientInterface_GetStudies_Call struct {
	*mock.Call
}

// GetStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - accountID string
//   - includeReports bool
//   - includeInstances bool
//   - activated *bool
//   - UUIDs []string
func (_e *MockRecordServiceClientInterface_Expecter) GetStudies(ctx interface{}, accountID interface{}, includeReports interface{}, includeInstances interface{}, activated interface{}, UUIDs interface{}) *MockRecordServiceClientInterface_GetStudies_Call {
	return &MockRecordServiceClientInterface_GetStudies_Call{Call: _e.mock.On("GetStudies", ctx, accountID, includeReports, includeInstances, activated, UUIDs)}
}

func (_c *MockRecordServiceClientInterface_GetStudies_Call) Run(run func(ctx context.Context, accountID string, includeReports bool, includeInstances bool, activated *bool, UUIDs []string)) *MockRecordServiceClientInterface_GetStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool), args[3].(bool), args[4].(*bool), args[5].([]string))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetStudies_Call) Return(_a0 []servicesrecordservice.PatientStudy, _a1 error) *MockRecordServiceClientInterface_GetStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetStudies_Call) RunAndReturn(run func(context.Context, string, bool, bool, *bool, []string) ([]servicesrecordservice.PatientStudy, error)) *MockRecordServiceClientInterface_GetStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetUploadOverview provides a mock function with given fields: ctx
func (_m *MockRecordServiceClientInterface) GetUploadOverview(ctx context.Context) (recordservice.GetUploadOverviewRes, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUploadOverview")
	}

	var r0 recordservice.GetUploadOverviewRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (recordservice.GetUploadOverviewRes, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) recordservice.GetUploadOverviewRes); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetUploadOverviewRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetUploadOverview_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUploadOverview'
type MockRecordServiceClientInterface_GetUploadOverview_Call struct {
	*mock.Call
}

// GetUploadOverview is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRecordServiceClientInterface_Expecter) GetUploadOverview(ctx interface{}) *MockRecordServiceClientInterface_GetUploadOverview_Call {
	return &MockRecordServiceClientInterface_GetUploadOverview_Call{Call: _e.mock.On("GetUploadOverview", ctx)}
}

func (_c *MockRecordServiceClientInterface_GetUploadOverview_Call) Run(run func(ctx context.Context)) *MockRecordServiceClientInterface_GetUploadOverview_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetUploadOverview_Call) Return(_a0 recordservice.GetUploadOverviewRes, _a1 error) *MockRecordServiceClientInterface_GetUploadOverview_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetUploadOverview_Call) RunAndReturn(run func(context.Context) (recordservice.GetUploadOverviewRes, error)) *MockRecordServiceClientInterface_GetUploadOverview_Call {
	_c.Call.Return(run)
	return _c
}

// GetV0ImagingTransfers provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV0ImagingTransfers(ctx context.Context, params recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV0ImagingTransfers")
	}

	var r0 recordservice.GetV0ImagingTransfersRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV0ImagingTransfersParams) recordservice.GetV0ImagingTransfersRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV0ImagingTransfersRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV0ImagingTransfersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV0ImagingTransfers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV0ImagingTransfers'
type MockRecordServiceClientInterface_GetV0ImagingTransfers_Call struct {
	*mock.Call
}

// GetV0ImagingTransfers is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV0ImagingTransfersParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV0ImagingTransfers(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call {
	return &MockRecordServiceClientInterface_GetV0ImagingTransfers_Call{Call: _e.mock.On("GetV0ImagingTransfers", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call) Run(run func(ctx context.Context, params recordservice.GetV0ImagingTransfersParams)) *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV0ImagingTransfersParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call) Return(_a0 recordservice.GetV0ImagingTransfersRes, _a1 error) *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call) RunAndReturn(run func(context.Context, recordservice.GetV0ImagingTransfersParams) (recordservice.GetV0ImagingTransfersRes, error)) *MockRecordServiceClientInterface_GetV0ImagingTransfers_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1MeddreamGenerate provides a mock function with given fields: ctx, request, params
func (_m *MockRecordServiceClientInterface) GetV1MeddreamGenerate(ctx context.Context, request recordservice.OptGenerateMedreamToken, params recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1MeddreamGenerate")
	}

	var r0 recordservice.GetV1MeddreamGenerateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) recordservice.GetV1MeddreamGenerateRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1MeddreamGenerateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1MeddreamGenerate'
type MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call struct {
	*mock.Call
}

// GetV1MeddreamGenerate is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptGenerateMedreamToken
//   - params recordservice.GetV1MeddreamGenerateParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1MeddreamGenerate(ctx interface{}, request interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call {
	return &MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call{Call: _e.mock.On("GetV1MeddreamGenerate", ctx, request, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call) Run(run func(ctx context.Context, request recordservice.OptGenerateMedreamToken, params recordservice.GetV1MeddreamGenerateParams)) *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptGenerateMedreamToken), args[2].(recordservice.GetV1MeddreamGenerateParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call) Return(_a0 recordservice.GetV1MeddreamGenerateRes, _a1 error) *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call) RunAndReturn(run func(context.Context, recordservice.OptGenerateMedreamToken, recordservice.GetV1MeddreamGenerateParams) (recordservice.GetV1MeddreamGenerateRes, error)) *MockRecordServiceClientInterface_GetV1MeddreamGenerate_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1PatientsUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV1PatientsUploadStatus(ctx context.Context, params recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1PatientsUploadStatus")
	}

	var r0 recordservice.GetV1PatientsUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) recordservice.GetV1PatientsUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1PatientsUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1PatientsUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1PatientsUploadStatus'
type MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call struct {
	*mock.Call
}

// GetV1PatientsUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1PatientsUploadStatusParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1PatientsUploadStatus(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call {
	return &MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call{Call: _e.mock.On("GetV1PatientsUploadStatus", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1PatientsUploadStatusParams)) *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1PatientsUploadStatusParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call) Return(_a0 recordservice.GetV1PatientsUploadStatusRes, _a1 error) *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1PatientsUploadStatusParams) (recordservice.GetV1PatientsUploadStatusRes, error)) *MockRecordServiceClientInterface_GetV1PatientsUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1PhysicianStudies provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV1PhysicianStudies(ctx context.Context, params recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1PhysicianStudies")
	}

	var r0 recordservice.GetV1PhysicianStudiesRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) recordservice.GetV1PhysicianStudiesRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1PhysicianStudiesRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1PhysicianStudiesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1PhysicianStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1PhysicianStudies'
type MockRecordServiceClientInterface_GetV1PhysicianStudies_Call struct {
	*mock.Call
}

// GetV1PhysicianStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1PhysicianStudiesParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1PhysicianStudies(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call {
	return &MockRecordServiceClientInterface_GetV1PhysicianStudies_Call{Call: _e.mock.On("GetV1PhysicianStudies", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call) Run(run func(ctx context.Context, params recordservice.GetV1PhysicianStudiesParams)) *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1PhysicianStudiesParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call) Return(_a0 recordservice.GetV1PhysicianStudiesRes, _a1 error) *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call) RunAndReturn(run func(context.Context, recordservice.GetV1PhysicianStudiesParams) (recordservice.GetV1PhysicianStudiesRes, error)) *MockRecordServiceClientInterface_GetV1PhysicianStudies_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1StudiesPermissions provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV1StudiesPermissions(ctx context.Context, params recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1StudiesPermissions")
	}

	var r0 recordservice.GetV1StudiesPermissionsRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) recordservice.GetV1StudiesPermissionsRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1StudiesPermissionsRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1StudiesPermissionsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1StudiesPermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1StudiesPermissions'
type MockRecordServiceClientInterface_GetV1StudiesPermissions_Call struct {
	*mock.Call
}

// GetV1StudiesPermissions is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1StudiesPermissionsParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1StudiesPermissions(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call {
	return &MockRecordServiceClientInterface_GetV1StudiesPermissions_Call{Call: _e.mock.On("GetV1StudiesPermissions", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call) Run(run func(ctx context.Context, params recordservice.GetV1StudiesPermissionsParams)) *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1StudiesPermissionsParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call) Return(_a0 recordservice.GetV1StudiesPermissionsRes, _a1 error) *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call) RunAndReturn(run func(context.Context, recordservice.GetV1StudiesPermissionsParams) (recordservice.GetV1StudiesPermissionsRes, error)) *MockRecordServiceClientInterface_GetV1StudiesPermissions_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1StudiesUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV1StudiesUploadStatus(ctx context.Context, params recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1StudiesUploadStatus")
	}

	var r0 recordservice.GetV1StudiesUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) recordservice.GetV1StudiesUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1StudiesUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1StudiesUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1StudiesUploadStatus'
type MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call struct {
	*mock.Call
}

// GetV1StudiesUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1StudiesUploadStatusParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1StudiesUploadStatus(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call {
	return &MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call{Call: _e.mock.On("GetV1StudiesUploadStatus", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1StudiesUploadStatusParams)) *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1StudiesUploadStatusParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call) Return(_a0 recordservice.GetV1StudiesUploadStatusRes, _a1 error) *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1StudiesUploadStatusParams) (recordservice.GetV1StudiesUploadStatusRes, error)) *MockRecordServiceClientInterface_GetV1StudiesUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetV1SupportPatientsUploadStatus provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) GetV1SupportPatientsUploadStatus(ctx context.Context, params recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetV1SupportPatientsUploadStatus")
	}

	var r0 recordservice.GetV1SupportPatientsUploadStatusRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) recordservice.GetV1SupportPatientsUploadStatusRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.GetV1SupportPatientsUploadStatusRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetV1SupportPatientsUploadStatus'
type MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call struct {
	*mock.Call
}

// GetV1SupportPatientsUploadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.GetV1SupportPatientsUploadStatusParams
func (_e *MockRecordServiceClientInterface_Expecter) GetV1SupportPatientsUploadStatus(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call {
	return &MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call{Call: _e.mock.On("GetV1SupportPatientsUploadStatus", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call) Run(run func(ctx context.Context, params recordservice.GetV1SupportPatientsUploadStatusParams)) *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.GetV1SupportPatientsUploadStatusParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call) Return(_a0 recordservice.GetV1SupportPatientsUploadStatusRes, _a1 error) *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call) RunAndReturn(run func(context.Context, recordservice.GetV1SupportPatientsUploadStatusParams) (recordservice.GetV1SupportPatientsUploadStatusRes, error)) *MockRecordServiceClientInterface_GetV1SupportPatientsUploadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// MatchPatientStudies provides a mock function with given fields: ctx, accountID, UUIDs
func (_m *MockRecordServiceClientInterface) MatchPatientStudies(ctx context.Context, accountID string, UUIDs []string) (bool, error) {
	ret := _m.Called(ctx, accountID, UUIDs)

	if len(ret) == 0 {
		panic("no return value specified for MatchPatientStudies")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []string) (bool, error)); ok {
		return rf(ctx, accountID, UUIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []string) bool); ok {
		r0 = rf(ctx, accountID, UUIDs)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []string) error); ok {
		r1 = rf(ctx, accountID, UUIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_MatchPatientStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MatchPatientStudies'
type MockRecordServiceClientInterface_MatchPatientStudies_Call struct {
	*mock.Call
}

// MatchPatientStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - accountID string
//   - UUIDs []string
func (_e *MockRecordServiceClientInterface_Expecter) MatchPatientStudies(ctx interface{}, accountID interface{}, UUIDs interface{}) *MockRecordServiceClientInterface_MatchPatientStudies_Call {
	return &MockRecordServiceClientInterface_MatchPatientStudies_Call{Call: _e.mock.On("MatchPatientStudies", ctx, accountID, UUIDs)}
}

func (_c *MockRecordServiceClientInterface_MatchPatientStudies_Call) Run(run func(ctx context.Context, accountID string, UUIDs []string)) *MockRecordServiceClientInterface_MatchPatientStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]string))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_MatchPatientStudies_Call) Return(_a0 bool, _a1 error) *MockRecordServiceClientInterface_MatchPatientStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_MatchPatientStudies_Call) RunAndReturn(run func(context.Context, string, []string) (bool, error)) *MockRecordServiceClientInterface_MatchPatientStudies_Call {
	_c.Call.Return(run)
	return _c
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke")
	}

	var r0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchSupportV1PatientsAccountIDStudiesUUIDRevoke'
type MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call struct {
	*mock.Call
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	return &MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call{Call: _e.mock.On("PatchSupportV1PatientsAccountIDStudiesUUIDRevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams)) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) Return(_a0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error)) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke")
	}

	var r0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke'
type MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call struct {
	*mock.Call
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	return &MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call{Call: _e.mock.On("PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams)) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) Return(_a0 recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (recordservice.PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error)) *MockRecordServiceClientInterface_PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingTransfersIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchV0ImagingTransfersIDRevoke(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingTransfersIDRevoke")
	}

	var r0 recordservice.PatchV0ImagingTransfersIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) recordservice.PatchV0ImagingTransfersIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingTransfersIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingTransfersIDRevoke'
type MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingTransfersIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingTransfersIDRevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchV0ImagingTransfersIDRevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call {
	return &MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call{Call: _e.mock.On("PatchV0ImagingTransfersIDRevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDRevokeParams)) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingTransfersIDRevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call) Return(_a0 recordservice.PatchV0ImagingTransfersIDRevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingTransfersIDRevokeParams) (recordservice.PatchV0ImagingTransfersIDRevokeRes, error)) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingTransfersIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchV0ImagingTransfersIDUnrevoke(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingTransfersIDUnrevoke")
	}

	var r0 recordservice.PatchV0ImagingTransfersIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) recordservice.PatchV0ImagingTransfersIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingTransfersIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingTransfersIDUnrevoke'
type MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingTransfersIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingTransfersIDUnrevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchV0ImagingTransfersIDUnrevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call {
	return &MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call{Call: _e.mock.On("PatchV0ImagingTransfersIDUnrevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingTransfersIDUnrevokeParams)) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingTransfersIDUnrevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call) Return(_a0 recordservice.PatchV0ImagingTransfersIDUnrevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingTransfersIDUnrevokeParams) (recordservice.PatchV0ImagingTransfersIDUnrevokeRes, error)) *MockRecordServiceClientInterface_PatchV0ImagingTransfersIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingUUIDRevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchV0ImagingUUIDRevoke(ctx context.Context, params recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingUUIDRevoke")
	}

	var r0 recordservice.PatchV0ImagingUUIDRevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) recordservice.PatchV0ImagingUUIDRevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingUUIDRevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingUUIDRevoke'
type MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingUUIDRevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingUUIDRevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchV0ImagingUUIDRevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call {
	return &MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call{Call: _e.mock.On("PatchV0ImagingUUIDRevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingUUIDRevokeParams)) *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingUUIDRevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call) Return(_a0 recordservice.PatchV0ImagingUUIDRevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingUUIDRevokeParams) (recordservice.PatchV0ImagingUUIDRevokeRes, error)) *MockRecordServiceClientInterface_PatchV0ImagingUUIDRevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PatchV0ImagingUUIDUnrevoke provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PatchV0ImagingUUIDUnrevoke(ctx context.Context, params recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PatchV0ImagingUUIDUnrevoke")
	}

	var r0 recordservice.PatchV0ImagingUUIDUnrevokeRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) recordservice.PatchV0ImagingUUIDUnrevokeRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PatchV0ImagingUUIDUnrevokeRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchV0ImagingUUIDUnrevoke'
type MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call struct {
	*mock.Call
}

// PatchV0ImagingUUIDUnrevoke is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PatchV0ImagingUUIDUnrevokeParams
func (_e *MockRecordServiceClientInterface_Expecter) PatchV0ImagingUUIDUnrevoke(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call {
	return &MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call{Call: _e.mock.On("PatchV0ImagingUUIDUnrevoke", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call) Run(run func(ctx context.Context, params recordservice.PatchV0ImagingUUIDUnrevokeParams)) *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PatchV0ImagingUUIDUnrevokeParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call) Return(_a0 recordservice.PatchV0ImagingUUIDUnrevokeRes, _a1 error) *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call) RunAndReturn(run func(context.Context, recordservice.PatchV0ImagingUUIDUnrevokeParams) (recordservice.PatchV0ImagingUUIDUnrevokeRes, error)) *MockRecordServiceClientInterface_PatchV0ImagingUUIDUnrevoke_Call {
	_c.Call.Return(run)
	return _c
}

// PostActivateStudies provides a mock function with given fields: ctx, accountID, activationKey, studyAvailabilityStatuses, orderID
func (_m *MockRecordServiceClientInterface) PostActivateStudies(ctx context.Context, accountID string, activationKey coreapi.ActivationKey, studyAvailabilityStatuses map[string]models.UnlockStatus, orderID string) ([]servicesrecordservice.PatientStudy, error) {
	ret := _m.Called(ctx, accountID, activationKey, studyAvailabilityStatuses, orderID)

	if len(ret) == 0 {
		panic("no return value specified for PostActivateStudies")
	}

	var r0 []servicesrecordservice.PatientStudy
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.ActivationKey, map[string]models.UnlockStatus, string) ([]servicesrecordservice.PatientStudy, error)); ok {
		return rf(ctx, accountID, activationKey, studyAvailabilityStatuses, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.ActivationKey, map[string]models.UnlockStatus, string) []servicesrecordservice.PatientStudy); ok {
		r0 = rf(ctx, accountID, activationKey, studyAvailabilityStatuses, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]servicesrecordservice.PatientStudy)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, coreapi.ActivationKey, map[string]models.UnlockStatus, string) error); ok {
		r1 = rf(ctx, accountID, activationKey, studyAvailabilityStatuses, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PostActivateStudies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostActivateStudies'
type MockRecordServiceClientInterface_PostActivateStudies_Call struct {
	*mock.Call
}

// PostActivateStudies is a helper method to define mock.On call
//   - ctx context.Context
//   - accountID string
//   - activationKey coreapi.ActivationKey
//   - studyAvailabilityStatuses map[string]models.UnlockStatus
//   - orderID string
func (_e *MockRecordServiceClientInterface_Expecter) PostActivateStudies(ctx interface{}, accountID interface{}, activationKey interface{}, studyAvailabilityStatuses interface{}, orderID interface{}) *MockRecordServiceClientInterface_PostActivateStudies_Call {
	return &MockRecordServiceClientInterface_PostActivateStudies_Call{Call: _e.mock.On("PostActivateStudies", ctx, accountID, activationKey, studyAvailabilityStatuses, orderID)}
}

func (_c *MockRecordServiceClientInterface_PostActivateStudies_Call) Run(run func(ctx context.Context, accountID string, activationKey coreapi.ActivationKey, studyAvailabilityStatuses map[string]models.UnlockStatus, orderID string)) *MockRecordServiceClientInterface_PostActivateStudies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(coreapi.ActivationKey), args[3].(map[string]models.UnlockStatus), args[4].(string))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PostActivateStudies_Call) Return(_a0 []servicesrecordservice.PatientStudy, _a1 error) *MockRecordServiceClientInterface_PostActivateStudies_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PostActivateStudies_Call) RunAndReturn(run func(context.Context, string, coreapi.ActivationKey, map[string]models.UnlockStatus, string) ([]servicesrecordservice.PatientStudy, error)) *MockRecordServiceClientInterface_PostActivateStudies_Call {
	_c.Call.Return(run)
	return _c
}

// PostV0SharesProviderSearch provides a mock function with given fields: ctx, request
func (_m *MockRecordServiceClientInterface) PostV0SharesProviderSearch(ctx context.Context, request recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for PostV0SharesProviderSearch")
	}

	var r0 []recordservice.ProviderShare
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptProviderShareSearch) []recordservice.ProviderShare); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]recordservice.ProviderShare)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptProviderShareSearch) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV0SharesProviderSearch'
type MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call struct {
	*mock.Call
}

// PostV0SharesProviderSearch is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptProviderShareSearch
func (_e *MockRecordServiceClientInterface_Expecter) PostV0SharesProviderSearch(ctx interface{}, request interface{}) *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call {
	return &MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call{Call: _e.mock.On("PostV0SharesProviderSearch", ctx, request)}
}

func (_c *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call) Run(run func(ctx context.Context, request recordservice.OptProviderShareSearch)) *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptProviderShareSearch))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call) Return(_a0 []recordservice.ProviderShare, _a1 error) *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call) RunAndReturn(run func(context.Context, recordservice.OptProviderShareSearch) ([]recordservice.ProviderShare, error)) *MockRecordServiceClientInterface_PostV0SharesProviderSearch_Call {
	_c.Call.Return(run)
	return _c
}

// PostV1BusinessRulesetsEvaluate provides a mock function with given fields: ctx, request
func (_m *MockRecordServiceClientInterface) PostV1BusinessRulesetsEvaluate(ctx context.Context, request recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for PostV1BusinessRulesetsEvaluate")
	}

	var r0 recordservice.PostV1BusinessRulesetsEvaluateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) recordservice.PostV1BusinessRulesetsEvaluateRes); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PostV1BusinessRulesetsEvaluateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV1BusinessRulesetsEvaluate'
type MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call struct {
	*mock.Call
}

// PostV1BusinessRulesetsEvaluate is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptPostV1BusinessRulesetsEvaluateReq
func (_e *MockRecordServiceClientInterface_Expecter) PostV1BusinessRulesetsEvaluate(ctx interface{}, request interface{}) *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call {
	return &MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call{Call: _e.mock.On("PostV1BusinessRulesetsEvaluate", ctx, request)}
}

func (_c *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call) Run(run func(ctx context.Context, request recordservice.OptPostV1BusinessRulesetsEvaluateReq)) *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptPostV1BusinessRulesetsEvaluateReq))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call) Return(_a0 recordservice.PostV1BusinessRulesetsEvaluateRes, _a1 error) *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call) RunAndReturn(run func(context.Context, recordservice.OptPostV1BusinessRulesetsEvaluateReq) (recordservice.PostV1BusinessRulesetsEvaluateRes, error)) *MockRecordServiceClientInterface_PostV1BusinessRulesetsEvaluate_Call {
	_c.Call.Return(run)
	return _c
}

// PostV1MeddreamValidate provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PostV1MeddreamValidate(ctx context.Context, params recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PostV1MeddreamValidate")
	}

	var r0 recordservice.PostV1MeddreamValidateRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PostV1MeddreamValidateParams) recordservice.PostV1MeddreamValidateRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PostV1MeddreamValidateRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PostV1MeddreamValidateParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PostV1MeddreamValidate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostV1MeddreamValidate'
type MockRecordServiceClientInterface_PostV1MeddreamValidate_Call struct {
	*mock.Call
}

// PostV1MeddreamValidate is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PostV1MeddreamValidateParams
func (_e *MockRecordServiceClientInterface_Expecter) PostV1MeddreamValidate(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call {
	return &MockRecordServiceClientInterface_PostV1MeddreamValidate_Call{Call: _e.mock.On("PostV1MeddreamValidate", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call) Run(run func(ctx context.Context, params recordservice.PostV1MeddreamValidateParams)) *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PostV1MeddreamValidateParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call) Return(_a0 recordservice.PostV1MeddreamValidateRes, _a1 error) *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call) RunAndReturn(run func(context.Context, recordservice.PostV1MeddreamValidateParams) (recordservice.PostV1MeddreamValidateRes, error)) *MockRecordServiceClientInterface_PostV1MeddreamValidate_Call {
	_c.Call.Return(run)
	return _c
}

// PutV0SharesShareIdExtend provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) PutV0SharesShareIdExtend(ctx context.Context, params recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PutV0SharesShareIdExtend")
	}

	var r0 recordservice.PutV0SharesShareIdExtendRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) recordservice.PutV0SharesShareIdExtendRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.PutV0SharesShareIdExtendRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.PutV0SharesShareIdExtendParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutV0SharesShareIdExtend'
type MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call struct {
	*mock.Call
}

// PutV0SharesShareIdExtend is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.PutV0SharesShareIdExtendParams
func (_e *MockRecordServiceClientInterface_Expecter) PutV0SharesShareIdExtend(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call {
	return &MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call{Call: _e.mock.On("PutV0SharesShareIdExtend", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call) Run(run func(ctx context.Context, params recordservice.PutV0SharesShareIdExtendParams)) *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.PutV0SharesShareIdExtendParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call) Return(_a0 recordservice.PutV0SharesShareIdExtendRes, _a1 error) *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call) RunAndReturn(run func(context.Context, recordservice.PutV0SharesShareIdExtendParams) (recordservice.PutV0SharesShareIdExtendRes, error)) *MockRecordServiceClientInterface_PutV0SharesShareIdExtend_Call {
	_c.Call.Return(run)
	return _c
}

// V1AccountsAccountIDStudiesAttributePost provides a mock function with given fields: ctx, request, params
func (_m *MockRecordServiceClientInterface) V1AccountsAccountIDStudiesAttributePost(ctx context.Context, request recordservice.OptStudyAttribution, params recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for V1AccountsAccountIDStudiesAttributePost")
	}

	var r0 recordservice.V1AccountsAccountIDStudiesAttributePostRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) recordservice.V1AccountsAccountIDStudiesAttributePostRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1AccountsAccountIDStudiesAttributePostRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1AccountsAccountIDStudiesAttributePost'
type MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call struct {
	*mock.Call
}

// V1AccountsAccountIDStudiesAttributePost is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptStudyAttribution
//   - params recordservice.V1AccountsAccountIDStudiesAttributePostParams
func (_e *MockRecordServiceClientInterface_Expecter) V1AccountsAccountIDStudiesAttributePost(ctx interface{}, request interface{}, params interface{}) *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call {
	return &MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call{Call: _e.mock.On("V1AccountsAccountIDStudiesAttributePost", ctx, request, params)}
}

func (_c *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call) Run(run func(ctx context.Context, request recordservice.OptStudyAttribution, params recordservice.V1AccountsAccountIDStudiesAttributePostParams)) *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptStudyAttribution), args[2].(recordservice.V1AccountsAccountIDStudiesAttributePostParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call) Return(_a0 recordservice.V1AccountsAccountIDStudiesAttributePostRes, _a1 error) *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call) RunAndReturn(run func(context.Context, recordservice.OptStudyAttribution, recordservice.V1AccountsAccountIDStudiesAttributePostParams) (recordservice.V1AccountsAccountIDStudiesAttributePostRes, error)) *MockRecordServiceClientInterface_V1AccountsAccountIDStudiesAttributePost_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebQidoSearchStudy provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1DicomwebQidoSearchStudy(ctx context.Context, params recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebQidoSearchStudy")
	}

	var r0 recordservice.V1DicomwebQidoSearchStudyRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) recordservice.V1DicomwebQidoSearchStudyRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebQidoSearchStudyRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebQidoSearchStudy'
type MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call struct {
	*mock.Call
}

// V1DicomwebQidoSearchStudy is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebQidoSearchStudyParams
func (_e *MockRecordServiceClientInterface_Expecter) V1DicomwebQidoSearchStudy(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call {
	return &MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call{Call: _e.mock.On("V1DicomwebQidoSearchStudy", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebQidoSearchStudyParams)) *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebQidoSearchStudyParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call) Return(_a0 recordservice.V1DicomwebQidoSearchStudyRes, _a1 error) *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebQidoSearchStudyParams) (recordservice.V1DicomwebQidoSearchStudyRes, error)) *MockRecordServiceClientInterface_V1DicomwebQidoSearchStudy_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebRetrieveInstance provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1DicomwebRetrieveInstance(ctx context.Context, params recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebRetrieveInstance")
	}

	var r0 recordservice.V1DicomwebRetrieveInstanceRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) recordservice.V1DicomwebRetrieveInstanceRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebRetrieveInstanceRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebRetrieveInstance'
type MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call struct {
	*mock.Call
}

// V1DicomwebRetrieveInstance is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebRetrieveInstanceParams
func (_e *MockRecordServiceClientInterface_Expecter) V1DicomwebRetrieveInstance(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call {
	return &MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call{Call: _e.mock.On("V1DicomwebRetrieveInstance", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebRetrieveInstanceParams)) *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebRetrieveInstanceParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call) Return(_a0 recordservice.V1DicomwebRetrieveInstanceRes, _a1 error) *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebRetrieveInstanceParams) (recordservice.V1DicomwebRetrieveInstanceRes, error)) *MockRecordServiceClientInterface_V1DicomwebRetrieveInstance_Call {
	_c.Call.Return(run)
	return _c
}

// V1DicomwebRetrieveMetadata provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1DicomwebRetrieveMetadata(ctx context.Context, params recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1DicomwebRetrieveMetadata")
	}

	var r0 recordservice.V1DicomwebRetrieveMetadataRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) recordservice.V1DicomwebRetrieveMetadataRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1DicomwebRetrieveMetadataRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1DicomwebRetrieveMetadata'
type MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call struct {
	*mock.Call
}

// V1DicomwebRetrieveMetadata is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1DicomwebRetrieveMetadataParams
func (_e *MockRecordServiceClientInterface_Expecter) V1DicomwebRetrieveMetadata(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call {
	return &MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call{Call: _e.mock.On("V1DicomwebRetrieveMetadata", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call) Run(run func(ctx context.Context, params recordservice.V1DicomwebRetrieveMetadataParams)) *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1DicomwebRetrieveMetadataParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call) Return(_a0 recordservice.V1DicomwebRetrieveMetadataRes, _a1 error) *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call) RunAndReturn(run func(context.Context, recordservice.V1DicomwebRetrieveMetadataParams) (recordservice.V1DicomwebRetrieveMetadataRes, error)) *MockRecordServiceClientInterface_V1DicomwebRetrieveMetadata_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesActivatePost provides a mock function with given fields: ctx, request, params
func (_m *MockRecordServiceClientInterface) V1PatientsAccountIDStudiesActivatePost(ctx context.Context, request recordservice.OptActivateStudy, params recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error) {
	ret := _m.Called(ctx, request, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesActivatePost")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesActivatePostRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error)); ok {
		return rf(ctx, request, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) recordservice.V1PatientsAccountIDStudiesActivatePostRes); ok {
		r0 = rf(ctx, request, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesActivatePostRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) error); ok {
		r1 = rf(ctx, request, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesActivatePost'
type MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesActivatePost is a helper method to define mock.On call
//   - ctx context.Context
//   - request recordservice.OptActivateStudy
//   - params recordservice.V1PatientsAccountIDStudiesActivatePostParams
func (_e *MockRecordServiceClientInterface_Expecter) V1PatientsAccountIDStudiesActivatePost(ctx interface{}, request interface{}, params interface{}) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call {
	return &MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesActivatePost", ctx, request, params)}
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call) Run(run func(ctx context.Context, request recordservice.OptActivateStudy, params recordservice.V1PatientsAccountIDStudiesActivatePostParams)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.OptActivateStudy), args[2].(recordservice.V1PatientsAccountIDStudiesActivatePostParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesActivatePostRes, _a1 error) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call) RunAndReturn(run func(context.Context, recordservice.OptActivateStudy, recordservice.V1PatientsAccountIDStudiesActivatePostParams) (recordservice.V1PatientsAccountIDStudiesActivatePostRes, error)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesActivatePost_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesGet provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1PatientsAccountIDStudiesGet(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesGet")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) recordservice.V1PatientsAccountIDStudiesGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesGet'
type MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PatientsAccountIDStudiesGetParams
func (_e *MockRecordServiceClientInterface_Expecter) V1PatientsAccountIDStudiesGet(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call {
	return &MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesGet", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call) Run(run func(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesGetParams)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PatientsAccountIDStudiesGetParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesGetRes, _a1 error) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PatientsAccountIDStudiesGetParams) (recordservice.V1PatientsAccountIDStudiesGetRes, error)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesGet_Call {
	_c.Call.Return(run)
	return _c
}

// V1PatientsAccountIDStudiesMatchGet provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1PatientsAccountIDStudiesMatchGet(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PatientsAccountIDStudiesMatchGet")
	}

	var r0 recordservice.V1PatientsAccountIDStudiesMatchGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) recordservice.V1PatientsAccountIDStudiesMatchGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PatientsAccountIDStudiesMatchGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PatientsAccountIDStudiesMatchGet'
type MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call struct {
	*mock.Call
}

// V1PatientsAccountIDStudiesMatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PatientsAccountIDStudiesMatchGetParams
func (_e *MockRecordServiceClientInterface_Expecter) V1PatientsAccountIDStudiesMatchGet(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call {
	return &MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call{Call: _e.mock.On("V1PatientsAccountIDStudiesMatchGet", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call) Run(run func(ctx context.Context, params recordservice.V1PatientsAccountIDStudiesMatchGetParams)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PatientsAccountIDStudiesMatchGetParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call) Return(_a0 recordservice.V1PatientsAccountIDStudiesMatchGetRes, _a1 error) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PatientsAccountIDStudiesMatchGetParams) (recordservice.V1PatientsAccountIDStudiesMatchGetRes, error)) *MockRecordServiceClientInterface_V1PatientsAccountIDStudiesMatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// V1PhysiciansAccountIDStudiesMatchGet provides a mock function with given fields: ctx, params
func (_m *MockRecordServiceClientInterface) V1PhysiciansAccountIDStudiesMatchGet(ctx context.Context, params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for V1PhysiciansAccountIDStudiesMatchGet")
	}

	var r0 recordservice.V1PhysiciansAccountIDStudiesMatchGetRes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) recordservice.V1PhysiciansAccountIDStudiesMatchGetRes); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(recordservice.V1PhysiciansAccountIDStudiesMatchGetRes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'V1PhysiciansAccountIDStudiesMatchGet'
type MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call struct {
	*mock.Call
}

// V1PhysiciansAccountIDStudiesMatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams
func (_e *MockRecordServiceClientInterface_Expecter) V1PhysiciansAccountIDStudiesMatchGet(ctx interface{}, params interface{}) *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call {
	return &MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call{Call: _e.mock.On("V1PhysiciansAccountIDStudiesMatchGet", ctx, params)}
}

func (_c *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call) Run(run func(ctx context.Context, params recordservice.V1PhysiciansAccountIDStudiesMatchGetParams)) *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(recordservice.V1PhysiciansAccountIDStudiesMatchGetParams))
	})
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call) Return(_a0 recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, _a1 error) *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call) RunAndReturn(run func(context.Context, recordservice.V1PhysiciansAccountIDStudiesMatchGetParams) (recordservice.V1PhysiciansAccountIDStudiesMatchGetRes, error)) *MockRecordServiceClientInterface_V1PhysiciansAccountIDStudiesMatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRecordServiceClientInterface creates a new instance of MockRecordServiceClientInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRecordServiceClientInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRecordServiceClientInterface {
	mock := &MockRecordServiceClientInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
