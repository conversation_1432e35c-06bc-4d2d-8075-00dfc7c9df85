// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockaudit

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	models "gitlab.com/pockethealth/coreapi/pkg/audit/models"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// CreatePhysicianPACSSearchEvent provides a mock function with given fields: ctx, data
func (_m *MockService) CreatePhysicianPACSSearchEvent(ctx context.Context, data models.EventDataPhysicianPACSSearch) error {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianPACSSearchEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.EventDataPhysicianPACSSearch) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_CreatePhysicianPACSSearchEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianPACSSearchEvent'
type MockService_CreatePhysicianPACSSearchEvent_Call struct {
	*mock.Call
}

// CreatePhysicianPACSSearchEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - data models.EventDataPhysicianPACSSearch
func (_e *MockService_Expecter) CreatePhysicianPACSSearchEvent(ctx interface{}, data interface{}) *MockService_CreatePhysicianPACSSearchEvent_Call {
	return &MockService_CreatePhysicianPACSSearchEvent_Call{Call: _e.mock.On("CreatePhysicianPACSSearchEvent", ctx, data)}
}

func (_c *MockService_CreatePhysicianPACSSearchEvent_Call) Run(run func(ctx context.Context, data models.EventDataPhysicianPACSSearch)) *MockService_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.EventDataPhysicianPACSSearch))
	})
	return _c
}

func (_c *MockService_CreatePhysicianPACSSearchEvent_Call) Return(_a0 error) *MockService_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_CreatePhysicianPACSSearchEvent_Call) RunAndReturn(run func(context.Context, models.EventDataPhysicianPACSSearch) error) *MockService_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianReportViewEvent provides a mock function with given fields: ctx, data
func (_m *MockService) CreatePhysicianReportViewEvent(ctx context.Context, data models.EventDataPhysicianReportView) error {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianReportViewEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.EventDataPhysicianReportView) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_CreatePhysicianReportViewEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianReportViewEvent'
type MockService_CreatePhysicianReportViewEvent_Call struct {
	*mock.Call
}

// CreatePhysicianReportViewEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - data models.EventDataPhysicianReportView
func (_e *MockService_Expecter) CreatePhysicianReportViewEvent(ctx interface{}, data interface{}) *MockService_CreatePhysicianReportViewEvent_Call {
	return &MockService_CreatePhysicianReportViewEvent_Call{Call: _e.mock.On("CreatePhysicianReportViewEvent", ctx, data)}
}

func (_c *MockService_CreatePhysicianReportViewEvent_Call) Run(run func(ctx context.Context, data models.EventDataPhysicianReportView)) *MockService_CreatePhysicianReportViewEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.EventDataPhysicianReportView))
	})
	return _c
}

func (_c *MockService_CreatePhysicianReportViewEvent_Call) Return(_a0 error) *MockService_CreatePhysicianReportViewEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_CreatePhysicianReportViewEvent_Call) RunAndReturn(run func(context.Context, models.EventDataPhysicianReportView) error) *MockService_CreatePhysicianReportViewEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianStudyRetrieveEvent provides a mock function with given fields: ctx, data
func (_m *MockService) CreatePhysicianStudyRetrieveEvent(ctx context.Context, data models.EventDataPhysicianStudyRetrieve) error {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianStudyRetrieveEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.EventDataPhysicianStudyRetrieve) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_CreatePhysicianStudyRetrieveEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianStudyRetrieveEvent'
type MockService_CreatePhysicianStudyRetrieveEvent_Call struct {
	*mock.Call
}

// CreatePhysicianStudyRetrieveEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - data models.EventDataPhysicianStudyRetrieve
func (_e *MockService_Expecter) CreatePhysicianStudyRetrieveEvent(ctx interface{}, data interface{}) *MockService_CreatePhysicianStudyRetrieveEvent_Call {
	return &MockService_CreatePhysicianStudyRetrieveEvent_Call{Call: _e.mock.On("CreatePhysicianStudyRetrieveEvent", ctx, data)}
}

func (_c *MockService_CreatePhysicianStudyRetrieveEvent_Call) Run(run func(ctx context.Context, data models.EventDataPhysicianStudyRetrieve)) *MockService_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.EventDataPhysicianStudyRetrieve))
	})
	return _c
}

func (_c *MockService_CreatePhysicianStudyRetrieveEvent_Call) Return(_a0 error) *MockService_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_CreatePhysicianStudyRetrieveEvent_Call) RunAndReturn(run func(context.Context, models.EventDataPhysicianStudyRetrieve) error) *MockService_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianStudyViewEvent provides a mock function with given fields: ctx, data
func (_m *MockService) CreatePhysicianStudyViewEvent(ctx context.Context, data models.EventDataPhysicianStudyView) error {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianStudyViewEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.EventDataPhysicianStudyView) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_CreatePhysicianStudyViewEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianStudyViewEvent'
type MockService_CreatePhysicianStudyViewEvent_Call struct {
	*mock.Call
}

// CreatePhysicianStudyViewEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - data models.EventDataPhysicianStudyView
func (_e *MockService_Expecter) CreatePhysicianStudyViewEvent(ctx interface{}, data interface{}) *MockService_CreatePhysicianStudyViewEvent_Call {
	return &MockService_CreatePhysicianStudyViewEvent_Call{Call: _e.mock.On("CreatePhysicianStudyViewEvent", ctx, data)}
}

func (_c *MockService_CreatePhysicianStudyViewEvent_Call) Run(run func(ctx context.Context, data models.EventDataPhysicianStudyView)) *MockService_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.EventDataPhysicianStudyView))
	})
	return _c
}

func (_c *MockService_CreatePhysicianStudyViewEvent_Call) Return(_a0 error) *MockService_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_CreatePhysicianStudyViewEvent_Call) RunAndReturn(run func(context.Context, models.EventDataPhysicianStudyView) error) *MockService_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
