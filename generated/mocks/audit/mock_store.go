// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockaudit

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	models "gitlab.com/pockethealth/coreapi/pkg/audit/models"
)

// MockStore is an autogenerated mock type for the Store type
type MockStore struct {
	mock.Mock
}

type MockStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockStore) EXPECT() *MockStore_Expecter {
	return &MockStore_Expecter{mock: &_m.Mock}
}

// CreatePhysicianPACSSearchEvent provides a mock function with given fields: _a0, _a1
func (_m *MockStore) CreatePhysicianPACSSearchEvent(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianPACSSearch]) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianPACSSearchEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Event[*models.EventDataPhysicianPACSSearch]) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockStore_CreatePhysicianPACSSearchEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianPACSSearchEvent'
type MockStore_CreatePhysicianPACSSearchEvent_Call struct {
	*mock.Call
}

// CreatePhysicianPACSSearchEvent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 models.Event[*models.EventDataPhysicianPACSSearch]
func (_e *MockStore_Expecter) CreatePhysicianPACSSearchEvent(_a0 interface{}, _a1 interface{}) *MockStore_CreatePhysicianPACSSearchEvent_Call {
	return &MockStore_CreatePhysicianPACSSearchEvent_Call{Call: _e.mock.On("CreatePhysicianPACSSearchEvent", _a0, _a1)}
}

func (_c *MockStore_CreatePhysicianPACSSearchEvent_Call) Run(run func(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianPACSSearch])) *MockStore_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.Event[*models.EventDataPhysicianPACSSearch]))
	})
	return _c
}

func (_c *MockStore_CreatePhysicianPACSSearchEvent_Call) Return(_a0 error) *MockStore_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStore_CreatePhysicianPACSSearchEvent_Call) RunAndReturn(run func(context.Context, models.Event[*models.EventDataPhysicianPACSSearch]) error) *MockStore_CreatePhysicianPACSSearchEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianReportViewEvent provides a mock function with given fields: _a0, _a1
func (_m *MockStore) CreatePhysicianReportViewEvent(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianReportView]) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianReportViewEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Event[*models.EventDataPhysicianReportView]) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockStore_CreatePhysicianReportViewEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianReportViewEvent'
type MockStore_CreatePhysicianReportViewEvent_Call struct {
	*mock.Call
}

// CreatePhysicianReportViewEvent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 models.Event[*models.EventDataPhysicianReportView]
func (_e *MockStore_Expecter) CreatePhysicianReportViewEvent(_a0 interface{}, _a1 interface{}) *MockStore_CreatePhysicianReportViewEvent_Call {
	return &MockStore_CreatePhysicianReportViewEvent_Call{Call: _e.mock.On("CreatePhysicianReportViewEvent", _a0, _a1)}
}

func (_c *MockStore_CreatePhysicianReportViewEvent_Call) Run(run func(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianReportView])) *MockStore_CreatePhysicianReportViewEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.Event[*models.EventDataPhysicianReportView]))
	})
	return _c
}

func (_c *MockStore_CreatePhysicianReportViewEvent_Call) Return(_a0 error) *MockStore_CreatePhysicianReportViewEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStore_CreatePhysicianReportViewEvent_Call) RunAndReturn(run func(context.Context, models.Event[*models.EventDataPhysicianReportView]) error) *MockStore_CreatePhysicianReportViewEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianStudyRetrieveEvent provides a mock function with given fields: _a0, _a1
func (_m *MockStore) CreatePhysicianStudyRetrieveEvent(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianStudyRetrieve]) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianStudyRetrieveEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Event[*models.EventDataPhysicianStudyRetrieve]) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockStore_CreatePhysicianStudyRetrieveEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianStudyRetrieveEvent'
type MockStore_CreatePhysicianStudyRetrieveEvent_Call struct {
	*mock.Call
}

// CreatePhysicianStudyRetrieveEvent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 models.Event[*models.EventDataPhysicianStudyRetrieve]
func (_e *MockStore_Expecter) CreatePhysicianStudyRetrieveEvent(_a0 interface{}, _a1 interface{}) *MockStore_CreatePhysicianStudyRetrieveEvent_Call {
	return &MockStore_CreatePhysicianStudyRetrieveEvent_Call{Call: _e.mock.On("CreatePhysicianStudyRetrieveEvent", _a0, _a1)}
}

func (_c *MockStore_CreatePhysicianStudyRetrieveEvent_Call) Run(run func(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianStudyRetrieve])) *MockStore_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.Event[*models.EventDataPhysicianStudyRetrieve]))
	})
	return _c
}

func (_c *MockStore_CreatePhysicianStudyRetrieveEvent_Call) Return(_a0 error) *MockStore_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStore_CreatePhysicianStudyRetrieveEvent_Call) RunAndReturn(run func(context.Context, models.Event[*models.EventDataPhysicianStudyRetrieve]) error) *MockStore_CreatePhysicianStudyRetrieveEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePhysicianStudyViewEvent provides a mock function with given fields: _a0, _a1
func (_m *MockStore) CreatePhysicianStudyViewEvent(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianStudyView]) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreatePhysicianStudyViewEvent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, models.Event[*models.EventDataPhysicianStudyView]) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockStore_CreatePhysicianStudyViewEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePhysicianStudyViewEvent'
type MockStore_CreatePhysicianStudyViewEvent_Call struct {
	*mock.Call
}

// CreatePhysicianStudyViewEvent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 models.Event[*models.EventDataPhysicianStudyView]
func (_e *MockStore_Expecter) CreatePhysicianStudyViewEvent(_a0 interface{}, _a1 interface{}) *MockStore_CreatePhysicianStudyViewEvent_Call {
	return &MockStore_CreatePhysicianStudyViewEvent_Call{Call: _e.mock.On("CreatePhysicianStudyViewEvent", _a0, _a1)}
}

func (_c *MockStore_CreatePhysicianStudyViewEvent_Call) Run(run func(_a0 context.Context, _a1 models.Event[*models.EventDataPhysicianStudyView])) *MockStore_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.Event[*models.EventDataPhysicianStudyView]))
	})
	return _c
}

func (_c *MockStore_CreatePhysicianStudyViewEvent_Call) Return(_a0 error) *MockStore_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStore_CreatePhysicianStudyViewEvent_Call) RunAndReturn(run func(context.Context, models.Event[*models.EventDataPhysicianStudyView]) error) *MockStore_CreatePhysicianStudyViewEvent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockStore creates a new instance of MockStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStore {
	mock := &MockStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
