// Code generated by ogen, DO NOT EDIT.

package recordservice

import (
	"bytes"
	"fmt"
	"io"
	"mime"
	"net/http"

	"github.com/go-faster/errors"
	"github.com/go-faster/jx"

	"github.com/ogen-go/ogen/ogenerrors"
	"github.com/ogen-go/ogen/validate"
)

func decodeDeletePatientStudiesResponse(resp *http.Response) (res DeletePatientStudiesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeletePatientStudiesOK{}, nil
	case 401:
		// Code 401.
		return &DeletePatientStudiesUnauthorized{}, nil
	case 499:
		// Code 499.
		return &DeletePatientStudiesCode499{}, nil
	case 500:
		// Code 500.
		return &DeletePatientStudiesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetAllPatientStudiesResponse(resp *http.Response) (res GetAllPatientStudiesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetAllPatientStudiesOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetAllPatientStudiesBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetAllPatientStudiesUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetAllPatientStudiesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetImagingResponse(resp *http.Response) (res GetImagingRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetImagingOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetImagingBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetImagingUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetImagingForbidden{}, nil
	case 500:
		// Code 500.
		return &GetImagingInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetRecentStudyUploadMetadataResponse(resp *http.Response) (res GetRecentStudyUploadMetadataRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetRecentStudyUploadMetadataOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetRecentStudyUploadMetadataBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetRecentStudyUploadMetadataUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetRecentStudyUploadMetadataInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUploadOverviewResponse(resp *http.Response) (res GetUploadOverviewRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadOverviewOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUploadOverviewUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV0ImagingTransfersResponse(resp *http.Response) (res GetV0ImagingTransfersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV0ImagingTransfersOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV0ImagingTransfersBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV0ImagingTransfersUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV0ImagingTransfersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1MeddreamGenerateResponse(resp *http.Response) (res GetV1MeddreamGenerateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1MeddreamGenerateOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV1MeddreamGenerateUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV1MeddreamGenerateNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1MeddreamGenerateInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1PatientsUploadStatusResponse(resp *http.Response) (res GetV1PatientsUploadStatusRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1PatientsUploadStatusOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV1PatientsUploadStatusBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV1PatientsUploadStatusUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV1PatientsUploadStatusInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1PhysicianStudiesResponse(resp *http.Response) (res GetV1PhysicianStudiesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PhysicianPatientStudies
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV1PhysicianStudiesBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV1PhysicianStudiesUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV1PhysicianStudiesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1StudiesPermissionsResponse(resp *http.Response) (res GetV1StudiesPermissionsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response StudyAccessPermissions
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV1StudiesPermissionsBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV1StudiesPermissionsUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV1StudiesPermissionsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1StudiesPermissionsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1StudiesUploadStatusResponse(resp *http.Response) (res GetV1StudiesUploadStatusRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1StudiesUploadStatusOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV1StudiesUploadStatusBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV1StudiesUploadStatusUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV1StudiesUploadStatusInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1SupportPatientsUploadStatusResponse(resp *http.Response) (res GetV1SupportPatientsUploadStatusRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1SupportPatientsUploadStatusOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV1SupportPatientsUploadStatusBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV1SupportPatientsUploadStatusUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV1SupportPatientsUploadStatusInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchSupportV1PatientsAccountIDStudiesUUIDRevokeResponse(resp *http.Response) (res PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDRevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDRevokeUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDRevokeNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDRevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeResponse(resp *http.Response) (res PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchV0ImagingTransfersIDRevokeResponse(resp *http.Response) (res PatchV0ImagingTransfersIDRevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchV0ImagingTransfersIDRevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchV0ImagingTransfersIDRevokeUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PatchV0ImagingTransfersIDRevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchV0ImagingTransfersIDUnrevokeResponse(resp *http.Response) (res PatchV0ImagingTransfersIDUnrevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchV0ImagingTransfersIDUnrevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchV0ImagingTransfersIDUnrevokeUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchV0ImagingTransfersIDUnrevokeNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchV0ImagingTransfersIDUnrevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchV0ImagingUUIDRevokeResponse(resp *http.Response) (res PatchV0ImagingUUIDRevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchV0ImagingUUIDRevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchV0ImagingUUIDRevokeUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchV0ImagingUUIDRevokeNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchV0ImagingUUIDRevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchV0ImagingUUIDUnrevokeResponse(resp *http.Response) (res PatchV0ImagingUUIDUnrevokeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchV0ImagingUUIDUnrevokeOK{}, nil
	case 401:
		// Code 401.
		return &PatchV0ImagingUUIDUnrevokeUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PatchV0ImagingUUIDUnrevokeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV0SharesProviderSearchResponse(resp *http.Response) (res []ProviderShare, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response []ProviderShare
			if err := func() error {
				response = make([]ProviderShare, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem ProviderShare
					if err := elem.Decode(d); err != nil {
						return err
					}
					response = append(response, elem)
					return nil
				}); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if response == nil {
					return errors.New("nil is invalid value")
				}
				var failures []validate.FieldError
				for i, elem := range response {
					if err := func() error {
						if err := elem.Validate(); err != nil {
							return err
						}
						return nil
					}(); err != nil {
						failures = append(failures, validate.FieldError{
							Name:  fmt.Sprintf("[%d]", i),
							Error: err,
						})
					}
				}
				if len(failures) > 0 {
					return &validate.Error{Fields: failures}
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1BusinessRulesetsEvaluateResponse(resp *http.Response) (res PostV1BusinessRulesetsEvaluateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV1BusinessRulesetsEvaluateSuccessResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV1BusinessRulesetsEvaluateBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV1BusinessRulesetsEvaluateUnauthorized{}, nil
	case 500:
		// Code 500.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV1BusinessRulesetsEvaluateErrorResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1MeddreamValidateResponse(resp *http.Response) (res PostV1MeddreamValidateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response MeddreamToken
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV1MeddreamValidateBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV1MeddreamValidateUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutV0SharesShareIdExtendResponse(resp *http.Response) (res PutV0SharesShareIdExtendRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutV0SharesShareIdExtendOK{}, nil
	case 400:
		// Code 400.
		return &PutV0SharesShareIdExtendBadRequest{}, nil
	case 500:
		// Code 500.
		return &PutV0SharesShareIdExtendInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1AccountsAccountIDStudiesAttributePostResponse(resp *http.Response) (res V1AccountsAccountIDStudiesAttributePostRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &V1AccountsAccountIDStudiesAttributePostOK{}, nil
	case 400:
		// Code 400.
		return &V1AccountsAccountIDStudiesAttributePostBadRequest{}, nil
	case 401:
		// Code 401.
		return &V1AccountsAccountIDStudiesAttributePostUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1AccountsAccountIDStudiesAttributePostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1DicomwebQidoSearchStudyResponse(resp *http.Response) (res V1DicomwebQidoSearchStudyRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1DicomwebQidoSearchStudyOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V1DicomwebQidoSearchStudyBadRequest{}, nil
	case 401:
		// Code 401.
		return &V1DicomwebQidoSearchStudyUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1DicomwebQidoSearchStudyNotFound{}, nil
	case 500:
		// Code 500.
		return &V1DicomwebQidoSearchStudyInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1DicomwebRetrieveInstanceResponse(resp *http.Response) (res V1DicomwebRetrieveInstanceRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/dicom":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := V1DicomwebRetrieveInstanceOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1DicomwebRetrieveInstanceUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1DicomwebRetrieveInstanceNotFound{}, nil
	case 500:
		// Code 500.
		return &V1DicomwebRetrieveInstanceInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1DicomwebRetrieveMetadataResponse(resp *http.Response) (res V1DicomwebRetrieveMetadataRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1DicomwebRetrieveMetadataOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1DicomwebRetrieveMetadataUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1DicomwebRetrieveMetadataNotFound{}, nil
	case 500:
		// Code 500.
		return &V1DicomwebRetrieveMetadataInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1PatientsAccountIDStudiesActivatePostResponse(resp *http.Response) (res V1PatientsAccountIDStudiesActivatePostRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1PatientsAccountIDStudiesActivatePostOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1PatientsAccountIDStudiesActivatePostUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1PatientsAccountIDStudiesActivatePostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1PatientsAccountIDStudiesGetResponse(resp *http.Response) (res V1PatientsAccountIDStudiesGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1PatientsAccountIDStudiesGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1PatientsAccountIDStudiesGetUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1PatientsAccountIDStudiesGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1PatientsAccountIDStudiesMatchGetResponse(resp *http.Response) (res V1PatientsAccountIDStudiesMatchGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1PatientsAccountIDStudiesMatchGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V1PatientsAccountIDStudiesMatchGetBadRequest{}, nil
	case 401:
		// Code 401.
		return &V1PatientsAccountIDStudiesMatchGetUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1PatientsAccountIDStudiesMatchGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1PhysiciansAccountIDStudiesMatchGetResponse(resp *http.Response) (res V1PhysiciansAccountIDStudiesMatchGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V1PhysiciansAccountIDStudiesMatchGetBadRequest{}, nil
	case 401:
		// Code 401.
		return &V1PhysiciansAccountIDStudiesMatchGetUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1PhysiciansAccountIDStudiesMatchGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}
