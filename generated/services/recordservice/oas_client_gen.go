// Code generated by ogen, DO NOT EDIT.

package recordservice

import (
	"context"
	"net/url"
	"strings"
	"time"

	"github.com/go-faster/errors"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/metric"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"

	"github.com/ogen-go/ogen/conv"
	ht "github.com/ogen-go/ogen/http"
	"github.com/ogen-go/ogen/ogenerrors"
	"github.com/ogen-go/ogen/otelogen"
	"github.com/ogen-go/ogen/uri"
)

// Invoker invokes operations described by OpenAPI v3 specification.
type Invoker interface {
	// DeletePatientStudies invokes DeletePatientStudies operation.
	//
	// Deletes all studies for a patient by `account_id` and `patient_id`. If a `provider_id` is
	// specified in the query parameters, only studies from that provider will be deleted.
	//
	// DELETE /internal/v1/patients/{account_id}/studies
	DeletePatientStudies(ctx context.Context, params DeletePatientStudiesParams) (DeletePatientStudiesRes, error)
	// GetAllPatientStudies invokes getAllPatientStudies operation.
	//
	// Retrieve all study data for an account ID with business rule result info.
	//
	// GET /internal/v1/patients/{account_id}/studies
	GetAllPatientStudies(ctx context.Context, params GetAllPatientStudiesParams) (GetAllPatientStudiesRes, error)
	// GetImaging invokes get-imaging operation.
	//
	// Get all the exams for an account. One of account_id, request_id, transfer_id params is required.
	// Sorted by exam date desc.
	//
	// GET /v0/imaging
	GetImaging(ctx context.Context, params GetImagingParams) (GetImagingRes, error)
	// GetRecentStudyUploadMetadata invokes getRecentStudyUploadMetadata operation.
	//
	// Retrieve recent study upload metadata.
	//
	// POST /internal/v1/providers/{provider_id}/recent-study-upload-metadata
	GetRecentStudyUploadMetadata(ctx context.Context, params GetRecentStudyUploadMetadataParams) (GetRecentStudyUploadMetadataRes, error)
	// GetUploadOverview invokes GetUploadOverview operation.
	//
	// Generates a 1-hour time bucketed view of recent study uploads over the last 12 hours for record
	// streaming providers. This provides a view into the average upload progress, study count, happy/sad
	// user (patients + physicians) count per hour.
	//
	// GET /internal/v1/providers/upload-overview
	GetUploadOverview(ctx context.Context) (GetUploadOverviewRes, error)
	// GetV0ImagingTransfers invokes get-v0-imaging-transfers operation.
	//
	// Get list of transfers for acct. Default results limit is 10 if not provided. Sorted by exam date
	// desc.
	//
	// GET /v0/imaging/transfers
	GetV0ImagingTransfers(ctx context.Context, params GetV0ImagingTransfersParams) (GetV0ImagingTransfersRes, error)
	// GetV1MeddreamGenerate invokes get-v1-meddream-generate operation.
	//
	// Generate a JWT meddream token.
	//
	// POST /v1/meddream/generate/{exam_uuid}
	GetV1MeddreamGenerate(ctx context.Context, request OptGenerateMedreamToken, params GetV1MeddreamGenerateParams) (GetV1MeddreamGenerateRes, error)
	// GetV1PatientsUploadStatus invokes get-v1-patients-upload-status operation.
	//
	// Gets a list of upload statuses for studies that a pockethealth patient account with the given
	// account_id has permission to access.
	//
	// GET /v1/patients/{account_id}/studies/upload-status
	GetV1PatientsUploadStatus(ctx context.Context, params GetV1PatientsUploadStatusParams) (GetV1PatientsUploadStatusRes, error)
	// GetV1PhysicianStudies invokes get-v1-physician-studies operation.
	//
	// Gets a list of record streaming studies that a physician has access to. This endpoint also
	// consolidates any group permissions that may apply to this physician account.
	//
	// GET /v1/physicians/{account_id}/studies
	GetV1PhysicianStudies(ctx context.Context, params GetV1PhysicianStudiesParams) (GetV1PhysicianStudiesRes, error)
	// GetV1StudiesPermissions invokes get-v1-studies-permissions operation.
	//
	// Gets user ids for users that have access permissions for a study with the given id that was shared
	// by the given provider. User ids are grouped by user type. Only one user of type patient can be
	// owner and therefore have access to a study. Multiple users of type physician can have access to a
	// study.
	//
	// GET /v1/studies/{id}/permissions
	GetV1StudiesPermissions(ctx context.Context, params GetV1StudiesPermissionsParams) (GetV1StudiesPermissionsRes, error)
	// GetV1StudiesUploadStatus invokes get-v1-studies-upload-status operation.
	//
	// Gets a list of upload states for record streaming studies that a physician has access to. Return
	// object is not a DICOM study or DICOM study tags, but instead general information on the study's
	// upload state like image upload percentage, name of sharing provider, if study has an uploaded
	// report.
	//
	// GET /v1/physicians/{account_id}/studies/upload-status
	GetV1StudiesUploadStatus(ctx context.Context, params GetV1StudiesUploadStatusParams) (GetV1StudiesUploadStatusRes, error)
	// GetV1SupportPatientsUploadStatus invokes get-v1-support-patients-upload-status operation.
	//
	// Gets a list of upload statuses for studies that a pockethealth patient account with the given
	// account_id has permission to access. Endpoint should be called only by support panel.
	//
	// GET /v1/studies/upload-status
	GetV1SupportPatientsUploadStatus(ctx context.Context, params GetV1SupportPatientsUploadStatusParams) (GetV1SupportPatientsUploadStatusRes, error)
	// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke invokes patch-support-v1-patients-account-id-studies-uuid-revoke operation.
	//
	// Revoke patient access to study.
	//
	// PATCH /support/v1/patients/{account_id}/studies/{uuid}/revoke
	PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error)
	// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke invokes patch-support-v1-patients-account-id-studies-uuid-unrevoke operation.
	//
	// Unrevoke patient access to study.
	//
	// PATCH /support/v1/patients/{account_id}/studies/{uuid}/unrevoke
	PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error)
	// PatchV0ImagingTransfersIDRevoke invokes patch-v0-imaging-transfers-id-revoke operation.
	//
	// Revoke patient access to all exams within transfer.
	//
	// PATCH /v0/imaging/transfers/{id}/revoke
	PatchV0ImagingTransfersIDRevoke(ctx context.Context, params PatchV0ImagingTransfersIDRevokeParams) (PatchV0ImagingTransfersIDRevokeRes, error)
	// PatchV0ImagingTransfersIDUnrevoke invokes patch-v0-imaging-transfers-id-unrevoke operation.
	//
	// Restore access to all exams in a transfer that have been revoked.
	//
	// PATCH /v0/imaging/transfers/{id}/unrevoke
	PatchV0ImagingTransfersIDUnrevoke(ctx context.Context, params PatchV0ImagingTransfersIDUnrevokeParams) (PatchV0ImagingTransfersIDUnrevokeRes, error)
	// PatchV0ImagingUUIDRevoke invokes patch-v0-imaging-uuid-revoke operation.
	//
	// Revoke patient access to exam.
	//
	// PATCH /v0/imaging/{uuid}/revoke
	PatchV0ImagingUUIDRevoke(ctx context.Context, params PatchV0ImagingUUIDRevokeParams) (PatchV0ImagingUUIDRevokeRes, error)
	// PatchV0ImagingUUIDUnrevoke invokes patch-v0-imaging-uuid-unrevoke operation.
	//
	// Restore access to an exam IFF it has been revoked. If not, this is a no-op (will still return 200
	// if so).
	//
	// PATCH /v0/imaging/{uuid}/unrevoke
	PatchV0ImagingUUIDUnrevoke(ctx context.Context, params PatchV0ImagingUUIDUnrevokeParams) (PatchV0ImagingUUIDUnrevokeRes, error)
	// PostV0SharesProviderSearch invokes post-v0-shares-provider-search operation.
	//
	// Search provider shares by their access code and/or DOB.
	//
	// POST /v0/shares/provider/search
	PostV0SharesProviderSearch(ctx context.Context, request OptProviderShareSearch) ([]ProviderShare, error)
	// PostV1BusinessRulesetsEvaluate invokes post-v1-business-rulesets-evaluate operation.
	//
	// Evaluate business rules given a ruleset and dicom tags.
	//
	// POST /v1/business-rulesets/evaluate
	PostV1BusinessRulesetsEvaluate(ctx context.Context, request OptPostV1BusinessRulesetsEvaluateReq) (PostV1BusinessRulesetsEvaluateRes, error)
	// PostV1MeddreamValidate invokes post-v1-meddream-validate operation.
	//
	// Validate a JWT meddream token.
	//
	// GET /v1/meddream/validate
	PostV1MeddreamValidate(ctx context.Context, params PostV1MeddreamValidateParams) (PostV1MeddreamValidateRes, error)
	// PutV0SharesShareIdExtend invokes put-v0-shares-shareId-extend operation.
	//
	// Extend the expiration of a Provider or Patient Share.
	//
	// PUT /v0/shares/{shareId}/extend
	PutV0SharesShareIdExtend(ctx context.Context, params PutV0SharesShareIdExtendParams) (PutV0SharesShareIdExtendRes, error)
	// V1AccountsAccountIDStudiesAttributePost invokes POST /v1/accounts/{account_id}/studies/attribute operation.
	//
	// Attribute the studies with the provided UUIDs and AvailabilityStatus.
	//
	// POST /v1/accounts/{account_id}/studies/attribute
	V1AccountsAccountIDStudiesAttributePost(ctx context.Context, request OptStudyAttribution, params V1AccountsAccountIDStudiesAttributePostParams) (V1AccountsAccountIDStudiesAttributePostRes, error)
	// V1DicomwebQidoSearchStudy invokes v1-dicomweb-qido-search-study operation.
	//
	// Search for study.
	//
	// GET /v1/dicoweb/search/studies
	V1DicomwebQidoSearchStudy(ctx context.Context, params V1DicomwebQidoSearchStudyParams) (V1DicomwebQidoSearchStudyRes, error)
	// V1DicomwebRetrieveInstance invokes v1-dicomweb-retrieve-instance operation.
	//
	// Return Dicom file with StudyInstanceUID tag (0020,000D).
	//
	// GET /v1/dicoweb/studies/images/{instance_uid}
	V1DicomwebRetrieveInstance(ctx context.Context, params V1DicomwebRetrieveInstanceParams) (V1DicomwebRetrieveInstanceRes, error)
	// V1DicomwebRetrieveMetadata invokes v1-dicomweb-retrieve-metadata operation.
	//
	// Get study metadata in Dicom Nema Json Model.
	//
	// GET /v1/dicoweb/studies/{study_uid}/metadata
	V1DicomwebRetrieveMetadata(ctx context.Context, params V1DicomwebRetrieveMetadataParams) (V1DicomwebRetrieveMetadataRes, error)
	// V1PatientsAccountIDStudiesActivatePost invokes POST /v1/patients/{account_id}/studies/activate operation.
	//
	// Activates and returns list of studies eligible for the provided date of birth.
	//
	// POST /v1/patients/{account_id}/studies/activate
	V1PatientsAccountIDStudiesActivatePost(ctx context.Context, request OptActivateStudy, params V1PatientsAccountIDStudiesActivatePostParams) (V1PatientsAccountIDStudiesActivatePostRes, error)
	// V1PatientsAccountIDStudiesGet invokes GET /v1/patients/{account_id}/studies operation.
	//
	// Fetches studies for a patient account with optional filtering by fetch mode
	// and optional uuids for study point-queries.
	// Requires authentication and read permissions.
	//
	// GET /v1/patients/{account_id}/studies
	V1PatientsAccountIDStudiesGet(ctx context.Context, params V1PatientsAccountIDStudiesGetParams) (V1PatientsAccountIDStudiesGetRes, error)
	// V1PatientsAccountIDStudiesMatchGet invokes GET /v1/patients/{account_id}/studies/match operation.
	//
	// Checks if studies belong to a patient account. Returns true if all studies provided belong to the
	// patient.
	// Requires authentication and read permissions.
	//
	// GET /v1/patients/{account_id}/studies/match
	V1PatientsAccountIDStudiesMatchGet(ctx context.Context, params V1PatientsAccountIDStudiesMatchGetParams) (V1PatientsAccountIDStudiesMatchGetRes, error)
	// V1PhysiciansAccountIDStudiesMatchGet invokes GET /v1/physicians/{account_id}/studies/match operation.
	//
	// Checks if studies are accessible to a physician account. Returns true
	// if all studies provided are accessible to the physician.
	// Requires authentication and read permissions.
	//
	// GET /v1/physicians/{account_id}/studies/match
	V1PhysiciansAccountIDStudiesMatchGet(ctx context.Context, params V1PhysiciansAccountIDStudiesMatchGetParams) (V1PhysiciansAccountIDStudiesMatchGetRes, error)
}

// Client implements OAS client.
type Client struct {
	serverURL *url.URL
	sec       SecuritySource
	baseClient
}

func trimTrailingSlashes(u *url.URL) {
	u.Path = strings.TrimRight(u.Path, "/")
	u.RawPath = strings.TrimRight(u.RawPath, "/")
}

// NewClient initializes new Client defined by OAS.
func NewClient(serverURL string, sec SecuritySource, opts ...ClientOption) (*Client, error) {
	u, err := url.Parse(serverURL)
	if err != nil {
		return nil, err
	}
	trimTrailingSlashes(u)

	c, err := newClientConfig(opts...).baseClient()
	if err != nil {
		return nil, err
	}
	return &Client{
		serverURL:  u,
		sec:        sec,
		baseClient: c,
	}, nil
}

type serverURLKey struct{}

// WithServerURL sets context key to override server URL.
func WithServerURL(ctx context.Context, u *url.URL) context.Context {
	return context.WithValue(ctx, serverURLKey{}, u)
}

func (c *Client) requestURL(ctx context.Context) *url.URL {
	u, ok := ctx.Value(serverURLKey{}).(*url.URL)
	if !ok {
		return c.serverURL
	}
	return u
}

// DeletePatientStudies invokes DeletePatientStudies operation.
//
// Deletes all studies for a patient by `account_id` and `patient_id`. If a `provider_id` is
// specified in the query parameters, only studies from that provider will be deleted.
//
// DELETE /internal/v1/patients/{account_id}/studies
func (c *Client) DeletePatientStudies(ctx context.Context, params DeletePatientStudiesParams) (DeletePatientStudiesRes, error) {
	res, err := c.sendDeletePatientStudies(ctx, params)
	return res, err
}

func (c *Client) sendDeletePatientStudies(ctx context.Context, params DeletePatientStudiesParams) (res DeletePatientStudiesRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("DeletePatientStudies"),
		semconv.HTTPRequestMethodKey.String("DELETE"),
		semconv.HTTPRouteKey.String("/internal/v1/patients/{account_id}/studies"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "DeletePatientStudies",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/internal/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "provider_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "provider_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.ProviderID.Get(); ok {
				return e.EncodeValue(conv.Int64ToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "patient_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "patient_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.PatientID.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "DELETE", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeDeletePatientStudiesResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetAllPatientStudies invokes getAllPatientStudies operation.
//
// Retrieve all study data for an account ID with business rule result info.
//
// GET /internal/v1/patients/{account_id}/studies
func (c *Client) GetAllPatientStudies(ctx context.Context, params GetAllPatientStudiesParams) (GetAllPatientStudiesRes, error) {
	res, err := c.sendGetAllPatientStudies(ctx, params)
	return res, err
}

func (c *Client) sendGetAllPatientStudies(ctx context.Context, params GetAllPatientStudiesParams) (res GetAllPatientStudiesRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("getAllPatientStudies"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/internal/v1/patients/{account_id}/studies"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetAllPatientStudies",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/internal/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetAllPatientStudiesResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetImaging invokes get-imaging operation.
//
// Get all the exams for an account. One of account_id, request_id, transfer_id params is required.
// Sorted by exam date desc.
//
// GET /v0/imaging
func (c *Client) GetImaging(ctx context.Context, params GetImagingParams) (GetImagingRes, error) {
	res, err := c.sendGetImaging(ctx, params)
	return res, err
}

func (c *Client) sendGetImaging(ctx context.Context, params GetImagingParams) (res GetImagingRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-imaging"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v0/imaging"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetImaging",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v0/imaging"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "account_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "account_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.AccountID.Get(); ok {
				return e.EncodeValue(conv.IntToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "request_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "request_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.RequestID.Get(); ok {
				return e.EncodeValue(conv.IntToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "limit" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "limit",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.Limit.Get(); ok {
				return e.EncodeValue(conv.Int32ToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "offset" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "offset",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.Offset.Get(); ok {
				return e.EncodeValue(conv.Int32ToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "offset_ids" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "offset_ids",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.OffsetIds.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "transfer_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "transfer_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.TransferID.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "GetImaging", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetImagingResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetRecentStudyUploadMetadata invokes getRecentStudyUploadMetadata operation.
//
// Retrieve recent study upload metadata.
//
// POST /internal/v1/providers/{provider_id}/recent-study-upload-metadata
func (c *Client) GetRecentStudyUploadMetadata(ctx context.Context, params GetRecentStudyUploadMetadataParams) (GetRecentStudyUploadMetadataRes, error) {
	res, err := c.sendGetRecentStudyUploadMetadata(ctx, params)
	return res, err
}

func (c *Client) sendGetRecentStudyUploadMetadata(ctx context.Context, params GetRecentStudyUploadMetadataParams) (res GetRecentStudyUploadMetadataRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("getRecentStudyUploadMetadata"),
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/internal/v1/providers/{provider_id}/recent-study-upload-metadata"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetRecentStudyUploadMetadata",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/internal/v1/providers/"
	{
		// Encode "provider_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "provider_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.Int64ToString(params.ProviderID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/recent-study-upload-metadata"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetRecentStudyUploadMetadataResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetUploadOverview invokes GetUploadOverview operation.
//
// Generates a 1-hour time bucketed view of recent study uploads over the last 12 hours for record
// streaming providers. This provides a view into the average upload progress, study count, happy/sad
// user (patients + physicians) count per hour.
//
// GET /internal/v1/providers/upload-overview
func (c *Client) GetUploadOverview(ctx context.Context) (GetUploadOverviewRes, error) {
	res, err := c.sendGetUploadOverview(ctx)
	return res, err
}

func (c *Client) sendGetUploadOverview(ctx context.Context) (res GetUploadOverviewRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("GetUploadOverview"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/internal/v1/providers/upload-overview"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetUploadOverview",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/internal/v1/providers/upload-overview"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetUploadOverviewResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV0ImagingTransfers invokes get-v0-imaging-transfers operation.
//
// Get list of transfers for acct. Default results limit is 10 if not provided. Sorted by exam date
// desc.
//
// GET /v0/imaging/transfers
func (c *Client) GetV0ImagingTransfers(ctx context.Context, params GetV0ImagingTransfersParams) (GetV0ImagingTransfersRes, error) {
	res, err := c.sendGetV0ImagingTransfers(ctx, params)
	return res, err
}

func (c *Client) sendGetV0ImagingTransfers(ctx context.Context, params GetV0ImagingTransfersParams) (res GetV0ImagingTransfersRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v0-imaging-transfers"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v0/imaging/transfers"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV0ImagingTransfers",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v0/imaging/transfers"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "account_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "account_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.IntToString(params.AccountID))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "limit" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "limit",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.Limit.Get(); ok {
				return e.EncodeValue(conv.IntToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "offset" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "offset",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.Offset.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "GetV0ImagingTransfers", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV0ImagingTransfersResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1MeddreamGenerate invokes get-v1-meddream-generate operation.
//
// Generate a JWT meddream token.
//
// POST /v1/meddream/generate/{exam_uuid}
func (c *Client) GetV1MeddreamGenerate(ctx context.Context, request OptGenerateMedreamToken, params GetV1MeddreamGenerateParams) (GetV1MeddreamGenerateRes, error) {
	res, err := c.sendGetV1MeddreamGenerate(ctx, request, params)
	return res, err
}

func (c *Client) sendGetV1MeddreamGenerate(ctx context.Context, request OptGenerateMedreamToken, params GetV1MeddreamGenerateParams) (res GetV1MeddreamGenerateRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-meddream-generate"),
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/v1/meddream/generate/{exam_uuid}"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1MeddreamGenerate",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [2]string
	pathParts[0] = "/v1/meddream/generate/"
	{
		// Encode "exam_uuid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "exam_uuid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.ExamUUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}
	if err := encodeGetV1MeddreamGenerateRequest(request, r); err != nil {
		return res, errors.Wrap(err, "encode request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "GetV1MeddreamGenerate", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1MeddreamGenerateResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1PatientsUploadStatus invokes get-v1-patients-upload-status operation.
//
// Gets a list of upload statuses for studies that a pockethealth patient account with the given
// account_id has permission to access.
//
// GET /v1/patients/{account_id}/studies/upload-status
func (c *Client) GetV1PatientsUploadStatus(ctx context.Context, params GetV1PatientsUploadStatusParams) (GetV1PatientsUploadStatusRes, error) {
	res, err := c.sendGetV1PatientsUploadStatus(ctx, params)
	return res, err
}

func (c *Client) sendGetV1PatientsUploadStatus(ctx context.Context, params GetV1PatientsUploadStatusParams) (res GetV1PatientsUploadStatusRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-patients-upload-status"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/patients/{account_id}/studies/upload-status"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1PatientsUploadStatus",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/upload-status"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:JwtBearer"
			switch err := c.securityJwtBearer(ctx, "GetV1PatientsUploadStatus", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"JwtBearer\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1PatientsUploadStatusResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1PhysicianStudies invokes get-v1-physician-studies operation.
//
// Gets a list of record streaming studies that a physician has access to. This endpoint also
// consolidates any group permissions that may apply to this physician account.
//
// GET /v1/physicians/{account_id}/studies
func (c *Client) GetV1PhysicianStudies(ctx context.Context, params GetV1PhysicianStudiesParams) (GetV1PhysicianStudiesRes, error) {
	res, err := c.sendGetV1PhysicianStudies(ctx, params)
	return res, err
}

func (c *Client) sendGetV1PhysicianStudies(ctx context.Context, params GetV1PhysicianStudiesParams) (res GetV1PhysicianStudiesRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-physician-studies"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/physicians/{account_id}/studies"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1PhysicianStudies",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/physicians/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "uuid" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "uuid",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if params.UUID != nil {
				return e.EncodeArray(func(e uri.Encoder) error {
					for i, item := range params.UUID {
						if err := func() error {
							return e.EncodeValue(conv.StringToString(item))
						}(); err != nil {
							return errors.Wrapf(err, "[%d]", i)
						}
					}
					return nil
				})
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "include_reports" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "include_reports",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.IncludeReports.Get(); ok {
				return e.EncodeValue(conv.BoolToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "include_instances" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "include_instances",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.IncludeInstances.Get(); ok {
				return e.EncodeValue(conv.BoolToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "patient_name" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "patient_name",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.PatientName.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "patient_birthdate" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "patient_birthdate",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.PatientBirthdate.Get(); ok {
				return e.EncodeValue(conv.StringToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "provider_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "provider_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.ProviderID.Get(); ok {
				return e.EncodeValue(conv.Int64ToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "GetV1PhysicianStudies", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1PhysicianStudiesResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1StudiesPermissions invokes get-v1-studies-permissions operation.
//
// Gets user ids for users that have access permissions for a study with the given id that was shared
// by the given provider. User ids are grouped by user type. Only one user of type patient can be
// owner and therefore have access to a study. Multiple users of type physician can have access to a
// study.
//
// GET /v1/studies/{id}/permissions
func (c *Client) GetV1StudiesPermissions(ctx context.Context, params GetV1StudiesPermissionsParams) (GetV1StudiesPermissionsRes, error) {
	res, err := c.sendGetV1StudiesPermissions(ctx, params)
	return res, err
}

func (c *Client) sendGetV1StudiesPermissions(ctx context.Context, params GetV1StudiesPermissionsParams) (res GetV1StudiesPermissionsRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-studies-permissions"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/studies/{id}/permissions"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1StudiesPermissions",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/studies/"
	{
		// Encode "id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.ID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/permissions"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "provider_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "provider_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.ProviderID.Get(); ok {
				return e.EncodeValue(conv.IntToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1StudiesPermissionsResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1StudiesUploadStatus invokes get-v1-studies-upload-status operation.
//
// Gets a list of upload states for record streaming studies that a physician has access to. Return
// object is not a DICOM study or DICOM study tags, but instead general information on the study's
// upload state like image upload percentage, name of sharing provider, if study has an uploaded
// report.
//
// GET /v1/physicians/{account_id}/studies/upload-status
func (c *Client) GetV1StudiesUploadStatus(ctx context.Context, params GetV1StudiesUploadStatusParams) (GetV1StudiesUploadStatusRes, error) {
	res, err := c.sendGetV1StudiesUploadStatus(ctx, params)
	return res, err
}

func (c *Client) sendGetV1StudiesUploadStatus(ctx context.Context, params GetV1StudiesUploadStatusParams) (res GetV1StudiesUploadStatusRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-studies-upload-status"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/physicians/{account_id}/studies/upload-status"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1StudiesUploadStatus",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/physicians/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/upload-status"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "GetV1StudiesUploadStatus", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1StudiesUploadStatusResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// GetV1SupportPatientsUploadStatus invokes get-v1-support-patients-upload-status operation.
//
// Gets a list of upload statuses for studies that a pockethealth patient account with the given
// account_id has permission to access. Endpoint should be called only by support panel.
//
// GET /v1/studies/upload-status
func (c *Client) GetV1SupportPatientsUploadStatus(ctx context.Context, params GetV1SupportPatientsUploadStatusParams) (GetV1SupportPatientsUploadStatusRes, error) {
	res, err := c.sendGetV1SupportPatientsUploadStatus(ctx, params)
	return res, err
}

func (c *Client) sendGetV1SupportPatientsUploadStatus(ctx context.Context, params GetV1SupportPatientsUploadStatusParams) (res GetV1SupportPatientsUploadStatusRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("get-v1-support-patients-upload-status"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/studies/upload-status"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "GetV1SupportPatientsUploadStatus",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v1/studies/upload-status"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "account_id" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "account_id",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:JwtBearer"
			switch err := c.securityJwtBearer(ctx, "GetV1SupportPatientsUploadStatus", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"JwtBearer\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeGetV1SupportPatientsUploadStatusResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevoke invokes patch-support-v1-patients-account-id-studies-uuid-revoke operation.
//
// Revoke patient access to study.
//
// PATCH /support/v1/patients/{account_id}/studies/{uuid}/revoke
func (c *Client) PatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, error) {
	res, err := c.sendPatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchSupportV1PatientsAccountIDStudiesUUIDRevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams) (res PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-support-v1-patients-account-id-studies-uuid-revoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/support/v1/patients/{account_id}/studies/{uuid}/revoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchSupportV1PatientsAccountIDStudiesUUIDRevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [5]string
	pathParts[0] = "/support/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/"
	{
		// Encode "uuid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "uuid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.UUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[3] = encoded
	}
	pathParts[4] = "/revoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:JwtBearer"
			switch err := c.securityJwtBearer(ctx, "PatchSupportV1PatientsAccountIDStudiesUUIDRevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"JwtBearer\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchSupportV1PatientsAccountIDStudiesUUIDRevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke invokes patch-support-v1-patients-account-id-studies-uuid-unrevoke operation.
//
// Unrevoke patient access to study.
//
// PATCH /support/v1/patients/{account_id}/studies/{uuid}/unrevoke
func (c *Client) PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, error) {
	res, err := c.sendPatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke(ctx context.Context, params PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams) (res PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-support-v1-patients-account-id-studies-uuid-unrevoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/support/v1/patients/{account_id}/studies/{uuid}/unrevoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [5]string
	pathParts[0] = "/support/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/"
	{
		// Encode "uuid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "uuid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.UUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[3] = encoded
	}
	pathParts[4] = "/unrevoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:JwtBearer"
			switch err := c.securityJwtBearer(ctx, "PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"JwtBearer\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchV0ImagingTransfersIDRevoke invokes patch-v0-imaging-transfers-id-revoke operation.
//
// Revoke patient access to all exams within transfer.
//
// PATCH /v0/imaging/transfers/{id}/revoke
func (c *Client) PatchV0ImagingTransfersIDRevoke(ctx context.Context, params PatchV0ImagingTransfersIDRevokeParams) (PatchV0ImagingTransfersIDRevokeRes, error) {
	res, err := c.sendPatchV0ImagingTransfersIDRevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchV0ImagingTransfersIDRevoke(ctx context.Context, params PatchV0ImagingTransfersIDRevokeParams) (res PatchV0ImagingTransfersIDRevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-v0-imaging-transfers-id-revoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/v0/imaging/transfers/{id}/revoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchV0ImagingTransfersIDRevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v0/imaging/transfers/"
	{
		// Encode "id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.ID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/revoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PatchV0ImagingTransfersIDRevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchV0ImagingTransfersIDRevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchV0ImagingTransfersIDUnrevoke invokes patch-v0-imaging-transfers-id-unrevoke operation.
//
// Restore access to all exams in a transfer that have been revoked.
//
// PATCH /v0/imaging/transfers/{id}/unrevoke
func (c *Client) PatchV0ImagingTransfersIDUnrevoke(ctx context.Context, params PatchV0ImagingTransfersIDUnrevokeParams) (PatchV0ImagingTransfersIDUnrevokeRes, error) {
	res, err := c.sendPatchV0ImagingTransfersIDUnrevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchV0ImagingTransfersIDUnrevoke(ctx context.Context, params PatchV0ImagingTransfersIDUnrevokeParams) (res PatchV0ImagingTransfersIDUnrevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-v0-imaging-transfers-id-unrevoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/v0/imaging/transfers/{id}/unrevoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchV0ImagingTransfersIDUnrevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v0/imaging/transfers/"
	{
		// Encode "id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.ID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/unrevoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PatchV0ImagingTransfersIDUnrevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchV0ImagingTransfersIDUnrevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchV0ImagingUUIDRevoke invokes patch-v0-imaging-uuid-revoke operation.
//
// Revoke patient access to exam.
//
// PATCH /v0/imaging/{uuid}/revoke
func (c *Client) PatchV0ImagingUUIDRevoke(ctx context.Context, params PatchV0ImagingUUIDRevokeParams) (PatchV0ImagingUUIDRevokeRes, error) {
	res, err := c.sendPatchV0ImagingUUIDRevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchV0ImagingUUIDRevoke(ctx context.Context, params PatchV0ImagingUUIDRevokeParams) (res PatchV0ImagingUUIDRevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-v0-imaging-uuid-revoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/v0/imaging/{uuid}/revoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchV0ImagingUUIDRevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v0/imaging/"
	{
		// Encode "uuid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "uuid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.UUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/revoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PatchV0ImagingUUIDRevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchV0ImagingUUIDRevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PatchV0ImagingUUIDUnrevoke invokes patch-v0-imaging-uuid-unrevoke operation.
//
// Restore access to an exam IFF it has been revoked. If not, this is a no-op (will still return 200
// if so).
//
// PATCH /v0/imaging/{uuid}/unrevoke
func (c *Client) PatchV0ImagingUUIDUnrevoke(ctx context.Context, params PatchV0ImagingUUIDUnrevokeParams) (PatchV0ImagingUUIDUnrevokeRes, error) {
	res, err := c.sendPatchV0ImagingUUIDUnrevoke(ctx, params)
	return res, err
}

func (c *Client) sendPatchV0ImagingUUIDUnrevoke(ctx context.Context, params PatchV0ImagingUUIDUnrevokeParams) (res PatchV0ImagingUUIDUnrevokeRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("patch-v0-imaging-uuid-unrevoke"),
		semconv.HTTPRequestMethodKey.String("PATCH"),
		semconv.HTTPRouteKey.String("/v0/imaging/{uuid}/unrevoke"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PatchV0ImagingUUIDUnrevoke",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v0/imaging/"
	{
		// Encode "uuid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "uuid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.UUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/unrevoke"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PATCH", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PatchV0ImagingUUIDUnrevoke", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePatchV0ImagingUUIDUnrevokeResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PostV0SharesProviderSearch invokes post-v0-shares-provider-search operation.
//
// Search provider shares by their access code and/or DOB.
//
// POST /v0/shares/provider/search
func (c *Client) PostV0SharesProviderSearch(ctx context.Context, request OptProviderShareSearch) ([]ProviderShare, error) {
	res, err := c.sendPostV0SharesProviderSearch(ctx, request)
	return res, err
}

func (c *Client) sendPostV0SharesProviderSearch(ctx context.Context, request OptProviderShareSearch) (res []ProviderShare, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("post-v0-shares-provider-search"),
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/v0/shares/provider/search"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PostV0SharesProviderSearch",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v0/shares/provider/search"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}
	if err := encodePostV0SharesProviderSearchRequest(request, r); err != nil {
		return res, errors.Wrap(err, "encode request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PostV0SharesProviderSearch", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePostV0SharesProviderSearchResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PostV1BusinessRulesetsEvaluate invokes post-v1-business-rulesets-evaluate operation.
//
// Evaluate business rules given a ruleset and dicom tags.
//
// POST /v1/business-rulesets/evaluate
func (c *Client) PostV1BusinessRulesetsEvaluate(ctx context.Context, request OptPostV1BusinessRulesetsEvaluateReq) (PostV1BusinessRulesetsEvaluateRes, error) {
	res, err := c.sendPostV1BusinessRulesetsEvaluate(ctx, request)
	return res, err
}

func (c *Client) sendPostV1BusinessRulesetsEvaluate(ctx context.Context, request OptPostV1BusinessRulesetsEvaluateReq) (res PostV1BusinessRulesetsEvaluateRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("post-v1-business-rulesets-evaluate"),
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/v1/business-rulesets/evaluate"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PostV1BusinessRulesetsEvaluate",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v1/business-rulesets/evaluate"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}
	if err := encodePostV1BusinessRulesetsEvaluateRequest(request, r); err != nil {
		return res, errors.Wrap(err, "encode request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "PostV1BusinessRulesetsEvaluate", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePostV1BusinessRulesetsEvaluateResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PostV1MeddreamValidate invokes post-v1-meddream-validate operation.
//
// Validate a JWT meddream token.
//
// GET /v1/meddream/validate
func (c *Client) PostV1MeddreamValidate(ctx context.Context, params PostV1MeddreamValidateParams) (PostV1MeddreamValidateRes, error) {
	res, err := c.sendPostV1MeddreamValidate(ctx, params)
	return res, err
}

func (c *Client) sendPostV1MeddreamValidate(ctx context.Context, params PostV1MeddreamValidateParams) (res PostV1MeddreamValidateRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("post-v1-meddream-validate"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/meddream/validate"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PostV1MeddreamValidate",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v1/meddream/validate"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "token" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "token",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.Token))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PostV1MeddreamValidate", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePostV1MeddreamValidateResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// PutV0SharesShareIdExtend invokes put-v0-shares-shareId-extend operation.
//
// Extend the expiration of a Provider or Patient Share.
//
// PUT /v0/shares/{shareId}/extend
func (c *Client) PutV0SharesShareIdExtend(ctx context.Context, params PutV0SharesShareIdExtendParams) (PutV0SharesShareIdExtendRes, error) {
	res, err := c.sendPutV0SharesShareIdExtend(ctx, params)
	return res, err
}

func (c *Client) sendPutV0SharesShareIdExtend(ctx context.Context, params PutV0SharesShareIdExtendParams) (res PutV0SharesShareIdExtendRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("put-v0-shares-shareId-extend"),
		semconv.HTTPRequestMethodKey.String("PUT"),
		semconv.HTTPRouteKey.String("/v0/shares/{shareId}/extend"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "PutV0SharesShareIdExtend",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v0/shares/"
	{
		// Encode "shareId" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "shareId",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.ShareId))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/extend"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "PUT", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "PutV0SharesShareIdExtend", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodePutV0SharesShareIdExtendResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1AccountsAccountIDStudiesAttributePost invokes POST /v1/accounts/{account_id}/studies/attribute operation.
//
// Attribute the studies with the provided UUIDs and AvailabilityStatus.
//
// POST /v1/accounts/{account_id}/studies/attribute
func (c *Client) V1AccountsAccountIDStudiesAttributePost(ctx context.Context, request OptStudyAttribution, params V1AccountsAccountIDStudiesAttributePostParams) (V1AccountsAccountIDStudiesAttributePostRes, error) {
	res, err := c.sendV1AccountsAccountIDStudiesAttributePost(ctx, request, params)
	return res, err
}

func (c *Client) sendV1AccountsAccountIDStudiesAttributePost(ctx context.Context, request OptStudyAttribution, params V1AccountsAccountIDStudiesAttributePostParams) (res V1AccountsAccountIDStudiesAttributePostRes, err error) {
	otelAttrs := []attribute.KeyValue{
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/v1/accounts/{account_id}/studies/attribute"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1AccountsAccountIDStudiesAttributePost",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/accounts/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/attribute"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}
	if err := encodeV1AccountsAccountIDStudiesAttributePostRequest(request, r); err != nil {
		return res, errors.Wrap(err, "encode request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "V1AccountsAccountIDStudiesAttributePost", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1AccountsAccountIDStudiesAttributePostResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1DicomwebQidoSearchStudy invokes v1-dicomweb-qido-search-study operation.
//
// Search for study.
//
// GET /v1/dicoweb/search/studies
func (c *Client) V1DicomwebQidoSearchStudy(ctx context.Context, params V1DicomwebQidoSearchStudyParams) (V1DicomwebQidoSearchStudyRes, error) {
	res, err := c.sendV1DicomwebQidoSearchStudy(ctx, params)
	return res, err
}

func (c *Client) sendV1DicomwebQidoSearchStudy(ctx context.Context, params V1DicomwebQidoSearchStudyParams) (res V1DicomwebQidoSearchStudyRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("v1-dicomweb-qido-search-study"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/dicoweb/search/studies"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1DicomwebQidoSearchStudy",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [1]string
	pathParts[0] = "/v1/dicoweb/search/studies"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "0020000D" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "0020000D",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.R0020000D))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "custom1" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "custom1",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.Custom1))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "V1DicomwebQidoSearchStudy", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1DicomwebQidoSearchStudyResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1DicomwebRetrieveInstance invokes v1-dicomweb-retrieve-instance operation.
//
// Return Dicom file with StudyInstanceUID tag (0020,000D).
//
// GET /v1/dicoweb/studies/images/{instance_uid}
func (c *Client) V1DicomwebRetrieveInstance(ctx context.Context, params V1DicomwebRetrieveInstanceParams) (V1DicomwebRetrieveInstanceRes, error) {
	res, err := c.sendV1DicomwebRetrieveInstance(ctx, params)
	return res, err
}

func (c *Client) sendV1DicomwebRetrieveInstance(ctx context.Context, params V1DicomwebRetrieveInstanceParams) (res V1DicomwebRetrieveInstanceRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("v1-dicomweb-retrieve-instance"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/dicoweb/studies/images/{instance_uid}"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1DicomwebRetrieveInstance",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [2]string
	pathParts[0] = "/v1/dicoweb/studies/images/"
	{
		// Encode "instance_uid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "instance_uid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.InstanceUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "custom1" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "custom1",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.Custom1))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "V1DicomwebRetrieveInstance", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1DicomwebRetrieveInstanceResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1DicomwebRetrieveMetadata invokes v1-dicomweb-retrieve-metadata operation.
//
// Get study metadata in Dicom Nema Json Model.
//
// GET /v1/dicoweb/studies/{study_uid}/metadata
func (c *Client) V1DicomwebRetrieveMetadata(ctx context.Context, params V1DicomwebRetrieveMetadataParams) (V1DicomwebRetrieveMetadataRes, error) {
	res, err := c.sendV1DicomwebRetrieveMetadata(ctx, params)
	return res, err
}

func (c *Client) sendV1DicomwebRetrieveMetadata(ctx context.Context, params V1DicomwebRetrieveMetadataParams) (res V1DicomwebRetrieveMetadataRes, err error) {
	otelAttrs := []attribute.KeyValue{
		otelogen.OperationID("v1-dicomweb-retrieve-metadata"),
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/dicoweb/studies/{study_uid}/metadata"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1DicomwebRetrieveMetadata",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/dicoweb/studies/"
	{
		// Encode "study_uid" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "study_uid",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.StudyUID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/metadata"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "custom1" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "custom1",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeValue(conv.StringToString(params.Custom1))
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:ApiKey1"
			switch err := c.securityApiKey1(ctx, "V1DicomwebRetrieveMetadata", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"ApiKey1\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1DicomwebRetrieveMetadataResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1PatientsAccountIDStudiesActivatePost invokes POST /v1/patients/{account_id}/studies/activate operation.
//
// Activates and returns list of studies eligible for the provided date of birth.
//
// POST /v1/patients/{account_id}/studies/activate
func (c *Client) V1PatientsAccountIDStudiesActivatePost(ctx context.Context, request OptActivateStudy, params V1PatientsAccountIDStudiesActivatePostParams) (V1PatientsAccountIDStudiesActivatePostRes, error) {
	res, err := c.sendV1PatientsAccountIDStudiesActivatePost(ctx, request, params)
	return res, err
}

func (c *Client) sendV1PatientsAccountIDStudiesActivatePost(ctx context.Context, request OptActivateStudy, params V1PatientsAccountIDStudiesActivatePostParams) (res V1PatientsAccountIDStudiesActivatePostRes, err error) {
	otelAttrs := []attribute.KeyValue{
		semconv.HTTPRequestMethodKey.String("POST"),
		semconv.HTTPRouteKey.String("/v1/patients/{account_id}/studies/activate"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1PatientsAccountIDStudiesActivatePost",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/activate"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "POST", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}
	if err := encodeV1PatientsAccountIDStudiesActivatePostRequest(request, r); err != nil {
		return res, errors.Wrap(err, "encode request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "V1PatientsAccountIDStudiesActivatePost", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1PatientsAccountIDStudiesActivatePostResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1PatientsAccountIDStudiesGet invokes GET /v1/patients/{account_id}/studies operation.
//
// Fetches studies for a patient account with optional filtering by fetch mode
// and optional uuids for study point-queries.
// Requires authentication and read permissions.
//
// GET /v1/patients/{account_id}/studies
func (c *Client) V1PatientsAccountIDStudiesGet(ctx context.Context, params V1PatientsAccountIDStudiesGetParams) (V1PatientsAccountIDStudiesGetRes, error) {
	res, err := c.sendV1PatientsAccountIDStudiesGet(ctx, params)
	return res, err
}

func (c *Client) sendV1PatientsAccountIDStudiesGet(ctx context.Context, params V1PatientsAccountIDStudiesGetParams) (res V1PatientsAccountIDStudiesGetRes, err error) {
	otelAttrs := []attribute.KeyValue{
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/patients/{account_id}/studies"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1PatientsAccountIDStudiesGet",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "include_reports" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "include_reports",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.IncludeReports.Get(); ok {
				return e.EncodeValue(conv.BoolToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "include_instances" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "include_instances",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.IncludeInstances.Get(); ok {
				return e.EncodeValue(conv.BoolToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "activated" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "activated",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if val, ok := params.Activated.Get(); ok {
				return e.EncodeValue(conv.BoolToString(val))
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	{
		// Encode "uuid" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "uuid",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			if params.UUID != nil {
				return e.EncodeArray(func(e uri.Encoder) error {
					for i, item := range params.UUID {
						if err := func() error {
							return e.EncodeValue(conv.StringToString(item))
						}(); err != nil {
							return errors.Wrapf(err, "[%d]", i)
						}
					}
					return nil
				})
			}
			return nil
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "V1PatientsAccountIDStudiesGet", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1PatientsAccountIDStudiesGetResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1PatientsAccountIDStudiesMatchGet invokes GET /v1/patients/{account_id}/studies/match operation.
//
// Checks if studies belong to a patient account. Returns true if all studies provided belong to the
// patient.
// Requires authentication and read permissions.
//
// GET /v1/patients/{account_id}/studies/match
func (c *Client) V1PatientsAccountIDStudiesMatchGet(ctx context.Context, params V1PatientsAccountIDStudiesMatchGetParams) (V1PatientsAccountIDStudiesMatchGetRes, error) {
	res, err := c.sendV1PatientsAccountIDStudiesMatchGet(ctx, params)
	return res, err
}

func (c *Client) sendV1PatientsAccountIDStudiesMatchGet(ctx context.Context, params V1PatientsAccountIDStudiesMatchGetParams) (res V1PatientsAccountIDStudiesMatchGetRes, err error) {
	otelAttrs := []attribute.KeyValue{
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/patients/{account_id}/studies/match"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1PatientsAccountIDStudiesMatchGet",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/patients/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/match"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "uuid" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "uuid",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeArray(func(e uri.Encoder) error {
				for i, item := range params.UUID {
					if err := func() error {
						return e.EncodeValue(conv.StringToString(item))
					}(); err != nil {
						return errors.Wrapf(err, "[%d]", i)
					}
				}
				return nil
			})
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "V1PatientsAccountIDStudiesMatchGet", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1PatientsAccountIDStudiesMatchGetResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}

// V1PhysiciansAccountIDStudiesMatchGet invokes GET /v1/physicians/{account_id}/studies/match operation.
//
// Checks if studies are accessible to a physician account. Returns true
// if all studies provided are accessible to the physician.
// Requires authentication and read permissions.
//
// GET /v1/physicians/{account_id}/studies/match
func (c *Client) V1PhysiciansAccountIDStudiesMatchGet(ctx context.Context, params V1PhysiciansAccountIDStudiesMatchGetParams) (V1PhysiciansAccountIDStudiesMatchGetRes, error) {
	res, err := c.sendV1PhysiciansAccountIDStudiesMatchGet(ctx, params)
	return res, err
}

func (c *Client) sendV1PhysiciansAccountIDStudiesMatchGet(ctx context.Context, params V1PhysiciansAccountIDStudiesMatchGetParams) (res V1PhysiciansAccountIDStudiesMatchGetRes, err error) {
	otelAttrs := []attribute.KeyValue{
		semconv.HTTPRequestMethodKey.String("GET"),
		semconv.HTTPRouteKey.String("/v1/physicians/{account_id}/studies/match"),
	}

	// Run stopwatch.
	startTime := time.Now()
	defer func() {
		// Use floating point division here for higher precision (instead of Millisecond method).
		elapsedDuration := time.Since(startTime)
		c.duration.Record(ctx, float64(float64(elapsedDuration)/float64(time.Millisecond)), metric.WithAttributes(otelAttrs...))
	}()

	// Increment request counter.
	c.requests.Add(ctx, 1, metric.WithAttributes(otelAttrs...))

	// Start a span for this request.
	ctx, span := c.cfg.Tracer.Start(ctx, "V1PhysiciansAccountIDStudiesMatchGet",
		trace.WithAttributes(otelAttrs...),
		clientSpanKind,
	)
	// Track stage for error reporting.
	var stage string
	defer func() {
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, stage)
			c.errors.Add(ctx, 1, metric.WithAttributes(otelAttrs...))
		}
		span.End()
	}()

	stage = "BuildURL"
	u := uri.Clone(c.requestURL(ctx))
	var pathParts [3]string
	pathParts[0] = "/v1/physicians/"
	{
		// Encode "account_id" parameter.
		e := uri.NewPathEncoder(uri.PathEncoderConfig{
			Param:   "account_id",
			Style:   uri.PathStyleSimple,
			Explode: false,
		})
		if err := func() error {
			return e.EncodeValue(conv.StringToString(params.AccountID))
		}(); err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		encoded, err := e.Result()
		if err != nil {
			return res, errors.Wrap(err, "encode path")
		}
		pathParts[1] = encoded
	}
	pathParts[2] = "/studies/match"
	uri.AddPathParts(u, pathParts[:]...)

	stage = "EncodeQueryParams"
	q := uri.NewQueryEncoder()
	{
		// Encode "uuid" parameter.
		cfg := uri.QueryParameterEncodingConfig{
			Name:    "uuid",
			Style:   uri.QueryStyleForm,
			Explode: true,
		}

		if err := q.EncodeParam(cfg, func(e uri.Encoder) error {
			return e.EncodeArray(func(e uri.Encoder) error {
				for i, item := range params.UUID {
					if err := func() error {
						return e.EncodeValue(conv.StringToString(item))
					}(); err != nil {
						return errors.Wrapf(err, "[%d]", i)
					}
				}
				return nil
			})
		}); err != nil {
			return res, errors.Wrap(err, "encode query")
		}
	}
	u.RawQuery = q.Values().Encode()

	stage = "EncodeRequest"
	r, err := ht.NewRequest(ctx, "GET", u)
	if err != nil {
		return res, errors.Wrap(err, "create request")
	}

	{
		type bitset = [1]uint8
		var satisfied bitset
		{
			stage = "Security:BasicAuth"
			switch err := c.securityBasicAuth(ctx, "V1PhysiciansAccountIDStudiesMatchGet", r); {
			case err == nil: // if NO error
				satisfied[0] |= 1 << 0
			case errors.Is(err, ogenerrors.ErrSkipClientSecurity):
				// Skip this security.
			default:
				return res, errors.Wrap(err, "security \"BasicAuth\"")
			}
		}

		if ok := func() bool {
		nextRequirement:
			for _, requirement := range []bitset{
				{0b00000001},
			} {
				for i, mask := range requirement {
					if satisfied[i]&mask != mask {
						continue nextRequirement
					}
				}
				return true
			}
			return false
		}(); !ok {
			return res, ogenerrors.ErrSecurityRequirementIsNotSatisfied
		}
	}

	stage = "SendRequest"
	resp, err := c.cfg.Client.Do(r)
	if err != nil {
		return res, errors.Wrap(err, "do request")
	}
	defer resp.Body.Close()

	stage = "DecodeResponse"
	result, err := decodeV1PhysiciansAccountIDStudiesMatchGetResponse(resp)
	if err != nil {
		return res, errors.Wrap(err, "decode response")
	}

	return result, nil
}
