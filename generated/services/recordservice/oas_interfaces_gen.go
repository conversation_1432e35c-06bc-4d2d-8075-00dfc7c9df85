// Code generated by ogen, DO NOT EDIT.
package recordservice

type DeletePatientStudiesRes interface {
	deletePatientStudiesRes()
}

type GetAllPatientStudiesRes interface {
	getAllPatientStudiesRes()
}

type GetImagingRes interface {
	getImagingRes()
}

type GetRecentStudyUploadMetadataRes interface {
	getRecentStudyUploadMetadataRes()
}

type GetUploadOverviewRes interface {
	getUploadOverviewRes()
}

type GetV0ImagingTransfersRes interface {
	getV0ImagingTransfersRes()
}

type GetV1MeddreamGenerateRes interface {
	getV1MeddreamGenerateRes()
}

type GetV1PatientsUploadStatusRes interface {
	getV1PatientsUploadStatusRes()
}

type GetV1PhysicianStudiesRes interface {
	getV1PhysicianStudiesRes()
}

type GetV1StudiesPermissionsRes interface {
	getV1StudiesPermissionsRes()
}

type GetV1StudiesUploadStatusRes interface {
	getV1StudiesUploadStatusRes()
}

type GetV1SupportPatientsUploadStatusRes interface {
	getV1SupportPatientsUploadStatusRes()
}

type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeRes interface {
	patchSupportV1PatientsAccountIDStudiesUUIDRevokeRes()
}

type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes interface {
	patchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes()
}

type PatchV0ImagingTransfersIDRevokeRes interface {
	patchV0ImagingTransfersIDRevokeRes()
}

type PatchV0ImagingTransfersIDUnrevokeRes interface {
	patchV0ImagingTransfersIDUnrevokeRes()
}

type PatchV0ImagingUUIDRevokeRes interface {
	patchV0ImagingUUIDRevokeRes()
}

type PatchV0ImagingUUIDUnrevokeRes interface {
	patchV0ImagingUUIDUnrevokeRes()
}

type PostV1BusinessRulesetsEvaluateRes interface {
	postV1BusinessRulesetsEvaluateRes()
}

type PostV1MeddreamValidateRes interface {
	postV1MeddreamValidateRes()
}

type PutV0SharesShareIdExtendRes interface {
	putV0SharesShareIdExtendRes()
}

type V1AccountsAccountIDStudiesAttributePostRes interface {
	v1AccountsAccountIDStudiesAttributePostRes()
}

type V1DicomwebQidoSearchStudyRes interface {
	v1DicomwebQidoSearchStudyRes()
}

type V1DicomwebRetrieveInstanceRes interface {
	v1DicomwebRetrieveInstanceRes()
}

type V1DicomwebRetrieveMetadataRes interface {
	v1DicomwebRetrieveMetadataRes()
}

type V1PatientsAccountIDStudiesActivatePostRes interface {
	v1PatientsAccountIDStudiesActivatePostRes()
}

type V1PatientsAccountIDStudiesGetRes interface {
	v1PatientsAccountIDStudiesGetRes()
}

type V1PatientsAccountIDStudiesMatchGetRes interface {
	v1PatientsAccountIDStudiesMatchGetRes()
}

type V1PhysiciansAccountIDStudiesMatchGetRes interface {
	v1PhysiciansAccountIDStudiesMatchGetRes()
}
