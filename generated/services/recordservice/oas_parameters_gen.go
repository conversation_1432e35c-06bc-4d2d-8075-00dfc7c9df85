// Code generated by ogen, DO NOT EDIT.

package recordservice

// DeletePatientStudiesParams is parameters of DeletePatientStudies operation.
type DeletePatientStudiesParams struct {
	// Account ID of the patient.
	AccountID string
	// Provider ID to filter studies to delete.
	ProviderID OptInt64
	// Optional patient ID for additional filtering.
	PatientID OptString
}

// GetAllPatientStudiesParams is parameters of getAllPatientStudies operation.
type GetAllPatientStudiesParams struct {
	AccountID string
}

// GetImagingParams is parameters of get-imaging operation.
type GetImagingParams struct {
	// Id for the account to get exams.
	AccountID OptInt
	// Id for the request to get exams.
	RequestID OptInt
	// Limit of results to return.
	Limit OptInt32
	// Pagination offset.
	Offset OptInt32
	// Exam uuid to offset results by, should be the last exam uuid of the previous results.
	//
	// Deprecated: schema marks this parameter as deprecated.
	OffsetIds OptString
	// Id for the transfer to get exams.
	TransferID OptString
}

// GetRecentStudyUploadMetadataParams is parameters of getRecentStudyUploadMetadata operation.
type GetRecentStudyUploadMetadataParams struct {
	ProviderID int64
}

// GetV0ImagingTransfersParams is parameters of get-v0-imaging-transfers operation.
type GetV0ImagingTransfersParams struct {
	// Account id.
	AccountID int
	// Number of results to return (default 10).
	Limit OptInt
	// Last transfer fetched, all transfers returned will have been uploaded before this one.
	Offset OptString
}

// GetV1MeddreamGenerateParams is parameters of get-v1-meddream-generate operation.
type GetV1MeddreamGenerateParams struct {
	ExamUUID string
}

// GetV1PatientsUploadStatusParams is parameters of get-v1-patients-upload-status operation.
type GetV1PatientsUploadStatusParams struct {
	// Account ID of patient.
	AccountID string
}

// GetV1PhysicianStudiesParams is parameters of get-v1-physician-studies operation.
type GetV1PhysicianStudiesParams struct {
	// The KSUID of the physician account.
	AccountID string
	// Specify studies by uuid to be checked.
	UUID []string
	// Specify if report metadata associated with the study should be included.
	IncludeReports OptBool
	// Specify if instance metadata associated with the study should be included.
	IncludeInstances OptBool
	// Name of patient whose studies should be retrieved in DICOM format. Studies for patients with other
	// names should not be included.
	PatientName OptString
	// Date of birth of patient whose studies should be retrieved. Studies for patients with other dates
	// of birth should not be included.
	PatientBirthdate OptString
	// ID of provider whose studies should be retrieved. Studies uploaded by other providers should not
	// be included.
	ProviderID OptInt64
}

// GetV1StudiesPermissionsParams is parameters of get-v1-studies-permissions operation.
type GetV1StudiesPermissionsParams struct {
	// DICOM UID of a study OR UUID of a study, if no provider_id is provided.
	ID string
	// Legacy provider id (int64) that identifies provider that uploaded the study.
	ProviderID OptInt
}

// GetV1StudiesUploadStatusParams is parameters of get-v1-studies-upload-status operation.
type GetV1StudiesUploadStatusParams struct {
	// The KSUID of the physician account.
	AccountID string
}

// GetV1SupportPatientsUploadStatusParams is parameters of get-v1-support-patients-upload-status operation.
type GetV1SupportPatientsUploadStatusParams struct {
	// Account ID of patient.
	AccountID string
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams is parameters of patch-support-v1-patients-account-id-studies-uuid-revoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeParams struct {
	AccountID string
	UUID      string
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams is parameters of patch-support-v1-patients-account-id-studies-uuid-unrevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeParams struct {
	AccountID string
	UUID      string
}

// PatchV0ImagingTransfersIDRevokeParams is parameters of patch-v0-imaging-transfers-id-revoke operation.
type PatchV0ImagingTransfersIDRevokeParams struct {
	ID string
}

// PatchV0ImagingTransfersIDUnrevokeParams is parameters of patch-v0-imaging-transfers-id-unrevoke operation.
type PatchV0ImagingTransfersIDUnrevokeParams struct {
	ID string
}

// PatchV0ImagingUUIDRevokeParams is parameters of patch-v0-imaging-uuid-revoke operation.
type PatchV0ImagingUUIDRevokeParams struct {
	UUID string
}

// PatchV0ImagingUUIDUnrevokeParams is parameters of patch-v0-imaging-uuid-unrevoke operation.
type PatchV0ImagingUUIDUnrevokeParams struct {
	UUID string
}

// PostV1MeddreamValidateParams is parameters of post-v1-meddream-validate operation.
type PostV1MeddreamValidateParams struct {
	Token string
}

// PutV0SharesShareIdExtendParams is parameters of put-v0-shares-shareId-extend operation.
type PutV0SharesShareIdExtendParams struct {
	ShareId string
}

// V1AccountsAccountIDStudiesAttributePostParams is parameters of POST /v1/accounts/{account_id}/studies/attribute operation.
type V1AccountsAccountIDStudiesAttributePostParams struct {
	// Account ID of patient.
	AccountID string
}

// V1DicomwebQidoSearchStudyParams is parameters of v1-dicomweb-qido-search-study operation.
type V1DicomwebQidoSearchStudyParams struct {
	// Study instance UID.
	R0020000D string
	// Search for study with study instanceUID and return metadata.
	Custom1 string
}

// V1DicomwebRetrieveInstanceParams is parameters of v1-dicomweb-retrieve-instance operation.
type V1DicomwebRetrieveInstanceParams struct {
	InstanceUID string
	// Return dicom file with {instance_uid}.
	Custom1 string
}

// V1DicomwebRetrieveMetadataParams is parameters of v1-dicomweb-retrieve-metadata operation.
type V1DicomwebRetrieveMetadataParams struct {
	StudyUID string
	// Return study metadata with {study_uid}.
	Custom1 string
}

// V1PatientsAccountIDStudiesActivatePostParams is parameters of POST /v1/patients/{account_id}/studies/activate operation.
type V1PatientsAccountIDStudiesActivatePostParams struct {
	// Account ID of patient.
	AccountID string
}

// V1PatientsAccountIDStudiesGetParams is parameters of GET /v1/patients/{account_id}/studies operation.
type V1PatientsAccountIDStudiesGetParams struct {
	// Account ID associated with the studies.
	AccountID string
	// Specify if report metadata associated with the study should be included.
	IncludeReports OptBool
	// Specify if instance metadata associated with the study should be included.
	IncludeInstances OptBool
	// Specify if studies should be activated or not.
	Activated OptBool
	// Specify studies by uuid to be fetched. If empty, all studies associated with the patient account
	// are returned.
	UUID []string
}

// V1PatientsAccountIDStudiesMatchGetParams is parameters of GET /v1/patients/{account_id}/studies/match operation.
type V1PatientsAccountIDStudiesMatchGetParams struct {
	// Account ID of patient.
	AccountID string
	// Specify studies by uuid to be checked.
	UUID []string
}

// V1PhysiciansAccountIDStudiesMatchGetParams is parameters of GET /v1/physicians/{account_id}/studies/match operation.
type V1PhysiciansAccountIDStudiesMatchGetParams struct {
	// Account ID of physician.
	AccountID string
	// Specify studies by uuid to be checked.
	UUID []string
}
