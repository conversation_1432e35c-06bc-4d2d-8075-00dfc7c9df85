// Code generated by ogen, DO NOT EDIT.

package recordservice

import (
	"context"
	"net/http"

	"github.com/go-faster/errors"
)

// SecuritySource is provider of security values (tokens, passwords, etc.).
type SecuritySource interface {
	// ApiKey1 provides apiKey1 security value.
	ApiKey1(ctx context.Context, operationName string) (ApiKey1, error)
	// BasicAuth provides basicAuth security value.
	BasicAuth(ctx context.Context, operationName string) (BasicAuth, error)
	// JwtBearer provides jwtBearer security value.
	JwtBearer(ctx context.Context, operationName string) (JwtBearer, error)
}

func (s *Client) securityApiKey1(ctx context.Context, operationName string, req *http.Request) error {
	t, err := s.sec.ApiKey1(ctx, operationName)
	if err != nil {
		return errors.Wrap(err, "security source \"ApiKey1\"")
	}
	req.Header.Set("API Key", t.<PERSON>)
	return nil
}
func (s *Client) securityBasicAuth(ctx context.Context, operationName string, req *http.Request) error {
	t, err := s.sec.BasicAuth(ctx, operationName)
	if err != nil {
		return errors.Wrap(err, "security source \"BasicAuth\"")
	}
	req.SetBasicAuth(t.Username, t.Password)
	return nil
}
func (s *Client) securityJwtBearer(ctx context.Context, operationName string, req *http.Request) error {
	t, err := s.sec.JwtBearer(ctx, operationName)
	if err != nil {
		return errors.Wrap(err, "security source \"JwtBearer\"")
	}
	req.Header.Set("Authorization", "Bearer "+t.Token)
	return nil
}
