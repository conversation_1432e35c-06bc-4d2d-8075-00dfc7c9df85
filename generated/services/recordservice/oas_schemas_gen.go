// Code generated by ogen, DO NOT EDIT.

package recordservice

import (
	"io"
	"time"

	"github.com/go-faster/errors"
	"github.com/go-faster/jx"
)

// Ref: #/components/schemas/activate_study
type ActivateStudy struct {
	DateOfBirth               OptString                                 `json:"dateOfBirth"`
	OrderId                   OptString                                 `json:"orderId"`
	StudyAvailabilityStatuses OptActivateStudyStudyAvailabilityStatuses `json:"studyAvailabilityStatuses"`
}

// GetDateOfBirth returns the value of DateOfBirth.
func (s *ActivateStudy) GetDateOfBirth() OptString {
	return s.DateOfBirth
}

// GetOrderId returns the value of OrderId.
func (s *ActivateStudy) GetOrderId() OptString {
	return s.OrderId
}

// GetStudyAvailabilityStatuses returns the value of StudyAvailabilityStatuses.
func (s *ActivateStudy) GetStudyAvailabilityStatuses() OptActivateStudyStudyAvailabilityStatuses {
	return s.StudyAvailabilityStatuses
}

// SetDateOfBirth sets the value of DateOfBirth.
func (s *ActivateStudy) SetDateOfBirth(val OptString) {
	s.DateOfBirth = val
}

// SetOrderId sets the value of OrderId.
func (s *ActivateStudy) SetOrderId(val OptString) {
	s.OrderId = val
}

// SetStudyAvailabilityStatuses sets the value of StudyAvailabilityStatuses.
func (s *ActivateStudy) SetStudyAvailabilityStatuses(val OptActivateStudyStudyAvailabilityStatuses) {
	s.StudyAvailabilityStatuses = val
}

type ActivateStudyStudyAvailabilityStatuses map[string]StudyAvailabilityStatus

func (s *ActivateStudyStudyAvailabilityStatuses) init() ActivateStudyStudyAvailabilityStatuses {
	m := *s
	if m == nil {
		m = map[string]StudyAvailabilityStatus{}
		*s = m
	}
	return m
}

type AnyValue jx.Raw

type ApiKey1 struct {
	APIKey string
}

// GetAPIKey returns the value of APIKey.
func (s *ApiKey1) GetAPIKey() string {
	return s.APIKey
}

// SetAPIKey sets the value of APIKey.
func (s *ApiKey1) SetAPIKey(val string) {
	s.APIKey = val
}

type BasicAuth struct {
	Username string
	Password string
}

// GetUsername returns the value of Username.
func (s *BasicAuth) GetUsername() string {
	return s.Username
}

// GetPassword returns the value of Password.
func (s *BasicAuth) GetPassword() string {
	return s.Password
}

// SetUsername sets the value of Username.
func (s *BasicAuth) SetUsername(val string) {
	s.Username = val
}

// SetPassword sets the value of Password.
func (s *BasicAuth) SetPassword(val string) {
	s.Password = val
}

// Ref: #/components/schemas/Block
type Block struct {
	Study    OptRule `json:"study"`
	Series   OptRule `json:"series"`
	Report   OptRule `json:"report"`
	Instance OptRule `json:"instance"`
}

// GetStudy returns the value of Study.
func (s *Block) GetStudy() OptRule {
	return s.Study
}

// GetSeries returns the value of Series.
func (s *Block) GetSeries() OptRule {
	return s.Series
}

// GetReport returns the value of Report.
func (s *Block) GetReport() OptRule {
	return s.Report
}

// GetInstance returns the value of Instance.
func (s *Block) GetInstance() OptRule {
	return s.Instance
}

// SetStudy sets the value of Study.
func (s *Block) SetStudy(val OptRule) {
	s.Study = val
}

// SetSeries sets the value of Series.
func (s *Block) SetSeries(val OptRule) {
	s.Series = val
}

// SetReport sets the value of Report.
func (s *Block) SetReport(val OptRule) {
	s.Report = val
}

// SetInstance sets the value of Instance.
func (s *Block) SetInstance(val OptRule) {
	s.Instance = val
}

// Ref: #/components/schemas/BusinessRuleResult
type BusinessRuleResult struct {
	Rule      OptString                   `json:"rule"`
	Result    OptBusinessRuleResultResult `json:"result"`
	DicomTags AnyValue                    `json:"dicom_tags"`
}

// GetRule returns the value of Rule.
func (s *BusinessRuleResult) GetRule() OptString {
	return s.Rule
}

// GetResult returns the value of Result.
func (s *BusinessRuleResult) GetResult() OptBusinessRuleResultResult {
	return s.Result
}

// GetDicomTags returns the value of DicomTags.
func (s *BusinessRuleResult) GetDicomTags() AnyValue {
	return s.DicomTags
}

// SetRule sets the value of Rule.
func (s *BusinessRuleResult) SetRule(val OptString) {
	s.Rule = val
}

// SetResult sets the value of Result.
func (s *BusinessRuleResult) SetResult(val OptBusinessRuleResultResult) {
	s.Result = val
}

// SetDicomTags sets the value of DicomTags.
func (s *BusinessRuleResult) SetDicomTags(val AnyValue) {
	s.DicomTags = val
}

type BusinessRuleResultResult string

const (
	BusinessRuleResultResultBlocked BusinessRuleResultResult = "blocked"
	BusinessRuleResultResultAllowed BusinessRuleResultResult = "allowed"
)

// AllValues returns all BusinessRuleResultResult values.
func (BusinessRuleResultResult) AllValues() []BusinessRuleResultResult {
	return []BusinessRuleResultResult{
		BusinessRuleResultResultBlocked,
		BusinessRuleResultResultAllowed,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s BusinessRuleResultResult) MarshalText() ([]byte, error) {
	switch s {
	case BusinessRuleResultResultBlocked:
		return []byte(s), nil
	case BusinessRuleResultResultAllowed:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *BusinessRuleResultResult) UnmarshalText(data []byte) error {
	switch BusinessRuleResultResult(data) {
	case BusinessRuleResultResultBlocked:
		*s = BusinessRuleResultResultBlocked
		return nil
	case BusinessRuleResultResultAllowed:
		*s = BusinessRuleResultResultAllowed
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Merged schema.
// Ref: #/components/schemas/DebugPatientStudy
type DebugPatientStudy struct {
	UUID                          OptString                     `json:"uuid"`
	PatientID                     OptString                     `json:"patient_id"`
	OrganizationID                OptInt64                      `json:"organization_id"`
	ActivatedTimestamp            OptDateTime                   `json:"activated_timestamp"`
	AvailabilityStatus            OptString                     `json:"availability_status"`
	OrderID                       OptString                     `json:"order_id"`
	TransferID                    OptString                     `json:"transfer_id"`
	InstanceUploadProgressPercent OptInt                        `json:"instance_upload_progress_percent"`
	DicomPatientTags              OptDicomPatientTags           `json:"dicom_patient_tags"`
	DicomStudyTags                OptDicomStudyTags             `json:"dicom_study_tags"`
	Series                        []DicomSeries                 `json:"series"`
	Reports                       OptNilStudyReportArray        `json:"reports"`
	HasReport                     OptBool                       `json:"has_report"`
	BusinessRuleResults           OptNilBusinessRuleResultArray `json:"business_rule_results"`
	IsRecordStreaming             OptBool                       `json:"is_record_streaming"`
}

// GetUUID returns the value of UUID.
func (s *DebugPatientStudy) GetUUID() OptString {
	return s.UUID
}

// GetPatientID returns the value of PatientID.
func (s *DebugPatientStudy) GetPatientID() OptString {
	return s.PatientID
}

// GetOrganizationID returns the value of OrganizationID.
func (s *DebugPatientStudy) GetOrganizationID() OptInt64 {
	return s.OrganizationID
}

// GetActivatedTimestamp returns the value of ActivatedTimestamp.
func (s *DebugPatientStudy) GetActivatedTimestamp() OptDateTime {
	return s.ActivatedTimestamp
}

// GetAvailabilityStatus returns the value of AvailabilityStatus.
func (s *DebugPatientStudy) GetAvailabilityStatus() OptString {
	return s.AvailabilityStatus
}

// GetOrderID returns the value of OrderID.
func (s *DebugPatientStudy) GetOrderID() OptString {
	return s.OrderID
}

// GetTransferID returns the value of TransferID.
func (s *DebugPatientStudy) GetTransferID() OptString {
	return s.TransferID
}

// GetInstanceUploadProgressPercent returns the value of InstanceUploadProgressPercent.
func (s *DebugPatientStudy) GetInstanceUploadProgressPercent() OptInt {
	return s.InstanceUploadProgressPercent
}

// GetDicomPatientTags returns the value of DicomPatientTags.
func (s *DebugPatientStudy) GetDicomPatientTags() OptDicomPatientTags {
	return s.DicomPatientTags
}

// GetDicomStudyTags returns the value of DicomStudyTags.
func (s *DebugPatientStudy) GetDicomStudyTags() OptDicomStudyTags {
	return s.DicomStudyTags
}

// GetSeries returns the value of Series.
func (s *DebugPatientStudy) GetSeries() []DicomSeries {
	return s.Series
}

// GetReports returns the value of Reports.
func (s *DebugPatientStudy) GetReports() OptNilStudyReportArray {
	return s.Reports
}

// GetHasReport returns the value of HasReport.
func (s *DebugPatientStudy) GetHasReport() OptBool {
	return s.HasReport
}

// GetBusinessRuleResults returns the value of BusinessRuleResults.
func (s *DebugPatientStudy) GetBusinessRuleResults() OptNilBusinessRuleResultArray {
	return s.BusinessRuleResults
}

// GetIsRecordStreaming returns the value of IsRecordStreaming.
func (s *DebugPatientStudy) GetIsRecordStreaming() OptBool {
	return s.IsRecordStreaming
}

// SetUUID sets the value of UUID.
func (s *DebugPatientStudy) SetUUID(val OptString) {
	s.UUID = val
}

// SetPatientID sets the value of PatientID.
func (s *DebugPatientStudy) SetPatientID(val OptString) {
	s.PatientID = val
}

// SetOrganizationID sets the value of OrganizationID.
func (s *DebugPatientStudy) SetOrganizationID(val OptInt64) {
	s.OrganizationID = val
}

// SetActivatedTimestamp sets the value of ActivatedTimestamp.
func (s *DebugPatientStudy) SetActivatedTimestamp(val OptDateTime) {
	s.ActivatedTimestamp = val
}

// SetAvailabilityStatus sets the value of AvailabilityStatus.
func (s *DebugPatientStudy) SetAvailabilityStatus(val OptString) {
	s.AvailabilityStatus = val
}

// SetOrderID sets the value of OrderID.
func (s *DebugPatientStudy) SetOrderID(val OptString) {
	s.OrderID = val
}

// SetTransferID sets the value of TransferID.
func (s *DebugPatientStudy) SetTransferID(val OptString) {
	s.TransferID = val
}

// SetInstanceUploadProgressPercent sets the value of InstanceUploadProgressPercent.
func (s *DebugPatientStudy) SetInstanceUploadProgressPercent(val OptInt) {
	s.InstanceUploadProgressPercent = val
}

// SetDicomPatientTags sets the value of DicomPatientTags.
func (s *DebugPatientStudy) SetDicomPatientTags(val OptDicomPatientTags) {
	s.DicomPatientTags = val
}

// SetDicomStudyTags sets the value of DicomStudyTags.
func (s *DebugPatientStudy) SetDicomStudyTags(val OptDicomStudyTags) {
	s.DicomStudyTags = val
}

// SetSeries sets the value of Series.
func (s *DebugPatientStudy) SetSeries(val []DicomSeries) {
	s.Series = val
}

// SetReports sets the value of Reports.
func (s *DebugPatientStudy) SetReports(val OptNilStudyReportArray) {
	s.Reports = val
}

// SetHasReport sets the value of HasReport.
func (s *DebugPatientStudy) SetHasReport(val OptBool) {
	s.HasReport = val
}

// SetBusinessRuleResults sets the value of BusinessRuleResults.
func (s *DebugPatientStudy) SetBusinessRuleResults(val OptNilBusinessRuleResultArray) {
	s.BusinessRuleResults = val
}

// SetIsRecordStreaming sets the value of IsRecordStreaming.
func (s *DebugPatientStudy) SetIsRecordStreaming(val OptBool) {
	s.IsRecordStreaming = val
}

// DeletePatientStudiesCode499 is response for DeletePatientStudies operation.
type DeletePatientStudiesCode499 struct{}

func (*DeletePatientStudiesCode499) deletePatientStudiesRes() {}

// DeletePatientStudiesInternalServerError is response for DeletePatientStudies operation.
type DeletePatientStudiesInternalServerError struct{}

func (*DeletePatientStudiesInternalServerError) deletePatientStudiesRes() {}

// DeletePatientStudiesOK is response for DeletePatientStudies operation.
type DeletePatientStudiesOK struct{}

func (*DeletePatientStudiesOK) deletePatientStudiesRes() {}

// DeletePatientStudiesUnauthorized is response for DeletePatientStudies operation.
type DeletePatientStudiesUnauthorized struct{}

func (*DeletePatientStudiesUnauthorized) deletePatientStudiesRes() {}

// Ref: #/components/schemas/dicom_instance
type DicomInstance struct {
	UUID          OptString            `json:"uuid"`
	DicomTags     OptDicomInstanceTags `json:"dicom_tags"`
	FileSizeBytes OptInt64             `json:"file_size_bytes"`
}

// GetUUID returns the value of UUID.
func (s *DicomInstance) GetUUID() OptString {
	return s.UUID
}

// GetDicomTags returns the value of DicomTags.
func (s *DicomInstance) GetDicomTags() OptDicomInstanceTags {
	return s.DicomTags
}

// GetFileSizeBytes returns the value of FileSizeBytes.
func (s *DicomInstance) GetFileSizeBytes() OptInt64 {
	return s.FileSizeBytes
}

// SetUUID sets the value of UUID.
func (s *DicomInstance) SetUUID(val OptString) {
	s.UUID = val
}

// SetDicomTags sets the value of DicomTags.
func (s *DicomInstance) SetDicomTags(val OptDicomInstanceTags) {
	s.DicomTags = val
}

// SetFileSizeBytes sets the value of FileSizeBytes.
func (s *DicomInstance) SetFileSizeBytes(val OptInt64) {
	s.FileSizeBytes = val
}

// Ref: #/components/schemas/dicom_instance_tags
type DicomInstanceTags struct {
	SopInstanceUID      OptString    `json:"sop_instance_uid"`
	Protocol            OptNilString `json:"protocol"`
	InstanceNumber      OptNilString `json:"instance_number"`
	ImageType           OptNilString `json:"image_type"`
	SopClassUID         OptNilString `json:"sop_class_uid"`
	ContentDate         OptNilString `json:"content_date"`
	ContentTime         OptNilString `json:"content_time"`
	AcquisitionDate     OptNilString `json:"acquisition_date"`
	AcquisitionTime     OptNilString `json:"acquisition_time"`
	ViewPosition        OptNilString `json:"view_position"`
	ImagePosition       OptNilString `json:"image_position"`
	ImageOrientation    OptNilString `json:"image_orientation"`
	FrameOfReferenceUID OptNilString `json:"frame_of_reference_uid"`
	ImageLaterality     OptNilString `json:"image_laterality"`
	NumberOfFrames      OptNilString `json:"number_of_frames"`
	Rows                OptNilString `json:"rows"`
	Columns             OptNilString `json:"columns"`
	PixelSpacing        OptNilString `json:"pixel_spacing"`
	TransferSyntaxUID   OptNilString `json:"transfer_syntax_uid"`
}

// GetSopInstanceUID returns the value of SopInstanceUID.
func (s *DicomInstanceTags) GetSopInstanceUID() OptString {
	return s.SopInstanceUID
}

// GetProtocol returns the value of Protocol.
func (s *DicomInstanceTags) GetProtocol() OptNilString {
	return s.Protocol
}

// GetInstanceNumber returns the value of InstanceNumber.
func (s *DicomInstanceTags) GetInstanceNumber() OptNilString {
	return s.InstanceNumber
}

// GetImageType returns the value of ImageType.
func (s *DicomInstanceTags) GetImageType() OptNilString {
	return s.ImageType
}

// GetSopClassUID returns the value of SopClassUID.
func (s *DicomInstanceTags) GetSopClassUID() OptNilString {
	return s.SopClassUID
}

// GetContentDate returns the value of ContentDate.
func (s *DicomInstanceTags) GetContentDate() OptNilString {
	return s.ContentDate
}

// GetContentTime returns the value of ContentTime.
func (s *DicomInstanceTags) GetContentTime() OptNilString {
	return s.ContentTime
}

// GetAcquisitionDate returns the value of AcquisitionDate.
func (s *DicomInstanceTags) GetAcquisitionDate() OptNilString {
	return s.AcquisitionDate
}

// GetAcquisitionTime returns the value of AcquisitionTime.
func (s *DicomInstanceTags) GetAcquisitionTime() OptNilString {
	return s.AcquisitionTime
}

// GetViewPosition returns the value of ViewPosition.
func (s *DicomInstanceTags) GetViewPosition() OptNilString {
	return s.ViewPosition
}

// GetImagePosition returns the value of ImagePosition.
func (s *DicomInstanceTags) GetImagePosition() OptNilString {
	return s.ImagePosition
}

// GetImageOrientation returns the value of ImageOrientation.
func (s *DicomInstanceTags) GetImageOrientation() OptNilString {
	return s.ImageOrientation
}

// GetFrameOfReferenceUID returns the value of FrameOfReferenceUID.
func (s *DicomInstanceTags) GetFrameOfReferenceUID() OptNilString {
	return s.FrameOfReferenceUID
}

// GetImageLaterality returns the value of ImageLaterality.
func (s *DicomInstanceTags) GetImageLaterality() OptNilString {
	return s.ImageLaterality
}

// GetNumberOfFrames returns the value of NumberOfFrames.
func (s *DicomInstanceTags) GetNumberOfFrames() OptNilString {
	return s.NumberOfFrames
}

// GetRows returns the value of Rows.
func (s *DicomInstanceTags) GetRows() OptNilString {
	return s.Rows
}

// GetColumns returns the value of Columns.
func (s *DicomInstanceTags) GetColumns() OptNilString {
	return s.Columns
}

// GetPixelSpacing returns the value of PixelSpacing.
func (s *DicomInstanceTags) GetPixelSpacing() OptNilString {
	return s.PixelSpacing
}

// GetTransferSyntaxUID returns the value of TransferSyntaxUID.
func (s *DicomInstanceTags) GetTransferSyntaxUID() OptNilString {
	return s.TransferSyntaxUID
}

// SetSopInstanceUID sets the value of SopInstanceUID.
func (s *DicomInstanceTags) SetSopInstanceUID(val OptString) {
	s.SopInstanceUID = val
}

// SetProtocol sets the value of Protocol.
func (s *DicomInstanceTags) SetProtocol(val OptNilString) {
	s.Protocol = val
}

// SetInstanceNumber sets the value of InstanceNumber.
func (s *DicomInstanceTags) SetInstanceNumber(val OptNilString) {
	s.InstanceNumber = val
}

// SetImageType sets the value of ImageType.
func (s *DicomInstanceTags) SetImageType(val OptNilString) {
	s.ImageType = val
}

// SetSopClassUID sets the value of SopClassUID.
func (s *DicomInstanceTags) SetSopClassUID(val OptNilString) {
	s.SopClassUID = val
}

// SetContentDate sets the value of ContentDate.
func (s *DicomInstanceTags) SetContentDate(val OptNilString) {
	s.ContentDate = val
}

// SetContentTime sets the value of ContentTime.
func (s *DicomInstanceTags) SetContentTime(val OptNilString) {
	s.ContentTime = val
}

// SetAcquisitionDate sets the value of AcquisitionDate.
func (s *DicomInstanceTags) SetAcquisitionDate(val OptNilString) {
	s.AcquisitionDate = val
}

// SetAcquisitionTime sets the value of AcquisitionTime.
func (s *DicomInstanceTags) SetAcquisitionTime(val OptNilString) {
	s.AcquisitionTime = val
}

// SetViewPosition sets the value of ViewPosition.
func (s *DicomInstanceTags) SetViewPosition(val OptNilString) {
	s.ViewPosition = val
}

// SetImagePosition sets the value of ImagePosition.
func (s *DicomInstanceTags) SetImagePosition(val OptNilString) {
	s.ImagePosition = val
}

// SetImageOrientation sets the value of ImageOrientation.
func (s *DicomInstanceTags) SetImageOrientation(val OptNilString) {
	s.ImageOrientation = val
}

// SetFrameOfReferenceUID sets the value of FrameOfReferenceUID.
func (s *DicomInstanceTags) SetFrameOfReferenceUID(val OptNilString) {
	s.FrameOfReferenceUID = val
}

// SetImageLaterality sets the value of ImageLaterality.
func (s *DicomInstanceTags) SetImageLaterality(val OptNilString) {
	s.ImageLaterality = val
}

// SetNumberOfFrames sets the value of NumberOfFrames.
func (s *DicomInstanceTags) SetNumberOfFrames(val OptNilString) {
	s.NumberOfFrames = val
}

// SetRows sets the value of Rows.
func (s *DicomInstanceTags) SetRows(val OptNilString) {
	s.Rows = val
}

// SetColumns sets the value of Columns.
func (s *DicomInstanceTags) SetColumns(val OptNilString) {
	s.Columns = val
}

// SetPixelSpacing sets the value of PixelSpacing.
func (s *DicomInstanceTags) SetPixelSpacing(val OptNilString) {
	s.PixelSpacing = val
}

// SetTransferSyntaxUID sets the value of TransferSyntaxUID.
func (s *DicomInstanceTags) SetTransferSyntaxUID(val OptNilString) {
	s.TransferSyntaxUID = val
}

// Key is the dicom tag.
// Ref: #/components/schemas/DicomMetadata
type DicomMetadata map[string]DicomMetadataItem

func (s *DicomMetadata) init() DicomMetadata {
	m := *s
	if m == nil {
		m = map[string]DicomMetadataItem{}
		*s = m
	}
	return m
}

type DicomMetadataItem struct {
	Vr    OptString `json:"vr"`
	Value OptString `json:"Value"`
}

// GetVr returns the value of Vr.
func (s *DicomMetadataItem) GetVr() OptString {
	return s.Vr
}

// GetValue returns the value of Value.
func (s *DicomMetadataItem) GetValue() OptString {
	return s.Value
}

// SetVr sets the value of Vr.
func (s *DicomMetadataItem) SetVr(val OptString) {
	s.Vr = val
}

// SetValue sets the value of Value.
func (s *DicomMetadataItem) SetValue(val OptString) {
	s.Value = val
}

// Ref: #/components/schemas/dicom_patient_tags
type DicomPatientTags struct {
	PatientName            OptString    `json:"patient_name"`
	PatientID              OptString    `json:"patient_id"`
	PatientBirthDate       OptString    `json:"patient_birth_date"`
	PatientSex             OptNilString `json:"patient_sex"`
	PatientTelephoneNumber OptNilString `json:"patient_telephone_number"`
}

// GetPatientName returns the value of PatientName.
func (s *DicomPatientTags) GetPatientName() OptString {
	return s.PatientName
}

// GetPatientID returns the value of PatientID.
func (s *DicomPatientTags) GetPatientID() OptString {
	return s.PatientID
}

// GetPatientBirthDate returns the value of PatientBirthDate.
func (s *DicomPatientTags) GetPatientBirthDate() OptString {
	return s.PatientBirthDate
}

// GetPatientSex returns the value of PatientSex.
func (s *DicomPatientTags) GetPatientSex() OptNilString {
	return s.PatientSex
}

// GetPatientTelephoneNumber returns the value of PatientTelephoneNumber.
func (s *DicomPatientTags) GetPatientTelephoneNumber() OptNilString {
	return s.PatientTelephoneNumber
}

// SetPatientName sets the value of PatientName.
func (s *DicomPatientTags) SetPatientName(val OptString) {
	s.PatientName = val
}

// SetPatientID sets the value of PatientID.
func (s *DicomPatientTags) SetPatientID(val OptString) {
	s.PatientID = val
}

// SetPatientBirthDate sets the value of PatientBirthDate.
func (s *DicomPatientTags) SetPatientBirthDate(val OptString) {
	s.PatientBirthDate = val
}

// SetPatientSex sets the value of PatientSex.
func (s *DicomPatientTags) SetPatientSex(val OptNilString) {
	s.PatientSex = val
}

// SetPatientTelephoneNumber sets the value of PatientTelephoneNumber.
func (s *DicomPatientTags) SetPatientTelephoneNumber(val OptNilString) {
	s.PatientTelephoneNumber = val
}

// Ref: #/components/schemas/dicom_series
type DicomSeries struct {
	DicomTags OptDicomSeriesTags       `json:"dicom_tags"`
	Instances OptNilDicomInstanceArray `json:"instances"`
}

// GetDicomTags returns the value of DicomTags.
func (s *DicomSeries) GetDicomTags() OptDicomSeriesTags {
	return s.DicomTags
}

// GetInstances returns the value of Instances.
func (s *DicomSeries) GetInstances() OptNilDicomInstanceArray {
	return s.Instances
}

// SetDicomTags sets the value of DicomTags.
func (s *DicomSeries) SetDicomTags(val OptDicomSeriesTags) {
	s.DicomTags = val
}

// SetInstances sets the value of Instances.
func (s *DicomSeries) SetInstances(val OptNilDicomInstanceArray) {
	s.Instances = val
}

// Ref: #/components/schemas/dicom_series_tags
type DicomSeriesTags struct {
	SeriesInstanceUID OptString    `json:"series_instance_uid"`
	SeriesNumber      OptNilString `json:"series_number"`
	SeriesDescription OptNilString `json:"series_description"`
}

// GetSeriesInstanceUID returns the value of SeriesInstanceUID.
func (s *DicomSeriesTags) GetSeriesInstanceUID() OptString {
	return s.SeriesInstanceUID
}

// GetSeriesNumber returns the value of SeriesNumber.
func (s *DicomSeriesTags) GetSeriesNumber() OptNilString {
	return s.SeriesNumber
}

// GetSeriesDescription returns the value of SeriesDescription.
func (s *DicomSeriesTags) GetSeriesDescription() OptNilString {
	return s.SeriesDescription
}

// SetSeriesInstanceUID sets the value of SeriesInstanceUID.
func (s *DicomSeriesTags) SetSeriesInstanceUID(val OptString) {
	s.SeriesInstanceUID = val
}

// SetSeriesNumber sets the value of SeriesNumber.
func (s *DicomSeriesTags) SetSeriesNumber(val OptNilString) {
	s.SeriesNumber = val
}

// SetSeriesDescription sets the value of SeriesDescription.
func (s *DicomSeriesTags) SetSeriesDescription(val OptNilString) {
	s.SeriesDescription = val
}

// Ref: #/components/schemas/dicom_study_tags
type DicomStudyTags struct {
	StudyInstanceUID   OptString    `json:"study_instance_uid"`
	StudyDate          OptString    `json:"study_date"`
	StudyDescription   OptNilString `json:"study_description"`
	AccessionNumber    OptNilString `json:"accession_number"`
	ReferringPhysician OptNilString `json:"referring_physician"`
	Modality           OptNilString `json:"modality"`
	BodyPart           OptNilString `json:"body_part"`
}

// GetStudyInstanceUID returns the value of StudyInstanceUID.
func (s *DicomStudyTags) GetStudyInstanceUID() OptString {
	return s.StudyInstanceUID
}

// GetStudyDate returns the value of StudyDate.
func (s *DicomStudyTags) GetStudyDate() OptString {
	return s.StudyDate
}

// GetStudyDescription returns the value of StudyDescription.
func (s *DicomStudyTags) GetStudyDescription() OptNilString {
	return s.StudyDescription
}

// GetAccessionNumber returns the value of AccessionNumber.
func (s *DicomStudyTags) GetAccessionNumber() OptNilString {
	return s.AccessionNumber
}

// GetReferringPhysician returns the value of ReferringPhysician.
func (s *DicomStudyTags) GetReferringPhysician() OptNilString {
	return s.ReferringPhysician
}

// GetModality returns the value of Modality.
func (s *DicomStudyTags) GetModality() OptNilString {
	return s.Modality
}

// GetBodyPart returns the value of BodyPart.
func (s *DicomStudyTags) GetBodyPart() OptNilString {
	return s.BodyPart
}

// SetStudyInstanceUID sets the value of StudyInstanceUID.
func (s *DicomStudyTags) SetStudyInstanceUID(val OptString) {
	s.StudyInstanceUID = val
}

// SetStudyDate sets the value of StudyDate.
func (s *DicomStudyTags) SetStudyDate(val OptString) {
	s.StudyDate = val
}

// SetStudyDescription sets the value of StudyDescription.
func (s *DicomStudyTags) SetStudyDescription(val OptNilString) {
	s.StudyDescription = val
}

// SetAccessionNumber sets the value of AccessionNumber.
func (s *DicomStudyTags) SetAccessionNumber(val OptNilString) {
	s.AccessionNumber = val
}

// SetReferringPhysician sets the value of ReferringPhysician.
func (s *DicomStudyTags) SetReferringPhysician(val OptNilString) {
	s.ReferringPhysician = val
}

// SetModality sets the value of Modality.
func (s *DicomStudyTags) SetModality(val OptNilString) {
	s.Modality = val
}

// SetBodyPart sets the value of BodyPart.
func (s *DicomStudyTags) SetBodyPart(val OptNilString) {
	s.BodyPart = val
}

// Ref: #/components/schemas/GenerateMedreamToken
type GenerateMedreamToken struct {
	ExamUUID  OptString `json:"exam_uuid"`
	StudyUID  OptString `json:"study_uid"`
	AccountID OptString `json:"account_id"`
}

// GetExamUUID returns the value of ExamUUID.
func (s *GenerateMedreamToken) GetExamUUID() OptString {
	return s.ExamUUID
}

// GetStudyUID returns the value of StudyUID.
func (s *GenerateMedreamToken) GetStudyUID() OptString {
	return s.StudyUID
}

// GetAccountID returns the value of AccountID.
func (s *GenerateMedreamToken) GetAccountID() OptString {
	return s.AccountID
}

// SetExamUUID sets the value of ExamUUID.
func (s *GenerateMedreamToken) SetExamUUID(val OptString) {
	s.ExamUUID = val
}

// SetStudyUID sets the value of StudyUID.
func (s *GenerateMedreamToken) SetStudyUID(val OptString) {
	s.StudyUID = val
}

// SetAccountID sets the value of AccountID.
func (s *GenerateMedreamToken) SetAccountID(val OptString) {
	s.AccountID = val
}

// GetAllPatientStudiesBadRequest is response for GetAllPatientStudies operation.
type GetAllPatientStudiesBadRequest struct{}

func (*GetAllPatientStudiesBadRequest) getAllPatientStudiesRes() {}

// GetAllPatientStudiesInternalServerError is response for GetAllPatientStudies operation.
type GetAllPatientStudiesInternalServerError struct{}

func (*GetAllPatientStudiesInternalServerError) getAllPatientStudiesRes() {}

type GetAllPatientStudiesOKApplicationJSON []DebugPatientStudy

func (*GetAllPatientStudiesOKApplicationJSON) getAllPatientStudiesRes() {}

// GetAllPatientStudiesUnauthorized is response for GetAllPatientStudies operation.
type GetAllPatientStudiesUnauthorized struct{}

func (*GetAllPatientStudiesUnauthorized) getAllPatientStudiesRes() {}

// GetImagingBadRequest is response for GetImaging operation.
type GetImagingBadRequest struct{}

func (*GetImagingBadRequest) getImagingRes() {}

// GetImagingForbidden is response for GetImaging operation.
type GetImagingForbidden struct{}

func (*GetImagingForbidden) getImagingRes() {}

// GetImagingInternalServerError is response for GetImaging operation.
type GetImagingInternalServerError struct{}

func (*GetImagingInternalServerError) getImagingRes() {}

type GetImagingOKApplicationJSON []ImagingExam

func (*GetImagingOKApplicationJSON) getImagingRes() {}

// GetImagingUnauthorized is response for GetImaging operation.
type GetImagingUnauthorized struct{}

func (*GetImagingUnauthorized) getImagingRes() {}

// GetRecentStudyUploadMetadataBadRequest is response for GetRecentStudyUploadMetadata operation.
type GetRecentStudyUploadMetadataBadRequest struct{}

func (*GetRecentStudyUploadMetadataBadRequest) getRecentStudyUploadMetadataRes() {}

// GetRecentStudyUploadMetadataInternalServerError is response for GetRecentStudyUploadMetadata operation.
type GetRecentStudyUploadMetadataInternalServerError struct{}

func (*GetRecentStudyUploadMetadataInternalServerError) getRecentStudyUploadMetadataRes() {}

type GetRecentStudyUploadMetadataOKApplicationJSON []StudyUploadMetadata

func (*GetRecentStudyUploadMetadataOKApplicationJSON) getRecentStudyUploadMetadataRes() {}

// GetRecentStudyUploadMetadataUnauthorized is response for GetRecentStudyUploadMetadata operation.
type GetRecentStudyUploadMetadataUnauthorized struct{}

func (*GetRecentStudyUploadMetadataUnauthorized) getRecentStudyUploadMetadataRes() {}

type GetUploadOverviewOKApplicationJSON []ProviderUploadOverview

func (*GetUploadOverviewOKApplicationJSON) getUploadOverviewRes() {}

// GetUploadOverviewUnauthorized is response for GetUploadOverview operation.
type GetUploadOverviewUnauthorized struct{}

func (*GetUploadOverviewUnauthorized) getUploadOverviewRes() {}

// GetV0ImagingTransfersBadRequest is response for GetV0ImagingTransfers operation.
type GetV0ImagingTransfersBadRequest struct{}

func (*GetV0ImagingTransfersBadRequest) getV0ImagingTransfersRes() {}

// GetV0ImagingTransfersInternalServerError is response for GetV0ImagingTransfers operation.
type GetV0ImagingTransfersInternalServerError struct{}

func (*GetV0ImagingTransfersInternalServerError) getV0ImagingTransfersRes() {}

type GetV0ImagingTransfersOKApplicationJSON []ImagingTransfer

func (*GetV0ImagingTransfersOKApplicationJSON) getV0ImagingTransfersRes() {}

// GetV0ImagingTransfersUnauthorized is response for GetV0ImagingTransfers operation.
type GetV0ImagingTransfersUnauthorized struct{}

func (*GetV0ImagingTransfersUnauthorized) getV0ImagingTransfersRes() {}

// GetV1MeddreamGenerateInternalServerError is response for GetV1MeddreamGenerate operation.
type GetV1MeddreamGenerateInternalServerError struct{}

func (*GetV1MeddreamGenerateInternalServerError) getV1MeddreamGenerateRes() {}

// GetV1MeddreamGenerateNotFound is response for GetV1MeddreamGenerate operation.
type GetV1MeddreamGenerateNotFound struct{}

func (*GetV1MeddreamGenerateNotFound) getV1MeddreamGenerateRes() {}

type GetV1MeddreamGenerateOKApplicationJSON string

func (*GetV1MeddreamGenerateOKApplicationJSON) getV1MeddreamGenerateRes() {}

// GetV1MeddreamGenerateUnauthorized is response for GetV1MeddreamGenerate operation.
type GetV1MeddreamGenerateUnauthorized struct{}

func (*GetV1MeddreamGenerateUnauthorized) getV1MeddreamGenerateRes() {}

// GetV1PatientsUploadStatusBadRequest is response for GetV1PatientsUploadStatus operation.
type GetV1PatientsUploadStatusBadRequest struct{}

func (*GetV1PatientsUploadStatusBadRequest) getV1PatientsUploadStatusRes() {}

// GetV1PatientsUploadStatusInternalServerError is response for GetV1PatientsUploadStatus operation.
type GetV1PatientsUploadStatusInternalServerError struct{}

func (*GetV1PatientsUploadStatusInternalServerError) getV1PatientsUploadStatusRes() {}

type GetV1PatientsUploadStatusOKApplicationJSON []UploadStatus

func (*GetV1PatientsUploadStatusOKApplicationJSON) getV1PatientsUploadStatusRes() {}

// GetV1PatientsUploadStatusUnauthorized is response for GetV1PatientsUploadStatus operation.
type GetV1PatientsUploadStatusUnauthorized struct{}

func (*GetV1PatientsUploadStatusUnauthorized) getV1PatientsUploadStatusRes() {}

// GetV1PhysicianStudiesBadRequest is response for GetV1PhysicianStudies operation.
type GetV1PhysicianStudiesBadRequest struct{}

func (*GetV1PhysicianStudiesBadRequest) getV1PhysicianStudiesRes() {}

// GetV1PhysicianStudiesInternalServerError is response for GetV1PhysicianStudies operation.
type GetV1PhysicianStudiesInternalServerError struct{}

func (*GetV1PhysicianStudiesInternalServerError) getV1PhysicianStudiesRes() {}

// GetV1PhysicianStudiesUnauthorized is response for GetV1PhysicianStudies operation.
type GetV1PhysicianStudiesUnauthorized struct{}

func (*GetV1PhysicianStudiesUnauthorized) getV1PhysicianStudiesRes() {}

// GetV1StudiesPermissionsBadRequest is response for GetV1StudiesPermissions operation.
type GetV1StudiesPermissionsBadRequest struct{}

func (*GetV1StudiesPermissionsBadRequest) getV1StudiesPermissionsRes() {}

// GetV1StudiesPermissionsInternalServerError is response for GetV1StudiesPermissions operation.
type GetV1StudiesPermissionsInternalServerError struct{}

func (*GetV1StudiesPermissionsInternalServerError) getV1StudiesPermissionsRes() {}

// GetV1StudiesPermissionsNotFound is response for GetV1StudiesPermissions operation.
type GetV1StudiesPermissionsNotFound struct{}

func (*GetV1StudiesPermissionsNotFound) getV1StudiesPermissionsRes() {}

// GetV1StudiesPermissionsUnauthorized is response for GetV1StudiesPermissions operation.
type GetV1StudiesPermissionsUnauthorized struct{}

func (*GetV1StudiesPermissionsUnauthorized) getV1StudiesPermissionsRes() {}

// GetV1StudiesUploadStatusBadRequest is response for GetV1StudiesUploadStatus operation.
type GetV1StudiesUploadStatusBadRequest struct{}

func (*GetV1StudiesUploadStatusBadRequest) getV1StudiesUploadStatusRes() {}

// GetV1StudiesUploadStatusInternalServerError is response for GetV1StudiesUploadStatus operation.
type GetV1StudiesUploadStatusInternalServerError struct{}

func (*GetV1StudiesUploadStatusInternalServerError) getV1StudiesUploadStatusRes() {}

type GetV1StudiesUploadStatusOKApplicationJSON []StudyUploadStatus

func (*GetV1StudiesUploadStatusOKApplicationJSON) getV1StudiesUploadStatusRes() {}

// GetV1StudiesUploadStatusUnauthorized is response for GetV1StudiesUploadStatus operation.
type GetV1StudiesUploadStatusUnauthorized struct{}

func (*GetV1StudiesUploadStatusUnauthorized) getV1StudiesUploadStatusRes() {}

// GetV1SupportPatientsUploadStatusBadRequest is response for GetV1SupportPatientsUploadStatus operation.
type GetV1SupportPatientsUploadStatusBadRequest struct{}

func (*GetV1SupportPatientsUploadStatusBadRequest) getV1SupportPatientsUploadStatusRes() {}

// GetV1SupportPatientsUploadStatusInternalServerError is response for GetV1SupportPatientsUploadStatus operation.
type GetV1SupportPatientsUploadStatusInternalServerError struct{}

func (*GetV1SupportPatientsUploadStatusInternalServerError) getV1SupportPatientsUploadStatusRes() {}

type GetV1SupportPatientsUploadStatusOKApplicationJSON []UploadStatus

func (*GetV1SupportPatientsUploadStatusOKApplicationJSON) getV1SupportPatientsUploadStatusRes() {}

// GetV1SupportPatientsUploadStatusUnauthorized is response for GetV1SupportPatientsUploadStatus operation.
type GetV1SupportPatientsUploadStatusUnauthorized struct{}

func (*GetV1SupportPatientsUploadStatusUnauthorized) getV1SupportPatientsUploadStatusRes() {}

// Ref: #/components/schemas/ImagingExam
type ImagingExam struct {
	ExamUUID    OptString        `json:"exam_uuid"`
	TransferID  OptString        `json:"transfer_id"`
	UploadTime  OptString        `json:"upload_time"`
	PatientName OptString        `json:"patient_name"`
	PatientDob  OptString        `json:"patient_dob"`
	Date        OptString        `json:"date"`
	Modality    OptString        `json:"modality"`
	Provider    OptString        `json:"provider"`
	Source      OptImagingSource `json:"source"`
	Activated   OptBool          `json:"activated"`
	RevokedAt   OptString        `json:"revoked_at"`
	UnrevokedAt OptString        `json:"unrevoked_at"`
	Reports     []Report         `json:"reports"`
	Series      OptSeries        `json:"series"`
}

// GetExamUUID returns the value of ExamUUID.
func (s *ImagingExam) GetExamUUID() OptString {
	return s.ExamUUID
}

// GetTransferID returns the value of TransferID.
func (s *ImagingExam) GetTransferID() OptString {
	return s.TransferID
}

// GetUploadTime returns the value of UploadTime.
func (s *ImagingExam) GetUploadTime() OptString {
	return s.UploadTime
}

// GetPatientName returns the value of PatientName.
func (s *ImagingExam) GetPatientName() OptString {
	return s.PatientName
}

// GetPatientDob returns the value of PatientDob.
func (s *ImagingExam) GetPatientDob() OptString {
	return s.PatientDob
}

// GetDate returns the value of Date.
func (s *ImagingExam) GetDate() OptString {
	return s.Date
}

// GetModality returns the value of Modality.
func (s *ImagingExam) GetModality() OptString {
	return s.Modality
}

// GetProvider returns the value of Provider.
func (s *ImagingExam) GetProvider() OptString {
	return s.Provider
}

// GetSource returns the value of Source.
func (s *ImagingExam) GetSource() OptImagingSource {
	return s.Source
}

// GetActivated returns the value of Activated.
func (s *ImagingExam) GetActivated() OptBool {
	return s.Activated
}

// GetRevokedAt returns the value of RevokedAt.
func (s *ImagingExam) GetRevokedAt() OptString {
	return s.RevokedAt
}

// GetUnrevokedAt returns the value of UnrevokedAt.
func (s *ImagingExam) GetUnrevokedAt() OptString {
	return s.UnrevokedAt
}

// GetReports returns the value of Reports.
func (s *ImagingExam) GetReports() []Report {
	return s.Reports
}

// GetSeries returns the value of Series.
func (s *ImagingExam) GetSeries() OptSeries {
	return s.Series
}

// SetExamUUID sets the value of ExamUUID.
func (s *ImagingExam) SetExamUUID(val OptString) {
	s.ExamUUID = val
}

// SetTransferID sets the value of TransferID.
func (s *ImagingExam) SetTransferID(val OptString) {
	s.TransferID = val
}

// SetUploadTime sets the value of UploadTime.
func (s *ImagingExam) SetUploadTime(val OptString) {
	s.UploadTime = val
}

// SetPatientName sets the value of PatientName.
func (s *ImagingExam) SetPatientName(val OptString) {
	s.PatientName = val
}

// SetPatientDob sets the value of PatientDob.
func (s *ImagingExam) SetPatientDob(val OptString) {
	s.PatientDob = val
}

// SetDate sets the value of Date.
func (s *ImagingExam) SetDate(val OptString) {
	s.Date = val
}

// SetModality sets the value of Modality.
func (s *ImagingExam) SetModality(val OptString) {
	s.Modality = val
}

// SetProvider sets the value of Provider.
func (s *ImagingExam) SetProvider(val OptString) {
	s.Provider = val
}

// SetSource sets the value of Source.
func (s *ImagingExam) SetSource(val OptImagingSource) {
	s.Source = val
}

// SetActivated sets the value of Activated.
func (s *ImagingExam) SetActivated(val OptBool) {
	s.Activated = val
}

// SetRevokedAt sets the value of RevokedAt.
func (s *ImagingExam) SetRevokedAt(val OptString) {
	s.RevokedAt = val
}

// SetUnrevokedAt sets the value of UnrevokedAt.
func (s *ImagingExam) SetUnrevokedAt(val OptString) {
	s.UnrevokedAt = val
}

// SetReports sets the value of Reports.
func (s *ImagingExam) SetReports(val []Report) {
	s.Reports = val
}

// SetSeries sets the value of Series.
func (s *ImagingExam) SetSeries(val OptSeries) {
	s.Series = val
}

// Ref: #/components/schemas/ImagingSource
type ImagingSource string

const (
	ImagingSourceSELFUPLOAD ImagingSource = "SELF_UPLOAD"
	ImagingSourceREQUEST    ImagingSource = "REQUEST"
	ImagingSourceSUBSEQUENT ImagingSource = "SUBSEQUENT"
	ImagingSourceUPH        ImagingSource = "UPH"
)

// AllValues returns all ImagingSource values.
func (ImagingSource) AllValues() []ImagingSource {
	return []ImagingSource{
		ImagingSourceSELFUPLOAD,
		ImagingSourceREQUEST,
		ImagingSourceSUBSEQUENT,
		ImagingSourceUPH,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s ImagingSource) MarshalText() ([]byte, error) {
	switch s {
	case ImagingSourceSELFUPLOAD:
		return []byte(s), nil
	case ImagingSourceREQUEST:
		return []byte(s), nil
	case ImagingSourceSUBSEQUENT:
		return []byte(s), nil
	case ImagingSourceUPH:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *ImagingSource) UnmarshalText(data []byte) error {
	switch ImagingSource(data) {
	case ImagingSourceSELFUPLOAD:
		*s = ImagingSourceSELFUPLOAD
		return nil
	case ImagingSourceREQUEST:
		*s = ImagingSourceREQUEST
		return nil
	case ImagingSourceSUBSEQUENT:
		*s = ImagingSourceSUBSEQUENT
		return nil
	case ImagingSourceUPH:
		*s = ImagingSourceUPH
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Ref: #/components/schemas/ImagingTransfer
type ImagingTransfer struct {
	TransferID      OptString  `json:"transfer_id"`
	PatientName     OptString  `json:"patient_name"`
	Provider        OptString  `json:"provider"`
	Uploaded        OptString  `json:"uploaded"`
	RequestID       OptInt     `json:"request_id"`
	Activated       OptBool    `json:"activated"`
	OrderID         OptString  `json:"order_id"`
	OrgID           OptFloat64 `json:"org_id"`
	HasRevokedExams OptBool    `json:"has_revoked_exams"`
}

// GetTransferID returns the value of TransferID.
func (s *ImagingTransfer) GetTransferID() OptString {
	return s.TransferID
}

// GetPatientName returns the value of PatientName.
func (s *ImagingTransfer) GetPatientName() OptString {
	return s.PatientName
}

// GetProvider returns the value of Provider.
func (s *ImagingTransfer) GetProvider() OptString {
	return s.Provider
}

// GetUploaded returns the value of Uploaded.
func (s *ImagingTransfer) GetUploaded() OptString {
	return s.Uploaded
}

// GetRequestID returns the value of RequestID.
func (s *ImagingTransfer) GetRequestID() OptInt {
	return s.RequestID
}

// GetActivated returns the value of Activated.
func (s *ImagingTransfer) GetActivated() OptBool {
	return s.Activated
}

// GetOrderID returns the value of OrderID.
func (s *ImagingTransfer) GetOrderID() OptString {
	return s.OrderID
}

// GetOrgID returns the value of OrgID.
func (s *ImagingTransfer) GetOrgID() OptFloat64 {
	return s.OrgID
}

// GetHasRevokedExams returns the value of HasRevokedExams.
func (s *ImagingTransfer) GetHasRevokedExams() OptBool {
	return s.HasRevokedExams
}

// SetTransferID sets the value of TransferID.
func (s *ImagingTransfer) SetTransferID(val OptString) {
	s.TransferID = val
}

// SetPatientName sets the value of PatientName.
func (s *ImagingTransfer) SetPatientName(val OptString) {
	s.PatientName = val
}

// SetProvider sets the value of Provider.
func (s *ImagingTransfer) SetProvider(val OptString) {
	s.Provider = val
}

// SetUploaded sets the value of Uploaded.
func (s *ImagingTransfer) SetUploaded(val OptString) {
	s.Uploaded = val
}

// SetRequestID sets the value of RequestID.
func (s *ImagingTransfer) SetRequestID(val OptInt) {
	s.RequestID = val
}

// SetActivated sets the value of Activated.
func (s *ImagingTransfer) SetActivated(val OptBool) {
	s.Activated = val
}

// SetOrderID sets the value of OrderID.
func (s *ImagingTransfer) SetOrderID(val OptString) {
	s.OrderID = val
}

// SetOrgID sets the value of OrgID.
func (s *ImagingTransfer) SetOrgID(val OptFloat64) {
	s.OrgID = val
}

// SetHasRevokedExams sets the value of HasRevokedExams.
func (s *ImagingTransfer) SetHasRevokedExams(val OptBool) {
	s.HasRevokedExams = val
}

type JwtBearer struct {
	Token string
}

// GetToken returns the value of Token.
func (s *JwtBearer) GetToken() string {
	return s.Token
}

// SetToken sets the value of Token.
func (s *JwtBearer) SetToken(val string) {
	s.Token = val
}

// Ref: #/components/schemas/MeddreamToken
type MeddreamToken struct {
	Items                []MeddreamTokenItemsItem                `json:"items"`
	StorageConfiguration []MeddreamTokenStorageConfigurationItem `json:"storageConfiguration"`
}

// GetItems returns the value of Items.
func (s *MeddreamToken) GetItems() []MeddreamTokenItemsItem {
	return s.Items
}

// GetStorageConfiguration returns the value of StorageConfiguration.
func (s *MeddreamToken) GetStorageConfiguration() []MeddreamTokenStorageConfigurationItem {
	return s.StorageConfiguration
}

// SetItems sets the value of Items.
func (s *MeddreamToken) SetItems(val []MeddreamTokenItemsItem) {
	s.Items = val
}

// SetStorageConfiguration sets the value of StorageConfiguration.
func (s *MeddreamToken) SetStorageConfiguration(val []MeddreamTokenStorageConfigurationItem) {
	s.StorageConfiguration = val
}

func (*MeddreamToken) postV1MeddreamValidateRes() {}

type MeddreamTokenItemsItem struct {
	Studies OptMeddreamTokenItemsItemStudies `json:"studies"`
}

// GetStudies returns the value of Studies.
func (s *MeddreamTokenItemsItem) GetStudies() OptMeddreamTokenItemsItemStudies {
	return s.Studies
}

// SetStudies sets the value of Studies.
func (s *MeddreamTokenItemsItem) SetStudies(val OptMeddreamTokenItemsItemStudies) {
	s.Studies = val
}

type MeddreamTokenItemsItemStudies struct {
	Study   OptString `json:"study"`
	Storage OptString `json:"storage"`
}

// GetStudy returns the value of Study.
func (s *MeddreamTokenItemsItemStudies) GetStudy() OptString {
	return s.Study
}

// GetStorage returns the value of Storage.
func (s *MeddreamTokenItemsItemStudies) GetStorage() OptString {
	return s.Storage
}

// SetStudy sets the value of Study.
func (s *MeddreamTokenItemsItemStudies) SetStudy(val OptString) {
	s.Study = val
}

// SetStorage sets the value of Storage.
func (s *MeddreamTokenItemsItemStudies) SetStorage(val OptString) {
	s.Storage = val
}

type MeddreamTokenStorageConfigurationItem struct {
	Storage OptString                                         `json:"storage"`
	Params  []MeddreamTokenStorageConfigurationItemParamsItem `json:"params"`
}

// GetStorage returns the value of Storage.
func (s *MeddreamTokenStorageConfigurationItem) GetStorage() OptString {
	return s.Storage
}

// GetParams returns the value of Params.
func (s *MeddreamTokenStorageConfigurationItem) GetParams() []MeddreamTokenStorageConfigurationItemParamsItem {
	return s.Params
}

// SetStorage sets the value of Storage.
func (s *MeddreamTokenStorageConfigurationItem) SetStorage(val OptString) {
	s.Storage = val
}

// SetParams sets the value of Params.
func (s *MeddreamTokenStorageConfigurationItem) SetParams(val []MeddreamTokenStorageConfigurationItemParamsItem) {
	s.Params = val
}

type MeddreamTokenStorageConfigurationItemParamsItem struct {
	Name  OptString `json:"name"`
	Value OptString `json:"value"`
}

// GetName returns the value of Name.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) GetName() OptString {
	return s.Name
}

// GetValue returns the value of Value.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) GetValue() OptString {
	return s.Value
}

// SetName sets the value of Name.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) SetName(val OptString) {
	s.Name = val
}

// SetValue sets the value of Value.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) SetValue(val OptString) {
	s.Value = val
}

// NewOptActivateStudy returns new OptActivateStudy with value set to v.
func NewOptActivateStudy(v ActivateStudy) OptActivateStudy {
	return OptActivateStudy{
		Value: v,
		Set:   true,
	}
}

// OptActivateStudy is optional ActivateStudy.
type OptActivateStudy struct {
	Value ActivateStudy
	Set   bool
}

// IsSet returns true if OptActivateStudy was set.
func (o OptActivateStudy) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptActivateStudy) Reset() {
	var v ActivateStudy
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptActivateStudy) SetTo(v ActivateStudy) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptActivateStudy) Get() (v ActivateStudy, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptActivateStudy) Or(d ActivateStudy) ActivateStudy {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptActivateStudyStudyAvailabilityStatuses returns new OptActivateStudyStudyAvailabilityStatuses with value set to v.
func NewOptActivateStudyStudyAvailabilityStatuses(v ActivateStudyStudyAvailabilityStatuses) OptActivateStudyStudyAvailabilityStatuses {
	return OptActivateStudyStudyAvailabilityStatuses{
		Value: v,
		Set:   true,
	}
}

// OptActivateStudyStudyAvailabilityStatuses is optional ActivateStudyStudyAvailabilityStatuses.
type OptActivateStudyStudyAvailabilityStatuses struct {
	Value ActivateStudyStudyAvailabilityStatuses
	Set   bool
}

// IsSet returns true if OptActivateStudyStudyAvailabilityStatuses was set.
func (o OptActivateStudyStudyAvailabilityStatuses) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptActivateStudyStudyAvailabilityStatuses) Reset() {
	var v ActivateStudyStudyAvailabilityStatuses
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptActivateStudyStudyAvailabilityStatuses) SetTo(v ActivateStudyStudyAvailabilityStatuses) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptActivateStudyStudyAvailabilityStatuses) Get() (v ActivateStudyStudyAvailabilityStatuses, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptActivateStudyStudyAvailabilityStatuses) Or(d ActivateStudyStudyAvailabilityStatuses) ActivateStudyStudyAvailabilityStatuses {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptBlock returns new OptBlock with value set to v.
func NewOptBlock(v Block) OptBlock {
	return OptBlock{
		Value: v,
		Set:   true,
	}
}

// OptBlock is optional Block.
type OptBlock struct {
	Value Block
	Set   bool
}

// IsSet returns true if OptBlock was set.
func (o OptBlock) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptBlock) Reset() {
	var v Block
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptBlock) SetTo(v Block) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptBlock) Get() (v Block, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptBlock) Or(d Block) Block {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptBool returns new OptBool with value set to v.
func NewOptBool(v bool) OptBool {
	return OptBool{
		Value: v,
		Set:   true,
	}
}

// OptBool is optional bool.
type OptBool struct {
	Value bool
	Set   bool
}

// IsSet returns true if OptBool was set.
func (o OptBool) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptBool) Reset() {
	var v bool
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptBool) SetTo(v bool) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptBool) Get() (v bool, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptBool) Or(d bool) bool {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptBusinessRuleResultResult returns new OptBusinessRuleResultResult with value set to v.
func NewOptBusinessRuleResultResult(v BusinessRuleResultResult) OptBusinessRuleResultResult {
	return OptBusinessRuleResultResult{
		Value: v,
		Set:   true,
	}
}

// OptBusinessRuleResultResult is optional BusinessRuleResultResult.
type OptBusinessRuleResultResult struct {
	Value BusinessRuleResultResult
	Set   bool
}

// IsSet returns true if OptBusinessRuleResultResult was set.
func (o OptBusinessRuleResultResult) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptBusinessRuleResultResult) Reset() {
	var v BusinessRuleResultResult
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptBusinessRuleResultResult) SetTo(v BusinessRuleResultResult) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptBusinessRuleResultResult) Get() (v BusinessRuleResultResult, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptBusinessRuleResultResult) Or(d BusinessRuleResultResult) BusinessRuleResultResult {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDate returns new OptDate with value set to v.
func NewOptDate(v time.Time) OptDate {
	return OptDate{
		Value: v,
		Set:   true,
	}
}

// OptDate is optional time.Time.
type OptDate struct {
	Value time.Time
	Set   bool
}

// IsSet returns true if OptDate was set.
func (o OptDate) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDate) Reset() {
	var v time.Time
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDate) SetTo(v time.Time) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDate) Get() (v time.Time, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDate) Or(d time.Time) time.Time {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDateTime returns new OptDateTime with value set to v.
func NewOptDateTime(v time.Time) OptDateTime {
	return OptDateTime{
		Value: v,
		Set:   true,
	}
}

// OptDateTime is optional time.Time.
type OptDateTime struct {
	Value time.Time
	Set   bool
}

// IsSet returns true if OptDateTime was set.
func (o OptDateTime) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDateTime) Reset() {
	var v time.Time
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDateTime) SetTo(v time.Time) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDateTime) Get() (v time.Time, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDateTime) Or(d time.Time) time.Time {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDicomInstanceTags returns new OptDicomInstanceTags with value set to v.
func NewOptDicomInstanceTags(v DicomInstanceTags) OptDicomInstanceTags {
	return OptDicomInstanceTags{
		Value: v,
		Set:   true,
	}
}

// OptDicomInstanceTags is optional DicomInstanceTags.
type OptDicomInstanceTags struct {
	Value DicomInstanceTags
	Set   bool
}

// IsSet returns true if OptDicomInstanceTags was set.
func (o OptDicomInstanceTags) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDicomInstanceTags) Reset() {
	var v DicomInstanceTags
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDicomInstanceTags) SetTo(v DicomInstanceTags) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDicomInstanceTags) Get() (v DicomInstanceTags, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDicomInstanceTags) Or(d DicomInstanceTags) DicomInstanceTags {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDicomPatientTags returns new OptDicomPatientTags with value set to v.
func NewOptDicomPatientTags(v DicomPatientTags) OptDicomPatientTags {
	return OptDicomPatientTags{
		Value: v,
		Set:   true,
	}
}

// OptDicomPatientTags is optional DicomPatientTags.
type OptDicomPatientTags struct {
	Value DicomPatientTags
	Set   bool
}

// IsSet returns true if OptDicomPatientTags was set.
func (o OptDicomPatientTags) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDicomPatientTags) Reset() {
	var v DicomPatientTags
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDicomPatientTags) SetTo(v DicomPatientTags) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDicomPatientTags) Get() (v DicomPatientTags, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDicomPatientTags) Or(d DicomPatientTags) DicomPatientTags {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDicomSeriesTags returns new OptDicomSeriesTags with value set to v.
func NewOptDicomSeriesTags(v DicomSeriesTags) OptDicomSeriesTags {
	return OptDicomSeriesTags{
		Value: v,
		Set:   true,
	}
}

// OptDicomSeriesTags is optional DicomSeriesTags.
type OptDicomSeriesTags struct {
	Value DicomSeriesTags
	Set   bool
}

// IsSet returns true if OptDicomSeriesTags was set.
func (o OptDicomSeriesTags) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDicomSeriesTags) Reset() {
	var v DicomSeriesTags
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDicomSeriesTags) SetTo(v DicomSeriesTags) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDicomSeriesTags) Get() (v DicomSeriesTags, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDicomSeriesTags) Or(d DicomSeriesTags) DicomSeriesTags {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptDicomStudyTags returns new OptDicomStudyTags with value set to v.
func NewOptDicomStudyTags(v DicomStudyTags) OptDicomStudyTags {
	return OptDicomStudyTags{
		Value: v,
		Set:   true,
	}
}

// OptDicomStudyTags is optional DicomStudyTags.
type OptDicomStudyTags struct {
	Value DicomStudyTags
	Set   bool
}

// IsSet returns true if OptDicomStudyTags was set.
func (o OptDicomStudyTags) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptDicomStudyTags) Reset() {
	var v DicomStudyTags
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptDicomStudyTags) SetTo(v DicomStudyTags) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptDicomStudyTags) Get() (v DicomStudyTags, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptDicomStudyTags) Or(d DicomStudyTags) DicomStudyTags {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptFloat64 returns new OptFloat64 with value set to v.
func NewOptFloat64(v float64) OptFloat64 {
	return OptFloat64{
		Value: v,
		Set:   true,
	}
}

// OptFloat64 is optional float64.
type OptFloat64 struct {
	Value float64
	Set   bool
}

// IsSet returns true if OptFloat64 was set.
func (o OptFloat64) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptFloat64) Reset() {
	var v float64
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptFloat64) SetTo(v float64) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptFloat64) Get() (v float64, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptFloat64) Or(d float64) float64 {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptGenerateMedreamToken returns new OptGenerateMedreamToken with value set to v.
func NewOptGenerateMedreamToken(v GenerateMedreamToken) OptGenerateMedreamToken {
	return OptGenerateMedreamToken{
		Value: v,
		Set:   true,
	}
}

// OptGenerateMedreamToken is optional GenerateMedreamToken.
type OptGenerateMedreamToken struct {
	Value GenerateMedreamToken
	Set   bool
}

// IsSet returns true if OptGenerateMedreamToken was set.
func (o OptGenerateMedreamToken) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptGenerateMedreamToken) Reset() {
	var v GenerateMedreamToken
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptGenerateMedreamToken) SetTo(v GenerateMedreamToken) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptGenerateMedreamToken) Get() (v GenerateMedreamToken, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptGenerateMedreamToken) Or(d GenerateMedreamToken) GenerateMedreamToken {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptImagingSource returns new OptImagingSource with value set to v.
func NewOptImagingSource(v ImagingSource) OptImagingSource {
	return OptImagingSource{
		Value: v,
		Set:   true,
	}
}

// OptImagingSource is optional ImagingSource.
type OptImagingSource struct {
	Value ImagingSource
	Set   bool
}

// IsSet returns true if OptImagingSource was set.
func (o OptImagingSource) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptImagingSource) Reset() {
	var v ImagingSource
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptImagingSource) SetTo(v ImagingSource) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptImagingSource) Get() (v ImagingSource, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptImagingSource) Or(d ImagingSource) ImagingSource {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptInt returns new OptInt with value set to v.
func NewOptInt(v int) OptInt {
	return OptInt{
		Value: v,
		Set:   true,
	}
}

// OptInt is optional int.
type OptInt struct {
	Value int
	Set   bool
}

// IsSet returns true if OptInt was set.
func (o OptInt) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptInt) Reset() {
	var v int
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptInt) SetTo(v int) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptInt) Get() (v int, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptInt) Or(d int) int {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptInt32 returns new OptInt32 with value set to v.
func NewOptInt32(v int32) OptInt32 {
	return OptInt32{
		Value: v,
		Set:   true,
	}
}

// OptInt32 is optional int32.
type OptInt32 struct {
	Value int32
	Set   bool
}

// IsSet returns true if OptInt32 was set.
func (o OptInt32) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptInt32) Reset() {
	var v int32
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptInt32) SetTo(v int32) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptInt32) Get() (v int32, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptInt32) Or(d int32) int32 {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptInt64 returns new OptInt64 with value set to v.
func NewOptInt64(v int64) OptInt64 {
	return OptInt64{
		Value: v,
		Set:   true,
	}
}

// OptInt64 is optional int64.
type OptInt64 struct {
	Value int64
	Set   bool
}

// IsSet returns true if OptInt64 was set.
func (o OptInt64) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptInt64) Reset() {
	var v int64
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptInt64) SetTo(v int64) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptInt64) Get() (v int64, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptInt64) Or(d int64) int64 {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptMeddreamTokenItemsItemStudies returns new OptMeddreamTokenItemsItemStudies with value set to v.
func NewOptMeddreamTokenItemsItemStudies(v MeddreamTokenItemsItemStudies) OptMeddreamTokenItemsItemStudies {
	return OptMeddreamTokenItemsItemStudies{
		Value: v,
		Set:   true,
	}
}

// OptMeddreamTokenItemsItemStudies is optional MeddreamTokenItemsItemStudies.
type OptMeddreamTokenItemsItemStudies struct {
	Value MeddreamTokenItemsItemStudies
	Set   bool
}

// IsSet returns true if OptMeddreamTokenItemsItemStudies was set.
func (o OptMeddreamTokenItemsItemStudies) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptMeddreamTokenItemsItemStudies) Reset() {
	var v MeddreamTokenItemsItemStudies
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptMeddreamTokenItemsItemStudies) SetTo(v MeddreamTokenItemsItemStudies) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptMeddreamTokenItemsItemStudies) Get() (v MeddreamTokenItemsItemStudies, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptMeddreamTokenItemsItemStudies) Or(d MeddreamTokenItemsItemStudies) MeddreamTokenItemsItemStudies {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptNilBusinessRuleResultArray returns new OptNilBusinessRuleResultArray with value set to v.
func NewOptNilBusinessRuleResultArray(v []BusinessRuleResult) OptNilBusinessRuleResultArray {
	return OptNilBusinessRuleResultArray{
		Value: v,
		Set:   true,
	}
}

// OptNilBusinessRuleResultArray is optional nullable []BusinessRuleResult.
type OptNilBusinessRuleResultArray struct {
	Value []BusinessRuleResult
	Set   bool
	Null  bool
}

// IsSet returns true if OptNilBusinessRuleResultArray was set.
func (o OptNilBusinessRuleResultArray) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptNilBusinessRuleResultArray) Reset() {
	var v []BusinessRuleResult
	o.Value = v
	o.Set = false
	o.Null = false
}

// SetTo sets value to v.
func (o *OptNilBusinessRuleResultArray) SetTo(v []BusinessRuleResult) {
	o.Set = true
	o.Null = false
	o.Value = v
}

// IsSet returns true if value is Null.
func (o OptNilBusinessRuleResultArray) IsNull() bool { return o.Null }

// SetNull sets value to null.
func (o *OptNilBusinessRuleResultArray) SetToNull() {
	o.Set = true
	o.Null = true
	var v []BusinessRuleResult
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptNilBusinessRuleResultArray) Get() (v []BusinessRuleResult, ok bool) {
	if o.Null {
		return v, false
	}
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptNilBusinessRuleResultArray) Or(d []BusinessRuleResult) []BusinessRuleResult {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptNilDicomInstanceArray returns new OptNilDicomInstanceArray with value set to v.
func NewOptNilDicomInstanceArray(v []DicomInstance) OptNilDicomInstanceArray {
	return OptNilDicomInstanceArray{
		Value: v,
		Set:   true,
	}
}

// OptNilDicomInstanceArray is optional nullable []DicomInstance.
type OptNilDicomInstanceArray struct {
	Value []DicomInstance
	Set   bool
	Null  bool
}

// IsSet returns true if OptNilDicomInstanceArray was set.
func (o OptNilDicomInstanceArray) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptNilDicomInstanceArray) Reset() {
	var v []DicomInstance
	o.Value = v
	o.Set = false
	o.Null = false
}

// SetTo sets value to v.
func (o *OptNilDicomInstanceArray) SetTo(v []DicomInstance) {
	o.Set = true
	o.Null = false
	o.Value = v
}

// IsSet returns true if value is Null.
func (o OptNilDicomInstanceArray) IsNull() bool { return o.Null }

// SetNull sets value to null.
func (o *OptNilDicomInstanceArray) SetToNull() {
	o.Set = true
	o.Null = true
	var v []DicomInstance
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptNilDicomInstanceArray) Get() (v []DicomInstance, ok bool) {
	if o.Null {
		return v, false
	}
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptNilDicomInstanceArray) Or(d []DicomInstance) []DicomInstance {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptNilPermissionGroupArray returns new OptNilPermissionGroupArray with value set to v.
func NewOptNilPermissionGroupArray(v []PermissionGroup) OptNilPermissionGroupArray {
	return OptNilPermissionGroupArray{
		Value: v,
		Set:   true,
	}
}

// OptNilPermissionGroupArray is optional nullable []PermissionGroup.
type OptNilPermissionGroupArray struct {
	Value []PermissionGroup
	Set   bool
	Null  bool
}

// IsSet returns true if OptNilPermissionGroupArray was set.
func (o OptNilPermissionGroupArray) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptNilPermissionGroupArray) Reset() {
	var v []PermissionGroup
	o.Value = v
	o.Set = false
	o.Null = false
}

// SetTo sets value to v.
func (o *OptNilPermissionGroupArray) SetTo(v []PermissionGroup) {
	o.Set = true
	o.Null = false
	o.Value = v
}

// IsSet returns true if value is Null.
func (o OptNilPermissionGroupArray) IsNull() bool { return o.Null }

// SetNull sets value to null.
func (o *OptNilPermissionGroupArray) SetToNull() {
	o.Set = true
	o.Null = true
	var v []PermissionGroup
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptNilPermissionGroupArray) Get() (v []PermissionGroup, ok bool) {
	if o.Null {
		return v, false
	}
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptNilPermissionGroupArray) Or(d []PermissionGroup) []PermissionGroup {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptNilString returns new OptNilString with value set to v.
func NewOptNilString(v string) OptNilString {
	return OptNilString{
		Value: v,
		Set:   true,
	}
}

// OptNilString is optional nullable string.
type OptNilString struct {
	Value string
	Set   bool
	Null  bool
}

// IsSet returns true if OptNilString was set.
func (o OptNilString) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptNilString) Reset() {
	var v string
	o.Value = v
	o.Set = false
	o.Null = false
}

// SetTo sets value to v.
func (o *OptNilString) SetTo(v string) {
	o.Set = true
	o.Null = false
	o.Value = v
}

// IsSet returns true if value is Null.
func (o OptNilString) IsNull() bool { return o.Null }

// SetNull sets value to null.
func (o *OptNilString) SetToNull() {
	o.Set = true
	o.Null = true
	var v string
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptNilString) Get() (v string, ok bool) {
	if o.Null {
		return v, false
	}
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptNilString) Or(d string) string {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptNilStudyReportArray returns new OptNilStudyReportArray with value set to v.
func NewOptNilStudyReportArray(v []StudyReport) OptNilStudyReportArray {
	return OptNilStudyReportArray{
		Value: v,
		Set:   true,
	}
}

// OptNilStudyReportArray is optional nullable []StudyReport.
type OptNilStudyReportArray struct {
	Value []StudyReport
	Set   bool
	Null  bool
}

// IsSet returns true if OptNilStudyReportArray was set.
func (o OptNilStudyReportArray) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptNilStudyReportArray) Reset() {
	var v []StudyReport
	o.Value = v
	o.Set = false
	o.Null = false
}

// SetTo sets value to v.
func (o *OptNilStudyReportArray) SetTo(v []StudyReport) {
	o.Set = true
	o.Null = false
	o.Value = v
}

// IsSet returns true if value is Null.
func (o OptNilStudyReportArray) IsNull() bool { return o.Null }

// SetNull sets value to null.
func (o *OptNilStudyReportArray) SetToNull() {
	o.Set = true
	o.Null = true
	var v []StudyReport
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptNilStudyReportArray) Get() (v []StudyReport, ok bool) {
	if o.Null {
		return v, false
	}
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptNilStudyReportArray) Or(d []StudyReport) []StudyReport {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptPostV1BusinessRulesetsEvaluateReq returns new OptPostV1BusinessRulesetsEvaluateReq with value set to v.
func NewOptPostV1BusinessRulesetsEvaluateReq(v PostV1BusinessRulesetsEvaluateReq) OptPostV1BusinessRulesetsEvaluateReq {
	return OptPostV1BusinessRulesetsEvaluateReq{
		Value: v,
		Set:   true,
	}
}

// OptPostV1BusinessRulesetsEvaluateReq is optional PostV1BusinessRulesetsEvaluateReq.
type OptPostV1BusinessRulesetsEvaluateReq struct {
	Value PostV1BusinessRulesetsEvaluateReq
	Set   bool
}

// IsSet returns true if OptPostV1BusinessRulesetsEvaluateReq was set.
func (o OptPostV1BusinessRulesetsEvaluateReq) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptPostV1BusinessRulesetsEvaluateReq) Reset() {
	var v PostV1BusinessRulesetsEvaluateReq
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptPostV1BusinessRulesetsEvaluateReq) SetTo(v PostV1BusinessRulesetsEvaluateReq) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptPostV1BusinessRulesetsEvaluateReq) Get() (v PostV1BusinessRulesetsEvaluateReq, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptPostV1BusinessRulesetsEvaluateReq) Or(d PostV1BusinessRulesetsEvaluateReq) PostV1BusinessRulesetsEvaluateReq {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptProviderShareSearch returns new OptProviderShareSearch with value set to v.
func NewOptProviderShareSearch(v ProviderShareSearch) OptProviderShareSearch {
	return OptProviderShareSearch{
		Value: v,
		Set:   true,
	}
}

// OptProviderShareSearch is optional ProviderShareSearch.
type OptProviderShareSearch struct {
	Value ProviderShareSearch
	Set   bool
}

// IsSet returns true if OptProviderShareSearch was set.
func (o OptProviderShareSearch) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptProviderShareSearch) Reset() {
	var v ProviderShareSearch
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptProviderShareSearch) SetTo(v ProviderShareSearch) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptProviderShareSearch) Get() (v ProviderShareSearch, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptProviderShareSearch) Or(d ProviderShareSearch) ProviderShareSearch {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptReportType returns new OptReportType with value set to v.
func NewOptReportType(v ReportType) OptReportType {
	return OptReportType{
		Value: v,
		Set:   true,
	}
}

// OptReportType is optional ReportType.
type OptReportType struct {
	Value ReportType
	Set   bool
}

// IsSet returns true if OptReportType was set.
func (o OptReportType) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptReportType) Reset() {
	var v ReportType
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptReportType) SetTo(v ReportType) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptReportType) Get() (v ReportType, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptReportType) Or(d ReportType) ReportType {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptRule returns new OptRule with value set to v.
func NewOptRule(v Rule) OptRule {
	return OptRule{
		Value: v,
		Set:   true,
	}
}

// OptRule is optional Rule.
type OptRule struct {
	Value Rule
	Set   bool
}

// IsSet returns true if OptRule was set.
func (o OptRule) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptRule) Reset() {
	var v Rule
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptRule) SetTo(v Rule) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptRule) Get() (v Rule, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptRule) Or(d Rule) Rule {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptRuleLogic returns new OptRuleLogic with value set to v.
func NewOptRuleLogic(v RuleLogic) OptRuleLogic {
	return OptRuleLogic{
		Value: v,
		Set:   true,
	}
}

// OptRuleLogic is optional RuleLogic.
type OptRuleLogic struct {
	Value RuleLogic
	Set   bool
}

// IsSet returns true if OptRuleLogic was set.
func (o OptRuleLogic) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptRuleLogic) Reset() {
	var v RuleLogic
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptRuleLogic) SetTo(v RuleLogic) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptRuleLogic) Get() (v RuleLogic, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptRuleLogic) Or(d RuleLogic) RuleLogic {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptRuleOperator returns new OptRuleOperator with value set to v.
func NewOptRuleOperator(v RuleOperator) OptRuleOperator {
	return OptRuleOperator{
		Value: v,
		Set:   true,
	}
}

// OptRuleOperator is optional RuleOperator.
type OptRuleOperator struct {
	Value RuleOperator
	Set   bool
}

// IsSet returns true if OptRuleOperator was set.
func (o OptRuleOperator) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptRuleOperator) Reset() {
	var v RuleOperator
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptRuleOperator) SetTo(v RuleOperator) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptRuleOperator) Get() (v RuleOperator, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptRuleOperator) Or(d RuleOperator) RuleOperator {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptRuleValue returns new OptRuleValue with value set to v.
func NewOptRuleValue(v RuleValue) OptRuleValue {
	return OptRuleValue{
		Value: v,
		Set:   true,
	}
}

// OptRuleValue is optional RuleValue.
type OptRuleValue struct {
	Value RuleValue
	Set   bool
}

// IsSet returns true if OptRuleValue was set.
func (o OptRuleValue) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptRuleValue) Reset() {
	var v RuleValue
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptRuleValue) SetTo(v RuleValue) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptRuleValue) Get() (v RuleValue, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptRuleValue) Or(d RuleValue) RuleValue {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptSeries returns new OptSeries with value set to v.
func NewOptSeries(v Series) OptSeries {
	return OptSeries{
		Value: v,
		Set:   true,
	}
}

// OptSeries is optional Series.
type OptSeries struct {
	Value Series
	Set   bool
}

// IsSet returns true if OptSeries was set.
func (o OptSeries) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptSeries) Reset() {
	var v Series
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptSeries) SetTo(v Series) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptSeries) Get() (v Series, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptSeries) Or(d Series) Series {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptString returns new OptString with value set to v.
func NewOptString(v string) OptString {
	return OptString{
		Value: v,
		Set:   true,
	}
}

// OptString is optional string.
type OptString struct {
	Value string
	Set   bool
}

// IsSet returns true if OptString was set.
func (o OptString) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptString) Reset() {
	var v string
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptString) SetTo(v string) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptString) Get() (v string, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptString) Or(d string) string {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptStudyAttribution returns new OptStudyAttribution with value set to v.
func NewOptStudyAttribution(v StudyAttribution) OptStudyAttribution {
	return OptStudyAttribution{
		Value: v,
		Set:   true,
	}
}

// OptStudyAttribution is optional StudyAttribution.
type OptStudyAttribution struct {
	Value StudyAttribution
	Set   bool
}

// IsSet returns true if OptStudyAttribution was set.
func (o OptStudyAttribution) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptStudyAttribution) Reset() {
	var v StudyAttribution
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptStudyAttribution) SetTo(v StudyAttribution) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptStudyAttribution) Get() (v StudyAttribution, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptStudyAttribution) Or(d StudyAttribution) StudyAttribution {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptStudyAvailabilityStatus returns new OptStudyAvailabilityStatus with value set to v.
func NewOptStudyAvailabilityStatus(v StudyAvailabilityStatus) OptStudyAvailabilityStatus {
	return OptStudyAvailabilityStatus{
		Value: v,
		Set:   true,
	}
}

// OptStudyAvailabilityStatus is optional StudyAvailabilityStatus.
type OptStudyAvailabilityStatus struct {
	Value StudyAvailabilityStatus
	Set   bool
}

// IsSet returns true if OptStudyAvailabilityStatus was set.
func (o OptStudyAvailabilityStatus) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptStudyAvailabilityStatus) Reset() {
	var v StudyAvailabilityStatus
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptStudyAvailabilityStatus) SetTo(v StudyAvailabilityStatus) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptStudyAvailabilityStatus) Get() (v StudyAvailabilityStatus, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptStudyAvailabilityStatus) Or(d StudyAvailabilityStatus) StudyAvailabilityStatus {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// NewOptStudyUploadMetadataAccountType returns new OptStudyUploadMetadataAccountType with value set to v.
func NewOptStudyUploadMetadataAccountType(v StudyUploadMetadataAccountType) OptStudyUploadMetadataAccountType {
	return OptStudyUploadMetadataAccountType{
		Value: v,
		Set:   true,
	}
}

// OptStudyUploadMetadataAccountType is optional StudyUploadMetadataAccountType.
type OptStudyUploadMetadataAccountType struct {
	Value StudyUploadMetadataAccountType
	Set   bool
}

// IsSet returns true if OptStudyUploadMetadataAccountType was set.
func (o OptStudyUploadMetadataAccountType) IsSet() bool { return o.Set }

// Reset unsets value.
func (o *OptStudyUploadMetadataAccountType) Reset() {
	var v StudyUploadMetadataAccountType
	o.Value = v
	o.Set = false
}

// SetTo sets value to v.
func (o *OptStudyUploadMetadataAccountType) SetTo(v StudyUploadMetadataAccountType) {
	o.Set = true
	o.Value = v
}

// Get returns value and boolean that denotes whether value was set.
func (o OptStudyUploadMetadataAccountType) Get() (v StudyUploadMetadataAccountType, ok bool) {
	if !o.Set {
		return v, false
	}
	return o.Value, true
}

// Or returns value if set, or given parameter if does not.
func (o OptStudyUploadMetadataAccountType) Or(d StudyUploadMetadataAccountType) StudyUploadMetadataAccountType {
	if v, ok := o.Get(); ok {
		return v
	}
	return d
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevokeInternalServerError is response for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeInternalServerError struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDRevokeInternalServerError) patchSupportV1PatientsAccountIDStudiesUUIDRevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevokeNotFound is response for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeNotFound struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDRevokeNotFound) patchSupportV1PatientsAccountIDStudiesUUIDRevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevokeOK is response for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeOK struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDRevokeOK) patchSupportV1PatientsAccountIDStudiesUUIDRevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDRevokeUnauthorized is response for PatchSupportV1PatientsAccountIDStudiesUUIDRevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDRevokeUnauthorized struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDRevokeUnauthorized) patchSupportV1PatientsAccountIDStudiesUUIDRevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeInternalServerError is response for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeInternalServerError struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeInternalServerError) patchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeNotFound is response for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeNotFound struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeNotFound) patchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeOK is response for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeOK struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeOK) patchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes() {
}

// PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeUnauthorized is response for PatchSupportV1PatientsAccountIDStudiesUUIDUnrevoke operation.
type PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeUnauthorized struct{}

func (*PatchSupportV1PatientsAccountIDStudiesUUIDUnrevokeUnauthorized) patchSupportV1PatientsAccountIDStudiesUUIDUnrevokeRes() {
}

// PatchV0ImagingTransfersIDRevokeInternalServerError is response for PatchV0ImagingTransfersIDRevoke operation.
type PatchV0ImagingTransfersIDRevokeInternalServerError struct{}

func (*PatchV0ImagingTransfersIDRevokeInternalServerError) patchV0ImagingTransfersIDRevokeRes() {}

// PatchV0ImagingTransfersIDRevokeOK is response for PatchV0ImagingTransfersIDRevoke operation.
type PatchV0ImagingTransfersIDRevokeOK struct{}

func (*PatchV0ImagingTransfersIDRevokeOK) patchV0ImagingTransfersIDRevokeRes() {}

// PatchV0ImagingTransfersIDRevokeUnauthorized is response for PatchV0ImagingTransfersIDRevoke operation.
type PatchV0ImagingTransfersIDRevokeUnauthorized struct{}

func (*PatchV0ImagingTransfersIDRevokeUnauthorized) patchV0ImagingTransfersIDRevokeRes() {}

// PatchV0ImagingTransfersIDUnrevokeInternalServerError is response for PatchV0ImagingTransfersIDUnrevoke operation.
type PatchV0ImagingTransfersIDUnrevokeInternalServerError struct{}

func (*PatchV0ImagingTransfersIDUnrevokeInternalServerError) patchV0ImagingTransfersIDUnrevokeRes() {}

// PatchV0ImagingTransfersIDUnrevokeNotFound is response for PatchV0ImagingTransfersIDUnrevoke operation.
type PatchV0ImagingTransfersIDUnrevokeNotFound struct{}

func (*PatchV0ImagingTransfersIDUnrevokeNotFound) patchV0ImagingTransfersIDUnrevokeRes() {}

// PatchV0ImagingTransfersIDUnrevokeOK is response for PatchV0ImagingTransfersIDUnrevoke operation.
type PatchV0ImagingTransfersIDUnrevokeOK struct{}

func (*PatchV0ImagingTransfersIDUnrevokeOK) patchV0ImagingTransfersIDUnrevokeRes() {}

// PatchV0ImagingTransfersIDUnrevokeUnauthorized is response for PatchV0ImagingTransfersIDUnrevoke operation.
type PatchV0ImagingTransfersIDUnrevokeUnauthorized struct{}

func (*PatchV0ImagingTransfersIDUnrevokeUnauthorized) patchV0ImagingTransfersIDUnrevokeRes() {}

// PatchV0ImagingUUIDRevokeInternalServerError is response for PatchV0ImagingUUIDRevoke operation.
type PatchV0ImagingUUIDRevokeInternalServerError struct{}

func (*PatchV0ImagingUUIDRevokeInternalServerError) patchV0ImagingUUIDRevokeRes() {}

// PatchV0ImagingUUIDRevokeNotFound is response for PatchV0ImagingUUIDRevoke operation.
type PatchV0ImagingUUIDRevokeNotFound struct{}

func (*PatchV0ImagingUUIDRevokeNotFound) patchV0ImagingUUIDRevokeRes() {}

// PatchV0ImagingUUIDRevokeOK is response for PatchV0ImagingUUIDRevoke operation.
type PatchV0ImagingUUIDRevokeOK struct{}

func (*PatchV0ImagingUUIDRevokeOK) patchV0ImagingUUIDRevokeRes() {}

// PatchV0ImagingUUIDRevokeUnauthorized is response for PatchV0ImagingUUIDRevoke operation.
type PatchV0ImagingUUIDRevokeUnauthorized struct{}

func (*PatchV0ImagingUUIDRevokeUnauthorized) patchV0ImagingUUIDRevokeRes() {}

// PatchV0ImagingUUIDUnrevokeInternalServerError is response for PatchV0ImagingUUIDUnrevoke operation.
type PatchV0ImagingUUIDUnrevokeInternalServerError struct{}

func (*PatchV0ImagingUUIDUnrevokeInternalServerError) patchV0ImagingUUIDUnrevokeRes() {}

// PatchV0ImagingUUIDUnrevokeOK is response for PatchV0ImagingUUIDUnrevoke operation.
type PatchV0ImagingUUIDUnrevokeOK struct{}

func (*PatchV0ImagingUUIDUnrevokeOK) patchV0ImagingUUIDUnrevokeRes() {}

// PatchV0ImagingUUIDUnrevokeUnauthorized is response for PatchV0ImagingUUIDUnrevoke operation.
type PatchV0ImagingUUIDUnrevokeUnauthorized struct{}

func (*PatchV0ImagingUUIDUnrevokeUnauthorized) patchV0ImagingUUIDUnrevokeRes() {}

// Describes an access permission of a pockethealth patient account to a specific study. Only set if
// a patient has access to the study. Contains account id of pockethealth patient account that has
// access permission to study, patient id of patient within pockethealth patient account that owns
// study and the availability of the study for that patient, based on their plan (e.g. locked, full
// access, ...).
// Ref: #/components/schemas/patient_account_permission
type PatientAccountPermission struct {
	// The KSUID associated with a pockethealth patient account.
	AccountID string `json:"account_id"`
	// The KSUID associated with a patient within a pockethealth patient account.
	PatientID               string                  `json:"patient_id"`
	StudyAvailabilityStatus StudyAvailabilityStatus `json:"study_availability_status"`
}

// GetAccountID returns the value of AccountID.
func (s *PatientAccountPermission) GetAccountID() string {
	return s.AccountID
}

// GetPatientID returns the value of PatientID.
func (s *PatientAccountPermission) GetPatientID() string {
	return s.PatientID
}

// GetStudyAvailabilityStatus returns the value of StudyAvailabilityStatus.
func (s *PatientAccountPermission) GetStudyAvailabilityStatus() StudyAvailabilityStatus {
	return s.StudyAvailabilityStatus
}

// SetAccountID sets the value of AccountID.
func (s *PatientAccountPermission) SetAccountID(val string) {
	s.AccountID = val
}

// SetPatientID sets the value of PatientID.
func (s *PatientAccountPermission) SetPatientID(val string) {
	s.PatientID = val
}

// SetStudyAvailabilityStatus sets the value of StudyAvailabilityStatus.
func (s *PatientAccountPermission) SetStudyAvailabilityStatus(val StudyAvailabilityStatus) {
	s.StudyAvailabilityStatus = val
}

// Ref: #/components/schemas/patient_study
type PatientStudy struct {
	UUID                          OptString              `json:"uuid"`
	PatientID                     OptString              `json:"patient_id"`
	OrganizationID                OptInt64               `json:"organization_id"`
	ActivatedTimestamp            OptDateTime            `json:"activated_timestamp"`
	AvailabilityStatus            OptString              `json:"availability_status"`
	OrderID                       OptString              `json:"order_id"`
	TransferID                    OptString              `json:"transfer_id"`
	InstanceUploadProgressPercent OptInt                 `json:"instance_upload_progress_percent"`
	DicomPatientTags              OptDicomPatientTags    `json:"dicom_patient_tags"`
	DicomStudyTags                OptDicomStudyTags      `json:"dicom_study_tags"`
	Series                        []DicomSeries          `json:"series"`
	Reports                       OptNilStudyReportArray `json:"reports"`
	HasReport                     OptBool                `json:"has_report"`
}

// GetUUID returns the value of UUID.
func (s *PatientStudy) GetUUID() OptString {
	return s.UUID
}

// GetPatientID returns the value of PatientID.
func (s *PatientStudy) GetPatientID() OptString {
	return s.PatientID
}

// GetOrganizationID returns the value of OrganizationID.
func (s *PatientStudy) GetOrganizationID() OptInt64 {
	return s.OrganizationID
}

// GetActivatedTimestamp returns the value of ActivatedTimestamp.
func (s *PatientStudy) GetActivatedTimestamp() OptDateTime {
	return s.ActivatedTimestamp
}

// GetAvailabilityStatus returns the value of AvailabilityStatus.
func (s *PatientStudy) GetAvailabilityStatus() OptString {
	return s.AvailabilityStatus
}

// GetOrderID returns the value of OrderID.
func (s *PatientStudy) GetOrderID() OptString {
	return s.OrderID
}

// GetTransferID returns the value of TransferID.
func (s *PatientStudy) GetTransferID() OptString {
	return s.TransferID
}

// GetInstanceUploadProgressPercent returns the value of InstanceUploadProgressPercent.
func (s *PatientStudy) GetInstanceUploadProgressPercent() OptInt {
	return s.InstanceUploadProgressPercent
}

// GetDicomPatientTags returns the value of DicomPatientTags.
func (s *PatientStudy) GetDicomPatientTags() OptDicomPatientTags {
	return s.DicomPatientTags
}

// GetDicomStudyTags returns the value of DicomStudyTags.
func (s *PatientStudy) GetDicomStudyTags() OptDicomStudyTags {
	return s.DicomStudyTags
}

// GetSeries returns the value of Series.
func (s *PatientStudy) GetSeries() []DicomSeries {
	return s.Series
}

// GetReports returns the value of Reports.
func (s *PatientStudy) GetReports() OptNilStudyReportArray {
	return s.Reports
}

// GetHasReport returns the value of HasReport.
func (s *PatientStudy) GetHasReport() OptBool {
	return s.HasReport
}

// SetUUID sets the value of UUID.
func (s *PatientStudy) SetUUID(val OptString) {
	s.UUID = val
}

// SetPatientID sets the value of PatientID.
func (s *PatientStudy) SetPatientID(val OptString) {
	s.PatientID = val
}

// SetOrganizationID sets the value of OrganizationID.
func (s *PatientStudy) SetOrganizationID(val OptInt64) {
	s.OrganizationID = val
}

// SetActivatedTimestamp sets the value of ActivatedTimestamp.
func (s *PatientStudy) SetActivatedTimestamp(val OptDateTime) {
	s.ActivatedTimestamp = val
}

// SetAvailabilityStatus sets the value of AvailabilityStatus.
func (s *PatientStudy) SetAvailabilityStatus(val OptString) {
	s.AvailabilityStatus = val
}

// SetOrderID sets the value of OrderID.
func (s *PatientStudy) SetOrderID(val OptString) {
	s.OrderID = val
}

// SetTransferID sets the value of TransferID.
func (s *PatientStudy) SetTransferID(val OptString) {
	s.TransferID = val
}

// SetInstanceUploadProgressPercent sets the value of InstanceUploadProgressPercent.
func (s *PatientStudy) SetInstanceUploadProgressPercent(val OptInt) {
	s.InstanceUploadProgressPercent = val
}

// SetDicomPatientTags sets the value of DicomPatientTags.
func (s *PatientStudy) SetDicomPatientTags(val OptDicomPatientTags) {
	s.DicomPatientTags = val
}

// SetDicomStudyTags sets the value of DicomStudyTags.
func (s *PatientStudy) SetDicomStudyTags(val OptDicomStudyTags) {
	s.DicomStudyTags = val
}

// SetSeries sets the value of Series.
func (s *PatientStudy) SetSeries(val []DicomSeries) {
	s.Series = val
}

// SetReports sets the value of Reports.
func (s *PatientStudy) SetReports(val OptNilStudyReportArray) {
	s.Reports = val
}

// SetHasReport sets the value of HasReport.
func (s *PatientStudy) SetHasReport(val OptBool) {
	s.HasReport = val
}

// Info on the permission group associated with a study.
// Ref: #/components/schemas/permission_group
type PermissionGroup struct {
	GroupID   OptInt64  `json:"group_id"`
	GroupName OptString `json:"groupName"`
}

// GetGroupID returns the value of GroupID.
func (s *PermissionGroup) GetGroupID() OptInt64 {
	return s.GroupID
}

// GetGroupName returns the value of GroupName.
func (s *PermissionGroup) GetGroupName() OptString {
	return s.GroupName
}

// SetGroupID sets the value of GroupID.
func (s *PermissionGroup) SetGroupID(val OptInt64) {
	s.GroupID = val
}

// SetGroupName sets the value of GroupName.
func (s *PermissionGroup) SetGroupName(val OptString) {
	s.GroupName = val
}

type PhysicianPatientStudies []PhysicianPatientStudy

func (*PhysicianPatientStudies) getV1PhysicianStudiesRes() {}

// Merged schema.
// Ref: #/components/schemas/physician_patient_study
type PhysicianPatientStudy struct {
	UUID                          OptString                  `json:"uuid"`
	PatientID                     OptString                  `json:"patient_id"`
	OrganizationID                OptInt64                   `json:"organization_id"`
	ActivatedTimestamp            OptDateTime                `json:"activated_timestamp"`
	AvailabilityStatus            OptString                  `json:"availability_status"`
	OrderID                       OptString                  `json:"order_id"`
	TransferID                    OptString                  `json:"transfer_id"`
	InstanceUploadProgressPercent OptInt                     `json:"instance_upload_progress_percent"`
	DicomPatientTags              OptDicomPatientTags        `json:"dicom_patient_tags"`
	DicomStudyTags                OptDicomStudyTags          `json:"dicom_study_tags"`
	Series                        []DicomSeries              `json:"series"`
	Reports                       OptNilStudyReportArray     `json:"reports"`
	HasReport                     OptBool                    `json:"has_report"`
	PhysicianAccountID            OptString                  `json:"physician_account_id"`
	PermissionGroups              OptNilPermissionGroupArray `json:"permission_groups"`
}

// GetUUID returns the value of UUID.
func (s *PhysicianPatientStudy) GetUUID() OptString {
	return s.UUID
}

// GetPatientID returns the value of PatientID.
func (s *PhysicianPatientStudy) GetPatientID() OptString {
	return s.PatientID
}

// GetOrganizationID returns the value of OrganizationID.
func (s *PhysicianPatientStudy) GetOrganizationID() OptInt64 {
	return s.OrganizationID
}

// GetActivatedTimestamp returns the value of ActivatedTimestamp.
func (s *PhysicianPatientStudy) GetActivatedTimestamp() OptDateTime {
	return s.ActivatedTimestamp
}

// GetAvailabilityStatus returns the value of AvailabilityStatus.
func (s *PhysicianPatientStudy) GetAvailabilityStatus() OptString {
	return s.AvailabilityStatus
}

// GetOrderID returns the value of OrderID.
func (s *PhysicianPatientStudy) GetOrderID() OptString {
	return s.OrderID
}

// GetTransferID returns the value of TransferID.
func (s *PhysicianPatientStudy) GetTransferID() OptString {
	return s.TransferID
}

// GetInstanceUploadProgressPercent returns the value of InstanceUploadProgressPercent.
func (s *PhysicianPatientStudy) GetInstanceUploadProgressPercent() OptInt {
	return s.InstanceUploadProgressPercent
}

// GetDicomPatientTags returns the value of DicomPatientTags.
func (s *PhysicianPatientStudy) GetDicomPatientTags() OptDicomPatientTags {
	return s.DicomPatientTags
}

// GetDicomStudyTags returns the value of DicomStudyTags.
func (s *PhysicianPatientStudy) GetDicomStudyTags() OptDicomStudyTags {
	return s.DicomStudyTags
}

// GetSeries returns the value of Series.
func (s *PhysicianPatientStudy) GetSeries() []DicomSeries {
	return s.Series
}

// GetReports returns the value of Reports.
func (s *PhysicianPatientStudy) GetReports() OptNilStudyReportArray {
	return s.Reports
}

// GetHasReport returns the value of HasReport.
func (s *PhysicianPatientStudy) GetHasReport() OptBool {
	return s.HasReport
}

// GetPhysicianAccountID returns the value of PhysicianAccountID.
func (s *PhysicianPatientStudy) GetPhysicianAccountID() OptString {
	return s.PhysicianAccountID
}

// GetPermissionGroups returns the value of PermissionGroups.
func (s *PhysicianPatientStudy) GetPermissionGroups() OptNilPermissionGroupArray {
	return s.PermissionGroups
}

// SetUUID sets the value of UUID.
func (s *PhysicianPatientStudy) SetUUID(val OptString) {
	s.UUID = val
}

// SetPatientID sets the value of PatientID.
func (s *PhysicianPatientStudy) SetPatientID(val OptString) {
	s.PatientID = val
}

// SetOrganizationID sets the value of OrganizationID.
func (s *PhysicianPatientStudy) SetOrganizationID(val OptInt64) {
	s.OrganizationID = val
}

// SetActivatedTimestamp sets the value of ActivatedTimestamp.
func (s *PhysicianPatientStudy) SetActivatedTimestamp(val OptDateTime) {
	s.ActivatedTimestamp = val
}

// SetAvailabilityStatus sets the value of AvailabilityStatus.
func (s *PhysicianPatientStudy) SetAvailabilityStatus(val OptString) {
	s.AvailabilityStatus = val
}

// SetOrderID sets the value of OrderID.
func (s *PhysicianPatientStudy) SetOrderID(val OptString) {
	s.OrderID = val
}

// SetTransferID sets the value of TransferID.
func (s *PhysicianPatientStudy) SetTransferID(val OptString) {
	s.TransferID = val
}

// SetInstanceUploadProgressPercent sets the value of InstanceUploadProgressPercent.
func (s *PhysicianPatientStudy) SetInstanceUploadProgressPercent(val OptInt) {
	s.InstanceUploadProgressPercent = val
}

// SetDicomPatientTags sets the value of DicomPatientTags.
func (s *PhysicianPatientStudy) SetDicomPatientTags(val OptDicomPatientTags) {
	s.DicomPatientTags = val
}

// SetDicomStudyTags sets the value of DicomStudyTags.
func (s *PhysicianPatientStudy) SetDicomStudyTags(val OptDicomStudyTags) {
	s.DicomStudyTags = val
}

// SetSeries sets the value of Series.
func (s *PhysicianPatientStudy) SetSeries(val []DicomSeries) {
	s.Series = val
}

// SetReports sets the value of Reports.
func (s *PhysicianPatientStudy) SetReports(val OptNilStudyReportArray) {
	s.Reports = val
}

// SetHasReport sets the value of HasReport.
func (s *PhysicianPatientStudy) SetHasReport(val OptBool) {
	s.HasReport = val
}

// SetPhysicianAccountID sets the value of PhysicianAccountID.
func (s *PhysicianPatientStudy) SetPhysicianAccountID(val OptString) {
	s.PhysicianAccountID = val
}

// SetPermissionGroups sets the value of PermissionGroups.
func (s *PhysicianPatientStudy) SetPermissionGroups(val OptNilPermissionGroupArray) {
	s.PermissionGroups = val
}

// PostV1BusinessRulesetsEvaluateBadRequest is response for PostV1BusinessRulesetsEvaluate operation.
type PostV1BusinessRulesetsEvaluateBadRequest struct{}

func (*PostV1BusinessRulesetsEvaluateBadRequest) postV1BusinessRulesetsEvaluateRes() {}

// Ref: #/components/schemas/PostV1BusinessRulesetsEvaluateErrorResponse
type PostV1BusinessRulesetsEvaluateErrorResponse struct {
	Error string `json:"error"`
}

// GetError returns the value of Error.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) GetError() string {
	return s.Error
}

// SetError sets the value of Error.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) SetError(val string) {
	s.Error = val
}

func (*PostV1BusinessRulesetsEvaluateErrorResponse) postV1BusinessRulesetsEvaluateRes() {}

type PostV1BusinessRulesetsEvaluateReq struct {
	Ruleset   Ruleset  `json:"ruleset"`
	DicomTags AnyValue `json:"dicom_tags"`
}

// GetRuleset returns the value of Ruleset.
func (s *PostV1BusinessRulesetsEvaluateReq) GetRuleset() Ruleset {
	return s.Ruleset
}

// GetDicomTags returns the value of DicomTags.
func (s *PostV1BusinessRulesetsEvaluateReq) GetDicomTags() AnyValue {
	return s.DicomTags
}

// SetRuleset sets the value of Ruleset.
func (s *PostV1BusinessRulesetsEvaluateReq) SetRuleset(val Ruleset) {
	s.Ruleset = val
}

// SetDicomTags sets the value of DicomTags.
func (s *PostV1BusinessRulesetsEvaluateReq) SetDicomTags(val AnyValue) {
	s.DicomTags = val
}

// Ref: #/components/schemas/PostV1BusinessRulesetsEvaluateSuccessResponse
type PostV1BusinessRulesetsEvaluateSuccessResponse struct {
	Result              PostV1BusinessRulesetsEvaluateSuccessResponseResult `json:"result"`
	Query               string                                              `json:"query"`
	BusinessRuleResults []BusinessRuleResult                                `json:"business_rule_results"`
	Error               OptString                                           `json:"error"`
}

// GetResult returns the value of Result.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) GetResult() PostV1BusinessRulesetsEvaluateSuccessResponseResult {
	return s.Result
}

// GetQuery returns the value of Query.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) GetQuery() string {
	return s.Query
}

// GetBusinessRuleResults returns the value of BusinessRuleResults.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) GetBusinessRuleResults() []BusinessRuleResult {
	return s.BusinessRuleResults
}

// GetError returns the value of Error.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) GetError() OptString {
	return s.Error
}

// SetResult sets the value of Result.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) SetResult(val PostV1BusinessRulesetsEvaluateSuccessResponseResult) {
	s.Result = val
}

// SetQuery sets the value of Query.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) SetQuery(val string) {
	s.Query = val
}

// SetBusinessRuleResults sets the value of BusinessRuleResults.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) SetBusinessRuleResults(val []BusinessRuleResult) {
	s.BusinessRuleResults = val
}

// SetError sets the value of Error.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) SetError(val OptString) {
	s.Error = val
}

func (*PostV1BusinessRulesetsEvaluateSuccessResponse) postV1BusinessRulesetsEvaluateRes() {}

type PostV1BusinessRulesetsEvaluateSuccessResponseResult string

const (
	PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked PostV1BusinessRulesetsEvaluateSuccessResponseResult = "blocked"
	PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed PostV1BusinessRulesetsEvaluateSuccessResponseResult = "allowed"
)

// AllValues returns all PostV1BusinessRulesetsEvaluateSuccessResponseResult values.
func (PostV1BusinessRulesetsEvaluateSuccessResponseResult) AllValues() []PostV1BusinessRulesetsEvaluateSuccessResponseResult {
	return []PostV1BusinessRulesetsEvaluateSuccessResponseResult{
		PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked,
		PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s PostV1BusinessRulesetsEvaluateSuccessResponseResult) MarshalText() ([]byte, error) {
	switch s {
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked:
		return []byte(s), nil
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponseResult) UnmarshalText(data []byte) error {
	switch PostV1BusinessRulesetsEvaluateSuccessResponseResult(data) {
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked:
		*s = PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked
		return nil
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed:
		*s = PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// PostV1BusinessRulesetsEvaluateUnauthorized is response for PostV1BusinessRulesetsEvaluate operation.
type PostV1BusinessRulesetsEvaluateUnauthorized struct{}

func (*PostV1BusinessRulesetsEvaluateUnauthorized) postV1BusinessRulesetsEvaluateRes() {}

// PostV1MeddreamValidateBadRequest is response for PostV1MeddreamValidate operation.
type PostV1MeddreamValidateBadRequest struct{}

func (*PostV1MeddreamValidateBadRequest) postV1MeddreamValidateRes() {}

// PostV1MeddreamValidateUnauthorized is response for PostV1MeddreamValidate operation.
type PostV1MeddreamValidateUnauthorized struct{}

func (*PostV1MeddreamValidateUnauthorized) postV1MeddreamValidateRes() {}

// Ref: #/components/schemas/ProviderShare
type ProviderShare struct {
	ID string `json:"id"`
	// Raw Code without the dashes.
	AccessCode string `json:"access_code"`
	SendingOrg string `json:"sending_org"`
	// Fax Number or Email.
	Recipient OptString `json:"recipient"`
	NumExams  OptString `json:"num_exams"`
	// ISO8601 Formatted.
	Dob               time.Time           `json:"dob"`
	ExpiresAt         time.Time           `json:"expires_at"`
	ExtendedExpiresAt OptDateTime         `json:"extended_expires_at"`
	Method            ProviderShareMethod `json:"method"`
}

// GetID returns the value of ID.
func (s *ProviderShare) GetID() string {
	return s.ID
}

// GetAccessCode returns the value of AccessCode.
func (s *ProviderShare) GetAccessCode() string {
	return s.AccessCode
}

// GetSendingOrg returns the value of SendingOrg.
func (s *ProviderShare) GetSendingOrg() string {
	return s.SendingOrg
}

// GetRecipient returns the value of Recipient.
func (s *ProviderShare) GetRecipient() OptString {
	return s.Recipient
}

// GetNumExams returns the value of NumExams.
func (s *ProviderShare) GetNumExams() OptString {
	return s.NumExams
}

// GetDob returns the value of Dob.
func (s *ProviderShare) GetDob() time.Time {
	return s.Dob
}

// GetExpiresAt returns the value of ExpiresAt.
func (s *ProviderShare) GetExpiresAt() time.Time {
	return s.ExpiresAt
}

// GetExtendedExpiresAt returns the value of ExtendedExpiresAt.
func (s *ProviderShare) GetExtendedExpiresAt() OptDateTime {
	return s.ExtendedExpiresAt
}

// GetMethod returns the value of Method.
func (s *ProviderShare) GetMethod() ProviderShareMethod {
	return s.Method
}

// SetID sets the value of ID.
func (s *ProviderShare) SetID(val string) {
	s.ID = val
}

// SetAccessCode sets the value of AccessCode.
func (s *ProviderShare) SetAccessCode(val string) {
	s.AccessCode = val
}

// SetSendingOrg sets the value of SendingOrg.
func (s *ProviderShare) SetSendingOrg(val string) {
	s.SendingOrg = val
}

// SetRecipient sets the value of Recipient.
func (s *ProviderShare) SetRecipient(val OptString) {
	s.Recipient = val
}

// SetNumExams sets the value of NumExams.
func (s *ProviderShare) SetNumExams(val OptString) {
	s.NumExams = val
}

// SetDob sets the value of Dob.
func (s *ProviderShare) SetDob(val time.Time) {
	s.Dob = val
}

// SetExpiresAt sets the value of ExpiresAt.
func (s *ProviderShare) SetExpiresAt(val time.Time) {
	s.ExpiresAt = val
}

// SetExtendedExpiresAt sets the value of ExtendedExpiresAt.
func (s *ProviderShare) SetExtendedExpiresAt(val OptDateTime) {
	s.ExtendedExpiresAt = val
}

// SetMethod sets the value of Method.
func (s *ProviderShare) SetMethod(val ProviderShareMethod) {
	s.Method = val
}

type ProviderShareMethod string

const (
	ProviderShareMethodEMAIL    ProviderShareMethod = "EMAIL"
	ProviderShareMethodFAX      ProviderShareMethod = "FAX"
	ProviderShareMethodPRINT    ProviderShareMethod = "PRINT"
	ProviderShareMethodDOWNLOAD ProviderShareMethod = "DOWNLOAD"
)

// AllValues returns all ProviderShareMethod values.
func (ProviderShareMethod) AllValues() []ProviderShareMethod {
	return []ProviderShareMethod{
		ProviderShareMethodEMAIL,
		ProviderShareMethodFAX,
		ProviderShareMethodPRINT,
		ProviderShareMethodDOWNLOAD,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s ProviderShareMethod) MarshalText() ([]byte, error) {
	switch s {
	case ProviderShareMethodEMAIL:
		return []byte(s), nil
	case ProviderShareMethodFAX:
		return []byte(s), nil
	case ProviderShareMethodPRINT:
		return []byte(s), nil
	case ProviderShareMethodDOWNLOAD:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *ProviderShareMethod) UnmarshalText(data []byte) error {
	switch ProviderShareMethod(data) {
	case ProviderShareMethodEMAIL:
		*s = ProviderShareMethodEMAIL
		return nil
	case ProviderShareMethodFAX:
		*s = ProviderShareMethodFAX
		return nil
	case ProviderShareMethodPRINT:
		*s = ProviderShareMethodPRINT
		return nil
	case ProviderShareMethodDOWNLOAD:
		*s = ProviderShareMethodDOWNLOAD
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Ref: #/components/schemas/ProviderShareSearch
type ProviderShareSearch struct {
	// Raw Code without the dashes.
	AccessCode OptString `json:"access_code"`
	// ISO8601 formatted.
	Dob OptDate `json:"dob"`
}

// GetAccessCode returns the value of AccessCode.
func (s *ProviderShareSearch) GetAccessCode() OptString {
	return s.AccessCode
}

// GetDob returns the value of Dob.
func (s *ProviderShareSearch) GetDob() OptDate {
	return s.Dob
}

// SetAccessCode sets the value of AccessCode.
func (s *ProviderShareSearch) SetAccessCode(val OptString) {
	s.AccessCode = val
}

// SetDob sets the value of Dob.
func (s *ProviderShareSearch) SetDob(val OptDate) {
	s.Dob = val
}

// Ref: #/components/schemas/ProviderUploadOverview
type ProviderUploadOverview struct {
	ProviderID              int64                  `json:"provider_id"`
	ProviderName            string                 `json:"provider_name"`
	ProviderUploadOverviews []ProviderUploadStatus `json:"provider_upload_overviews"`
}

// GetProviderID returns the value of ProviderID.
func (s *ProviderUploadOverview) GetProviderID() int64 {
	return s.ProviderID
}

// GetProviderName returns the value of ProviderName.
func (s *ProviderUploadOverview) GetProviderName() string {
	return s.ProviderName
}

// GetProviderUploadOverviews returns the value of ProviderUploadOverviews.
func (s *ProviderUploadOverview) GetProviderUploadOverviews() []ProviderUploadStatus {
	return s.ProviderUploadOverviews
}

// SetProviderID sets the value of ProviderID.
func (s *ProviderUploadOverview) SetProviderID(val int64) {
	s.ProviderID = val
}

// SetProviderName sets the value of ProviderName.
func (s *ProviderUploadOverview) SetProviderName(val string) {
	s.ProviderName = val
}

// SetProviderUploadOverviews sets the value of ProviderUploadOverviews.
func (s *ProviderUploadOverview) SetProviderUploadOverviews(val []ProviderUploadStatus) {
	s.ProviderUploadOverviews = val
}

// Ref: #/components/schemas/ProviderUploadStatus
type ProviderUploadStatus struct {
	TimeBucket                    time.Time `json:"time_bucket"`
	AverageInstanceUploadProgress int64     `json:"average_instance_upload_progress"`
	StudyCount                    int64     `json:"study_count"`
	HappyUserCount                int64     `json:"happy_user_count"`
	SadUserCount                  int64     `json:"sad_user_count"`
}

// GetTimeBucket returns the value of TimeBucket.
func (s *ProviderUploadStatus) GetTimeBucket() time.Time {
	return s.TimeBucket
}

// GetAverageInstanceUploadProgress returns the value of AverageInstanceUploadProgress.
func (s *ProviderUploadStatus) GetAverageInstanceUploadProgress() int64 {
	return s.AverageInstanceUploadProgress
}

// GetStudyCount returns the value of StudyCount.
func (s *ProviderUploadStatus) GetStudyCount() int64 {
	return s.StudyCount
}

// GetHappyUserCount returns the value of HappyUserCount.
func (s *ProviderUploadStatus) GetHappyUserCount() int64 {
	return s.HappyUserCount
}

// GetSadUserCount returns the value of SadUserCount.
func (s *ProviderUploadStatus) GetSadUserCount() int64 {
	return s.SadUserCount
}

// SetTimeBucket sets the value of TimeBucket.
func (s *ProviderUploadStatus) SetTimeBucket(val time.Time) {
	s.TimeBucket = val
}

// SetAverageInstanceUploadProgress sets the value of AverageInstanceUploadProgress.
func (s *ProviderUploadStatus) SetAverageInstanceUploadProgress(val int64) {
	s.AverageInstanceUploadProgress = val
}

// SetStudyCount sets the value of StudyCount.
func (s *ProviderUploadStatus) SetStudyCount(val int64) {
	s.StudyCount = val
}

// SetHappyUserCount sets the value of HappyUserCount.
func (s *ProviderUploadStatus) SetHappyUserCount(val int64) {
	s.HappyUserCount = val
}

// SetSadUserCount sets the value of SadUserCount.
func (s *ProviderUploadStatus) SetSadUserCount(val int64) {
	s.SadUserCount = val
}

// PutV0SharesShareIdExtendBadRequest is response for PutV0SharesShareIdExtend operation.
type PutV0SharesShareIdExtendBadRequest struct{}

func (*PutV0SharesShareIdExtendBadRequest) putV0SharesShareIdExtendRes() {}

// PutV0SharesShareIdExtendInternalServerError is response for PutV0SharesShareIdExtend operation.
type PutV0SharesShareIdExtendInternalServerError struct{}

func (*PutV0SharesShareIdExtendInternalServerError) putV0SharesShareIdExtendRes() {}

// PutV0SharesShareIdExtendOK is response for PutV0SharesShareIdExtend operation.
type PutV0SharesShareIdExtendOK struct{}

func (*PutV0SharesShareIdExtendOK) putV0SharesShareIdExtendRes() {}

// Ref: #/components/schemas/Report
type Report struct {
	ID   OptString     `json:"id"`
	Type OptReportType `json:"type"`
	Date OptString     `json:"date"`
}

// GetID returns the value of ID.
func (s *Report) GetID() OptString {
	return s.ID
}

// GetType returns the value of Type.
func (s *Report) GetType() OptReportType {
	return s.Type
}

// GetDate returns the value of Date.
func (s *Report) GetDate() OptString {
	return s.Date
}

// SetID sets the value of ID.
func (s *Report) SetID(val OptString) {
	s.ID = val
}

// SetType sets the value of Type.
func (s *Report) SetType(val OptReportType) {
	s.Type = val
}

// SetDate sets the value of Date.
func (s *Report) SetDate(val OptString) {
	s.Date = val
}

type ReportType string

const (
	ReportTypeER       ReportType = "ER"
	ReportTypeORIGINAL ReportType = "ORIGINAL"
)

// AllValues returns all ReportType values.
func (ReportType) AllValues() []ReportType {
	return []ReportType{
		ReportTypeER,
		ReportTypeORIGINAL,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s ReportType) MarshalText() ([]byte, error) {
	switch s {
	case ReportTypeER:
		return []byte(s), nil
	case ReportTypeORIGINAL:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *ReportType) UnmarshalText(data []byte) error {
	switch ReportType(data) {
	case ReportTypeER:
		*s = ReportTypeER
		return nil
	case ReportTypeORIGINAL:
		*s = ReportTypeORIGINAL
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Ref: #/components/schemas/Rule
type Rule struct {
	// Logical operator to combine rules (AND/OR).
	Logic OptRuleLogic `json:"logic"`
	// Nested rules.
	Rules []Rule `json:"rules"`
	// DICOM tag, e.g. (0008,0080) Instituion Name.
	Tag OptString `json:"tag"`
	// DICOM tag name for tag hex code auto populated from the endpoint.
	TagName OptString `json:"tagName"`
	// Human readable description of the intent of the rule.
	Description OptString `json:"description"`
	// SQL comparison operator to use for the where condition.
	Operator OptRuleOperator `json:"operator"`
	// Value for the where condition following rules for MySQL pattern matching.
	Value OptRuleValue `json:"value"`
}

// GetLogic returns the value of Logic.
func (s *Rule) GetLogic() OptRuleLogic {
	return s.Logic
}

// GetRules returns the value of Rules.
func (s *Rule) GetRules() []Rule {
	return s.Rules
}

// GetTag returns the value of Tag.
func (s *Rule) GetTag() OptString {
	return s.Tag
}

// GetTagName returns the value of TagName.
func (s *Rule) GetTagName() OptString {
	return s.TagName
}

// GetDescription returns the value of Description.
func (s *Rule) GetDescription() OptString {
	return s.Description
}

// GetOperator returns the value of Operator.
func (s *Rule) GetOperator() OptRuleOperator {
	return s.Operator
}

// GetValue returns the value of Value.
func (s *Rule) GetValue() OptRuleValue {
	return s.Value
}

// SetLogic sets the value of Logic.
func (s *Rule) SetLogic(val OptRuleLogic) {
	s.Logic = val
}

// SetRules sets the value of Rules.
func (s *Rule) SetRules(val []Rule) {
	s.Rules = val
}

// SetTag sets the value of Tag.
func (s *Rule) SetTag(val OptString) {
	s.Tag = val
}

// SetTagName sets the value of TagName.
func (s *Rule) SetTagName(val OptString) {
	s.TagName = val
}

// SetDescription sets the value of Description.
func (s *Rule) SetDescription(val OptString) {
	s.Description = val
}

// SetOperator sets the value of Operator.
func (s *Rule) SetOperator(val OptRuleOperator) {
	s.Operator = val
}

// SetValue sets the value of Value.
func (s *Rule) SetValue(val OptRuleValue) {
	s.Value = val
}

// Logical operator to combine rules (AND/OR).
type RuleLogic string

const (
	RuleLogicAND RuleLogic = "AND"
	RuleLogicOR  RuleLogic = "OR"
)

// AllValues returns all RuleLogic values.
func (RuleLogic) AllValues() []RuleLogic {
	return []RuleLogic{
		RuleLogicAND,
		RuleLogicOR,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s RuleLogic) MarshalText() ([]byte, error) {
	switch s {
	case RuleLogicAND:
		return []byte(s), nil
	case RuleLogicOR:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *RuleLogic) UnmarshalText(data []byte) error {
	switch RuleLogic(data) {
	case RuleLogicAND:
		*s = RuleLogicAND
		return nil
	case RuleLogicOR:
		*s = RuleLogicOR
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// SQL comparison operator to use for the where condition.
type RuleOperator string

const (
	RuleOperator_0  RuleOperator = "="
	RuleOperator_1  RuleOperator = "!="
	RuleOperator_2  RuleOperator = "<>"
	RuleOperator_3  RuleOperator = ">"
	RuleOperator_4  RuleOperator = "<"
	RuleOperator_5  RuleOperator = ">="
	RuleOperator_6  RuleOperator = "<="
	RuleOperator_7  RuleOperator = "->"
	RuleOperator_8  RuleOperator = "->>"
	RuleOperator_9  RuleOperator = "IN"
	RuleOperator_10 RuleOperator = "NOT IN"
	RuleOperator_11 RuleOperator = "LIKE"
	RuleOperator_12 RuleOperator = "NOT LIKE"
	RuleOperator_13 RuleOperator = "IS"
	RuleOperator_14 RuleOperator = "IS NOT"
	RuleOperator_15 RuleOperator = "REGEXP"
	RuleOperator_16 RuleOperator = "NOT REGEXP"
	RuleOperator_17 RuleOperator = "RLIKE"
)

// AllValues returns all RuleOperator values.
func (RuleOperator) AllValues() []RuleOperator {
	return []RuleOperator{
		RuleOperator_0,
		RuleOperator_1,
		RuleOperator_2,
		RuleOperator_3,
		RuleOperator_4,
		RuleOperator_5,
		RuleOperator_6,
		RuleOperator_7,
		RuleOperator_8,
		RuleOperator_9,
		RuleOperator_10,
		RuleOperator_11,
		RuleOperator_12,
		RuleOperator_13,
		RuleOperator_14,
		RuleOperator_15,
		RuleOperator_16,
		RuleOperator_17,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s RuleOperator) MarshalText() ([]byte, error) {
	switch s {
	case RuleOperator_0:
		return []byte(s), nil
	case RuleOperator_1:
		return []byte(s), nil
	case RuleOperator_2:
		return []byte(s), nil
	case RuleOperator_3:
		return []byte(s), nil
	case RuleOperator_4:
		return []byte(s), nil
	case RuleOperator_5:
		return []byte(s), nil
	case RuleOperator_6:
		return []byte(s), nil
	case RuleOperator_7:
		return []byte(s), nil
	case RuleOperator_8:
		return []byte(s), nil
	case RuleOperator_9:
		return []byte(s), nil
	case RuleOperator_10:
		return []byte(s), nil
	case RuleOperator_11:
		return []byte(s), nil
	case RuleOperator_12:
		return []byte(s), nil
	case RuleOperator_13:
		return []byte(s), nil
	case RuleOperator_14:
		return []byte(s), nil
	case RuleOperator_15:
		return []byte(s), nil
	case RuleOperator_16:
		return []byte(s), nil
	case RuleOperator_17:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *RuleOperator) UnmarshalText(data []byte) error {
	switch RuleOperator(data) {
	case RuleOperator_0:
		*s = RuleOperator_0
		return nil
	case RuleOperator_1:
		*s = RuleOperator_1
		return nil
	case RuleOperator_2:
		*s = RuleOperator_2
		return nil
	case RuleOperator_3:
		*s = RuleOperator_3
		return nil
	case RuleOperator_4:
		*s = RuleOperator_4
		return nil
	case RuleOperator_5:
		*s = RuleOperator_5
		return nil
	case RuleOperator_6:
		*s = RuleOperator_6
		return nil
	case RuleOperator_7:
		*s = RuleOperator_7
		return nil
	case RuleOperator_8:
		*s = RuleOperator_8
		return nil
	case RuleOperator_9:
		*s = RuleOperator_9
		return nil
	case RuleOperator_10:
		*s = RuleOperator_10
		return nil
	case RuleOperator_11:
		*s = RuleOperator_11
		return nil
	case RuleOperator_12:
		*s = RuleOperator_12
		return nil
	case RuleOperator_13:
		*s = RuleOperator_13
		return nil
	case RuleOperator_14:
		*s = RuleOperator_14
		return nil
	case RuleOperator_15:
		*s = RuleOperator_15
		return nil
	case RuleOperator_16:
		*s = RuleOperator_16
		return nil
	case RuleOperator_17:
		*s = RuleOperator_17
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Value for the where condition following rules for MySQL pattern matching.
// RuleValue represents sum type.
type RuleValue struct {
	Type    RuleValueType // switch on this field
	String  string
	Float64 float64
}

// RuleValueType is oneOf type of RuleValue.
type RuleValueType string

// Possible values for RuleValueType.
const (
	StringRuleValue  RuleValueType = "string"
	Float64RuleValue RuleValueType = "float64"
)

// IsString reports whether RuleValue is string.
func (s RuleValue) IsString() bool { return s.Type == StringRuleValue }

// IsFloat64 reports whether RuleValue is float64.
func (s RuleValue) IsFloat64() bool { return s.Type == Float64RuleValue }

// SetString sets RuleValue to string.
func (s *RuleValue) SetString(v string) {
	s.Type = StringRuleValue
	s.String = v
}

// GetString returns string and true boolean if RuleValue is string.
func (s RuleValue) GetString() (v string, ok bool) {
	if !s.IsString() {
		return v, false
	}
	return s.String, true
}

// NewStringRuleValue returns new RuleValue from string.
func NewStringRuleValue(v string) RuleValue {
	var s RuleValue
	s.SetString(v)
	return s
}

// SetFloat64 sets RuleValue to float64.
func (s *RuleValue) SetFloat64(v float64) {
	s.Type = Float64RuleValue
	s.Float64 = v
}

// GetFloat64 returns float64 and true boolean if RuleValue is float64.
func (s RuleValue) GetFloat64() (v float64, ok bool) {
	if !s.IsFloat64() {
		return v, false
	}
	return s.Float64, true
}

// NewFloat64RuleValue returns new RuleValue from float64.
func NewFloat64RuleValue(v float64) RuleValue {
	var s RuleValue
	s.SetFloat64(v)
	return s
}

// Ref: #/components/schemas/Ruleset
type Ruleset struct {
	Block OptBlock `json:"block"`
}

// GetBlock returns the value of Block.
func (s *Ruleset) GetBlock() OptBlock {
	return s.Block
}

// SetBlock sets the value of Block.
func (s *Ruleset) SetBlock(val OptBlock) {
	s.Block = val
}

// Ref: #/components/schemas/Series
type Series struct {
	SeriesUID   OptString  `json:"series_uid"`
	Description OptString  `json:"description"`
	NumImages   OptFloat64 `json:"num_images"`
}

// GetSeriesUID returns the value of SeriesUID.
func (s *Series) GetSeriesUID() OptString {
	return s.SeriesUID
}

// GetDescription returns the value of Description.
func (s *Series) GetDescription() OptString {
	return s.Description
}

// GetNumImages returns the value of NumImages.
func (s *Series) GetNumImages() OptFloat64 {
	return s.NumImages
}

// SetSeriesUID sets the value of SeriesUID.
func (s *Series) SetSeriesUID(val OptString) {
	s.SeriesUID = val
}

// SetDescription sets the value of Description.
func (s *Series) SetDescription(val OptString) {
	s.Description = val
}

// SetNumImages sets the value of NumImages.
func (s *Series) SetNumImages(val OptFloat64) {
	s.NumImages = val
}

// Permission object for a study. Contains ids of users that have access to a given study. Ids are
// grouped by user type.
// Ref: #/components/schemas/study_access_permissions
type StudyAccessPermissions struct {
	// A list of KSUIDs for physicians that have access to a study.
	Physicians []string `json:"physicians"`
	// A list of permissions for patients that have access to a study.
	Patients []PatientAccountPermission `json:"patients"`
}

// GetPhysicians returns the value of Physicians.
func (s *StudyAccessPermissions) GetPhysicians() []string {
	return s.Physicians
}

// GetPatients returns the value of Patients.
func (s *StudyAccessPermissions) GetPatients() []PatientAccountPermission {
	return s.Patients
}

// SetPhysicians sets the value of Physicians.
func (s *StudyAccessPermissions) SetPhysicians(val []string) {
	s.Physicians = val
}

// SetPatients sets the value of Patients.
func (s *StudyAccessPermissions) SetPatients(val []PatientAccountPermission) {
	s.Patients = val
}

func (*StudyAccessPermissions) getV1StudiesPermissionsRes() {}

// Ref: #/components/schemas/study_attribution
type StudyAttribution struct {
	// A list of UUIDs for a study.
	Uuids              []string  `json:"uuids"`
	OrderId            OptString `json:"orderId"`
	AvailabilityStatus OptString `json:"availabilityStatus"`
}

// GetUuids returns the value of Uuids.
func (s *StudyAttribution) GetUuids() []string {
	return s.Uuids
}

// GetOrderId returns the value of OrderId.
func (s *StudyAttribution) GetOrderId() OptString {
	return s.OrderId
}

// GetAvailabilityStatus returns the value of AvailabilityStatus.
func (s *StudyAttribution) GetAvailabilityStatus() OptString {
	return s.AvailabilityStatus
}

// SetUuids sets the value of Uuids.
func (s *StudyAttribution) SetUuids(val []string) {
	s.Uuids = val
}

// SetOrderId sets the value of OrderId.
func (s *StudyAttribution) SetOrderId(val OptString) {
	s.OrderId = val
}

// SetAvailabilityStatus sets the value of AvailabilityStatus.
func (s *StudyAttribution) SetAvailabilityStatus(val OptString) {
	s.AvailabilityStatus = val
}

// Study availability status from the perspective of a patient:
// * `locked` - Study is currently locked
// * `fullAccess` - Study is currently fully available
// * `limitedAvailability` - Study is partially available (unlocked with the Basic plan).
// Ref: #/components/schemas/study_availability_status
type StudyAvailabilityStatus string

const (
	StudyAvailabilityStatusLocked              StudyAvailabilityStatus = "locked"
	StudyAvailabilityStatusFullAccess          StudyAvailabilityStatus = "fullAccess"
	StudyAvailabilityStatusLimitedAvailability StudyAvailabilityStatus = "limitedAvailability"
)

// AllValues returns all StudyAvailabilityStatus values.
func (StudyAvailabilityStatus) AllValues() []StudyAvailabilityStatus {
	return []StudyAvailabilityStatus{
		StudyAvailabilityStatusLocked,
		StudyAvailabilityStatusFullAccess,
		StudyAvailabilityStatusLimitedAvailability,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s StudyAvailabilityStatus) MarshalText() ([]byte, error) {
	switch s {
	case StudyAvailabilityStatusLocked:
		return []byte(s), nil
	case StudyAvailabilityStatusFullAccess:
		return []byte(s), nil
	case StudyAvailabilityStatusLimitedAvailability:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *StudyAvailabilityStatus) UnmarshalText(data []byte) error {
	switch StudyAvailabilityStatus(data) {
	case StudyAvailabilityStatusLocked:
		*s = StudyAvailabilityStatusLocked
		return nil
	case StudyAvailabilityStatusFullAccess:
		*s = StudyAvailabilityStatusFullAccess
		return nil
	case StudyAvailabilityStatusLimitedAvailability:
		*s = StudyAvailabilityStatusLimitedAvailability
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Uniquely identifies a study uploaded via record streaming. ID consists of DICOM UID of study and
// int64 legacy provider id (also called org id) of the provider who uploaded the study.
// Ref: #/components/schemas/study_identifier
type StudyIdentifier struct {
	// The DICOM identifier of the study.
	StudyUID string `json:"study_uid"`
	// The int64 identifier of a provider that uploaded a study (also known as legacy provider id or org
	// id).
	ProviderID int64 `json:"provider_id"`
}

// GetStudyUID returns the value of StudyUID.
func (s *StudyIdentifier) GetStudyUID() string {
	return s.StudyUID
}

// GetProviderID returns the value of ProviderID.
func (s *StudyIdentifier) GetProviderID() int64 {
	return s.ProviderID
}

// SetStudyUID sets the value of StudyUID.
func (s *StudyIdentifier) SetStudyUID(val string) {
	s.StudyUID = val
}

// SetProviderID sets the value of ProviderID.
func (s *StudyIdentifier) SetProviderID(val int64) {
	s.ProviderID = val
}

// Ref: #/components/schemas/study_report
type StudyReport struct {
	UUID            OptString `json:"uuid"`
	FileSizeBytes   OptInt64  `json:"file_size_bytes"`
	EncapsulatedPdf OptBool   `json:"encapsulated_pdf"`
	Type            OptString `json:"type"`
}

// GetUUID returns the value of UUID.
func (s *StudyReport) GetUUID() OptString {
	return s.UUID
}

// GetFileSizeBytes returns the value of FileSizeBytes.
func (s *StudyReport) GetFileSizeBytes() OptInt64 {
	return s.FileSizeBytes
}

// GetEncapsulatedPdf returns the value of EncapsulatedPdf.
func (s *StudyReport) GetEncapsulatedPdf() OptBool {
	return s.EncapsulatedPdf
}

// GetType returns the value of Type.
func (s *StudyReport) GetType() OptString {
	return s.Type
}

// SetUUID sets the value of UUID.
func (s *StudyReport) SetUUID(val OptString) {
	s.UUID = val
}

// SetFileSizeBytes sets the value of FileSizeBytes.
func (s *StudyReport) SetFileSizeBytes(val OptInt64) {
	s.FileSizeBytes = val
}

// SetEncapsulatedPdf sets the value of EncapsulatedPdf.
func (s *StudyReport) SetEncapsulatedPdf(val OptBool) {
	s.EncapsulatedPdf = val
}

// SetType sets the value of Type.
func (s *StudyReport) SetType(val OptString) {
	s.Type = val
}

// Ref: #/components/schemas/StudyUploadMetadata
type StudyUploadMetadata struct {
	AccountType         OptStudyUploadMetadataAccountType `json:"account_type"`
	AccountID           OptString                         `json:"account_id"`
	ProviderID          OptInt64                          `json:"provider_id"`
	StudyUID            OptString                         `json:"study_uid"`
	InstanceCount       OptInt64                          `json:"instance_count"`
	InstancesUploaded   OptInt64                          `json:"instances_uploaded"`
	CreatedTimestampUtc OptDateTime                       `json:"created_timestamp_utc"`
}

// GetAccountType returns the value of AccountType.
func (s *StudyUploadMetadata) GetAccountType() OptStudyUploadMetadataAccountType {
	return s.AccountType
}

// GetAccountID returns the value of AccountID.
func (s *StudyUploadMetadata) GetAccountID() OptString {
	return s.AccountID
}

// GetProviderID returns the value of ProviderID.
func (s *StudyUploadMetadata) GetProviderID() OptInt64 {
	return s.ProviderID
}

// GetStudyUID returns the value of StudyUID.
func (s *StudyUploadMetadata) GetStudyUID() OptString {
	return s.StudyUID
}

// GetInstanceCount returns the value of InstanceCount.
func (s *StudyUploadMetadata) GetInstanceCount() OptInt64 {
	return s.InstanceCount
}

// GetInstancesUploaded returns the value of InstancesUploaded.
func (s *StudyUploadMetadata) GetInstancesUploaded() OptInt64 {
	return s.InstancesUploaded
}

// GetCreatedTimestampUtc returns the value of CreatedTimestampUtc.
func (s *StudyUploadMetadata) GetCreatedTimestampUtc() OptDateTime {
	return s.CreatedTimestampUtc
}

// SetAccountType sets the value of AccountType.
func (s *StudyUploadMetadata) SetAccountType(val OptStudyUploadMetadataAccountType) {
	s.AccountType = val
}

// SetAccountID sets the value of AccountID.
func (s *StudyUploadMetadata) SetAccountID(val OptString) {
	s.AccountID = val
}

// SetProviderID sets the value of ProviderID.
func (s *StudyUploadMetadata) SetProviderID(val OptInt64) {
	s.ProviderID = val
}

// SetStudyUID sets the value of StudyUID.
func (s *StudyUploadMetadata) SetStudyUID(val OptString) {
	s.StudyUID = val
}

// SetInstanceCount sets the value of InstanceCount.
func (s *StudyUploadMetadata) SetInstanceCount(val OptInt64) {
	s.InstanceCount = val
}

// SetInstancesUploaded sets the value of InstancesUploaded.
func (s *StudyUploadMetadata) SetInstancesUploaded(val OptInt64) {
	s.InstancesUploaded = val
}

// SetCreatedTimestampUtc sets the value of CreatedTimestampUtc.
func (s *StudyUploadMetadata) SetCreatedTimestampUtc(val OptDateTime) {
	s.CreatedTimestampUtc = val
}

type StudyUploadMetadataAccountType string

const (
	StudyUploadMetadataAccountTypePATIENT   StudyUploadMetadataAccountType = "PATIENT"
	StudyUploadMetadataAccountTypePHYSICIAN StudyUploadMetadataAccountType = "PHYSICIAN"
)

// AllValues returns all StudyUploadMetadataAccountType values.
func (StudyUploadMetadataAccountType) AllValues() []StudyUploadMetadataAccountType {
	return []StudyUploadMetadataAccountType{
		StudyUploadMetadataAccountTypePATIENT,
		StudyUploadMetadataAccountTypePHYSICIAN,
	}
}

// MarshalText implements encoding.TextMarshaler.
func (s StudyUploadMetadataAccountType) MarshalText() ([]byte, error) {
	switch s {
	case StudyUploadMetadataAccountTypePATIENT:
		return []byte(s), nil
	case StudyUploadMetadataAccountTypePHYSICIAN:
		return []byte(s), nil
	default:
		return nil, errors.Errorf("invalid value: %q", s)
	}
}

// UnmarshalText implements encoding.TextUnmarshaler.
func (s *StudyUploadMetadataAccountType) UnmarshalText(data []byte) error {
	switch StudyUploadMetadataAccountType(data) {
	case StudyUploadMetadataAccountTypePATIENT:
		*s = StudyUploadMetadataAccountTypePATIENT
		return nil
	case StudyUploadMetadataAccountTypePHYSICIAN:
		*s = StudyUploadMetadataAccountTypePHYSICIAN
		return nil
	default:
		return errors.Errorf("invalid value: %q", data)
	}
}

// Upload status for a study. Contains ids of study (DICOM study UID and ID of uploading provider,
// unique ksuid created for study on upload) and information on the study's upload state (has
// uploaded report, percentage of images uploaded).
// Ref: #/components/schemas/study_upload_status
type StudyUploadStatus struct {
	StudyIdentifier StudyIdentifier `json:"study_identifier"`
	// A unique KSUID associated with the study.
	UUID string `json:"uuid"`
	// Boolean value, true if a report has been successfully uploaded for the study. False if the study
	// has no report OR if no report has been uploaded yet.
	HasReport bool `json:"has_report"`
	// Percentage of instances of the study that have been uploaded.
	ProgressPercent int `json:"progress_percent"`
}

// GetStudyIdentifier returns the value of StudyIdentifier.
func (s *StudyUploadStatus) GetStudyIdentifier() StudyIdentifier {
	return s.StudyIdentifier
}

// GetUUID returns the value of UUID.
func (s *StudyUploadStatus) GetUUID() string {
	return s.UUID
}

// GetHasReport returns the value of HasReport.
func (s *StudyUploadStatus) GetHasReport() bool {
	return s.HasReport
}

// GetProgressPercent returns the value of ProgressPercent.
func (s *StudyUploadStatus) GetProgressPercent() int {
	return s.ProgressPercent
}

// SetStudyIdentifier sets the value of StudyIdentifier.
func (s *StudyUploadStatus) SetStudyIdentifier(val StudyIdentifier) {
	s.StudyIdentifier = val
}

// SetUUID sets the value of UUID.
func (s *StudyUploadStatus) SetUUID(val string) {
	s.UUID = val
}

// SetHasReport sets the value of HasReport.
func (s *StudyUploadStatus) SetHasReport(val bool) {
	s.HasReport = val
}

// SetProgressPercent sets the value of ProgressPercent.
func (s *StudyUploadStatus) SetProgressPercent(val int) {
	s.ProgressPercent = val
}

// Upload status of a study. Contains some study metadata, id and name of provider and upload
// progress percentage (% of instances uploaded for the study).
// Ref: #/components/schemas/upload_status
type UploadStatus struct {
	// Provider id.
	OrgId int64 `json:"orgId"`
	// DICOM UID of study.
	ExamId string `json:"examId"`
	// Ksuid of exam object created during study upload, if available.
	UUID OptNilString `json:"uuid"`
	// Upload percentage of study - determined by how many images have been uploaded compared to total
	// number of images in the study.
	ProgressPercent int `json:"progressPercent"`
	// Modality of the study - determined by the modality of the first series within the study.
	Modality string `json:"modality"`
	// Type of exam - based on modality.
	ExamType string `json:"examType"`
	// Date of exam, in format 'Mon dd, yyyy'.
	ExamDate string `json:"examDate"`
	// Description of study.
	Description string `json:"description"`
	// Name of provider, 'Unknown' if no name is available.
	OrgName string `json:"orgName"`
	// Id of patient within pockethealth patient account that owns this study.
	PatientId               OptNilString               `json:"patientId"`
	StudyAvailabilityStatus OptStudyAvailabilityStatus `json:"study_availability_status"`
}

// GetOrgId returns the value of OrgId.
func (s *UploadStatus) GetOrgId() int64 {
	return s.OrgId
}

// GetExamId returns the value of ExamId.
func (s *UploadStatus) GetExamId() string {
	return s.ExamId
}

// GetUUID returns the value of UUID.
func (s *UploadStatus) GetUUID() OptNilString {
	return s.UUID
}

// GetProgressPercent returns the value of ProgressPercent.
func (s *UploadStatus) GetProgressPercent() int {
	return s.ProgressPercent
}

// GetModality returns the value of Modality.
func (s *UploadStatus) GetModality() string {
	return s.Modality
}

// GetExamType returns the value of ExamType.
func (s *UploadStatus) GetExamType() string {
	return s.ExamType
}

// GetExamDate returns the value of ExamDate.
func (s *UploadStatus) GetExamDate() string {
	return s.ExamDate
}

// GetDescription returns the value of Description.
func (s *UploadStatus) GetDescription() string {
	return s.Description
}

// GetOrgName returns the value of OrgName.
func (s *UploadStatus) GetOrgName() string {
	return s.OrgName
}

// GetPatientId returns the value of PatientId.
func (s *UploadStatus) GetPatientId() OptNilString {
	return s.PatientId
}

// GetStudyAvailabilityStatus returns the value of StudyAvailabilityStatus.
func (s *UploadStatus) GetStudyAvailabilityStatus() OptStudyAvailabilityStatus {
	return s.StudyAvailabilityStatus
}

// SetOrgId sets the value of OrgId.
func (s *UploadStatus) SetOrgId(val int64) {
	s.OrgId = val
}

// SetExamId sets the value of ExamId.
func (s *UploadStatus) SetExamId(val string) {
	s.ExamId = val
}

// SetUUID sets the value of UUID.
func (s *UploadStatus) SetUUID(val OptNilString) {
	s.UUID = val
}

// SetProgressPercent sets the value of ProgressPercent.
func (s *UploadStatus) SetProgressPercent(val int) {
	s.ProgressPercent = val
}

// SetModality sets the value of Modality.
func (s *UploadStatus) SetModality(val string) {
	s.Modality = val
}

// SetExamType sets the value of ExamType.
func (s *UploadStatus) SetExamType(val string) {
	s.ExamType = val
}

// SetExamDate sets the value of ExamDate.
func (s *UploadStatus) SetExamDate(val string) {
	s.ExamDate = val
}

// SetDescription sets the value of Description.
func (s *UploadStatus) SetDescription(val string) {
	s.Description = val
}

// SetOrgName sets the value of OrgName.
func (s *UploadStatus) SetOrgName(val string) {
	s.OrgName = val
}

// SetPatientId sets the value of PatientId.
func (s *UploadStatus) SetPatientId(val OptNilString) {
	s.PatientId = val
}

// SetStudyAvailabilityStatus sets the value of StudyAvailabilityStatus.
func (s *UploadStatus) SetStudyAvailabilityStatus(val OptStudyAvailabilityStatus) {
	s.StudyAvailabilityStatus = val
}

// V1AccountsAccountIDStudiesAttributePostBadRequest is response for V1AccountsAccountIDStudiesAttributePost operation.
type V1AccountsAccountIDStudiesAttributePostBadRequest struct{}

func (*V1AccountsAccountIDStudiesAttributePostBadRequest) v1AccountsAccountIDStudiesAttributePostRes() {
}

// V1AccountsAccountIDStudiesAttributePostInternalServerError is response for V1AccountsAccountIDStudiesAttributePost operation.
type V1AccountsAccountIDStudiesAttributePostInternalServerError struct{}

func (*V1AccountsAccountIDStudiesAttributePostInternalServerError) v1AccountsAccountIDStudiesAttributePostRes() {
}

// V1AccountsAccountIDStudiesAttributePostOK is response for V1AccountsAccountIDStudiesAttributePost operation.
type V1AccountsAccountIDStudiesAttributePostOK struct{}

func (*V1AccountsAccountIDStudiesAttributePostOK) v1AccountsAccountIDStudiesAttributePostRes() {}

// V1AccountsAccountIDStudiesAttributePostUnauthorized is response for V1AccountsAccountIDStudiesAttributePost operation.
type V1AccountsAccountIDStudiesAttributePostUnauthorized struct{}

func (*V1AccountsAccountIDStudiesAttributePostUnauthorized) v1AccountsAccountIDStudiesAttributePostRes() {
}

// V1DicomwebQidoSearchStudyBadRequest is response for V1DicomwebQidoSearchStudy operation.
type V1DicomwebQidoSearchStudyBadRequest struct{}

func (*V1DicomwebQidoSearchStudyBadRequest) v1DicomwebQidoSearchStudyRes() {}

// V1DicomwebQidoSearchStudyInternalServerError is response for V1DicomwebQidoSearchStudy operation.
type V1DicomwebQidoSearchStudyInternalServerError struct{}

func (*V1DicomwebQidoSearchStudyInternalServerError) v1DicomwebQidoSearchStudyRes() {}

// V1DicomwebQidoSearchStudyNotFound is response for V1DicomwebQidoSearchStudy operation.
type V1DicomwebQidoSearchStudyNotFound struct{}

func (*V1DicomwebQidoSearchStudyNotFound) v1DicomwebQidoSearchStudyRes() {}

type V1DicomwebQidoSearchStudyOKApplicationJSON []DicomMetadata

func (*V1DicomwebQidoSearchStudyOKApplicationJSON) v1DicomwebQidoSearchStudyRes() {}

// V1DicomwebQidoSearchStudyUnauthorized is response for V1DicomwebQidoSearchStudy operation.
type V1DicomwebQidoSearchStudyUnauthorized struct{}

func (*V1DicomwebQidoSearchStudyUnauthorized) v1DicomwebQidoSearchStudyRes() {}

// V1DicomwebRetrieveInstanceInternalServerError is response for V1DicomwebRetrieveInstance operation.
type V1DicomwebRetrieveInstanceInternalServerError struct{}

func (*V1DicomwebRetrieveInstanceInternalServerError) v1DicomwebRetrieveInstanceRes() {}

// V1DicomwebRetrieveInstanceNotFound is response for V1DicomwebRetrieveInstance operation.
type V1DicomwebRetrieveInstanceNotFound struct{}

func (*V1DicomwebRetrieveInstanceNotFound) v1DicomwebRetrieveInstanceRes() {}

type V1DicomwebRetrieveInstanceOK struct {
	Data io.Reader
}

// Read reads data from the Data reader.
//
// Kept to satisfy the io.Reader interface.
func (s V1DicomwebRetrieveInstanceOK) Read(p []byte) (n int, err error) {
	if s.Data == nil {
		return 0, io.EOF
	}
	return s.Data.Read(p)
}

func (*V1DicomwebRetrieveInstanceOK) v1DicomwebRetrieveInstanceRes() {}

// V1DicomwebRetrieveInstanceUnauthorized is response for V1DicomwebRetrieveInstance operation.
type V1DicomwebRetrieveInstanceUnauthorized struct{}

func (*V1DicomwebRetrieveInstanceUnauthorized) v1DicomwebRetrieveInstanceRes() {}

// V1DicomwebRetrieveMetadataInternalServerError is response for V1DicomwebRetrieveMetadata operation.
type V1DicomwebRetrieveMetadataInternalServerError struct{}

func (*V1DicomwebRetrieveMetadataInternalServerError) v1DicomwebRetrieveMetadataRes() {}

// V1DicomwebRetrieveMetadataNotFound is response for V1DicomwebRetrieveMetadata operation.
type V1DicomwebRetrieveMetadataNotFound struct{}

func (*V1DicomwebRetrieveMetadataNotFound) v1DicomwebRetrieveMetadataRes() {}

type V1DicomwebRetrieveMetadataOKApplicationJSON []DicomMetadata

func (*V1DicomwebRetrieveMetadataOKApplicationJSON) v1DicomwebRetrieveMetadataRes() {}

// V1DicomwebRetrieveMetadataUnauthorized is response for V1DicomwebRetrieveMetadata operation.
type V1DicomwebRetrieveMetadataUnauthorized struct{}

func (*V1DicomwebRetrieveMetadataUnauthorized) v1DicomwebRetrieveMetadataRes() {}

// V1PatientsAccountIDStudiesActivatePostInternalServerError is response for V1PatientsAccountIDStudiesActivatePost operation.
type V1PatientsAccountIDStudiesActivatePostInternalServerError struct{}

func (*V1PatientsAccountIDStudiesActivatePostInternalServerError) v1PatientsAccountIDStudiesActivatePostRes() {
}

type V1PatientsAccountIDStudiesActivatePostOKApplicationJSON []PatientStudy

func (*V1PatientsAccountIDStudiesActivatePostOKApplicationJSON) v1PatientsAccountIDStudiesActivatePostRes() {
}

// V1PatientsAccountIDStudiesActivatePostUnauthorized is response for V1PatientsAccountIDStudiesActivatePost operation.
type V1PatientsAccountIDStudiesActivatePostUnauthorized struct{}

func (*V1PatientsAccountIDStudiesActivatePostUnauthorized) v1PatientsAccountIDStudiesActivatePostRes() {
}

// V1PatientsAccountIDStudiesGetInternalServerError is response for V1PatientsAccountIDStudiesGet operation.
type V1PatientsAccountIDStudiesGetInternalServerError struct{}

func (*V1PatientsAccountIDStudiesGetInternalServerError) v1PatientsAccountIDStudiesGetRes() {}

type V1PatientsAccountIDStudiesGetOKApplicationJSON []PatientStudy

func (*V1PatientsAccountIDStudiesGetOKApplicationJSON) v1PatientsAccountIDStudiesGetRes() {}

// V1PatientsAccountIDStudiesGetUnauthorized is response for V1PatientsAccountIDStudiesGet operation.
type V1PatientsAccountIDStudiesGetUnauthorized struct{}

func (*V1PatientsAccountIDStudiesGetUnauthorized) v1PatientsAccountIDStudiesGetRes() {}

// V1PatientsAccountIDStudiesMatchGetBadRequest is response for V1PatientsAccountIDStudiesMatchGet operation.
type V1PatientsAccountIDStudiesMatchGetBadRequest struct{}

func (*V1PatientsAccountIDStudiesMatchGetBadRequest) v1PatientsAccountIDStudiesMatchGetRes() {}

// V1PatientsAccountIDStudiesMatchGetInternalServerError is response for V1PatientsAccountIDStudiesMatchGet operation.
type V1PatientsAccountIDStudiesMatchGetInternalServerError struct{}

func (*V1PatientsAccountIDStudiesMatchGetInternalServerError) v1PatientsAccountIDStudiesMatchGetRes() {
}

type V1PatientsAccountIDStudiesMatchGetOKApplicationJSON bool

func (*V1PatientsAccountIDStudiesMatchGetOKApplicationJSON) v1PatientsAccountIDStudiesMatchGetRes() {}

// V1PatientsAccountIDStudiesMatchGetUnauthorized is response for V1PatientsAccountIDStudiesMatchGet operation.
type V1PatientsAccountIDStudiesMatchGetUnauthorized struct{}

func (*V1PatientsAccountIDStudiesMatchGetUnauthorized) v1PatientsAccountIDStudiesMatchGetRes() {}

// V1PhysiciansAccountIDStudiesMatchGetBadRequest is response for V1PhysiciansAccountIDStudiesMatchGet operation.
type V1PhysiciansAccountIDStudiesMatchGetBadRequest struct{}

func (*V1PhysiciansAccountIDStudiesMatchGetBadRequest) v1PhysiciansAccountIDStudiesMatchGetRes() {}

// V1PhysiciansAccountIDStudiesMatchGetInternalServerError is response for V1PhysiciansAccountIDStudiesMatchGet operation.
type V1PhysiciansAccountIDStudiesMatchGetInternalServerError struct{}

func (*V1PhysiciansAccountIDStudiesMatchGetInternalServerError) v1PhysiciansAccountIDStudiesMatchGetRes() {
}

type V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON bool

func (*V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON) v1PhysiciansAccountIDStudiesMatchGetRes() {
}

// V1PhysiciansAccountIDStudiesMatchGetUnauthorized is response for V1PhysiciansAccountIDStudiesMatchGet operation.
type V1PhysiciansAccountIDStudiesMatchGetUnauthorized struct{}

func (*V1PhysiciansAccountIDStudiesMatchGetUnauthorized) v1PhysiciansAccountIDStudiesMatchGetRes() {}
