// Code generated by ogen, DO NOT EDIT.

package recordservice

import (
	"math/bits"
	"strconv"
	"time"

	"github.com/go-faster/errors"
	"github.com/go-faster/jx"

	"github.com/ogen-go/ogen/json"
	"github.com/ogen-go/ogen/validate"
)

// Encode implements json.Marshaler.
func (s *ActivateStudy) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ActivateStudy) encodeFields(e *jx.Encoder) {
	{
		if s.DateOfBirth.Set {
			e.FieldStart("dateOfBirth")
			s.DateOfBirth.Encode(e)
		}
	}
	{
		if s.OrderId.Set {
			e.FieldStart("orderId")
			s.OrderId.Encode(e)
		}
	}
	{
		if s.StudyAvailabilityStatuses.Set {
			e.FieldStart("studyAvailabilityStatuses")
			s.StudyAvailabilityStatuses.Encode(e)
		}
	}
}

var jsonFieldsNameOfActivateStudy = [3]string{
	0: "dateOfBirth",
	1: "orderId",
	2: "studyAvailabilityStatuses",
}

// Decode decodes ActivateStudy from json.
func (s *ActivateStudy) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ActivateStudy to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "dateOfBirth":
			if err := func() error {
				s.DateOfBirth.Reset()
				if err := s.DateOfBirth.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dateOfBirth\"")
			}
		case "orderId":
			if err := func() error {
				s.OrderId.Reset()
				if err := s.OrderId.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"orderId\"")
			}
		case "studyAvailabilityStatuses":
			if err := func() error {
				s.StudyAvailabilityStatuses.Reset()
				if err := s.StudyAvailabilityStatuses.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"studyAvailabilityStatuses\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ActivateStudy")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ActivateStudy) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ActivateStudy) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s ActivateStudyStudyAvailabilityStatuses) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields implements json.Marshaler.
func (s ActivateStudyStudyAvailabilityStatuses) encodeFields(e *jx.Encoder) {
	for k, elem := range s {
		e.FieldStart(k)

		elem.Encode(e)
	}
}

// Decode decodes ActivateStudyStudyAvailabilityStatuses from json.
func (s *ActivateStudyStudyAvailabilityStatuses) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ActivateStudyStudyAvailabilityStatuses to nil")
	}
	m := s.init()
	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		var elem StudyAvailabilityStatus
		if err := func() error {
			if err := elem.Decode(d); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			return errors.Wrapf(err, "decode field %q", k)
		}
		m[string(k)] = elem
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ActivateStudyStudyAvailabilityStatuses")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s ActivateStudyStudyAvailabilityStatuses) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ActivateStudyStudyAvailabilityStatuses) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes AnyValue as json.
func (s AnyValue) Encode(e *jx.Encoder) {
	unwrapped := jx.Raw(s)

	if len(unwrapped) != 0 {
		e.Raw(unwrapped)
	}
}

// Decode decodes AnyValue from json.
func (s *AnyValue) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode AnyValue to nil")
	}
	var unwrapped jx.Raw
	if err := func() error {
		v, err := d.RawAppend(nil)
		unwrapped = jx.Raw(v)
		if err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = AnyValue(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s AnyValue) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *AnyValue) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *Block) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *Block) encodeFields(e *jx.Encoder) {
	{
		if s.Study.Set {
			e.FieldStart("study")
			s.Study.Encode(e)
		}
	}
	{
		if s.Series.Set {
			e.FieldStart("series")
			s.Series.Encode(e)
		}
	}
	{
		if s.Report.Set {
			e.FieldStart("report")
			s.Report.Encode(e)
		}
	}
	{
		if s.Instance.Set {
			e.FieldStart("instance")
			s.Instance.Encode(e)
		}
	}
}

var jsonFieldsNameOfBlock = [4]string{
	0: "study",
	1: "series",
	2: "report",
	3: "instance",
}

// Decode decodes Block from json.
func (s *Block) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode Block to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "study":
			if err := func() error {
				s.Study.Reset()
				if err := s.Study.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study\"")
			}
		case "series":
			if err := func() error {
				s.Series.Reset()
				if err := s.Series.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series\"")
			}
		case "report":
			if err := func() error {
				s.Report.Reset()
				if err := s.Report.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"report\"")
			}
		case "instance":
			if err := func() error {
				s.Instance.Reset()
				if err := s.Instance.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode Block")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *Block) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *Block) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *BusinessRuleResult) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *BusinessRuleResult) encodeFields(e *jx.Encoder) {
	{
		if s.Rule.Set {
			e.FieldStart("rule")
			s.Rule.Encode(e)
		}
	}
	{
		if s.Result.Set {
			e.FieldStart("result")
			s.Result.Encode(e)
		}
	}
	{
		e.FieldStart("dicom_tags")
		s.DicomTags.Encode(e)
	}
}

var jsonFieldsNameOfBusinessRuleResult = [3]string{
	0: "rule",
	1: "result",
	2: "dicom_tags",
}

// Decode decodes BusinessRuleResult from json.
func (s *BusinessRuleResult) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode BusinessRuleResult to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "rule":
			if err := func() error {
				s.Rule.Reset()
				if err := s.Rule.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"rule\"")
			}
		case "result":
			if err := func() error {
				s.Result.Reset()
				if err := s.Result.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"result\"")
			}
		case "dicom_tags":
			if err := func() error {
				if err := s.DicomTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_tags\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode BusinessRuleResult")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *BusinessRuleResult) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *BusinessRuleResult) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes BusinessRuleResultResult as json.
func (s BusinessRuleResultResult) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes BusinessRuleResultResult from json.
func (s *BusinessRuleResultResult) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode BusinessRuleResultResult to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch BusinessRuleResultResult(v) {
	case BusinessRuleResultResultBlocked:
		*s = BusinessRuleResultResultBlocked
	case BusinessRuleResultResultAllowed:
		*s = BusinessRuleResultResultAllowed
	default:
		*s = BusinessRuleResultResult(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s BusinessRuleResultResult) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *BusinessRuleResultResult) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DebugPatientStudy) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DebugPatientStudy) encodeFields(e *jx.Encoder) {
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		if s.PatientID.Set {
			e.FieldStart("patient_id")
			s.PatientID.Encode(e)
		}
	}
	{
		if s.OrganizationID.Set {
			e.FieldStart("organization_id")
			s.OrganizationID.Encode(e)
		}
	}
	{
		if s.ActivatedTimestamp.Set {
			e.FieldStart("activated_timestamp")
			s.ActivatedTimestamp.Encode(e, json.EncodeDateTime)
		}
	}
	{
		if s.AvailabilityStatus.Set {
			e.FieldStart("availability_status")
			s.AvailabilityStatus.Encode(e)
		}
	}
	{
		if s.OrderID.Set {
			e.FieldStart("order_id")
			s.OrderID.Encode(e)
		}
	}
	{
		if s.TransferID.Set {
			e.FieldStart("transfer_id")
			s.TransferID.Encode(e)
		}
	}
	{
		if s.InstanceUploadProgressPercent.Set {
			e.FieldStart("instance_upload_progress_percent")
			s.InstanceUploadProgressPercent.Encode(e)
		}
	}
	{
		if s.DicomPatientTags.Set {
			e.FieldStart("dicom_patient_tags")
			s.DicomPatientTags.Encode(e)
		}
	}
	{
		if s.DicomStudyTags.Set {
			e.FieldStart("dicom_study_tags")
			s.DicomStudyTags.Encode(e)
		}
	}
	{
		if s.Series != nil {
			e.FieldStart("series")
			e.ArrStart()
			for _, elem := range s.Series {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Reports.Set {
			e.FieldStart("reports")
			s.Reports.Encode(e)
		}
	}
	{
		if s.HasReport.Set {
			e.FieldStart("has_report")
			s.HasReport.Encode(e)
		}
	}
	{
		if s.BusinessRuleResults.Set {
			e.FieldStart("business_rule_results")
			s.BusinessRuleResults.Encode(e)
		}
	}
	{
		if s.IsRecordStreaming.Set {
			e.FieldStart("is_record_streaming")
			s.IsRecordStreaming.Encode(e)
		}
	}
}

var jsonFieldsNameOfDebugPatientStudy = [15]string{
	0:  "uuid",
	1:  "patient_id",
	2:  "organization_id",
	3:  "activated_timestamp",
	4:  "availability_status",
	5:  "order_id",
	6:  "transfer_id",
	7:  "instance_upload_progress_percent",
	8:  "dicom_patient_tags",
	9:  "dicom_study_tags",
	10: "series",
	11: "reports",
	12: "has_report",
	13: "business_rule_results",
	14: "is_record_streaming",
}

// Decode decodes DebugPatientStudy from json.
func (s *DebugPatientStudy) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DebugPatientStudy to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "patient_id":
			if err := func() error {
				s.PatientID.Reset()
				if err := s.PatientID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_id\"")
			}
		case "organization_id":
			if err := func() error {
				s.OrganizationID.Reset()
				if err := s.OrganizationID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"organization_id\"")
			}
		case "activated_timestamp":
			if err := func() error {
				s.ActivatedTimestamp.Reset()
				if err := s.ActivatedTimestamp.Decode(d, json.DecodeDateTime); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"activated_timestamp\"")
			}
		case "availability_status":
			if err := func() error {
				s.AvailabilityStatus.Reset()
				if err := s.AvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"availability_status\"")
			}
		case "order_id":
			if err := func() error {
				s.OrderID.Reset()
				if err := s.OrderID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"order_id\"")
			}
		case "transfer_id":
			if err := func() error {
				s.TransferID.Reset()
				if err := s.TransferID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_id\"")
			}
		case "instance_upload_progress_percent":
			if err := func() error {
				s.InstanceUploadProgressPercent.Reset()
				if err := s.InstanceUploadProgressPercent.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance_upload_progress_percent\"")
			}
		case "dicom_patient_tags":
			if err := func() error {
				s.DicomPatientTags.Reset()
				if err := s.DicomPatientTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_patient_tags\"")
			}
		case "dicom_study_tags":
			if err := func() error {
				s.DicomStudyTags.Reset()
				if err := s.DicomStudyTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_study_tags\"")
			}
		case "series":
			if err := func() error {
				s.Series = make([]DicomSeries, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem DicomSeries
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Series = append(s.Series, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series\"")
			}
		case "reports":
			if err := func() error {
				s.Reports.Reset()
				if err := s.Reports.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"reports\"")
			}
		case "has_report":
			if err := func() error {
				s.HasReport.Reset()
				if err := s.HasReport.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"has_report\"")
			}
		case "business_rule_results":
			if err := func() error {
				s.BusinessRuleResults.Reset()
				if err := s.BusinessRuleResults.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"business_rule_results\"")
			}
		case "is_record_streaming":
			if err := func() error {
				s.IsRecordStreaming.Reset()
				if err := s.IsRecordStreaming.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"is_record_streaming\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DebugPatientStudy")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DebugPatientStudy) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DebugPatientStudy) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomInstance) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomInstance) encodeFields(e *jx.Encoder) {
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		if s.DicomTags.Set {
			e.FieldStart("dicom_tags")
			s.DicomTags.Encode(e)
		}
	}
	{
		if s.FileSizeBytes.Set {
			e.FieldStart("file_size_bytes")
			s.FileSizeBytes.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomInstance = [3]string{
	0: "uuid",
	1: "dicom_tags",
	2: "file_size_bytes",
}

// Decode decodes DicomInstance from json.
func (s *DicomInstance) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomInstance to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "dicom_tags":
			if err := func() error {
				s.DicomTags.Reset()
				if err := s.DicomTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_tags\"")
			}
		case "file_size_bytes":
			if err := func() error {
				s.FileSizeBytes.Reset()
				if err := s.FileSizeBytes.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"file_size_bytes\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomInstance")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomInstance) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomInstance) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomInstanceTags) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomInstanceTags) encodeFields(e *jx.Encoder) {
	{
		if s.SopInstanceUID.Set {
			e.FieldStart("sop_instance_uid")
			s.SopInstanceUID.Encode(e)
		}
	}
	{
		if s.Protocol.Set {
			e.FieldStart("protocol")
			s.Protocol.Encode(e)
		}
	}
	{
		if s.InstanceNumber.Set {
			e.FieldStart("instance_number")
			s.InstanceNumber.Encode(e)
		}
	}
	{
		if s.ImageType.Set {
			e.FieldStart("image_type")
			s.ImageType.Encode(e)
		}
	}
	{
		if s.SopClassUID.Set {
			e.FieldStart("sop_class_uid")
			s.SopClassUID.Encode(e)
		}
	}
	{
		if s.ContentDate.Set {
			e.FieldStart("content_date")
			s.ContentDate.Encode(e)
		}
	}
	{
		if s.ContentTime.Set {
			e.FieldStart("content_time")
			s.ContentTime.Encode(e)
		}
	}
	{
		if s.AcquisitionDate.Set {
			e.FieldStart("acquisition_date")
			s.AcquisitionDate.Encode(e)
		}
	}
	{
		if s.AcquisitionTime.Set {
			e.FieldStart("acquisition_time")
			s.AcquisitionTime.Encode(e)
		}
	}
	{
		if s.ViewPosition.Set {
			e.FieldStart("view_position")
			s.ViewPosition.Encode(e)
		}
	}
	{
		if s.ImagePosition.Set {
			e.FieldStart("image_position")
			s.ImagePosition.Encode(e)
		}
	}
	{
		if s.ImageOrientation.Set {
			e.FieldStart("image_orientation")
			s.ImageOrientation.Encode(e)
		}
	}
	{
		if s.FrameOfReferenceUID.Set {
			e.FieldStart("frame_of_reference_uid")
			s.FrameOfReferenceUID.Encode(e)
		}
	}
	{
		if s.ImageLaterality.Set {
			e.FieldStart("image_laterality")
			s.ImageLaterality.Encode(e)
		}
	}
	{
		if s.NumberOfFrames.Set {
			e.FieldStart("number_of_frames")
			s.NumberOfFrames.Encode(e)
		}
	}
	{
		if s.Rows.Set {
			e.FieldStart("rows")
			s.Rows.Encode(e)
		}
	}
	{
		if s.Columns.Set {
			e.FieldStart("columns")
			s.Columns.Encode(e)
		}
	}
	{
		if s.PixelSpacing.Set {
			e.FieldStart("pixel_spacing")
			s.PixelSpacing.Encode(e)
		}
	}
	{
		if s.TransferSyntaxUID.Set {
			e.FieldStart("transfer_syntax_uid")
			s.TransferSyntaxUID.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomInstanceTags = [19]string{
	0:  "sop_instance_uid",
	1:  "protocol",
	2:  "instance_number",
	3:  "image_type",
	4:  "sop_class_uid",
	5:  "content_date",
	6:  "content_time",
	7:  "acquisition_date",
	8:  "acquisition_time",
	9:  "view_position",
	10: "image_position",
	11: "image_orientation",
	12: "frame_of_reference_uid",
	13: "image_laterality",
	14: "number_of_frames",
	15: "rows",
	16: "columns",
	17: "pixel_spacing",
	18: "transfer_syntax_uid",
}

// Decode decodes DicomInstanceTags from json.
func (s *DicomInstanceTags) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomInstanceTags to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "sop_instance_uid":
			if err := func() error {
				s.SopInstanceUID.Reset()
				if err := s.SopInstanceUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"sop_instance_uid\"")
			}
		case "protocol":
			if err := func() error {
				s.Protocol.Reset()
				if err := s.Protocol.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"protocol\"")
			}
		case "instance_number":
			if err := func() error {
				s.InstanceNumber.Reset()
				if err := s.InstanceNumber.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance_number\"")
			}
		case "image_type":
			if err := func() error {
				s.ImageType.Reset()
				if err := s.ImageType.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"image_type\"")
			}
		case "sop_class_uid":
			if err := func() error {
				s.SopClassUID.Reset()
				if err := s.SopClassUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"sop_class_uid\"")
			}
		case "content_date":
			if err := func() error {
				s.ContentDate.Reset()
				if err := s.ContentDate.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"content_date\"")
			}
		case "content_time":
			if err := func() error {
				s.ContentTime.Reset()
				if err := s.ContentTime.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"content_time\"")
			}
		case "acquisition_date":
			if err := func() error {
				s.AcquisitionDate.Reset()
				if err := s.AcquisitionDate.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"acquisition_date\"")
			}
		case "acquisition_time":
			if err := func() error {
				s.AcquisitionTime.Reset()
				if err := s.AcquisitionTime.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"acquisition_time\"")
			}
		case "view_position":
			if err := func() error {
				s.ViewPosition.Reset()
				if err := s.ViewPosition.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"view_position\"")
			}
		case "image_position":
			if err := func() error {
				s.ImagePosition.Reset()
				if err := s.ImagePosition.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"image_position\"")
			}
		case "image_orientation":
			if err := func() error {
				s.ImageOrientation.Reset()
				if err := s.ImageOrientation.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"image_orientation\"")
			}
		case "frame_of_reference_uid":
			if err := func() error {
				s.FrameOfReferenceUID.Reset()
				if err := s.FrameOfReferenceUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"frame_of_reference_uid\"")
			}
		case "image_laterality":
			if err := func() error {
				s.ImageLaterality.Reset()
				if err := s.ImageLaterality.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"image_laterality\"")
			}
		case "number_of_frames":
			if err := func() error {
				s.NumberOfFrames.Reset()
				if err := s.NumberOfFrames.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"number_of_frames\"")
			}
		case "rows":
			if err := func() error {
				s.Rows.Reset()
				if err := s.Rows.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"rows\"")
			}
		case "columns":
			if err := func() error {
				s.Columns.Reset()
				if err := s.Columns.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"columns\"")
			}
		case "pixel_spacing":
			if err := func() error {
				s.PixelSpacing.Reset()
				if err := s.PixelSpacing.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"pixel_spacing\"")
			}
		case "transfer_syntax_uid":
			if err := func() error {
				s.TransferSyntaxUID.Reset()
				if err := s.TransferSyntaxUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_syntax_uid\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomInstanceTags")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomInstanceTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomInstanceTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s DicomMetadata) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields implements json.Marshaler.
func (s DicomMetadata) encodeFields(e *jx.Encoder) {
	for k, elem := range s {
		e.FieldStart(k)

		elem.Encode(e)
	}
}

// Decode decodes DicomMetadata from json.
func (s *DicomMetadata) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomMetadata to nil")
	}
	m := s.init()
	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		var elem DicomMetadataItem
		if err := func() error {
			if err := elem.Decode(d); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			return errors.Wrapf(err, "decode field %q", k)
		}
		m[string(k)] = elem
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomMetadata")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s DicomMetadata) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomMetadata) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomMetadataItem) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomMetadataItem) encodeFields(e *jx.Encoder) {
	{
		if s.Vr.Set {
			e.FieldStart("vr")
			s.Vr.Encode(e)
		}
	}
	{
		if s.Value.Set {
			e.FieldStart("Value")
			s.Value.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomMetadataItem = [2]string{
	0: "vr",
	1: "Value",
}

// Decode decodes DicomMetadataItem from json.
func (s *DicomMetadataItem) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomMetadataItem to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "vr":
			if err := func() error {
				s.Vr.Reset()
				if err := s.Vr.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"vr\"")
			}
		case "Value":
			if err := func() error {
				s.Value.Reset()
				if err := s.Value.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"Value\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomMetadataItem")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomMetadataItem) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomMetadataItem) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomPatientTags) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomPatientTags) encodeFields(e *jx.Encoder) {
	{
		if s.PatientName.Set {
			e.FieldStart("patient_name")
			s.PatientName.Encode(e)
		}
	}
	{
		if s.PatientID.Set {
			e.FieldStart("patient_id")
			s.PatientID.Encode(e)
		}
	}
	{
		if s.PatientBirthDate.Set {
			e.FieldStart("patient_birth_date")
			s.PatientBirthDate.Encode(e)
		}
	}
	{
		if s.PatientSex.Set {
			e.FieldStart("patient_sex")
			s.PatientSex.Encode(e)
		}
	}
	{
		if s.PatientTelephoneNumber.Set {
			e.FieldStart("patient_telephone_number")
			s.PatientTelephoneNumber.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomPatientTags = [5]string{
	0: "patient_name",
	1: "patient_id",
	2: "patient_birth_date",
	3: "patient_sex",
	4: "patient_telephone_number",
}

// Decode decodes DicomPatientTags from json.
func (s *DicomPatientTags) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomPatientTags to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "patient_name":
			if err := func() error {
				s.PatientName.Reset()
				if err := s.PatientName.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_name\"")
			}
		case "patient_id":
			if err := func() error {
				s.PatientID.Reset()
				if err := s.PatientID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_id\"")
			}
		case "patient_birth_date":
			if err := func() error {
				s.PatientBirthDate.Reset()
				if err := s.PatientBirthDate.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_birth_date\"")
			}
		case "patient_sex":
			if err := func() error {
				s.PatientSex.Reset()
				if err := s.PatientSex.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_sex\"")
			}
		case "patient_telephone_number":
			if err := func() error {
				s.PatientTelephoneNumber.Reset()
				if err := s.PatientTelephoneNumber.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_telephone_number\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomPatientTags")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomPatientTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomPatientTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomSeries) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomSeries) encodeFields(e *jx.Encoder) {
	{
		if s.DicomTags.Set {
			e.FieldStart("dicom_tags")
			s.DicomTags.Encode(e)
		}
	}
	{
		if s.Instances.Set {
			e.FieldStart("instances")
			s.Instances.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomSeries = [2]string{
	0: "dicom_tags",
	1: "instances",
}

// Decode decodes DicomSeries from json.
func (s *DicomSeries) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomSeries to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "dicom_tags":
			if err := func() error {
				s.DicomTags.Reset()
				if err := s.DicomTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_tags\"")
			}
		case "instances":
			if err := func() error {
				s.Instances.Reset()
				if err := s.Instances.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instances\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomSeries")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomSeries) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomSeries) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomSeriesTags) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomSeriesTags) encodeFields(e *jx.Encoder) {
	{
		if s.SeriesInstanceUID.Set {
			e.FieldStart("series_instance_uid")
			s.SeriesInstanceUID.Encode(e)
		}
	}
	{
		if s.SeriesNumber.Set {
			e.FieldStart("series_number")
			s.SeriesNumber.Encode(e)
		}
	}
	{
		if s.SeriesDescription.Set {
			e.FieldStart("series_description")
			s.SeriesDescription.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomSeriesTags = [3]string{
	0: "series_instance_uid",
	1: "series_number",
	2: "series_description",
}

// Decode decodes DicomSeriesTags from json.
func (s *DicomSeriesTags) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomSeriesTags to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "series_instance_uid":
			if err := func() error {
				s.SeriesInstanceUID.Reset()
				if err := s.SeriesInstanceUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series_instance_uid\"")
			}
		case "series_number":
			if err := func() error {
				s.SeriesNumber.Reset()
				if err := s.SeriesNumber.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series_number\"")
			}
		case "series_description":
			if err := func() error {
				s.SeriesDescription.Reset()
				if err := s.SeriesDescription.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series_description\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomSeriesTags")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomSeriesTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomSeriesTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *DicomStudyTags) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *DicomStudyTags) encodeFields(e *jx.Encoder) {
	{
		if s.StudyInstanceUID.Set {
			e.FieldStart("study_instance_uid")
			s.StudyInstanceUID.Encode(e)
		}
	}
	{
		if s.StudyDate.Set {
			e.FieldStart("study_date")
			s.StudyDate.Encode(e)
		}
	}
	{
		if s.StudyDescription.Set {
			e.FieldStart("study_description")
			s.StudyDescription.Encode(e)
		}
	}
	{
		if s.AccessionNumber.Set {
			e.FieldStart("accession_number")
			s.AccessionNumber.Encode(e)
		}
	}
	{
		if s.ReferringPhysician.Set {
			e.FieldStart("referring_physician")
			s.ReferringPhysician.Encode(e)
		}
	}
	{
		if s.Modality.Set {
			e.FieldStart("modality")
			s.Modality.Encode(e)
		}
	}
	{
		if s.BodyPart.Set {
			e.FieldStart("body_part")
			s.BodyPart.Encode(e)
		}
	}
}

var jsonFieldsNameOfDicomStudyTags = [7]string{
	0: "study_instance_uid",
	1: "study_date",
	2: "study_description",
	3: "accession_number",
	4: "referring_physician",
	5: "modality",
	6: "body_part",
}

// Decode decodes DicomStudyTags from json.
func (s *DicomStudyTags) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode DicomStudyTags to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "study_instance_uid":
			if err := func() error {
				s.StudyInstanceUID.Reset()
				if err := s.StudyInstanceUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_instance_uid\"")
			}
		case "study_date":
			if err := func() error {
				s.StudyDate.Reset()
				if err := s.StudyDate.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_date\"")
			}
		case "study_description":
			if err := func() error {
				s.StudyDescription.Reset()
				if err := s.StudyDescription.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_description\"")
			}
		case "accession_number":
			if err := func() error {
				s.AccessionNumber.Reset()
				if err := s.AccessionNumber.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"accession_number\"")
			}
		case "referring_physician":
			if err := func() error {
				s.ReferringPhysician.Reset()
				if err := s.ReferringPhysician.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"referring_physician\"")
			}
		case "modality":
			if err := func() error {
				s.Modality.Reset()
				if err := s.Modality.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"modality\"")
			}
		case "body_part":
			if err := func() error {
				s.BodyPart.Reset()
				if err := s.BodyPart.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"body_part\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode DicomStudyTags")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *DicomStudyTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *DicomStudyTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *GenerateMedreamToken) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *GenerateMedreamToken) encodeFields(e *jx.Encoder) {
	{
		if s.ExamUUID.Set {
			e.FieldStart("exam_uuid")
			s.ExamUUID.Encode(e)
		}
	}
	{
		if s.StudyUID.Set {
			e.FieldStart("study_uid")
			s.StudyUID.Encode(e)
		}
	}
	{
		if s.AccountID.Set {
			e.FieldStart("account_id")
			s.AccountID.Encode(e)
		}
	}
}

var jsonFieldsNameOfGenerateMedreamToken = [3]string{
	0: "exam_uuid",
	1: "study_uid",
	2: "account_id",
}

// Decode decodes GenerateMedreamToken from json.
func (s *GenerateMedreamToken) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GenerateMedreamToken to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "exam_uuid":
			if err := func() error {
				s.ExamUUID.Reset()
				if err := s.ExamUUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"exam_uuid\"")
			}
		case "study_uid":
			if err := func() error {
				s.StudyUID.Reset()
				if err := s.StudyUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_uid\"")
			}
		case "account_id":
			if err := func() error {
				s.AccountID.Reset()
				if err := s.AccountID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"account_id\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode GenerateMedreamToken")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *GenerateMedreamToken) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GenerateMedreamToken) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetAllPatientStudiesOKApplicationJSON as json.
func (s GetAllPatientStudiesOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []DebugPatientStudy(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetAllPatientStudiesOKApplicationJSON from json.
func (s *GetAllPatientStudiesOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetAllPatientStudiesOKApplicationJSON to nil")
	}
	var unwrapped []DebugPatientStudy
	if err := func() error {
		unwrapped = make([]DebugPatientStudy, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem DebugPatientStudy
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetAllPatientStudiesOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetAllPatientStudiesOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetAllPatientStudiesOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetImagingOKApplicationJSON as json.
func (s GetImagingOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []ImagingExam(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetImagingOKApplicationJSON from json.
func (s *GetImagingOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetImagingOKApplicationJSON to nil")
	}
	var unwrapped []ImagingExam
	if err := func() error {
		unwrapped = make([]ImagingExam, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem ImagingExam
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetImagingOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetImagingOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetImagingOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetRecentStudyUploadMetadataOKApplicationJSON as json.
func (s GetRecentStudyUploadMetadataOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []StudyUploadMetadata(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetRecentStudyUploadMetadataOKApplicationJSON from json.
func (s *GetRecentStudyUploadMetadataOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetRecentStudyUploadMetadataOKApplicationJSON to nil")
	}
	var unwrapped []StudyUploadMetadata
	if err := func() error {
		unwrapped = make([]StudyUploadMetadata, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem StudyUploadMetadata
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetRecentStudyUploadMetadataOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetRecentStudyUploadMetadataOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetRecentStudyUploadMetadataOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetUploadOverviewOKApplicationJSON as json.
func (s GetUploadOverviewOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []ProviderUploadOverview(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetUploadOverviewOKApplicationJSON from json.
func (s *GetUploadOverviewOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetUploadOverviewOKApplicationJSON to nil")
	}
	var unwrapped []ProviderUploadOverview
	if err := func() error {
		unwrapped = make([]ProviderUploadOverview, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem ProviderUploadOverview
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetUploadOverviewOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetUploadOverviewOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetUploadOverviewOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetV0ImagingTransfersOKApplicationJSON as json.
func (s GetV0ImagingTransfersOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []ImagingTransfer(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetV0ImagingTransfersOKApplicationJSON from json.
func (s *GetV0ImagingTransfersOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetV0ImagingTransfersOKApplicationJSON to nil")
	}
	var unwrapped []ImagingTransfer
	if err := func() error {
		unwrapped = make([]ImagingTransfer, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem ImagingTransfer
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetV0ImagingTransfersOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetV0ImagingTransfersOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetV0ImagingTransfersOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetV1MeddreamGenerateOKApplicationJSON as json.
func (s GetV1MeddreamGenerateOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := string(s)

	e.Str(unwrapped)
}

// Decode decodes GetV1MeddreamGenerateOKApplicationJSON from json.
func (s *GetV1MeddreamGenerateOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetV1MeddreamGenerateOKApplicationJSON to nil")
	}
	var unwrapped string
	if err := func() error {
		v, err := d.Str()
		unwrapped = string(v)
		if err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetV1MeddreamGenerateOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetV1MeddreamGenerateOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetV1MeddreamGenerateOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetV1PatientsUploadStatusOKApplicationJSON as json.
func (s GetV1PatientsUploadStatusOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []UploadStatus(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetV1PatientsUploadStatusOKApplicationJSON from json.
func (s *GetV1PatientsUploadStatusOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetV1PatientsUploadStatusOKApplicationJSON to nil")
	}
	var unwrapped []UploadStatus
	if err := func() error {
		unwrapped = make([]UploadStatus, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem UploadStatus
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetV1PatientsUploadStatusOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetV1PatientsUploadStatusOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetV1PatientsUploadStatusOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetV1StudiesUploadStatusOKApplicationJSON as json.
func (s GetV1StudiesUploadStatusOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []StudyUploadStatus(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetV1StudiesUploadStatusOKApplicationJSON from json.
func (s *GetV1StudiesUploadStatusOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetV1StudiesUploadStatusOKApplicationJSON to nil")
	}
	var unwrapped []StudyUploadStatus
	if err := func() error {
		unwrapped = make([]StudyUploadStatus, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem StudyUploadStatus
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetV1StudiesUploadStatusOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetV1StudiesUploadStatusOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetV1StudiesUploadStatusOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GetV1SupportPatientsUploadStatusOKApplicationJSON as json.
func (s GetV1SupportPatientsUploadStatusOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []UploadStatus(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes GetV1SupportPatientsUploadStatusOKApplicationJSON from json.
func (s *GetV1SupportPatientsUploadStatusOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode GetV1SupportPatientsUploadStatusOKApplicationJSON to nil")
	}
	var unwrapped []UploadStatus
	if err := func() error {
		unwrapped = make([]UploadStatus, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem UploadStatus
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = GetV1SupportPatientsUploadStatusOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s GetV1SupportPatientsUploadStatusOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *GetV1SupportPatientsUploadStatusOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ImagingExam) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ImagingExam) encodeFields(e *jx.Encoder) {
	{
		if s.ExamUUID.Set {
			e.FieldStart("exam_uuid")
			s.ExamUUID.Encode(e)
		}
	}
	{
		if s.TransferID.Set {
			e.FieldStart("transfer_id")
			s.TransferID.Encode(e)
		}
	}
	{
		if s.UploadTime.Set {
			e.FieldStart("upload_time")
			s.UploadTime.Encode(e)
		}
	}
	{
		if s.PatientName.Set {
			e.FieldStart("patient_name")
			s.PatientName.Encode(e)
		}
	}
	{
		if s.PatientDob.Set {
			e.FieldStart("patient_dob")
			s.PatientDob.Encode(e)
		}
	}
	{
		if s.Date.Set {
			e.FieldStart("date")
			s.Date.Encode(e)
		}
	}
	{
		if s.Modality.Set {
			e.FieldStart("modality")
			s.Modality.Encode(e)
		}
	}
	{
		if s.Provider.Set {
			e.FieldStart("provider")
			s.Provider.Encode(e)
		}
	}
	{
		if s.Source.Set {
			e.FieldStart("source")
			s.Source.Encode(e)
		}
	}
	{
		if s.Activated.Set {
			e.FieldStart("activated")
			s.Activated.Encode(e)
		}
	}
	{
		if s.RevokedAt.Set {
			e.FieldStart("revoked_at")
			s.RevokedAt.Encode(e)
		}
	}
	{
		if s.UnrevokedAt.Set {
			e.FieldStart("unrevoked_at")
			s.UnrevokedAt.Encode(e)
		}
	}
	{
		if s.Reports != nil {
			e.FieldStart("reports")
			e.ArrStart()
			for _, elem := range s.Reports {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Series.Set {
			e.FieldStart("series")
			s.Series.Encode(e)
		}
	}
}

var jsonFieldsNameOfImagingExam = [14]string{
	0:  "exam_uuid",
	1:  "transfer_id",
	2:  "upload_time",
	3:  "patient_name",
	4:  "patient_dob",
	5:  "date",
	6:  "modality",
	7:  "provider",
	8:  "source",
	9:  "activated",
	10: "revoked_at",
	11: "unrevoked_at",
	12: "reports",
	13: "series",
}

// Decode decodes ImagingExam from json.
func (s *ImagingExam) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ImagingExam to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "exam_uuid":
			if err := func() error {
				s.ExamUUID.Reset()
				if err := s.ExamUUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"exam_uuid\"")
			}
		case "transfer_id":
			if err := func() error {
				s.TransferID.Reset()
				if err := s.TransferID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_id\"")
			}
		case "upload_time":
			if err := func() error {
				s.UploadTime.Reset()
				if err := s.UploadTime.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"upload_time\"")
			}
		case "patient_name":
			if err := func() error {
				s.PatientName.Reset()
				if err := s.PatientName.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_name\"")
			}
		case "patient_dob":
			if err := func() error {
				s.PatientDob.Reset()
				if err := s.PatientDob.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_dob\"")
			}
		case "date":
			if err := func() error {
				s.Date.Reset()
				if err := s.Date.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"date\"")
			}
		case "modality":
			if err := func() error {
				s.Modality.Reset()
				if err := s.Modality.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"modality\"")
			}
		case "provider":
			if err := func() error {
				s.Provider.Reset()
				if err := s.Provider.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider\"")
			}
		case "source":
			if err := func() error {
				s.Source.Reset()
				if err := s.Source.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"source\"")
			}
		case "activated":
			if err := func() error {
				s.Activated.Reset()
				if err := s.Activated.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"activated\"")
			}
		case "revoked_at":
			if err := func() error {
				s.RevokedAt.Reset()
				if err := s.RevokedAt.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"revoked_at\"")
			}
		case "unrevoked_at":
			if err := func() error {
				s.UnrevokedAt.Reset()
				if err := s.UnrevokedAt.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"unrevoked_at\"")
			}
		case "reports":
			if err := func() error {
				s.Reports = make([]Report, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem Report
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Reports = append(s.Reports, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"reports\"")
			}
		case "series":
			if err := func() error {
				s.Series.Reset()
				if err := s.Series.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ImagingExam")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ImagingExam) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ImagingExam) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ImagingSource as json.
func (s ImagingSource) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes ImagingSource from json.
func (s *ImagingSource) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ImagingSource to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch ImagingSource(v) {
	case ImagingSourceSELFUPLOAD:
		*s = ImagingSourceSELFUPLOAD
	case ImagingSourceREQUEST:
		*s = ImagingSourceREQUEST
	case ImagingSourceSUBSEQUENT:
		*s = ImagingSourceSUBSEQUENT
	case ImagingSourceUPH:
		*s = ImagingSourceUPH
	default:
		*s = ImagingSource(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s ImagingSource) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ImagingSource) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ImagingTransfer) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ImagingTransfer) encodeFields(e *jx.Encoder) {
	{
		if s.TransferID.Set {
			e.FieldStart("transfer_id")
			s.TransferID.Encode(e)
		}
	}
	{
		if s.PatientName.Set {
			e.FieldStart("patient_name")
			s.PatientName.Encode(e)
		}
	}
	{
		if s.Provider.Set {
			e.FieldStart("provider")
			s.Provider.Encode(e)
		}
	}
	{
		if s.Uploaded.Set {
			e.FieldStart("uploaded")
			s.Uploaded.Encode(e)
		}
	}
	{
		if s.RequestID.Set {
			e.FieldStart("request_id")
			s.RequestID.Encode(e)
		}
	}
	{
		if s.Activated.Set {
			e.FieldStart("activated")
			s.Activated.Encode(e)
		}
	}
	{
		if s.OrderID.Set {
			e.FieldStart("order_id")
			s.OrderID.Encode(e)
		}
	}
	{
		if s.OrgID.Set {
			e.FieldStart("org_id")
			s.OrgID.Encode(e)
		}
	}
	{
		if s.HasRevokedExams.Set {
			e.FieldStart("has_revoked_exams")
			s.HasRevokedExams.Encode(e)
		}
	}
}

var jsonFieldsNameOfImagingTransfer = [9]string{
	0: "transfer_id",
	1: "patient_name",
	2: "provider",
	3: "uploaded",
	4: "request_id",
	5: "activated",
	6: "order_id",
	7: "org_id",
	8: "has_revoked_exams",
}

// Decode decodes ImagingTransfer from json.
func (s *ImagingTransfer) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ImagingTransfer to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "transfer_id":
			if err := func() error {
				s.TransferID.Reset()
				if err := s.TransferID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_id\"")
			}
		case "patient_name":
			if err := func() error {
				s.PatientName.Reset()
				if err := s.PatientName.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_name\"")
			}
		case "provider":
			if err := func() error {
				s.Provider.Reset()
				if err := s.Provider.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider\"")
			}
		case "uploaded":
			if err := func() error {
				s.Uploaded.Reset()
				if err := s.Uploaded.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uploaded\"")
			}
		case "request_id":
			if err := func() error {
				s.RequestID.Reset()
				if err := s.RequestID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"request_id\"")
			}
		case "activated":
			if err := func() error {
				s.Activated.Reset()
				if err := s.Activated.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"activated\"")
			}
		case "order_id":
			if err := func() error {
				s.OrderID.Reset()
				if err := s.OrderID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"order_id\"")
			}
		case "org_id":
			if err := func() error {
				s.OrgID.Reset()
				if err := s.OrgID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"org_id\"")
			}
		case "has_revoked_exams":
			if err := func() error {
				s.HasRevokedExams.Reset()
				if err := s.HasRevokedExams.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"has_revoked_exams\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ImagingTransfer")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ImagingTransfer) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ImagingTransfer) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *MeddreamToken) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *MeddreamToken) encodeFields(e *jx.Encoder) {
	{
		if s.Items != nil {
			e.FieldStart("items")
			e.ArrStart()
			for _, elem := range s.Items {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.StorageConfiguration != nil {
			e.FieldStart("storageConfiguration")
			e.ArrStart()
			for _, elem := range s.StorageConfiguration {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
}

var jsonFieldsNameOfMeddreamToken = [2]string{
	0: "items",
	1: "storageConfiguration",
}

// Decode decodes MeddreamToken from json.
func (s *MeddreamToken) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode MeddreamToken to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "items":
			if err := func() error {
				s.Items = make([]MeddreamTokenItemsItem, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem MeddreamTokenItemsItem
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Items = append(s.Items, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"items\"")
			}
		case "storageConfiguration":
			if err := func() error {
				s.StorageConfiguration = make([]MeddreamTokenStorageConfigurationItem, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem MeddreamTokenStorageConfigurationItem
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.StorageConfiguration = append(s.StorageConfiguration, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"storageConfiguration\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode MeddreamToken")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *MeddreamToken) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *MeddreamToken) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *MeddreamTokenItemsItem) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *MeddreamTokenItemsItem) encodeFields(e *jx.Encoder) {
	{
		if s.Studies.Set {
			e.FieldStart("studies")
			s.Studies.Encode(e)
		}
	}
}

var jsonFieldsNameOfMeddreamTokenItemsItem = [1]string{
	0: "studies",
}

// Decode decodes MeddreamTokenItemsItem from json.
func (s *MeddreamTokenItemsItem) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode MeddreamTokenItemsItem to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "studies":
			if err := func() error {
				s.Studies.Reset()
				if err := s.Studies.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"studies\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode MeddreamTokenItemsItem")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *MeddreamTokenItemsItem) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *MeddreamTokenItemsItem) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *MeddreamTokenItemsItemStudies) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *MeddreamTokenItemsItemStudies) encodeFields(e *jx.Encoder) {
	{
		if s.Study.Set {
			e.FieldStart("study")
			s.Study.Encode(e)
		}
	}
	{
		if s.Storage.Set {
			e.FieldStart("storage")
			s.Storage.Encode(e)
		}
	}
}

var jsonFieldsNameOfMeddreamTokenItemsItemStudies = [2]string{
	0: "study",
	1: "storage",
}

// Decode decodes MeddreamTokenItemsItemStudies from json.
func (s *MeddreamTokenItemsItemStudies) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode MeddreamTokenItemsItemStudies to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "study":
			if err := func() error {
				s.Study.Reset()
				if err := s.Study.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study\"")
			}
		case "storage":
			if err := func() error {
				s.Storage.Reset()
				if err := s.Storage.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"storage\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode MeddreamTokenItemsItemStudies")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *MeddreamTokenItemsItemStudies) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *MeddreamTokenItemsItemStudies) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *MeddreamTokenStorageConfigurationItem) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *MeddreamTokenStorageConfigurationItem) encodeFields(e *jx.Encoder) {
	{
		if s.Storage.Set {
			e.FieldStart("storage")
			s.Storage.Encode(e)
		}
	}
	{
		if s.Params != nil {
			e.FieldStart("params")
			e.ArrStart()
			for _, elem := range s.Params {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
}

var jsonFieldsNameOfMeddreamTokenStorageConfigurationItem = [2]string{
	0: "storage",
	1: "params",
}

// Decode decodes MeddreamTokenStorageConfigurationItem from json.
func (s *MeddreamTokenStorageConfigurationItem) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode MeddreamTokenStorageConfigurationItem to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "storage":
			if err := func() error {
				s.Storage.Reset()
				if err := s.Storage.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"storage\"")
			}
		case "params":
			if err := func() error {
				s.Params = make([]MeddreamTokenStorageConfigurationItemParamsItem, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem MeddreamTokenStorageConfigurationItemParamsItem
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Params = append(s.Params, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"params\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode MeddreamTokenStorageConfigurationItem")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *MeddreamTokenStorageConfigurationItem) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *MeddreamTokenStorageConfigurationItem) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) encodeFields(e *jx.Encoder) {
	{
		if s.Name.Set {
			e.FieldStart("name")
			s.Name.Encode(e)
		}
	}
	{
		if s.Value.Set {
			e.FieldStart("value")
			s.Value.Encode(e)
		}
	}
}

var jsonFieldsNameOfMeddreamTokenStorageConfigurationItemParamsItem = [2]string{
	0: "name",
	1: "value",
}

// Decode decodes MeddreamTokenStorageConfigurationItemParamsItem from json.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode MeddreamTokenStorageConfigurationItemParamsItem to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "name":
			if err := func() error {
				s.Name.Reset()
				if err := s.Name.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"name\"")
			}
		case "value":
			if err := func() error {
				s.Value.Reset()
				if err := s.Value.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"value\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode MeddreamTokenStorageConfigurationItemParamsItem")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *MeddreamTokenStorageConfigurationItemParamsItem) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ActivateStudy as json.
func (o OptActivateStudy) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes ActivateStudy from json.
func (o *OptActivateStudy) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptActivateStudy to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptActivateStudy) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptActivateStudy) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ActivateStudyStudyAvailabilityStatuses as json.
func (o OptActivateStudyStudyAvailabilityStatuses) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes ActivateStudyStudyAvailabilityStatuses from json.
func (o *OptActivateStudyStudyAvailabilityStatuses) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptActivateStudyStudyAvailabilityStatuses to nil")
	}
	o.Set = true
	o.Value = make(ActivateStudyStudyAvailabilityStatuses)
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptActivateStudyStudyAvailabilityStatuses) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptActivateStudyStudyAvailabilityStatuses) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes Block as json.
func (o OptBlock) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes Block from json.
func (o *OptBlock) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptBlock to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptBlock) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptBlock) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes bool as json.
func (o OptBool) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Bool(bool(o.Value))
}

// Decode decodes bool from json.
func (o *OptBool) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptBool to nil")
	}
	o.Set = true
	v, err := d.Bool()
	if err != nil {
		return err
	}
	o.Value = bool(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptBool) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptBool) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes BusinessRuleResultResult as json.
func (o OptBusinessRuleResultResult) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes BusinessRuleResultResult from json.
func (o *OptBusinessRuleResultResult) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptBusinessRuleResultResult to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptBusinessRuleResultResult) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptBusinessRuleResultResult) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes time.Time as json.
func (o OptDate) Encode(e *jx.Encoder, format func(*jx.Encoder, time.Time)) {
	if !o.Set {
		return
	}
	format(e, o.Value)
}

// Decode decodes time.Time from json.
func (o *OptDate) Decode(d *jx.Decoder, format func(*jx.Decoder) (time.Time, error)) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDate to nil")
	}
	o.Set = true
	v, err := format(d)
	if err != nil {
		return err
	}
	o.Value = v
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDate) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e, json.EncodeDate)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDate) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d, json.DecodeDate)
}

// Encode encodes time.Time as json.
func (o OptDateTime) Encode(e *jx.Encoder, format func(*jx.Encoder, time.Time)) {
	if !o.Set {
		return
	}
	format(e, o.Value)
}

// Decode decodes time.Time from json.
func (o *OptDateTime) Decode(d *jx.Decoder, format func(*jx.Decoder) (time.Time, error)) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDateTime to nil")
	}
	o.Set = true
	v, err := format(d)
	if err != nil {
		return err
	}
	o.Value = v
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDateTime) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e, json.EncodeDateTime)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDateTime) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d, json.DecodeDateTime)
}

// Encode encodes DicomInstanceTags as json.
func (o OptDicomInstanceTags) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes DicomInstanceTags from json.
func (o *OptDicomInstanceTags) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDicomInstanceTags to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDicomInstanceTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDicomInstanceTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes DicomPatientTags as json.
func (o OptDicomPatientTags) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes DicomPatientTags from json.
func (o *OptDicomPatientTags) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDicomPatientTags to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDicomPatientTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDicomPatientTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes DicomSeriesTags as json.
func (o OptDicomSeriesTags) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes DicomSeriesTags from json.
func (o *OptDicomSeriesTags) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDicomSeriesTags to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDicomSeriesTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDicomSeriesTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes DicomStudyTags as json.
func (o OptDicomStudyTags) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes DicomStudyTags from json.
func (o *OptDicomStudyTags) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptDicomStudyTags to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptDicomStudyTags) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptDicomStudyTags) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes float64 as json.
func (o OptFloat64) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Float64(float64(o.Value))
}

// Decode decodes float64 from json.
func (o *OptFloat64) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptFloat64 to nil")
	}
	o.Set = true
	v, err := d.Float64()
	if err != nil {
		return err
	}
	o.Value = float64(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptFloat64) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptFloat64) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes GenerateMedreamToken as json.
func (o OptGenerateMedreamToken) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes GenerateMedreamToken from json.
func (o *OptGenerateMedreamToken) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptGenerateMedreamToken to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptGenerateMedreamToken) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptGenerateMedreamToken) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ImagingSource as json.
func (o OptImagingSource) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes ImagingSource from json.
func (o *OptImagingSource) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptImagingSource to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptImagingSource) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptImagingSource) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes int as json.
func (o OptInt) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Int(int(o.Value))
}

// Decode decodes int from json.
func (o *OptInt) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptInt to nil")
	}
	o.Set = true
	v, err := d.Int()
	if err != nil {
		return err
	}
	o.Value = int(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptInt) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptInt) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes int64 as json.
func (o OptInt64) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Int64(int64(o.Value))
}

// Decode decodes int64 from json.
func (o *OptInt64) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptInt64 to nil")
	}
	o.Set = true
	v, err := d.Int64()
	if err != nil {
		return err
	}
	o.Value = int64(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptInt64) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptInt64) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes MeddreamTokenItemsItemStudies as json.
func (o OptMeddreamTokenItemsItemStudies) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes MeddreamTokenItemsItemStudies from json.
func (o *OptMeddreamTokenItemsItemStudies) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptMeddreamTokenItemsItemStudies to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptMeddreamTokenItemsItemStudies) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptMeddreamTokenItemsItemStudies) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes []BusinessRuleResult as json.
func (o OptNilBusinessRuleResultArray) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	if o.Null {
		e.Null()
		return
	}
	e.ArrStart()
	for _, elem := range o.Value {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes []BusinessRuleResult from json.
func (o *OptNilBusinessRuleResultArray) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptNilBusinessRuleResultArray to nil")
	}
	if d.Next() == jx.Null {
		if err := d.Null(); err != nil {
			return err
		}

		var v []BusinessRuleResult
		o.Value = v
		o.Set = true
		o.Null = true
		return nil
	}
	o.Set = true
	o.Null = false
	o.Value = make([]BusinessRuleResult, 0)
	if err := d.Arr(func(d *jx.Decoder) error {
		var elem BusinessRuleResult
		if err := elem.Decode(d); err != nil {
			return err
		}
		o.Value = append(o.Value, elem)
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptNilBusinessRuleResultArray) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptNilBusinessRuleResultArray) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes []DicomInstance as json.
func (o OptNilDicomInstanceArray) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	if o.Null {
		e.Null()
		return
	}
	e.ArrStart()
	for _, elem := range o.Value {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes []DicomInstance from json.
func (o *OptNilDicomInstanceArray) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptNilDicomInstanceArray to nil")
	}
	if d.Next() == jx.Null {
		if err := d.Null(); err != nil {
			return err
		}

		var v []DicomInstance
		o.Value = v
		o.Set = true
		o.Null = true
		return nil
	}
	o.Set = true
	o.Null = false
	o.Value = make([]DicomInstance, 0)
	if err := d.Arr(func(d *jx.Decoder) error {
		var elem DicomInstance
		if err := elem.Decode(d); err != nil {
			return err
		}
		o.Value = append(o.Value, elem)
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptNilDicomInstanceArray) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptNilDicomInstanceArray) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes []PermissionGroup as json.
func (o OptNilPermissionGroupArray) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	if o.Null {
		e.Null()
		return
	}
	e.ArrStart()
	for _, elem := range o.Value {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes []PermissionGroup from json.
func (o *OptNilPermissionGroupArray) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptNilPermissionGroupArray to nil")
	}
	if d.Next() == jx.Null {
		if err := d.Null(); err != nil {
			return err
		}

		var v []PermissionGroup
		o.Value = v
		o.Set = true
		o.Null = true
		return nil
	}
	o.Set = true
	o.Null = false
	o.Value = make([]PermissionGroup, 0)
	if err := d.Arr(func(d *jx.Decoder) error {
		var elem PermissionGroup
		if err := elem.Decode(d); err != nil {
			return err
		}
		o.Value = append(o.Value, elem)
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptNilPermissionGroupArray) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptNilPermissionGroupArray) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes string as json.
func (o OptNilString) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	if o.Null {
		e.Null()
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes string from json.
func (o *OptNilString) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptNilString to nil")
	}
	if d.Next() == jx.Null {
		if err := d.Null(); err != nil {
			return err
		}

		var v string
		o.Value = v
		o.Set = true
		o.Null = true
		return nil
	}
	o.Set = true
	o.Null = false
	v, err := d.Str()
	if err != nil {
		return err
	}
	o.Value = string(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptNilString) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptNilString) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes []StudyReport as json.
func (o OptNilStudyReportArray) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	if o.Null {
		e.Null()
		return
	}
	e.ArrStart()
	for _, elem := range o.Value {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes []StudyReport from json.
func (o *OptNilStudyReportArray) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptNilStudyReportArray to nil")
	}
	if d.Next() == jx.Null {
		if err := d.Null(); err != nil {
			return err
		}

		var v []StudyReport
		o.Value = v
		o.Set = true
		o.Null = true
		return nil
	}
	o.Set = true
	o.Null = false
	o.Value = make([]StudyReport, 0)
	if err := d.Arr(func(d *jx.Decoder) error {
		var elem StudyReport
		if err := elem.Decode(d); err != nil {
			return err
		}
		o.Value = append(o.Value, elem)
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptNilStudyReportArray) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptNilStudyReportArray) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes PostV1BusinessRulesetsEvaluateReq as json.
func (o OptPostV1BusinessRulesetsEvaluateReq) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes PostV1BusinessRulesetsEvaluateReq from json.
func (o *OptPostV1BusinessRulesetsEvaluateReq) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptPostV1BusinessRulesetsEvaluateReq to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptPostV1BusinessRulesetsEvaluateReq) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptPostV1BusinessRulesetsEvaluateReq) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ProviderShareSearch as json.
func (o OptProviderShareSearch) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes ProviderShareSearch from json.
func (o *OptProviderShareSearch) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptProviderShareSearch to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptProviderShareSearch) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptProviderShareSearch) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ReportType as json.
func (o OptReportType) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes ReportType from json.
func (o *OptReportType) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptReportType to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptReportType) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptReportType) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes Rule as json.
func (o OptRule) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes Rule from json.
func (o *OptRule) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptRule to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptRule) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptRule) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleLogic as json.
func (o OptRuleLogic) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes RuleLogic from json.
func (o *OptRuleLogic) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptRuleLogic to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptRuleLogic) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptRuleLogic) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleOperator as json.
func (o OptRuleOperator) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes RuleOperator from json.
func (o *OptRuleOperator) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptRuleOperator to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptRuleOperator) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptRuleOperator) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleValue as json.
func (o OptRuleValue) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes RuleValue from json.
func (o *OptRuleValue) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptRuleValue to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptRuleValue) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptRuleValue) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes Series as json.
func (o OptSeries) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes Series from json.
func (o *OptSeries) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptSeries to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptSeries) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptSeries) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes string as json.
func (o OptString) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes string from json.
func (o *OptString) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptString to nil")
	}
	o.Set = true
	v, err := d.Str()
	if err != nil {
		return err
	}
	o.Value = string(v)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptString) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptString) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes StudyAttribution as json.
func (o OptStudyAttribution) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	o.Value.Encode(e)
}

// Decode decodes StudyAttribution from json.
func (o *OptStudyAttribution) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptStudyAttribution to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptStudyAttribution) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptStudyAttribution) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes StudyAvailabilityStatus as json.
func (o OptStudyAvailabilityStatus) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes StudyAvailabilityStatus from json.
func (o *OptStudyAvailabilityStatus) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptStudyAvailabilityStatus to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptStudyAvailabilityStatus) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptStudyAvailabilityStatus) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes StudyUploadMetadataAccountType as json.
func (o OptStudyUploadMetadataAccountType) Encode(e *jx.Encoder) {
	if !o.Set {
		return
	}
	e.Str(string(o.Value))
}

// Decode decodes StudyUploadMetadataAccountType from json.
func (o *OptStudyUploadMetadataAccountType) Decode(d *jx.Decoder) error {
	if o == nil {
		return errors.New("invalid: unable to decode OptStudyUploadMetadataAccountType to nil")
	}
	o.Set = true
	if err := o.Value.Decode(d); err != nil {
		return err
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s OptStudyUploadMetadataAccountType) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *OptStudyUploadMetadataAccountType) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PatientAccountPermission) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PatientAccountPermission) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("account_id")
		e.Str(s.AccountID)
	}
	{
		e.FieldStart("patient_id")
		e.Str(s.PatientID)
	}
	{
		e.FieldStart("study_availability_status")
		s.StudyAvailabilityStatus.Encode(e)
	}
}

var jsonFieldsNameOfPatientAccountPermission = [3]string{
	0: "account_id",
	1: "patient_id",
	2: "study_availability_status",
}

// Decode decodes PatientAccountPermission from json.
func (s *PatientAccountPermission) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PatientAccountPermission to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "account_id":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Str()
				s.AccountID = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"account_id\"")
			}
		case "patient_id":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.PatientID = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_id\"")
			}
		case "study_availability_status":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				if err := s.StudyAvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_availability_status\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PatientAccountPermission")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000111,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfPatientAccountPermission) {
					name = jsonFieldsNameOfPatientAccountPermission[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PatientAccountPermission) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PatientAccountPermission) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PatientStudy) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PatientStudy) encodeFields(e *jx.Encoder) {
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		if s.PatientID.Set {
			e.FieldStart("patient_id")
			s.PatientID.Encode(e)
		}
	}
	{
		if s.OrganizationID.Set {
			e.FieldStart("organization_id")
			s.OrganizationID.Encode(e)
		}
	}
	{
		if s.ActivatedTimestamp.Set {
			e.FieldStart("activated_timestamp")
			s.ActivatedTimestamp.Encode(e, json.EncodeDateTime)
		}
	}
	{
		if s.AvailabilityStatus.Set {
			e.FieldStart("availability_status")
			s.AvailabilityStatus.Encode(e)
		}
	}
	{
		if s.OrderID.Set {
			e.FieldStart("order_id")
			s.OrderID.Encode(e)
		}
	}
	{
		if s.TransferID.Set {
			e.FieldStart("transfer_id")
			s.TransferID.Encode(e)
		}
	}
	{
		if s.InstanceUploadProgressPercent.Set {
			e.FieldStart("instance_upload_progress_percent")
			s.InstanceUploadProgressPercent.Encode(e)
		}
	}
	{
		if s.DicomPatientTags.Set {
			e.FieldStart("dicom_patient_tags")
			s.DicomPatientTags.Encode(e)
		}
	}
	{
		if s.DicomStudyTags.Set {
			e.FieldStart("dicom_study_tags")
			s.DicomStudyTags.Encode(e)
		}
	}
	{
		if s.Series != nil {
			e.FieldStart("series")
			e.ArrStart()
			for _, elem := range s.Series {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Reports.Set {
			e.FieldStart("reports")
			s.Reports.Encode(e)
		}
	}
	{
		if s.HasReport.Set {
			e.FieldStart("has_report")
			s.HasReport.Encode(e)
		}
	}
}

var jsonFieldsNameOfPatientStudy = [13]string{
	0:  "uuid",
	1:  "patient_id",
	2:  "organization_id",
	3:  "activated_timestamp",
	4:  "availability_status",
	5:  "order_id",
	6:  "transfer_id",
	7:  "instance_upload_progress_percent",
	8:  "dicom_patient_tags",
	9:  "dicom_study_tags",
	10: "series",
	11: "reports",
	12: "has_report",
}

// Decode decodes PatientStudy from json.
func (s *PatientStudy) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PatientStudy to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "patient_id":
			if err := func() error {
				s.PatientID.Reset()
				if err := s.PatientID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_id\"")
			}
		case "organization_id":
			if err := func() error {
				s.OrganizationID.Reset()
				if err := s.OrganizationID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"organization_id\"")
			}
		case "activated_timestamp":
			if err := func() error {
				s.ActivatedTimestamp.Reset()
				if err := s.ActivatedTimestamp.Decode(d, json.DecodeDateTime); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"activated_timestamp\"")
			}
		case "availability_status":
			if err := func() error {
				s.AvailabilityStatus.Reset()
				if err := s.AvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"availability_status\"")
			}
		case "order_id":
			if err := func() error {
				s.OrderID.Reset()
				if err := s.OrderID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"order_id\"")
			}
		case "transfer_id":
			if err := func() error {
				s.TransferID.Reset()
				if err := s.TransferID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_id\"")
			}
		case "instance_upload_progress_percent":
			if err := func() error {
				s.InstanceUploadProgressPercent.Reset()
				if err := s.InstanceUploadProgressPercent.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance_upload_progress_percent\"")
			}
		case "dicom_patient_tags":
			if err := func() error {
				s.DicomPatientTags.Reset()
				if err := s.DicomPatientTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_patient_tags\"")
			}
		case "dicom_study_tags":
			if err := func() error {
				s.DicomStudyTags.Reset()
				if err := s.DicomStudyTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_study_tags\"")
			}
		case "series":
			if err := func() error {
				s.Series = make([]DicomSeries, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem DicomSeries
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Series = append(s.Series, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series\"")
			}
		case "reports":
			if err := func() error {
				s.Reports.Reset()
				if err := s.Reports.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"reports\"")
			}
		case "has_report":
			if err := func() error {
				s.HasReport.Reset()
				if err := s.HasReport.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"has_report\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PatientStudy")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PatientStudy) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PatientStudy) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PermissionGroup) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PermissionGroup) encodeFields(e *jx.Encoder) {
	{
		if s.GroupID.Set {
			e.FieldStart("group_id")
			s.GroupID.Encode(e)
		}
	}
	{
		if s.GroupName.Set {
			e.FieldStart("groupName")
			s.GroupName.Encode(e)
		}
	}
}

var jsonFieldsNameOfPermissionGroup = [2]string{
	0: "group_id",
	1: "groupName",
}

// Decode decodes PermissionGroup from json.
func (s *PermissionGroup) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PermissionGroup to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "group_id":
			if err := func() error {
				s.GroupID.Reset()
				if err := s.GroupID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"group_id\"")
			}
		case "groupName":
			if err := func() error {
				s.GroupName.Reset()
				if err := s.GroupName.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"groupName\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PermissionGroup")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PermissionGroup) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PermissionGroup) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes PhysicianPatientStudies as json.
func (s PhysicianPatientStudies) Encode(e *jx.Encoder) {
	unwrapped := []PhysicianPatientStudy(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes PhysicianPatientStudies from json.
func (s *PhysicianPatientStudies) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PhysicianPatientStudies to nil")
	}
	var unwrapped []PhysicianPatientStudy
	if err := func() error {
		unwrapped = make([]PhysicianPatientStudy, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem PhysicianPatientStudy
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = PhysicianPatientStudies(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s PhysicianPatientStudies) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PhysicianPatientStudies) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PhysicianPatientStudy) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PhysicianPatientStudy) encodeFields(e *jx.Encoder) {
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		if s.PatientID.Set {
			e.FieldStart("patient_id")
			s.PatientID.Encode(e)
		}
	}
	{
		if s.OrganizationID.Set {
			e.FieldStart("organization_id")
			s.OrganizationID.Encode(e)
		}
	}
	{
		if s.ActivatedTimestamp.Set {
			e.FieldStart("activated_timestamp")
			s.ActivatedTimestamp.Encode(e, json.EncodeDateTime)
		}
	}
	{
		if s.AvailabilityStatus.Set {
			e.FieldStart("availability_status")
			s.AvailabilityStatus.Encode(e)
		}
	}
	{
		if s.OrderID.Set {
			e.FieldStart("order_id")
			s.OrderID.Encode(e)
		}
	}
	{
		if s.TransferID.Set {
			e.FieldStart("transfer_id")
			s.TransferID.Encode(e)
		}
	}
	{
		if s.InstanceUploadProgressPercent.Set {
			e.FieldStart("instance_upload_progress_percent")
			s.InstanceUploadProgressPercent.Encode(e)
		}
	}
	{
		if s.DicomPatientTags.Set {
			e.FieldStart("dicom_patient_tags")
			s.DicomPatientTags.Encode(e)
		}
	}
	{
		if s.DicomStudyTags.Set {
			e.FieldStart("dicom_study_tags")
			s.DicomStudyTags.Encode(e)
		}
	}
	{
		if s.Series != nil {
			e.FieldStart("series")
			e.ArrStart()
			for _, elem := range s.Series {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Reports.Set {
			e.FieldStart("reports")
			s.Reports.Encode(e)
		}
	}
	{
		if s.HasReport.Set {
			e.FieldStart("has_report")
			s.HasReport.Encode(e)
		}
	}
	{
		if s.PhysicianAccountID.Set {
			e.FieldStart("physician_account_id")
			s.PhysicianAccountID.Encode(e)
		}
	}
	{
		if s.PermissionGroups.Set {
			e.FieldStart("permission_groups")
			s.PermissionGroups.Encode(e)
		}
	}
}

var jsonFieldsNameOfPhysicianPatientStudy = [15]string{
	0:  "uuid",
	1:  "patient_id",
	2:  "organization_id",
	3:  "activated_timestamp",
	4:  "availability_status",
	5:  "order_id",
	6:  "transfer_id",
	7:  "instance_upload_progress_percent",
	8:  "dicom_patient_tags",
	9:  "dicom_study_tags",
	10: "series",
	11: "reports",
	12: "has_report",
	13: "physician_account_id",
	14: "permission_groups",
}

// Decode decodes PhysicianPatientStudy from json.
func (s *PhysicianPatientStudy) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PhysicianPatientStudy to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "patient_id":
			if err := func() error {
				s.PatientID.Reset()
				if err := s.PatientID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patient_id\"")
			}
		case "organization_id":
			if err := func() error {
				s.OrganizationID.Reset()
				if err := s.OrganizationID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"organization_id\"")
			}
		case "activated_timestamp":
			if err := func() error {
				s.ActivatedTimestamp.Reset()
				if err := s.ActivatedTimestamp.Decode(d, json.DecodeDateTime); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"activated_timestamp\"")
			}
		case "availability_status":
			if err := func() error {
				s.AvailabilityStatus.Reset()
				if err := s.AvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"availability_status\"")
			}
		case "order_id":
			if err := func() error {
				s.OrderID.Reset()
				if err := s.OrderID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"order_id\"")
			}
		case "transfer_id":
			if err := func() error {
				s.TransferID.Reset()
				if err := s.TransferID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"transfer_id\"")
			}
		case "instance_upload_progress_percent":
			if err := func() error {
				s.InstanceUploadProgressPercent.Reset()
				if err := s.InstanceUploadProgressPercent.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance_upload_progress_percent\"")
			}
		case "dicom_patient_tags":
			if err := func() error {
				s.DicomPatientTags.Reset()
				if err := s.DicomPatientTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_patient_tags\"")
			}
		case "dicom_study_tags":
			if err := func() error {
				s.DicomStudyTags.Reset()
				if err := s.DicomStudyTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_study_tags\"")
			}
		case "series":
			if err := func() error {
				s.Series = make([]DicomSeries, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem DicomSeries
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Series = append(s.Series, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series\"")
			}
		case "reports":
			if err := func() error {
				s.Reports.Reset()
				if err := s.Reports.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"reports\"")
			}
		case "has_report":
			if err := func() error {
				s.HasReport.Reset()
				if err := s.HasReport.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"has_report\"")
			}
		case "physician_account_id":
			if err := func() error {
				s.PhysicianAccountID.Reset()
				if err := s.PhysicianAccountID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"physician_account_id\"")
			}
		case "permission_groups":
			if err := func() error {
				s.PermissionGroups.Reset()
				if err := s.PermissionGroups.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"permission_groups\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PhysicianPatientStudy")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PhysicianPatientStudy) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PhysicianPatientStudy) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("error")
		e.Str(s.Error)
	}
}

var jsonFieldsNameOfPostV1BusinessRulesetsEvaluateErrorResponse = [1]string{
	0: "error",
}

// Decode decodes PostV1BusinessRulesetsEvaluateErrorResponse from json.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PostV1BusinessRulesetsEvaluateErrorResponse to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "error":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Str()
				s.Error = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"error\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PostV1BusinessRulesetsEvaluateErrorResponse")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000001,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfPostV1BusinessRulesetsEvaluateErrorResponse) {
					name = jsonFieldsNameOfPostV1BusinessRulesetsEvaluateErrorResponse[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PostV1BusinessRulesetsEvaluateErrorResponse) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateReq) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PostV1BusinessRulesetsEvaluateReq) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("ruleset")
		s.Ruleset.Encode(e)
	}
	{
		e.FieldStart("dicom_tags")
		s.DicomTags.Encode(e)
	}
}

var jsonFieldsNameOfPostV1BusinessRulesetsEvaluateReq = [2]string{
	0: "ruleset",
	1: "dicom_tags",
}

// Decode decodes PostV1BusinessRulesetsEvaluateReq from json.
func (s *PostV1BusinessRulesetsEvaluateReq) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PostV1BusinessRulesetsEvaluateReq to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "ruleset":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				if err := s.Ruleset.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"ruleset\"")
			}
		case "dicom_tags":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				if err := s.DicomTags.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dicom_tags\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PostV1BusinessRulesetsEvaluateReq")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000011,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfPostV1BusinessRulesetsEvaluateReq) {
					name = jsonFieldsNameOfPostV1BusinessRulesetsEvaluateReq[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateReq) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PostV1BusinessRulesetsEvaluateReq) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("result")
		s.Result.Encode(e)
	}
	{
		e.FieldStart("query")
		e.Str(s.Query)
	}
	{
		e.FieldStart("business_rule_results")
		if s.BusinessRuleResults == nil {
			e.Null()
		} else {
			e.ArrStart()
			for _, elem := range s.BusinessRuleResults {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Error.Set {
			e.FieldStart("error")
			s.Error.Encode(e)
		}
	}
}

var jsonFieldsNameOfPostV1BusinessRulesetsEvaluateSuccessResponse = [4]string{
	0: "result",
	1: "query",
	2: "business_rule_results",
	3: "error",
}

// Decode decodes PostV1BusinessRulesetsEvaluateSuccessResponse from json.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PostV1BusinessRulesetsEvaluateSuccessResponse to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "result":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				if err := s.Result.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"result\"")
			}
		case "query":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.Query = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"query\"")
			}
		case "business_rule_results":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				switch tt := d.Next(); tt {
				case jx.Null:
					if err := d.Skip(); err != nil {
						return err
					}
				default:
					s.BusinessRuleResults = make([]BusinessRuleResult, 0)
					if err := d.Arr(func(d *jx.Decoder) error {
						var elem BusinessRuleResult
						if err := elem.Decode(d); err != nil {
							return err
						}
						s.BusinessRuleResults = append(s.BusinessRuleResults, elem)
						return nil
					}); err != nil {
						return err
					}
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"business_rule_results\"")
			}
		case "error":
			if err := func() error {
				s.Error.Reset()
				if err := s.Error.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"error\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode PostV1BusinessRulesetsEvaluateSuccessResponse")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000111,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfPostV1BusinessRulesetsEvaluateSuccessResponse) {
					name = jsonFieldsNameOfPostV1BusinessRulesetsEvaluateSuccessResponse[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponse) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes PostV1BusinessRulesetsEvaluateSuccessResponseResult as json.
func (s PostV1BusinessRulesetsEvaluateSuccessResponseResult) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes PostV1BusinessRulesetsEvaluateSuccessResponseResult from json.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponseResult) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode PostV1BusinessRulesetsEvaluateSuccessResponseResult to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch PostV1BusinessRulesetsEvaluateSuccessResponseResult(v) {
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked:
		*s = PostV1BusinessRulesetsEvaluateSuccessResponseResultBlocked
	case PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed:
		*s = PostV1BusinessRulesetsEvaluateSuccessResponseResultAllowed
	default:
		*s = PostV1BusinessRulesetsEvaluateSuccessResponseResult(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s PostV1BusinessRulesetsEvaluateSuccessResponseResult) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *PostV1BusinessRulesetsEvaluateSuccessResponseResult) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ProviderShare) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ProviderShare) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("id")
		e.Str(s.ID)
	}
	{
		e.FieldStart("access_code")
		e.Str(s.AccessCode)
	}
	{
		e.FieldStart("sending_org")
		e.Str(s.SendingOrg)
	}
	{
		if s.Recipient.Set {
			e.FieldStart("recipient")
			s.Recipient.Encode(e)
		}
	}
	{
		if s.NumExams.Set {
			e.FieldStart("num_exams")
			s.NumExams.Encode(e)
		}
	}
	{
		e.FieldStart("dob")
		json.EncodeDate(e, s.Dob)
	}
	{
		e.FieldStart("expires_at")
		json.EncodeDateTime(e, s.ExpiresAt)
	}
	{
		if s.ExtendedExpiresAt.Set {
			e.FieldStart("extended_expires_at")
			s.ExtendedExpiresAt.Encode(e, json.EncodeDateTime)
		}
	}
	{
		e.FieldStart("method")
		s.Method.Encode(e)
	}
}

var jsonFieldsNameOfProviderShare = [9]string{
	0: "id",
	1: "access_code",
	2: "sending_org",
	3: "recipient",
	4: "num_exams",
	5: "dob",
	6: "expires_at",
	7: "extended_expires_at",
	8: "method",
}

// Decode decodes ProviderShare from json.
func (s *ProviderShare) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ProviderShare to nil")
	}
	var requiredBitSet [2]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "id":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Str()
				s.ID = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"id\"")
			}
		case "access_code":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.AccessCode = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"access_code\"")
			}
		case "sending_org":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				v, err := d.Str()
				s.SendingOrg = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"sending_org\"")
			}
		case "recipient":
			if err := func() error {
				s.Recipient.Reset()
				if err := s.Recipient.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"recipient\"")
			}
		case "num_exams":
			if err := func() error {
				s.NumExams.Reset()
				if err := s.NumExams.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"num_exams\"")
			}
		case "dob":
			requiredBitSet[0] |= 1 << 5
			if err := func() error {
				v, err := json.DecodeDate(d)
				s.Dob = v
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dob\"")
			}
		case "expires_at":
			requiredBitSet[0] |= 1 << 6
			if err := func() error {
				v, err := json.DecodeDateTime(d)
				s.ExpiresAt = v
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"expires_at\"")
			}
		case "extended_expires_at":
			if err := func() error {
				s.ExtendedExpiresAt.Reset()
				if err := s.ExtendedExpiresAt.Decode(d, json.DecodeDateTime); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"extended_expires_at\"")
			}
		case "method":
			requiredBitSet[1] |= 1 << 0
			if err := func() error {
				if err := s.Method.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"method\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ProviderShare")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [2]uint8{
		0b01100111,
		0b00000001,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfProviderShare) {
					name = jsonFieldsNameOfProviderShare[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ProviderShare) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ProviderShare) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ProviderShareMethod as json.
func (s ProviderShareMethod) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes ProviderShareMethod from json.
func (s *ProviderShareMethod) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ProviderShareMethod to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch ProviderShareMethod(v) {
	case ProviderShareMethodEMAIL:
		*s = ProviderShareMethodEMAIL
	case ProviderShareMethodFAX:
		*s = ProviderShareMethodFAX
	case ProviderShareMethodPRINT:
		*s = ProviderShareMethodPRINT
	case ProviderShareMethodDOWNLOAD:
		*s = ProviderShareMethodDOWNLOAD
	default:
		*s = ProviderShareMethod(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s ProviderShareMethod) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ProviderShareMethod) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ProviderShareSearch) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ProviderShareSearch) encodeFields(e *jx.Encoder) {
	{
		if s.AccessCode.Set {
			e.FieldStart("access_code")
			s.AccessCode.Encode(e)
		}
	}
	{
		if s.Dob.Set {
			e.FieldStart("dob")
			s.Dob.Encode(e, json.EncodeDate)
		}
	}
}

var jsonFieldsNameOfProviderShareSearch = [2]string{
	0: "access_code",
	1: "dob",
}

// Decode decodes ProviderShareSearch from json.
func (s *ProviderShareSearch) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ProviderShareSearch to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "access_code":
			if err := func() error {
				s.AccessCode.Reset()
				if err := s.AccessCode.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"access_code\"")
			}
		case "dob":
			if err := func() error {
				s.Dob.Reset()
				if err := s.Dob.Decode(d, json.DecodeDate); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"dob\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ProviderShareSearch")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ProviderShareSearch) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ProviderShareSearch) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ProviderUploadOverview) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ProviderUploadOverview) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("provider_id")
		e.Int64(s.ProviderID)
	}
	{
		e.FieldStart("provider_name")
		e.Str(s.ProviderName)
	}
	{
		e.FieldStart("provider_upload_overviews")
		e.ArrStart()
		for _, elem := range s.ProviderUploadOverviews {
			elem.Encode(e)
		}
		e.ArrEnd()
	}
}

var jsonFieldsNameOfProviderUploadOverview = [3]string{
	0: "provider_id",
	1: "provider_name",
	2: "provider_upload_overviews",
}

// Decode decodes ProviderUploadOverview from json.
func (s *ProviderUploadOverview) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ProviderUploadOverview to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "provider_id":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Int64()
				s.ProviderID = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider_id\"")
			}
		case "provider_name":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.ProviderName = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider_name\"")
			}
		case "provider_upload_overviews":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				s.ProviderUploadOverviews = make([]ProviderUploadStatus, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem ProviderUploadStatus
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.ProviderUploadOverviews = append(s.ProviderUploadOverviews, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider_upload_overviews\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ProviderUploadOverview")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000111,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfProviderUploadOverview) {
					name = jsonFieldsNameOfProviderUploadOverview[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ProviderUploadOverview) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ProviderUploadOverview) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *ProviderUploadStatus) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *ProviderUploadStatus) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("time_bucket")
		json.EncodeDateTime(e, s.TimeBucket)
	}
	{
		e.FieldStart("average_instance_upload_progress")
		e.Int64(s.AverageInstanceUploadProgress)
	}
	{
		e.FieldStart("study_count")
		e.Int64(s.StudyCount)
	}
	{
		e.FieldStart("happy_user_count")
		e.Int64(s.HappyUserCount)
	}
	{
		e.FieldStart("sad_user_count")
		e.Int64(s.SadUserCount)
	}
}

var jsonFieldsNameOfProviderUploadStatus = [5]string{
	0: "time_bucket",
	1: "average_instance_upload_progress",
	2: "study_count",
	3: "happy_user_count",
	4: "sad_user_count",
}

// Decode decodes ProviderUploadStatus from json.
func (s *ProviderUploadStatus) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ProviderUploadStatus to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "time_bucket":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := json.DecodeDateTime(d)
				s.TimeBucket = v
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"time_bucket\"")
			}
		case "average_instance_upload_progress":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Int64()
				s.AverageInstanceUploadProgress = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"average_instance_upload_progress\"")
			}
		case "study_count":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				v, err := d.Int64()
				s.StudyCount = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_count\"")
			}
		case "happy_user_count":
			requiredBitSet[0] |= 1 << 3
			if err := func() error {
				v, err := d.Int64()
				s.HappyUserCount = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"happy_user_count\"")
			}
		case "sad_user_count":
			requiredBitSet[0] |= 1 << 4
			if err := func() error {
				v, err := d.Int64()
				s.SadUserCount = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"sad_user_count\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode ProviderUploadStatus")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00011111,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfProviderUploadStatus) {
					name = jsonFieldsNameOfProviderUploadStatus[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *ProviderUploadStatus) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ProviderUploadStatus) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *Report) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *Report) encodeFields(e *jx.Encoder) {
	{
		if s.ID.Set {
			e.FieldStart("id")
			s.ID.Encode(e)
		}
	}
	{
		if s.Type.Set {
			e.FieldStart("type")
			s.Type.Encode(e)
		}
	}
	{
		if s.Date.Set {
			e.FieldStart("date")
			s.Date.Encode(e)
		}
	}
}

var jsonFieldsNameOfReport = [3]string{
	0: "id",
	1: "type",
	2: "date",
}

// Decode decodes Report from json.
func (s *Report) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode Report to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "id":
			if err := func() error {
				s.ID.Reset()
				if err := s.ID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"id\"")
			}
		case "type":
			if err := func() error {
				s.Type.Reset()
				if err := s.Type.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"type\"")
			}
		case "date":
			if err := func() error {
				s.Date.Reset()
				if err := s.Date.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"date\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode Report")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *Report) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *Report) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes ReportType as json.
func (s ReportType) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes ReportType from json.
func (s *ReportType) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode ReportType to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch ReportType(v) {
	case ReportTypeER:
		*s = ReportTypeER
	case ReportTypeORIGINAL:
		*s = ReportTypeORIGINAL
	default:
		*s = ReportType(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s ReportType) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *ReportType) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *Rule) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *Rule) encodeFields(e *jx.Encoder) {
	{
		if s.Logic.Set {
			e.FieldStart("logic")
			s.Logic.Encode(e)
		}
	}
	{
		if s.Rules != nil {
			e.FieldStart("rules")
			e.ArrStart()
			for _, elem := range s.Rules {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Tag.Set {
			e.FieldStart("tag")
			s.Tag.Encode(e)
		}
	}
	{
		if s.TagName.Set {
			e.FieldStart("tagName")
			s.TagName.Encode(e)
		}
	}
	{
		if s.Description.Set {
			e.FieldStart("description")
			s.Description.Encode(e)
		}
	}
	{
		if s.Operator.Set {
			e.FieldStart("operator")
			s.Operator.Encode(e)
		}
	}
	{
		if s.Value.Set {
			e.FieldStart("value")
			s.Value.Encode(e)
		}
	}
}

var jsonFieldsNameOfRule = [7]string{
	0: "logic",
	1: "rules",
	2: "tag",
	3: "tagName",
	4: "description",
	5: "operator",
	6: "value",
}

// Decode decodes Rule from json.
func (s *Rule) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode Rule to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "logic":
			if err := func() error {
				s.Logic.Reset()
				if err := s.Logic.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"logic\"")
			}
		case "rules":
			if err := func() error {
				s.Rules = make([]Rule, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem Rule
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Rules = append(s.Rules, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"rules\"")
			}
		case "tag":
			if err := func() error {
				s.Tag.Reset()
				if err := s.Tag.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"tag\"")
			}
		case "tagName":
			if err := func() error {
				s.TagName.Reset()
				if err := s.TagName.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"tagName\"")
			}
		case "description":
			if err := func() error {
				s.Description.Reset()
				if err := s.Description.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"description\"")
			}
		case "operator":
			if err := func() error {
				s.Operator.Reset()
				if err := s.Operator.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"operator\"")
			}
		case "value":
			if err := func() error {
				s.Value.Reset()
				if err := s.Value.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"value\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode Rule")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *Rule) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *Rule) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleLogic as json.
func (s RuleLogic) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes RuleLogic from json.
func (s *RuleLogic) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode RuleLogic to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch RuleLogic(v) {
	case RuleLogicAND:
		*s = RuleLogicAND
	case RuleLogicOR:
		*s = RuleLogicOR
	default:
		*s = RuleLogic(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s RuleLogic) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *RuleLogic) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleOperator as json.
func (s RuleOperator) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes RuleOperator from json.
func (s *RuleOperator) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode RuleOperator to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch RuleOperator(v) {
	case RuleOperator_0:
		*s = RuleOperator_0
	case RuleOperator_1:
		*s = RuleOperator_1
	case RuleOperator_2:
		*s = RuleOperator_2
	case RuleOperator_3:
		*s = RuleOperator_3
	case RuleOperator_4:
		*s = RuleOperator_4
	case RuleOperator_5:
		*s = RuleOperator_5
	case RuleOperator_6:
		*s = RuleOperator_6
	case RuleOperator_7:
		*s = RuleOperator_7
	case RuleOperator_8:
		*s = RuleOperator_8
	case RuleOperator_9:
		*s = RuleOperator_9
	case RuleOperator_10:
		*s = RuleOperator_10
	case RuleOperator_11:
		*s = RuleOperator_11
	case RuleOperator_12:
		*s = RuleOperator_12
	case RuleOperator_13:
		*s = RuleOperator_13
	case RuleOperator_14:
		*s = RuleOperator_14
	case RuleOperator_15:
		*s = RuleOperator_15
	case RuleOperator_16:
		*s = RuleOperator_16
	case RuleOperator_17:
		*s = RuleOperator_17
	default:
		*s = RuleOperator(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s RuleOperator) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *RuleOperator) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes RuleValue as json.
func (s RuleValue) Encode(e *jx.Encoder) {
	switch s.Type {
	case StringRuleValue:
		e.Str(s.String)
	case Float64RuleValue:
		e.Float64(s.Float64)
	}
}

// Decode decodes RuleValue from json.
func (s *RuleValue) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode RuleValue to nil")
	}
	// Sum type type_discriminator.
	switch t := d.Next(); t {
	case jx.Number:
		v, err := d.Float64()
		s.Float64 = float64(v)
		if err != nil {
			return err
		}
		s.Type = Float64RuleValue
	case jx.String:
		v, err := d.Str()
		s.String = string(v)
		if err != nil {
			return err
		}
		s.Type = StringRuleValue
	default:
		return errors.Errorf("unexpected json type %q", t)
	}
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s RuleValue) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *RuleValue) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *Ruleset) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *Ruleset) encodeFields(e *jx.Encoder) {
	{
		if s.Block.Set {
			e.FieldStart("block")
			s.Block.Encode(e)
		}
	}
}

var jsonFieldsNameOfRuleset = [1]string{
	0: "block",
}

// Decode decodes Ruleset from json.
func (s *Ruleset) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode Ruleset to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "block":
			if err := func() error {
				s.Block.Reset()
				if err := s.Block.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"block\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode Ruleset")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *Ruleset) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *Ruleset) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *Series) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *Series) encodeFields(e *jx.Encoder) {
	{
		if s.SeriesUID.Set {
			e.FieldStart("series_uid")
			s.SeriesUID.Encode(e)
		}
	}
	{
		if s.Description.Set {
			e.FieldStart("description")
			s.Description.Encode(e)
		}
	}
	{
		if s.NumImages.Set {
			e.FieldStart("num_images")
			s.NumImages.Encode(e)
		}
	}
}

var jsonFieldsNameOfSeries = [3]string{
	0: "series_uid",
	1: "description",
	2: "num_images",
}

// Decode decodes Series from json.
func (s *Series) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode Series to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "series_uid":
			if err := func() error {
				s.SeriesUID.Reset()
				if err := s.SeriesUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"series_uid\"")
			}
		case "description":
			if err := func() error {
				s.Description.Reset()
				if err := s.Description.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"description\"")
			}
		case "num_images":
			if err := func() error {
				s.NumImages.Reset()
				if err := s.NumImages.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"num_images\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode Series")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *Series) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *Series) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyAccessPermissions) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyAccessPermissions) encodeFields(e *jx.Encoder) {
	{
		if s.Physicians != nil {
			e.FieldStart("physicians")
			e.ArrStart()
			for _, elem := range s.Physicians {
				e.Str(elem)
			}
			e.ArrEnd()
		}
	}
	{
		if s.Patients != nil {
			e.FieldStart("patients")
			e.ArrStart()
			for _, elem := range s.Patients {
				elem.Encode(e)
			}
			e.ArrEnd()
		}
	}
}

var jsonFieldsNameOfStudyAccessPermissions = [2]string{
	0: "physicians",
	1: "patients",
}

// Decode decodes StudyAccessPermissions from json.
func (s *StudyAccessPermissions) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyAccessPermissions to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "physicians":
			if err := func() error {
				s.Physicians = make([]string, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem string
					v, err := d.Str()
					elem = string(v)
					if err != nil {
						return err
					}
					s.Physicians = append(s.Physicians, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"physicians\"")
			}
		case "patients":
			if err := func() error {
				s.Patients = make([]PatientAccountPermission, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem PatientAccountPermission
					if err := elem.Decode(d); err != nil {
						return err
					}
					s.Patients = append(s.Patients, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patients\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyAccessPermissions")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyAccessPermissions) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyAccessPermissions) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyAttribution) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyAttribution) encodeFields(e *jx.Encoder) {
	{
		if s.Uuids != nil {
			e.FieldStart("uuids")
			e.ArrStart()
			for _, elem := range s.Uuids {
				e.Str(elem)
			}
			e.ArrEnd()
		}
	}
	{
		if s.OrderId.Set {
			e.FieldStart("orderId")
			s.OrderId.Encode(e)
		}
	}
	{
		if s.AvailabilityStatus.Set {
			e.FieldStart("availabilityStatus")
			s.AvailabilityStatus.Encode(e)
		}
	}
}

var jsonFieldsNameOfStudyAttribution = [3]string{
	0: "uuids",
	1: "orderId",
	2: "availabilityStatus",
}

// Decode decodes StudyAttribution from json.
func (s *StudyAttribution) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyAttribution to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuids":
			if err := func() error {
				s.Uuids = make([]string, 0)
				if err := d.Arr(func(d *jx.Decoder) error {
					var elem string
					v, err := d.Str()
					elem = string(v)
					if err != nil {
						return err
					}
					s.Uuids = append(s.Uuids, elem)
					return nil
				}); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuids\"")
			}
		case "orderId":
			if err := func() error {
				s.OrderId.Reset()
				if err := s.OrderId.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"orderId\"")
			}
		case "availabilityStatus":
			if err := func() error {
				s.AvailabilityStatus.Reset()
				if err := s.AvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"availabilityStatus\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyAttribution")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyAttribution) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyAttribution) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes StudyAvailabilityStatus as json.
func (s StudyAvailabilityStatus) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes StudyAvailabilityStatus from json.
func (s *StudyAvailabilityStatus) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyAvailabilityStatus to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch StudyAvailabilityStatus(v) {
	case StudyAvailabilityStatusLocked:
		*s = StudyAvailabilityStatusLocked
	case StudyAvailabilityStatusFullAccess:
		*s = StudyAvailabilityStatusFullAccess
	case StudyAvailabilityStatusLimitedAvailability:
		*s = StudyAvailabilityStatusLimitedAvailability
	default:
		*s = StudyAvailabilityStatus(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s StudyAvailabilityStatus) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyAvailabilityStatus) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyIdentifier) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyIdentifier) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("study_uid")
		e.Str(s.StudyUID)
	}
	{
		e.FieldStart("provider_id")
		e.Int64(s.ProviderID)
	}
}

var jsonFieldsNameOfStudyIdentifier = [2]string{
	0: "study_uid",
	1: "provider_id",
}

// Decode decodes StudyIdentifier from json.
func (s *StudyIdentifier) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyIdentifier to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "study_uid":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Str()
				s.StudyUID = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_uid\"")
			}
		case "provider_id":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Int64()
				s.ProviderID = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider_id\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyIdentifier")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00000011,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfStudyIdentifier) {
					name = jsonFieldsNameOfStudyIdentifier[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyIdentifier) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyIdentifier) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyReport) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyReport) encodeFields(e *jx.Encoder) {
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		if s.FileSizeBytes.Set {
			e.FieldStart("file_size_bytes")
			s.FileSizeBytes.Encode(e)
		}
	}
	{
		if s.EncapsulatedPdf.Set {
			e.FieldStart("encapsulated_pdf")
			s.EncapsulatedPdf.Encode(e)
		}
	}
	{
		if s.Type.Set {
			e.FieldStart("type")
			s.Type.Encode(e)
		}
	}
}

var jsonFieldsNameOfStudyReport = [4]string{
	0: "uuid",
	1: "file_size_bytes",
	2: "encapsulated_pdf",
	3: "type",
}

// Decode decodes StudyReport from json.
func (s *StudyReport) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyReport to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "file_size_bytes":
			if err := func() error {
				s.FileSizeBytes.Reset()
				if err := s.FileSizeBytes.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"file_size_bytes\"")
			}
		case "encapsulated_pdf":
			if err := func() error {
				s.EncapsulatedPdf.Reset()
				if err := s.EncapsulatedPdf.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"encapsulated_pdf\"")
			}
		case "type":
			if err := func() error {
				s.Type.Reset()
				if err := s.Type.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"type\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyReport")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyReport) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyReport) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyUploadMetadata) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyUploadMetadata) encodeFields(e *jx.Encoder) {
	{
		if s.AccountType.Set {
			e.FieldStart("account_type")
			s.AccountType.Encode(e)
		}
	}
	{
		if s.AccountID.Set {
			e.FieldStart("account_id")
			s.AccountID.Encode(e)
		}
	}
	{
		if s.ProviderID.Set {
			e.FieldStart("provider_id")
			s.ProviderID.Encode(e)
		}
	}
	{
		if s.StudyUID.Set {
			e.FieldStart("study_uid")
			s.StudyUID.Encode(e)
		}
	}
	{
		if s.InstanceCount.Set {
			e.FieldStart("instance_count")
			s.InstanceCount.Encode(e)
		}
	}
	{
		if s.InstancesUploaded.Set {
			e.FieldStart("instances_uploaded")
			s.InstancesUploaded.Encode(e)
		}
	}
	{
		if s.CreatedTimestampUtc.Set {
			e.FieldStart("created_timestamp_utc")
			s.CreatedTimestampUtc.Encode(e, json.EncodeDateTime)
		}
	}
}

var jsonFieldsNameOfStudyUploadMetadata = [7]string{
	0: "account_type",
	1: "account_id",
	2: "provider_id",
	3: "study_uid",
	4: "instance_count",
	5: "instances_uploaded",
	6: "created_timestamp_utc",
}

// Decode decodes StudyUploadMetadata from json.
func (s *StudyUploadMetadata) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyUploadMetadata to nil")
	}

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "account_type":
			if err := func() error {
				s.AccountType.Reset()
				if err := s.AccountType.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"account_type\"")
			}
		case "account_id":
			if err := func() error {
				s.AccountID.Reset()
				if err := s.AccountID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"account_id\"")
			}
		case "provider_id":
			if err := func() error {
				s.ProviderID.Reset()
				if err := s.ProviderID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"provider_id\"")
			}
		case "study_uid":
			if err := func() error {
				s.StudyUID.Reset()
				if err := s.StudyUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_uid\"")
			}
		case "instance_count":
			if err := func() error {
				s.InstanceCount.Reset()
				if err := s.InstanceCount.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instance_count\"")
			}
		case "instances_uploaded":
			if err := func() error {
				s.InstancesUploaded.Reset()
				if err := s.InstancesUploaded.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"instances_uploaded\"")
			}
		case "created_timestamp_utc":
			if err := func() error {
				s.CreatedTimestampUtc.Reset()
				if err := s.CreatedTimestampUtc.Decode(d, json.DecodeDateTime); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"created_timestamp_utc\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyUploadMetadata")
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyUploadMetadata) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyUploadMetadata) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes StudyUploadMetadataAccountType as json.
func (s StudyUploadMetadataAccountType) Encode(e *jx.Encoder) {
	e.Str(string(s))
}

// Decode decodes StudyUploadMetadataAccountType from json.
func (s *StudyUploadMetadataAccountType) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyUploadMetadataAccountType to nil")
	}
	v, err := d.StrBytes()
	if err != nil {
		return err
	}
	// Try to use constant string.
	switch StudyUploadMetadataAccountType(v) {
	case StudyUploadMetadataAccountTypePATIENT:
		*s = StudyUploadMetadataAccountTypePATIENT
	case StudyUploadMetadataAccountTypePHYSICIAN:
		*s = StudyUploadMetadataAccountTypePHYSICIAN
	default:
		*s = StudyUploadMetadataAccountType(v)
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s StudyUploadMetadataAccountType) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyUploadMetadataAccountType) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *StudyUploadStatus) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *StudyUploadStatus) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("study_identifier")
		s.StudyIdentifier.Encode(e)
	}
	{
		e.FieldStart("uuid")
		e.Str(s.UUID)
	}
	{
		e.FieldStart("has_report")
		e.Bool(s.HasReport)
	}
	{
		e.FieldStart("progress_percent")
		e.Int(s.ProgressPercent)
	}
}

var jsonFieldsNameOfStudyUploadStatus = [4]string{
	0: "study_identifier",
	1: "uuid",
	2: "has_report",
	3: "progress_percent",
}

// Decode decodes StudyUploadStatus from json.
func (s *StudyUploadStatus) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode StudyUploadStatus to nil")
	}
	var requiredBitSet [1]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "study_identifier":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				if err := s.StudyIdentifier.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_identifier\"")
			}
		case "uuid":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.UUID = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "has_report":
			requiredBitSet[0] |= 1 << 2
			if err := func() error {
				v, err := d.Bool()
				s.HasReport = bool(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"has_report\"")
			}
		case "progress_percent":
			requiredBitSet[0] |= 1 << 3
			if err := func() error {
				v, err := d.Int()
				s.ProgressPercent = int(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"progress_percent\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode StudyUploadStatus")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [1]uint8{
		0b00001111,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfStudyUploadStatus) {
					name = jsonFieldsNameOfStudyUploadStatus[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *StudyUploadStatus) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *StudyUploadStatus) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode implements json.Marshaler.
func (s *UploadStatus) Encode(e *jx.Encoder) {
	e.ObjStart()
	s.encodeFields(e)
	e.ObjEnd()
}

// encodeFields encodes fields.
func (s *UploadStatus) encodeFields(e *jx.Encoder) {
	{
		e.FieldStart("orgId")
		e.Int64(s.OrgId)
	}
	{
		e.FieldStart("examId")
		e.Str(s.ExamId)
	}
	{
		if s.UUID.Set {
			e.FieldStart("uuid")
			s.UUID.Encode(e)
		}
	}
	{
		e.FieldStart("progressPercent")
		e.Int(s.ProgressPercent)
	}
	{
		e.FieldStart("modality")
		e.Str(s.Modality)
	}
	{
		e.FieldStart("examType")
		e.Str(s.ExamType)
	}
	{
		e.FieldStart("examDate")
		e.Str(s.ExamDate)
	}
	{
		e.FieldStart("description")
		e.Str(s.Description)
	}
	{
		e.FieldStart("orgName")
		e.Str(s.OrgName)
	}
	{
		if s.PatientId.Set {
			e.FieldStart("patientId")
			s.PatientId.Encode(e)
		}
	}
	{
		if s.StudyAvailabilityStatus.Set {
			e.FieldStart("study_availability_status")
			s.StudyAvailabilityStatus.Encode(e)
		}
	}
}

var jsonFieldsNameOfUploadStatus = [11]string{
	0:  "orgId",
	1:  "examId",
	2:  "uuid",
	3:  "progressPercent",
	4:  "modality",
	5:  "examType",
	6:  "examDate",
	7:  "description",
	8:  "orgName",
	9:  "patientId",
	10: "study_availability_status",
}

// Decode decodes UploadStatus from json.
func (s *UploadStatus) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode UploadStatus to nil")
	}
	var requiredBitSet [2]uint8

	if err := d.ObjBytes(func(d *jx.Decoder, k []byte) error {
		switch string(k) {
		case "orgId":
			requiredBitSet[0] |= 1 << 0
			if err := func() error {
				v, err := d.Int64()
				s.OrgId = int64(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"orgId\"")
			}
		case "examId":
			requiredBitSet[0] |= 1 << 1
			if err := func() error {
				v, err := d.Str()
				s.ExamId = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"examId\"")
			}
		case "uuid":
			if err := func() error {
				s.UUID.Reset()
				if err := s.UUID.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"uuid\"")
			}
		case "progressPercent":
			requiredBitSet[0] |= 1 << 3
			if err := func() error {
				v, err := d.Int()
				s.ProgressPercent = int(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"progressPercent\"")
			}
		case "modality":
			requiredBitSet[0] |= 1 << 4
			if err := func() error {
				v, err := d.Str()
				s.Modality = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"modality\"")
			}
		case "examType":
			requiredBitSet[0] |= 1 << 5
			if err := func() error {
				v, err := d.Str()
				s.ExamType = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"examType\"")
			}
		case "examDate":
			requiredBitSet[0] |= 1 << 6
			if err := func() error {
				v, err := d.Str()
				s.ExamDate = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"examDate\"")
			}
		case "description":
			requiredBitSet[0] |= 1 << 7
			if err := func() error {
				v, err := d.Str()
				s.Description = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"description\"")
			}
		case "orgName":
			requiredBitSet[1] |= 1 << 0
			if err := func() error {
				v, err := d.Str()
				s.OrgName = string(v)
				if err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"orgName\"")
			}
		case "patientId":
			if err := func() error {
				s.PatientId.Reset()
				if err := s.PatientId.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"patientId\"")
			}
		case "study_availability_status":
			if err := func() error {
				s.StudyAvailabilityStatus.Reset()
				if err := s.StudyAvailabilityStatus.Decode(d); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return errors.Wrap(err, "decode field \"study_availability_status\"")
			}
		default:
			return d.Skip()
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "decode UploadStatus")
	}
	// Validate required fields.
	var failures []validate.FieldError
	for i, mask := range [2]uint8{
		0b11111011,
		0b00000001,
	} {
		if result := (requiredBitSet[i] & mask) ^ mask; result != 0 {
			// Mask only required fields and check equality to mask using XOR.
			//
			// If XOR result is not zero, result is not equal to expected, so some fields are missed.
			// Bits of fields which would be set are actually bits of missed fields.
			missed := bits.OnesCount8(result)
			for bitN := 0; bitN < missed; bitN++ {
				bitIdx := bits.TrailingZeros8(result)
				fieldIdx := i*8 + bitIdx
				var name string
				if fieldIdx < len(jsonFieldsNameOfUploadStatus) {
					name = jsonFieldsNameOfUploadStatus[fieldIdx]
				} else {
					name = strconv.Itoa(fieldIdx)
				}
				failures = append(failures, validate.FieldError{
					Name:  name,
					Error: validate.ErrFieldRequired,
				})
				// Reset bit.
				result &^= 1 << bitIdx
			}
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}

	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s *UploadStatus) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *UploadStatus) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1DicomwebQidoSearchStudyOKApplicationJSON as json.
func (s V1DicomwebQidoSearchStudyOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []DicomMetadata(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes V1DicomwebQidoSearchStudyOKApplicationJSON from json.
func (s *V1DicomwebQidoSearchStudyOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1DicomwebQidoSearchStudyOKApplicationJSON to nil")
	}
	var unwrapped []DicomMetadata
	if err := func() error {
		unwrapped = make([]DicomMetadata, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem DicomMetadata
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1DicomwebQidoSearchStudyOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1DicomwebQidoSearchStudyOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1DicomwebQidoSearchStudyOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1DicomwebRetrieveMetadataOKApplicationJSON as json.
func (s V1DicomwebRetrieveMetadataOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []DicomMetadata(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes V1DicomwebRetrieveMetadataOKApplicationJSON from json.
func (s *V1DicomwebRetrieveMetadataOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1DicomwebRetrieveMetadataOKApplicationJSON to nil")
	}
	var unwrapped []DicomMetadata
	if err := func() error {
		unwrapped = make([]DicomMetadata, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem DicomMetadata
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1DicomwebRetrieveMetadataOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1DicomwebRetrieveMetadataOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1DicomwebRetrieveMetadataOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1PatientsAccountIDStudiesActivatePostOKApplicationJSON as json.
func (s V1PatientsAccountIDStudiesActivatePostOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []PatientStudy(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes V1PatientsAccountIDStudiesActivatePostOKApplicationJSON from json.
func (s *V1PatientsAccountIDStudiesActivatePostOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1PatientsAccountIDStudiesActivatePostOKApplicationJSON to nil")
	}
	var unwrapped []PatientStudy
	if err := func() error {
		unwrapped = make([]PatientStudy, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem PatientStudy
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1PatientsAccountIDStudiesActivatePostOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1PatientsAccountIDStudiesActivatePostOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1PatientsAccountIDStudiesActivatePostOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1PatientsAccountIDStudiesGetOKApplicationJSON as json.
func (s V1PatientsAccountIDStudiesGetOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := []PatientStudy(s)

	e.ArrStart()
	for _, elem := range unwrapped {
		elem.Encode(e)
	}
	e.ArrEnd()
}

// Decode decodes V1PatientsAccountIDStudiesGetOKApplicationJSON from json.
func (s *V1PatientsAccountIDStudiesGetOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1PatientsAccountIDStudiesGetOKApplicationJSON to nil")
	}
	var unwrapped []PatientStudy
	if err := func() error {
		unwrapped = make([]PatientStudy, 0)
		if err := d.Arr(func(d *jx.Decoder) error {
			var elem PatientStudy
			if err := elem.Decode(d); err != nil {
				return err
			}
			unwrapped = append(unwrapped, elem)
			return nil
		}); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1PatientsAccountIDStudiesGetOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1PatientsAccountIDStudiesGetOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1PatientsAccountIDStudiesGetOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1PatientsAccountIDStudiesMatchGetOKApplicationJSON as json.
func (s V1PatientsAccountIDStudiesMatchGetOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := bool(s)

	e.Bool(unwrapped)
}

// Decode decodes V1PatientsAccountIDStudiesMatchGetOKApplicationJSON from json.
func (s *V1PatientsAccountIDStudiesMatchGetOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1PatientsAccountIDStudiesMatchGetOKApplicationJSON to nil")
	}
	var unwrapped bool
	if err := func() error {
		v, err := d.Bool()
		unwrapped = bool(v)
		if err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1PatientsAccountIDStudiesMatchGetOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1PatientsAccountIDStudiesMatchGetOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1PatientsAccountIDStudiesMatchGetOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}

// Encode encodes V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON as json.
func (s V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON) Encode(e *jx.Encoder) {
	unwrapped := bool(s)

	e.Bool(unwrapped)
}

// Decode decodes V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON from json.
func (s *V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON) Decode(d *jx.Decoder) error {
	if s == nil {
		return errors.New("invalid: unable to decode V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON to nil")
	}
	var unwrapped bool
	if err := func() error {
		v, err := d.Bool()
		unwrapped = bool(v)
		if err != nil {
			return err
		}
		return nil
	}(); err != nil {
		return errors.Wrap(err, "alias")
	}
	*s = V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(unwrapped)
	return nil
}

// MarshalJSON implements stdjson.Marshaler.
func (s V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON) MarshalJSON() ([]byte, error) {
	e := jx.Encoder{}
	s.Encode(&e)
	return e.Bytes(), nil
}

// UnmarshalJSON implements stdjson.Unmarshaler.
func (s *V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON) UnmarshalJSON(data []byte) error {
	d := jx.DecodeBytes(data)
	return s.Decode(d)
}
