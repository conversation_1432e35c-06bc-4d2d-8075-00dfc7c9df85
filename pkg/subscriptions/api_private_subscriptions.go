/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package subscriptions

import (
	"encoding/json"
	"net/http"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

// A PrivateSubscriptionsApiController binds http requests to an api service and writes the service results to the http response
type PrivateSubscriptionsApiController struct {
	service coreapi.SubscriptionsApiServicer
}

// NewPrivateSubscriptionsApiController creates a default api controller
func NewPrivateSubscriptionsApiController(
	s coreapi.SubscriptionsApiServicer,
) coreapi.PrivateSubscriptionsApiRouter {
	return &PrivateSubscriptionsApiController{service: s}
}

// Routes returns all of the api route for the PrivateSubscriptionsApiController
func (c *PrivateSubscriptionsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PutToggleAutoRenew",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/toggleautorenew",
			HandlerFunc: c.PutToggleAutoRenew,
		},
	}
}

func (c *PrivateSubscriptionsApiController) GetPathPrefix() string {
	return "/v1/users/subscription"
}

func (c *PrivateSubscriptionsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all PrivateSubscriptions paths require auth
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func getAccountIdFromToken(r *http.Request) (string, error) {
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	return acctId, err
}

// TODO: this really belongs in a v2orders e p
func (c *PrivateSubscriptionsApiController) PutToggleAutoRenew(
	w http.ResponseWriter,
	r *http.Request,
) {
	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	}
	req := coreapi.AutoRenewRequest{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PutToggleAutoRenew(r.Context(), acctId, req)
	if err != nil {
		if strings.Contains(err.Error(), "no rows") {
			httperror.ErrorWithLog(w, r, "no subscription found", http.StatusNotFound)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	w.WriteHeader(http.StatusOK)
}
