/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package subscriptions

import (
	"context"
	"database/sql"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// SubscriptionsApiService is a service that implents the logic for the SubscriptionsApiServicer
// This service should implement the business logic for every endpoint for the SubscriptionsApi API.
// Include any external packages or services that will be required by this service.
type SubscriptionsApiService struct {
	sqldb                *sql.DB
	i18nBundle           *i18n.Bundle
	languageTagProviders languageproviders.LanguageTagProviders
	supportedLanguages   map[string]string
	acctsvcClient        accountservice.AccountService

	//inject functions
	isAuthForFeature IsAuthForFeatureFunc
}

type IsAuthForFeatureFunc func(token string, featureId uint64) (bool, error)

// NewSubscriptionsApiService creates a default api service
func NewSubscriptionsApiService(
	db *sql.DB,
	i18nBundle *i18n.Bundle,
	langProviders languageproviders.LanguageTagProviders,
	supportedLanguages map[string]string,
	acctsvc accountservice.AccountService,
) coreapi.SubscriptionsApiServicer {
	return &SubscriptionsApiService{
		sqldb:                db,
		i18nBundle:           i18nBundle,
		languageTagProviders: langProviders,
		supportedLanguages:   supportedLanguages,
		acctsvcClient:        acctsvc,
		isAuthForFeature:     auth.IsAuthForFeature,
	}
}

// PutToggleAutoRenew - Toggle AutoRenew
func (s *SubscriptionsApiService) PutToggleAutoRenew(
	ctx context.Context,
	acctId string,
	autoRenewReq coreapi.AutoRenewRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acctId)
	err := s.acctsvcClient.ToggleAutoRenew(
		ctx,
		acctId,
		autoRenewReq.OrderId,
		autoRenewReq.AutoRenew,
	)
	if err != nil {
		lg.WithError(err).Error("error toggling autorenew in acctsvc")
		return err
	}
	return nil
}
