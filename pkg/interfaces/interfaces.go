package interfaces

import (
	"github.com/amplitude/analytics-go/amplitude"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
)

type AmplitudeExperimentClient interface {
	Fetch(user *experiment.User) (map[string]experiment.Variant, error)
}

type CIOProducerClient interface {
	UpdateUserAttributes(
		acctId string,
		email string,
		metadata map[string]interface{},
	) error
	Produce(
		acctId string,
		email string,
		eventName string,
		userAttributes map[string]interface{},
		eventAttributes map[string]interface{},
	) error
	ProduceEvent(
		acctId string,
		eventName string,
		metadata map[string]interface{},
	) error
}

type AmplitudeEventClient interface {
	Track(event amplitude.Event)
}
