package ratelimit

import (
	"errors"
	"net/http"
	"time"

	"github.com/go-chi/httprate"
	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RateLimitConfig struct {
	PeriodMinutes time.Duration
	MaxRequests   int
}

func KeyByAccountID(r *http.Request) (string, error) {
	params := mux.Vars(r)
	if accountID, ok := params["account_id"]; ok {
		return accountID, nil
	} else {
		return "", errors.New("accountID not in request")
	}
}

func KeyByAccountIDAndIP(r *http.Request) (string, error) {
	logger := logutils.DebugCtxLogger(r.Context())
	IPAddress, err := httprate.KeyByIP(r)
	if err != nil {
		logger.WithError(err).Error("RateLimit: Could not fetch IP")
	}
	accountID, err := KeyByAccountID(r)

	rateLimitKey := IPAddress + accountID
	logger.With<PERSON>ield("RateLimitKey", rateLimitKey).
		With<PERSON>ield("IPAddress", IPAddress).
		WithField("accountID", accountID).
		Debug()
	return rateLimitKey, nil
}

func NewRateLimiter(
	periodMinutes time.Duration,
	maxRequests int,
) func(next http.Handler) http.Handler {
	// Key by IP + AccountID for a few reasons:
	// 1. If our gateway/reverse proxy does not explicitly set client IP header
	//    then it can be spoofed by an attacker and therefore cannot be trusted
	// 2. AccountID may not exist on anonymous (no account ID/session available) endpoints
	// 3. Given that #1 and #2 are not always reliable, use both
	//    In the worst case, attackers can still DDOS anonymous endpoints
	return httprate.Limit(
		maxRequests,
		periodMinutes*time.Minute,
		httprate.WithKeyFuncs(
			KeyByAccountIDAndIP,
		),
		httprate.WithLimitHandler(func(w http.ResponseWriter, r *http.Request) {
			logger := logutils.DebugCtxLogger(r.Context())
			rateLimitKey, _ := KeyByAccountIDAndIP(r)
			logger.WithField("RateLimitKey", rateLimitKey).Warning("Rate limit enforced")
			http.Error(
				w,
				http.StatusText(http.StatusTooManyRequests),
				http.StatusTooManyRequests,
			)
		}),
	)
}
