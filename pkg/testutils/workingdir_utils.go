package testutils

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func SetWorkingDirToRoot(t *testing.T) {
	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.<PERSON>al(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.<PERSON>al("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.<PERSON>al(err)
	}
}
