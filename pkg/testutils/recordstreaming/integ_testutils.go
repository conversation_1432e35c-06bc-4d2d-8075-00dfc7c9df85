package recordstreaming

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"

	sqlStudyPermissions "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/exams"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func SetupRecordStreamingData(
	t *testing.T,
	db *sql.DB,
	providerID int64,
	physicianAccountID string,
	studyUID string,
	objectID string,
	isReportUploaded bool,
) (string, string) {
	examID := exams.CreateAndInsertTestExam(t, db, nil).UUID
	if studyUID == "" {
		studyUID = phtestutils.GenerateRandomString(t, 10)
	}
	if objectID == "" {
		objectID = phtestutils.GenerateRandomString(t, 10)
	}
	instanceUID := phtestutils.GenerateRandomString(t, 10)

	_, err := db.Exec("INSERT INTO objects (object_id, study_id) VALUES (?, ?)", objectID, studyUID)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}

	_, err = db.Exec(
		"INSERT INTO object_mappings (object_id, exam_uuid) VALUES (?, ?)",
		objectID,
		examID,
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}

	_, err = db.Exec(
		"INSERT INTO unique_study_index (provider_id, study_uid, exam_id) VALUES (?, ?, ?)",
		providerID,
		studyUID,
		examID,
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}

	_, err = db.Exec(
		"INSERT INTO unique_instance_index (provider_id, study_uid, instance_uid, object_id) VALUES (?, ?, ?, ?)",
		providerID,
		studyUID,
		instanceUID,
		objectID,
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}

	if isReportUploaded {
		_, err = db.Exec(
			"INSERT INTO upload_status_log (provider_id, dicom_level, entity_id, step, success) VALUES (?, ?, ?, ?, ?)",
			providerID,
			sqlStudyPermissions.DICOMLevelTypeReport,
			studyUID,
			sqlStudyPermissions.StepUploaded,
			true,
		)
		if err != nil {
			t.Fatalf("failed to setup test data: %q", err.Error())
		}
	}

	t.Cleanup(func() {
		_, _ = db.Exec("DELETE FROM object_mappings WHERE object_id=?", objectID)
		_, _ = db.Exec("DELETE FROM unique_study_index WHERE exam_id=?", examID)
		_, _ = db.Exec("DELETE FROM unique_instance_index WHERE provider_id=?", providerID)
		_, _ = db.Exec("DELETE FROM upload_status_log WHERE provider_id=?", providerID)
	})

	return examID, instanceUID
}
