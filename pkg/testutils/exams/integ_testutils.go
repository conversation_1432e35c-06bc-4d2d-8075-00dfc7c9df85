package exams

import (
	"database/sql"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/samber/lo"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	phtestutil "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

// inserts a test exam and returns the entity
func CreateAndInsertTestExam(t *testing.T, db *sql.DB, accountID *string) coreapi.ExamRawBasic {
	testExam := GenerateTestExam(t, "Ramirez^Zina", "********", accountID)

	InsertTestExam(t, db, testExam, false)

	t.Cleanup(func() {
		DeleteExam(db, testExam.UUID)
	})
	return testExam
}

// inserts a test exam and returns the entity
func CreateAndInsertTestExamWithNameAndDOB(
	t *testing.T,
	db *sql.DB,
	patientName string,
	patientDOB string,
) coreapi.ExamRawBasic {
	testExam := GenerateTestExam(t, patientName, patientDOB, nil)

	InsertTestExam(t, db, testExam, false)
	return testExam
}

// inserts a test exam and returns the entity
func InsertTestExam(t *testing.T, db *sql.DB, testExam coreapi.ExamRawBasic, hasReport bool) {
	_, err := db.Exec(`INSERT INTO exams
			(
				uuid,
				exam_uid,
				transfer_id,
				activated,
				referring_physician,
				description,
				date,
				patient_name,
				patient_mrn,
				dob,
				sex,
				phone,
				accession_number,
				body_part,
				account_id,
				patient_id,
				transfer_status,
				has_report
			)
			VALUES
			(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,
		testExam.UUID, testExam.ExamId, testExam.TransferId, testExam.Activated,
		testExam.DICOMReferringPhysician, testExam.Description, testExam.DICOMExamDate,
		testExam.DICOMPatientName, testExam.PatientMrn, testExam.DICOMBirthDate, testExam.Sex,
		testExam.Phone, testExam.PatientMrn, testExam.BodyPart, testExam.AccountId,
		testExam.PatientId, testExam.TransferStatus, hasReport,
	)
	if err != nil {
		t.Fatal("failed to insert test data", err)
	}
}

func RandomExamID(t *testing.T) string {
	t.Helper()
	return strings.Join([]string{
		phtestutil.GenerateRandomDigits(t, 3),
		phtestutil.GenerateRandomDigits(t, 3),
		phtestutil.GenerateRandomDigits(t, 4),
		phtestutil.GenerateRandomDigits(t, 3),
	}, ".")
}

func GenerateTestExam(
	t *testing.T,
	patientName string,
	patientDOB string,
	accountID *string,
) coreapi.ExamRawBasic {
	rand := phtestutil.GenerateRandomString(t, 8)
	return coreapi.ExamRawBasic{
		UUID:                    "test-exam-" + rand,
		ExamId:                  RandomExamID(t),
		TransferId:              "test-transfer-" + rand,
		Activated:               true,
		DICOMReferringPhysician: "Dr. Zina",
		Description:             "pie is better than cake",
		DICOMExamDate:           "********",
		DICOMPatientName:        patientName,
		DICOMBirthDate:          patientDOB,
		Sex:                     "F",
		Phone:                   "**********",
		BodyPart:                "foot",
		AccountId: lo.Ternary(
			accountID == nil,
			"test-account-"+rand,
			lo.FromPtr(accountID),
		),
		PatientId:      "test-patient-" + rand,
		PatientMrn:     "test-mrn-" + rand,
		TransferStatus: coreapi.V3_TRANSFER_FULL,
	}
}

func DeleteExam(db *sql.DB, exam_uuid string) {
	_, _ = db.Exec(`DELETE FROM exams WHERE uuid=?`, exam_uuid)
}
