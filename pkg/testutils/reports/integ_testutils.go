package reports

import (
	"database/sql"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/samber/lo"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	phtestutil "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

type TestReport struct {
	coreapi.Report
	AccountID string
	ScanID    string
	ExamUUID  string
}

// inserts a test exam and returns the entity
func CreateAndInsertTestReport(t *testing.T, db *sql.DB, accountID *string) TestReport {
	report := GenerateTestReport(t, accountID)

	InsertTestReport(t, db, report)

	t.Cleanup(func() {
		DeleteReport(db, report)
	})
	return report
}

func InsertTestReport(t *testing.T, db *sql.DB, report TestReport) {
	_, err := db.Exec(`INSERT INTO objects
			(
				object_id,
				protocol
			)
			VALUES
			(
				?,
				?
			)`,
		report.ReportId,
		report.Protocol,
	)
	require.NoError(t, err)

	_, err = db.Exec(`INSERT INTO scans
			(
				scan_id,
				uploaded
			)
			VALUES
			(
				?,
				?
			)`,
		report.ScanID,
		report.UploadTime,
	)
	require.NoError(t, err)

	_, err = db.Exec(`INSERT INTO exams
			(
				uuid,
				account_id,
				transfer_id,
				activated,
				sex,
				dob,
				patient_name,
				referring_physician
			)
			VALUES
			(
				?,
				?,
				?,
				?,
				?,
				?,
				?,
				?
			)`,
		report.ExamUUID,
		report.AccountID,
		report.ScanID,
		report.Activated,
		report.Sex,
		report.Dob,
		report.PatientName.DicomName,
		report.ReferringPhysician,
	)
	require.NoError(t, err)

	_, err = db.Exec(`INSERT INTO object_mappings
			(
				object_id,
				exam_uuid,
				size
			)
			VALUES
			(
				?,
				?,
				?
			)`,
		report.ReportId,
		report.ExamUUID,
		report.Size,
	)
	require.NoError(t, err)

}

// Returns an object usable with objects.GetReportMetadata()
func GenerateTestReport(t *testing.T, accountID *string) TestReport {
	return TestReport{
		Report: coreapi.Report{
			ReportId:   ksuid.New().String(),
			UploadTime: time.Now().String(),
			Activated:  true,
			Sex:        "F",
			Dob:        time.Now().AddDate(-20, 0, 0).Format(time.DateOnly),
			PatientName: models.PatientName{
				DicomName: phtestutil.GenerateRandomString(
					t,
					5,
				) + " " + phtestutil.GenerateRandomString(
					t,
					5,
				),
			},
			Size:     phtestutil.GenerateRandomInt64(t),
			Protocol: "R",
			ReferringPhysician: phtestutil.GenerateRandomString(
				t,
				5,
			) + " " + phtestutil.GenerateRandomString(
				t,
				5,
			),
		},
		AccountID: lo.Ternary(lo.IsNil(accountID), ksuid.New().String(), lo.FromPtr(accountID)),
		ExamUUID:  ksuid.New().String(),
		ScanID:    ksuid.New().String(),
	}
}

func DeleteReport(db *sql.DB, report TestReport) {
	_, _ = db.Exec(
		`DELETE FROM object_mappings WHERE object_id = ? AND exam_uuid = ?`,
		report.ReportId,
		report.ExamUUID,
	)
	_, _ = db.Exec(`DELETE FROM exams WHERE uuid = ?`, report.ExamUUID)
	_, _ = db.Exec(`DELETE FROM scans WHERE scan_id = ?`, report.ScanID)
	_, _ = db.Exec(`DELETE FROM objects WHERE object_id = ?`, report.ReportId)
}
