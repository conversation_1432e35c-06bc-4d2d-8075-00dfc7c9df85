package testutils

import (
	"context"
	"fmt"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
)

func GetBlobsWithPrefix(prefix string, containerClient *container.Client) ([]string, error) {
	var blobNames []string
	err := visitBlobsWithPrefix(prefix, containerClient, func(blobName string) {
		blobNames = append(blobNames, blobName)
	})
	return blobNames, err
}

func DeleteBlobsWithPrefix(t *testing.T, prefix string, containerClient *container.Client) {
	err := visitBlobsWithPrefix(prefix, containerClient, func(blobName string) {
		err := azureUtils.DeleteBlob(context.Background(), containerClient, blobName)
		assert.NoError(t, err, "failed deleting blob %v", blobName)
	})
	assert.NoError(t, err, "failed listing blobs with prefix %v", prefix)
}

func visitBlobsWithPrefix(
	prefix string,
	containerClient *container.Client,
	visitor func(blobName string),
) error {
	pager := containerClient.NewListBlobsFlatPager(&container.ListBlobsFlatOptions{
		Prefix: &prefix,
	})
	for pager.More() {
		resp, err := pager.NextPage(context.Background())
		if err != nil {
			return fmt.Errorf("failed getting next page: %w", err)
		}

		for _, v := range resp.Segment.BlobItems {
			visitor(*v.Name)
		}
	}

	return nil
}
