package testutils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

// MockHttpServer is a small http server mock that can be set up to return
// responses based on endpoints and records any requests it receives
// Usage:
//
//		func TestSomething(t *testing.T) {
//			httpServer := testutils.NewMockHttpServer(t, map[string]string{
//			  "/hello": "HELLO WORLD",
//			})
//			defer httpServer.Close()
//
//	        // send request
//			resp,err := http.Get(httpServer.URL+"/hello")
//			require.NoError(t, err)
//
//	        // check response body
//			bodyBytes,err := io.ReadAll(resp.Body)
//			defer resp.Body.Close()
//			require.NoError(t, err)
//			fmt.Printf("response body: %v\n", string(bodyBytes))
//		}
type MockHttpServer[T any] struct {
	t *testing.T
	// maps paths like "/v1/followup/2" to a response
	responses map[string]T
	server    *httptest.Server

	URL string

	endpointHits map[string][]*http.Request
}

func NewMockHttpServer[T any](t *testing.T, responses map[string]T) *MockHttpServer[T] {
	m := &MockHttpServer[T]{
		t:            t,
		responses:    responses,
		endpointHits: make(map[string][]*http.Request),
	}
	m.server = httptest.NewServer(http.HandlerFunc(m.Handle))
	m.URL = m.server.URL

	t.Cleanup(func() {
		m.Close()
	})

	return m
}

func (m *MockHttpServer[T]) SetResponses(rs map[string]T) {
	m.responses = rs
}

func (m *MockHttpServer[any]) Handle(w http.ResponseWriter, r *http.Request) {
	if resp, ok := m.responses[r.URL.Path]; ok {

		// need to copy the body so it can be accessed later because it is
		// closed automatically by the server
		bodyCopy, err := copyReadCloser(r.Body)
		require.NoError(m.t, err)
		r.Body.Close() /* #nosec G104 */
		r.Body = bodyCopy

		m.recordEndpointHit(r.URL.Path, r)
		err = json.NewEncoder(w).Encode(resp)
		require.NoError(m.t, err, "failed encoding response as JSON")
	} else {
		m.t.Logf("No endpoint registered for %v", r.URL.Path)
		w.WriteHeader(http.StatusNotFound)
	}
}
func copyReadCloser(r io.ReadCloser) (io.ReadCloser, error) {
	bb, err := io.ReadAll(r)
	if err != nil {
		return nil, fmt.Errorf("failed reading from readcloser: %w", err)
	}
	return io.NopCloser(bytes.NewBuffer(bb)), nil
}

func (m *MockHttpServer[any]) recordEndpointHit(endpoint string, r *http.Request) {
	m.endpointHits[endpoint] = append(m.endpointHits[endpoint], r)
}

func (m *MockHttpServer[any]) EndpointCalls(endpoint string) []*http.Request {
	if reqs, ok := m.endpointHits[endpoint]; ok {
		return reqs
	}
	return []*http.Request{}
}

func (m *MockHttpServer[any]) Close() {
	m.server.Close()
}
