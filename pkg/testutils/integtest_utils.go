//go:build integration
// +build integration

package testutils

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
)

const (
	localClaimIp = "127.0.0.1"
)

type IntegTestConfig struct {
	SqlConnString                  string `json:"sqlConnString"`
	AzureStorageAccount            string `json:"azure_storage_account"`
	AcctSvcUrl                     string `json:"acct_svc_url"`
	RRUrl                          string `json:"rr_url"`
	PlanSvcUrl                     string `json:"plans_url"`
	FaxUrl                         string `json:"fax_url"`
	ReportInsightsStorageContainer string `json:"azure_reportinsight_storage_container"`
	OrgSvcUrl                      string `json:"orgsvc_url"`
	OrgSvcUser                     string `json:"orgsvc_uname"`
	OrgSvcApiKey                   string `json:"orgsvc_api_key"`
	ExamInsightsSrvcUrl            string `json:"exam_insights_srvc_url"`
	ExamInsightsSrvcApiKey         string `json:"exam_insights_srvc_api_key"`
	ExamInsightsSrvcApiKeySecName  string `json:"exam_insights_srvc_api_key_sec_name"`
	GatewayAuthUrl                 string `json:"gateway_auth_url"`
	ReportInsightsUrl              string `json:"report_insights_url"`
}

func LoadTestConfigFromEnvVar(t *testing.T) IntegTestConfig {
	t.Helper()

	confPath, ok := os.LookupEnv("IT_CONF")

	if !ok {
		t.Fatal(
			"can't setup integ test - config file not set.  set it with environment variable 'IT_CONF'.",
		)
	}

	configJSON, err := os.ReadFile(filepath.Clean(confPath))
	if err != nil {
		t.Fatal("Unable to read config: ", err)
	}

	var conf IntegTestConfig
	err = json.Unmarshal(configJSON, &conf)
	if err != nil {
		t.Fatal("Unable to parse config")
	}
	// TODO figure out how to get keyvault config values so we do not push secrets into our repo

	return conf
}

func CreateAccountServiceTestAccount(
	t *testing.T,
	ctx context.Context,
	client *http.Client,
	regionID uint16,
	url string,
	user string,
	apikey string,
) (acctID string, testEmail string, password string, err error) {
	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	testEmail = "test_email_" + rand + "@example.com"

	password = fmt.Sprintf("%stest%s", rand, rand)

	endpoint := fmt.Sprintf("%s/v1/accounts", url)
	body := map[string]interface{}{
		"email":       testEmail,
		"main_region": regionID,
		"password":    password,
	}

	reqBody, err := json.Marshal(body)
	if err != nil {
		return "", "", "", err
	}

	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", "", "", err
	}

	req.Header.Set(
		"Authorization",
		"Basic "+base64.StdEncoding.EncodeToString([]byte(user+":"+apikey)),
	)
	req.Header.Set("Content-type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return "", "", "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", "", "", fmt.Errorf("got http status code: %d", resp.StatusCode)
	}

	gotBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", err
	}

	err = json.Unmarshal(gotBody, &acctID)
	if err != nil {
		return "", "", "", err
	}

	t.Cleanup(
		func() {
			assert.NoError(t, DeleteAccountSvcTestAccount(client, acctID, url, user, apikey))
		},
	)

	return acctID, testEmail, password, nil
}

func DeleteAccountSvcTestAccount(
	client *http.Client,
	acctID string,
	url string,
	acctServiceUser string,
	acctSvcApiKey string,
) error {
	endpoint := fmt.Sprintf("%s/v1/accounts/%s", url, acctID)

	req, err := http.NewRequest("DELETE", endpoint, nil)
	if err != nil {
		return fmt.Errorf("error forming request: %q", err)
	}

	authKey := base64.StdEncoding.EncodeToString([]byte(acctServiceUser + ":" + acctSvcApiKey))
	req.Header.Set("Authorization", "Basic "+authKey)
	req.Header.Set("Content-type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending http request: %q", err)
	}

	if resp.StatusCode/100 != 2 {
		return fmt.Errorf("expected 2xx status code, got: %q", resp.StatusCode)
	}

	return nil
}

func SetupTestDB(t *testing.T) *sql.DB {
	cfg := LoadTestConfigFromEnvVar(t)
	db, err := sql.Open("mysql", cfg.SqlConnString)
	if err != nil {
		t.Fatalf("unable to perform sql.Open: %q", err.Error())
	}
	err = db.Ping()
	if err != nil {
		t.Fatalf("unable to ping db: %q", err.Error())
	}
	return db
}

func GetAccountAuthToken(accountID string) string {
	return "Bearer " + auth.MakeAccountAuthToken(
		accountID,
		localClaimIp,
	)
}

func CreateRequest(
	t *testing.T,
	method string,
	path string,
	payload any,
) *http.Request {
	body, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("failed to marshal request payload: %v", err)
	}
	buffer := bytes.NewReader(body)
	request, err := http.NewRequest(method, path, buffer)
	if err != nil {
		t.Fatalf("failed to create http request: %v", err)
	}
	return request
}

func CreateAuthorizedRequest(
	t *testing.T,
	accountID string,
	method string,
	path string,
	payload any,
) *http.Request {
	request := CreateRequest(
		t,
		method,
		path,
		payload,
	)
	request.Header.Set("Authorization", GetAccountAuthToken(accountID))
	return request
}

func MakeRequestToHandler(
	t *testing.T,
	request *http.Request,
	handlerFunc func(http.ResponseWriter, *http.Request),
) (int, []byte) {
	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlerFunc)
	handler.ServeHTTP(rr, request)
	return rr.Code, rr.Body.Bytes()
}
