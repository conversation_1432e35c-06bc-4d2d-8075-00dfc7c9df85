package testutils

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"encoding/pem"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/signature"
)

func SetupXPhSignaturePrivKey(t *testing.T) *ecdsa.PrivateKey {
	privateKey, err := ecdsa.GenerateKey(elliptic.P384(), rand.Reader)
	require.NoError(t, err)

	publicKey := &privateKey.PublicKey
	x509EncodedPub, err := x509.MarshalPKIXPublicKey(publicKey)
	require.NoError(t, err)

	pemEncodedPub := pem.EncodeToMemory(&pem.Block{Type: "PUBLIC KEY", Bytes: x509EncodedPub})
	err = auth.SetAcctSvcPublicKey(pemEncodedPub)
	require.NoError(t, err)

	return privateKey
}

// Sign the request as in phutils/httpclient/xphsignaturetransport.go
func SignRequest(t *testing.T, r *http.Request, privKey *ecdsa.PrivateKey) {
	t.Helper()

	var body []byte
	if r.Body != nil {
		var err error
		// The error *is* handled by require.NoError(), so mute the gosec error
		body, err = io.ReadAll(r.Body) // #nosec: G104
		defer r.Body.Close()
		require.NoError(t, err)
		r.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	sig, err := signature.SignBase64Enc(
		append([]byte(r.URL.Path), append([]byte(r.URL.RawQuery), body...)...),
		privKey,
	)
	require.NoError(t, err)
	r.Header.Add("X-PH-Signature", sig)
}
