package incompleterequests

import (
	"context"
	"database/sql"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	sqlIncompleteRequestBlobs "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequestblobs"
	sqlIncompleteRequests "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequests"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func deleteBlob(
	ctx context.Context,
	container *container.Client,
	db *sql.DB,
	blobId string,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	err := azureUtils.DeleteBlob(ctx, container, blobId)
	if err != nil {
		lg.WithError(err).Error("unable to delete incomplete request blob bytes from azure")
		return err
	}
	err = sqlIncompleteRequestBlobs.DeleteBlob(ctx, db, blobId)
	if err != nil {
		lg.WithError(err).Error("unable to cleanup incomplete request blob entry from sql")
		return err
	}
	return nil
}

// We need to clear PHI for expired incomplete requests from our db.
// We're clearing this data async so we don't interfere with requests
func IncompleteRequestDeletionWorker(
	ctx context.Context,
	container *container.Client,
	db *sql.DB,
) {
	lg := logutils.DebugCtxLogger(ctx)
	for {
		expiredBlobIds, err := sqlIncompleteRequestBlobs.GetExpiredBlobIds(ctx, db)
		if err != nil {
			lg.WithError(err).Error("unable to get expired incomplete request ids")
		} else {
			for _, id := range expiredBlobIds {
				go deleteBlob(ctx, container, db, id)
			}
		}

		cleared, err := sqlIncompleteRequests.ClearExpiredRequests(ctx, db)
		if err != nil {
			lg.WithError(err).Error("unable to clear expired incomplete requests")
		} else {
			lg.WithField("totalCleared", cleared).Info("cleared expired incomplete requests")
		}
		time.Sleep(time.Hour * 12)
	}
}
