package models

type UploadStatusStudyRequest struct {
	StudyUID   string `json:"studyUID"`
	ProviderID int64  `json:"providerID"`
}

// UploadStatusStudy describes the current upload status
// of a study that is uploaded via record streaming
// NOTE: meant for use with the v1/uploadstatus/study providersservice endopint
type UploadStatusStudy struct {
	StudyUID        string              `json:"studyUID"`
	ProviderID      int64               `json:"providerID"`
	UploadCompleted bool                `json:"uploadCompleted"`
	UploadManifest  StudyUploadManifest `json:"uploadManifest"`
}

type StudyUploadManifest struct {
	Study     bool            `json:"study"`
	Report    bool            `json:"report"`
	Instances map[string]bool `json:"instances"`
}
