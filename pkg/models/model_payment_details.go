/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

import (
	"time"
)

type PaymentDetails struct {
	Token            string          `json:"token,omitempty"`
	PaymentProvider  PaymentProvider `json:"paymentProvider,omitempty"`
	PlanId           uint64          `json:"planId,omitempty"`
	DisableAutoRenew bool            `json:"disableAutoRenew,omitempty"`
	Source           string          `json:"source,omitempty"`
}

type TransactionHistory struct {
	OrderId string `json:"orderId,omitempty"`

	Plan string `json:"plan,omitempty"`

	Amount int32 `json:"amount,omitempty"`

	Tax int32 `json:"tax,omitempty"`

	Total int32 `json:"total,omitempty"`

	DiscountAmount int32 `json:"discountAmount,omitempty"`

	DiscountName string `json:"discountName,omitempty"`

	RefundedAmount int32 `json:"refundedAmount,omitempty"`

	Timestamp time.Time `json:"timestamp,omitempty"`
}

func (p *PaymentDetails) IsValid() bool {
	return *p != PaymentDetails{}
}
