/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

type StudyRequestRecentExamDetails struct {
	Type string `json:"exam_type,omitempty"`

	Site string `json:"exam_site,omitempty"`

	// Year+Month
	Date string `json:"exam_date,omitempty"`

	ExamMetadatas []ExamMetadata `json:"multi_exam,omitempty"`

	Address *StudyRequestRecentExamDetailsAddress `json:"address,omitempty"`
}

type ExamMetadata struct {
	Type  string `json:"exam_type,omitempty"`
	Year  string `json:"exam_year,omitempty"`
	Month string `json:"exam_month,omitempty"`
}
