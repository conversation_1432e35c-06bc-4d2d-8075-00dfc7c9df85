/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

import (
	"mime/multipart"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
)

type CreateRequest struct {
	RequestDetails      Request                 `json:"requestDetails"`
	AccountId           string                  `json:"accountId"`
	SignatureImage      string                  `json:"signatureImg"`
	MinorSignatureImage string                  `json:"minorSignatureImg"`
	PaymentDetails      PaymentDetails          `json:"paymentDetails"`
	DeviceId            string                  `json:"deviceID"`
	DelegForm           []*multipart.FileHeader `json:"delegForm,omitempty"`
	DelegPhotoId        []*multipart.FileHeader `json:"delegPhotoId,omitempty"`
	InAppRequest        bool                    `json:"inApp"`
}

type Request struct {

	// Not populated on POST
	RequestId int64 `json:"requestId,omitempty"`

	FirstName string `json:"firstName,omitempty"`

	LastName string `json:"lastName,omitempty"`

	AltLastName string `json:"altLastName,omitempty"`

	Ohip string `json:"ohip,omitempty"`

	Ohipvc string `json:"ohipvc,omitempty"`

	Bcphn string `json:"bcphn,omitempty"`

	Mrn string `json:"mrn,omitempty"`

	OtherId string `json:"otherId,omitempty"`

	Dob string `json:"dob,omitempty"`

	Tel string `json:"tel,omitempty"`

	Email string `json:"email,omitempty"`

	Contents StudyRequest `json:"contents,omitempty"`

	Ssn string `json:"ssn,omitempty"`

	Ipn string `json:"ipn,omitempty"`

	//this is providerLegacyId
	OrgId int64 `json:"orgId,omitempty"`

	//This is clinicId
	ProviderId int64 `json:"providerId,omitempty"`

	AltId string `json:"altId,omitempty"`

	NeedsIdVerification bool

	PatientId string `json:"patientId,omitempty"`

	RejectionReason int64 `json:"rejectionReason,omitempty"`
}

func (r *Request) IsValid() bool {
	return *r != Request{} && r.ProfilesAreValid()
}

// normalize apostrophes in request FirstName, LastName, and AltLastName
func (request *Request) NormalizeNames() {
	request.FirstName = strings.Trim(strings.Replace(request.FirstName, "’", "'", -1), " ")
	request.LastName = strings.Trim(strings.Replace(request.LastName, "’", "'", -1), " ")
	request.AltLastName = strings.Trim(strings.Replace(request.AltLastName, "’", "'", -1), " ")
}

const MaxIncompleteRequestFileUploadSize = 5 << 20

func (req *Request) FormatDob() string {
	return datetime.ConvertMMDDYYYYToISO8061(req.Dob)
}

func (req *Request) ProfilesAreValid() bool {

	delegateIsValid := req.Contents.Delegate == nil
	if !delegateIsValid {
		delegateIsValid = req.Contents.Delegate.IsValid()
	}

	return req.FirstName != "" &&
		req.LastName != "" &&
		req.Email != "" &&
		req.Tel != "" &&
		req.FormatDob() != "" &&
		delegateIsValid
}
