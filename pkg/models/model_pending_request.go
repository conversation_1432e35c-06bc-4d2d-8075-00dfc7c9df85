/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

type PendingRequest struct {
	FirstName string `json:"firstName,omitempty"`

	LastName string `json:"lastName,omitempty"`

	AltLastName string `json:"altLastName,omitempty"`

	PatientId string `json:"patientId,omitempty"`

	ClinicId int64 `json:"clinicId,omitempty"`

	Timestamp string `json:"timestamp,omitempty"`

	ClinicName string `json:"clinicName,omitempty"`

	Date string `json:"date,omitempty"`

	RequestId int32 `json:"requestId,omitempty"`

	Editable bool `json:"editable,omitempty"`

	Status string `json:"status,omitempty"`

	DOB string `json:"dob,omitempty"`

	Ohip string `json:"ohip,omitempty"`

	OhipVC string `json:"ohipVc,omitempty"`

	IsBcphn bool `json:"isBcphn,omitempty"`

	Ipn string `json:"ipn,omitempty"`

	Mrn string `json:"mrn,omitempty"`

	AltId string `json:"altId,omitempty"`

	Ssn string `json:"ssn,omitempty"`

	Contents StudyRequest `json:"contents,omitempty"`

	OrgId int64 `json:"orgId,omitempty"`

	ProviderName string `json:"providerName,omitempty"`

	ProviderLogo string `json:"providerLogo,omitempty"`

	IsUph bool `json:"isUph"`
}

type PendingRequests []PendingRequest

// HasRequestWithStatus returns whether or not any of the requests have the
// specified status.
func (prs PendingRequests) HasRequestWithStatus(status string) bool {
	for _, pr := range prs {
		if pr.Status == status {
			return true
		}
	}
	return false
}
