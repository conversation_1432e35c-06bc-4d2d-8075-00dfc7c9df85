/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

type StudyRequestRecentExamDetailsAddress struct {
	StreetNumber string `json:"street_number,omitempty"`

	StreetName string `json:"street_name,omitempty"`

	PostalCode string `json:"postal_code,omitempty"`
}

func (addr *StudyRequestRecentExamDetailsAddress) String() string {
	return addr.StreetNumber + " " + addr.StreetName + ", " + addr.PostalCode
}
