/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

import (
	"context"
	"regexp"
	"time"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PhiPatient struct {
	PatientId string `json:"patientId,omitempty"`

	AccountId string `json:"-"`

	FirstName string `json:"firstName,omitempty"`

	LastName string `json:"lastName,omitempty"`

	AltLastName string `json:"altLastName,omitempty"`

	DOB string `json:"DOB,omitempty"`

	Ohip string `json:"ohip,omitempty"`

	Ohipvc string `json:"ohipvc,omitempty"`

	Bcphn string `json:"bcphn,omitempty"`

	AltId string `json:"altId,omitempty"`

	Phone string `json:"phone,omitempty"`

	Email string `json:"email,omitempty"`

	Ipn string `json:"ipn,omitempty"`

	Ssn string `json:"ssn,omitempty"`

	Sex string `json:"sex,omitempty"`

	Country string `json:"country,omitempty"`

	Subdivision string `json:"subdivision,omitempty"`

	PostalCode string `json:"postalCode,omitempty"`

	IsAccountOwner bool `json:"isAccountOwner,omitempty"`

	Source string `json:"source,omitempty"`
}

func (p *PhiPatient) IsValid(ctx context.Context) bool {
	lg := logutils.DebugCtxLogger(ctx)
	// if below info are not empty, validate
	// copied over from coreapi request validation
	nameRegex, _ := regexp.Compile(`^[0-9a-z A-Z \- '’]+$`)
	if p.FirstName != "" && !nameRegex.MatchString(p.FirstName) {
		lg.Error("invalid first name")
		return false
	}
	if p.LastName != "" && !nameRegex.MatchString(p.LastName) {
		lg.Error("invalid last name")
		return false
	}
	if p.AltLastName != "" && !nameRegex.MatchString(p.AltLastName) {
		lg.Error("invalid alt last name")
		return false
	}

	//dob format yyyy-mm-dd ISO 8601
	if p.DOB != "" {
		parsed, err := time.Parse("2006-01-02", p.DOB)
		if err != nil || parsed.IsZero() {
			lg.Error("invalid dob")
			return false
		}
	}

	//following email validation in https://developer.mozilla.org/ca/docs/Web/HTML/Element/Input/email#Validation
	emailRegex := regexp.MustCompile(
		"^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$",
	)
	if p.Email != "" && !emailRegex.MatchString(p.Email) {
		lg.Error("invalid email")
		return false
	}

	telRegex, _ := regexp.Compile(`^\+?(\d{0,3})[\(]?(\d{3})[\)]?[-]?(\d{3})[-]?(\d{4})$`)
	if p.Phone != "" && !telRegex.MatchString(p.Phone) {
		lg.Error("invalid phone")
		return false
	}
	return true
}
