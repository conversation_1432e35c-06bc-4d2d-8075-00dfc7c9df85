/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

// IncompleteRequestMetadata
type IncompleteRequestMetadata struct {
	ClinicName string `json:"clinicName,omitempty"`

	ClinicId int64 `json:"clinicId,omitempty"`

	EmailStep string `json:"emailStep,omitempty"`

	Status IncompleteRequestStatus `json:"status,omitempty"`
}
