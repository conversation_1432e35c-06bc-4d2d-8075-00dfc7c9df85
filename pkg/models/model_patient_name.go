/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

import (
	"context"
	"errors"
	"strings"

	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
)

type PatientName struct {
	DicomName string `json:"dicomName,omitempty"`

	FirstAndMiddleName string `json:"firstAndMiddleName,omitempty"`

	LastName string `json:"lastName,omitempty"`
}

func (p *PatientName) GetLastCommaFirst() string {
	return p.LastName + ", " + p.FirstAndMiddleName
}

func (p *PatientName) GetFullName() string {
	return p.FirstAndMiddleName + " " + p.LastName
}

func (o *PatientName) Validate() error {
	if o.DicomName == "" && o.FirstAndMiddleName == "" && o.LastName == "" {
		return errors.New("need at least one of dicom name, first name and last name")
	}
	return nil
}

func NewPatientName(ctx context.Context, dicomName string) PatientName {
	firstName, lastName := dcmtools.ParseFirstLastName(ctx, dicomName)
	return PatientName{
		DicomName:          strings.TrimSpace(dicomName),
		FirstAndMiddleName: strings.TrimSpace(firstName),
		LastName:           strings.TrimSpace(lastName),
	}
}
