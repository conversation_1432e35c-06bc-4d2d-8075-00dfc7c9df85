/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

import "time"

type StudyRequestDelegate struct {
	FirstName    string `json:"firstName,omitempty"`
	LastName     string `json:"lastName,omitempty"`
	Dob          string `json:"dob,omitempty"` // Format: 01/02/2006
	Relation     string `json:"relation,omitempty"`
	Address      string `json:"address,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Email        string `json:"email,omitempty"`
	RelationType string `json:"relationType,omitempty"`
}

const (
	FAMILY_MEMBER    = "family-member"
	LEGAL_INDIVIDUAL = "legal-individual"
	PUBLIC_GUARDIAN  = "public-guardian"
)

func (s *StudyRequestDelegate) FullName() string {
	return s.FirstName + " " + s.LastName
}

func (s *StudyRequestDelegate) IsValid() bool {
	if s.Dob != "" {
		if _, err := time.Parse("01/02/2006", s.Dob); err != nil {
			return false
		}
	}
	return s.FirstName != "" &&
		s.LastName != "" &&
		s.Relation != ""
}
