package models

import "fmt"

type UnlockStatus string

const (
	EXAM_LOCKED               UnlockStatus = "locked"
	EXAM_FULL_ACCESS          UnlockStatus = "fullAccess"
	EXAM_LIMITED_AVAILABILITY UnlockStatus = "limitedAvailability"
)

// Define a map for ranking each UnlockStatus
var unlockStatusRank = map[UnlockStatus]int{
	EXAM_LOCKED:               1,
	EXAM_LIMITED_AVAILABILITY: 2,
	EXAM_FULL_ACCESS:          3,
}

// HasGreaterStudyAccessLevel determines if status A has greater level of access than status B
func HasGreaterStudyAccessLevel(a, b UnlockStatus) (bool, error) {
	// Ensure both statuses exist in the ranking map
	rankA, existsA := unlockStatusRank[a]
	rankB, existsB := unlockStatusRank[b]

	if !existsA || !existsB {
		return false, fmt.Errorf("invalid UnlockStatus: A='%s', B='%s'", a, b)
	}

	return rankA > rankB, nil
}
