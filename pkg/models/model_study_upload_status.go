package models

import (
	"gitlab.com/pockethealth/coreapi/generated/services/recordservice"
)

type RecordUploadStatus struct {
	LegacyProviderID int64        `json:"orgId"`
	StudyUID         string       `json:"examId"`          // DICOM identifier of study, named to match exams
	ExamUUID         string       `json:"uuid"`            // ksuid of exam object in our system, named to match exams
	ProgressPercent  int          `json:"progressPercent"` // percentage of study upload progress, always 0 at this point
	StudyModality    string       `json:"modality"`
	StudyDate        string       `json:"examDate"` // exam date, in format 'Jan 01, 2000'
	StudyDescription string       `json:"description"`
	StudyType        string       `json:"examType"`
	ProviderName     string       `json:"orgName"`
	PatientID        string       `json:"patientId"`
	PatientName      PatientName  `json:"patientName"`
	UnlockStatus     UnlockStatus `json:"unlockStatus"`
}

func ToRecordUploadStatus(
	uploadStatus recordservice.UploadStatus,
) RecordUploadStatus {
	return RecordUploadStatus{
		LegacyProviderID: uploadStatus.OrgId,
		StudyUID:         uploadStatus.ExamId,
		ExamUUID:         uploadStatus.UUID.Value,
		ProgressPercent:  uploadStatus.ProgressPercent,
		StudyModality:    uploadStatus.Modality,
		StudyDate:        uploadStatus.ExamDate,
		StudyDescription: uploadStatus.Description,
		StudyType:        uploadStatus.ExamType,
		ProviderName:     uploadStatus.OrgName,
		PatientID:        uploadStatus.PatientId.Value,
		UnlockStatus:     UnlockStatus(uploadStatus.StudyAvailabilityStatus.Value),
	}
}

type RecordUploadStatuses []RecordUploadStatus

// InstancesFoundForPatient returns whether or not any of the studies from a
// providee have ProgressPercent > 0 for a patient.
func (rus RecordUploadStatuses) InstancesFoundForPatient(providerID int64, patientID string) bool {
	for _, ru := range rus {
		if ru.LegacyProviderID == providerID && ru.PatientID == patientID &&
			ru.ProgressPercent > 0 {
			return true
		}
	}
	return false
}
