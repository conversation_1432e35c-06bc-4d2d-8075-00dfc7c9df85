/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package models

type StudyRequest struct {
	Mode string `json:"mode,omitempty"`

	AllStudies bool `json:"allStudies,omitempty"`

	Details string `json:"details,omitempty"`

	StartDate string `json:"startDate,omitempty"`

	EndDate string `json:"endDate,omitempty"`

	EnrollmentConsent *bool `json:"enrollment_consent,omitempty"`

	RecentExamDetails *StudyRequestRecentExamDetails `json:"recent_exam_details,omitempty"`

	Delegate *StudyRequestDelegate `json:"delegate,omitempty"`

	PatientAddress Address `json:"patientAddress,omitempty"`

	ProviderAddress Address `json:"providerAddress,omitempty"`
}

type Address struct {
	Line1      string `json:"line1"`
	Line2      string `json:"line2"`
	City       string `json:"city"`
	State      string `json:"state"`
	PostalCode string `json:"postal_code"`
	Country    string `json:"country"`
}
