package payment

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/pmts"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PaymentApiService struct {
	sqldb             *sql.DB
	paymentSvcUser    pmts.PaymentSvcUser
	acctSvcClient     accountservice.AccountService
	orgSvcClient      orgs.OrgService
	amplitudeClient   interfaces.AmplitudeEventClient
	planServiceClient planservice.PlanService
}

func NewPaymentApiService(
	db *sql.DB,
	paymentSvcUser pmts.PaymentSvcUser,
	acctSvcClient accountservice.AccountService,
	orgSvcClient orgs.OrgService,
	amplitudeClient interfaces.AmplitudeEventClient,
	planServiceClient planservice.PlanService,
) coreapi.PaymentApiServicer {
	return &PaymentApiService{
		sqldb:             db,
		paymentSvcUser:    paymentSvcUser,
		acctSvcClient:     acctSvcClient,
		orgSvcClient:      orgSvcClient,
		amplitudeClient:   amplitudeClient,
		planServiceClient: planServiceClient,
	}
}

func (s *PaymentApiService) GetPaymentProviders(
	ctx context.Context,
	country string,
) ([]string, error) {
	lg := logutils.DebugCtxLogger(ctx)

	resp, err := s.paymentSvcUser.GetPaymentProviders(ctx, country)
	if err != nil {
		lg.WithError(err).Error("failed to get payment providers")
		return nil, err
	}

	return resp, nil
}

func (s *PaymentApiService) GetPaymentHistory(
	ctx context.Context,
	accountId string,
) ([]models.TransactionHistory, error) {
	lg := logutils.DebugCtxLogger(ctx)

	resp, err := s.paymentSvcUser.GetTransactionHistory(ctx, accountId)
	if err != nil {
		lg.WithError(err).Error("failed to get transaction history")
		return nil, err
	}

	mapped := make([]models.TransactionHistory, len(resp.Data))

	for i, dto := range resp.Data {
		// Map dto to Transaction struct
		if dto.Status != "Declined" {
			mapped[i] = SetTransaction(dto)
		}
	}

	return mapped, nil
}

func (s *PaymentApiService) GetPaymentReceiptPdf(
	ctx context.Context,
	transactionHistory *models.TransactionHistory,
	accountId string,
	language string,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx)
	order, err := s.acctSvcClient.GetOrderById(ctx, accountId, transactionHistory.OrderId)
	if err != nil {
		lg.WithError(err).Info("error failed to retrieve stripe order for receipt pdf")
		return nil, err
	}

	file, err := s.generatePaymentReceiptPdf(
		ctx,
		transactionHistory.OrderId,
		order.StartDate,
		order.ExpiryDate,
		transactionHistory.Plan,
		transactionHistory.Amount,
		transactionHistory.Tax,
		transactionHistory.Total,
		transactionHistory.DiscountAmount,
		transactionHistory.DiscountName,
		order.Card.Type,
		order.Card.MaskedCard,
		language,
	)
	if err != nil {
		lg.WithError(err).Info("error failed to generate receipt pdf")
		return nil, err
	}

	return file, nil
}

func (s *PaymentApiService) PostPaymentIntent(
	ctx context.Context,
	paymentIntentInfo *coreapi.PaymentIntentRequest,
) (accountservice.CreateOrderIntentResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	lg = lg.WithField("acct_id", paymentIntentInfo.AccountID)

	_, country, err := s.acctSvcClient.GetAccountMainRegion(ctx, paymentIntentInfo.AccountID)
	if err != nil {
		lg.WithError(err).Error("could not get account country")
		return accountservice.CreateOrderIntentResponse{}, err
	}

	order := accountservice.NewOrderIntent{
		AccountId:        paymentIntentInfo.AccountID,
		PlanId:           paymentIntentInfo.PlanId,
		ProviderId:       uint64(paymentIntentInfo.ProviderId), // #nosec G115 provider ID > 0
		ZipCode:          "A1A1A1",
		Country:          country,
		PaymentType:      paymentIntentInfo.PaymentType,
		IsActive:         false,
		DisableAutoRenew: paymentIntentInfo.DisableAutoRenew,
		DiscountName:     paymentIntentInfo.DiscountName,
	}

	orderResp, err := s.acctSvcClient.CreateOrderIntent(ctx, order)
	if err != nil {
		lg.WithError(err).Error("failed to create order")
		return accountservice.CreateOrderIntentResponse{}, err
	}

	return orderResp, nil
}

func (s *PaymentApiService) PostPaymentSuccess(
	ctx context.Context,
	paymentSuccessRequest *coreapi.PaymentSuccessRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	lg = lg.WithField("acct_id", paymentSuccessRequest.AccountID)

	_, country, err := s.acctSvcClient.GetAccountMainRegion(ctx, paymentSuccessRequest.AccountID)
	if err != nil {
		lg.WithError(err).Error("could not get account country")
		return err
	}

	updateOrderIntent := accountservice.UpdateOrderIntent{
		AccountId:       paymentSuccessRequest.AccountID,
		OrderId:         paymentSuccessRequest.OrderId,
		Country:         country,
		ZipCode:         "A1A1A1",
		PaymentIntentId: paymentSuccessRequest.PaymentIntentId,
		PaymentMethod:   paymentSuccessRequest.PaymentMethod,
		Source:          paymentSuccessRequest.Source,
	}

	err = s.acctSvcClient.UpdateOrderIntent(ctx, updateOrderIntent)
	if err != nil {
		lg.WithError(err).Error("failed to update order")
		return err
	}

	return nil
}

func (s *PaymentApiService) GetDiscountCode(
	ctx context.Context,
	discountName string,
) (coreapi.DiscountDetails, error) {
	lg := logutils.DebugCtxLogger(ctx)

	discountDetails, err := s.paymentSvcUser.GetDiscountCode(ctx, discountName)
	if err != nil {
		lg.WithError(err).Error("failed to get payment providers")
		return coreapi.DiscountDetails{}, err
	}

	if len(discountDetails) == 0 {
		return coreapi.DiscountDetails{}, fmt.Errorf(
			"no discounts were found for the given discount name",
		)
	}

	if len(discountDetails) > 1 {
		lg.Warn(
			"found more than 1 discount detail that match the discount name, only returning the first discount detail",
		)
	}

	discountDetail := discountDetails[0]

	return discountDetail, nil
}

func SetTransaction(data pmts.Transaction) models.TransactionHistory {
	// Remove the "_Refunded" suffix from plan type
	if data.ChargeInfo.RefundAmount != 0 {
		parts := strings.Split(data.Item, "_")
		if len(parts) > 0 {
			data.Item = parts[0]
		}
	}
	return models.TransactionHistory{
		OrderId:        data.OrderID,
		Plan:           data.Item,
		Amount:         data.ChargeInfo.Amount,
		Tax:            data.ChargeInfo.Tax,
		Total:          data.ChargeInfo.TotalCharged,
		DiscountAmount: data.ChargeInfo.DiscountAmount,
		DiscountName:   data.ChargeInfo.DiscountName,
		RefundedAmount: data.ChargeInfo.RefundAmount,
		Timestamp:      data.ChargeInfo.Timestamp,
	}
}
