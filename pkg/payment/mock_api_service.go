package payment

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
)

// MockPaymentApiService is a service that implents the logic for PaymentApiService for testing
type MockPaymentApiService struct{}

// NewProvidersApiService creates a default api service
func NewMockNewPaymentApiService() coreapi.PaymentApiServicer {
	return &MockPaymentApiService{}
}

func (m *MockPaymentApiService) GetPaymentProviders(
	ctx context.Context,
	country string,
) ([]string, error) {
	return nil, nil
}

func (m *MockPaymentApiService) GetPaymentHistory(
	ctx context.Context,
	accountId string,
) ([]models.TransactionHistory, error) {
	return nil, nil
}

func (m *MockPaymentApiService) GetPaymentReceiptPdf(
	ctx context.Context,
	transactionHistory *models.TransactionHistory,
	accountId string,
	language string,
) ([]byte, error) {
	return nil, nil
}

func (m *MockPaymentApiService) PostPaymentIntent(
	ctx context.Context,
	paymentIntentInfo *coreapi.PaymentIntentRequest,
) (accountservice.CreateOrderIntentResponse, error) {
	return accountservice.CreateOrderIntentResponse{}, nil
}

func (m *MockPaymentApiService) PostPaymentSuccess(
	ctx context.Context,
	paymentSuccessRequest *coreapi.PaymentSuccessRequest,
) error {
	return nil
}

func (m *MockPaymentApiService) GetDiscountCode(
	ctx context.Context,
	discountName string,
) (coreapi.DiscountDetails, error) {
	return coreapi.DiscountDetails{}, nil
}
