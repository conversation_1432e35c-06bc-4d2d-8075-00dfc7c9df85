package payment

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"
	"unicode"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/consentPdf"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	languageTag "golang.org/x/text/language"
	"golang.org/x/text/message"
	liquid "gopkg.in/osteele/liquid.v1"
)

// TODO: refactor this solution (primarily copied from requests_tools.go)
// into reusable handler functions for pdf generation
func (s *PaymentApiService) generatePaymentReceiptPdf(
	ctx context.Context,
	orderId string,
	startDate time.Time,
	endDate time.Time,
	plan string,
	amount int32,
	tax int32,
	total int32,
	discountAmount int32,
	discountName string,
	cardType string,
	lastFour string,
	language string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"order_id": orderId,
	})
	// setup temporary directory for file processing
	// all contents to be torn down at the end of this function
	temporaryFileDirectory := fmt.Sprintf("vault/tmp/prep/%s/RECEIPT", orderId)
	if _, err := os.Stat(temporaryFileDirectory); err != nil {
		err = os.MkdirAll(temporaryFileDirectory, 0o700)
		if err != nil {
			return []byte{}, err
		}
	}
	defer pdfs.CleanupTmpFiles(ctx, temporaryFileDirectory)

	pdfChannels := []chan struct {
		string
		error
	}{}

	pdfChannels = append(
		pdfChannels,
		s.generateBaseReceiptPdf(
			ctx,
			orderId,
			startDate,
			endDate,
			plan,
			amount,
			tax,
			total,
			discountAmount,
			discountName,
			cardType,
			lastFour,
			language,
			temporaryFileDirectory,
		),
	)

	pdfFileResults := make([]struct {
		string
		error
	}, len(pdfChannels))
	for i, pdfChannel := range pdfChannels {
		pdfFileResults[i] = <-pdfChannel
	}

	pdfFileNames := []string{}
	for _, pdfFileResult := range pdfFileResults {
		if pdfFileResult.error != nil {
			lg.WithError(pdfFileResult.error).Error("failed to generate consent pdf component")
			return []byte{}, pdfFileResult.error
		} else {
			pdfFileNames = append(pdfFileNames, pdfFileResult.string)
		}
	}

	finalFilePath := temporaryFileDirectory + "/FINAL.pdf"

	if len(pdfFileNames) == 1 {
		finalFilePath = pdfFileNames[0]
	} else {
		args := append(pdfFileNames, finalFilePath)
		cmd := exec.Command(
			"convert",
			args...,
		) // #nosec G204 hope this is ok :/

		var stderr2 bytes.Buffer
		cmd.Stderr = &stderr2

		err := cmd.Run()
		if err != nil {
			lg.WithError(err).Error(stderr2.String())
			return []byte{}, errors.New(errormsgs.ERR_COJOIN_FAIL)
		}
	}

	file, err := os.ReadFile(finalFilePath) // #nosec G304
	if err != nil {
		lg.WithError(err).Error("failed to read stored consent pdf")
		return []byte{}, err
	}

	return file, nil
}

func (s *PaymentApiService) generateBaseReceiptPdf(
	ctx context.Context,
	orderId string,
	startDate time.Time,
	endDate time.Time,
	plan string,
	amount int32,
	tax int32,
	total int32,
	discountAmount int32,
	discountName string,
	cardType string,
	lastFour string,
	language string,
	temporaryFileDirectory string,
) chan struct {
	string
	error
} {
	channel := make(chan struct {
		string
		error
	})

	go func() {
		defer close(channel)
		lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"order_id": orderId,
		})
		// Format parameters for presentation
		runes := []rune(plan)
		runes[0] = unicode.ToUpper(runes[0])
		plan = string(runes)

		date := time.Now().Format("2006/01/02")
		start := startDate.Format("2006/01/02")
		expiry := endDate.Format("2006/01/02")

		formattedAmount := ""
		formattedTax := ""
		formattedTotal := ""
		formattedDiscountAmount := ""
		if language == "fr-CA" {
			p := message.NewPrinter(languageTag.French)
			formattedAmount = p.Sprintf("%.2f $", float64(amount)/100)
			formattedTax = p.Sprintf("%.2f $", float64(tax)/100)
			formattedTotal = p.Sprintf("%.2f $", float64(total)/100)
			formattedDiscountAmount = p.Sprintf("%.2f $", float64(discountAmount)/100)
		} else {
			formattedAmount = fmt.Sprintf("$%.2f", float64(amount)/100)
			formattedTax = fmt.Sprintf("$%.2f", float64(tax)/100)
			formattedTotal = fmt.Sprintf("$%.2f", float64(total)/100)
			formattedDiscountAmount = fmt.Sprintf("$%.2f", float64(discountAmount)/100)
		}

		parts := strings.Split(lastFour, " ")
		lastFourDigits := parts[len(parts)-1]

		pocketHealthLogoBase64 := pdfs.GetBase64PocketHealthLogo()
		cardLogoBase64 := GetCardImageBase64(ctx, cardType)

		parameters := liquid.Bindings{
			"Date":             date,
			"OrderId":          orderId,
			"StartDate":        start,
			"EndDate":          expiry,
			"Plan":             plan,
			"Subtotal":         formattedAmount,
			"Tax":              formattedTax,
			"Total":            formattedTotal,
			"DiscountAmount":   formattedDiscountAmount,
			"DiscountName":     discountName,
			"CardType":         cardType,
			"LastFour":         lastFourDigits,
			"PocketHealthLogo": pocketHealthLogoBase64,
			"CardLogo":         cardLogoBase64,
		}

		// Populate the html template
		tag, err := languageTag.Parse(language)
		if err != nil {
			lg.WithError(err).Error("Failed to parse language")
			tag = languageTag.English
		}
		populatedTemplate, err := consentPdf.PopulateLiquidTemplate(
			phlanguage.GetTemplateForLanguage(ctx, "receipt.html", tag),
			parameters,
		)
		if err != nil {
			lg.WithError(err).Error("Failed to populate receipt template")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		// Convert populated html template to pdf
		pdfBytes, err := consentPdf.HtmlToPdf(populatedTemplate, 0)
		if err != nil {
			lg.WithError(err).Error("Failed to convert html to pdf")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		// Store temporary file for ImageMagick
		filePath := temporaryFileDirectory + "/MAIN.pdf"
		err = os.WriteFile(filePath, pdfBytes, 0o600)
		channel <- struct {
			string
			error
		}{filePath, err}
	}()

	return channel
}

func GetReceiptPdfParameters(
	ctx context.Context,
	date string,
	orderId string,
	startDate string,
	endDate string,
	plan string,
	amount int32,
	tax int32,
	total int32,
	cardType string,
	lastFour int32,
	pocketHealthLogoBase64 string,
) liquid.Bindings {
	parameters := liquid.Bindings{
		"Date":             date,
		"OrderId":          orderId,
		"StartDate":        startDate,
		"EndDate":          endDate,
		"Plan":             plan,
		"Subtotal":         amount,
		"Tax":              tax,
		"Total":            total,
		"CardType":         cardType,
		"LastFour":         lastFour,
		"PocketHealthLogo": pocketHealthLogoBase64,
	}
	return parameters
}

func GetCardImageBase64(ctx context.Context, cardType string) string {
	lg := logutils.DebugCtxLogger(ctx)
	var fileName string
	switch cardType {
	case "American Express":
		fileName = "Amex_logo.svg.txt"
	case "DinersClub":
		fileName = "Dinersclub_logo.svg.txt"
	case "Discover":
		fileName = "Discover_logo.svg.txt"
	case "JCB":
		fileName = "Jcb_logo.svg.txt"
	case "MasterCard":
		fileName = "Mastercard_logo.svg.txt"
	case "UnionPay":
		fileName = "Unionpay_logo.svg.txt"
	case "Visa":
		fileName = "Visa_logo.svg.txt"
	default:
		fileName = "default_logo.svg.txt"
	}

	file, err := os.Open("assets/card-logos/" + fileName) // #nosec G304
	if err != nil {
		lg.WithError(err).Error("error failed to open card logo file")
	}

	defer func(f *os.File) {
		if err := f.Close(); err != nil {
			lg.WithError(err).Error("error failed to close card logo file")
		}
	}(file)

	bytes, _ := io.ReadAll(file)
	return string(bytes)
}
