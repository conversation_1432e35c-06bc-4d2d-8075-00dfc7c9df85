package payment

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

func TestGetTransactionHistory(t *testing.T) {

	t.Run("valid payment history request", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPrivatePaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		req, err := http.NewRequest("GET", "/v1/payments/history", nil)

		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("2LWDYBQc6Ep6m4Nn9RhBPYfcdj6", "")
		fmt.Println(token)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestGetPaymentReceipt(t *testing.T) {
	t.Run("valid payment receipt request", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPrivatePaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		transaction := &models.TransactionHistory{
			OrderId:   "2XoSrMhD5RrMDjzJsBNgnKRg8TS",
			Plan:      "unlimited",
			Amount:    4900,
			Tax:       637,
			Total:     5537,
			Timestamp: time.Now(),
		}
		body, _ := json.Marshal(transaction)
		req, err := http.NewRequest("POST", "/v1/payments/receipt", bytes.NewReader(body))

		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("2LWDYBQc6Ep6m4Nn9RhBPYfcdj6", "")
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}
