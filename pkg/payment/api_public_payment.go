package payment

import (
	"encoding/json"
	"net/http"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PublicPaymentApiController struct {
	service coreapi.PaymentApiServicer
}

func NewPublicPaymentApiController(s coreapi.PaymentApiServicer) coreapi.PublicPaymentApiRouter {
	return &PublicPaymentApiController{service: s}
}

func (c *PublicPaymentApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetPaymentProviders",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/providers",
			HandlerFunc: c.GetPaymentProviders,
		},
		{
			Name:        "PostPaymentIntent",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/paymentIntent",
			HandlerFunc: c.PostPaymentIntent,
		},
		{
			Name:        "PostPaymentSuccess",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/paymentSuccess",
			HandlerFunc: c.PostPaymentSuccess,
		},
		{
			Name:        "GetDiscountCode",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/discount",
			HandlerFunc: c.GetDiscountCode,
		},
	}
}

func (c *PublicPaymentApiController) GetPathPrefix() string {
	return "/v1/transactions"
}

func (c *PublicPaymentApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

// GetPaymentProviders - Get payment providers
func (c *PublicPaymentApiController) GetPaymentProviders(w http.ResponseWriter, r *http.Request) {
	country, ok := r.URL.Query()["country"]
	if !ok || len(country) < 1 {
		return
	}

	result, err := c.service.GetPaymentProviders(r.Context(), country[0])
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostPaymentIntent - Get payment intent for the user of the selected plan
func (c *PublicPaymentApiController) PostPaymentIntent(w http.ResponseWriter, r *http.Request) {
	paymentIntentInfo := &coreapi.PaymentIntentRequest{}
	var err error
	if err = json.NewDecoder(r.Body).Decode(&paymentIntentInfo); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if !paymentIntentInfo.Valid() {
		httperror.ErrorWithLog(w, r, "Invalid Request Body", http.StatusBadRequest)
		return
	}

	result, err := c.service.PostPaymentIntent(r.Context(), paymentIntentInfo)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostPaymentSuccess - Make the order active after successful express payment
func (c *PublicPaymentApiController) PostPaymentSuccess(w http.ResponseWriter, r *http.Request) {
	paymentSuccessRequest := &coreapi.PaymentSuccessRequest{}
	var err error
	if err = json.NewDecoder(r.Body).Decode(&paymentSuccessRequest); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if !paymentSuccessRequest.Valid() {
		httperror.ErrorWithLog(w, r, "Invalid Request Body", http.StatusBadRequest)
		return
	}

	err = c.service.PostPaymentSuccess(r.Context(), paymentSuccessRequest)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), nil, nil, w)
}

func (c *PublicPaymentApiController) GetDiscountCode(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	query := r.URL.Query()

	discountNameExists := query.Has("discount_name")
	discountName := query.Get("discount_name")

	if !discountNameExists || discountName == "" {
		lg.Error("no discount code was provided")
		httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		return
	}

	discountCode, err := c.service.GetDiscountCode(r.Context(), discountName)
	if err != nil {
		lg.WithError(err).Error("error failed to retrieve discount code information")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), discountCode, nil, w)
}
