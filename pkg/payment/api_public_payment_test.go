package payment

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func TestPostPaymentIntent(t *testing.T) {

	t.Run("valid payment intent request", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentIntentRequest := &coreapi.PaymentIntentRequest{
			PlanId:      7,
			ProviderId:  100,
			PaymentType: "APPLEPAY",
			AccountID:   "some-mock-accountID",
		}
		body, _ := json.Marshal(paymentIntentRequest)
		req, err := http.NewRequest("POST", "/v1/transactions/paymentIntent", bytes.NewReader(body))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("valid payment intent request when disableAutoRenew Flag present", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentIntentRequest := &coreapi.PaymentIntentRequest{
			PlanId:           7,
			ProviderId:       100,
			PaymentType:      "APPLEPAY",
			AccountID:        "some-mock-accountID",
			DisableAutoRenew: false,
		}
		body, _ := json.Marshal(paymentIntentRequest)
		req, err := http.NewRequest("POST", "/v1/transactions/paymentIntent", bytes.NewReader(body))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("invalid payment intent request - only planId present", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentIntentRequest := &coreapi.PaymentIntentRequest{
			PlanId: 7,
		}
		body, _ := json.Marshal(paymentIntentRequest)
		req, err := http.NewRequest("POST", "/v1/transactions/paymentIntent", bytes.NewReader(body))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run(
		"invalid payment intent request - only planId and providerId present",
		func(t *testing.T) {

			service := NewMockNewPaymentApiService()

			controller := NewPublicPaymentApiController(service)
			router, err := coreapi.NewRouter(controller)
			if err != nil {
				t.Fatal(err)
			}

			paymentIntentRequest := &coreapi.PaymentIntentRequest{
				PlanId:     7,
				ProviderId: 100,
			}
			body, _ := json.Marshal(paymentIntentRequest)
			req, err := http.NewRequest(
				"POST",
				"/v1/transactions/paymentIntent",
				bytes.NewReader(body),
			)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			want := http.StatusBadRequest
			if status := rr.Code; status != want {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, want)
			}
		},
	)

	t.Run("invalid payment intent request - userInfo absent", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentIntentRequest := &coreapi.PaymentIntentRequest{
			PlanId:      7,
			ProviderId:  100,
			PaymentType: "APPLEPAY",
		}
		body, _ := json.Marshal(paymentIntentRequest)
		req, err := http.NewRequest("POST", "/v1/transactions/paymentIntent", bytes.NewReader(body))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run(
		"valid payment intent request - userInfo missing some fields but accountID present",
		func(t *testing.T) {

			service := NewMockNewPaymentApiService()

			controller := NewPublicPaymentApiController(service)
			router, err := coreapi.NewRouter(controller)
			if err != nil {
				t.Fatal(err)
			}

			paymentIntentRequest := &coreapi.PaymentIntentRequest{
				PlanId:      7,
				ProviderId:  100,
				PaymentType: "APPLEPAY",
				AccountID:   "some-mock-accountID",
			}
			body, _ := json.Marshal(paymentIntentRequest)
			req, err := http.NewRequest(
				"POST",
				"/v1/transactions/paymentIntent",
				bytes.NewReader(body),
			)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			want := http.StatusOK
			if status := rr.Code; status != want {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, want)
			}
		},
	)

	t.Run("valid payment intent request - when providerId absent", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentIntentRequest := &coreapi.PaymentIntentRequest{
			PlanId:      7,
			PaymentType: "APPLEPAY",
			AccountID:   "some-mock-accountID",
		}
		body, _ := json.Marshal(paymentIntentRequest)
		req, err := http.NewRequest("POST", "/v1/transactions/paymentIntent", bytes.NewReader(body))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestPostPaymentSuccess(t *testing.T) {

	t.Run("valid payment success request", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentSuccessRequest := &coreapi.PaymentSuccessRequest{
			OrderId:         "some-order-id",
			PaymentIntentId: "some-payment-intent-id",
			PaymentMethod:   "some-payment-method",
			AccountID:       "some-mock-accountID",
		}
		body, _ := json.Marshal(paymentSuccessRequest)
		req, err := http.NewRequest(
			"POST",
			"/v1/transactions/paymentSuccess",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("invalid payment intent request - only orderId present", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentSuccessRequest := &coreapi.PaymentSuccessRequest{
			OrderId: "some-order-id",
		}
		body, _ := json.Marshal(paymentSuccessRequest)
		req, err := http.NewRequest(
			"POST",
			"/v1/transactions/paymentSuccess",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run(
		"invalid payment intent request - only orderId and paymentIntentId present",
		func(t *testing.T) {

			service := NewMockNewPaymentApiService()

			controller := NewPublicPaymentApiController(service)
			router, err := coreapi.NewRouter(controller)
			if err != nil {
				t.Fatal(err)
			}

			paymentSuccessRequest := &coreapi.PaymentSuccessRequest{
				OrderId:         "some-order-id",
				PaymentIntentId: "some-payment-intent-id",
			}
			body, _ := json.Marshal(paymentSuccessRequest)
			req, err := http.NewRequest(
				"POST",
				"/v1/transactions/paymentSuccess",
				bytes.NewReader(body),
			)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			want := http.StatusBadRequest
			if status := rr.Code; status != want {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, want)
			}
		},
	)

	t.Run("invalid payment intent request - accountID missing", func(t *testing.T) {

		service := NewMockNewPaymentApiService()

		controller := NewPublicPaymentApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}

		paymentSuccessRequest := &coreapi.PaymentSuccessRequest{
			OrderId:         "some-order-id",
			PaymentIntentId: "some-payment-intent-id",
			PaymentMethod:   "some-payment-method",
		}
		body, _ := json.Marshal(paymentSuccessRequest)
		req, err := http.NewRequest(
			"POST",
			"/v1/transactions/paymentSuccess",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}
