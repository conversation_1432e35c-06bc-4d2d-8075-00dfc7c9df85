package payment

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivatePaymentApiController struct {
	service coreapi.PaymentApiServicer
}

func NewPrivatePaymentApiController(s coreapi.PaymentApiServicer) coreapi.PrivatePaymentApiRouter {
	return &PrivatePaymentApiController{service: s}
}

func (c *PrivatePaymentApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetPaymentHistory",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/history",
			HandlerFunc: c.GetPaymentHistory,
		},
		{
			Name:        "GetPaymentReceiptPdf",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/receipt",
			HandlerFunc: c.GetPaymentReceiptPdf,
		},
		{
			Name:        "GetDiscountCode",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/discount",
			HandlerFunc: c.GetDiscountCode,
		},
	}
}

func (c *PrivatePaymentApiController) GetPathPrefix() string {
	return "/v1/payments"
}

func (c *PrivatePaymentApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *PrivatePaymentApiController) GetPaymentHistory(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		lg.WithError(err).Error("error unauthorized")
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetPaymentHistory(r.Context(), accountId)
	if err != nil {
		lg.WithError(err).Error("error failed to retrieve payment history")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivatePaymentApiController) GetPaymentReceiptPdf(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("Authorization")
	lg := logutils.DebugCtxLogger(r.Context())

	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		lg.WithError(err).Error("error unauthorized")
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	language := r.Header.Get("Language")

	transactionHistory := &models.TransactionHistory{}

	lg.WithField("order_id", transactionHistory.OrderId).Info("order identifier")

	if err = json.NewDecoder(r.Body).Decode(&transactionHistory); err != nil {
		lg.WithError(err).Error("failed to unmarshal transactionHistory")
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	result, err := c.service.GetPaymentReceiptPdf(r.Context(), transactionHistory, accountId, language)

	if err != nil {
		lg.WithError(err).Error("failed to handle receipt pdf generation")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	buffer := bytes.NewBuffer(result)
	w.Header().
		Set("Content-Disposition", "attachment; filename=Receipt_"+transactionHistory.OrderId+".pdf")
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Length", strconv.Itoa(len(buffer.Bytes())))
	if _, err := w.Write(buffer.Bytes()); err != nil {
		lg.WithError(err).Error("failed to write pdf file buffer to connection")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
}

func (c *PrivatePaymentApiController) GetDiscountCode(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	token := r.Header.Get("Authorization")
	_, errAcc := auth.DecodeAccountToken(token)
	if errAcc != nil {
		lg.WithError(errAcc).Error("error unauthorized")
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	query := r.URL.Query()

	discountNameExists := query.Has("discount_name")
	discountName := query.Get("discount_name")

	if !discountNameExists || discountName == "" {
		lg.Error("no discount code was provided")
		httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		return
	}

	discountCode, err := c.service.GetDiscountCode(r.Context(), discountName)
	if err != nil {
		lg.WithError(err).Error("failed to retrieve discount code information")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), discountCode, nil, w)
}
