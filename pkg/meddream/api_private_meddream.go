package meddream

import (
	"net/http"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivateMeddreamAPIController struct {
	service *MeddreamAPIService
}

func NewPrivateMeddreamController(s *MeddreamAPIService) *PrivateMeddreamAPIController {
	return &PrivateMeddreamAPIController{service: s}
}

func (c *PrivateMeddreamAPIController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GenerateToken",
			Method:      http.MethodGet,
			Pattern:     "/generate/{exam_uuid}",
			HandlerFunc: c.GenerateToken,
		},
	}
}

func (c *PrivateMeddreamAPIController) GetPathPrefix() string {
	return "/v1/meddream"
}

// require user to be logged in
func (c *PrivateMeddreamAPIController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

// generate a token for meddream
// ensure first that the user has access to this exam
func (c *PrivateMeddreamAPIController) GenerateToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	lg := logutils.CtxLogger(ctx)
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)

	if err != nil {
		lg.WithError(err).Error("failed to decode token")
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	params := mux.Vars(r)
	examUuid := params["exam_uuid"]
	tokenStr, code := c.service.GetToken(ctx, acctId, examUuid)

	if code != http.StatusOK {
		http.Error(w, http.StatusText(code), code)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), tokenStr, &code, w)
}
