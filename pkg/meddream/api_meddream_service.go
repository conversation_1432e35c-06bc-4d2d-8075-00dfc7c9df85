package meddream

import (
	"context"
	"database/sql"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/exams"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type MeddreamAPIService struct {
	db           *sql.DB
	recSvcClient recordservice.RecordServiceClientInterface
	examService  exams.ExamServiceInterface
}

func NewAPIService(
	db *sql.DB,
	rs recordservice.RecordServiceClientInterface,
	examService exams.ExamServiceInterface,
) *MeddreamAPIService {
	return &MeddreamAPIService{
		db:           db,
		recSvcClient: rs,
		examService:  examService,
	}
}

func (s *MeddreamAPIService) GetToken(
	ctx context.Context,
	accountID string,
	examUUID string,
) (string, int) {
	lg := logutils.CtxLogger(ctx)
	exam, err := s.examService.GetExamBasic(ctx, accountID, examUUID)
	if err != nil {
		lg.WithError(err).Error("failed to fetch exam")
		if err == sql.ErrNoRows {
			return "", http.StatusNotFound
		}
		if _, ok := err.(sqlExams.ErrAccountIdMismatch); ok {
			return "", http.StatusUnauthorized
		}
		return "", http.StatusInternalServerError
	}
	tokenStr, err := s.recSvcClient.GetMeddreamToken(ctx, recordservice.TokenRequest{
		ExamUuid:  examUUID,
		StudyUid:  exam.ExamId,
		AccountId: accountID,
	})
	if err != nil {
		lg.WithError(err).Error("failed to get meddream token")
		return "", http.StatusInternalServerError
	}
	return tokenStr, http.StatusOK
}
