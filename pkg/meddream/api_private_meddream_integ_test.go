//go:build integration
// +build integration

package meddream

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/mock"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestMeddreamAPI(t *testing.T) {
	db := testutils.SetupTestDB(t)
	testExam := exams.CreateAndInsertTestExam(t, db)
	testTokenRequest := recordservice.TokenRequest{
		ExamUuid:  testExam.UUID,
		AccountId: testExam.AccountId,
		StudyUid:  testExam.ExamId,
	}

	t.Cleanup(func() {
		exams.DeleteExam(t, db, testExam.UUID)
	})
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)
	service := NewAPIService(db, recordServiceMock, examServiceMock)
	controller := NewPrivateMeddreamController(service)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	token := auth.MakeAccountAuthToken(testExam.AccountId, "")

	// test controller functions
	t.Run("get a token for an actual exam", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodGet,
			"/v1/meddream/generate/"+testExam.UUID,
			strings.NewReader(""),
		)
		req.Header.Set("Authorization", "Bearer "+token)

		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  //instanceUploadProgressPercent
		)
		study.DicomStudyTags.StudyInstanceUID = testExam.ExamId
		var b *bool
		recordServiceMock.ExpectedCalls = nil
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, testExam.AccountId, false, false, b, []string{testExam.UUID}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)
		recordServiceMock.EXPECT().GetMeddreamToken(mock.Anything, testTokenRequest).Return(
			"mock-token", nil,
		)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusOK {
			t.Fatalf("expected OK status but got: %d", rr.Code)
		}
		res, err := io.ReadAll(rr.Body)
		if err != nil {
			t.Fatal(err)
		}
		if string(res) == "" {
			t.Fatal("expected to get a token but got nothing")
		}
	})

	t.Run("no exam for uuid", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodGet,
			"/v1/meddream/generate/bad-uuid",
			strings.NewReader(""),
		)
		req.Header.Set("Authorization", "Bearer "+token)
		var b *bool
		recordServiceMock.ExpectedCalls = nil
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, testExam.AccountId, false, false, b, []string{"bad-uuid"}).
			Return(
				[]recordservice.PatientStudy{}, nil,
			)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusNotFound {
			t.Fatalf("expected StatusBadRequest status but got: %d", rr.Code)
		}
	})

	t.Run("mismatched account id", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodGet,
			"/v1/meddream/generate/"+testExam.UUID,
			strings.NewReader(""),
		)
		badToken := auth.MakeAccountAuthToken("bad-account", "")
		req.Header.Set("Authorization", "Bearer "+badToken)

		var b *bool
		recordServiceMock.ExpectedCalls = nil
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "bad-account", false, false, b, []string{testExam.UUID}).
			Return(
				[]recordservice.PatientStudy{}, nil,
			)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if rr.Code != http.StatusNotFound {
			t.Fatalf("expected StatusNotFound status but got: %d", rr.Code)
		}
	})
}
