package v2rho

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RhoApiService struct {
	sqldb       *sql.DB
	eiSvcClient examinsights.EIServiceUser
	examService exams.ExamServiceInterface
}

func NewRhoApiService(
	db *sql.DB,
	eiSvcClient examinsights.EIServiceUser,
	examService exams.ExamServiceInterface,
) coreapi.RhoApiServicer {
	return &RhoApiService{
		sqldb:       db,
		eiSvcClient: eiSvcClient,
		examService: examService,
	}
}

func (s *RhoApiService) GetRhoEligibility(
	ctx context.Context,
	accountId string,
	examUuid string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"exam_uuid":  examUuid,
	})

	canAccess, err := s.canAccessExam(ctx, examUuid, accountId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to exam")
		return nil, err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access exam")
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	resp, err := s.eiSvcClient.CheckAnalysisEligibility(ctx, accountId, examinsights.RHO, []string{examUuid})
	if err != nil {
		lg.WithError(err).Error("failed to get rho eligibility")
		return nil, err
	}

	return resp, nil
}

func (s *RhoApiService) PostRhoRequest(
	ctx context.Context,
	accountId string,
	examUuid string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"exam_uuid":  examUuid,
	})

	canAccess, err := s.canAccessExam(ctx, examUuid, accountId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to exam")
		return "", err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access exam")
		return "", errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	exam, err := s.examService.GetExamBasic(ctx, accountId, examUuid)
	if err != nil {
		lg.WithError(err).Error("error getting exam patientId")
		return "", err
	}

	rhoData := examinsights.RhoAnalysisRequest{
		ExamUuid: examUuid,
	}

	jsonData, err := json.Marshal(rhoData)
	if err != nil {
		lg.WithError(err).Error("unable to encode rhoData into json")
		return "", err
	}

	reqId, err := s.eiSvcClient.PostAnalysisRequest(ctx, accountId, exam.PatientId, examinsights.RHO, jsonData)
	if err != nil {
		lg.WithError(err).Error("failed to post rho request")
		return "", err
	}

	return reqId, nil
}

func (s *RhoApiService) canAccessExam(
	ctx context.Context,
	examUuid string,
	accountId string,
) (bool, error) {

	var exam = []string{examUuid}
	found, err := s.examService.ExamsBelongToAccount(ctx, accountId, exam)
	if err != nil {
		return false, err
	}
	if !found {
		return false, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	return found, nil
}
