package v2rho

import (
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type PrivateRhoApiController struct {
	service coreapi.RhoApiServicer
}

func NewPrivateRhoApiController(
	s coreapi.RhoApiServicer,
) coreapi.PrivateRhoApiRouter {
	return &PrivateRhoApiController{service: s}
}

func (c *PrivateRhoApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostRequest",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{examUuid}",
			HandlerFunc: c.PostRhoRequest,
		},
		{
			Name:        "GetEligibility",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/eligible/{examUuid}",
			HandlerFunc: c.GetRhoEligibility,
		},
	}
}

func (c *PrivateRhoApiController) GetPathPrefix() string {
	return "/v2/rho"
}

func (c *PrivateRhoApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateRhoApiController) GetRhoEligibility(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	examUuid := params["examUuid"]
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)

	result, err := c.service.GetRhoEligibility(r.Context(), accountId, examUuid)
	if hasError(&w, r, err) {
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateRhoApiController) PostRhoRequest(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	examUuid := params["examUuid"]
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)

	result, err := c.service.PostRhoRequest(r.Context(), accountId, examUuid)
	if hasError(&w, r, err) {
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func hasError(w *http.ResponseWriter, r *http.Request, err error) bool {
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(*w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(*w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else if err.Error() == errmsg.ERR_INVALID_REQ_BODY {
			httperror.ErrorWithLog(*w, r, errmsg.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		} else if err.Error() == errmsg.ERR_DATA_NOT_VALID {
			httperror.ErrorWithLog(*w, r, errmsg.ERR_UNPROCESSABLE_ENTITY, http.StatusUnprocessableEntity)
		} else {
			httperror.ErrorWithLog(*w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return true
	}
	return false
}
