//go:build integration
// +build integration

package v2rho

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/mock"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"

	_ "github.com/go-sql-driver/mysql"
)

func TestGetRhoEligibility(t *testing.T) {
	db := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	sos := examinsights.EIServiceUser{
		URL:           cfg.ExamInsightsSrvcUrl,
		ApiKey:        cfg.ExamInsightsSrvcApiKey,
		ApiKeySecName: cfg.ExamInsightsSrvcApiKeySecName,
		HttpClient:    http.DefaultClient,
	}

	t.Run("check eligibility - exam does not belong to account", func(t *testing.T) {
		ctx := context.Background()

		orgServiceMock := &orgs.OrgServiceMock{}
		planServiceMock := &planservice.PlanSvcMock{}
		accountServiceMock := &accountservice.AcctSvcMock{}
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).Return(false, nil)
		providersServiceMock := providersservice.NewProviderServiceMock()

		examService := exams.NewExamService(
			db,
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providersServiceMock,
			&exams.MockMigrationHelper{},
		)
		service := NewRhoApiService(
			db,
			examinsights.EIServiceUser{},
			examService,
		)
		_, err := service.GetRhoEligibility(
			ctx,
			"2QTcBrqDPqp3C6z9ASfpX9ISDzk",
			"2BwQ3Abn63pOVwkIQedFWmmYp68",
		)
		if err == nil {
			t.Fatal("expect to get an error but got nothing")
		}

		if err.Error() != errmsg.ERR_NOT_AUTHORIZED {
			t.Fatalf("expect to get %s but got %s", errmsg.ERR_NOT_AUTHORIZED, err.Error())
		}
	})

	t.Run("check eligibility - exam belong to account - eligible", func(t *testing.T) {
		ctx := context.Background()

		doRequestWithCtxMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			res := io.NopCloser(strings.NewReader(`
			{
				"eligible": true,
				"review_status": "pending"
			}`))

			if !(req.HTTPMethod == "POST" &&
				req.TargetURL == sos.URL+"/v2/analysis/eligible?") {
				t.Errorf("unexpected request to prov svc: %s %s", req.HTTPMethod, req.TargetURL)
			}

			return res, http.StatusOK, nil
		}
		sos.SetDoRequest(doRequestWithCtxMock)

		orgServiceMock := &orgs.OrgServiceMock{}
		planServiceMock := &planservice.PlanSvcMock{}
		accountServiceMock := &accountservice.AcctSvcMock{}
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).Return(true, nil)
		providersServiceMock := providersservice.NewProviderServiceMock()

		examService := exams.NewExamService(
			db,
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providersServiceMock,
			&exams.MockMigrationHelper{},
		)
		service := NewRhoApiService(
			db,
			sos,
			examService,
		)

		eligibility, err := service.GetRhoEligibility(
			ctx,
			"1wIQT8jrCCOgKaDAEo7wTk4deSz",
			"1vMqZSPKTQf5nIOPIBauwWQELkL",
		)
		if err != nil {
			t.Fatalf("unable to get rho eligibility: %s", err)
		}

		if err != nil {
			t.Fatalf("expect to get no error but got error %s", err.Error())
		}

		if eligibility == nil {
			t.Fatal("expect to get eligibility response but got nil")
		}

		if !eligibility.(*examinsights.AnalysisEligibilityResp).Eligible {
			t.Fatal("expect to get eligibility true but got false")
		}
	})

	t.Run("check eligibility - exam belong to account - not eligible", func(t *testing.T) {
		ctx := context.Background()

		doRequestWithCtxMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			res := io.NopCloser(strings.NewReader(`
			{
				"eligible": false,
				"review_status": "pending"
			}`))

			if !(req.HTTPMethod == "POST" &&
				req.TargetURL == sos.URL+"/v2/analysis/eligible?") {
				t.Errorf("unexpected request to eisvc: %s %s", req.HTTPMethod, req.TargetURL)
			}
			return res, http.StatusOK, nil
		}
		sos.SetDoRequest(doRequestWithCtxMock)

		orgServiceMock := &orgs.OrgServiceMock{}
		planServiceMock := &planservice.PlanSvcMock{}
		accountServiceMock := &accountservice.AcctSvcMock{}
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).Return(true, nil)
		providersServiceMock := providersservice.NewProviderServiceMock()

		examService := exams.NewExamService(
			db,
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providersServiceMock,
			&exams.MockMigrationHelper{},
		)
		service := NewRhoApiService(
			db,
			sos,
			examService,
		)

		eligibility, err := service.GetRhoEligibility(
			ctx,
			"1wIQT8jrCCOgKaDAEo7wTk4deSz",
			"1vMqZSPKTQf5nIOPIBauwWQELkL",
		)
		if err != nil {
			t.Fatalf("unable to get rho eligibility: %s", err)
		}

		if err != nil {
			t.Fatalf("expect to get no error but got error %s", err.Error())
		}

		if eligibility == nil {
			t.Fatal("expect to get eligibility response but got nil")
		}

		if eligibility.(*examinsights.AnalysisEligibilityResp).Eligible {
			t.Fatal("expect to get eligibility false but got true")
		}
	})
}

func TestPostRhoRequest(t *testing.T) {
	db := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	sos := examinsights.EIServiceUser{
		URL:           cfg.ExamInsightsSrvcUrl,
		ApiKey:        cfg.ExamInsightsSrvcApiKey,
		ApiKeySecName: cfg.ExamInsightsSrvcApiKeySecName,
		HttpClient:    http.DefaultClient,
	}

	t.Run("submit rho request - exam does not belong to account", func(t *testing.T) {
		ctx := context.Background()
		doRequestWithCtxMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			res := io.NopCloser(strings.NewReader(`"some_id"`))

			if !(req.HTTPMethod == "POST" &&
				req.TargetURL == sos.URL+"/v2/analysis?") {
				t.Errorf("unexpected request to eisvc: %s %s", req.HTTPMethod, req.TargetURL)
			}
			return res, http.StatusOK, nil
		}
		sos.SetDoRequest(doRequestWithCtxMock)

		orgServiceMock := &orgs.OrgServiceMock{}
		planServiceMock := &planservice.PlanSvcMock{}
		accountServiceMock := &accountservice.AcctSvcMock{}
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).Return(false, nil)
		providersServiceMock := providersservice.NewProviderServiceMock()

		examService := exams.NewExamService(
			db,
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providersServiceMock,
			&exams.MockMigrationHelper{},
		)
		service := NewRhoApiService(
			db,
			sos,
			examService,
		)
		_, err := service.PostRhoRequest(
			ctx,
			"2QTcBrqDPqp3C6z9ASfpX9ISDzk",
			"2BwQ3Abn63pOVwkIQedFWmmYp68",
		)
		if err == nil {
			t.Fatal("expect to get an error but got nothing")
		}

		if err.Error() != errmsg.ERR_NOT_AUTHORIZED {
			t.Fatalf("expect to get %s but got %s", errmsg.ERR_NOT_AUTHORIZED, err.Error())
		}
	})

	// t.Run("submit rho request - exam belong to account - success", func(t *testing.T) {
	// 	ctx := context.Background()

	// 	doRequestWithCtxMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
	// 		res := io.NopCloser(strings.NewReader(`"some_id"`))

	// 		if !(req.HTTPMethod == "POST" &&
	// 			req.TargetURL == sos.URL+"/v2/analysis?") {
	// 			t.Errorf("unexpected request to eisvc: %s %s", req.HTTPMethod, req.TargetURL)
	// 		}
	// 		return res, http.StatusOK, nil
	// 	}
	// 	sos.SetDoRequest(doRequestWithCtxMock)

	// 	orgServiceMock := &orgs.OrgServiceMock{}
	// 	planServiceMock := &planservice.PlanSvcMock{}
	// 	accountServiceMock := &accountservice.AcctSvcMock{}
	// 	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	// 	providersServiceMock := providersservice.NewProviderServiceMock()

	// 	examService := exams.NewExamService(
	// 		db,
	// 		orgServiceMock,
	// 		recordServiceMock,
	// 		planServiceMock,
	// 		accountServiceMock,
	// 		providersServiceMock,
	// 		&exams.MockMigrationHelper{ShouldMigrateExamsBelongToAccount: true},
	// 	)
	// 	service := NewRhoApiService(
	// 		db,
	// 		sos,
	// 		examService,
	// 	)

	// 	id, err := service.PostRhoRequest(
	// 		ctx,
	// 		"1wIQT8jrCCOgKaDAEo7wTk4deSz",
	// 		"1vMqZSPKTQf5nIOPIBauwWQELkL",
	// 	)
	// 	if err != nil {
	// 		t.Fatalf("expect to get no error but got error %s", err.Error())
	// 	}

	// 	if id != "some_id" {
	// 		t.Fatalf("expect to get new_id back but got %s", id)
	// 	}
	// })
}
