/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package internalsvc

import (
	"net/http"
	"strings"

	"github.com/gorilla/mux"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateInternalApiController binds http requests to an api service and writes the service results to the http response
type PrivateInternalApiController struct {
	service coreapi.InternalApiServicer
}

// NewPrivateInternalApiController creates a default api controller
func NewPrivateInternalApiController(
	s coreapi.InternalApiServicer,
) coreapi.PrivateInternalApiRouter {
	return &PrivateInternalApiController{service: s}
}

// Routes returns all of the api route for the PrivateInternalApiController
func (c *PrivateInternalApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "DeletePHIProfile",
			Method:      strings.ToUpper("Delete"),
			Pattern:     "/patients/{patient_id}/phiprofiles",
			HandlerFunc: c.DeletePHIProfile,
		},
	}
}

func (c *PrivateInternalApiController) GetPathPrefix() string {
	return "/v1/internal"
}

func (c *PrivateInternalApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all PrivateSubscriptions paths require auth
	return []func(next http.Handler) http.Handler{}
}

// AccountService -> CoreAPI interactions will use X-PH-Signature header for auth
// The payload of the signature is the URI + the body
func isValidASAuth(r *http.Request, signature string) bool {
	return auth.ValidateAcctSvcSignature(r, signature)
}

func (c *PrivateInternalApiController) DeletePHIProfile(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	if !isValidASAuth(r, r.Header.Get("X-PH-Signature")) {
		lg.WithField("auth_header", r.Header.Get("X-PH-Signature")).Error("invalid signature")
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	patientId := params["patient_id"]
	if patientId == "" {
		lg.Error("can't parse patient_id")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err := c.service.DeletePHIProfile(r.Context(), patientId)
	if err != nil {
		lg.WithError(err).Errorf("could not get delete PHI profile for patient_id:%s ", patientId)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

}
