package internalsvc

import (
	"context"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/signature"
)

type MockInternalService struct {

	// for DeletePHIProfile
	expectedPatientID string

	// shared by all funcs
	expectError bool
	mockError   error
}

func (m *MockInternalService) DeletePHIProfile(
	ctx context.Context,
	account_id string,
) error {
	if m.expectError {
		return m.mockError
	}
	return nil
}

func TestDeletePHIProfile(t *testing.T) {
	ctx := context.Background()
	privateKey := testutils.SetupXPhSignaturePrivKey(t)

	t.Run(
		"/v1/internal/patients/{testPtid}/phiprofiles should return 200 ok if no errors",
		func(t *testing.T) {
			testPatientId := "test1234"

			unit := &MockInternalService{
				expectedPatientID: testPatientId,
			}

			controller := NewPrivateInternalApiController(unit)
			router, err := coreapi.NewRouter(controller)
			if err != nil {
				t.Fatal("unexpected error creating router:", err)
			}
			rec := httptest.NewRecorder()
			endpoint := fmt.Sprintf("/v1/internal/patients/%s/phiprofiles", testPatientId)

			req, err := http.NewRequestWithContext(ctx, "DELETE", endpoint, strings.NewReader(""))
			if err != nil {
				t.Fatal("error forming http request:", err)
			}

			sign, err := signature.SignBase64Enc(
				append([]byte(req.URL.Path), []byte(req.URL.RawQuery)...),
				privateKey,
			)
			if err != nil {
				t.Fatalf("failed to sign payload. err: %q", err)
			}
			req.Header.Add("X-PH-Signature", sign)

			router.ServeHTTP(rec, req)

			if rec.Code != http.StatusOK {
				t.Errorf("unexpected status code, want: %d, got: %d", http.StatusOK, rec.Code)
			}
		},
	)

	t.Run(
		"/v1/internal/patients/{testPtid}/phiprofiles returns 401 on invalid signature",
		func(t *testing.T) {
			// use regionrouter private key
			badPrivKey := SetUpTestRRPrivKey(t)

			unit := &MockInternalService{}

			controller := NewPrivateInternalApiController(unit)
			router, err := coreapi.NewRouter(controller)
			if err != nil {
				t.Fatal("unexpected error creating router:", err)
			}

			rec := httptest.NewRecorder()
			endpoint := "/v1/internal/patients/testId/phiprofiles"

			req, err := http.NewRequestWithContext(ctx, "DELETE", endpoint, strings.NewReader(""))
			if err != nil {
				t.Fatal("error forming http request:", err)
			}

			sign, err := signature.SignBase64Enc([]byte(""), badPrivKey)
			if err != nil {
				t.Fatalf("failed to sign payload. err: %q", err)
			}
			req.Header.Add("X-PH-Signature", sign)

			router.ServeHTTP(rec, req)

			if rec.Code != http.StatusUnauthorized {
				t.Errorf(
					"unexpected status code, want: %d, got: %d",
					http.StatusUnauthorized,
					rec.Code,
				)
			}
		},
	)
}

func SetUpTestRRPrivKey(t *testing.T) *ecdsa.PrivateKey {
	t.Helper()

	privateKey, err := ecdsa.GenerateKey(elliptic.P384(), rand.Reader)
	if err != nil {
		t.Fatal("error generating private key:", err)
	}
	publicKey := &privateKey.PublicKey

	x509EncodedPub, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		t.Fatal("error converting public key:", err)
	}
	pemEncodedPub := pem.EncodeToMemory(&pem.Block{Type: "PUBLIC KEY", Bytes: x509EncodedPub})
	err = auth.SetRegionRouterPublicKey(pemEncodedPub)
	if err != nil {
		t.Fatal(err)
	}

	return privateKey
}
