/*
* Core API
*
* Core API for PocketHealth
*
* API version: 1.0
* Contact: <EMAIL>
* Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package internalsvc

import (
	"context" //#nosec G505
	"database/sql"
	"sync"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	sqlPhiProfiles "gitlab.com/pockethealth/coreapi/pkg/mysql/phi_profiles"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type InternalApiService struct {
	sqldb      *sql.DB
	waitGroup  *sync.WaitGroup
	AcctClient accountservice.AccountService
}

func NewInternalApiService(
	db *sql.DB,
	waitGroup *sync.WaitGroup,
	as accountservice.AccountService,
) coreapi.InternalApiServicer {
	return &InternalApiService{sqldb: db, waitGroup: waitGroup, AcctClient: as}
}

func (r *InternalApiService) DeletePHIProfile(
	ctx context.Context,
	patientId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("patient_id", patientId)

	err := sqlPhiProfiles.DeletePHIProfile(ctx, r.sqldb, patientId)
	if err != nil {
		lg.WithError(err).Error("error deleting phi profile for account")
		return err
	}

	return nil
}
