package lockouttracker

import (
	"context"
	"database/sql"
	"time"

	"github.com/sirupsen/logrus"
	lockouts "gitlab.com/pockethealth/coreapi/pkg/mysql/lockouts"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

const (
	// 5 minute lockout duration
	Duration        = time.Second * 300
	AllowedAttempts = 5
)

// create an interface so it can be mocked for testing
type LockoutTracker interface {
	Init(db *sql.DB)
	IncrementAttempts(ctx context.Context, resource string)
	IsLocked(ctx context.Context, resource string) bool
	Reset(ctx context.Context, resource string)
}

type DBLockoutTracker struct {
	db *sql.DB
}

func (lt *DBLockoutTracker) Init(sqldb *sql.DB) {
	lt.db = sqldb
}

func (lt *DBLockoutTracker) IncrementAttempts(ctx context.Context, resource string) {
	var attempts int
	attempts, err := lockouts.GetResourceAttempts(ctx, lt.db, resource)
	if err != nil {
		attempts = 1
		err = lockouts.InsertAttempt(ctx, lt.db, resource, attempts)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"resource":  resource,
				"error_msg": err.Error(),
			}).Error("insert attempt failed")
		}
	} else {
		attempts = attempts + 1

		if attempts >= AllowedAttempts {
			err = lockouts.LockResource(ctx, lt.db, resource)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
					"resource":  resource,
					"error_msg": err.Error(),
				}).Error("lock resource failed")
			}
		} else {
			err = lockouts.IncrementAttempts(ctx, lt.db, resource)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
					"resource":  resource,
					"error_msg": err.Error(),
				}).Error("increment lockout tracker attempts failed")
			}
		}
	}
}

func (lt *DBLockoutTracker) IsLocked(ctx context.Context, resource string) bool {
	locktime, err := lockouts.GetLockedTime(ctx, lt.db, resource)
	if err != nil {
		return false
	} else {
		if locktime.Add(Duration).After(time.Now()) {
			return true
		} else {
			lt.Reset(ctx, resource)
			return false
		}
	}
}

func (lt *DBLockoutTracker) Reset(ctx context.Context, resource string) {
	err := lockouts.ResetResource(ctx, lt.db, resource)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"resource":  resource,
			"error_msg": err.Error(),
		}).Error("reset resource failed")
	}
}
