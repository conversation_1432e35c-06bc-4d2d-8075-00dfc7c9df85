package lockouttracker

import (
	"context"
	"database/sql"
	"fmt"
	"time"
)

type MockLockoutTracker struct {
	Attempts map[string]int
	Locks    map[string]time.Time
}

func NewMockLockoutTracker() LockoutTracker {
	return &MockLockoutTracker{Attempts: make(map[string]int), Locks: make(map[string]time.Time)}
}

func (lt *MockLockoutTracker) Init(db *sql.DB) {
	//noop
}
func (lt *MockLockoutTracker) IncrementAttempts(ctx context.Context, resource string) {
	if count, ok := lt.Attempts[resource]; ok {
		count = count + 1
		lt.Attempts[resource] = count
	} else {
		count = 1
		lt.Attempts[resource] = count
	}
	fmt.Println(lt.Attempts[resource])
	if lt.Attempts[resource] >= AllowedAttempts {
		lt.Locks[resource] = time.Now()
	}
}

func (lt *MockLockoutTracker) IsLocked(ctx context.Context, resource string) bool {
	if lock, ok := lt.Locks[resource]; ok {
		if lock.Add(Duration).After(time.Now()) {
			return true
		} else {
			lt.Reset(ctx, resource)
			return false
		}
	} else {
		return false
	}
}

func (lt *MockLockoutTracker) Reset(ctx context.Context, resource string) {
	delete(lt.Attempts, resource)
	delete(lt.Locks, resource)
}
