//go:build integration
// +build integration

package users

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/reports"
	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"

	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	mysqlobjects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/exams"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/recordstreaming"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	_ "github.com/go-sql-driver/mysql"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/samber/lo"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"golang.org/x/text/language"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	scansStore "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	examstestutils "gitlab.com/pockethealth/coreapi/pkg/testutils/exams"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	integtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestNotifications(t *testing.T) {
	db := testutils.SetupTestDB(t)
	var err error

	// Mock (empty) variables for user service
	var containerClient azureUtils.ContainerClient
	i18nBundle := i18n.NewBundle(language.English)
	mockLangProviders := languageproviders.LanguageTagProviders{}
	mailer, _ := cioEmail.NewMock()
	mockEmailSender := cio_email.ConfigureMail(mailer, map[string]string{"friend_referral": "21"})
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}
	testRegionID := uint16(1)
	regions.SetRegionID(testRegionID)
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{
		GetAccountInfoReturn: accountservice.Account{MainRegion: 1},
	}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	service := NewUsersApiService(
		db,
		containerClient,
		mockEmailSender,
		"qa.pocket.health",
		i18nBundle,
		mockLangProviders,
		mockSupportedLanguages,
		accountServiceMock,
		orgServiceMock,
		planServiceMock,
		nil,
		false,
		nil,
		nil,
		providersServiceMock,
		nil,
		nil,
		nil,
		examServiceMock,
		nil,
		nil,
		roiServiceMock,
	)

	t.Run(
		"When notification marked as read, should change state of read in db",
		func(t *testing.T) {
			ctx := context.Background()
			rand.Seed(time.Now().UnixNano())
			id := ksuid.New().String()
			acctId := ksuid.New().String()
			_, err = db.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp) VALUES (?,?,?,?,?)",
				id,
				acctId,
				"TEST_NOTIFICAITON",
				0,
				"2023-05-031T00:00:00Z",
			)
			if err != nil {
				t.Fatalf("error setting up test: %v", err)
			}
			errPut := service.UpdateNotificationAsReadById(ctx, acctId, id)
			if errPut != nil {
				t.Fatalf("error to mark notification as read: %s\n", err.Error())
			}

			var read bool
			errRead := db.QueryRow("SELECT un.is_read FROM user_notifications un where id= ?", id).
				Scan(&read)
			if errRead != nil {
				t.Fatalf("error checking read value from user_notifications : %v", err)
			}

			if !read {
				t.Fatalf("Notification wasn't marked as read : ")
			}

			t.Cleanup(func() {
				db.Exec("delete from user_notifications where id = ?", id)
			})
		},
	)

	t.Run(
		"When all notifications are read, should not return any notification if includeRead is false",
		func(t *testing.T) {
			ctx := context.Background()
			id := ksuid.New().String()
			acctId := ksuid.New().String()
			_, err = db.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp) VALUES (?,?,?,?,?)",
				id,
				acctId,
				"TEST_NOTIFICAITON",
				1,
				"2023-05-031T00:00:00Z",
			)
			if err != nil {
				t.Fatalf("error setting up test: %v", err)
			}

			existingNotification, errExisting := service.GetNotificationsByAccount(
				ctx,
				acctId,
				false,
			)
			if errExisting != nil {
				t.Fatalf("error to call get notification: %s\n", err.Error())
			}

			if len(existingNotification.Notifications) != 0 {
				t.Fatalf("Notification returned after marking as read")
			}

			t.Cleanup(func() {
				db.Exec("delete from user_notifications where id = ?", id)
			})
		},
	)

	t.Run(
		"When all notifications are read, should return notifications if includeRead is true",
		func(t *testing.T) {
			ctx := context.Background()
			id := ksuid.New().String()
			acctId := ksuid.New().String()
			_, err = db.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp) VALUES (?,?,?,?,?)",
				id,
				acctId,
				"TEST_NOTIFICAITON",
				1,
				"2023-05-031T00:00:00Z",
			)
			require.NoError(t, err, "could not set up test")

			existingNotification, errExisting := service.GetNotificationsByAccount(
				ctx,
				acctId,
				true,
			)
			require.NoError(t, errExisting, "error to call get notification\n")

			assert.Len(t, existingNotification.Notifications, 1)

			t.Cleanup(func() {
				db.Exec("delete from user_notifications where id = ?", id)
			})
		},
	)

	t.Run("When existing unread notification, should return the notification", func(t *testing.T) {
		ctx := context.Background()
		id := ksuid.New().String()
		acctId := ksuid.New().String()
		_, err = db.Exec(
			"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp) VALUES (?,?,?,?,?)",
			id,
			acctId,
			"TEST_NOTIFICAITON",
			0,
			"2023-05-031T00:00:00Z",
		)
		if err != nil {
			t.Fatalf("error setting up test: %v", err)
		}

		existingNotification, errExisting := service.GetNotificationsByAccount(ctx, acctId, false)
		if errExisting != nil {
			t.Fatalf("error to call get notification: %s\n", err.Error())
		}

		if len(existingNotification.Notifications) != 1 {
			t.Fatalf("Existing notification wasn't returned")
		}

		t.Cleanup(func() {
			db.Exec("delete from user_notifications where id = ?", id)
		})
	})

	t.Run("When creating a notification", func(t *testing.T) {
		ctx := context.Background()
		acctId := ksuid.New().String()
		_, err = db.Exec(
			"INSERT INTO user_notifications (id, account_id, name) VALUES (?,?,?)",
			ksuid.New().String(),
			acctId,
			coreapi.NotificationMultiRegion,
		)
		require.NoError(t, err)

		t.Cleanup(func() {
			db.Exec("DELETE FROM user_notifications WHERE account_id = ?", acctId)
		})

		testCases := []struct {
			TestName         string
			AccountId        string
			NotificationType coreapi.NotificationType
			ExceptedError    bool
			ErrorMsg         string
		}{
			{
				TestName:         "and the notification type is not valid",
				NotificationType: coreapi.NotificationType("CHEDDAR_CHEESE"),
				ExceptedError:    true,
				ErrorMsg:         errormsgs.ERR_INVALID_REQ_BODY,
			},
			{
				TestName:         "and the notification type is valid, and the account already has a notification of this kind",
				AccountId:        acctId,
				NotificationType: coreapi.NotificationMultiRegion,
				ExceptedError:    true,
				ErrorMsg:         errormsgs.ERR_CONFLICT,
			},
			{
				TestName:         "and the notification type is valid",
				AccountId:        acctId,
				NotificationType: coreapi.NotificationPatientInfoIncomplete,
			},
		}

		for _, test := range testCases {
			t.Run(test.TestName, func(t *testing.T) {
				newNotification, err := service.CreateUserNotification(
					ctx,
					test.AccountId,
					test.NotificationType,
				)
				if test.ExceptedError {
					require.Error(t, err)
					assert.EqualError(t, err, test.ErrorMsg)
				} else {
					require.NoError(t, err)
					assert.NotEmpty(t, newNotification.Id)
					assert.Equal(t, newNotification.Type, test.NotificationType)
				}
			})
		}
	})

	t.Run("When deleting a notification", func(t *testing.T) {
		ctx := context.Background()
		notifId := ksuid.New().String()
		acctId := ksuid.New().String()
		_, err = db.Exec(
			"INSERT INTO user_notifications (id, account_id, name) VALUES (?,?,?)",
			notifId,
			acctId,
			"TEST_NOTIFICATION",
		)
		require.NoError(t, err)

		err := service.DeleteUserNotificationById(ctx, acctId, notifId)
		require.NoError(t, err)
		row, err := db.Query("SELECT * FROM user_notifications WHERE id = ?", notifId)
		defer row.Close()
		require.NoError(t, err)
		assert.False(t, row.Next())
	})
}

func TestExams(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// Mock (empty) variables for user service
	var containerClient azureUtils.ContainerClient
	i18nBundle := i18n.NewBundle(language.English)
	mockLangProviders := languageproviders.LanguageTagProviders{}
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}
	mailer, _ := cioEmail.NewMock()
	mockEmailSender := cio_email.ConfigureMail(mailer, map[string]string{"friend_referral": "21"})

	// mockConvertImage returns an image with the imageId as a byte array for validating the imageId after running this mock function
	mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
		data := []byte(imageId)
		return io.NopCloser(bytes.NewBuffer(data)), int64(len(data)), nil
	}
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	service := NewUsersApiService(
		db,
		containerClient,
		mockEmailSender,
		"qa.pocket.health",
		i18nBundle,
		mockLangProviders,
		mockSupportedLanguages,
		accountServiceMock,
		orgServiceMock,
		planServiceMock,
		&sync.WaitGroup{},
		false,
		nil,
		nil,
		providersServiceMock,
		mockConvertImage,
		nil,
		nil,
		examServiceMock,
		nil,
		nil,
		roiServiceMock,
	)

	getThumbnailTestCases := []struct {
		name          string
		rolloutPct    int
		imageIDResult []byte
	}{
		{
			name:       "Test get exam thumbnail by UUID without record streaming rollout",
			rolloutPct: 0,
		},
		{
			name:       "Test get exam thumbnail by UUID with record streaming rollout",
			rolloutPct: 100,
		},
	}

	for _, tc := range getThumbnailTestCases {
		t.Run(tc.name, func(t *testing.T) {
			var originalRolloutPct int
			err := db.QueryRow(
				"SELECT pct FROM rollout WHERE name='use_first_available_record_streaming_thumbnail'",
			).Scan(&originalRolloutPct)
			require.NoError(t, err)
			_, err = db.Exec(
				`INSERT INTO rollout (name, pct) VALUES (?, ?) ON DUPLICATE KEY UPDATE pct=VALUES(pct)`,
				"use_first_available_record_streaming_thumbnail",
				tc.rolloutPct,
			)
			require.NoError(t, err)

			acctID := integtestutils.GenerateRandomString(t, 10)
			exam := examstestutils.CreateAndInsertTestExam(t, db, lo.ToPtr(acctID))
			examUUID := exam.UUID
			shareID := integtestutils.GenerateRandomString(t, 10)
			objectID := integtestutils.GenerateRandomString(t, 10)

			createExamWithObjects(t, db, acctID, exam, shareID, objectID)

			t.Cleanup(func() {
				_, err = db.Exec(
					`INSERT INTO rollout (name, pct) VALUES (?, ?) ON DUPLICATE KEY UPDATE pct=VALUES(pct)`,
					"use_first_available_record_streaming_thumbnail",
					originalRolloutPct,
				)
			})
			ctx := context.WithValue(
				context.Background(),
				logutils.CorrelationIdContextKey,
				"api_users_service_integ_test_"+acctID,
			)
			resultReader, _, err := service.GetExamThumbnailByUuid(
				ctx,
				examUUID,
				acctID,
				shareID,
			)
			require.NoError(t, err)

			result, err := io.ReadAll(resultReader)
			require.NoError(t, err)

			require.Equal(t, objectID, string(result))
		})
	}

	testObjectID := integtestutils.GenerateRandomString(t, 10)
	getRecordStreamingThumbnailTestCases := []struct {
		name          string
		rolloutPct    int
		objectID      string
		imageIDResult []byte
		expectedErr   string
	}{
		{
			name:          "Test get exam thumbnail by UUID for record streaming without rollout",
			rolloutPct:    0,
			objectID:      integtestutils.GenerateRandomString(t, 10),
			imageIDResult: []byte{},
			expectedErr:   "Not found",
		},
		{
			name:          "Test get exam thumbnail by UUID for record streaming with rollout",
			rolloutPct:    100,
			objectID:      testObjectID,
			imageIDResult: []byte(testObjectID),
			expectedErr:   "",
		},
	}

	for _, tc := range getRecordStreamingThumbnailTestCases {
		t.Run(tc.name, func(t *testing.T) {
			var originalRolloutPct int
			err := db.QueryRow(
				"SELECT pct FROM rollout WHERE name='use_first_available_record_streaming_thumbnail'",
			).Scan(&originalRolloutPct)
			require.NoError(t, err)
			_, err = db.Exec(
				`INSERT INTO rollout (name, pct) VALUES (?, ?) ON DUPLICATE KEY UPDATE pct=VALUES(pct)`,
				"use_first_available_record_streaming_thumbnail",
				tc.rolloutPct,
			)
			require.NoError(t, err)

			// Setup test data
			providerID := integtestutils.GenerateRandomInt64(t)
			physicianAccountID := integtestutils.GenerateRandomString(t, 10)
			objectID := tc.objectID
			studyUID := integtestutils.GenerateRandomString(t, 10)
			examUUID, instanceUID := recordstreaming.SetupRecordStreamingData(
				t,
				db,
				providerID,
				physicianAccountID,
				studyUID, // studyUID
				objectID,
				false, // is report uploaded
			)

			// Setup providersservice mock
			uploadStatusStudyResp := models.UploadStatusStudy{
				StudyUID:        studyUID,
				ProviderID:      providerID,
				UploadCompleted: true,
				UploadManifest: models.StudyUploadManifest{
					Study:  true,
					Report: false,
					Instances: map[string]bool{
						"1.2.3.4.100.354": false,
						instanceUID:       true,
						"1.2.3.4.100.356": false,
					},
				},
			}
			providersServiceMock.SetupReturn("GetUploadStatusForStudyProvider", struct {
				Value *models.UploadStatusStudy
				Err   error
			}{Value: &uploadStatusStudyResp, Err: nil})

			// Setup share data to make thumbnail accessible
			acctID := integtestutils.GenerateRandomString(t, 10)
			shareID := integtestutils.GenerateRandomString(t, 10)
			shareobjects.InsertShareObject(t, db, shareID, objectID, false)
			t.Cleanup(func() {
				shareobjects.DeleteShareObject(t, db, shareID, objectID)
				_, err = db.Exec(
					`INSERT INTO rollout (name, pct) VALUES (?, ?) ON DUPLICATE KEY UPDATE pct=VALUES(pct)`,
					"use_first_available_record_streaming_thumbnail",
					originalRolloutPct,
				)
			})

			ctx := context.WithValue(
				context.Background(),
				logutils.CorrelationIdContextKey,
				"api_users_service_integ_test_"+acctID,
			)
			resultReader, _, err := service.GetExamThumbnailByUuid(
				ctx,
				examUUID,
				acctID,
				shareID,
			)
			if err != nil {
				require.Equal(t, tc.expectedErr, err.Error())
			} else {
				result, err := io.ReadAll(resultReader)
				require.NoError(t, err)

				require.Equal(t, tc.imageIDResult, result)

			}
		})
	}
}

func TestRegister(t *testing.T) {
	// go test uses package location as working directory; need to change to project dir so function under test can locate the email templates
	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	db := testutils.SetupTestDB(t)

	// Mock (empty) variables for user service
	var waitGroup *sync.WaitGroup
	waitGroup = &sync.WaitGroup{}
	var containerClient azureUtils.ContainerClient
	mailer, _ := cioEmail.NewMock()
	mockEmailSender := cio_email.ConfigureMail(mailer, map[string]string{"friend_referral": "21"})
	i18nBundle := i18n.NewBundle(language.English)
	mockProvider := phlanguage.LanguageTagProvider{
		GetLanguageTag: func(ctx context.Context, id interface{}) language.Tag { return language.English },
	}
	mockLangProviders := languageproviders.LanguageTagProviders{
		AccountId: mockProvider,
		ClinicId:  mockProvider,
		OrgId:     mockProvider,
	}
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	acctservice := accountservice.NewHTTPAccountServiceClient(
		qaAcctSvcUrl,
		acctServiceUser,
		acctSvcApiKey,
		httpClient,
	)

	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		acctservice,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	service := NewUsersApiService(
		db,
		containerClient,
		mockEmailSender,
		"qa.pocket.health",
		i18nBundle,
		mockLangProviders,
		mockSupportedLanguages,
		acctservice,
		orgServiceMock,
		planServiceMock,
		waitGroup,
		false,
		experimentsClient,
		nil,
		providersServiceMock,
		nil,
		nil,
		nil,
		examServiceMock,
		nil,
		nil,
		roiServiceMock,
	)
	t.Run("no transfer, valid post", func(t *testing.T) {
		rndStr, _ := secure.GenerateRandomString(8)
		r := coreapi.RegisterData{
			Email:     "grace+" + rndStr + "@example.com",
			Password:  "1234test1234",
			FirstName: "first",
			LastName:  "last",
			DOB:       time.Date(1997, 9, 9, 0, 0, 0, 0, time.UTC),
		}
		ctx := context.WithValue(
			context.TODO(),
			logutils.CorrelationIdContextKey,
			"notransfervalidpost",
		)
		_, err := service.PostUsers(ctx, r, "", "someip", "")
		if err != nil {
			t.Fatalf("post users got error when expected none: %v", err)
		}
	})

	t.Run("user exists", func(t *testing.T) {
		r := coreapi.RegisterData{
			Email:    "<EMAIL>",
			Password: "1234test1234",
		}
		ctx := context.WithValue(context.TODO(), logutils.CorrelationIdContextKey, "userexists")

		_, err := service.PostUsers(ctx, r, "", "", "")
		if err != nil {
			t.Fatalf("expected error to be nil but it was %q", err.Error())
		}
	})

	t.Run("bad password", func(t *testing.T) {
		rndStr, _ := secure.GenerateRandomString(8)
		r := coreapi.RegisterData{
			Email:     "grace+" + rndStr + "@example.com",
			Password:  "zag12wsx", // from the breached passwords list
			FirstName: "first",
			LastName:  "last",
			DOB:       time.Date(1997, 9, 9, 0, 0, 0, 0, time.UTC),
		}
		ctx := context.WithValue(
			context.TODO(),
			logutils.CorrelationIdContextKey,
			"badpassword",
		)
		actual := accountservice.ErrBadNewPassword{}
		_, err := service.PostUsers(ctx, r, "", "someip", "")
		require.NotNil(t, err, "expected error for bad password")
		require.True(t, errors.As(err, &actual), "expected bad password error")
	})
}

// func TestTriggerOrganvizInference(t *testing.T) {
// 	// Assemble test env
// 	accountId := "27nakqZdHKADEuxK8kgfROJimwo"
// 	organvizMock := organviz.MockOrganvizService{
// 		EligibleExams: []string{
// 			// These exams are in the QA database already, associated with the
// 			// account id set above
// 			"1rDFmkRKBmF3K12Y1CVGLJwcJgN",
// 			"1qX62nMai0ruAmZOrrfW9bHMKlc",
// 		},
// 	}

// 	db := testutils.SetupTestDB(t)
// 	var containerClient azureUtils.ContainerClient
// 	orgServiceMock := &orgs.OrgServiceMock{}
// 	planServiceMock := &planservice.PlanSvcMock{}
// 	accountServiceMock := &accountservice.AcctSvcMock{}
// 	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
// 	providersServiceMock := providersservice.NewProviderServiceMock()

// 	examServiceMock := examService.NewExamService(
// 		db,
// 		orgServiceMock,
// 		recordServiceMock,
// 		planServiceMock,
// 		accountServiceMock,
// 		providersServiceMock,
// 		&examService.MockMigrationHelper{},
// 	)

// 	service := NewUsersApiService(
// 		db,
// 		containerClient,
// 		nil,
// 		"qa.pocket.health",
// 		nil,
// 		languageproviders.LanguageTagProviders{},
// 		nil,
// 		nil,
// 		orgServiceMock,
// 		nil,
// 		&sync.WaitGroup{},
// 		false, /* excludeSoftDeletedExams */
// 		nil,
// 		nil,
// 		&organvizMock,
// 		providersServiceMock,
// 		nil,
// 		nil,
// 		nil,
// 		examServiceMock,
// 	)

// 	q, err := queries.NewWhere(nil)
// 	require.NoError(t, err)

// 	// Act
// 	err = service.TriggerOrganVisualizationInference(
// 		context.Background(),
// 		accountId,
// 		q,
// 	)
// 	require.NoError(t, err)

// 	// Assert that all eligible reports were sent to reportprocessor
// 	expectedReports := []string{
// 		// These reports are in the QA database already, associated with the
// 		// account id set above
// 		"AkQnl0iE_onlMoyqjyq-6m-6S4XA4rzTCbx3EiBakP8=",
// 		"PPFH8T716VZpfG5yOtXB_DMaYD3QC-itzhUoFUmJdAc=",
// 	}
// 	assert.ElementsMatch(t, expectedReports, organvizMock.FetchedVizForReports)
// }

func TestGetExamInsightsEligibility(t *testing.T) {
	phdb := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	sos := examinsights.EIServiceUser{
		URL:           cfg.ExamInsightsSrvcUrl,
		ApiKey:        cfg.ExamInsightsSrvcApiKey,
		ApiKeySecName: cfg.ExamInsightsSrvcApiKeySecName,
		HttpClient:    http.DefaultClient,
	}
	sos.SetDoRequest(nil)
	ri := reportinsights.NewReportInsightsMock()
	ri.SetupReturn("GetFollowups", struct {
		Value map[string]reportinsights.FollowUpsResp
		Err   error
	}{Value: map[string]reportinsights.FollowUpsResp{
		"BeLMzKDelsbY2kvwpev6C7YwebJKzmCyrL2rXEaome0=": {
			Exist:       true,
			Occurrences: make([]reportinsights.FollowUpOccurrence, 0),
		},
	}, Err: nil})

	var containerClient azureUtils.ContainerClient
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{AccountHasAccessToPatientReturn: true}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}

	examServiceMock := examService.NewExamService(
		phdb,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	t.Run("no exams in account for patient", func(t *testing.T) {
		ctx := context.Background()

		s := NewUsersApiService(phdb,
			containerClient,
			nil,
			"qa.pocket.health",
			nil,
			languageproviders.LanguageTagProviders{},
			nil,
			accountServiceMock,
			orgServiceMock,
			nil,
			&sync.WaitGroup{},
			false, /* excludeSoftDeletedExams */
			nil,
			nil,
			providersServiceMock,
			nil,
			&sos,
			nil,
			examServiceMock,
			nil,
			ri,
			roiServiceMock,
		)
		recordServiceMock.ExpectedCalls = nil
		var b *bool
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "2jTBBvQThUyiWsVxFZ3cpRIOVbJ", true, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{}, nil,
			)
		e, err := s.GetExamInsightsEligibility(
			ctx,
			"2jTBBvQThUyiWsVxFZ3cpRIOVbJ",
			"2jTBBvcPcWUvcsPriZZUbVgQnvm",
		)
		require.NoError(t, err)
		assert.Empty(t, e, "expected to get empty map, got map with length %d", len(e))
	})

	t.Run("account does not have access to patient", func(t *testing.T) {
		ctx := context.Background()

		s := NewUsersApiService(phdb,
			containerClient,
			nil,
			"qa.pocket.health",
			nil,
			languageproviders.LanguageTagProviders{},
			nil,
			&accountservice.AcctSvcMock{},
			orgServiceMock,
			nil,
			&sync.WaitGroup{},
			false, /* excludeSoftDeletedExams */
			nil,
			nil,
			providersservice.NewProviderServiceMock(),
			nil,
			&sos,
			nil,
			examServiceMock,
			nil,
			ri,
			roiServiceMock,
		)

		_, err := s.GetExamInsightsEligibility(
			ctx,
			"2jTBBvQThUyiWsVxFZ3cpRIOVbJ",
			"2jTBBvcPcWUvcsPriZZUbVgQnzm",
		)
		require.Error(t, err)
		assert.Equal(t, errmsg.ERR_NOT_FOUND, err.Error())
	})

	t.Run("has exams eligible for all programs", func(t *testing.T) {
		expected := make(map[string]*coreapi.ExamInsightsEligibility)
		expected["2jTk7weFjpzZ1uUnJfWFpgb6Ndd"] = &coreapi.ExamInsightsEligibility{Rho: true}
		expected["2jTlGQrMppxop25yGuj70r0dZN2"] = &coreapi.ExamInsightsEligibility{
			Report: true,
			Followups: []coreapi.FollowUp{
				{
					ReportId:    "BeLMzKDelsbY2kvwpev6C7YwebJKzmCyrL2rXEaome0=",
					HasFollowup: true,
					Occurrences: []reportinsights.FollowUpOccurrence{},
				},
			},
		}

		ctx := context.Background()
		s := NewUsersApiService(phdb,
			containerClient,
			nil,
			"qa.pocket.health",
			nil,
			languageproviders.LanguageTagProviders{},
			nil,
			&accountservice.AcctSvcMock{AccountHasAccessToPatientReturn: true},
			orgServiceMock,
			nil,
			&sync.WaitGroup{},
			false, /* excludeSoftDeletedExams */
			nil,
			nil,
			providersservice.NewProviderServiceMock(),
			nil,
			&sos,
			nil,
			examServiceMock,
			nil,
			ri,
			roiServiceMock,
		)

		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  // instanceUploadProgressPercent
		)
		study.AccountID = "2jTBBvQThUyiWsVxFZ3cpRIOVbJ"
		study.PatientID = "2jTX1SKTRQsrh0TWxhDOiTWGEvo"
		study.UUID = "2jTlGQrMppxop25yGuj70r0dZN2"
		study.Reports[0].UUID = "BeLMzKDelsbY2kvwpev6C7YwebJKzmCyrL2rXEaome0="
		study.Reports[1].UUID = "BeLMzKDelsbY2kvwpev6C7YwebJKzmCyrL2rXEaome0="
		recordServiceMock.ExpectedCalls = nil
		var b *bool
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "2jTBBvQThUyiWsVxFZ3cpRIOVbJ", true, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)
		eligibility, err := s.GetExamInsightsEligibility(
			ctx,
			"2jTBBvQThUyiWsVxFZ3cpRIOVbJ",
			"2jTX1SKTRQsrh0TWxhDOiTWGEvo",
		)
		require.NoError(t, err)
		assert.Equal(t, expected, eligibility)
	})

	t.Run("requests insights for all patients on account", func(t *testing.T) {
		expected := make(map[string]*coreapi.ExamInsightsEligibility)
		expected["2jTk7weFjpzZ1uUnJfWFpgb6Ndd"] = &coreapi.ExamInsightsEligibility{Rho: true}
		expected["2jTlGQrMppxop25yGuj70r0dZN2"] = &coreapi.ExamInsightsEligibility{
			Report: true,
			Followups: []coreapi.FollowUp{
				{
					ReportId:    "BeLMzKDelsbY2kvwpev6C7YwebJKzmCyrL2rXEaome0=",
					HasFollowup: true,
					Occurrences: []reportinsights.FollowUpOccurrence{},
				},
			},
		}

		ctx := context.Background()
		s := NewUsersApiService(phdb,
			containerClient,
			nil,
			"qa.pocket.health",
			nil,
			languageproviders.LanguageTagProviders{},
			nil,
			&accountservice.AcctSvcMock{AccountHasAccessToPatientReturn: true},
			orgServiceMock,
			nil,
			&sync.WaitGroup{},
			false, /* excludeSoftDeletedExams */
			nil,
			nil,
			providersservice.NewProviderServiceMock(),
			nil,
			&sos,
			nil,
			examServiceMock,
			nil,
			ri,
			roiServiceMock,
		)

		eligibility, err := s.GetExamInsightsEligibility(
			ctx,
			"2jTBBvQThUyiWsVxFZ3cpRIOVbJ",
			"",
		)
		require.NoError(t, err)
		assert.Equal(t, expected, eligibility)
	})
}

func TestGetReportInsights(t *testing.T) {
	phdb := testutils.SetupTestDB(t)

	var waitGroup *sync.WaitGroup
	waitGroup = &sync.WaitGroup{}
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}

	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}

	examServiceMock := examService.NewExamService(
		phdb,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		&accountservice.AcctSvcMock{},
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)
	reportInsightsClient := reportinsights.NewReportInsightsMock()
	service := NewUsersApiService(
		phdb,
		azureUtils.ContainerClient{},
		nil,
		"qa.pocket.health",
		nil,
		languageproviders.LanguageTagProviders{},
		mockSupportedLanguages,
		&accountservice.AcctSvcMock{},
		orgServiceMock,
		planServiceMock,
		waitGroup,
		false,
		experimentsClient,
		nil,
		providersServiceMock,
		nil,
		nil,
		nil,
		examServiceMock,
		&reports.MockReportsApiService{},
		reportInsightsClient,
		roiServiceMock,
	)
	t.Run("ensure organviz user notification gets created", func(t *testing.T) {
		acctID := integtestutils.GenerateRandomString(t, 10)
		exam := exams.CreateAndInsertTestExam(t, phdb, &acctID)
		examUUID := exam.UUID
		scansStore.InsertScan(t, phdb, exam.TransferId, "CD")
		reportId := integtestutils.GenerateRandomString(t, 10)
		mysqlobjects.InsertObject(t, phdb, reportId, true)
		mysqlobjects.InsertObjectMapping(t, phdb, examUUID, reportId, 1)
		_, err := phdb.Exec(
			"UPDATE exams SET has_report=1 where uuid=? limit 1",
			examUUID,
		)
		if err != nil {
			t.Fatalf("unable to setup exam %v", err)
		}
		ctx := context.Background()
		ret := reportinsights.InsightsResponse{}
		ret.Reports = map[string]reportinsights.ReportInsight{
			reportId: {
				Organviz: reportinsights.OrganViz{NumMasksGenerated: 3, Model: "ct_abd"},
			},
		}
		ret.OrganvizCount = reportinsights.TotalOrganViz{Xray: 0, Ct: 3}
		reportInsightsClient.SetupReturn("GetInsights", struct {
			Value reportinsights.InsightsResponse
			Err   error
		}{Value: ret, Err: nil})

		t.Cleanup(func() {
			phdb.Exec("delete from user_notifications where account_id = ?", acctID)
			mysqlobjects.DeleteObjectMapping(t, phdb, examUUID, reportId)
			mysqlobjects.DeleteObject(t, phdb, reportId)
			exams.DeleteExam(phdb, examUUID)
		})
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  // instanceUploadProgressPercent
		)
		study.UUID = examUUID
		recordServiceMock.ExpectedCalls = nil
		var b *bool
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, acctID, true, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)

		_, err = service.GetReportInsights(ctx, acctID, "")
		assert.NoError(t, err)
		var count int
		err = phdb.QueryRow(
			"SELECT count(*) FROM user_notifications WHERE name='IMAGE_READER' and account_id = ?",
			acctID,
		).Scan(&count)
		assert.NoError(t, err)
		assert.Equal(t, 1, count)
	})

	t.Run(
		"ensure no new organviz notification gets created if there's already one",
		func(t *testing.T) {
			acctID := integtestutils.GenerateRandomString(t, 10)
			exam := exams.CreateAndInsertTestExam(t, phdb, &acctID)
			examUUID := exam.UUID
			scansStore.InsertScan(t, phdb, exam.TransferId, "CD")
			reportId := integtestutils.GenerateRandomString(t, 10)
			mysqlobjects.InsertObject(t, phdb, reportId, true)
			mysqlobjects.InsertObjectMapping(t, phdb, examUUID, reportId, 1)
			_, err := phdb.Exec(
				"UPDATE exams SET has_report=1 where uuid=? limit 1",
				examUUID,
			)
			if err != nil {
				t.Fatalf("unable to setup exam %v", err)
			}
			notifID := integtestutils.GenerateRandomString(t, 10)
			data := coreapi.NotificationData{ExamUuid: examUUID}
			jsdata, err := json.Marshal(data)
			if err != nil {
				t.Fatalf("unable to create notification data %v", err)
			}
			_, err = phdb.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp, data) VALUES (?,?,?,?,?,?)",
				notifID,
				acctID,
				"IMAGE_READER",
				0,
				"2024-05-031T00:00:00Z",
				jsdata,
			)
			if err != nil {
				t.Fatalf("unable to create notification %v", err)
			}
			ctx := context.Background()
			ret := reportinsights.InsightsResponse{}
			ret.Reports = map[string]reportinsights.ReportInsight{
				reportId: {
					Organviz: reportinsights.OrganViz{NumMasksGenerated: 3, Model: "ct_abd"},
				},
			}
			ret.OrganvizCount = reportinsights.TotalOrganViz{Xray: 0, Ct: 3}
			reportInsightsClient.SetupReturn("GetInsights", struct {
				Value reportinsights.InsightsResponse
				Err   error
			}{Value: ret, Err: nil})

			t.Cleanup(func() {
				phdb.Exec("delete from user_notifications where account_id = ?", acctID)
				mysqlobjects.DeleteObjectMapping(t, phdb, examUUID, reportId)
				mysqlobjects.DeleteObject(t, phdb, reportId)
				exams.DeleteExam(phdb, examUUID)
			})

			study := recordservice.FormatPatientStudy(
				true, // activated
				recordservice.FULL_AVAILABILITY,
				true, // hasReport
				100,  // instanceUploadProgressPercent
			)
			study.UUID = examUUID
			recordServiceMock.ExpectedCalls = nil
			var b *bool
			recordServiceMock.EXPECT().
				GetStudies(mock.Anything, acctID, true, false, b, []string{}).
				Return(
					[]recordservice.PatientStudy{study}, nil,
				)

			_, err = service.GetReportInsights(ctx, acctID, "")
			assert.NoError(t, err)
			var count int
			err = phdb.QueryRow(
				"SELECT count(*) FROM user_notifications WHERE name='IMAGE_READER' and account_id = ?",
				acctID,
			).Scan(&count)
			assert.NoError(t, err)
			assert.Equal(t, 1, count)
		},
	)

	t.Run(
		"ensure no new organviz notification gets created if there's already one, even its read",
		func(t *testing.T) {
			acctID := integtestutils.GenerateRandomString(t, 10)
			exam := exams.CreateAndInsertTestExam(t, phdb, &acctID)
			examUUID := exam.UUID
			scansStore.InsertScan(t, phdb, exam.TransferId, "CD")
			reportId := integtestutils.GenerateRandomString(t, 10)
			mysqlobjects.InsertObject(t, phdb, reportId, true)
			mysqlobjects.InsertObjectMapping(t, phdb, examUUID, reportId, 1)
			_, err := phdb.Exec(
				"UPDATE exams SET has_report=1 where uuid=? limit 1",
				examUUID,
			)
			if err != nil {
				t.Fatalf("unable to setup exam %v", err)
			}
			notifID := integtestutils.GenerateRandomString(t, 10)
			data := coreapi.NotificationData{ExamUuid: examUUID}
			jsdata, err := json.Marshal(data)
			if err != nil {
				t.Fatalf("unable to create notification data %v", err)
			}
			_, err = phdb.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp, data) VALUES (?,?,?,?,?,?)",
				notifID,
				acctID,
				"IMAGE_READER",
				1,
				"2024-05-031T00:00:00Z",
				jsdata,
			)
			if err != nil {
				t.Fatalf("unable to create notification %v", err)
			}
			ctx := context.Background()
			ret := reportinsights.InsightsResponse{}
			ret.Reports = map[string]reportinsights.ReportInsight{
				reportId: {
					Organviz: reportinsights.OrganViz{NumMasksGenerated: 3, Model: "ct_abd"},
				},
			}
			ret.OrganvizCount = reportinsights.TotalOrganViz{Xray: 0, Ct: 3}
			reportInsightsClient.SetupReturn("GetInsights", struct {
				Value reportinsights.InsightsResponse
				Err   error
			}{Value: ret, Err: nil})

			t.Cleanup(func() {
				phdb.Exec("delete from user_notifications where account_id = ?", acctID)
				mysqlobjects.DeleteObjectMapping(t, phdb, examUUID, reportId)
				mysqlobjects.DeleteObject(t, phdb, reportId)
				exams.DeleteExam(phdb, examUUID)
			})

			study := recordservice.FormatPatientStudy(
				true, // activated
				recordservice.FULL_AVAILABILITY,
				true, // hasReport
				100,  // instanceUploadProgressPercent
			)
			study.UUID = examUUID
			recordServiceMock.ExpectedCalls = nil
			var b *bool
			recordServiceMock.EXPECT().
				GetStudies(mock.Anything, acctID, true, false, b, []string{}).
				Return(
					[]recordservice.PatientStudy{study}, nil,
				)

			_, err = service.GetReportInsights(ctx, acctID, "")
			assert.NoError(t, err)
			var count int
			err = phdb.QueryRow(
				"SELECT count(*) FROM user_notifications WHERE name='IMAGE_READER' and account_id = ?",
				acctID,
			).Scan(&count)
			assert.NoError(t, err)
			assert.Equal(t, 1, count)
		},
	)

	t.Run(
		"create new organviz notification gets created if the old one is read and has different examuuid",
		func(t *testing.T) {
			acctID := integtestutils.GenerateRandomString(t, 10)
			oldExamUuid := integtestutils.GenerateRandomString(t, 10)
			exam := exams.CreateAndInsertTestExam(t, phdb, &acctID)
			examUUID := exam.UUID
			scansStore.InsertScan(t, phdb, exam.TransferId, "CD")
			reportId := integtestutils.GenerateRandomString(t, 10)
			mysqlobjects.InsertObject(t, phdb, reportId, true)
			mysqlobjects.InsertObjectMapping(t, phdb, examUUID, reportId, 1)
			_, err := phdb.Exec(
				"UPDATE exams SET has_report=1 where uuid=? limit 1",
				examUUID,
			)
			if err != nil {
				t.Fatalf("unable to setup exam %v", err)
			}
			notifID := integtestutils.GenerateRandomString(t, 10)
			data := coreapi.NotificationData{ExamUuid: oldExamUuid}
			jsdata, err := json.Marshal(data)
			if err != nil {
				t.Fatalf("unable to create notification data %v", err)
			}
			_, err = phdb.Exec(
				"INSERT INTO user_notifications (id, account_id, name, is_read, created_timestamp, data) VALUES (?,?,?,?,?,?)",
				notifID,
				acctID,
				"IMAGE_READER",
				1,
				"2024-05-031T00:00:00Z",
				jsdata,
			)
			if err != nil {
				t.Fatalf("unable to create notification %v", err)
			}
			ctx := context.Background()
			ret := reportinsights.InsightsResponse{}
			ret.Reports = map[string]reportinsights.ReportInsight{
				reportId: {
					Organviz: reportinsights.OrganViz{NumMasksGenerated: 3, Model: "ct_abd"},
				},
			}
			ret.OrganvizCount = reportinsights.TotalOrganViz{Xray: 0, Ct: 3}
			reportInsightsClient.SetupReturn("GetInsights", struct {
				Value reportinsights.InsightsResponse
				Err   error
			}{Value: ret, Err: nil})

			t.Cleanup(func() {
				phdb.Exec("delete from user_notifications where account_id = ?", acctID)
				mysqlobjects.DeleteObjectMapping(t, phdb, examUUID, reportId)
				mysqlobjects.DeleteObject(t, phdb, reportId)
				exams.DeleteExam(phdb, examUUID)
			})

			study := recordservice.FormatPatientStudy(
				true, // activated
				recordservice.FULL_AVAILABILITY,
				true, // hasReport
				100,  // instanceUploadProgressPercent
			)
			study.UUID = examUUID
			recordServiceMock.ExpectedCalls = nil
			var b *bool
			recordServiceMock.EXPECT().
				GetStudies(mock.Anything, acctID, true, false, b, []string{}).
				Return(
					[]recordservice.PatientStudy{study}, nil,
				)

			_, err = service.GetReportInsights(ctx, acctID, "")
			assert.NoError(t, err)
			var count int
			err = phdb.QueryRow(
				"SELECT count(*) FROM user_notifications WHERE name='IMAGE_READER' and account_id = ?",
				acctID,
			).Scan(&count)
			assert.NoError(t, err)
			assert.Equal(t, 2, count)
		},
	)

	t.Run("return early if no report in the account", func(t *testing.T) {
		// reset the counter first
		reportInsightsClient.Counters["GetInsights"] = 0

		acctID := integtestutils.GenerateRandomString(t, 10)
		exam := exams.CreateAndInsertTestExam(t, phdb, &acctID)
		examUUID := exam.UUID
		scansStore.InsertScan(t, phdb, exam.TransferId, "CD")
		ctx := context.Background()

		t.Cleanup(func() {
			exams.DeleteExam(phdb, examUUID)
		})
		recordServiceMock.ExpectedCalls = nil
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			false, // hasReport
			100,   // instanceUploadProgressPercent
		)
		study.UUID = examUUID
		recordServiceMock.ExpectedCalls = nil
		var b *bool
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, acctID, true, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)
		res, err := service.GetReportInsights(ctx, acctID, "")
		assert.NoError(t, err)
		assert.Equal(t, reportinsights.InsightsResponse{}, res)
		assert.Equal(t, reportInsightsClient.Counters["GetInsights"], 0)
	})
}

func createExamWithObjects(
	t *testing.T,
	db *sql.DB,
	acctID string,
	exam coreapi.ExamRawBasic,
	shareID string,
	objectID string,
) {
	// insert share objects
	shareobjects.InsertShareObject(t, db, shareID, objectID, false)
	_, err := db.ExecContext(
		context.Background(),
		`INSERT INTO scans (	
					scan_id
					) VALUES (?)`,
		exam.TransferId,
	)
	require.NoError(t, err)

	// insert objects
	_, err = db.ExecContext(
		context.Background(),
		`INSERT INTO objects (	
					object_id,
					scan_id,
					is_report
					) VALUES (?, ?, ?)`,
		objectID,
		exam.TransferId,
		0,
	)
	require.NoError(t, err)

	// insert view metadata
	_, err = db.ExecContext(
		context.Background(),
		`INSERT INTO view_metadata (	
					object_id,
					sop_class_uid
					) VALUES (?, ?)`,
		objectID,
		"1.2.3",
	)
	require.NoError(t, err)

	// insert object mappings
	_, err = db.ExecContext(
		context.Background(),
		`INSERT INTO object_mappings (	
					exam_uuid,
					object_id
					) VALUES (?, ?)`,
		exam.UUID,
		objectID,
	)
	require.NoError(t, err)

	t.Cleanup(func() {
		shareobjects.DeleteShareObject(t, db, shareID, objectID)
		mysqlobjects.DeleteObjectMapping(t, db, exam.UUID, objectID)
		_, err = db.Exec(
			`DELETE FROM scans WHERE scan_id=?`,
			exam.TransferId,
		)
		assert.NoError(t, err)
		_, err = db.Exec(
			`DELETE FROM view_metadata WHERE object_id=?`,
			objectID,
		)
		assert.NoError(t, err)
		_, err = db.Exec(
			`DELETE FROM objects WHERE object_id=?`,
			objectID,
		)
		assert.NoError(t, err)
	})
}
