/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package users

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// A UsersApiController binds http requests to an api service and writes the service results to the http response
type PrivateUsersApiController struct {
	service             coreapi.UsersApiServicer
	refreshCookieDomain string
}

// NewUsersApiController creates a default api controller
func NewPrivateUsersApiController(
	s coreapi.UsersApiServicer,
	frontendHost string,
) coreapi.PrivateUsersApiRouter {
	return &PrivateUsersApiController{service: s, refreshCookieDomain: frontendHost}
}

// Routes returns all of the api route for the UsersApiController
func (c *PrivateUsersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetUserExamsSize",
			Method:      http.MethodGet,
			Pattern:     "/exams/size",
			HandlerFunc: c.GetUserExamsSize,
		},
		{
			Name:        "GetExamThumbnailByUuid",
			Method:      http.MethodGet,
			Pattern:     "/exams/{examuuid}/thumbnail",
			HandlerFunc: c.GetExamThumbnailByUuid,
		},
		{
			Name:        "GetUserExamInsightEligibility",
			Method:      http.MethodGet,
			Pattern:     "/exams/eligible-insights",
			HandlerFunc: c.GetExamInsightsEligibility,
		},
		{
			Name:        "GetUserExamById",
			Method:      http.MethodGet,
			Pattern:     "/exams/{examId}",
			HandlerFunc: c.GetUserExamById,
		},
		{
			Name:        "GetUserRequestConsent",
			Method:      http.MethodGet,
			Pattern:     "/consents",
			HandlerFunc: c.GetUserRequestConsent,
		},
		{
			Name:        "GetNotifications",
			Method:      http.MethodGet,
			Pattern:     "/notifications",
			HandlerFunc: c.GetNotifications,
		},
		{
			Name:        "PostNotification",
			Method:      http.MethodPost,
			Pattern:     "/notifications",
			HandlerFunc: c.PostNotification,
		},
		{
			Name:        "MarkNotificationRead",
			Method:      http.MethodPut,
			Pattern:     "/notifications/{notificationId}",
			HandlerFunc: c.MarkNotificationRead,
		},
		{
			Name:        "DeleteNotification",
			Method:      http.MethodDelete,
			Pattern:     "/notifications/{notificationId}",
			HandlerFunc: c.DeleteNotification,
		},
		{
			Name:        "DeleteUsersLogout",
			Method:      http.MethodDelete,
			Pattern:     "/logout",
			HandlerFunc: c.DeleteUsersLogout,
		},
		{
			Name:        "FriendReferral",
			Method:      http.MethodPost,
			Pattern:     "/referral",
			HandlerFunc: c.PostFriendReferral,
		},
		{
			Name:        "GetUserSettings",
			Method:      http.MethodGet,
			Pattern:     "/settings",
			HandlerFunc: c.GetUserSettings,
		},
		{
			Name:        "UpdateUserSettings",
			Method:      http.MethodPut,
			Pattern:     "/settings",
			HandlerFunc: c.UpdateUserSettings,
		},
		{
			Name:        "TriggerOrganVisualizationInference",
			Method:      http.MethodPost,
			Pattern:     "/organviz",
			HandlerFunc: c.TriggerOrganvizInference,
		},
		{
			Name:        "GetReportInsights",
			Method:      http.MethodGet,
			Pattern:     "/reportinsights",
			HandlerFunc: c.GetReportInsights,
		},
	}
}

func (c *PrivateUsersApiController) GetPathPrefix() string {
	return "/v1/users"
}

func (c *PrivateUsersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func getAccountFromToken(r *http.Request) (string, error) {
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	return acctId, err
}

func (c *PrivateUsersApiController) GetExamThumbnailByUuid(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	examUuid := params["examuuid"]
	token := r.Header.Get("Authorization")
	acctID, errAcc := auth.DecodeAccountToken(token)
	var errTransfer error
	if errAcc != nil {
		_, acctID, errTransfer = auth.DecodeChallengeUnlockToken(token)
	}
	shareID, _, errSV := auth.DecodeShareViewerToken(token)

	if errAcc != nil && errSV != nil && errTransfer != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, size, err := c.service.GetExamThumbnailByUuid(r.Context(), examUuid, acctID, shareID)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "image/png")
	w.Header().Set("Content-Length", fmt.Sprint(size))

	maxAge := 365 * 24 * 60 * 60
	w.Header().Set("Cache-Control", "private, max-age="+strconv.Itoa(maxAge))
	w.Header().Set("Expires", time.Now().Add(365*24*time.Hour).Format(http.TimeFormat))
	w.Header().Del("Pragma")

	_, err = io.Copy(w, result)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).WithError(err).Error("failed to write response bytes")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

// GetUserExamById - Get exam
func (c *PrivateUsersApiController) GetUserExamById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	examId := params["examId"]
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	result, err := c.service.GetUserExamById(r.Context(), acctId, examId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
			return
		}
		logutils.CtxLogger(r.Context()).WithError(err).Error("error getting exam")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetUserExamsSize - Get exams size
func (c *PrivateUsersApiController) GetUserExamsSize(w http.ResponseWriter, r *http.Request) {

	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	result, err := c.service.GetUserExamsSize(r.Context(), acctId)

	if err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetUserRequestConsent - Get consent pdf(s)
func (c *PrivateUsersApiController) GetUserRequestConsent(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()
	requestIdStr := query.Get("requestID")
	providerIdStr := query.Get("providerID")
	transferId := query.Get("transferID")

	var requestId int64
	var providerId uint
	if requestIdStr != "" {
		requestId, _ = coreapi.ParseIntParameter(requestIdStr)
	}
	if providerIdStr != "" {
		providerId, _ = coreapi.ParseUIntParameter(providerIdStr)
	}

	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	if requestId == 0 && providerId == 0 && transferId == "" {
		httperror.ErrorWithLog(w, r, "must provide 1 query parameter", http.StatusBadRequest)
		return
	}

	result, err := c.service.GetUserRequestConsent(
		r.Context(),
		acctId,
		requestId,
		providerId,
		transferId,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	buffer := bytes.NewBuffer(result)
	w.Header().
		Set("Content-Disposition", "attachment; filename=Request_"+strconv.FormatInt(requestId, 10)+".pdf")
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Length", strconv.Itoa(len(buffer.Bytes())))
	if _, err := w.Write(buffer.Bytes()); err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

// GetNotifications - Get notifications of the user
func (c *PrivateUsersApiController) GetNotifications(w http.ResponseWriter, r *http.Request) {

	//include_read is an optional query parameter, default is false.
	exists, includeRead, err := coreapi.ParseQueryParamBool(r, "include_read")
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_BAD_QUERY_PARAM+": include_read",
			http.StatusBadRequest,
		)
		return
	}
	if !exists {
		includeRead = false
	}

	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	result, err := c.service.GetNotificationsByAccount(r.Context(), acctId, includeRead)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// Create a notification for the authed user
func (c *PrivateUsersApiController) PostNotification(w http.ResponseWriter, r *http.Request) {
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	var body struct {
		NotificationType coreapi.NotificationType `json:"notification_type"`
	}
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	result, err := c.service.CreateUserNotification(r.Context(), acctId, body.NotificationType)
	if err != nil && err.Error() == errormsgs.ERR_CONFLICT {
		// If a notification of this type has already been created for a user then just swallow the conflict error
		// and present a 200 as we don't need to do anything in this case.
		w.WriteHeader(http.StatusOK)
		return
	} else if err != nil {
		// Can't use ErrorWithLog here as that only logs the returned error and doesn't map the underlying error to a status
		// So log manually, then parse the error message into a status and generic message.
		logutils.Logger(r.Context()).
			WithFields(logrus.Fields{"account_id": acctId, "request_body": body}).
			WithError(err).Error("Could not create notification")

		status := httperror.ErrormsgToStatus(err.Error())
		http.Error(w, http.StatusText(status), status)
		return
	}

	status := http.StatusCreated
	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}

// Delete a user notification
func (c *PrivateUsersApiController) DeleteNotification(w http.ResponseWriter, r *http.Request) {
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	notifId := params["notificationId"]
	err = c.service.DeleteUserNotificationById(r.Context(), acctId, notifId)
	if err != nil {
		status := httperror.ErrormsgToStatus(err.Error())
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// MarkNotificationRead - Mark user notification as read
func (c *PrivateUsersApiController) MarkNotificationRead(w http.ResponseWriter, r *http.Request) {
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	id := params["notificationId"]
	err = c.service.UpdateNotificationAsReadById(r.Context(), acctId, id)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusNotFound)
			return
		}

		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// DeleteUsersLogout - Logout
func (c *PrivateUsersApiController) DeleteUsersLogout(w http.ResponseWriter, r *http.Request) {

	token := r.Header.Get("Authorization")
	_, err := c.service.DeleteUsersLogout(r.Context(), token)
	if err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	// remove refresh token cookie
	http.SetCookie(
		w,
		&http.Cookie{
			Name:   "refresh_token",
			MaxAge: -1,
			Path:   "/",
			Domain: c.refreshCookieDomain,
		},
	)

	coreapi.EncodeJSONResponse(r.Context(), nil, nil, w)
}

func (c *PrivateUsersApiController) PostFriendReferral(w http.ResponseWriter, r *http.Request) {

	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	// Request Body: {email: string}
	var body coreapi.Referral
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err = c.service.PostFriendReferral(r.Context(), acctId, body.Email)
	if err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	w.WriteHeader(http.StatusOK)
}

func (c *PrivateUsersApiController) GetUserSettings(w http.ResponseWriter, r *http.Request) {
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	result, err := c.service.GetUserSettings(r.Context(), acctId)
	if err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateUsersApiController) UpdateUserSettings(w http.ResponseWriter, r *http.Request) {
	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	var body accountservice.UserSettingsRequest
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.UpdateUserSettings(r.Context(), acctId, body)
	if err != nil {
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	w.WriteHeader(http.StatusOK)
}

func (c *PrivateUsersApiController) TriggerOrganvizInference(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.CtxLogger(r.Context())

	acctId, err := getAccountFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	lg = lg.WithField("account_id", acctId)

	q, err := queries.NewFromRequest(r)
	if err != nil {
		lg.WithError(err).Error("Failed creating query from request")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	err = c.service.TriggerOrganVisualizationInference(r.Context(), acctId, q)
	if err != nil {
		lg.WithError(err).Error("Unable to trigger organviz inference")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (c *PrivateUsersApiController) GetExamInsightsEligibility(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)
	patientId := r.URL.Query().Get("patient_id")
	// TODO [PE-450]: Remove this once we no longer call it from the FE
	// Check the query params
	if patientId == "" {
		patientId = r.URL.Query().Get("patientId")
	}

	res, err := c.service.GetExamInsightsEligibility(r.Context(), accountId, patientId)
	if err == nil {
		coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
		return
	}

	if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
	} else if err.Error() == errormsgs.ERR_NOT_FOUND {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_FOUND, http.StatusNotFound)
	} else {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *PrivateUsersApiController) GetReportInsights(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)
	patientId := r.URL.Query().Get("patient_id")
	if patientId == "" {
		patientId = r.URL.Query().Get("patientId")
	}

	res, err := c.service.GetReportInsights(r.Context(), accountId, patientId)
	if err == nil {
		coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
		return
	}

	if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
	} else if err.Error() == errormsgs.ERR_NOT_FOUND {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_FOUND, http.StatusNotFound)
	} else {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}
