package users

import (
	"context"
	"errors"
	"io"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// MockUsersApiService is a service that implents the logic for the UsersApiServicer for testing
type MockUsersApiService struct {
	MockLogout                       func(string) (interface{}, error)
	MockGetNotificationsByAccount    func(id string) (coreapi.UserNotifications, error)
	MockUpdateNotificationAsReadById func(id string) error
	MockCreateUserNotification       func(accountId string, notificationType coreapi.NotificationType) (coreapi.Notification, error)
	MockGetUserExams                 func(id string, q queries.Queries) ([]coreapi.Exam, error)
	MockGetConsent                   func(id string, rID int64, id2 uint, s2ID string) ([]byte, error)
	MockGetExamThumbnailByUuid       func(id string, acctID string, shareID string) (io.ReadCloser, int64, error)
	MockPost                         func(id uint, body map[string]interface{}) (interface{}, error)
	MockAuth                         func(s1 string, s2 string) error
	MockLogin                        func(s1 string, s2 string) (interface{}, string, error)
	MockEvalPassWrd                  func(s1 string, s2 string) error

	GetExamByIdRet interface{}
	GetExamByIdErr error
}

// NewMockUsersApiService creates a default api service
func NewMockUsersApiService(
	mockGetExams func(id string, q queries.Queries) ([]coreapi.Exam, error),
) coreapi.UsersApiServicer {
	return &MockUsersApiService{MockGetUserExams: mockGetExams}
}

// DeleteUsersLogout - Logout
func (s *MockUsersApiService) DeleteUsersLogout(
	ctx context.Context,
	token string,
) (interface{}, error) {
	return s.MockLogout(token)
}

// GetUserExamById - Get exam
func (s *MockUsersApiService) GetUserExamById(
	ctx context.Context,
	acctId string,
	examId string,
) (interface{}, error) {
	return s.GetExamByIdRet, s.GetExamByIdErr
}

// GetExamThumbnailByUuid - Get exam thumbnail
func (s *MockUsersApiService) GetExamThumbnailByUuid(
	ctx context.Context,
	examUuid string,
	acctId string,
	shareId string,
) (io.ReadCloser, int64, error) {
	return s.MockGetExamThumbnailByUuid(examUuid, acctId, shareId)
}

// GetUserExams - Get exams, optionally filter by activated if not null
func (s *MockUsersApiService) GetUserExams(
	ctx context.Context,
	acctId string,
	includeReports bool,
	q queries.Queries,
) ([]coreapi.Exam, error) {
	return s.MockGetUserExams(acctId, q)
}

func (s *MockUsersApiService) GetUserExamsSize(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	return nil, errors.New("not implemented")
}

// GetUserRequestConsent - Get consent pdf(s)
func (s *MockUsersApiService) GetUserRequestConsent(
	ctx context.Context,
	acctId string,
	requestID int64,
	providerID uint,
	transferID string,
) ([]byte, error) {
	return s.MockGetConsent(acctId, requestID, providerID, transferID)
}

// GetNotificationsByAccount - Get user notification
func (s *MockUsersApiService) GetNotificationsByAccount(
	ctx context.Context,
	acctId string,
	includeRead bool,
) (coreapi.UserNotifications, error) {
	return s.MockGetNotificationsByAccount(acctId)
}

func (s *MockUsersApiService) CreateUserNotification(
	ctx context.Context,
	accountId string,
	notifcationType coreapi.NotificationType,
) (coreapi.Notification, error) {
	return s.MockCreateUserNotification(accountId, notifcationType)
}

func (s *MockUsersApiService) DeleteUserNotificationById(
	ctx context.Context,
	accountId, notificationId string,
) error {
	return nil
}

func (s *MockUsersApiService) UpdateNotificationAsReadById(
	ctx context.Context,
	accountId, notificationId string,
) error {
	return s.MockUpdateNotificationAsReadById(notificationId)
}

// PostUsers - Create new user
func (s *MockUsersApiService) PostUsers(
	ctx context.Context,
	rd coreapi.RegisterData,
	transferId, ip, source string,
) (string, error) {
	return "", s.MockAuth(rd.Email, rd.Password)
}

// PostUsersLogin - Login
func (s *MockUsersApiService) PostUsersLoginSSO(
	ctx context.Context,
	token string,
	ip string,
) (accountservice.AccountSSOLoginResponse, error) {
	return accountservice.AccountSSOLoginResponse{}, errors.New("not implemented")
}

func (s *MockUsersApiService) PostUsersLoginGoogle(
	ctx context.Context,
	token string,
	ip string,
) (accountservice.GoogleSSOLoginResponse, error) {
	return accountservice.GoogleSSOLoginResponse{}, errors.New("not implemented")
}

// FriendReferral
func (s *MockUsersApiService) PostFriendReferral(
	ctx context.Context,
	acctId string,
	emailAddr string,
) error {
	return nil
}

// GetUserSettings
func (s *MockUsersApiService) GetUserSettings(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	return false, nil
}

// UpdateUserSettings
func (s *MockUsersApiService) UpdateUserSettings(
	ctx context.Context,
	acctId string,
	settings accountservice.UserSettingsRequest,
) error {
	return nil
}

func (s *MockUsersApiService) PostLoginViaSSO(
	ctx context.Context,
	token, accountId, ip string,
) (accountservice.AccountSSOLoginResponse, error) {
	return accountservice.AccountSSOLoginResponse{}, errors.New("not implemented")
}

func (s *MockUsersApiService) TriggerOrganVisualizationInference(
	ctx context.Context,
	acctId string,
	q queries.Queries,
) error {
	return nil
}

func (s *MockUsersApiService) GetExamInsightsEligibility(
	context.Context,
	string,
	string,
) (map[string]*coreapi.ExamInsightsEligibility, error) {
	return nil, nil
}

func (s *MockUsersApiService) GetReportInsights(
	context.Context,
	string,
	string,
) (reportinsights.InsightsResponse, error) {
	return reportinsights.InsightsResponse{}, nil
}

func (s *MockUsersApiService) GetOrganVisualizationByExamId(ctx context.Context, examId string, acctId string, interactive bool) ([]byte, error) {
	return []byte{}, nil
}
