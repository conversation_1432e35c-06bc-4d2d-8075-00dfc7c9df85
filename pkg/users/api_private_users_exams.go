package users

import (
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
	"github.com/samber/lo"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

type privateUsersExamsApiController struct {
	service coreapi.UsersApiServicer
}

// This controller is specifically for the GET /users/exams endpoint that supports dual
// authentication with either a JWT auth token or X-PH-Signature header. It uses different
// middleware than the rest of the /users endpoints.
//
// Important: This controller needs to be registered with the router before any other /users
// controller (there are two: NewPrivateUsersApiController() and NewPublicUsersApiController()).
// This constraint exists because this controller uses a path prefix that would otherwise be
// captured by those controllers. Registering this controller first ensures that the router knows
// this route is special.
//
// If ever additional /users routes need dual authentication, this controller's GetPathPrefix()
// should change to /users (thereby removing the above constraint) and of course the GetUserExams()
// path should be specified (/exams).
func NewPrivateUsersExamsApiController(s coreapi.UsersApiServicer) coreapi.PrivateUsersExamsApiRouter {
	return &privateUsersExamsApiController{service: s}
}

func (c *privateUsersExamsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetUserExams",
			Method:      http.MethodGet,
			HandlerFunc: c.GetUserExams,
		},
		{
			Name:        "GetOrganVisualizationByExamId",
			Method:      http.MethodGet,
			Pattern:     "/{examId}/organviz",
			HandlerFunc: c.GetOrganVisualizationByExamId,
		},
		{
			Name:        "GetInteractiveOrganVisualizationByExamId",
			Method:      http.MethodGet,
			Pattern:     "/{examId}/organviz/interactive",
			HandlerFunc: c.GetInteractiveOrganVisualizationByExamId,
		},
	}
}

func (c *privateUsersExamsApiController) GetPathPrefix() string {
	return "/v1/users/exams"
}

func (c *privateUsersExamsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	// Note different auth than other /users routes
	return []func(next http.Handler) http.Handler{auth.ValidateDualAuth}
}

func (c *privateUsersExamsApiController) GetUserExams(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Since mux isn't validating requests against the openapi spec, we aren't guaranteed to get
	// the (defaulted) `activated` query parameter. So..... add it back?
	// This is gross and git blame will forevermore point its finger of chagrin at me for this
	if ok := r.URL.Query().Has("activated"); !ok {
		query := r.URL.Query()
		query.Add("activated", "true")
		r.URL.RawQuery = query.Encode()
	}
	// For now handle this outside of `queries` package... it needs better support for params that
	// are not filterable
	var includeReports bool
	if ok := r.URL.Query().Has("include_reports"); ok {
		var err error
		includeReports, err = strconv.ParseBool(r.URL.Query().Get("include_reports"))
		if err != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_QUERY_PARAM+": "+err.Error(), http.StatusBadRequest)
			return
		}
		query := r.URL.Query()
		// Delete this query parameter so `queries` doesn't get cranky
		query.Del("include_reports")
		r.URL.RawQuery = query.Encode()
	}
	q, err := queries.NewFromRequest(
		r,
		queries.WithQueries(
			queries.Query{
				Name:       "uid",
				ColType:    queries.String,
				ColName:    lo.ToPtr("exam_uid"),
				Filterable: true,
				Listable:   true,
			},
			queries.Query{Name: "activated", ColType: queries.Bool, Filterable: true},
			queries.Query{
				Name:       "account_id",
				ColName:    lo.ToPtr("e.account_id"),
				ColType:    queries.String,
				Filterable: true,
			},
		),
	)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_BAD_QUERY_PARAM+": "+err.Error(),
			http.StatusBadRequest,
		)
		return
	}

	ctx = q.WithFields(ctx)

	var acctId string
	if auth.IsValidatedWithBearerToken(ctx) {
		acctId, err = getAccountFromToken(r)
		if err != nil {
			logutils.Errorx(ctx, "failed to retrieve account ID from token", err)
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}
	} // else request is signed

	// Exams can be searched by:
	// - account ID (bearer token has a valid account ID and/or query parameter)
	// - UUID (bearer token is a share token, in which case the UUIDs come from a DB query)
	// - UID (query parameter currently used only by provider-outreach)
	//
	// We have to protect against abuse of the query parameters by users. Users with an account
	// token are not a concern (their search is constrained by the account ID) but care must be
	// taken to ensure that share tokens cannot be used to conduct unconstrained searches. Only
	// other PocketHealth services are permitted to conduct such searches. Unlike user-initiated
	// searches, searches conducted by other services are signed
	if acctId == "" && !auth.IsValidatedWithSignedRequest(ctx) &&
		(exams.IsUidSearch(ctx) || exams.IsAccountIDSearch(ctx)) {
		logutils.Errorx(ctx, "query not permitted", err)
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	// Some of the helpers in UsersApiService.GetUserExams() require a usable account ID, so let's
	// do what we can to populate one
	if acctId == "" && exams.IsAccountIDSearch(ctx) {
		acctIDs := logutils.Fields(ctx)["account_id"].([]string)
		if len(acctIDs) == 1 {
			acctId = acctIDs[0]
		}
	}

	result, err := c.service.GetUserExams(ctx, acctId, includeReports, q)
	if err != nil {
		logutils.Errorx(ctx, "failed on GetUserExams()", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *privateUsersExamsApiController) GetOrganVisualizationByExamId(
	w http.ResponseWriter,
	r *http.Request,
) {
	c.getOrganVisualizationByExamId(w, r, false)
}

func (c *privateUsersExamsApiController) GetInteractiveOrganVisualizationByExamId(
	w http.ResponseWriter,
	r *http.Request,
) {
	c.getOrganVisualizationByExamId(w, r, true)
}

func (c *privateUsersExamsApiController) getOrganVisualizationByExamId(
	w http.ResponseWriter,
	r *http.Request,
	interactive bool,
) {
	params := mux.Vars(r)
	examId := params["examId"]

	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	results, err := c.service.GetOrganVisualizationByExamId(r.Context(), examId, acctId, interactive)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), httperror.ErrormsgToStatus(err.Error()))
		return
	}

	if results == nil && err == nil {
		w.WriteHeader(http.StatusNoContent)
		return
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(results)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}
