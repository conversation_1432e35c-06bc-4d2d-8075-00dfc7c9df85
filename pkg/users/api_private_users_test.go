package users

import (
	"bytes"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

// when you test locally this ends up being what clientAddr[0] looks like
const (
	localClaimIp = ""
)

func TestGetUserExams(t *testing.T) {
	privateKey := testutils.SetupXPhSignaturePrivKey(t)

	service := mockcoreapi.NewMockUsersApiServicer(t)
	service.EXPECT().
		GetUserExams(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return([]coreapi.Exam{}, nil)

	usersController := NewPrivateUsersApiController(service, "")
	usersExamsController := NewPrivateUsersExamsApiController(service)
	router, err := coreapi.NewRouter(usersExamsController, usersController)
	if err != nil {
		t.Fatal(err)
	}
	// happy case - locked exams
	t.Run("good locked exams", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams?activated=false", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	// happy case - unlocked exams
	t.Run("good locked exams", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	// bad activated value
	t.Run("invalid activated param", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams?activated=garbage", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("unauthorized get size", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams/size", nil)
		if err != nil {
			t.Fatal(err)
		}
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("unauthorized get exams", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams", nil)
		if err != nil {
			t.Fatal(err)
		}
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("authorized get exams, invalid activated param ", func(t *testing.T) {
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/users/exams?activated=fals", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("unauthorized get exam by id", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/users/exams/2.16.124.113615.911631.1252259.********.************",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
	t.Run("get exam by id not found", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			GetUserExamById(mock.Anything, mock.Anything, mock.Anything).
			Return(nil, errors.New(errmsg.ERR_NOT_FOUND))

		controller := NewPrivateUsersApiController(service, "")
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/users/exams/abc123", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusNotFound
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("get exam by id error", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			GetUserExamById(mock.Anything, mock.Anything, mock.Anything).
			Return(nil, errors.New("something has gone terribly wrong"))

		controller := NewPrivateUsersApiController(service, "")
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/users/exams/abc123", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusInternalServerError
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("get exam by id good", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			GetUserExamById(mock.Anything, mock.Anything, mock.Anything).
			Return(coreapi.Exam{UUID: "abc123"}, nil)

		controller := NewPrivateUsersApiController(service, "")
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/users/exams/abc123", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("query exam thumbnail by uuid", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		retdata := []byte{}
		service.EXPECT().
			GetExamThumbnailByUuid(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(io.NopCloser(bytes.NewBuffer(retdata)), int64(len(retdata)), nil)

		controller := NewPrivateUsersApiController(service, "")
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)
		req, err := http.NewRequest(
			"GET",
			"/v1/users/exams/abc123/thumbnail",
			nil,
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("query exams by id", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			GetUserExams(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return([]coreapi.Exam{{ExamId: "2.16.124.113615.911631.1252259.********.************"}}, nil)

		controller := NewPrivateUsersExamsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)
		req, err := http.NewRequest(
			"GET",
			"/v1/users/exams?uid=2.16.124.113615.911631.1252259.********.************",
			nil,
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("query exams by id using X-PH-Signature", func(t *testing.T) {
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			GetUserExams(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return([]coreapi.Exam{{ExamId: "2.16.124.113615.911631.1252259.********.************"}}, nil)

		controller := NewPrivateUsersExamsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)
		req, err := http.NewRequest(
			"GET",
			"/v1/users/exams?uid=2.16.124.113615.911631.1252259.********.************",
			nil,
		)
		require.NoError(t, err)

		testutils.SignRequest(t, req, privateKey)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("share token unauthorize", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/users/exams", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestPostNotification(t *testing.T) {
	service := mockcoreapi.NewMockUsersApiServicer(t)
	controller := NewPrivateUsersApiController(service, "")
	router, err := coreapi.NewRouter(controller)
	require.NoError(t, err)

	t.Run("when the request is valid", func(t *testing.T) {
		acctId := ksuid.New().String()
		service.EXPECT().
			CreateUserNotification(mock.Anything, acctId, coreapi.NotificationObsp).
			Return(coreapi.Notification{
				Type: coreapi.NotificationObsp,
				Id:   ksuid.New().String(),
			}, nil)

		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/users/notifications",
			strings.NewReader(`{"notification_type": "OBSP"}`),
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken(acctId, localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusCreated, rr.Code)
	})

	t.Run("when the request is valid, and a conflict error is raised", func(t *testing.T) {
		acctId := ksuid.New().String()
		service.EXPECT().
			CreateUserNotification(mock.Anything, acctId, coreapi.NotificationObsp).
			Return(coreapi.Notification{}, errors.New(errormsgs.ERR_CONFLICT))

		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/users/notifications",
			strings.NewReader(`{"notification_type": "OBSP"}`),
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken(acctId, localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("when the request is invalid, and a bad request error is raised", func(t *testing.T) {
		acctId := ksuid.New().String()
		service.EXPECT().
			CreateUserNotification(mock.Anything, acctId, coreapi.NotificationType("CHEESE")).
			Return(coreapi.Notification{}, errors.New(errormsgs.ERR_INVALID_REQ_BODY))

		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/users/notifications",
			strings.NewReader(`{"notification_type": "CHEESE"}`),
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken(acctId, localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusBadRequest, rr.Code)
	})

	t.Run("when the request is invalid, and a bad request error is raised", func(t *testing.T) {
		acctId := ksuid.New().String()
		service.EXPECT().
			CreateUserNotification(mock.Anything, mock.Anything, mock.Anything).
			Return(coreapi.Notification{}, errors.New("something has gone very wrong"))

		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/users/notifications",
			strings.NewReader(`{"notification_type": "OBSP"}`),
		)
		require.NoError(t, err)
		token := auth.MakeAccountAuthToken(acctId, localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusInternalServerError, rr.Code)
	})
}
