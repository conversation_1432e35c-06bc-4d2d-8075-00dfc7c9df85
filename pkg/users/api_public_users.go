/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package users

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A UsersApiController binds http requests to an api service and writes the service results to the http response
type PublicUsersApiController struct {
	service              coreapi.UsersApiServicer
	lt                   lockouttracker.LockoutTracker
	i18nBundle           *i18n.Bundle
	languageTagProviders languageproviders.LanguageTagProviders
	supportedLanguages   map[string]string
	frontEndHost         string
}

// NewUsersApiController creates a default api controller
func NewPublicUsersApiController(
	s coreapi.UsersApiServicer,
	lockout lockouttracker.LockoutTracker,
	frontHost string,
	i18nBundle *i18n.Bundle,
	languageTagProviders languageproviders.LanguageTagProviders,
	supportedLanguages map[string]string,
) coreapi.PublicUsersApiRouter {
	return &PublicUsersApiController{
		service:              s,
		lt:                   lockout,
		frontEndHost:         frontHost,
		i18nBundle:           i18nBundle,
		languageTagProviders: languageTagProviders,
		supportedLanguages:   supportedLanguages,
	}
}

// Routes returns all of the api route for the UsersApiController
func (c *PublicUsersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostUsers",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostUsers,
		},
		{
			Name:        "PostUsersLoginSSO",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/login/sso",
			HandlerFunc: c.PostUsersLoginSSO,
		},
		{
			Name:        "PostUsersLoginGoogle",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/login/google",
			HandlerFunc: c.PostUsersLoginGoogle,
		},
		{
			Name:        "PostUsersLoginMagicLink",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/login/magiclink",
			HandlerFunc: c.PostUsersLoginMagicLink,
		},
	}
}

func (c *PublicUsersApiController) GetPathPrefix() string {
	return "/v1/users"
}

func (c *PublicUsersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//no routes require auth
	return []func(next http.Handler) http.Handler{}
}

// PostUsers - Create new user
func (c *PublicUsersApiController) PostUsers(w http.ResponseWriter, r *http.Request) {
	rd := &coreapi.RegisterData{}
	if err := json.NewDecoder(r.Body).Decode(&rd); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	//new users can be created from transfers, in which case they'll have an auth token
	//if they don't, they won't have a token
	transferId := ""
	var err error
	token := r.Header.Get("Authorization")
	if token != "" {
		transferId, _, err = auth.DecodeChallengeUnlockToken(token)
		if err != nil {
			httperror.ErrorWithLog(
				w,
				r,
				err.Error(),
				http.StatusUnauthorized,
			) // Error handler for now, may change if new register scenario implement
			return
		}
	}

	if err := rd.Valid(); err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			fmt.Sprintf("%s: %s", errmsg.ERR_INVALID_REQ_BODY, err.Error()),
			http.StatusBadRequest,
		)
		return
	}

	query := r.URL.Query()
	source := query.Get("source")

	clientAddr := strings.Split(r.RemoteAddr, ":")
	ip := clientAddr[0]
	verificationToken, err := c.service.PostUsers(r.Context(), *rd, transferId, ip, source)
	if err != nil {

		_, isBadPasswordErr := err.(accountservice.ErrBadNewPassword)
		// if it's an error with the client given password or register pin we can let the client know
		if isBadPasswordErr || strings.HasPrefix(err.Error(), errormsgs.ERR_INVALID_REQ_BODY) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		} else if strings.HasPrefix(err.Error(), errormsgs.ERR_NOT_AUTHORIZED) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know about it
		// so only log it for now and return a 200
		logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
			"error_msg": err.Error(),
		}).Error("Post User Error")
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), verificationToken, &status, w)
}

// PostUsersLoginSSO - LoginSSO
func (c *PublicUsersApiController) PostUsersLoginSSO(w http.ResponseWriter, r *http.Request) {

	var ssoToken string
	err := json.NewDecoder(r.Body).Decode(&ssoToken)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if ssoToken == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	clientAddr := strings.Split(r.RemoteAddr, ":")
	ip := clientAddr[0]
	result, err := c.service.PostUsersLoginSSO(r.Context(), ssoToken, ip)

	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_ACCOUNT_LOCKED {
			status = http.StatusForbidden
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	refreshToken := result.RefreshToken
	if refreshToken != "" {
		http.SetCookie(
			w,
			&http.Cookie{
				Name:     "refresh_token",
				Value:    refreshToken,
				HttpOnly: true,
				Secure:   true,
				Path:     "/",
			},
		)
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PublicUsersApiController) PostUsersLoginGoogle(w http.ResponseWriter, r *http.Request) {

	var request coreapi.OneTapSSOLoginRequest
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if request.JWT == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	clientAddr := strings.Split(r.RemoteAddr, ":")
	ip := clientAddr[0]
	result, err := c.service.PostUsersLoginGoogle(r.Context(), request.JWT, ip)

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_ACCOUNT_LOCKED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_ACCOUNT_LOCKED, http.StatusForbidden)
		} else if err.Error() == errmsg.ERR_BAD_TOKEN {
			httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_TOKEN, http.StatusBadRequest)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	refreshToken := result.RefreshToken
	if refreshToken != "" {
		http.SetCookie(
			w,
			&http.Cookie{
				Name:     "refresh_token",
				Value:    refreshToken,
				HttpOnly: true,
				Secure:   true,
				Path:     "/",
			},
		)
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PublicUsersApiController) PostUsersLoginMagicLink(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	lg := logutils.CtxLogger(ctx)

	var request coreapi.AccountLoginViaSSORequest
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		lg.WithError(err).Info("error reading request body")
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if request.Token == "" || request.AccountId == "" {
		lg.Error("cannot process user login via sso token: invalid param accountId/token")
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	clientAddr := strings.Split(r.RemoteAddr, ":")
	ip := clientAddr[0]
	result, err := c.service.PostLoginViaSSO(ctx, request.Token, request.AccountId, ip)

	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_ACCOUNT_LOCKED {
			status = http.StatusForbidden
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	refreshToken := result.RefreshToken
	if refreshToken != "" {
		http.SetCookie(
			w,
			&http.Cookie{
				Name:     "refresh_token",
				Value:    refreshToken,
				HttpOnly: true,
				Secure:   true,
				Path:     "/",
			},
		)
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(ctx, result, &status, w)
}
