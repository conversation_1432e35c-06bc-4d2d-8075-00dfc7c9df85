package users

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/stretchr/testify/mock"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"golang.org/x/text/language"
)

type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

func TestPostUsers(t *testing.T) {
	loginRequest := &coreapi.RegisterData{
		Email:     "testemail",
		Password:  "testpw",
		DOB:       time.Now().Add(-48 * time.Hour),
		FirstName: "J",
		LastName:  "J",
	}
	credentials, err := json.Marshal(loginRequest)
	i18nBundle := i18n.NewBundle(language.English)
	if err != nil {
		t.Fatal(err)
	}
	mockProvider := phlanguage.LanguageTagProvider{
		GetLanguageTag: func(ctx context.Context, id interface{}) language.Tag { return language.English },
	}
	mockLangProviders := languageproviders.LanguageTagProviders{
		AccountId: mockProvider,
		ClinicId:  mockProvider,
		OrgId:     mockProvider,
	}
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}

	t.Run("invalid password", func(t *testing.T) {
		request := httptest.NewRequest("POST", "/v1/users", bytes.NewReader(credentials))
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			PostUsers(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return("", errors.New(errormsgs.ERR_INVALID_REQ_BODY))
		lt := lockouttracker.NewMockLockoutTracker()
		c := NewPublicUsersApiController(
			service,
			lt,
			"testhost",
			i18nBundle,
			mockLangProviders,
			mockSupportedLanguages,
		)

		actual := httptest.NewRecorder()
		c.PostUsers(actual, request)
		// response should be http error bad request.
		expected := httptest.NewRecorder()
		http.Error(expected, errmsg.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		compareResponseRecorders(actual, expected, t)
	})

	t.Run("good case, with auth token", func(t *testing.T) {
		request := httptest.NewRequest("POST", "/v1/users", bytes.NewReader(credentials))
		ip := strings.Split(request.RemoteAddr, ":")[0]
		service := mockcoreapi.NewMockUsersApiServicer(t)
		service.EXPECT().
			PostUsers(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return("", nil)
		lt := lockouttracker.NewMockLockoutTracker()
		c := NewPublicUsersApiController(
			service,
			lt,
			"testhost",
			i18nBundle,
			mockLangProviders,
			mockSupportedLanguages,
		)
		token, err := auth.MakeChallengeUnlockToken("140", "abc", ip)
		if err != nil {
			t.Fatal(err)
		}
		request.Header.Add("Authorization", "Bearer "+token)
		actual := httptest.NewRecorder()
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("expected PostUsers not to panic but it did, with message %q", r)
			}
		}()
		c.PostUsers(actual, request)

		if actual.Code != 200 {
			t.Errorf("expected status %d but got %d", 200, actual.Code)
		}
	})
	// This is the /JOIN case, where there's no transfer or request from the user
	t.Run("good case, without auth token", func(t *testing.T) {
		registerRequest := &coreapi.RegisterData{
			Email:     "testemail",
			Password:  "testpw",
			FirstName: "name",
			LastName:  "name",
			DOB:       time.Date(2021, 0o3, 0o2, 0, 0, 0, 0, time.UTC),
		}
		credentials, err := json.Marshal(registerRequest)
		if err != nil {
			t.Fatalf("setup failed: register request did not marshal - %v", err)
		}
		request := httptest.NewRequest("POST", "/v1/users", bytes.NewReader(credentials))
		calls := 0
		service := &MockUsersApiService{MockAuth: func(email string, password string) error {
			calls++
			return nil
		}}
		lt := lockouttracker.NewMockLockoutTracker()
		c := NewPublicUsersApiController(
			service,
			lt,
			"testhost",
			i18nBundle,
			mockLangProviders,
			mockSupportedLanguages,
		)
		actual := httptest.NewRecorder()
		c.PostUsers(actual, request)
		if calls != 1 {
			t.Errorf("expected MockAuth to be called once but it was called %d times", calls)
		}

		if actual.Code != 200 {
			t.Errorf("expected status %d but got %d", 200, actual.Code)
		}
	})
	t.Run("bad request: invalid token", func(t *testing.T) {
		token := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************.qD9odj2LZn2bh7hLbIwOkI4NhcWWI8hmUq6-hBGuVzI"
		request := httptest.NewRequest("POST", "/v1/users", bytes.NewReader(credentials))
		request.Header.Add("Authorization", token)
		lockoutTracker := lockouttracker.NewMockLockoutTracker()
		service := &MockUsersApiService{}
		c := NewPublicUsersApiController(
			service,
			lockoutTracker,
			"testhost",
			i18nBundle,
			mockLangProviders,
			mockSupportedLanguages,
		)

		actual := httptest.NewRecorder()
		c.PostUsers(actual, request)
		resp := actual.Result()
		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func compareResponseRecorders(
	actual *httptest.ResponseRecorder,
	expected *httptest.ResponseRecorder,
	t *testing.T,
) {
	compareHttpResponse(actual.Result(), expected.Result(), t)
}

// Compare status and body of the given http responses. If they do not match, log and fail the test.
func compareHttpResponse(actual *http.Response, expected *http.Response, t *testing.T) {
	status, message, err := readResponse(actual)
	if err != nil {
		t.Fatal(err)
	}
	expectedStatus, expectedMessage, err := readResponse(expected)
	if err != nil {
		t.Fatal(err)
	}
	if !(status == expectedStatus && message == expectedMessage) {
		t.Errorf("expected response to be %q: %q but it was Status %q: %q",
			expectedStatus, expectedMessage, status, message)
	}
}

// Extract a status code and message from a ResponseRecorder
func readResponse(resp *http.Response) (status string, message string, err error) {
	status = resp.Status
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	message = string(body)
	return status, message, err
}
