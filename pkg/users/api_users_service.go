/*
* Core API
*
* Core API for PocketHealth
*
* API version: 1.0
* Contact: <EMAIL>
* Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package users

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"slices"
	"strconv"
	"sync"
	"time"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/samber/lo"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	notifications "gitlab.com/pockethealth/coreapi/pkg/mysql/notifications"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	sqlScans "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/amplitude_util"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/examutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/coreapi/pkg/util/rollout"
	"gitlab.com/pockethealth/coreapi/pkg/v2users"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

const (
	ORGANVIZ_MAX_REPORTS_TO_TRIGGER = 3
)

// UsersApiService is a service that implements the logic for the UsersApiServicer
// This service should implement the business logic for every endpoint for the UsersApi API.
// Include any external packages or services that will be required by this service.
type UsersApiService struct {
	sqldb                   *sql.DB
	containerClient         azureUtils.ContainerClient
	cioEmailer              cio_email.CIOEMailer
	frontEndHost            string
	i18nBundle              *i18n.Bundle
	languageTagProviders    languageproviders.LanguageTagProviders
	supportedLanguages      map[string]string
	acctServiceClient       accountservice.AccountService
	planSvc                 planservice.PlanService
	orgSvcClient            orgsvc.OrgService
	waitGroup               *sync.WaitGroup
	convertImage            ConvertImageFunc
	excludeSoftDeletedExams bool
	experimentsClient       interfaces.AmplitudeExperimentClient
	organvizService         organviz.Service
	providersService        providersservice.ProvidersService
	eisvcClient             *examinsights.EIServiceUser
	rpClient                *reportprocessor.RPClient
	examService             examService.ExamServiceInterface
	reportService           coreapi.ReportsApiServicer
	riClient                reportinsights.ReportInsights
	roiClient               roiservice.RoiService
}

type ConvertImageFunc func(ctx context.Context, imageId string, accept string, auth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error)

type ReferEmail struct {
	FirstName string
	RegionID  uint16
}

type SSOLoginResponse struct {
	Token      string `json:"token"`
	UserExists bool   `json:"userExists"`
}

type VerifyEmailResponse struct {
	Successful   bool `json:"successful,omitempty"`
	AccountExist bool `json:"accountExist,omitempty"`
}

type RefreshResponse struct {
	Token string `json:"token,omitempty"`
}

// NewUsersApiService creates a default api service
func NewUsersApiService(mysqlDB *sql.DB,
	cc azureUtils.ContainerClient,
	es cio_email.CIOEMailer,
	fronthost string,
	i18nBundle *i18n.Bundle,
	langProviders languageproviders.LanguageTagProviders,
	supportedLanguages map[string]string,
	acctSvc accountservice.AccountService,
	orgsvc orgsvc.OrgService,
	planSvc planservice.PlanService,
	waitGroup *sync.WaitGroup,
	excludeSoftDeletedExams bool,
	experimentsClient interfaces.AmplitudeExperimentClient,
	organvizService organviz.Service,
	providersService providersservice.ProvidersService,
	convertImage ConvertImageFunc,
	eisvcClient *examinsights.EIServiceUser,
	rpClient *reportprocessor.RPClient,
	examService examService.ExamServiceInterface,
	reportService coreapi.ReportsApiServicer,
	riClient reportinsights.ReportInsights,
	roiClient roiservice.RoiService,
) coreapi.UsersApiServicer {
	return &UsersApiService{
		sqldb:                   mysqlDB,
		containerClient:         cc,
		cioEmailer:              es,
		frontEndHost:            fronthost,
		i18nBundle:              i18nBundle,
		languageTagProviders:    langProviders,
		supportedLanguages:      supportedLanguages,
		acctServiceClient:       acctSvc,
		orgSvcClient:            orgsvc,
		planSvc:                 planSvc,
		waitGroup:               waitGroup,
		excludeSoftDeletedExams: excludeSoftDeletedExams,
		experimentsClient:       experimentsClient,
		organvizService:         organvizService,
		providersService:        providersService,
		convertImage:            convertImage,
		eisvcClient:             eisvcClient,
		rpClient:                rpClient,
		examService:             examService,
		reportService:           reportService,
		riClient:                riClient,
		roiClient:               roiClient,
	}
}

// GetUserExamById - Get exam
func (s *UsersApiService) GetUserExamById(
	ctx context.Context,
	acctId string,
	examId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"exam_id": examId,
		"before":  time.Now().UTC(),
	})

	examRaw, err := s.examService.GetExam(ctx, acctId, examId)
	if err != nil {
		lg.WithError(err).Error("could not fetch exam from SQL")
	} else if examRaw.UUID == "" {
		lg.Error("no exam found in SQL")
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	}

	examRawList := []coreapi.ExamRaw{examRaw}

	// set unlock status by order attribution
	err = examutils.ApplyExamUnlockStatus(
		ctx,
		s.planSvc,
		s.acctServiceClient,
		acctId,
		examRawList,
	)
	if err != nil {
		lg.WithError(err).Error("failed to apply exam unlock statuses")
		return nil, err
	}
	exam := examRawList[0].ToExam(ctx)

	lg.WithFields(logrus.Fields{
		"after": time.Now().UTC(),
	}).Info("examLatency")
	return exam, err
}

func (s *UsersApiService) GetExamThumbnailByUuid(
	ctx context.Context,
	examUuid string,
	acctId string,
	shareId string,
) (io.ReadCloser, int64, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"share_id": shareId,
	})

	// verify that exam belongs to the share or user
	if shareId != "" {
		idList, err := exams.GetExamUUIDListForShare(ctx, s.sqldb, shareId)
		if err != nil {
			lg.Error("no exams found for this shareId")
			return nil, 0, errors.New(errmsg.ERR_NOT_FOUND)
		}

		found := false
		for _, uuid := range idList {
			if examUuid == uuid {
				found = true
				break
			}
		}

		if !found {
			lg.Error("exam does not belong to this user")
			return nil, 0, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

	} else {
		belongs, err := s.examService.ExamsBelongToAccount(ctx, acctId, []string{examUuid})

		if err != nil {
			lg.Error("failed to find exam")
			return nil, 0, errors.New("failed to find exam")
		} else if !belongs {
			lg.Error("exam does not belong to this user")
			return nil, 0, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}
	}

	var imageID string
	var err error

	if rollout.Rollout(ctx, s.sqldb, "use_first_available_record_streaming_thumbnail") {
		imageID, err = s.getRecordStreamingThumbnailForExamUUID(ctx, examUuid)
		if err != nil {
			lg.WithError(err).Error(
				"error getting record streaming thumbnail - falling back to default thumbnail fetch logic",
			)
		}
	}

	if imageID == "" {
		imageID, err = s.examService.GetFirstImageID(ctx, acctId, examUuid)
		if err != nil {
			lg.WithError(err).Error("no image found for this exam")
			return nil, 0, errors.New(errmsg.ERR_NOT_FOUND)
		}
	}

	return s.convertImage(
		ctx,
		imageID,
		"image/png",
		true,
		s.containerClient,
		s.sqldb,
		s.waitGroup,
		false,
	)
}

// GetUserExams - Get exams, optionally filter by activated if not null
func (s *UsersApiService) GetUserExams(
	ctx context.Context,
	accountID string,
	includeReports bool,
	q queries.Queries,
) ([]coreapi.Exam, error) {
	examList, err := s.examService.GetExamsResponse(
		ctx,
		accountID,
		[]string{},
		q,
		includeReports,
		s.excludeSoftDeletedExams,
	)
	if err != nil {
		return nil, err
	}

	transferIds := map[string]int{}
	for _, ex := range examList {
		if _, ok := transferIds[ex.TransferId]; !ok {
			transferIds[ex.TransferId] = 1
		}
	}

	logutils.Debugx(q.WithFields(ctx), "GET /exams result",
		"num_exams", len(examList),
		"num_transfers", len(transferIds),
		"acct_id", accountID,
	)

	return examList, nil
}

func (s *UsersApiService) GetUserExamsSize(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	return s.examService.GetUserExamsSize(ctx, acctId)
}

// GetUserRequestConsent - Get consent pdf(s)
func (s *UsersApiService) GetUserRequestConsent(
	ctx context.Context,
	acctId string,
	requestId int64,
	providerId uint,
	transferId string,
) ([]byte, error) {
	if requestId != 0 {

		auth, err := requests.BelongsToAcct(ctx, s.sqldb, acctId, requestId)
		if err != nil {
			return nil, err
		}
		if !auth {
			return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		blobReader, _ := azureUtils.DownloadBlobReader(
			ctx,
			s.containerClient.Requests,
			strconv.FormatInt(requestId, 10),
		)
		decBytesFile, err := io.ReadAll(blobReader)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't read blob")
			return nil, err
		}
		return decBytesFile, nil
	} else {
		err := errors.New("consent type retrieval not implemented")
		logutils.DebugCtxLogger(ctx).WithError(err).Error()
		return nil, err
	}
}

// GetNotificationByAccount - Get notifications of the acct
func (s *UsersApiService) GetNotificationsByAccount(
	ctx context.Context,
	acctId string,
	includeRead bool,
) (coreapi.UserNotifications, error) {
	userNotifications, err := notifications.GetAllAccountNotifications(ctx, s.sqldb, acctId)
	if err != nil {
		logrus.WithField("acct_id", acctId).
			WithError(err).
			Error("failed to get user notifications")
		return userNotifications, err
	}

	if !includeRead {
		unreadNotifications := notifications.FilterNotificationsByUnread(userNotifications)
		userNotifications.Notifications = unreadNotifications
	}
	return userNotifications, err
}

// Create a new notification for a user, if a notification of this type already exists then a `CONFLICT` error will the thrown.
func (s *UsersApiService) CreateUserNotification(
	ctx context.Context,
	accountId string,
	notification coreapi.NotificationType,
) (coreapi.Notification, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":        accountId,
		"notification_type": notification,
	})

	if !notification.Valid() {
		lg.Error("Invalid notification type")
		return coreapi.Notification{}, errors.New(errormsgs.ERR_INVALID_REQ_BODY)
	}

	currentNotifCount, err := notifications.CountNotificationsForAccountByType(
		ctx,
		s.sqldb,
		accountId,
		notification,
	)
	if err != nil && err.Error() != sql.ErrNoRows.Error() {
		lg.WithError(err).Error("Could not create notification")
		return coreapi.Notification{}, err
	}
	if currentNotifCount > 0 {
		return coreapi.Notification{}, errors.New(errormsgs.ERR_CONFLICT)
	}

	newNotif, err := notifications.CreateAccountNotification(
		ctx,
		s.sqldb,
		ksuid.New().String(),
		accountId,
		notification,
		"",
		nil,
	)
	if err != nil {
		lg.WithError(err).Error("Could not create notification")
		return coreapi.Notification{}, err
	}

	return newNotif, nil
}

func (s *UsersApiService) DeleteUserNotificationById(
	ctx context.Context,
	accountId, notificationId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":      accountId,
		"notification_id": notificationId,
	})

	err := notifications.DeleteNotificationById(ctx, s.sqldb, accountId, notificationId)
	if err != nil {
		lg.WithError(err).Error("Could not delete notification")
		return err
	}

	return nil
}

// Mark user notification as read
func (s *UsersApiService) UpdateNotificationAsReadById(
	ctx context.Context,
	accountId, notificationId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":      accountId,
		"notification_id": notificationId,
	})

	err := notifications.SetNotificationReadById(ctx, s.sqldb, accountId, notificationId)
	if err != nil && err.Error() == sql.ErrNoRows.Error() {
		lg.WithError(err).Error("failed to find the notification with the given id")
		return errors.New(errmsg.ERR_NOT_FOUND)
	} else if err != nil {
		lg.WithError(err).Error("failed to query the database")
	}

	return err
}

// DeleteUsersLogout - Logout
func (s *UsersApiService) DeleteUsersLogout(
	ctx context.Context,
	token string,
) (interface{}, error) {
	// Blacklist token. Expiry will already have been checked in auth middleware
	return nil, auth.AddToBlacklist(ctx, token)
}

// PostUsers - Create new user
func (s *UsersApiService) PostUsers(
	ctx context.Context,
	rd coreapi.RegisterData,
	transferId, ip, acctCreationSource string,
) (verificationToken string, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("transfer_id", transferId)

	if transferId == "" {
		// signing up through /join
		newProf := coreapi.UserProfile{
			IsAccountOwner: true,
			Email:          rd.Email,
			FirstName:      rd.FirstName,
			LastName:       rd.LastName,
			DOB:            rd.DOB.Format("01/02/2006"),
		}
		// send them through acctsvc verification flow
		acctId, _, err := accounts.RegisterNewAccount(
			ctx,
			s.sqldb,
			s.acctServiceClient,
			lg,
			rd.Email,
			rd.Password,
			newProf,
			"",
			"",
			acctCreationSource,
			accountservice.PATIENT_CREATION_ACCOUNT,
		)

		if _, ok := err.(accountservice.ErrNewAcctConflict); !ok && err != nil {
			// do not return with error if account exists
			lg.WithError(err).Error("unable to register acct and send verification email")
			return "", err
		} else if err == nil {
			lg.WithField("acct_id", acctId).
				Info("created account and sent verification email")
		}

		if _, ok := err.(accountservice.ErrNewAcctConflict); acctId == "" && ok {
			// Fetch existing account so that verify-code token is returned in all cases.
			// Otherwise, this EP reveals if accounts exist in our system
			account, err := s.acctServiceClient.GetAccountInfoByEmail(ctx, rd.Email)
			if err != nil {
				lg.WithError(err).
					Info("unable to fetch existing account by email")
				return "", nil
			} else {
				acctId = account.AccountId
			}
		}

		experiments, err := s.experimentsClient.Fetch(
			&experiment.User{UserId: acctId},
		)
		if err != nil {
			lg.WithError(err).
				Infof("unable to fetch experiment for accountId: %s", acctId)
		}

		triggerVerificationCode := false
		if variant, ok := experiments[amplitude_util.REGISTER_PAGE_CODE]; ok &&
			variant.Value == "on" {
			triggerVerificationCode = true
		}

		var token string
		if triggerVerificationCode {
			token, err = s.acctServiceClient.PostAccountVerificationCode(ctx, acctId)
			if err != nil {
				lg.WithError(err).
					Infof("unable to get token for account verification for accountId: %s", acctId)
				return "", err
			}
		}

		return token, nil
	} else {
		// complete signup after activating first transfer
		// sets password and verify email in acctsvc
		lg := lg.WithFields(logrus.Fields{
			"transfer_id": transferId,
		})
		acctId, err := sqlScans.GetScanAccountId(ctx, s.sqldb, transferId)
		if err != nil {
			lg.WithError(err).Error("error getting user id for transfer")
			return "", err
		}
		err = s.acctServiceClient.SetPasswordAndVerify(ctx, acctId, rd.Password)
		if err != nil {
			lg.WithError(err).Error("error setting password and finalizing user")
			return "", err
		}
		return "", nil
	}
}

func (s *UsersApiService) PostLoginViaSSO(
	ctx context.Context,
	token, accountId, ip string,
) (accountservice.AccountSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	response, err := s.acctServiceClient.AccountLoginViaSSO(ctx, token, accountId, ip)
	if err != nil {
		lg.WithError(err).Info("error processing account login via SSO")
		return accountservice.AccountSSOLoginResponse{}, err
	}
	// historical FE functionality - users will always exist here, but if this is false the FE redirects to a different page.
	response.UserExists = true

	// sync patient jacket on login
	v2users.SyncPatientJacketOnLogin(ctx, lg, s.sqldb, s.roiClient, response.GetAccountID())

	return response, nil
}

// PostUsersLoginSSO - LoginSSO
func (s *UsersApiService) PostUsersLoginSSO(
	ctx context.Context,
	ssoToken string,
	ip string,
) (accountservice.AccountSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	response, err := s.acctServiceClient.AccountLoginSSO(ctx, ssoToken, ip)
	if err != nil {
		lg.WithError(err).Error("error processing account login with SSO")
		return accountservice.AccountSSOLoginResponse{}, err
	}
	// historical FE functionality - users will always exist here, but if this is false the FE redirects to a different page.
	response.UserExists = true

	// sync patient jacket on login
	v2users.SyncPatientJacketOnLogin(ctx, lg, s.sqldb, s.roiClient, response.GetAccountID())

	return response, nil
}

func (s *UsersApiService) PostUsersLoginGoogle(
	ctx context.Context,
	jwt string,
	ip string,
) (accountservice.GoogleSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	response, err := s.acctServiceClient.AccountLoginGoogle(ctx, jwt, ip)
	if err != nil {
		lg.WithError(err).Error("error processing account login with SSO")
		return accountservice.GoogleSSOLoginResponse{}, err
	}
	// historical FE functionality - users will always exist here, but if this is false the FE redirects to a different page.
	response.UserExists = true

	// sync patient jacket on login
	v2users.SyncPatientJacketOnLogin(ctx, lg, s.sqldb, s.roiClient, response.GetAccountID())

	return response, nil
}

func (s *UsersApiService) PostFriendReferral(
	ctx context.Context,
	accountId string,
	emailAddr string,
) error {
	// if the email already has an account associated, don't send a referral
	referralAcctId, err := s.acctServiceClient.LookupAccountIdByEmail(ctx, emailAddr)
	if referralAcctId != "" || err != nil {
		return nil
	}
	var acctOwner accountservice.Patient
	pts, err := s.acctServiceClient.GetPatients(ctx, accountId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't retrieve acct pts")
	} else {
		acctOwner = accountservice.GetAcctOwner(pts)
	}
	firstName := acctOwner.FirstName

	err = s.cioEmailer.Send(
		ctx,
		emailAddr,
		accountId,
		cio_email.FRIEND_REFERRAL,
		map[string]interface{}{
			"first_name": firstName,
			"region_id":  regions.GetRegionID(),
		},
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't send email")
		return err
	}

	return nil
}

func (s *UsersApiService) GetUserSettings(ctx context.Context, acctId string) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})

	acctSettings, err := s.acctServiceClient.GetAccountSettings(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("unable to retrieve settings from acctsvc")
		return nil, err
	}

	return acctSettings, nil
}

func (s *UsersApiService) UpdateUserSettings(
	ctx context.Context,
	acctId string,
	settings accountservice.UserSettingsRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})

	err := s.acctServiceClient.UpdateAccountSettings(ctx, acctId, settings)
	if err != nil {
		lg.WithError(err).Error("error updating settings in acctsvc")
	}

	return err
}

func (s *UsersApiService) TriggerOrganVisualizationInference(
	ctx context.Context,
	acctId string,
	q queries.Queries,
) error {
	lg := logutils.CtxLogger(ctx)

	// fetch all reports a user has
	examList, err := s.examService.GetExamSummaryData(
		ctx,
		acctId,
		[]string{}, // list of exams to get summaries for; empty means all
		q,
		true, // needReports
		s.excludeSoftDeletedExams,
	)
	if err != nil {
		return fmt.Errorf("failed getting exams: %w", err)
	}
	// ensure we always prepare newest reports first
	sortByExamDateDesc(ctx, examList)

	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
	go func(bgCtx context.Context) {
		numExamsTriggered := 0
		for _, exam := range examList {
			lg = lg.WithField("exam_uuid", exam.UUID)

			eligibility, err := s.organvizService.IsExamEligible(bgCtx, exam.ExamRawBasic, false /* interactive */)
			if err != nil {
				lg.WithError(err).Error("unable to determine if exam is eligible for organviz")
				continue
			}
			if !slices.Contains(
				[]organviz.ExamEligibility{
					organviz.ELIGIBLE_FOR_MEDSAM,
					organviz.ELIGIBLE_FOR_MRI,
					organviz.ELIGIBLE_FOR_XRAY,
				}, eligibility) {
				continue
			}

			// trigger inference by requesting visualizations, but not using the results
			_, err = s.organvizService.FetchVisualizationsForExam(
				bgCtx,
				exam.UUID,
				acctId,
				eligibility,
				false, // only non-interactive oviz gets generated on login for now
			)
			if err != nil {
				lg.WithError(err).Error("failed triggering inference for exam")
				continue
			}
			numExamsTriggered++
			if numExamsTriggered == ORGANVIZ_MAX_REPORTS_TO_TRIGGER {
				break
			}
		}
	}(bgCtx)
	return nil
}

func sortByExamDateDesc(ctx context.Context, examList []coreapi.ExamRaw) {
	lg := logutils.CtxLogger(ctx)
	slices.SortStableFunc(examList, func(a, b coreapi.ExamRaw) int {
		lg = lg.WithFields(logrus.Fields{
			"exam1": a.ExamId,
			"exam2": b.ExamId,
		})

		dateA, err := time.Parse(coreapi.DICOMDateFormat, a.DICOMExamDate)
		if err != nil {
			lg.WithError(err).Error("failed parsing exam date")
			return 1 // put unknown dates at the end
		}

		dateB, err := time.Parse(coreapi.DICOMDateFormat, b.DICOMExamDate)
		if err != nil {
			lg.WithError(err).Error("failed parsing exam date")
			return -1 // put unknown dates at the end
		}

		return -1 * dateA.Compare(dateB)
	})
}

func (s *UsersApiService) getRecordStreamingThumbnailForExamUUID(
	ctx context.Context,
	examUUID string,
) (string, error) {
	var imageID string
	// lookup exam uuid in unique_study_index to get provider id and study uid
	studyUID, providerID, err := mysql.GetStudyUIDAndProviderIDByExamUUID(s.sqldb, examUUID)

	// return without error for non-record streaming exams
	if errors.Is(err, sql.ErrNoRows) {
		return "", nil
	} else if err != nil {
		return "", err
	}

	// get study upload status from providersservice
	uploadStatusStudy, err := s.providersService.GetUploadStatusForStudyProvider(
		ctx,
		studyUID,
		providerID,
	)
	if err != nil {
		return "", err
	}
	if uploadStatusStudy == nil {
		return "", errors.New(errmsg.ERR_NOT_FOUND)
	}

	// pick first available instance
	var instanceUID string
	for k, v := range uploadStatusStudy.UploadManifest.Instances {
		if v {
			instanceUID = k
			break
		}
	}
	if instanceUID == "" {
		return "", errors.New(errmsg.ERR_NOT_FOUND)
	}

	// get object ID from unique_instance_index
	imageID, err = mysql.GetObjectIDByInstanceProviderStudy(
		s.sqldb,
		instanceUID,
		providerID,
		studyUID,
	)
	if err != nil {
		return "", errors.New(errmsg.ERR_NOT_FOUND)
	}
	return imageID, nil
}

func (s *UsersApiService) GetExamInsightsEligibility(
	ctx context.Context,
	accountId string,
	patientId string,
) (map[string]*coreapi.ExamInsightsEligibility, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"patient_id": patientId,
	})

	if patientId != "" {
		if !s.acctServiceClient.AccountHasAccessToPatient(ctx, accountId, patientId) {
			return nil, errors.New(errormsgs.ERR_NOT_FOUND)
		}
	}

	// {"exam_uuid": {eligibilitiesForExams + reports}}
	eligibilitiesForExams := make(map[string]*coreapi.ExamInsightsEligibility)
	// get rho eligibility
	rhoEligibility, err := s.eisvcClient.GetInsightEligibility(
		ctx,
		accountId,
		patientId,
		examinsights.Rho,
	)
	if err != nil && err.Error() == errmsg.ERR_NOT_FOUND {
		// either patient not found, or no exams found
		// or an actual 404 (path not found) maybe?
		// continue filling in other eligibilities
		lg.WithError(err).Error("patient or eligible exams for patient not found in eisvc")
	} else if err != nil {
		lg.WithError(err).Error("error getting rho eligibility from eisvc")
		return nil, err
	}
	for _, exam := range rhoEligibility {
		if e, ok := eligibilitiesForExams[exam.ExamUuid]; ok {
			e.Rho = exam.Eligible
		} else {
			eligibilitiesForExams[exam.ExamUuid] = &coreapi.ExamInsightsEligibility{
				Rho: exam.Eligible,
			}
		}
	}

	// get follow ups
	// get all exam uuids and report id for patient
	// since the eligible EPs from Exam Insights service doesn't necessarily return ALL exams a patient has
	examToReportIDs, err := s.examService.GetAllReportIDsForPatient(ctx, accountId, patientId)
	if err != nil {
		lg.WithError(err).Error("error getting all exam uuids")
		return nil, err
	}

	for exam, reportIDs := range examToReportIDs {
		// initialize eligibility object if not already done so
		if _, ok := eligibilitiesForExams[exam]; !ok {
			// todo: do we need to return absolutely ALL exams?
			eligibilitiesForExams[exam] = &coreapi.ExamInsightsEligibility{}
		}

		eligibilitiesForExams[exam].Report = true

		// get follow ups from ri
		followups, err := s.riClient.GetFollowups(ctx, reportIDs)
		if err != nil {
			lg.WithError(err).Error("error getting follow ups from reportinsights")
			// log, and continue to next exam
			continue
		}

		// fill in availability/eligibility
		for reportId := range followups {
			eligibilitiesForExams[exam].Followups = append(
				eligibilitiesForExams[exam].Followups,
				coreapi.FollowUp{
					ReportId:    reportId,
					HasFollowup: followups[reportId].Exist,
					Occurrences: followups[reportId].Occurrences,
				},
			)
		}
	}

	return eligibilitiesForExams, nil
}

func (s *UsersApiService) GetReportInsights(
	ctx context.Context,
	accountId string,
	patientId string,
) (reportinsights.InsightsResponse, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"patient_id": patientId,
	})
	examToReportIDs, err := s.examService.GetAllReportIDsForPatient(ctx, accountId, patientId)
	if err != nil {
		lg.WithError(err).Error("error getting all exam uuids and report ids")
		return reportinsights.InsightsResponse{}, err
	}
	reportIdsList := lo.Values(examToReportIDs)
	reportIds := lo.Flatten(reportIdsList)
	if len(reportIds) == 0 {
		// return early as no report is available in the account
		return reportinsights.InsightsResponse{}, nil
	}
	res, err := s.riClient.GetInsights(ctx, reportIds)
	if err != nil {
		lg.WithError(err).Error("error getting report insights")
		return reportinsights.InsightsResponse{}, err
	}

	if (res.OrganvizCount.Ct + res.OrganvizCount.Xray) > 0 {
		err = s.handleReportInsightsNotification(
			ctx,
			accountId,
			lg,
			coreapi.NotificationImageReader,
			res,
			examToReportIDs,
		)
		if err != nil {
			return res, err
		}
	}

	err = s.handleReportInsightsNotification(
		ctx,
		accountId,
		lg,
		coreapi.NotificationReportExplanations,
		res,
		examToReportIDs,
	)
	if err != nil {
		return res, err
	}

	return res, nil
}

func (s *UsersApiService) GetOrganVisualizationByExamId(ctx context.Context, examId string, acctId string, interactive bool) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_id":    examId,
		"account_id": acctId,
	})

	exam, err := s.examService.GetExam(ctx, acctId, examId)
	var errAccountIdMismatch sqlExams.ErrAccountIdMismatch
	if errors.As(err, &errAccountIdMismatch) {
		return nil, fmt.Errorf(errormsgs.ERR_NOT_FOUND)
	} else if err != nil {
		return nil, fmt.Errorf("failed fetching data about exam %v: %w", examId, err)
	}

	// we currently only support CT scans of the abdomen; don't prepare anything
	// for any other type of reports
	eligibility, err := s.organvizService.IsExamEligible(ctx, exam.ExamRawBasic, interactive)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			return nil, err
		}
		return nil, fmt.Errorf("failed checking if exam is eligible: %w", err)
	}
	if !slices.Contains([]organviz.ExamEligibility{organviz.ELIGIBLE_FOR_MEDSAM, organviz.ELIGIBLE_FOR_MRI, organviz.ELIGIBLE_FOR_XRAY}, eligibility) {
		lg.WithField("reason", eligibility).Debug("Exam is not eligible for organ visualization")
		return nil, nil
	}

	// we want to make sure the ep call does not get terminated on context cancelled
	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)

	data, err := s.organvizService.FetchVisualizationsForExam(bgCtx, examId, acctId, eligibility, interactive)
	if err != nil {
		lg.WithError(err).Errorf("could not fetch visualization for exam id %s", examId)
		return nil, err
	}

	if data == nil {
		lg.Warnf("no visualization data returned for exam id %s", examId)
		return json.Marshal(coreapi.OrganVisualizationResponse{Organs: []coreapi.OrganVisualization{}})
	}

	// Validate the response format
	var visualization coreapi.OrganVisualizationResponse
	if err := json.Unmarshal(data, &visualization); err != nil {
		lg.WithError(err).Errorf("could not unmarshal visualization for exam id %s", examId)
		return nil, err
	}

	if len(visualization.Organs) == 0 {
		lg.Warnf("no organs found in visualization for exam id %s", examId)
	}

	return data, nil
}

func (s *UsersApiService) handleReportInsightsNotification(
	ctx context.Context,
	accountId string,
	lg *logrus.Entry,
	notificationType coreapi.NotificationType,
	reportInsightResp reportinsights.InsightsResponse,
	examToReportIDs map[string][]string,
) error {
	notifs, err := notifications.GetNotificationsForAccountByType(
		ctx,
		s.sqldb,
		accountId,
		notificationType,
	)
	if err != nil && err.Error() != sql.ErrNoRows.Error() {
		lg.WithError(err).Errorf("Could not check current %s notification", notificationType)
	}
	latestNotif := coreapi.Notification{}
	if len(notifs.Notifications) > 0 {
		latestNotif = notifs.Notifications[0]
		if !latestNotif.Read { // there's an unread notification, dont need to add another
			return nil
		}
	}

	examContainsFeature := make([]string, 0)
	for uuid, reportIds := range examToReportIDs {
		for _, reportId := range reportIds {
			switch notificationType {
			case coreapi.NotificationImageReader:
				if report, exists := reportInsightResp.Reports[reportId]; exists &&
					(report.Organviz.NumMasksGenerated > 0 || report.Organviz.Model != "") {
					examContainsFeature = append(examContainsFeature, uuid)
				}
			case coreapi.NotificationReportExplanations:
				report, err := s.reportService.GetReportMetadataById(ctx, reportId, accountId, "")
				if err != nil {
					lg.WithError(err).Errorf("error getting report metadata by for report id %s", reportId)
					return err
				}
				if report.Protocol != "RHO" && report.Protocol != "SO" && report.Protocol != "LL" {
					examContainsFeature = append(examContainsFeature, uuid)
				}
			}
		}
	}

	q, err := queries.NewWhere(map[string][]any{"e.activated": {true}})
	if err != nil {
		return err
	}

	examList, err := s.examService.GetExamSummaryData(
		ctx,
		accountId,
		examContainsFeature, // list of exams to get summaries for; empty means all
		q,
		false,
		s.excludeSoftDeletedExams,
	)
	if err != nil {
		lg.WithError(err).Error("failed getting exams")
		return fmt.Errorf("failed getting exams: %w", err)
	}
	sortByExamDateDesc(ctx, examList)
	latestExam := examList[0]
	if latestNotif.Data != nil && latestNotif.Data.ExamUuid == latestExam.UUID {
		return nil // user already read the notification that's associate with the latest exam, no need to add another
	}

	// only check for org level feature auth, if orgId is 0, cd upload we dont care about org level feature auth
	if latestExam.OrgId != 0 {
		mycareNavigatorAuth, err := s.planSvc.AuthorizeFeature(
			ctx,
			planservice.MYCARE_NAVIGATOR,
			planservice.FeatureAuthorizationParams{
				ProviderId: uint64(latestExam.OrgId),
			}, // #nosec G115 not worrying about max_int64
		)
		if err != nil {
			lg.WithError(err).Error("could not check mycare navigator feature authorize")
			return fmt.Errorf("could not check mycare navigator feature authorize: %w", err)
		}
		// if exam is not authorized for MCN, return early as we dont need to add notification
		if !mycareNavigatorAuth.Authorized {
			lg.Warn("unable to create report insights notifications - mycare navigator not authorized")
			return nil
		}

		var featureAuth planservice.FeatureAuthorizationResponse
		switch notificationType {
		case coreapi.NotificationImageReader:
			featureAuth, err = s.planSvc.AuthorizeFeature(
				ctx,
				planservice.IMAGE_READER,
				planservice.FeatureAuthorizationParams{ProviderId: uint64(latestExam.OrgId)},
			)
			if err != nil {
				lg.WithError(err).Error("could not check image reader feature authorize")
				return fmt.Errorf("could not check image reader feature authorize: %w", err)
			}
		case coreapi.NotificationReportExplanations:
			featureAuth, err = s.planSvc.AuthorizeFeature(
				ctx,
				planservice.REPORT_EXPLANATIONS,
				planservice.FeatureAuthorizationParams{ProviderId: uint64(latestExam.OrgId)},
			)
			if err != nil {
				lg.WithError(err).Error("could not check report explanations feature authorize")
				return fmt.Errorf("could not check report explanations feature authorize: %w", err)
			}
		}
		if !featureAuth.Authorized {
			lg.Warnf("unable to create report insights notification - %s not authorized", notificationType)
			return nil
		}

	}

	notifID := ksuid.New().String()
	_, err = notifications.CreateAccountNotification(
		ctx,
		s.sqldb,
		notifID,
		accountId,
		notificationType,
		latestExam.PatientId,
		&coreapi.NotificationData{ExamUuid: latestExam.UUID},
	)
	if err != nil {
		lg.WithError(err).Errorf("Could not create new %s notification", notificationType)
	}

	return nil
}
