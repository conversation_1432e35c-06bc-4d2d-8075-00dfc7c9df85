package cio_email

import (
	"context"
	"errors"

	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type CIOEMailer interface {
	Send(context.Context, string, string, NotificationType, map[string]interface{}) error
}

type CIOEmail struct {
	mailer   cioEmail.CIOMail
	emailIds map[string]string
}

func ConfigureMail(cioEmail cioEmail.CIOMail, cioEmailIds map[string]string) CIOEMailer {
	mail := CIOEmail{
		mailer:   cioEmail,
		emailIds: cioEmailIds,
	}
	return &mail
}

func (c *CIOEmail) Send(
	ctx context.Context,
	emailAddr string,
	accountId string,
	emailType NotificationType,
	messageData map[string]interface{},
) error {
	lg := logutils.CtxLogger(ctx).WithField("emailAddr", emailAddr)

	if _, ok := c.emailIds[string(emailType)]; !ok {
		return errors.New("invalid email type")
	}

	messageId := c.emailIds[string(emailType)]
	_, err := c.mailer.Send(ctx, emailAddr, accountId, messageId, messageData)
	if err != nil {
		lg.WithError(err).Error("failed to send email")
		return err
	}
	return nil
}
