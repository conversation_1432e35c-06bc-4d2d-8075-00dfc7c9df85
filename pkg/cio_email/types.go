package cio_email

type NotificationType string

const (
	REQUEST_CONFIRMATION        NotificationType = "request_confirmation"
	REQUEST_CONTINUE_TO_ACCOUNT NotificationType = "continue_to_account"
	REQUEST_DUPLICATE           NotificationType = "duplicate_request"
	PVR_PX                      NotificationType = "pvr_px"
	DELEGATE_REVIEW             NotificationType = "delegate_review"
	REQUEST_REJECTED            NotificationType = "request_rejected"
	ALREADY_ENROLLED            NotificationType = "already_enrolled"
	FRIEND_REFERRAL             NotificationType = "friend_referral"
	PATIENT_SHARE               NotificationType = "patient_share"
	SHARE_FAX_CONFIRMED         NotificationType = "share_fax_confirm"
	SHARE_EMAIL_CONFIRM         NotificationType = "share_email_confirm"
	SHARE_PAPER_CONFIRM         NotificationType = "share_paper_confirm"
	SHARE_FAX_FAILED            NotificationType = "share_fax_failed"
	SHARE_EMAIL_FAILED          NotificationType = "share_email_failed"
	SHARE_FAILED                NotificationType = "share_failed" // for print shares
)
