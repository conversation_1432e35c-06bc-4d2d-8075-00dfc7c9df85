//go:build integration
// +build integration

package providers

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/phutils/v10/pkg/random"
	phutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
	"golang.org/x/text/language"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	_ "github.com/go-sql-driver/mysql"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	sqlOrgs "gitlab.com/pockethealth/coreapi/pkg/mysql/organizations"
	sqlProviders "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	acctSvc "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
)

func setupProvider(
	t *testing.T,
	db *sql.DB,
	urlName string,
	offlineModalTitle string,
	orgName string,
) (orgId int64, clinicId int64) {
	patientFee := 260
	enrollConsent := true
	legal := true
	clinicName := "Providers Integration Test Clinic"
	legalSurcharge := "providers_integ_test"
	offlineModalHtml := "This org was created by providers integration tests. It should be automatically deleted when the test completes."
	//set up organization
	var res sql.Result
	var err error
	if offlineModalTitle != "" {
		res, err = db.Exec(
			`INSERT INTO organizations(url_name, name, patient_fee, offline_modal_title, offline_modal_html) VALUES (?, ?, ?, ?, ?)`,
			urlName,
			orgName,
			patientFee,
			offlineModalTitle,
			offlineModalHtml,
		)
	} else {
		res, err = db.Exec(`INSERT INTO organizations(url_name, name, patient_fee, offline_modal_html) VALUES (?, ?, ?, ?)`, urlName, orgName, patientFee, offlineModalHtml)
	}
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	orgId, err = res.LastInsertId()
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	t.Cleanup(func() {
		db.Exec(`DELETE FROM organizations WHERE id=?`, orgId)
	})
	//set up clinic
	res, err = db.Exec(`INSERT INTO clinics(organization_id, name) VALUES(?, ?)`, orgId, clinicName)
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	clinicId, err = res.LastInsertId()
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	t.Cleanup(func() {
		db.Exec(`DELETE FROM clinics WHERE id=?`, clinicId)
	})
	//set up form
	res, err = db.Exec(
		`INSERT INTO forms(organization_id, enroll_consent, legal, legal_surcharge) VALUES(?, ?, ?, ?)`,
		orgId,
		enrollConsent,
		legal,
		legalSurcharge,
	)
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	t.Cleanup(func() {
		db.Exec(`DELETE FROM forms WHERE organization_id=?`, orgId)
	})
	return
}
func TestProviders(t *testing.T) {
	db := testutils.SetupTestDB(t)

	accts := &accountservice.AcctSvcMock{}
	orgsvc := &orgservice.OrgServiceMock{ReturnDemoClinicData: true}
	provSvc := providersservice.NewProviderServiceMock()

	// Mock (empty) variables for providers api service
	languageTagProviders := languageproviders.LanguageTagProviders{
		AccountId: accounts.GetAccountIdLanguageTagProvider(accts),
		ClinicId:  sqlProviders.GetClinicLanguageTagProvider(orgsvc),
		OrgId:     sqlOrgs.GetOrgIdLanguageTagProvider(orgsvc),
	}
	i18nBundle := i18n.NewBundle(language.MustParse("en-US"))
	ampliEventClient := &mocks.MockAmplitudeEventData{
		EventLogs: make([]amplitude.Event, 0),
	}
	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	service := ProvidersApiService{
		sqldb:                db,
		containerClient:      azureUtils.ContainerClient{},
		i18nBundle:           i18nBundle,
		acctSvcClient:        accts,
		languageTagProviders: languageTagProviders,
		supportedLanguages:   map[string]string{"en": "English (United States)"},
		orgSvc:               orgsvc,
		ampliClient:          ampliEventClient,
		experimentsClient:    experimentsClient,
		cioEventProducer: &mocks.MockCIOProducerData{
			CallLogs: make([]map[string]interface{}, 0),
		},
		ServiceBusQueue: nil,
		provSvc:         provSvc,
	}
	t.Run("offline form", func(t *testing.T) {
		_, clinicId := setupProvider(
			t,
			db,
			"",
			"providers_integ_test",
			"Providers Integration Test Org",
		)
		ctx := context.Background()
		form, err := service.GetProviderFormConfig(ctx, clinicId, true, "")
		if err != nil {
			t.Error("unable to get offline form:", err.Error())
		}
		if _, ok := form.(coreapi.OfflineRequestConfig); !ok {
			t.Error("expected an offline form")
		}
	})

	t.Run("online form", func(t *testing.T) {
		_, clinicId := setupProvider(
			t,
			db,
			"providers_integ_test",
			"",
			"Providers Integration Test Org",
		)
		ctx := context.Background()
		form, err := service.GetProviderFormConfig(ctx, clinicId, true, "")
		if err != nil {
			t.Error("unable to get online form:", err.Error())
		}
		formConfig, ok := form.(coreapi.RequestFormConfig)
		if !ok {
			t.Error("expected an online form")
		}
		if formConfig.Provider.OrgName != "Providers Integration Test Org" {
			t.Error("Expected OrgName to be populated on provider object")
		}
	})

	t.Run("form - nonexistent provider", func(t *testing.T) {
		expectedError := "organization not found"
		ctx := context.Background()
		_, err := service.GetProviderFormConfig(ctx, 111111111111, true, "")
		if err == nil {
			t.Error("expected error but got none")
		} else if err.Error() != expectedError {
			t.Errorf("expected error to be %q but it was %q", expectedError, err.Error())
		}
	})

	//TODO Toks deprecate
	t.Run("fuzzySearch - deactivated providers", func(t *testing.T) {
		searchTerm := "Providers Integration Test"
		// deactivated clinics have neither a url_name nor valid offline modal info.
		orgId, _ := setupProvider(t, db, "", "", "Providers Integration Test Org")
		ctx := context.Background()
		results, err := service.GetProviders(ctx, searchTerm)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		for _, result := range results {
			if result.OrgId == orgId {
				t.Errorf(
					"got deactivated provider (orgId: %d) in result when expected not",
					result.OrgId,
				)
			}
		}
	})

	//TODO Toks deprecate
	t.Run("fuzzySearch -- offline / online providers", func(t *testing.T) {
		searchTerm := "fuzzySearch test provider"
		onlineOrgId, _ := setupProvider(
			t,
			db,
			"online fuzzySearch test provider",
			"",
			"Providers Integration Test Org",
		)
		offlineOrgId, _ := setupProvider(
			t,
			db,
			"",
			"offline_modal_title",
			"offline fuzzySearch test provider",
		)
		ctx := context.Background()
		results, err := service.GetProviders(ctx, searchTerm)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		foundOffline := false
		foundOnline := false
		for _, result := range results {
			if result.OrgId == onlineOrgId {
				foundOnline = true
			}
			if result.OrgId == offlineOrgId {
				foundOffline = true
			}
		}
		if !foundOnline {
			t.Errorf("expected online provider id %d in search but found none", onlineOrgId)
		}
		if !foundOffline {
			t.Errorf("expected offline provider id %d in search but found none", offlineOrgId)
		}
	})

	//TODO Toks deprecate
	t.Run("fuzzySearch -- Empty result", func(t *testing.T) {
		searchTerm := "nothing should match this search term"
		ctx := context.Background()
		results, err := service.GetProviders(ctx, searchTerm)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if len(results) != 0 {
			t.Errorf(
				"expected no providers to match search term but %d providers matched",
				len(results),
			)
		}
	})

	t.Run("get single provider", func(t *testing.T) {
		ctx := context.Background()
		//demo provider ->  Demo Medical Imaging
		clinicId := int64(13)
		orgId := int64(9)
		provider, err := service.GetProviderById(ctx, clinicId)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		ref := reflect.ValueOf(provider)
		refOrgId := ref.FieldByName("OrgId")
		providerOrgId := refOrgId.Int()
		if providerOrgId != orgId {
			t.Errorf("expected provider's orgId to be %d but it was %d", orgId, providerOrgId)
		}
		refProviderId := ref.FieldByName("ProviderId")
		providerId := refProviderId.Int()
		if providerId != clinicId {
			t.Errorf("expected provider's ProviderId to be %d but it was %d", clinicId, providerId)
		}
		providerStruct, ok := provider.(coreapi.Provider)
		if !ok {
			t.Error("expected to get a provider")
		}
		if providerStruct.Name != "Demo Medical Imaging" ||
			providerStruct.Name != providerStruct.ClinicName {
			t.Errorf(
				"expected provider's name to be %s but it was %s",
				"Demo Medical Imaging",
				providerStruct.Name,
			)
		}
		if providerStruct.OrgName != "Demo Medical Imaging" {
			t.Errorf(
				"expected provider's name to be %s but it was %s",
				"Demo Medical Imaging",
				providerStruct.OrgName,
			)
		}
	})

	t.Run("get single provider -- non exist provider", func(t *testing.T) {
		ctx := context.Background()
		_, err := service.GetProviderById(ctx, 0)
		if err == nil {
			t.Error("expected error but got none")
		}
	})

	t.Run("Get consent -- Non existent consent id", func(t *testing.T) {
		ctx := context.Background()
		_, err := service.GetProviderConsentData(
			ctx,
			"7gs_6ITYtA32HSP-BGELpWWy6YjcxiItjjI645Qcabk=",
		)
		if err == nil {
			t.Error("expected error but got none")
		}
	})

	t.Run("Post consent -- Non existent consent id", func(t *testing.T) {
		expectedError := errormsgs.ERR_NOT_FOUND
		consent := coreapi.Consent{}
		ctx := context.Background()
		_, err := service.PostProviderConsent(ctx, "thisisthefakeconsentid=", consent, "deviceId")
		if err == nil {
			t.Error("expected error but got none")
		} else if err.Error() != expectedError {
			t.Errorf("expected error to be %q but it was %q", expectedError, err.Error())
		}
	})

	t.Run(
		"post consent - appointment reminder -- good consent id opting in without account should call createAccount",
		func(t *testing.T) {
			consent := coreapi.Consent{
				Opt:          "in",
				FullName:     "Person One",
				SignatureImg: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
			}
			ctx := context.Background()
			dummyValue, err := random.GenerateRandomString(10)
			require.NoError(t, err, "error generating random string")
			email := generateRandEmail(t)
			mrn, err := random.GenerateRandomDigits(5)
			require.NoError(t, err, "error generating random digits as mrn")
			id := ksuid.New().String()
			appointmentReminderId := ksuid.New().String()
			deviceId := ksuid.New().String()
			accntId := ksuid.New().String()
			dummyDatetime := generateDateTimeStr(t, time.DateTime)
			cadence := 24
			testFirstName := dummyValue + "first"
			testLastName := dummyValue + "last"
			testdob := generateDateTimeStr(t, time.DateOnly)
			testDobSlash := generateDateTimeStr(
				t,
				"01/02/2006",
			) // this is dumb smh. for testing in createAccount() since it requires 01/02/2006 for dob

			// set up new consent
			_, err = db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email, is_appointment_reminder, first_name, last_name, dob) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
				id,
				1,
				mrn,
				email,
				1,
				testFirstName,
				testLastName,
				testdob,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			// set up appointment reminder data
			_, err = db.Exec(
				`INSERT IGNORE INTO appointment_reminders(consent_id, cio_temp_account_id, appointment_cadence, email, 
					mrn, dob, appointment_date, event_sent, 
					reminder_id, patient_name, gender, mobile, clinic_name, status, 
					clinic_info_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
				id,
				appointmentReminderId,
				cadence,
				email,
				mrn,
				dummyDatetime,
				dummyDatetime,
				dummyDatetime,
				dummyValue,
				dummyValue,
				dummyValue,
				dummyValue,
				dummyValue,
				dummyValue,
				"1",
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
				db.Exec("DELETE FROM appointment_reminders WHERE consent_id=?", id)
				db.Exec("DELETE FROM enrollments where email=?", email)
			})

			// set up mock
			mockCalled := false
			createAccountMock := func(_ context.Context, _ acctSvc.AccountService, _ *sql.DB, acctId string, emailAddr string, p coreapi.UserProfile, language string, deviceId string, acctSource string, ptSource string) (*accountservice.Account, string, bool, error) {

				mockCalled = true

				// `require` fails the test immediately
				require.Equal(t, email, emailAddr, "createAccount called with wrong email")
				// appointment reminder consents have first/last name pre-populated, such should use existing name instead of trying to parse from user input
				require.Equal(
					t,
					testFirstName,
					p.FirstName,
					"createAccount called with wrong first name",
				)
				require.Equal(
					t,
					testLastName,
					p.LastName,
					"createAccount called with wrong last name",
				)
				// appointment reminder consents sholud have dob
				require.Equal(t, testDobSlash, p.DOB, "createAccount called with wrong dob")
				require.Equal(
					t,
					accountservice.PATIENT_CREATION_CONSENT,
					ptSource,
					"createAccount called with wrong patient creation source",
				)

				// if none of the above fails, return normally
				return &acctSvc.Account{
					AccountId: accntId,
				}, "123", true, nil
			}
			service.getOrCreateAccount = createAccountMock
			service.acctSvcClient = &acctSvc.AcctSvcMock{LookUpAccountByEmailReturn: ""}
			provSvc.SetupReturn("GetConsentAppointmentReminderData", struct {
				Value coreapi.ConsentAppointmentData
				Err   error
			}{Value: coreapi.ConsentAppointmentData{
				CioTempAccountId:   appointmentReminderId,
				AppointmentCadence: cadence,
				MessageType:        "Email",
			}, Err: nil})

			_, err = service.PostProviderConsent(ctx, id, consent, deviceId)
			if err != nil &&
				err.Error() != "pdf maker failed" { // ignore pdf maker errors, covered in apptest
				t.Errorf("do not expect error but got: %q", err)
			}
			if !mockCalled {
				t.Error("expected createAccountOnConsent to be called")
			}

			expectedAmplitudeAccountCreatedEvent := amplitude.Event{
				EventType: "account created",
				UserID:    accntId,
				DeviceID:  deviceId,
				EventProperties: map[string]interface{}{
					"account_creation_source": "appointment reminders consent",
				},
			}
			if !reflect.DeepEqual(
				ampliEventClient.EventLogs[0],
				expectedAmplitudeAccountCreatedEvent,
			) {
				t.Fatalf(
					"expected amplitude event %#v but got %#v",
					expectedAmplitudeAccountCreatedEvent,
					ampliEventClient.EventLogs[0],
				)
			}

			expectedAmplitudeIdentifyEvent := amplitude.Event{
				EventType: "$identify",
				UserID:    accntId,
				DeviceID:  deviceId,
				UserProperties: map[amplitude.IdentityOp]map[string]interface{}{
					"$set": {
						"appointment_reminder_id": appointmentReminderId,
					},
				},
			}
			if !reflect.DeepEqual(ampliEventClient.EventLogs[1], expectedAmplitudeIdentifyEvent) {
				t.Fatalf(
					"expected amplitude event %#v but got %#v",
					expectedAmplitudeIdentifyEvent,
					ampliEventClient.EventLogs[1],
				)
			}
		},
	)

	t.Run(
		"post consent - non appointment reminder -- good consent id opting in without account should call createAccount",
		func(t *testing.T) {
			consent := coreapi.Consent{
				Opt:          "in",
				FullName:     "Person One",
				SignatureImg: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
			}
			ctx := context.Background()
			email := generateRandEmail(t)
			mrn, err := random.GenerateRandomDigits(5)
			require.NoError(t, err, "error generating random digits as mrn")
			id := ksuid.New().String()
			deviceId := ksuid.New().String()
			accntId := ksuid.New().String()

			// set up new consent
			_, err = db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email) VALUES (?, ?, ?, ?)`,
				id,
				1,
				mrn,
				email,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
				db.Exec("DELETE FROM enrollments where email=?", email)
			})

			// set up mock
			mockCalled := false
			createAccountMock := func(_ context.Context, _ acctSvc.AccountService, _ *sql.DB, acctId string, emailAddr string, p coreapi.UserProfile, language string, deviceId string, acctSource string, ptSource string) (*accountservice.Account, string, bool, error) {

				mockCalled = true
				assert.Equal(t, email, emailAddr, "mock called with wrong email")
				assert.Equal(
					t,
					consent.FullName,
					fmt.Sprintf("%s %s", p.FirstName, p.LastName),
					"mocked called with wrong full name",
				)
				assert.Equal(
					t,
					accountservice.PATIENT_CREATION_CONSENT,
					ptSource,
					"mock called with wrong patient creation source",
				)
				return &acctSvc.Account{
					AccountId: accntId,
				}, "123", true, nil
			}
			service.getOrCreateAccount = createAccountMock
			service.acctSvcClient = &acctSvc.AcctSvcMock{LookUpAccountByEmailReturn: ""}

			_, err = service.PostProviderConsent(ctx, id, consent, deviceId)
			if err != nil &&
				err.Error() != "pdf maker failed" { // ignore pdf maker errors, covered in apptest
				t.Errorf("do not expect error but got: %q", err)
			}
			if !mockCalled {
				t.Error("expected createAccountOnConsent to be called")
			}
		},
	)

	t.Run(
		"post consent -- good consent id opting out shouldn't call createAccount ",
		func(t *testing.T) {
			consent := coreapi.Consent{
				Opt:          "out",
				FullName:     "Person One",
				SignatureImg: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
			}
			ctx := context.Background()
			email := "gr4ce+" + time.Now().String() + "@example.com"
			mrn := "11111"
			id := "integ_test_id_" + time.Now().String()

			// set up new consent
			_, err := db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email) VALUES (?, ?, ?, ?)`,
				id, 1, mrn, email,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
			})

			// set up mock
			mockCalled := false
			getOrCreate := func(_ context.Context, _ acctSvc.AccountService, _ *sql.DB, acctId string, emailAddr string, p coreapi.UserProfile, language string, deviceId string, acctSource string, ptSource string) (*accountservice.Account, string, bool, error) {
				mockCalled = true
				return &accountservice.Account{AccountId: "abc"}, "123", false, nil
			}
			service.getOrCreateAccount = getOrCreate
			service.acctSvcClient = &acctSvc.AcctSvcMock{}

			_, err = service.PostProviderConsent(ctx, id, consent, "deviceId")
			if err != nil &&
				err.Error() != "pdf maker failed" { // ignore pdf maker errors, covered in apptest
				t.Errorf("do not expect error but got: %q", err)
			}
			if mockCalled {
				t.Error("expected getOrCreate account to not be called")
			}
		},
	)

	t.Run("verify consent -- correct date of birth returns valid",
		func(t *testing.T) {
			ctx := context.Background()
			email := "gr4ce+" + time.Now().String() + "@example.com"
			mrn := "11111"
			id := "integ_test_id_" + time.Now().String()
			dob := "********"
			parsedDoB, _ := time.Parse("********", dob)
			ip := "127.0.0.1"

			// set up new consent
			_, err := db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email, dob) VALUES (?, ?, ?, ?, ?)`,
				id,
				1,
				mrn,
				email,
				dob,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
			})

			result, err := service.VerifyProviderConsent(
				ctx,
				id,
				parsedDoB,
				ip,
			)

			resultId, decodeErr := auth.DecodeVerifiedConsentToken("Bearer " + result.Token)

			assert.NoError(t, err, "do not expect error but got: %q", err)
			assert.NotNil(t, result, "date of birth validation failed")
			assert.NoError(t, decodeErr)
			assert.Equal(t, id, resultId)
		},
	)

	t.Run("verify consent -- incorrect date of birth returns Not authorized error",
		func(t *testing.T) {
			ctx := context.Background()
			email := "gr4ce+" + time.Now().String() + "@example.com"
			mrn := "11111"
			id := "integ_test_id_" + time.Now().String()
			dob := "********"
			parsedWrongDoB, _ := time.Parse("********", "19911003")
			ip := "127.0.0.1"

			// set up new consent
			_, err := db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email, dob) VALUES (?, ?, ?, ?, ?)`,
				id,
				1,
				mrn,
				email,
				dob,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
			})

			result, err := service.VerifyProviderConsent(
				ctx,
				id,
				parsedWrongDoB,
				ip,
			)

			assert.ErrorContains(
				t,
				err,
				errormsgs.ERR_NOT_AUTHORIZED,
				"expected error to be %q but it was %q",
				errormsgs.ERR_NOT_AUTHORIZED,
				err,
			)
			assert.Nil(t, result, "date of birth validation succeeded when it should have failed")
		},
	)

	t.Run("Is Appointment Reminder -- Non existent consent id", func(t *testing.T) {
		ctx := context.Background()
		result, err := service.IsAppointmentReminder(
			ctx,
			"7gs_6ITYtA32HSP-BGELpWWy6YjcxiItjjI645Qcabk=",
		)
		assert.False(t, result)
		assert.Error(t, err)
	})

	t.Run(
		"Is Appointment Reminder -- returns true if consent is appointment reminder",
		func(t *testing.T) {
			id := phutils.GenerateRandomString(t, 26)
			// set up new consent
			_, err := db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email, is_appointment_reminder) VALUES (?, ?, ?, ?, ?)`,
				id,
				1,
				phutils.GenerateRandomString(t, 7),
				phutils.GenerateRandomString(t, 12),
				true,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
			})

			ctx := context.Background()
			result, err := service.IsAppointmentReminder(ctx, id)

			assert.True(t, result)
			assert.NoError(t, err)
		},
	)

	t.Run(
		"Is Appointment Reminder -- returns false if consent is not appointment reminder",
		func(t *testing.T) {
			id := phutils.GenerateRandomString(t, 26)
			// set up new consent
			_, err := db.Exec(
				`INSERT INTO consents(id, org_id, mrn, email, is_appointment_reminder) VALUES (?, ?, ?, ?, ?)`,
				id,
				1,
				phutils.GenerateRandomString(t, 7),
				phutils.GenerateRandomString(t, 12),
				false,
			)
			if err != nil {
				t.Fatalf("unable to set up test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM consents WHERE id=?", id)
			})
			ctx := context.Background()
			result, err := service.IsAppointmentReminder(ctx, id)

			assert.False(t, result)
			assert.NoError(t, err)
		},
	)
}

func generateRandEmail(t *testing.T) string {
	t.Helper()
	randStr, err := random.GenerateRandomString(10)
	require.NoError(t, err, "error generating random string")
	return randStr + "@example.com"
}

func generateDateTimeStr(t *testing.T, format string) string {
	t.Helper()
	return time.Now().Format(format)
}
