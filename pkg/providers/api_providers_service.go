/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package providers

import (
	"context"
	"database/sql"
	"errors"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/orders"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	consents "gitlab.com/pockethealth/coreapi/pkg/mysql/consents"
	providers "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	acctsvc "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/coreapi/pkg/util/rollout"
	"gitlab.com/pockethealth/coreapi/pkg/util/script"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"

	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
)

// ProvidersApiService is a service that implents the logic for the ProvidersApiServicer
// This service should implement the business logic for every endpoint for the ProvidersApi API.
// Include any external packages or services that will be required by this service.
type ProvidersApiService struct {
	sqldb           *sql.DB
	containerClient azureUtils.ContainerClient
	i18nBundle      *i18n.Bundle
	acctSvcClient   acctsvc.AccountService
	planSvcClient   planservice.PlanService
	orgSvc          orgsvc.OrgService

	languageTagProviders languageproviders.LanguageTagProviders
	supportedLanguages   map[string]string

	getOrCreateAccount GetOrCreateAccountIds
	ampliClient        interfaces.AmplitudeEventClient
	experimentsClient  interfaces.AmplitudeExperimentClient
	cioEventProducer   interfaces.CIOProducerClient

	//service bus
	ServiceBusQueue pubsub.Queue

	provSvc providersservice.ProvidersService
}

// NewProvidersApiService creates a default api service
func NewProvidersApiService(
	db *sql.DB,
	cc azureUtils.ContainerClient,
	ib *i18n.Bundle,
	ltps languageproviders.LanguageTagProviders,
	sl map[string]string,
	acctSvcUser acctsvc.AccountService,
	planSvcClient planservice.PlanService,
	orgSvc orgsvc.OrgService,
	amplitudeClient interfaces.AmplitudeEventClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
	cioEventProducer interfaces.CIOProducerClient,
	serviceBusConnString string,
	serviceBusName string,
	provSvc providersservice.ProvidersService,
) coreapi.ProvidersApiServicer {
	return &ProvidersApiService{
		sqldb:                db,
		containerClient:      cc,
		i18nBundle:           ib,
		languageTagProviders: ltps,
		supportedLanguages:   sl,
		acctSvcClient:        acctSvcUser,
		planSvcClient:        planSvcClient,
		getOrCreateAccount:   accounts.GetOrCreateAccountIds,
		orgSvc:               orgSvc,
		ampliClient:          amplitudeClient,
		experimentsClient:    experimentsClient,
		cioEventProducer:     cioEventProducer,
		ServiceBusQueue:      azureUtils.NewServiceBusQueue(serviceBusConnString, serviceBusName),
		provSvc:              provSvc,
	}
}

type GetOrCreateAccountIds func(
	ctx context.Context,
	acctClient acctsvc.AccountService,
	db *sql.DB,
	acctId string,
	email string,
	prof coreapi.UserProfile,
	language string,
	deviceId string,
	acctCreationSource string,
	ptCreationSource string,
) (*acctsvc.Account, string, bool, error)

// GetProviderById - Get a provider
func (s *ProvidersApiService) GetProviderById(
	ctx context.Context,
	clinicId int64,
) (interface{}, error) {
	clinic, err := s.orgSvc.GetClinicByLegacyId(ctx, clinicId)
	if err != nil {
		return coreapi.Provider{}, err
	}

	provider := coreapi.Provider{
		ProviderId: clinic.LegacyId,
		Name:       clinic.Name,
		OrgName:    clinic.ProviderName,
		OrgId:      clinic.Provider.LegacyId,
		Address:    clinic.FullAddress,
		TaxName:    clinic.Provider.TaxName,
		Region:     clinic.Provider.Region,
		ClinicName: clinic.Name,
		Url:        clinic.Provider.Url,
	}
	//format alt urls string
	var trimmedString = strings.ReplaceAll(clinic.Provider.AltUrl, " ", "")
	provider.AltUrls = strings.Split(trimmedString, ",")
	return provider, nil
}

func (s *ProvidersApiService) GetProviderByUrl(
	ctx context.Context,
	url string,
) (interface{}, error) {
	return s.orgSvc.GetProviderByUrl(ctx, url)
}

// GetProviderFormConfig - Get request form config
func (s *ProvidersApiService) GetProviderFormConfig(
	ctx context.Context,
	providerId int64,
	inApp bool,
	deviceId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("providerId", providerId)
	form, err := providers.GetFormConfig(ctx, s.sqldb, providerId)
	if err != nil {
		lg.WithError(err).Error("failed to fetch legacy form config")
		return form, err
	}
	requestForm, isValid := form.(coreapi.RequestFormConfig)
	if isValid {
		formRepsonseV2, err := s.getFormByLegacyProviderId(
			ctx,
			requestForm.Provider.OrgId,
			inApp,
		)
		if err != nil {
			lg.WithError(err).Error("failed to fetch form config v2")
		} else {
			requestForm.FormResponseV2 = formRepsonseV2
			return requestForm, nil
		}
	}
	return form, nil
}

// GetProviderConfig - Get frontend configuration
func (s *ProvidersApiService) GetProviderConfig(
	ctx context.Context,
) (coreapi.ProviderConfig, error) {

	providerConfig, err := providers.GetProviderConfig(ctx, s.sqldb)
	if err != nil {
		logrus.WithError(err).Error("failed to query the database")
	}
	return providerConfig, err
}

// GetProviders - performs a search query for providers and clinics
func (s *ProvidersApiService) GetProviders(
	ctx context.Context,
	searchTerm string,
) ([]coreapi.Provider, error) {
	if rollout.Rollout(ctx, s.sqldb, "use_orgsvc_meilisearch") {
		searchProviders, err := s.getProvidersMeilisearch(ctx, searchTerm)
		if err != nil {
			return s.getProvidersFuzzySearch(ctx, searchTerm)
		}
		return searchProviders, nil
	}

	return s.getProvidersFuzzySearch(ctx, searchTerm)
}

func (s *ProvidersApiService) GetProviderConsentData(
	ctx context.Context,
	consentId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("consent_id", consentId)
	//get consent data
	consentData, err := s.getConsentData(ctx, consentId)
	if err != nil {
		lg.WithError(err).Info("error getting consent data")
		return coreapi.ConsentFormData{}, err
	}
	//get consent form data
	consentFormData, err := s.getConsentFormData(ctx, consentId, consentData)
	return consentFormData, err
}

func (s *ProvidersApiService) IsAppointmentReminder(
	ctx context.Context,
	consentId string,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("consent_id", consentId)
	//get consent data
	consentData, err := s.getConsentData(ctx, consentId)
	if err != nil {
		lg.WithError(err).Info("error getting consent data")
		return false, errors.New(errormsgs.ERR_NOT_FOUND)
	}
	return consentData.IsAppointmentReminder, err
}

func (s *ProvidersApiService) VerifyProviderConsent(
	ctx context.Context,
	consentId string,
	dateOfBirth time.Time,
	ip string,
) (*coreapi.VerifiedConsent, error) {
	consentUserInfo, err := consents.GetConsentUserInfo(ctx, s.sqldb, consentId)
	if err != nil {
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	// Date of birth not set in DB, return Not Found error
	if consentUserInfo.Dob.IsZero() {
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	if consentUserInfo.Dob.Equal(dateOfBirth) {
		token, err := auth.MakeVerifiedConsentToken(consentId, ip)
		if err != nil {
			return nil, err
		}

		isEnrolled, err := consents.IsEnrolled(ctx, s.sqldb, consentId)
		if err != nil {
			return nil, err
		}

		return &coreapi.VerifiedConsent{Token: token, IsEnrolled: isEnrolled}, nil
	}

	return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
}

func (s *ProvidersApiService) PostProviderConsent(
	ctx context.Context,
	consentId string,
	consent coreapi.Consent,
	deviceId string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("consent_id", consentId)

	//validate/get consent data
	consentData, err := s.getConsentData(ctx, consentId)
	if err != nil {
		lg.WithError(err).Info("error getting consent data")
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}
	lg.WithField("provider_id", consentData.ProviderId).Info("consent provider identifier")
	//check consent for opt out, update DB and return early
	if consent.Opt == "out" {
		//update db about opt-out status
		err := consents.OptOut(ctx, s.sqldb, consentId)
		if err != nil {
			lg.WithError(err).Info("error updating opt out in db")
		}
		return nil, nil
	}

	if consent.Opt == "in" {
		//get consent form data
		formData, err := s.getConsentFormData(ctx, consentId, consentData)
		if err != nil {
			lg.WithError(err).Info("error getting consent form data")
			return nil, errors.New(errormsgs.ERR_NOT_FOUND)
		}

		email := consent.Email
		consentUserInfo, err := consents.GetConsentUserInfo(ctx, s.sqldb, consentId)
		if err != nil {
			lg.WithError(err).Error("failed to get consent user info")
			return nil, err
		}
		if email == "" {
			email = consentUserInfo.Email
		}
		if consentUserInfo.FirstName == "" || consentUserInfo.LastName == "" {
			// appointment reminder consents have first/last/dob pre-populated
			// parse name to first/last name only if none are present
			consentUserInfo.FirstName, consentUserInfo.LastName = s.parsePatientNameFromConsent(
				consent.FullName,
			)
		}

		// prep UserProfile
		userProfile := coreapi.UserProfile{
			FirstName: consentUserInfo.FirstName,
			LastName:  consentUserInfo.LastName,
			Email:     email,
			Phone:     consentUserInfo.Phone,
		}
		if !consentUserInfo.Dob.IsZero() {
			// only pass in dob when it is pre-populated (appointment reminder consent)
			userProfile.DOB = consentUserInfo.Dob.Format(
				"01/02/2006",
			) // getOrCreateAccount() needs dob with slashes
		}

		acct, ptId, isNewAccount, err := s.getOrCreateAccount(
			ctx,
			s.acctSvcClient,
			s.sqldb,
			"",
			email,
			userProfile,
			"",
			"",
			acctsvc.PROVIDER_CONSENT,
			acctsvc.PATIENT_CREATION_CONSENT,
		)
		if err != nil {
			lg.WithError(err).Error("failed to get/create consent account")
			return nil, err
		}

		if isNewAccount {
			s.ampliClient.Track(amplitude.Event{
				EventType: "account created",
				UserID:    acct.AccountId,
				DeviceID:  deviceId,
				EventProperties: map[string]interface{}{
					"account_creation_source": "appointment reminders consent",
				},
			})
		}

		if consent.PaymentDetails.PlanId != 0 {
			err = orders.ProcessPayment(
				ctx,
				lg,
				s.acctSvcClient,
				s.planSvcClient,
				acct,
				consent.PaymentDetails,
				formData.ProviderId,
			)
			if err != nil {
				lg.WithError(err).Error("failed to process order")
				return nil, err
			}
		}

		// track event for enrollment account creation to amplitude
		s.logEnrollmentEvent(
			ctx,
			consentId,
			deviceId,
			acct.AccountId,
			formData.ProviderName,
			formData.AppointmentReminderId,
			formData.ConsentSource,
		)

		//update db about opt-in status
		enrollmentSuccess, err := consents.OptIn(
			ctx,
			s.sqldb,
			consentId,
			acct.AccountId,
			ptId,
			email,
		)
		if err != nil {
			lg.WithError(err).Error("error updating opt in in db")
			return nil, err
		}
		lg.Infof("consent enrollment created: %t", enrollmentSuccess)

		//publish provider data sync event if no error inserting enrollment record
		if enrollmentSuccess && s.ServiceBusQueue != nil {
			client, err := s.ServiceBusQueue.GetGlobalClient()
			if err != nil {
				lg.WithError(err).Info("service bus client is not valid")
			} else {
				data := orgsvc.SyncProvidersDataMessage{
					ProviderLegacyId: consentData.ProviderId,
					Type:             orgsvc.ProviderMetadataInfoType,
				}
				s.ServiceBusQueue.Publish(ctx, &data, "providerdatasync", client)
			}
		}

		lang := phlanguage.Select(ctx, s.supportedLanguages).
			WithOrganization(s.languageTagProviders.OrgId, consentData.ProviderId).
			GetLanguageTag()

		pdfJson := pdfs.CreateEnrollmentConsentPDFJson(
			ctx,
			s.i18nBundle,
			lang,
			time.Now(),
			formData,
			consent,
		)

		path := "vault/tmp/prep/" + consentId
		if _, err := os.Stat(path + "/CONSENT"); err != nil {
			err := os.MkdirAll(path+"/CONSENT", 0700)
			if err != nil {
				return nil, errors.New(errormsgs.ERR_OP_CONFLICT)
			}
		} else {
			err := os.RemoveAll(path + "/CONSENT")
			if err != nil {
				return nil, errors.New(errormsgs.ERR_OP_CONFLICT)
			}
			err = os.MkdirAll(path+"/CONSENT", 0700)
			if err != nil {
				return nil, errors.New(errormsgs.ERR_OP_CONFLICT)
			}
		}
		defer os.RemoveAll(path + "/CONSENT")

		bytebuf := []byte(pdfJson)

		err = ioutil.WriteFile(path+"/CONSENT/DESCRIPTOR", bytebuf, 0600)
		if err != nil {
			return nil, errors.New(errormsgs.ERR_COPY_FAIL)
		}

		err, stdErrStr := script.RunNodeScript(
			"jsScripts/pdfHeader.js",
			[]string{path + "/CONSENT/DESCRIPTOR"},
		)

		if err != nil {
			lg.Error("pdf maker failed: " + stdErrStr)
			return nil, errors.New("pdf maker failed")
		}

		finalFilePath := path + "/CONSENT/MAIN.pdf"
		file, err := os.ReadFile(finalFilePath) // #nosec G304
		if err != nil {
			return nil, errors.New(errormsgs.ERR_READ_FAIL)
		}
		err = azureUtils.UploadBlobBytes(ctx, s.containerClient.Consents, consentId, file)
		if err != nil {
			lg.WithError(err).
				WithField("file_size", len(file)).
				Error("failed to upload consent PDF")
			return nil, errors.New("unable to write file to storage")

		}

		return file, nil
	}

	return nil, nil
}

func (s *ProvidersApiService) PostProviderConsentVerification(
	ctx context.Context,
	consentId string,
	email string,
) (coreapi.ConsentEmailVerification, error) {
	lg := logutils.DebugCtxLogger(ctx)

	consentUserInfo, err := consents.GetConsentUserInfo(ctx, s.sqldb, consentId)
	if err != nil {
		lg.WithError(err).Error("failed to get consent user info")
		return coreapi.ConsentEmailVerification{}, err
	}
	if email == "" {
		email = consentUserInfo.Email
	}

	account, _, _, err := accounts.GetOrCreateAccountIds(
		ctx,
		s.acctSvcClient,
		s.sqldb,
		"",
		email,
		coreapi.UserProfile{
			FirstName: consentUserInfo.FirstName,
			LastName:  consentUserInfo.LastName,
			Phone:     consentUserInfo.Phone,
			DOB:       consentUserInfo.Dob.Format("01/02/2006"),
		},
		"",
		"",
		acctsvc.PROVIDER_CONSENT,
		acctsvc.PATIENT_CREATION_CONSENT,
	)
	if err != nil {
		lg.WithError(err).Error("unable get or create account")
		return coreapi.ConsentEmailVerification{}, err
	}

	token, err := s.acctSvcClient.PostAccountVerificationCode(ctx, account.AccountId)
	if err != nil {
		lg.WithError(err).
			Infof("unable to get token for account verification for accountId: %s", account.AccountId)
		return coreapi.ConsentEmailVerification{}, err
	}

	return coreapi.ConsentEmailVerification{Token: token, Email: email}, nil
}

func (s *ProvidersApiService) getConsentData(
	ctx context.Context,
	consentId string,
) (coreapi.ConsentData, error) {
	//get consent data
	return consents.GetConsentData(ctx, s.sqldb, consentId)
}

func (s *ProvidersApiService) getConsentFormData(
	ctx context.Context,
	consentId string,
	consentData coreapi.ConsentData,
) (coreapi.ConsentFormData, error) {
	var result coreapi.ConsentFormData

	//get/set appointment reminder data if needed
	if consentData.IsAppointmentReminder {
		consentAppointmentData, err := s.provSvc.GetConsentAppointmentReminderData(ctx, consentId)
		if err != nil {
			return coreapi.ConsentFormData{}, err
		}
		result.AppointmentReminderId = consentAppointmentData.CioTempAccountId
		// setting consent source
		result.ConsentSource = getConsentSourceForAppointment(
			consentAppointmentData.AppointmentCadence,
			consentAppointmentData.MessageType,
		)
	}
	//get consent form data provider name
	provider, err := s.orgSvc.GetProviderByLegacyId(ctx, consentData.ProviderId)
	if err != nil {
		return result, err
	}

	providerPlanIDs := []uint64{}
	for _, plan := range provider.Plans {
		providerPlanIDs = append(providerPlanIDs, plan.PlanId)
	}

	result.ProviderPlanIds = providerPlanIDs
	result.ProviderName = provider.Name
	result.ProviderId = consentData.ProviderId
	//validate consent data optIn, optOut and enrolled flags, set consent form values accordingly
	if !consentData.OptIn && !consentData.OptOut && !consentData.IsEnrolled {
		result.New = true //NOTE this is always triggered because consent enrolled column is never set anywhere, TODO: investigate why
	}
	if consentData.OptIn {
		result.Opt = "in"
	}
	if consentData.OptOut {
		result.Opt = "out"
	}
	//get/set consent form data text
	result.ConsentText = consents.GetConsentFormTextByProvider(ctx, s.sqldb, consentData.ProviderId)

	//get provider language tag
	lang := phlanguage.Select(ctx, s.supportedLanguages).
		WithOrganization(s.languageTagProviders.OrgId, consentData.ProviderId).
		GetLanguageTag()

	result.UnderstandItems = make([]string, 3)
	result.UnderstandItems[0] = phlanguage.LocalizeString(
		ctx,
		s.i18nBundle,
		&i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "ConsentItem0",
				Other: "My records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.",
			},
		},
		lang,
	)
	result.UnderstandItems[1] = phlanguage.LocalizeString(
		ctx,
		s.i18nBundle,
		&i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "ConsentItem1",
				Other: "My future records, if/when they are created, will also be made available to access and add to my account, at my discretion.",
			},
		},
		lang,
	)
	result.UnderstandItems[2] = phlanguage.LocalizeString(
		ctx,
		s.i18nBundle,
		&i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "ConsentItem2",
				Other: "I will be able to opt out of this online access at any time.",
			},
		},
		lang,
	)
	if result.ConsentText == "" {
		result.ConsentText = phlanguage.LocalizeString(
			ctx,
			s.i18nBundle,
			&i18n.LocalizeConfig{
				DefaultMessage: &i18n.Message{
					ID:    "ConsentText",
					Other: "I hereby waive all claims against {{.ProviderName}}, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.",
				},
				TemplateData: map[string]interface{}{
					"ProviderName": result.ProviderName,
				},
			},
			lang,
		)
	}

	return result, err

}

func (s *ProvidersApiService) getFormByLegacyProviderId(
	ctx context.Context,
	providerId int64,
	inApp bool,
) (orgsvc.FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	formResponse, err := s.orgSvc.GetFormByLegacyProviderId(ctx, providerId, inApp, "")
	if err != nil {
		lg.WithError(err).Error("error fetching form by legacy provider ID")
		return orgsvc.FormResponse{}, err
	}
	return formResponse, nil
}

func (s *ProvidersApiService) logEnrollmentEvent(
	ctx context.Context,
	consentId string,
	deviceId string,
	accountId string,
	orgName string,
	appointmentReminderId string,
	consentSource string,
) {
	lg := logutils.DebugCtxLogger(ctx)

	// identify newly created account
	s.ampliClient.Track(amplitude.Event{
		EventType: "$identify",
		UserID:    accountId,
		DeviceID:  deviceId,
		UserProperties: map[amplitude.IdentityOp]map[string]interface{}{
			"$set": {
				"appointment_reminder_id": appointmentReminderId,
			},
		},
	})

	// track enrollment event to amplitude
	s.ampliClient.Track(amplitude.Event{
		EventType: "enrollment completed",
		UserID:    accountId,
		DeviceID:  deviceId,
		EventProperties: map[string]interface{}{
			"consent_id":        consentId,
			"consent_source":    consentSource,
			"organization_name": orgName,
		},
	})

	// if this enrollment was the result of an appointment reminder, mark the patient in CIO
	// for some appointment-reminder specific campaign experimentation/targeting
	err := s.cioEventProducer.ProduceEvent(
		accountId,
		"consent_form_submitted",
		map[string]any{"consent_source": consentSource},
	)
	if err != nil {
		lg.WithError(err).Error("failed to produce appointment reminder consent cio event")
	}
}

func (s *ProvidersApiService) getProvidersMeilisearch(
	ctx context.Context,
	searchTerm string,
) ([]coreapi.Provider, error) {
	lg := logutils.DebugCtxLogger(ctx)
	searchProviders, err := s.orgSvc.GetSearchProviders(ctx, searchTerm)
	if err != nil {
		lg.WithError(err).Info("error getting search providers from orgsvc")
		return []coreapi.Provider{}, err
	}

	providers := []coreapi.Provider{}
	for _, searchProv := range searchProviders {
		provider := coreapi.Provider{
			ProviderId: searchProv.ClinicLegacyId,
			OrgId:      searchProv.ProviderLegacyId,
			Name:       searchProv.DisplayName,
			OrgName:    searchProv.ProviderName,
			ClinicName: searchProv.ClinicName,
			Address:    searchProv.Address,
			Url:        searchProv.ProviderUrl,
			Region:     searchProv.ProviderRegion,
		}
		if searchProv.ProviderAltUrls != "" {
			trimmedString := strings.ReplaceAll(searchProv.ProviderAltUrls, " ", "")
			provider.AltUrls = strings.Split(trimmedString, ",")
		}
		providers = append(providers, provider)
	}
	return providers, nil
}

func (s *ProvidersApiService) getProvidersFuzzySearch(
	ctx context.Context,
	searchTerm string,
) ([]coreapi.Provider, error) {
	//escape special characters to be able to use in sql query
	searchTerm = strings.Replace(searchTerm, ",", " ", -1)
	searchTerm = strings.Replace(searchTerm, "'", "\\'", -1)
	searchTerm = strings.Replace(searchTerm, "\u2018", "\\'", -1)
	searchTerm = strings.Replace(searchTerm, "\u2019", "\\'", -1)
	searchTerm = strings.Replace(searchTerm, "\u201C", "\\'", -1)
	searchTerm = strings.Replace(searchTerm, "\u201D", "\\'", -1)
	searchTerm = strings.Replace(searchTerm, "\"", "\\'", -1)
	searchTerms := strings.Fields(searchTerm)

	return providers.FuzzySearch(ctx, s.sqldb, searchTerms, searchTerm)
}

func getConsentSourceForAppointment(
	appointmentCadence int,
	appointmentMessageType string,
) (consentSource string) {
	return "APPOINTMENT_" + strings.ToUpper(
		appointmentMessageType,
	) + "_" + strconv.Itoa(
		appointmentCadence,
	)
}

func (s *ProvidersApiService) parsePatientNameFromConsent(
	fullName string,
) (firstName string, lastName string) {
	splitName := strings.Split(fullName, " ")
	firstName = strings.TrimSpace(strings.Join(splitName[:len(splitName)-1], " "))
	lastName = strings.TrimSpace(splitName[len(splitName)-1])
	return
}
