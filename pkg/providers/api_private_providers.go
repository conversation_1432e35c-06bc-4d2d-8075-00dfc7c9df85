package providers

import (
	"encoding/json"
	"io"
	"net/http"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
)

type PrivateProvidersApiController struct {
	service         coreapi.ProvidersApiServicer
	ampCookieHeader string
}

func NewPrivateProvidersApiController(
	s coreapi.ProvidersApiServicer,
	ampCookieHeader string,
) coreapi.PrivateProvidersApiRouter {
	return &PrivateProvidersApiController{
		service:         s,
		ampCookieHeader: ampCookieHeader,
	}
}

func (c *PrivateProvidersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetProviderConsentData",
			Method:      http.MethodGet,
			Pattern:     "/consents/{consentId}",
			HandlerFunc: c.GetProviderConsentData,
		},
		{
			Name:        "PostProviderConsent",
			Method:      http.MethodPost,
			Pattern:     "/consents/{consentId}",
			HandlerFunc: c.PostProviderConsent,
		},
		{
			Name:        "PostProviderConsentVerification",
			Method:      http.MethodPost,
			Pattern:     "/consents/{consentId}/email-verification",
			HandlerFunc: c.PostProviderConsentVerification,
		},
	}
}

func (c *PrivateProvidersApiController) GetPathPrefix() string {
	return "/v1/providers"
}

func (c *PrivateProvidersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth, verifyClaim}
}

// GetProviderConsentData - Get consent data for a consentid
func (c *PrivateProvidersApiController) GetProviderConsentData(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]

	result, err := c.service.GetProviderConsentData(r.Context(), consentId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostProviderConsent - Send consent data
func (c *PrivateProvidersApiController) PostProviderConsent(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]
	deviceId := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	consent := coreapi.Consent{}
	err := json.NewDecoder(r.Body).Decode(&consent)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	pdfBytes, err := c.service.PostProviderConsent(r.Context(), consentId, consent, deviceId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	w.Header().Set("Content-Type", "application/pdf")
	_, err = w.Write(pdfBytes)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
}

func (c *PrivateProvidersApiController) PostProviderConsentVerification(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]
	var email string
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "failed to read email body", http.StatusInternalServerError)
		return
	}
	defer r.Body.Close()
	email = string(body)

	result, err := c.service.PostProviderConsentVerification(r.Context(), consentId, email)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func verifyClaim(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		params := mux.Vars(r)
		consentId := params["consentId"]
		token := r.Header.Get("Authorization")

		claimedConsentId, err := auth.DecodeVerifiedConsentToken(token)
		if err != nil {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
			return
		} else if claimedConsentId != consentId {
			httperror.ErrorWithLog(w, r, errormsgs.ERR_BAD_TOKEN, http.StatusUnauthorized)
			return
		}

		next.ServeHTTP(w, r)
	})
}
