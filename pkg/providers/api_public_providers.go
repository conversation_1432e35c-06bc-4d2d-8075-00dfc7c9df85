/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package providers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
)

// A ProvidersApiController binds http requests to an api service and writes the service results to the http response
type PublicProvidersApiController struct {
	service         coreapi.ProvidersApiServicer
	ampCookieHeader string
}

type consentVerify struct {
	DateOfBirth string `json:"dateOfBirth"`
}

// NewProvidersApiController creates a default api controller
func NewPublicProvidersApiController(
	s coreapi.ProvidersApiServicer,
	ampCookieHeader string,
) coreapi.PublicProvidersApiRouter {
	return &PublicProvidersApiController{
		service:         s,
		ampCookieHeader: ampCookieHeader,
	}
}

// Routes returns all of the api route for the ProvidersApiController
func (c *PublicProvidersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetProviderConfig",
			Method:      http.MethodGet,
			Pattern:     "/providerConfig",
			HandlerFunc: c.GetProviderConfig,
		},
		{
			Name:        "GetProviderById",
			Method:      http.MethodGet,
			Pattern:     "/{providerId}",
			HandlerFunc: c.GetProviderById,
		},
		{
			Name:        "GetProviderByUrl",
			Method:      http.MethodGet,
			Pattern:     "/url/{url}",
			HandlerFunc: c.GetProviderByUrl,
		},
		{
			Name:        "GetProviderFormConfig",
			Method:      http.MethodGet,
			Pattern:     "/{providerId}/formConfig",
			HandlerFunc: c.GetProviderFormConfig,
		},
		{
			Name:        "GetProviderConsentData",
			Method:      http.MethodGet,
			Pattern:     "/consents/{consentId}/unverified",
			HandlerFunc: c.GetProviderConsentData,
		},
		{
			Name:        "PostProviderConsent",
			Method:      http.MethodPost,
			Pattern:     "/consents/{consentId}/unverified",
			HandlerFunc: c.PostProviderConsent,
		},
		{
			Name:        "GetProviderConsentType",
			Method:      http.MethodGet,
			Pattern:     "/consents/{consentId}/type",
			HandlerFunc: c.GetProviderConsentType,
		},
		{
			Name:        "VerifyProviderConsent",
			Method:      http.MethodPost,
			Pattern:     "/consents/{consentId}/verify",
			HandlerFunc: c.VerifyProviderConsent,
		},
		{
			Name:        "GetProviders",
			Method:      http.MethodGet,
			Pattern:     "",
			HandlerFunc: c.GetProviders,
		},
	}
}

func (c *PublicProvidersApiController) GetPathPrefix() string {
	return "/v1/providers"
}

func (c *PublicProvidersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{} // no middleware for providers, return empty list
}

// GetProviderById - Get a provider
func (c *PublicProvidersApiController) GetProviderById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	providerId, _ := coreapi.ParseIntParameter(params["providerId"])
	result, err := c.service.GetProviderById(r.Context(), providerId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetProviderById - Get a provider
func (c *PublicProvidersApiController) GetProviderByUrl(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	urlName := params["url"]
	result, err := c.service.GetProviderByUrl(r.Context(), urlName)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetProviderFormConfig - Get request form config
func (c *PublicProvidersApiController) GetProviderFormConfig(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	providerId, _ := coreapi.ParseIntParameter(params["providerId"])
	deviceId := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)
	query := r.URL.Query()
	inAppBooleanString := query.Get("inApp")
	var inApp bool
	var err error

	if inAppBooleanString != "" {
		inApp, err = strconv.ParseBool(inAppBooleanString)
		if err != nil {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusBadRequest),
				http.StatusBadRequest,
			)
			return
		}
	}

	result, err := c.service.GetProviderFormConfig(r.Context(), providerId, inApp, deviceId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetProviders - Get providers
func (c *PublicProvidersApiController) GetProviders(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()
	searchTerm := query.Get("searchTerm")
	if searchTerm == "" {
		httperror.ErrorWithLog(w, r, "invalid searchTerm", http.StatusBadRequest)
		return
	}
	result, err := c.service.GetProviders(r.Context(), searchTerm)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetProviderConfig - Get provider configuration
func (c *PublicProvidersApiController) GetProviderConfig(w http.ResponseWriter, r *http.Request) {
	result, err := c.service.GetProviderConfig(r.Context())
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PublicProvidersApiController) VerifyProviderConsent(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]
	ip := strings.Split(r.RemoteAddr, ":")[0]

	consentVerify := consentVerify{}
	err := json.NewDecoder(r.Body).Decode(&consentVerify)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	} else if len(consentVerify.DateOfBirth) == 0 {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		return
	}

	dob, err := time.Parse("20060102", consentVerify.DateOfBirth)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		return
	}

	result, err := c.service.VerifyProviderConsent(
		r.Context(),
		consentId,
		dob,
		ip,
	)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetProviderConsentType - Get consent type for a consentid
func (c *PublicProvidersApiController) GetProviderConsentType(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]

	result, err := c.service.IsAppointmentReminder(r.Context(), consentId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	response := coreapi.ConsentType{}
	if result {
		response.Verified = true
	} else {
		response.Verified = false
	}

	coreapi.EncodeJSONResponse(r.Context(), response, nil, w)
}

// GetProviderConsentData - Get consent data for a consentid
func (c *PublicProvidersApiController) GetProviderConsentData(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]

	// Don't allow appointment reminders to use this route
	isAppointmentReminder, err := c.service.IsAppointmentReminder(r.Context(), consentId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	if isAppointmentReminder {
		httperror.ErrorWithLog(
			w,
			r,
			errormsgs.ERR_DOB_VERIFICATION_REQUIRED,
			http.StatusUnauthorized,
		)
		return
	}

	result, err := c.service.GetProviderConsentData(r.Context(), consentId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostProviderConsent - Send consent data
func (c *PublicProvidersApiController) PostProviderConsent(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	consentId := params["consentId"]
	deviceId := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	// Don't allow appointment reminders to use this route
	isAppointmentReminder, err := c.service.IsAppointmentReminder(r.Context(), consentId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	if isAppointmentReminder {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	consent := coreapi.Consent{}
	err = json.NewDecoder(r.Body).Decode(&consent)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	pdfBytes, err := c.service.PostProviderConsent(r.Context(), consentId, consent, deviceId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	w.Header().Set("Content-Type", "application/pdf")
	_, err = w.Write(pdfBytes)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
}
