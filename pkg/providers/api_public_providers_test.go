package providers

import (
	"bytes"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
)

const (
	localClaimIp = "127.0.0.1"
)

type ControllerTestData struct {
	name                 string
	method               string
	url                  string
	body                 string // body as json string
	expectedStatus       int
	skipAuthorizedHeader bool
	serviceSetupFunc     func(service *mockcoreapi.MockProvidersApiServicer)
}

func TestFuzzySearch(t *testing.T) {
	t.Run("valid search", func(t *testing.T) {
		service := mockcoreapi.NewMockProvidersApiServicer(t)
		service.EXPECT().GetProviders(mock.Anything, mock.Anything).Return([]coreapi.Provider{}, nil)

		controller := NewPublicProvidersApiController(service, "testAmpCookieHeader")
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/providers?searchTerm=clinic", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("empty search", func(t *testing.T) {
		service := mockcoreapi.NewMockProvidersApiServicer(t)

		controller := NewPublicProvidersApiController(service, "testAmpCookieHeader")
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/providers?searchTerm=", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestVerifyProviderConsent(t *testing.T) {
	testcases := []ControllerTestData{
		{
			name:   "VerifyProviderConsent - valid request",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/verify",
			body: `{
				"dateOfBirth": "19900101"
			}`,
			expectedStatus: http.StatusOK,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().
					VerifyProviderConsent(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(&coreapi.VerifiedConsent{}, nil)
			},
		},
		{
			name:           "VerifyProviderConsent - empty body",
			method:         http.MethodPost,
			url:            "/v1/providers/consents/test-consent-id/verify",
			body:           `{}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "VerifyProviderConsent - bad date of birth",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/verify",
			body: `{
				"dateOfBirth": "1990-01-01T00:00:00.000Z"
			}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "VerifyProviderConsent - service did not find consent",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/verify",
			body: `{
				"dateOfBirth": "19900101"
			}`,
			expectedStatus: http.StatusNotFound,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().
					VerifyProviderConsent(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New(errormsgs.ERR_NOT_FOUND))
			},
		},
		{
			name:   "VerifyProviderConsent - verification did not match",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/verify",
			body: `{
				"dateOfBirth": "19900101"
			}`,
			expectedStatus: http.StatusUnauthorized,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().
					VerifyProviderConsent(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED))
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestGetProviderConsentData(t *testing.T) {
	testcases := []ControllerTestData{
		{
			name:           "GetProviderConsentData - valid request",
			method:         http.MethodGet,
			url:            "/v1/providers/consents/test-consent-id/unverified",
			expectedStatus: http.StatusOK,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().IsAppointmentReminder(mock.Anything, mock.Anything).Return(false, nil)
				service.EXPECT().
					GetProviderConsentData(mock.Anything, mock.Anything).
					Return(coreapi.ConsentFormData{}, nil)
			},
		},
		{
			name:           "GetProviderConsentData - is appointment reminder",
			method:         http.MethodGet,
			url:            "/v1/providers/consents/test-consent-id/unverified",
			expectedStatus: http.StatusUnauthorized, // because no DOB
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().IsAppointmentReminder(mock.Anything, mock.Anything).Return(true, nil)
			},
		},
		{
			name:           "GetProviderConsentData - service did not find consent",
			method:         http.MethodGet,
			url:            "/v1/providers/consents/test-consent-id/unverified",
			expectedStatus: http.StatusNotFound,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().
					IsAppointmentReminder(mock.Anything, mock.Anything).
					Return(false, errors.New(errormsgs.ERR_NOT_FOUND))
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestPostProviderConsent(t *testing.T) {
	testcases := []ControllerTestData{
		{
			name:   "PostProviderConsent - valid request",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/unverified",
			body: `{
				"opt": "in",
				"fullName": "random name",
				"signatureImg": "",
				"email": "<EMAIL>"
			}`,
			expectedStatus: http.StatusOK,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().IsAppointmentReminder(mock.Anything, mock.Anything).Return(false, nil)
				service.EXPECT().
					PostProviderConsent(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, nil)
			},
		},
		{
			name:   "PostProviderConsent - is appointment reminder",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/unverified",
			body: `{
				"opt": "in",
				"fullName": "random name",
				"signatureImg": "",
				"email": "<EMAIL>"
			}`,
			expectedStatus: http.StatusUnauthorized,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().IsAppointmentReminder(mock.Anything, mock.Anything).Return(true, nil)
			},
		},
		{
			name:   "PostProviderConsent - service did not find consent",
			method: http.MethodPost,
			url:    "/v1/providers/consents/test-consent-id/unverified",
			body: `{
				"opt": "in",
				"fullName": "random name",
				"signatureImg": "",
				"email": "<EMAIL>"
			}`,
			expectedStatus: http.StatusNotFound,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().
					IsAppointmentReminder(mock.Anything, mock.Anything).
					Return(false, errors.New(errormsgs.ERR_NOT_FOUND))
			},
		},
		{
			name:           "PostProviderConsent - bad request body",
			method:         http.MethodPost,
			url:            "/v1/providers/consents/test-consent-id/unverified",
			body:           `{"wrong": "body",}`,
			expectedStatus: http.StatusBadRequest,
			serviceSetupFunc: func(service *mockcoreapi.MockProvidersApiServicer) {
				service.EXPECT().IsAppointmentReminder(mock.Anything, mock.Anything).Return(false, nil)
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func setupController(t *testing.T) (coreapi.Router, *mockcoreapi.MockProvidersApiServicer) {
	service := mockcoreapi.NewMockProvidersApiServicer(t)
	controller := NewPublicProvidersApiController(
		service,
		"testAmpCookieHeader",
	)

	return controller, service
}

func runControllerTest(t *testing.T, data ControllerTestData) {
	// init controller and services
	controller, service := setupController(t)
	// call setup function
	if data.serviceSetupFunc != nil {
		data.serviceSetupFunc(service)
	}

	// set up controller
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// create request body
	buf := bytes.NewBuffer([]byte(data.body))

	// create request
	request, err := http.NewRequest(data.method, data.url, buf)
	if err != nil {
		t.Fatal(err)
	}

	// add auth header
	if !data.skipAuthorizedHeader {
		token := auth.MakeAccountAuthToken("2Xzj6jbsqdRaQYmqu0ynoebKVFw", localClaimIp)
		request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	// set up response recorder
	rr := httptest.NewRecorder()

	// process request
	router.ServeHTTP(rr, request)

	// check status code
	assert.Equal(t, data.expectedStatus, rr.Code)
}
