//go:build integration
// +build integration

package requests

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/testutil"

	"bytes"
	"net/http/httptest"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	incompleteRequestBlobs "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequestblobs"
	sqlOrgs "gitlab.com/pockethealth/coreapi/pkg/mysql/organizations"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"

	"io"
	"mime/multipart"
	"net/http"
	"os"
)

// Shared Configs/Data
const AZURE_DEV_BLOB = "phdevcentralcabloblrs"
const AZURE_REQUESTS_STORAGE = "requests"
const SECRET_KEY = "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18" // TODO: This should not be in a test file
const AMPLITUDE_TEST_HEADER = "testAmpCookieHeader"
const DEVICE_ID = "james123"
const NORMAL_DELEGATE_FILE = "../../apptest/gotests/assets/samplejpeg.jpeg" // any file < 5MB
const BIG_DELEGATE_FILE = "../../apptest/gotests/assets/test-image-3.png"   // any file >= 5MB

type incompleteRequestMultipartBodyParams struct {
	request              models.Request
	lastCompletedStep    models.RequestFormStep
	currentStep          *models.RequestFormStep
	numDelegatePhotoId   int
	isBigDelegatePhotoId bool
	numDelegForm         int
	isBigDelegForm       bool
}

type multipartFileParams struct {
	queryStr string
	filename string
	fileType string
	file     *os.File
}

// Utils
func setupContainerClient() azureUtils.ContainerClient {
	generalRetry := policy.RetryOptions{
		TryTimeout: 5 * time.Second,
	}

	requestsContainerURL, _ := azstorageauth.GetContainerClient(
		context.TODO(),
		AZURE_DEV_BLOB,
		AZURE_REQUESTS_STORAGE,
		&generalRetry,
	)

	return azureUtils.ContainerClient{
		Requests: requestsContainerURL,
	}
}

func setupService(db *sql.DB, containerClient azureUtils.ContainerClient) RequestsApiService {
	// setup auth token key
	auth.SetSecretKey([]byte(SECRET_KEY))

	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	cioProducer := &mocks.MockCIOProducerData{
		CallLogs: make([]map[string]any, 0),
	}

	// setup lang providers
	languageTagProviders := languageproviders.LanguageTagProviders{
		OrgId: sqlOrgs.GetOrgIdLanguageTagProvider(&orgservice.OrgServiceMock{}),
	}

	return RequestsApiService{
		sqldb:                db,
		hostName:             "localhost",
		acctSvcClient:        &accountservice.AcctSvcMock{},
		experimentsClient:    experimentsClient,
		cioEventProducer:     cioProducer,
		containerClient:      containerClient,
		orgSvcClient:         &orgservice.OrgServiceMock{ReturnDemoClinicData: true},
		languageTagProviders: languageTagProviders,
	}
}

func generateTestRequest() models.Request {
	// create test data
	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	email := fmt.Sprintf("<EMAIL>", rand)
	return models.Request{
		FirstName:  "Ada",
		ProviderId: 13,
		OrgId:      9,
		LastName:   "Lovelace",
		Dob:        "********",
		Email:      email,
	}
}

func generateCookie() *http.Cookie {
	return &http.Cookie{
		Name: AMPLITUDE_TEST_HEADER,
		Value: base64.StdEncoding.EncodeToString(
			[]byte(fmt.Sprintf("{\"DeviceID\": \"%s\"}", DEVICE_ID)),
		),
		MaxAge: 300,
	}
}

func getIncompleteRequestMultipartBody(
	t *testing.T,
	requestBodyParams incompleteRequestMultipartBodyParams,
) (*bytes.Buffer, *multipart.Writer) {
	incompleteRequestBytes, _ := json.Marshal(requestBodyParams.request)
	values := map[string]io.Reader{
		"requestData": bytes.NewReader(incompleteRequestBytes),
		"signatureImg": strings.NewReader(
			`data:image/png;base64,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`,
		),
		"lastCompletedStep": strings.NewReader(string(requestBodyParams.lastCompletedStep)),
		"currentStep":       strings.NewReader(string(requestBodyParams.lastCompletedStep)),
	}
	// Prepare a form
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	// add delegate photo id
	srcDelegateFile := NORMAL_DELEGATE_FILE
	if requestBodyParams.isBigDelegatePhotoId {
		srcDelegateFile = BIG_DELEGATE_FILE
	}
	delegateFile, err := os.OpenFile(srcDelegateFile, os.O_RDONLY, 0600)
	if err != nil {
		t.Fatalf("test setup failed: could not open file to send request: %v", err)
	}
	defer delegateFile.Close()
	for i := 0; i < requestBodyParams.numDelegatePhotoId; i++ {
		generateFormFileWithType(w, t, multipartFileParams{
			queryStr: fmt.Sprintf("delegatePhotoId[%d]", i),
			file:     delegateFile,
		})
	}

	// add auth document
	srcAuthFile := NORMAL_DELEGATE_FILE
	if requestBodyParams.isBigDelegForm {
		srcAuthFile = BIG_DELEGATE_FILE
	}
	authFile, err := os.OpenFile(srcAuthFile, os.O_RDONLY, 0600)
	if err != nil {
		t.Fatalf("test setup failed: could not open file to send request: %v", err)
	}
	defer authFile.Close()

	for i := 0; i < requestBodyParams.numDelegForm; i++ {
		generateFormFileWithType(w, t, multipartFileParams{
			queryStr: fmt.Sprintf("delegForm[%d]", i),
			file:     authFile,
		})
	}

	// add form data
	for key, r := range values {
		var fw io.Writer
		if x, ok := r.(io.Closer); ok {
			defer x.Close()
		}
		// Add other fields
		fw, _ = w.CreateFormField(key)
		io.Copy(fw, r)
	}
	// Don't forget to close the multipart writer.
	w.Close()
	return &b, w
}

func generateFormFileWithType(w *multipart.Writer, t *testing.T, fileParams multipartFileParams) {
	if fileParams.queryStr == "" {
		t.Fatalf("generateFormFileWithType test setup failed: no query string found")
	}
	if fileParams.file == nil {
		t.Fatalf("generateFormFileWithType test setup failed: no file found")
	}
	if fileParams.filename == "" {
		fileParams.filename = "samplejpeg.jpeg"
	}
	if fileParams.fileType == "" {
		fileParams.fileType = "image/jpeg"
	}

	fw, err := testutil.CreateFormFileWithType(
		w,
		fileParams.queryStr,
		fileParams.filename,
		fileParams.fileType,
	)
	if err != nil {
		t.Fatalf("generateFormFileWithType test setup failed: could not create form data header: %s", err)
	}

	_, err = io.Copy(fw, fileParams.file)
	if err != nil {
		t.Fatalf("generateFormFileWithType test setup failed: could not write file to form: %v", err)
	}

	return
}

func cleanup(db *sql.DB, incompleteReqId string, containerClient azureUtils.ContainerClient) func() {
	return func() {
		blobs, _ := incompleteRequestBlobs.GetBlobsByIncompleteRequestId(
			context.TODO(),
			db,
			incompleteReqId,
		)
		for _, blob := range blobs {
			azureUtils.DeleteBlob(context.TODO(), containerClient.Requests, blob.BlobId)
			db.Exec("DELETE FROM incomplete_request_blobs WHERE blob_id=?", blob.BlobId)
		}
		db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
	}
}

// Tests
func TestPostIncompleteRequestsV2(t *testing.T) {
	// setup services
	containerClient := setupContainerClient()
	db := testutils.SetupTestDB(t)
	service := setupService(db, containerClient)

	controller := PublicRequestsApiControllerV2{
		service:         &service,
		ampCookieHeader: AMPLITUDE_TEST_HEADER,
	}
	t.Run("should encrypt token correctly", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 1,
			numDelegForm:       3,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v1/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		// Check the status code is what we expect.
		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

		// Check the response body is what we expect.
		respBody := rr.Body.String()
		if !strings.Contains(respBody, "incompleteRequestId") {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
		var incompleteRequestInitResponse models.IncompleteRequestInitResponse
		err := json.Unmarshal([]byte(respBody), &incompleteRequestInitResponse)
		if err != nil {
			t.Errorf("could not properly unmarshal response body: %v", err.Error())
		}

		incompleteReqId := incompleteRequestInitResponse.IncompleteRequestId

		t.Cleanup(cleanup(db, incompleteReqId, containerClient))

		// assert db contains correct data
		var gotLastCompletedStep models.RequestFormStep
		var gotDataToken string
		err = db.QueryRow(
			"SELECT last_completed_step, data_token FROM incomplete_requests WHERE id=?",
			incompleteReqId,
		).Scan(&gotLastCompletedStep, &gotDataToken)
		if err != nil {
			t.Errorf("error getting incomplete request from db: %q", err.Error())
		}

		// decode token
		encryptionKey := reqBodyParams.request.Dob
		jwtTok, err := auth.DecryptJWETwoPartKey(gotDataToken, encryptionKey)
		gotIncomplRequest, err := auth.DecodeIncomplReqDataToken(string(jwtTok))
		if err != nil {
			t.Fatalf("error decoding token: %q", err.Error())
		}

		if reqBodyParams.lastCompletedStep != gotLastCompletedStep {
			t.Errorf("expected %s, got %s", string(reqBodyParams.lastCompletedStep), string(gotLastCompletedStep))
		}
		if reqBodyParams.request.Mrn != gotIncomplRequest.RequestData.Mrn {
			t.Errorf("expected %s, got %s", reqBodyParams.request.Mrn, gotIncomplRequest.RequestData.Mrn)
		}
	})

	t.Run("should error if a delegate photo id file is too large", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:              generateTestRequest(),
			lastCompletedStep:    models.RequestStepHealthId,
			numDelegatePhotoId:   1,
			isBigDelegatePhotoId: true,
			numDelegForm:         1,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v1/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusBadRequest {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusBadRequest)
		}

		// Check the response body is what we expect.
		respBody := rr.Body.String()
		if !strings.Contains(respBody, errormsgs.ERR_FILE_SIZE) {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
	})

	t.Run("should error if there are more than 1 delegate photo id files", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 2,
			numDelegForm:       1,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v1/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusBadRequest {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusBadRequest)
		}

		// Check the response body is what we expect.
		respBody := rr.Body.String()
		if !strings.Contains(respBody, errormsgs.ERR_NUMBER_OF_FILES) {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
	})

	t.Run("should error if a delegate form file is too large", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 1,
			numDelegForm:       1,
			isBigDelegForm:     true,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v1/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusBadRequest {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusBadRequest)
		}

		// Check the response body is what we expect.
		respBody := rr.Body.String()
		if !strings.Contains(respBody, errormsgs.ERR_FILE_SIZE) {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
	})

	t.Run("should error if there are more than 3 delegate form files", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 1,
			numDelegForm:       4,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v1/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusBadRequest {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusBadRequest)
		}

		// Check the response body is what we expect.
		respBody := rr.Body.String()
		if !strings.Contains(respBody, errormsgs.ERR_NUMBER_OF_FILES) {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
	})
}

func TestPutIncompleteRequestsV2(t *testing.T) {
	// setup services
	containerClient := setupContainerClient()
	db := testutils.SetupTestDB(t)
	service := setupService(db, containerClient)

	controller := PublicRequestsApiControllerV2{
		service:         &service,
		ampCookieHeader: AMPLITUDE_TEST_HEADER,
	}
	t.Run("should encrypt token correctly", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 1,
			numDelegForm:       1,
		}

		cookie := generateCookie()

		postReqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v2/requests/incomplete", postReqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		// Check the status code is what we expect.
		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}
		responseBody := rr.Body.String()
		if !strings.Contains(responseBody, "incompleteRequestId") {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
		var incompleteRequestInitResponse models.IncompleteRequestInitResponse
		_ = json.Unmarshal([]byte(responseBody), &incompleteRequestInitResponse)

		incompleteReqId := incompleteRequestInitResponse.IncompleteRequestId

		t.Cleanup(cleanup(db, incompleteReqId, containerClient))

		putReqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ = http.NewRequest(
			"PUT",
			fmt.Sprintf(
				"/incomplete/%s/v2",
				incompleteReqId,
			),
			putReqBody,
		)
		req = mux.SetURLVars(req, map[string]string{"id": incompleteReqId})
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.Header.Set("Authorization", "Bearer "+incompleteRequestInitResponse.AuthToken)
		req.AddCookie(cookie)
		rr = httptest.NewRecorder()
		handler = http.HandlerFunc(controller.PutIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		// Check the status code is what we expect.
		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

	})
}

func TestPostIncompleteRequestVerifyV2(t *testing.T) {
	// setup services
	containerClient := setupContainerClient()
	db := testutils.SetupTestDB(t)
	service := setupService(db, containerClient)

	controller := PublicRequestsApiControllerV2{
		service:         &service,
		ampCookieHeader: AMPLITUDE_TEST_HEADER,
	}
	t.Run("when valid dob, should return decrypted token data", func(t *testing.T) {
		// create test data
		reqBodyParams := incompleteRequestMultipartBodyParams{
			request:            generateTestRequest(),
			lastCompletedStep:  models.RequestStepHealthId,
			numDelegatePhotoId: 1,
			numDelegForm:       1,
		}

		cookie := generateCookie()

		reqBody, w := getIncompleteRequestMultipartBody(t, reqBodyParams)
		req, _ := http.NewRequest("POST", "/v2/requests/incomplete", reqBody)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.AddCookie(cookie)
		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(controller.PostIncompleteRequestsV2)

		// Our handlers satisfy http.Handler, so we can call their ServeHTTP method
		// directly and pass in our Request and ResponseRecorder.
		handler.ServeHTTP(rr, req)

		// Check the status code is what we expect.
		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}
		responseBody := rr.Body.String()
		if !strings.Contains(responseBody, "incompleteRequestId") {
			t.Errorf("handler returned unexpected body: got: %v",
				rr.Body.String())
		}
		var incompleteRequestInitResponse models.IncompleteRequestInitResponse
		_ = json.Unmarshal([]byte(responseBody), &incompleteRequestInitResponse)

		incompleteReqId := incompleteRequestInitResponse.IncompleteRequestId

		t.Cleanup(cleanup(db, incompleteReqId, containerClient))

		// act
		res, err := service.PostIncompleteRequestVerifyV2(
			context.Background(),
			incompleteReqId,
			reqBodyParams.request.Dob,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		// assert
		if reqBodyParams.lastCompletedStep != res.LastCompletedStep {
			t.Errorf(
				"expected %s, got %s",
				string(reqBodyParams.lastCompletedStep),
				string(res.LastCompletedStep),
			)
		}
		if reqBodyParams.request.Email != res.RequestData.Email {
			t.Errorf(
				"expected %s, got %s",
				reqBodyParams.request.Email,
				res.RequestData.Email,
			)
		}
		if reqBodyParams.request.FirstName != res.RequestData.FirstName {
			t.Errorf(
				"expected %s, got %s",
				reqBodyParams.request.FirstName,
				res.RequestData.FirstName,
			)
		}
		if res.SignatureImg == "" {
			t.Error(
				"expected non-empty signature",
			)
		}
		if len(res.DelegateForms) != 1 {
			t.Error(
				"expected non-empty delegate form",
			)
		}
		if !strings.HasPrefix(res.DelegateForms[0], "data:image/jpeg;base64,") {
			t.Error(
				"delegate form is missing content type prefix",
			)
		}
		if !strings.HasPrefix(res.DelegatePhoto, "data:image/jpeg;base64,") {
			t.Error(
				"delegate photo is missing content type prefix",
			)
		}
	})
}
