/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package requests

import (
	"encoding/json"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PublicRequestsApiController binds http requests to an api service and writes the service results to the http response
type PublicRequestsApiControllerV2 struct {
	service         coreapi.RequestsApiServicer
	lt              lockouttracker.LockoutTracker
	ampCookieHeader string
	rateLimiter     coreapi.RateLimiter
}

// NewUnauthRequestsApiController creates a default api controller
func NewPublicRequestsApiControllerV2(
	s coreapi.RequestsApiServicer,
	lockout lockouttracker.LockoutTracker,
	ampCookieHeader string,
) coreapi.PublicRequestsApiRouterV2 {
	return &PublicRequestsApiControllerV2{
		service:         s,
		lt:              lockout,
		ampCookieHeader: ampCookieHeader,
		rateLimiter: ratelimit.NewRateLimiter(
			5,   // period in minutes
			300, // max hits per period
		),
	}
}

// Routes returns all of the api route for the PublicRequestsApiController
func (c *PublicRequestsApiControllerV2) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostIncompleteRequestsV2",
			Method:      "POST",
			Pattern:     "/incomplete",
			HandlerFunc: c.PostIncompleteRequestsV2,
		},
		{
			Name:        "PutIncompleteRequestsV2",
			Method:      "PUT",
			Pattern:     "/incomplete/{id}",
			HandlerFunc: c.PutIncompleteRequestsV2,
		},
		{
			Name:        "PostIncompleteRequestVerifyV2",
			Method:      "POST",
			Pattern:     "/incomplete/{id}/verify",
			HandlerFunc: c.PostIncompleteRequestVerifyV2,
		},
		{
			Name:        "SubmitStep",
			Method:      "POST",
			Pattern:     "/submitStep",
			HandlerFunc: c.PostSubmitStep,
		},
		{
			Name:        "SubmitStep",
			Method:      "PUT",
			Pattern:     "/submitStep",
			HandlerFunc: c.PutSubmitStep,
		},
		{
			Name:        "PostCreateRequest",
			Method:      "POST",
			Pattern:     "/create",
			HandlerFunc: c.PostCreateRequest,
		},
		{
			Name:        "RequestStatusHistory",
			Method:      "GET",
			Pattern:     "/{rid}/status/history",
			HandlerFunc: c.GetRequestStatusHistory,
		},
	}
}
func (c *PublicRequestsApiControllerV2) GetPathPrefix() string {
	return "/v2/requests"
}

func (c *PublicRequestsApiControllerV2) GetMiddleware() [](func(http.Handler) http.Handler) {
	return [](func(http.Handler) http.Handler){c.rateLimiter}
}

// PostIncompleteRequests - Create a new incomplete request
func (c *PublicRequestsApiControllerV2) PostIncompleteRequestsV2(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	var signatureImg string
	var delegForm []*multipart.FileHeader
	var delegatePhotoId []*multipart.FileHeader
	request := &models.IncompleteRequest{}
	err := r.ParseMultipartForm(32 << 20)
	if err == nil {
		m := r.MultipartForm

		for key, files := range m.File {
			// Only get the first file per key since we only upload 1 file per key (e.g. delegForm[0], delegForm[1], delegForm[2] etc.)
			// That being said, if someone attempts to upload 2 files to a given key (i.e. delegForm[0]), then we will only parse the first one
			parsedFile := files[0]
			if parsedFile.Size > models.MaxIncompleteRequestFileUploadSize {
				httperror.ErrorWithLog(w, r, errmsg.ERR_FILE_SIZE, http.StatusBadRequest)
				return
			}

			if strings.Contains(key, "delegatePhotoId") {
				delegatePhotoId = append(delegatePhotoId, parsedFile)
			} else if strings.Contains(key, "delegForm") {
				delegForm = append(delegForm, parsedFile)
			}
		}

		if len(delegatePhotoId) > 1 || len(delegForm) > 3 {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NUMBER_OF_FILES, http.StatusBadRequest)
			return
		}

		requestData := r.FormValue("requestData")
		errReqJson := json.Unmarshal([]byte(requestData), &request.RequestData)
		if errReqJson != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
			return
		}
		request.LastCompletedStep = models.RequestFormStep(r.FormValue("lastCompletedStep"))
		request.CurrentStep = models.RequestFormStep(r.FormValue("currentStep"))
		signatureImg = r.FormValue("signatureImg")
	} else {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	res, err := c.service.PostIncompleteRequestsV2(
		r.Context(),
		*request,
		signatureImg,
		delegForm,
		delegatePhotoId,
		deviceID,
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

// PutIncompleteRequests - Update an incomplete request with new request data
func (c *PublicRequestsApiControllerV2) PutIncompleteRequestsV2(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	params := mux.Vars(r)
	requestId := params["id"]
	if requestId == "" {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	authTok := r.Header.Get("Authorization")
	if authTok == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	requestIdClaim, err := auth.DecodeIncompleteReqEmailToken(authTok)
	if err != nil || requestId != requestIdClaim {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	var signatureImg string
	var delegForm []*multipart.FileHeader
	var delegatePhotoId []*multipart.FileHeader
	request := &models.IncompleteRequest{}
	err = r.ParseMultipartForm(32 << 20)
	if err == nil {
		m := r.MultipartForm

		for key, files := range m.File {
			if strings.Contains(key, "delegatePhotoId") {
				delegatePhotoId = append(delegatePhotoId, files[0])
			} else {
				delegForm = append(delegForm, files[0])
			}

		}

		requestData := r.FormValue("requestData")
		request.LastCompletedStep = models.RequestFormStep(r.FormValue("lastCompletedStep"))
		request.CurrentStep = models.RequestFormStep(r.FormValue("currentStep"))
		errReqJson := json.Unmarshal([]byte(requestData), &request.RequestData)
		if errReqJson != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
			return
		}
		signatureImg = r.FormValue("signatureImg")
	} else {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PutIncompleteRequestsV2(
		r.Context(),
		requestId,
		*request,
		signatureImg,
		delegForm,
		delegatePhotoId,
		deviceID,
	)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
	}

	w.WriteHeader(http.StatusOK)
}

// PostIncompleteRequestVerifyV2 - Authenticate access to Incomplete Request and return request data
func (c *PublicRequestsApiControllerV2) PostIncompleteRequestVerifyV2(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	requestId := params["id"]
	if requestId == "" {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	verify := &models.IncompleteRequestVerify{}

	if err := json.NewDecoder(r.Body).Decode(&verify); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if verify.Dob == "" {
		httperror.ErrorWithLog(w, r, "missing dob field", http.StatusBadRequest)
	}

	if c.lt.IsLocked(r.Context(), requestId) {
		httperror.ErrorWithLog(w, r, errmsg.ERR_TOO_MANY_ATTEMPTS, http.StatusForbidden)
		return
	}

	res, err := c.service.PostIncompleteRequestVerifyV2(
		r.Context(),
		requestId,
		verify.Dob,
	)

	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else if err.Error() == errmsg.ERR_BAD_TOKEN {
			status = http.StatusBadRequest
		} else if err.Error() == errmsg.ERR_BAD_CREDENTIALS {
			c.lt.IncrementAttempts(r.Context(), requestId)
			status = http.StatusUnauthorized
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)

}

func (c *PublicRequestsApiControllerV2) PostSubmitStep(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	stepBody := orgs.StepValidationBody{}
	if err := json.NewDecoder(r.Body).Decode(&stepBody); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	res, err := c.service.PostSubmitStep(
		r.Context(),
		stepBody,
		deviceID,
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *PublicRequestsApiControllerV2) PutSubmitStep(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	stepBody := orgs.StepValidationBody{}
	if err := json.NewDecoder(r.Body).Decode(&stepBody); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if stepBody.RequestId == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	res, err := c.service.PutSubmitStep(
		r.Context(),
		stepBody,
		deviceID,
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *PublicRequestsApiControllerV2) PostCreateRequest(w http.ResponseWriter, r *http.Request) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	//get/set request form body items
	var requestBody models.Request
	var paymentDetails models.PaymentDetails
	inAppRequest := false

	lg := logutils.DebugCtxLogger(r.Context())

	language := r.Header.Get("Language")

	requestBodyStr := r.FormValue("requestBody")
	err := json.Unmarshal([]byte(requestBodyStr), &requestBody)
	if err != nil {
		lg.WithError(err).Info(errmsg.ERR_JSON_UNMARSHAL)
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_JSON_UNMARSHAL+" form requestBody",
			http.StatusBadRequest,
		)
		return
	}
	paymentDetailsStr := r.FormValue("paymentToken")
	err = json.Unmarshal([]byte(paymentDetailsStr), &paymentDetails)
	if err != nil {
		lg.WithError(err).Info(errmsg.ERR_JSON_UNMARSHAL)
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_JSON_UNMARSHAL+" form paymentToken",
			http.StatusBadRequest,
		)
		return
	}

	inApp := r.FormValue("inApp")
	err = json.Unmarshal([]byte(inApp), &inAppRequest)
	if err != nil {
		lg.WithError(err).Info(errmsg.ERR_JSON_UNMARSHAL)
	}
	signatureImg := r.FormValue("signatureImg")
	minorSignatureImg := r.FormValue("minorSignatureImg")

	//get/set form files
	var delegateForm []*multipart.FileHeader
	var delegatePhotoId []*multipart.FileHeader
	//TODO: to prevent abuse, consider something like https://medium.com/@owlwalks/dont-parse-everything-from-client-multipart-post-golang-9280d23cd4ad
	r.Body = http.MaxBytesReader(w, r.Body, 32<<20+1024)
	err = r.ParseMultipartForm(32 << 20)
	if err != nil {
		lg.WithError(err).Info("cannot parse form files")
		httperror.ErrorWithLog(w, r, "cannot parse form files", http.StatusBadRequest)
		return
	}

	m := r.MultipartForm
	for key, files := range m.File {
		if strings.Contains(key, "delegatePhotoId") {
			delegatePhotoId = append(delegatePhotoId, files[0])
		} else {
			delegateForm = append(delegateForm, files[0])
		}

	}
	//token is not required for this endpoint, but IF it is present, pass along the acct ID
	acctId := ""
	if tokenHeader := r.Header.Get("Authorization"); tokenHeader != "" && inAppRequest {
		acctId, err = auth.DecodeAccountToken(tokenHeader)
		if err != nil {
			lg.WithError(err).Info("cannot decode request account token from token header")
		}
	}
	//add new request
	createRequest := models.CreateRequest{
		RequestDetails:      requestBody,
		AccountId:           acctId,
		SignatureImage:      signatureImg,
		MinorSignatureImage: minorSignatureImg,
		PaymentDetails:      paymentDetails,
		DeviceId:            deviceID,
		DelegForm:           delegateForm,
		DelegPhotoId:        delegatePhotoId,
		InAppRequest:        inAppRequest,
	}
	createRequest.RequestDetails.NormalizeNames()

	pdf, id, verificationToken, idVerificationLink, err := c.service.AddRequest(
		r.Context(),
		createRequest,
		language,
	)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate request") {
			statusAccepted := http.StatusAccepted
			coreapi.EncodeJSONResponse(
				r.Context(),
				NewRequestResponse{pdf, id, verificationToken, idVerificationLink},
				&statusAccepted,
				w,
			)
			return
		}

		if err.Error() == errormsgs.ERR_DATA_NOT_VALID {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnprocessableEntity)
		} else {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	status := http.StatusCreated
	coreapi.EncodeJSONResponse(
		r.Context(),
		NewRequestResponse{pdf, id, verificationToken, idVerificationLink},
		&status,
		w,
	)
}

func (c *PublicRequestsApiControllerV2) GetRequestStatusHistory(
	w http.ResponseWriter,
	r *http.Request,
) {
	ctx := r.Context()
	lg := logutils.DebugCtxLogger(ctx)
	params := mux.Vars(r)

	requestId := params["rid"]
	if requestId == "" {
		lg.Info("cannot process get request status history: invalid param rid")
		httperror.ErrorWithLog(w, r, "invalid request identifier", http.StatusBadRequest)
		return
	}
	lg.WithField("request_id", requestId).Info("request init param")

	var status int
	result := c.service.GetRequestStatusHistory(ctx, requestId)
	status = http.StatusOK

	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}
