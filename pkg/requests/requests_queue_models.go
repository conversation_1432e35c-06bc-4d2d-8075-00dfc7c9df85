package requests

import "time"

type RequestStatusMessage struct {
	RequestId string    `json:"requestId"`
	Status    string    `json:"status"`
	Region    string    `json:"region"`
	Created   time.Time `json:"created"`
}

func (r *RequestStatusMessage) IsDefault() bool {
	return *r == RequestStatusMessage{}
}

type RequestCreatedEvent struct {
	RequestId  string    `json:"requestId"`
	AccountId  string    `json:"accountId"`
	ProviderId string    `json:"providerId"`
	ClinicId   string    `json:"clinicId"`
	Region     string    `json:"region"`
	Created    time.Time `json:"created"`
}

func (r *RequestCreatedEvent) IsDefault() bool {
	return *r == RequestCreatedEvent{}
}

type RequestEnrolmentExpiryEvent struct {
	RequestId   string                     `json:"requestId"`
	AccountId   string                     `json:"accountId"`
	EnrolId     string                     `json:"enrolId"`
	ProviderId  string                     `json:"providerId"`
	PatientId   string                     `json:"patientId"`
	DateOfBirth string                     `json:"dateOfBirth"`
	Source      EnrolmentExpiryEventSource `json:"source"`
	Region      string                     `json:"region"`
	Created     time.Time                  `json:"created"`
}

func (r *RequestEnrolmentExpiryEvent) IsDefault() bool {
	return *r == RequestEnrolmentExpiryEvent{}
}

type EnrolmentExpiryEventSource string

const (
	RequestEventSource   EnrolmentExpiryEventSource = "request"
	EnrolmentEventSource EnrolmentExpiryEventSource = "enrolment"
)
