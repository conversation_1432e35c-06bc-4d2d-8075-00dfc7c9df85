package requests

import (
	"strconv"
)

const (
	REQUEST_CREATED_EVENT_TYPE = "request_created"
)

type RequestEvent struct {
	RequestId        int64  `json:"request_id"`
	AccountId        string `json:"account_id"`
	ProviderId       int64  `json:"provider_id"`
	ClinicId         int64  `json:"clinic_id"`
	IsUph            bool   `json:"is_uph"`
	Region           string `json:"region"`
	EventType        string `json:"event_type"`
	TimestampSeconds int64  `json:"timestamp_seconds"`
}

func (r *RequestEvent) IsDefault() bool {
	return *r == RequestEvent{}
}

func (r *RequestEvent) ApplicationProperties() map[string]any {
	return map[string]any{
		"is_uph":     strconv.FormatBool(r.IsUph),
		"region":     r.Region,
		"event_type": r.EventType,
	}
}
