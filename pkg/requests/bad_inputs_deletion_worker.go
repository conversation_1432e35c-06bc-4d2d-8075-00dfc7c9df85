package requests

import (
	"context"
	"database/sql"
	"time"

	badInputs "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// We need to clear PHI for expired incomplete requests from our db.
// We're clearing this data async so we don't interfere with requests
func BadInputsDeletionWorker(
	ctx context.Context,
	db *sql.DB,
) {
	lg := logutils.DebugCtxLogger(ctx)
	for {
		err := badInputs.DeleteExpiredEntries(ctx, db)
		if err != nil {
			lg.WithError(err).Error("unable to get expired bad request input entries")
		}
		time.Sleep(time.Hour * 12)
	}
}
