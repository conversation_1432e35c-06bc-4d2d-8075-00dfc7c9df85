package requests

import "strconv"

type PatientEnrollmentEvent struct {
	CorrelationId string                   `json:"cor_id"`
	RequestId     int64                    `json:"request_id"`
	ProviderId    int64                    `json:"provider_id"` // legacy provider id
	Request       PatientEnrollmentRequest `json:"request"`
}

type PatientEnrollmentRequest struct {
	ID              int64  `json:"ID"`
	FirstName       string `json:"FirstName"`
	LastName        string `json:"LastName"`
	OHIP            string `json:"OHIP"`
	OHIPVersionCode string `json:"OHIPVersionCode"`
	SSN             string `json:"SSN"`
	IPN             string `json:"IPN"`
	MRN             string `json:"MRN"`
	AltID           string `json:"AltId"`
	DOB             string `json:"Dob"`
	Tel             string `json:"Tel"`
	Email           string `json:"Email"`
	Contents        string `json:"Contents"`
	AccountID       string `json:"account_id,omitempty"`
}

func (r *PatientEnrollmentEvent) IsDefault() bool {
	return *r == PatientEnrollmentEvent{}
}

func (r *PatientEnrollmentEvent) ApplicationProperties() map[string]any {
	return map[string]any{
		"providerId": strconv.FormatInt(r.ProviderId, 10),
	}
}
