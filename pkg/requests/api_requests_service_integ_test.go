//go:build integration
// +build integration

package requests

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/customerio/go-customerio/v3"
	_ "github.com/go-sql-driver/mysql"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	sqlOrgs "gitlab.com/pockethealth/coreapi/pkg/mysql/organizations"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

type testRequest struct {
	RequestID    int64
	FirstName    string
	LastName     string
	PatientID    string
	DOB          string
	AccountID    string
	MRN          string
	ScanID       string
	ClinicID     int64
	OrgID        int64
	ProviderName string
	ClinicName   string
}

func (tr testRequest) ToPendingRequest() models.PendingRequest {
	return models.PendingRequest{
		RequestId:    int32(tr.RequestID),
		FirstName:    tr.FirstName,
		LastName:     tr.LastName,
		PatientId:    tr.PatientID,
		ClinicId:     tr.ClinicID,
		DOB:          tr.DOB,
		Mrn:          tr.MRN,
		OrgId:        tr.OrgID,
		Status:       tr.ScanID,
		ProviderName: tr.ProviderName,
		ClinicName:   tr.ClinicName,
	}
}

func TestGetRequests(t *testing.T) {
	testCases := []struct {
		desc         string
		initMocks    func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string)
		initTestData func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, orgID int64) []models.PendingRequest
		testRequest  testRequest
	}{
		{
			desc: "returns pending requests for transfer studies",
			initMocks: func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string) {
				recordServiceMock.EXPECT().GetPatientStudiesWithUploadStatus(
					mock.Anything,
					mock.Anything,
				).Return([]models.RecordUploadStatus{}, nil)
			},
			initTestData: func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, orgID int64) []models.PendingRequest {
				// create transfers request
				transfersRequest := createAndInsertRequest(t, db, accountID, patientID, orgID)

				// return both pending request
				return []models.PendingRequest{
					transfersRequest.ToPendingRequest(),
				}
			},
		},
		{
			desc: "returns pending requests for transfer studies and record streaming studies with 0 ProgressPercent",
			initMocks: func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string) {
				recordServiceMock.EXPECT().GetPatientStudiesWithUploadStatus(
					mock.Anything,
					mock.Anything,
				).Return(
					[]models.RecordUploadStatus{
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        patientID,
							ProgressPercent:  0, // no upload progress
						},
					},
					nil,
				)
			},
			initTestData: func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, recordStreamingOrgID int64) []models.PendingRequest {
				// create transfers request
				transferOrgID := phtestutils.GenerateRandomInt64(t)
				transfersRequest := createAndInsertRequest(
					t,
					db,
					accountID,
					patientID,
					transferOrgID,
				)

				// create record streaming request
				recordStreamingRequest := createAndInsertRequest(
					t,
					db,
					accountID,
					patientID,
					recordStreamingOrgID,
				)

				// return both pending requests
				return []models.PendingRequest{
					transfersRequest.ToPendingRequest(),
					recordStreamingRequest.ToPendingRequest(),
				}
			},
		},
		{
			desc: "returns pending request for record streaming studies if no upload progress has been made",
			initMocks: func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string) {
				recordServiceMock.EXPECT().GetPatientStudiesWithUploadStatus(
					mock.Anything,
					mock.Anything,
				).Return(
					[]models.RecordUploadStatus{
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        patientID,
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  0, // no upload progress
						},
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        patientID,
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  0, // no upload progress
						},
					},
					nil,
				)
			},
			initTestData: func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, orgID int64) []models.PendingRequest {
				// create record streaming request
				testRequest := createAndInsertRequest(t, db, accountID, patientID, orgID)

				// return pending request since there is 0 ProgressPercent on all studies for provider
				return []models.PendingRequest{testRequest.ToPendingRequest()}
			},
		},
		{
			desc: "does not return pending request for record streaming studies if some upload progress has been made for the specified patientID",
			initMocks: func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string) {
				recordServiceMock.EXPECT().GetPatientStudiesWithUploadStatus(
					mock.Anything,
					mock.Anything,
				).Return(
					[]models.RecordUploadStatus{
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        patientID,
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  3, // some upload progress
						},
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        patientID,
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  0, // no upload progress
						},
					},
					nil,
				)
			},
			initTestData: func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, orgID int64) []models.PendingRequest {
				_ = createAndInsertRequest(t, db, accountID, patientID, orgID)

				// no pending requests returned since at least 1 study has ProgressPercent > 0
				return []models.PendingRequest{}
			},
		},
		{
			desc: "return pending request for record streaming studies if some upload progress has been made but not for specified patientID",
			initMocks: func(t *testing.T, recordServiceMock *mockrecordservice.MockRecordServiceClientInterface, orgID int64, patientID string) {
				recordServiceMock.EXPECT().GetPatientStudiesWithUploadStatus(
					mock.Anything,
					mock.Anything,
				).Return(
					[]models.RecordUploadStatus{
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        "mock-patientID",
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  3, // some upload progress
						},
						{
							LegacyProviderID: orgID,
							StudyUID:         phtestutils.GenerateRandomString(t, 10),
							PatientID:        "mock-patientID",
							ExamUUID:         phtestutils.GenerateRandomString(t, 10),
							ProgressPercent:  0, // no upload progress
						},
					},
					nil,
				)
			},
			initTestData: func(t *testing.T, ctx context.Context, db *sql.DB, accountID string, patientID string, orgID int64) []models.PendingRequest {
				// create record streaming request
				testRequest := createAndInsertRequest(t, db, accountID, patientID, orgID)
				return []models.PendingRequest{testRequest.ToPendingRequest()}
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			db := testutils.SetupTestDB(t)
			ctx := context.Background()

			// set up test data
			accountID := phtestutils.GenerateRandomString(t, 10)
			patientID := phtestutils.GenerateRandomString(t, 10)
			orgID := phtestutils.GenerateRandomInt64(t)

			var expectedRequests []models.PendingRequest
			if tc.initTestData != nil {
				expectedRequests = tc.initTestData(t, ctx, db, accountID, patientID, orgID)
			}

			// set up mocks
			orgServiceMock := &orgservice.OrgServiceMock{
				ReturnDemoClinicData: true,
			}
			recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
			if tc.initMocks != nil {
				tc.initMocks(t, recordServiceMock, orgID, patientID)
			}

			// create service
			s := RequestsApiService{
				sqldb:           db,
				orgSvcClient:    orgServiceMock,
				recordSvcClient: recordServiceMock,
			}

			// call GetRequests
			requests, err := s.GetRequests(ctx, accountID)
			require.NoError(t, err)

			// verify response and ignore generated timestamps
			diff := cmp.Diff(
				expectedRequests,
				requests,
				cmpopts.IgnoreFields(models.PendingRequest{}, "Date", "Timestamp"),
			)
			require.Empty(t, diff, "comparison diff was not empty")
		})
	}
}

func TestPostIncompleteRequests(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// setup auth token key
	secretKey := "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18"
	auth.SetSecretKey([]byte(secretKey))

	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	cioProducer := &mocks.MockCIOProducerData{
		make([]map[string]any, 0),
	}
	// setup service
	orgsvc := &orgservice.OrgServiceMock{
		FormConfigurationMap: orgservice.ConfigurationFieldMap{
			"ohip": orgservice.ConfigurationFieldInfo{Value: true},
		},
		ReturnDemoClinicData: true,
	}
	s := RequestsApiService{
		sqldb:             db,
		hostName:          "localhost",
		acctSvcClient:     &accountservice.AcctSvcMock{},
		experimentsClient: experimentsClient,
		cioEventProducer:  cioProducer,
		orgSvcClient:      orgsvc,
	}

	// setup lang providers
	languageTagProviders := languageproviders.LanguageTagProviders{
		OrgId: sqlOrgs.GetOrgIdLanguageTagProvider(orgsvc),
	}
	s.languageTagProviders = languageTagProviders

	// create test data
	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	lastCompletedStep := models.RequestStepHealthId
	email := fmt.Sprintf("<EMAIL>", rand)
	request := models.Request{
		FirstName:  "Ada",
		ProviderId: 13,
		OrgId:      9,
		LastName:   "Lovelace",
		Dob:        "********",
		Email:      email,
	}
	incompleteRequest := models.IncompleteRequest{
		RequestData:       request,
		LastCompletedStep: lastCompletedStep,
		CurrentStep:       lastCompletedStep,
	}
	deviceID := "james123"

	t.Run("should send cio events", func(t *testing.T) {
		// setup lang providers
		languageTagProviders := languageproviders.LanguageTagProviders{
			OrgId: sqlOrgs.GetOrgIdLanguageTagProvider(orgsvc),
		}
		s.languageTagProviders = languageTagProviders

		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		lastCompletedStep := models.RequestStepHealthId
		email := fmt.Sprintf("<EMAIL>", rand)
		request := models.Request{
			FirstName:  "Ada",
			ProviderId: 13,
			OrgId:      9,
			LastName:   "Lovelace",
			Dob:        "********",
			Email:      email,
			Mrn:        rand,
		}
		incompleteRequest := models.IncompleteRequest{
			RequestData:       request,
			LastCompletedStep: lastCompletedStep,
			CurrentStep:       lastCompletedStep,
		}

		// act
		initResp, err := s.PostIncompleteRequests(
			context.Background(),
			incompleteRequest,
			deviceID,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		incomplReqID := initResp.(models.IncompleteRequestInitResponse).IncompleteRequestId
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
				incomplReqID,
			)
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incomplReqID)
		})

		// assert CIO events sent
		if len(cioProducer.CallLogs) != 4 {
			t.Fatalf("expected 4 cio events produced but got %d", len(cioProducer.CallLogs))
		}

		// Event 1: Initialize CIO user
		firstCallLog := cioProducer.CallLogs[0]
		abandonedCartTreatment := firstCallLog["metadata"].(map[string]any)["abandoned_cart_treatment"].(map[string]any)
		emailParams := abandonedCartTreatment["email_params"].(map[string]any)
		if firstCallLog["method"] != "UpdateUserAttributes" {
			t.Fatalf(
				"expected method %s but got %s",
				"UpdateUserAttributes",
				firstCallLog["method"],
			)
		}
		if firstCallLog["acctId"] != email {
			t.Fatalf("expected acctId %s but got %s", email, firstCallLog["acctId"])
		}
		if firstCallLog["email"] != email {
			t.Fatalf("expected email %s but got %s", email, firstCallLog["email"])
		}
		if abandonedCartTreatment["treatment"] != "default" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"default",
				abandonedCartTreatment["treatment"],
			)
		}
		if abandonedCartTreatment["variant_name"] != "" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"",
				abandonedCartTreatment["variant_name"],
			)
		}
		if emailParams["org_name"] != "Demo Medical Imaging" {
			t.Fatalf(
				"expected provider name %s but got %s",
				"Demo Medical Imaging",
				emailParams["org_name"],
			)
		}
		if emailParams["device_id"] != deviceID {
			t.Fatalf(
				"expected device_id %s but got %s",
				deviceID,
				emailParams["device_id"],
			)
		}
		if emailParams["required_id_type"] != "OHIP" {
			t.Fatalf(
				"expected required_id_type %s but got %s",
				"ohip",
				emailParams["required_id_type"],
			)
		}

		// Event 2: request step INIT
		expectedCioEvent := map[string]any{
			"method":    "ProduceEvent",
			"acctId":    email,
			"eventName": "request_step_init",
		}
		eventAttributes := cioProducer.CallLogs[1]["eventAttributes"].(map[string]any)
		delete(cioProducer.CallLogs[1], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[1], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[1])
		}

		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		abandonedCartTreatment = eventAttributes["abandoned_cart_treatment"].(map[string]any)
		emailParams = abandonedCartTreatment["email_params"].(map[string]any)
		if abandonedCartTreatment["treatment"] != "default" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"default",
				abandonedCartTreatment["treatment"],
			)
		}
		if abandonedCartTreatment["variant_name"] != "" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"",
				abandonedCartTreatment["variant_name"],
			)
		}

		if emailParams["org_name"] != "Demo Medical Imaging" {
			t.Fatalf(
				"expected provider name %s but got %s",
				"Demo Medical Imaging",
				emailParams["org_name"],
			)
		}

		if emailParams["device_id"] != deviceID {
			t.Fatalf(
				"expected device_id %s but got %s",
				deviceID,
				emailParams["device_id"],
			)
		}

		// Event 3: request step PATIENT_INFO
		expectedCioEvent = map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_current_step_%s",
				strings.ToLower(string(lastCompletedStep)),
			),
		}
		eventAttributes = cioProducer.CallLogs[2]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[2], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[2], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[2])
		}

		// Event 4: request step PATIENT_INFO
		expectedCioEvent = map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_step_%s",
				strings.ToLower(string(lastCompletedStep)),
			),
		}
		eventAttributes = cioProducer.CallLogs[3]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[3], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[3], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[2])
		}
	})

	t.Run("should encrypt token correctly", func(t *testing.T) {
		// act
		initResp, err := s.PostIncompleteRequests(
			context.Background(),
			incompleteRequest,
			deviceID,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		incomplReqID := initResp.(models.IncompleteRequestInitResponse).IncompleteRequestId
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
				incomplReqID,
			)
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incomplReqID)
		})

		// assert db contains correct data
		var gotLastCompletedStep models.RequestFormStep
		var gotDataToken string
		err = db.QueryRow(
			"SELECT last_completed_step, data_token FROM incomplete_requests WHERE id=?",
			incomplReqID,
		).Scan(&gotLastCompletedStep, &gotDataToken)
		if err != nil {
			t.Errorf("error getting incomplete request from db: %q", err.Error())
		}

		// decode token
		encryptionKey := request.Dob
		jwtTok, err := auth.DecryptJWETwoPartKey(gotDataToken, encryptionKey)
		gotIncomplRequest, err := auth.DecodeIncomplReqDataToken(string(jwtTok))
		if err != nil {
			t.Fatalf("error decoding token: %q", err.Error())
		}

		if lastCompletedStep != gotLastCompletedStep {
			t.Errorf("expected %s, got %s", string(lastCompletedStep), string(gotLastCompletedStep))
		}
		if request.Mrn != gotIncomplRequest.RequestData.Mrn {
			t.Errorf("expected %s, got %s", request.Mrn, gotIncomplRequest.RequestData.Mrn)
		}
	})
}

func TestPostIncompleteRequestsSendCIOEmailCampaign(t *testing.T) {
	db := testutils.SetupTestDB(t)
	deviceID := "james123"

	// setup auth token key
	secretKey := "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18"
	auth.SetSecretKey([]byte(secretKey))

	cioProducer := &mocks.MockCIOProducerData{
		make([]map[string]any, 0),
	}
	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{
			"email-campaign-params-flag": {
				Value: "test",
				Payload: map[string]any{
					"experiment_name": "health_id_reminder",
				},
			},
			"health_id_reminder": {
				Value: "test_health_id_reminder",
				Payload: map[string]any{
					"exposure_request_steps": []any{
						models.RequestStepHealthId,
					},
					"email_params": map[string]any{"plan_id": 16},
				},
			},
		},
	}
	eventClient := &mocks.MockAmplitudeEventData{
		make([]amplitude.Event, 0),
	}
	// setup service
	s := RequestsApiService{
		sqldb:             db,
		hostName:          "localhost",
		acctSvcClient:     &accountservice.AcctSvcMock{},
		ampliClient:       eventClient,
		experimentsClient: experimentsClient,
		cioEventProducer:  cioProducer,
		orgSvcClient: &orgservice.OrgServiceMock{
			FormConfigurationMap: orgservice.ConfigurationFieldMap{
				"ohip": orgservice.ConfigurationFieldInfo{Value: true},
			},
			ReturnDemoClinicData: true,
		},
	}

	// setup lang providers
	languageTagProviders := languageproviders.LanguageTagProviders{
		OrgId: sqlOrgs.GetOrgIdLanguageTagProvider(&orgservice.OrgServiceMock{}),
	}
	s.languageTagProviders = languageTagProviders

	// create test data
	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	lastCompletedStep := models.RequestStepHealthId
	email := fmt.Sprintf("<EMAIL>", rand)
	providerId := int64(13)
	request := models.Request{
		FirstName:  "Ada",
		ProviderId: providerId,
		OrgId:      9,
		LastName:   "Lovelace",
		Dob:        "********",
		Email:      email,
	}
	incompleteRequest := models.IncompleteRequest{
		RequestData:       request,
		LastCompletedStep: lastCompletedStep,
		CurrentStep:       lastCompletedStep,
	}

	t.Run("should set CIO email campaign params", func(t *testing.T) {
		initResp, err := s.PostIncompleteRequests(
			context.Background(),
			incompleteRequest,
			deviceID,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		incomplReqID := initResp.(models.IncompleteRequestInitResponse).IncompleteRequestId
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
				incomplReqID,
			)
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incomplReqID)
		})

		// assert CIO events sent
		if len(cioProducer.CallLogs) != 4 {
			t.Fatalf("expected 4 cio events produced but got %d", len(cioProducer.CallLogs))
		}

		// Event 1: Initialize CIO user
		firstCallLog := cioProducer.CallLogs[0]
		metadata := firstCallLog["metadata"].(map[string]any)
		abandonedCartTreatment := metadata["abandoned_cart_treatment"].(map[string]any)
		language := metadata["language"].(string)
		emailParams := abandonedCartTreatment["email_params"].(map[string]any)
		if firstCallLog["method"] != "UpdateUserAttributes" {
			t.Fatalf(
				"expected method %s but got %s",
				"UpdateUserAttributes",
				firstCallLog["method"],
			)
		}
		if firstCallLog["acctId"] != email {
			t.Fatalf("expected acctId %s but got %s", email, firstCallLog["acctId"])
		}
		if firstCallLog["email"] != email {
			t.Fatalf("expected email %s but got %s", email, firstCallLog["email"])
		}
		if language != "en" {
			t.Fatalf("expected language en but got %s", language)
		}
		if abandonedCartTreatment["treatment"] != "health_id_reminder" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"health_id_reminder",
				abandonedCartTreatment["treatment"],
			)
		}
		if abandonedCartTreatment["variant_name"] != "test_health_id_reminder" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"test_health_id_reminder",
				abandonedCartTreatment["variant_name"],
			)
		}

		if emailParams["org_name"] != "Demo Medical Imaging" {
			t.Fatalf(
				"expected provider name %s but got %s",
				"Demo Medical Imaging",
				emailParams["org_name"],
			)
		}

		if emailParams["device_id"] != deviceID {
			t.Fatalf(
				"expected device_id %s but got %s",
				deviceID,
				emailParams["device_id"],
			)
		}
		if emailParams["required_id_type"] != "OHIP" {
			t.Fatalf(
				"expected required_id_type %s but got %s",
				"ohip",
				emailParams["required_id_type"],
			)
		}
		if emailParams["plan_id"] != 16 {
			t.Fatalf(
				"expected plan_id %d but got %d",
				16,
				emailParams["plan_id"],
			)
		}

		// Event 2: request step INIT
		expectedCioEvent := map[string]any{
			"method":    "ProduceEvent",
			"acctId":    email,
			"eventName": "request_step_init",
		}
		eventAttributes := cioProducer.CallLogs[1]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[1], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[1], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[2])
		}

		abandonedCartTreatment = eventAttributes["abandoned_cart_treatment"].(map[string]any)
		emailParams = abandonedCartTreatment["email_params"].(map[string]any)
		if abandonedCartTreatment["treatment"] != "health_id_reminder" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"health_id_reminder",
				abandonedCartTreatment["treatment"],
			)
		}
		if abandonedCartTreatment["variant_name"] != "test_health_id_reminder" {
			t.Fatalf(
				"expected treatment %s but got %s",
				"test_health_id_reminder",
				abandonedCartTreatment["variant_name"],
			)
		}
		if emailParams["org_name"] != "Demo Medical Imaging" {
			t.Fatalf(
				"expected provider name %s but got %s",
				"Demo Medical Imaging",
				emailParams["org_name"],
			)
		}
		if emailParams["device_id"] != deviceID {
			t.Fatalf(
				"expected device_id %s but got %s",
				deviceID,
				emailParams["device_id"],
			)
		}
		delete(cioProducer.CallLogs[1], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[1], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[1])
		}

		// Event 3: request current step PATIENT_INFO
		expectedCioEvent = map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_current_step_%s",
				strings.ToLower(string(lastCompletedStep)),
			),
		}
		eventAttributes = cioProducer.CallLogs[2]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[2], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[2], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[2])
		}

		// Event 4: request step PATIENT_INFO
		expectedCioEvent = map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_step_%s",
				strings.ToLower(string(lastCompletedStep)),
			),
		}
		eventAttributes = cioProducer.CallLogs[3]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[3], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[3], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[2])
		}

		expectedAmplitudeEvent := amplitude.Event{
			EventType: "$exposure",
			UserID:    "",
			DeviceID:  deviceID,
			// date in format YYYY-MM-DDThh:mm:ss e.g. 2022-08-17T15:36:42
			EventProperties: map[string]any{
				"flag_key": "health_id_reminder",
				"variant":  "test_health_id_reminder",
			},
		}
		if !reflect.DeepEqual(eventClient.EventLogs[0], expectedAmplitudeEvent) {
			t.Fatalf(
				"expected amplitude event %#v but got %#v",
				expectedAmplitudeEvent,
				eventClient.EventLogs[0],
			)
		}

		// Undo exposure event
		s.undoEmailCampaignExposure(
			context.TODO(), email, providerId, deviceID,
		)
		expectedAmplitudeEvent = amplitude.Event{
			EventType: "$exposure",
			UserID:    "",
			DeviceID:  deviceID,
			// date in format YYYY-MM-DDThh:mm:ss e.g. 2022-08-17T15:36:42
			EventProperties: map[string]any{
				"flag_key": "health_id_reminder",
				"variant":  "",
			},
		}
		if !reflect.DeepEqual(eventClient.EventLogs[1], expectedAmplitudeEvent) {
			t.Fatalf(
				"expected amplitude event %#v but got %#v",
				expectedAmplitudeEvent,
				eventClient.EventLogs[1],
			)
		}
	})
}

func TestPutIncompleteRequests(t *testing.T) {
	db := testutils.SetupTestDB(t)

	deviceID := "james123"

	// setup auth token key
	secretKey := "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18"
	auth.SetSecretKey([]byte(secretKey))

	cioProducer := &mocks.MockCIOProducerData{
		make([]map[string]any, 0),
	}
	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	s := RequestsApiService{
		sqldb:             db,
		acctSvcClient:     &accountservice.AcctSvcMock{},
		cioEventProducer:  cioProducer,
		experimentsClient: experimentsClient,
		orgSvcClient:      &orgservice.OrgServiceMock{},
	}

	t.Run("when incomplete request exists, should update data token", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()
		lastCompletedStep := models.RequestStepHealthId
		email := fmt.Sprintf("<EMAIL>", incompleteReqId)
		request := models.Request{
			FirstName: "Ada",
			LastName:  "Lovelace",
			Dob:       "********",
			Email:     email,
			Mrn:       "12345",
		}
		incompleteReq := models.IncompleteRequest{
			RequestData:       request,
			LastCompletedStep: lastCompletedStep,
			CurrentStep:       lastCompletedStep,
		}
		dataTok, err := s.encryptDataToken(context.TODO(), incompleteReq)
		if err != nil {
			t.Fatalf("unable to encrypt test data token")
		}
		// setup db entry
		_, err = db.Exec(
			`INSERT incomplete_requests
			SET id=?, last_completed_step=?, data_token=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 1 HOUR)`,
			incompleteReqId, lastCompletedStep, dataTok,
		)
		if err != nil {
			t.Fatalf("unable to setup test incomplete request: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
		})

		// update fields
		incompleteReq.LastCompletedStep = models.RequestStepComplete
		incompleteReq.CurrentStep = models.RequestStepComplete
		incompleteReq.RequestData.Mrn = "1000431282"

		// act
		err = s.PutIncompleteRequests(
			context.Background(),
			incompleteReqId,
			incompleteReq,
			deviceID,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		// assert
		var gotLastCompletedStep models.RequestFormStep
		var gotDataToken string
		var gotIncomplRequest models.IncompleteRequest
		err = db.QueryRow(
			"SELECT last_completed_step, data_token FROM incomplete_requests WHERE id=?",
			incompleteReqId,
		).Scan(&gotLastCompletedStep, &gotDataToken)
		if err != nil {
			t.Errorf("error getting incomplete request from db: %q", err.Error())
		}
		// assert CIO events sent
		if len(cioProducer.CallLogs) != 2 {
			t.Fatalf("expected 2 cio events produced but got %d", len(cioProducer.CallLogs))
		}
		// Event 1: Current step
		expectedCioEvent := map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_current_step_%s",
				strings.ToLower(string(models.RequestStepComplete)),
			),
		}
		eventAttributes := cioProducer.CallLogs[0]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[0], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[0], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[0])
		}
		// Event 2: Last step
		expectedCioEvent = map[string]any{
			"method": "ProduceEvent",
			"acctId": email,
			"eventName": fmt.Sprintf(
				"request_step_%s",
				strings.ToLower(string(models.RequestStepComplete)),
			),
		}
		eventAttributes = cioProducer.CallLogs[1]["eventAttributes"].(map[string]any)
		if _, ok := eventAttributes["incomplete_request_id"]; !ok {
			t.Fatal("expected eventAttributes to contain incomplete_request_id")
		}
		delete(cioProducer.CallLogs[1], "eventAttributes")
		if !reflect.DeepEqual(cioProducer.CallLogs[1], expectedCioEvent) {
			t.Fatalf("expected cio event %s but got %s", expectedCioEvent, cioProducer.CallLogs[1])
		}

		// decode token
		encryptionKey := request.Dob
		gotJwtTok, err := auth.DecryptJWETwoPartKey(gotDataToken, encryptionKey)
		gotIncomplRequest, err = auth.DecodeIncomplReqDataToken(string(gotJwtTok))
		if err != nil {
			t.Fatalf("error decoding token: %q", err.Error())
		}

		if incompleteReq.LastCompletedStep != gotLastCompletedStep {
			t.Errorf(
				"expected %s, got %s",
				string(incompleteReq.LastCompletedStep),
				string(gotLastCompletedStep),
			)
		}
		if incompleteReq.RequestData.Mrn != gotIncomplRequest.RequestData.Mrn {
			t.Errorf(
				"expected %s, got %s",
				incompleteReq.RequestData.Mrn,
				gotIncomplRequest.RequestData.Mrn,
			)
		}
	})

	t.Run("when no matching db entry exists, should return not found", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()
		lastCompletedStep := models.RequestStepHealthId
		request := models.Request{
			FirstName: "Ada",
			LastName:  "Lovelace",
			Dob:       "********",
			Email:     fmt.Sprintf("<EMAIL>", incompleteReqId),
		}
		incompleteReq := models.IncompleteRequest{
			RequestData:       request,
			LastCompletedStep: lastCompletedStep,
			CurrentStep:       lastCompletedStep,
		}

		err := s.PutIncompleteRequests(
			context.Background(),
			incompleteReqId,
			incompleteReq,
			deviceID,
		)
		if err == nil {
			t.Error("expected error, got none")
		} else if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Errorf("expected error %q, got %q", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})
}

func TestGetIncompleteRequestById(t *testing.T) {
	db := testutils.SetupTestDB(t)
	// deviceID := "james123"

	// setup auth token key
	secretKey := "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18"
	auth.SetSecretKey([]byte(secretKey))

	cioProducer := &mocks.MockCIOProducerData{
		make([]map[string]any, 0),
	}
	s := RequestsApiService{
		sqldb:            db,
		acctSvcClient:    &accountservice.AcctSvcMock{},
		orgSvcClient:     &orgservice.OrgServiceMock{ReturnDemoClinicData: true},
		cioEventProducer: cioProducer,
	}

	t.Run(
		"when incomplete request exists and is incomplete, should return correct metadata",
		func(t *testing.T) {
			incompleteReqId := ksuid.New().String()
			lastCompletedStep := models.RequestStepHealthId
			clinicId := 13 // Demo Medical Imaging in DB
			request := models.Request{
				Email:     fmt.Sprintf("<EMAIL>", incompleteReqId),
				Dob:       "********",
				FirstName: "Ada",
				LastName:  "Lovelace",
			}
			incompleteReq := models.IncompleteRequest{
				RequestData:       request,
				LastCompletedStep: lastCompletedStep,
				CurrentStep:       lastCompletedStep,
			}

			dataTok, err := s.encryptDataToken(context.TODO(), incompleteReq)
			if err != nil {
				t.Fatalf("unable to encrypt test data token")
			}
			// setup db entry
			_, err = db.Exec(
				`INSERT incomplete_requests
						SET id=?, last_completed_step=?, data_token=?, clinic_id=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 1 HOUR)`,
				incompleteReqId, lastCompletedStep, dataTok, clinicId,
			)
			if err != nil {
				t.Fatalf("unable to setup test incomplete request: %q", err.Error())
			}

			t.Cleanup(func() {
				db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
				db.Exec(
					"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
					incompleteReqId,
				)
			})

			// act
			res, err := s.GetIncompleteRequestById(context.Background(), incompleteReqId)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			// assert

			wantStatus := models.IncomplRequestIncomplete
			if wantStatus != res.(models.IncompleteRequestMetadata).Status {
				t.Errorf(
					"expected %s, got %s",
					wantStatus,
					res.(models.IncompleteRequestMetadata).ClinicName,
				)
			}
			wantClinicName := "Demo Medical Imaging"
			if wantClinicName != res.(models.IncompleteRequestMetadata).ClinicName {
				t.Errorf(
					"expected %s, got %s",
					wantClinicName,
					res.(models.IncompleteRequestMetadata).Status,
				)
			}
		},
	)

	t.Run(
		"when incomplete request exists but is completed and expired, should return completed",
		func(t *testing.T) {
			incompleteReqId := ksuid.New().String()
			lastCompletedStep := models.RequestStepComplete
			clinicId := 13 // Demo Medical Imaging in DB
			request := models.Request{
				FirstName: "Ada",
				LastName:  "Lovelace",
				Dob:       "********",
				Email:     fmt.Sprintf("<EMAIL>", incompleteReqId),
			}
			incompleteReq := models.IncompleteRequest{
				RequestData:       request,
				LastCompletedStep: lastCompletedStep,
				CurrentStep:       lastCompletedStep,
			}

			// setup request db entry
			res, err := db.Exec(
				`INSERT INTO requests(clinic_id, email, dob) VALUES (?, ?, ?)`,
				int64(clinicId), request.Email, request.Dob,
			)
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			requestId, err := res.LastInsertId()
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}

			dataTok, err := s.encryptDataToken(context.TODO(), incompleteReq)
			if err != nil {
				t.Fatalf("unable to encrypt test data token")
			}
			// setup incomplete request entry
			_, err = db.Exec(
				`INSERT incomplete_requests
					SET id=?, last_completed_step=?, data_token=?, clinic_id=?, request_id=?, expiry_timestamp=DATE_SUB(NOW(), INTERVAL 1 HOUR)`,
				incompleteReqId, lastCompletedStep, dataTok, clinicId, requestId,
			)
			if err != nil {
				t.Fatalf("unable to setup test incomplete request: %q", err.Error())
			}

			t.Cleanup(func() {
				// incomplete request must be deleted first b/c of foreign key constraint
				db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
				db.Exec("DELETE FROM requests WHERE id=?", requestId)
				db.Exec(
					"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
					incompleteReqId,
				)
			})

			// act
			resp, err := s.GetIncompleteRequestById(context.Background(), incompleteReqId)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			wantStatus := models.IncomplRequestCompleted
			if wantStatus != resp.(models.IncompleteRequestMetadata).Status {
				t.Errorf(
					"expected %s, got %s",
					wantStatus,
					resp.(models.IncompleteRequestMetadata).Status,
				)
			}
		},
	)
	t.Run(
		"when incomplete request exists but is expired, should return expired",
		func(t *testing.T) {
			incompleteReqId := ksuid.New().String()
			clinicId := 13 // Demo Medical Imaging in DB
			// setup incomplete request entry
			_, err := db.Exec(
				`INSERT incomplete_requests
				SET id=?, clinic_id=?, expiry_timestamp=DATE_SUB(NOW(), INTERVAL 1 HOUR)`,
				incompleteReqId, clinicId,
			)
			if err != nil {
				t.Fatalf("unable to setup test incomplete request: %q", err.Error())
			}

			t.Cleanup(func() {
				db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
				db.Exec(
					"DELETE FROM incomplete_request_experiment_groups WHERE incomplete_request_id=?",
					incompleteReqId,
				)
			})

			// act
			resp, err := s.GetIncompleteRequestById(context.Background(), incompleteReqId)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			wantStatus := models.IncomplRequestExpired
			if wantStatus != resp.(models.IncompleteRequestMetadata).Status {
				t.Errorf(
					"expected %s, got %s",
					wantStatus,
					resp.(models.IncompleteRequestMetadata).Status,
				)
			}
		},
	)
	t.Run("when no matching db entry exists, should return not found", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()

		_, err := s.GetIncompleteRequestById(context.Background(), incompleteReqId)
		if err == nil {
			t.Error("expected error, got none")
		} else if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Errorf("expected error %q, got %q", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})
}

func TestPostIncompleteRequestVerify(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// setup auth token key
	secretKey := "e66acaff8ca8f0d97c3e869b9501a0a4afc0484923693e76b5b6f2dac0ad6c18"
	auth.SetSecretKey([]byte(secretKey))

	cioProducer := &mocks.MockCIOProducerData{
		make([]map[string]any, 0),
	}
	s := RequestsApiService{
		sqldb:            db,
		acctSvcClient:    &accountservice.AcctSvcMock{},
		cioEventProducer: cioProducer,
		orgSvcClient:     &orgservice.OrgServiceMock{},
	}

	t.Run("when valid dob, should return decrypted token data", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()
		lastCompletedStep := models.RequestStepHealthId
		request := models.Request{
			FirstName: "Ada",
			LastName:  "Lovelace",
			Dob:       "********",
			Email:     fmt.Sprintf("<EMAIL>", incompleteReqId),
		}
		incompleteReq := models.IncompleteRequest{
			RequestData:       request,
			LastCompletedStep: lastCompletedStep,
			CurrentStep:       lastCompletedStep,
		}

		dataTok, err := s.encryptDataToken(context.TODO(), incompleteReq)
		if err != nil {
			t.Fatalf("unable to encrypt test data token: %q", err.Error())
		}
		// setup db entry
		_, err = db.Exec(
			`INSERT incomplete_requests
			SET id=?, last_completed_step=?, data_token=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 1 HOUR)`,
			incompleteReqId, lastCompletedStep, dataTok,
		)
		if err != nil {
			t.Fatalf("unable to setup test incomplete request: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
		})

		// act
		res, err := s.PostIncompleteRequestVerify(
			context.Background(),
			incompleteReqId,
			request.Dob,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		// assert
		if lastCompletedStep != res.(models.IncompleteRequestResponse).LastCompletedStep {
			t.Errorf(
				"expected %s, got %s",
				string(lastCompletedStep),
				string(res.(models.IncompleteRequestResponse).LastCompletedStep),
			)
		}
		if request.Email != res.(models.IncompleteRequestResponse).RequestData.Email {
			t.Errorf(
				"expected %s, got %s",
				request.Email,
				res.(models.IncompleteRequestResponse).RequestData.Email,
			)
		}
		if request.FirstName != res.(models.IncompleteRequestResponse).RequestData.FirstName {
			t.Errorf(
				"expected %s, got %s",
				request.FirstName,
				res.(models.IncompleteRequestResponse).RequestData.FirstName,
			)
		}
	})

	t.Run("when invalid dob, should return bad credentials", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()
		lastCompletedStep := models.RequestStepHealthId
		request := models.Request{
			FirstName: "Ada",
			LastName:  "Lovelace",
			Dob:       "********",
			Email:     fmt.Sprintf("<EMAIL>", incompleteReqId),
		}
		incompleteReq := models.IncompleteRequest{
			RequestData:       request,
			LastCompletedStep: lastCompletedStep,
			CurrentStep:       lastCompletedStep,
		}

		dataTok, err := s.encryptDataToken(context.TODO(), incompleteReq)
		if err != nil {
			t.Fatalf("unable to encrypt test data token")
		}
		// setup db entry
		_, err = db.Exec(
			`INSERT incomplete_requests
			SET id=?, last_completed_step=?, data_token=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 1 HOUR)`,
			incompleteReqId, lastCompletedStep, dataTok,
		)
		if err != nil {
			t.Fatalf("unable to setup test incomplete request: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteReqId)
		})

		// act
		_, err = s.PostIncompleteRequestVerify(
			context.Background(),
			incompleteReqId,
			"10101010",
		)
		if err == nil {
			t.Error("expected error, got none")
		} else if err.Error() != errormsgs.ERR_BAD_CREDENTIALS {
			t.Errorf("expected error %q, got %q", errormsgs.ERR_BAD_CREDENTIALS, err.Error())
		}
	})

	t.Run("when no matching db entry exists, should return not found", func(t *testing.T) {
		incompleteReqId := ksuid.New().String()
		dob := "20001010"

		_, err := s.PostIncompleteRequestVerify(
			context.Background(),
			incompleteReqId,
			dob,
		)
		if err == nil {
			t.Error("expected error, got none")
		} else if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Errorf("expected error %q, got %q", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})
}

// TODO to be refactored
func TestPostVerifyRejectedEmail(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// create a mock email sender
	serviceBus := azureUtils.NewServiceBusQueue("testconnection", "sb-ph-qa")
	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	mailer, mockclient := cioEmail.NewMock()
	mockmail := cio_email.ConfigureMail(mailer, map[string]string{"pvr_px": "21"})

	// setup api service
	s := RequestsApiService{
		sqldb:               db,
		acctSvcClient:       &accountservice.AcctSvcMock{},
		orgSvcClient:        &orgservice.OrgServiceMock{ReturnDemoClinicData: true},
		ServiceBusQueue:     serviceBus,
		experimentsClient:   experimentsClient,
		cioEmailer:          mockmail,
		supportEmailAddress: "<EMAIL>",
	}

	t.Run("when provider confirmed, should send email in OrgConfirmed format", func(t *testing.T) {
		clinicId := 13
		scanId := "REJECTED"
		acctId := "ABCDEFGH"
		request := models.Request{
			FirstName: "Ada",
			LastName:  "Lovelace",
			Dob:       "********",
			Email:     "<EMAIL>",
		}
		rejectVerifyDetails := coreapi.RejectVerifyDetails{
			RecentExamDate:    "1999-01-01",
			HasOlderExams:     false,
			ProviderConfirmed: true,
			ExamTypes:         []string{"abc", "def"},
			Comments:          "abcdef",
		}

		// setup db entry
		requestId := insertRequest(t, db, map[string]interface{}{
			"clinic_id":        clinicId,
			"email":            request.Email,
			"first_name":       request.FirstName,
			"last_name":        request.LastName,
			"dob":              request.Dob,
			"scan_id":          scanId,
			"account_id":       acctId,
			"rejection_reason": 2,
		})

		// act
		err := s.PostVerifyRejectedEmail(
			context.Background(),
			acctId,
			requestId,
			rejectVerifyDetails,
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
			t.FailNow()
		}

		// assert
		logs := mockclient.CallLogs.Get("SendEmail")
		require.True(t, logs.CalledOnce())
		call := logs.History[0]
		p, ok := call.Params.(map[string]interface{})
		require.True(t, ok, "expected params to be in map[string]interface format")
		iMailReq := p["req"]

		mailReq, ok := iMailReq.(*customerio.SendEmailRequest)
		assert.True(t, ok)

		expectedData := map[string]interface{}{
			"comments":                "abcdef",
			"exam_types":              []string{"abc", "def"},
			"location_change":         false,
			"most_recent_exam_date":   "1999-01-01",
			"new_clinic_address":      "",
			"new_clinic_name":         "",
			"new_org_found":           false,
			"new_org_name":            "",
			"new_provider_text":       "",
			"org_confirmed":           true,
			"org_id":                  int64(9),
			"original_clinic_address": "",
			"original_clinic_name":    "Demo Medical Imaging",
			"original_org_name":       "Demo Medical Imaging",
			"patient_email":           "<EMAIL>",
			"priors":                  false,
			"rejection_reason":        "INTERNAL_ERROR",
			"request_id":              requestId,
			"unlimited":               false,
		}
		assert.Equal(t, expectedData, mailReq.MessageData)
	})
}

func createAndInsertRequest(
	t *testing.T,
	db *sql.DB,
	accountID string,
	patientID string,
	orgID int64,
) testRequest {
	t.Helper()
	req := testRequest{
		FirstName:    "Ada",
		LastName:     "Lovelace",
		PatientID:    patientID,
		DOB:          "********",
		AccountID:    accountID,
		MRN:          phtestutils.GenerateRandomString(t, 10),
		ScanID:       "SUPPRESS",
		ClinicID:     phtestutils.GenerateRandomInt64(t),
		OrgID:        orgID,
		ProviderName: "Demo Medical Imaging", // matches hardcoded return value from orgservice mock
		ClinicName:   "Demo Medical Imaging", // matches hardcoded return value from orgservice mock
	}
	reqID := insertRequest(t, db, map[string]interface{}{
		"first_name":         req.FirstName,
		"last_name":          req.LastName,
		"patient_id":         req.PatientID,
		"dob":                req.DOB,
		"account_id":         req.AccountID,
		"mrn":                req.MRN,
		"scan_id":            req.ScanID,
		"clinic_id":          req.ClinicID,
		"legacy_provider_id": req.OrgID,
	})
	req.RequestID = reqID
	return req
}

func insertRequest(t *testing.T, db *sql.DB, columns map[string]interface{}) int64 {
	t.Helper()

	// build insert statement with columns passed in through the `columns` parameter
	query := "INSERT pockethealth.requests SET"
	var values []interface{}
	for column, value := range columns {
		query = fmt.Sprintf("%s %s=?,", query, column)
		values = append(values, value)
	}

	res, err := db.Exec(
		query[:len(query)-1], // remove last comma
		values...,
	)
	require.NoError(t, err, "unable to execute query")

	requestId, err := res.LastInsertId()
	require.NoError(t, err, "unable to get requestId")

	t.Cleanup(func() {
		db.Exec("DELETE FROM pockethealth.requests WHERE id=?", requestId)
	})

	return requestId
}
