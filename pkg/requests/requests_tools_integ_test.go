//go:build integration
// +build integration

package requests

import (
	"context"
	"database/sql"
	"strconv"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"golang.org/x/text/language"
)

func TestHandleDuplicateRequest(t *testing.T) {
	db := testutils.SetupTestDB(t)
	//setup service
	mockProvider := phlanguage.LanguageTagProvider{
		GetLanguageTag: func(ctx context.Context, id interface{}) language.Tag { return language.English },
	}
	mockLangProviders := languageproviders.LanguageTagProviders{
		AccountId: mockProvider,
		ClinicId:  mockProvider,
		OrgId:     mockProvider,
	}
	s := RequestsApiService{
		sqldb: db,
		// unused
		containerClient:      azureUtils.ContainerClient{},
		i18nBundle:           i18n.NewBundle(language.English),
		languageTagProviders: mockLangProviders,
		acctSvcClient:        &accountservice.AcctSvcMock{},
		orgSvcClient:         &orgservice.OrgServiceMock{},
	}

	t.Run(
		"when duplicate request exists with pending status, should return true and call sendDuplicateEmail",
		func(t *testing.T) {
			rand := strconv.FormatInt(time.Now().UnixNano(), 10)
			email := "test_email" + rand + "@example.com"
			dob := "06/26/1990"
			// enrollment org & clinic
			orgId := int64(129)
			clinicId := int64(365)

			// setup duplicate request
			res, err := db.Exec(
				`INSERT INTO requests(clinic_id, legacy_provider_id, email, dob, scan_id, account_id) VALUES (?, ?, ?, ?, ?, ?)`,
				clinicId,
				orgId,
				email,
				dob,
				"",
				rand,
			)
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			requestId, err := res.LastInsertId()
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM requests WHERE id=?", requestId)
			})

			// setup mock
			mockCalled := false
			sendDuplicateEmailMock := func(_ context.Context, _ *sql.DB, _ *i18n.Bundle, _ language.Tag, _ *RequestsApiService, emailAddr string, duplicateRequestEmail PatientRequestEmail, _ string) {
				mockCalled = true
				if emailAddr != email {
					t.Errorf("mock called with wrong email: expected %s, got %s", email, emailAddr)
				}
				if duplicateRequestEmail.RequestID != requestId {
					t.Errorf(
						"mock called with wrong request id: expected %d, got %d",
						requestId,
						duplicateRequestEmail.RequestID,
					)
				}
			}
			s.sendDuplicateEmail = sendDuplicateEmailMock

			acct := accountservice.Account{
				AccountId: rand,
				IsPassSet: false,
			}

			s.processDuplicateNotification(
				context.Background(),
				email,
				"en",
				"testClinicName",
				requests.UNDER_REVIEW,
				roiservice.VerificationConfig{Active: false},
				&acct,
				requestId,
				orgId,
				true, // InAppRequest to be set to true
				PatientRequestEmail{RequestID: requestId},
			)
			if !mockCalled {
				t.Error("expected sendDuplicateEmail to be called")
			}
		},
	)

	t.Run(
		"when duplicate request exists with rejected status, should return true and call sendRejectedEmail",
		func(t *testing.T) {
			rand := strconv.FormatInt(time.Now().UnixNano(), 10)
			email := "test_email" + rand + "@example.com"
			dob := "06/26/1990"
			// enrollment org & clinic
			orgId := int64(129)
			clinicId := int64(365)

			// setup duplicate request
			res, err := db.Exec(
				`INSERT INTO requests(clinic_id, legacy_provider_id, email, dob, scan_id, account_id) VALUES (?, ?, ?, ?, ?, ?)`,
				clinicId,
				orgId,
				email,
				dob,
				requests.REJECTED,
				rand,
			)
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			requestId, err := res.LastInsertId()
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM requests WHERE id=?", requestId)
			})

			// setup mock
			mockCalled := false
			sendRejectedEmailMock := func(_ context.Context, _ cio_email.CIOEMailer, emailAddr string, id int64, _ string, _ string, _ bool, _ string) {
				mockCalled = true
				if emailAddr != email {
					t.Errorf("mock called with wrong email: expected %s, got %s", email, emailAddr)
				}
				if id != requestId {
					t.Errorf(
						"mock called with wrong request id: expected %d, got %d",
						requestId,
						id,
					)
				}
			}
			s.sendRejectedEmail = sendRejectedEmailMock

			acct := accountservice.Account{
				AccountId: rand,
				IsPassSet: false,
			}

			s.processDuplicateNotification(
				context.Background(),
				email,
				"en",
				"testClinicName",
				requests.REJECTED,
				roiservice.VerificationConfig{},
				&acct,
				requestId,
				orgId,
				true, // InAppRequest to be set to true
				PatientRequestEmail{},
			)
			if !mockCalled {
				t.Error("expected sendRejectedEmail to be called")
			}
		},
	)

	t.Run(
		"when duplicate request exists with valid status, should return true and call sendAlreadyEnrolledEmail",
		func(t *testing.T) {
			rand := strconv.FormatInt(time.Now().UnixNano(), 10)
			email := "test_email" + rand + "@example.com"
			dob := "06/26/1990"
			scanId := ksuid.New().String()
			// enrollment org & clinic
			orgId := int64(129)
			clinicId := int64(365)

			// setup duplicate request
			res, err := db.Exec(
				`INSERT INTO requests(clinic_id, legacy_provider_id, email, dob, scan_id, account_id) VALUES (?, ?, ?, ?, ?, ?)`,
				clinicId,
				orgId,
				email,
				dob,
				scanId,
				rand,
			)
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			requestId, err := res.LastInsertId()
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM requests WHERE id=?", requestId)
			})

			// setup mock
			mockCalled := false
			sendAlreadyEnrolledEmailMock := func(_ context.Context, _ cio_email.CIOEMailer, emailAddr string, _ string, _ string) {
				mockCalled = true
				if emailAddr != email {
					t.Errorf("mock called with wrong email: expected %s, got %s", email, emailAddr)
				}
			}
			s.sendAlreadyEnrolledEmail = sendAlreadyEnrolledEmailMock
			acct := accountservice.Account{
				AccountId: rand,
				IsPassSet: false,
			}

			s.processDuplicateNotification(
				context.Background(),
				email,
				"en",
				"testClinicName",
				scanId,
				roiservice.VerificationConfig{},
				&acct,
				requestId,
				orgId,
				true, // InAppRequest to be set to true
				PatientRequestEmail{},
			)

			if !mockCalled {
				t.Error("expected sendAlreadyEnrolledEmail to be called")
			}
		},
	)
}
