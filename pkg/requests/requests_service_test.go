package requests

import (
	"context"
	"testing"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"
)

func TestAddRequestClinicProviderInfo(t *testing.T) {
	ctx := context.Background()

	t.Run("Get error for invalid request details", func(t *testing.T) {
		service := setup(t, ctx, false, false, false, false)
		file, id, verificationToken, _, err := service.AddRequest(ctx, models.CreateRequest{}, "")
		if err == nil {
			t.Errorf("expected error but got none")
		}
		if id != -1 {
			t.Errorf("expected id = -1, got: %d", id)
		}
		if file != nil {
			t.Errorf("expected nil file returned")
		}
		if verificationToken != "" {
			t.Error("expected verificationToken to be emoty")
		}
	})

	t.Run("Get error for zero value request provider identifier", func(t *testing.T) {
		service := setup(t, ctx, false, false, false, false)
		file, id, verificationToken, _, err := service.AddRequest(ctx, models.CreateRequest{
			RequestDetails: models.Request{
				OrgId: 0,
			},
		}, "")
		if err == nil {
			t.Errorf("expected error but got none")
		}
		if id != -1 {
			t.Errorf("expected id = -1, got: %d", id)
		}
		if file != nil {
			t.Errorf("expected nil file returned")
		}
		if verificationToken != "" {
			t.Error("expected verificationToken to be empty")
		}
	})

	t.Run("Get error for valid provider but zero value clinic identifier", func(t *testing.T) {
		service := setup(t, ctx, false, false, true, false)
		file, id, verificationToken, _, err := service.AddRequest(ctx, models.CreateRequest{
			RequestDetails: models.Request{
				OrgId:      9,
				ProviderId: 0, // this is actually clinicId....
			},
		}, "")
		if err == nil {
			t.Errorf("expected error but got none")
		}
		if id != -1 {
			t.Errorf("expected id = -1, got: %d", id)
		}
		if file != nil {
			t.Errorf("expected nil file returned")
		}
		if verificationToken != "" {
			t.Error("expected verificationToken to be empty")
		}
	})

	t.Run("Get error for valid provider and invalid non-zero clinic identifier, but clinic  cannot be found", func(t *testing.T) {
		service := setup(t, ctx, false, false, false, true)
		file, id, verificationToken, _, err := service.AddRequest(ctx, models.CreateRequest{
			RequestDetails: models.Request{
				OrgId:      9,
				ProviderId: 100000, // this is actually clinicId....
			},
		}, "")
		if err == nil {
			t.Errorf("expected error but got none")
		}
		if id != -1 {
			t.Errorf("expected id = -1, got: %d", id)
		}
		if file != nil {
			t.Errorf("expected nil file returned")
		}
		if verificationToken != "" {
			t.Error("expected verificationToken to be empty")
		}
	})
}

func TestGetRequestStatusHistory(t *testing.T) {
	ctx := context.Background()

	t.Run("get no records for empty request identifier", func(t *testing.T) {
		service := setup(t, ctx, false, false, false, false)
		results := service.GetRequestStatusHistory(ctx, "")
		if len(results) != 0 {
			t.Errorf("expected no records in result, got %d", len(results))
		}
	})

	t.Run("get no records for non empty request identifier that is not a real request ID", func(t *testing.T) {
		service := setup(t, ctx, true, false, false, false)
		results := service.GetRequestStatusHistory(ctx, "1")
		if len(results) != 0 {
			t.Errorf("expected no records in result, got %d", len(results))
		}
	})
}

func setup(
	t *testing.T,
	ctx context.Context,
	expectError bool,
	returnTestData bool,
	returnEmptyTestDataList bool,
	expectClinicDataError bool,
) RequestsApiService {
	logrus.SetLevel(logrus.TraceLevel)

	generalRetry := policy.RetryOptions{
		TryTimeout: 5 * time.Second,
	}
	requestsContainerURL, _ := azstorageauth.GetContainerClient(
		ctx,
		"phdevcentralcabloblrs",
		"requests",
		&generalRetry,
	)

	containerClient := azureUtils.ContainerClient{
		Requests: requestsContainerURL,
	}

	experimentsClient := &mocks.MockAmplitudeExperimentClientData{
		Variants: map[string]experiment.Variant{},
	}
	cioProducer := &mocks.MockCIOProducerData{CallLogs: make([]map[string]any, 0)}
	// setup service
	service := RequestsApiService{
		sqldb:             nil,
		hostName:          "localhost",
		acctSvcClient:     &accountservice.AcctSvcMock{},
		experimentsClient: experimentsClient,
		cioEventProducer:  cioProducer,
		containerClient:   containerClient,
		orgSvcClient: &orgservice.OrgServiceMock{
			ReturnDemoClinicData:  returnTestData,
			ReturnEmptyDataList:   returnEmptyTestDataList,
			ExpectClinicDataError: expectClinicDataError,
			ExpectError:           expectError,
		},
		roiSvcClient: &roiservice.RoiServiceMock{ExpectError: expectError},
	}
	return service
}
