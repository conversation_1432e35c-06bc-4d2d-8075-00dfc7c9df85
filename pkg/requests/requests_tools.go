package requests

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/consentPdf"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	phi "gitlab.com/pockethealth/coreapi/pkg/mysql/phi_profiles"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	"gitlab.com/pockethealth/coreapi/pkg/util/phiHelpers"

	languageTag "golang.org/x/text/language"

	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	enrollments "gitlab.com/pockethealth/coreapi/pkg/mysql/enrollments"
	incompleteRequests "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequests"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/util/rollout"
	"gitlab.com/pockethealth/coreapi/pkg/util/script"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"

	fileutil "gitlab.com/pockethealth/coreapi/pkg/util/file"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type SendDuplicateEmail func(
	ctx context.Context, db *sql.DB, i18nBundle *i18n.Bundle, language languageTag.Tag, s *RequestsApiService,
	emailAddr string, duplicateRequestEmail PatientRequestEmail, accountId string,
)

type SendRejectedEmail func(
	ctx context.Context, mailer cio_email.CIOEMailer, emailAddr string, requestId int64, clinicName string,
	accountId string, hasAccount bool, setupToken string,
)

type SendAlreadyEnrolledEmail func(
	ctx context.Context, mailer cio_email.CIOEMailer, emailAddr string, accoutnId string, clinicName string,
)

type ClinicRequestEmail struct {
	Subject      string
	RequestID    int64
	IsEnrollment bool
	RegionID     uint16
}

type PatientRequestEmail struct {
	RequestID      int64
	IncludeReceipt bool
	FeeAmount      string
	FeeTax         string
	FeeTotal       string
	CardType       string
	LastFour       string
	TaxName        string
	RegionID       uint16
	IsUPH          bool
}

type DuplicateEmail struct {
	RequestID  int64
	ClinicName string
	HasAccount bool
	RegionID   uint16
}

type DelegateReviewEmail struct {
	RequestID int64
	OrgName   string
}

type IDVEmail struct {
	IdVerificationLink string
	ProviderName       string
}

func (s *RequestsApiService) requestSubmissionPublishEvents(
	ctx context.Context,
	requestId int64,
	accountId string,
	providerId string,
	clinicId string,
	status string,
) {
	// publish request creation message - uncomment when consumer is implemented in roi
	// s.publishRequestCreatedEvent(ctx, requestId, accountId, providerId, clinicId)
	// publish request status message
	s.publishRequestStatusEvent(ctx, requestId, status)
}

func (s *RequestsApiService) publishRequestDelegateExpiryEvent(
	ctx context.Context,
	requestId int64,
	accountId string,
	providerId string,
	patientId string,
	dateOfBirth string,
) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":  accountId,
		"request_id":  requestId,
		"provider_id": providerId,
	})

	if s.ServiceBusQueue == nil {
		return
	}

	client, err := s.ServiceBusQueue.GetRegionClient()
	if err != nil {
		lg.WithError(err).Info("service bus client is not valid")
		return
	}

	rid := strconv.FormatInt(requestId, 10)
	// publish request creation message
	if s.ServiceBusQueue != nil {
		s.ServiceBusQueue.Publish(
			ctx,
			&RequestEnrolmentExpiryEvent{
				RequestId:   rid,
				AccountId:   accountId,
				ProviderId:  providerId,
				PatientId:   patientId,
				DateOfBirth: dateOfBirth,
				Region:      s.region,
				Source:      RequestEventSource,
				Created:     time.Now().UTC(),
			},
			"enrolmentexpiry",
			client,
		)

		lg.Info("request delegate expiry event published successfully")
	}
}

func (s *RequestsApiService) publishRequestMetadataEvent(
	ctx context.Context,
	requestId int64,
	accountId string,
	providerId int64,
	clinicId int64,
	isUph bool,
) {
	lg := logutils.CtxLogger(ctx).WithField("request_id", requestId)

	s.RequestsTopic.Publish(ctx, &RequestEvent{
		RequestId:        requestId,
		AccountId:        accountId,
		ProviderId:       providerId,
		ClinicId:         clinicId,
		IsUph:            isUph,
		Region:           s.region,
		EventType:        REQUEST_CREATED_EVENT_TYPE,
		TimestampSeconds: time.Now().Unix(),
	})

	lg.Info("request metadata event published successfully")
}

func (s *RequestsApiService) patientEnrollmentPublishEvents(
	ctx context.Context,
	requestId int64,
	legacyProviderId int64,
	accountId string,
	request models.Request,
) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":         requestId,
		"legacy_provider_id": legacyProviderId,
		"account_id":         accountId,
	})

	contentJson, _ := json.Marshal(request.Contents)
	correlationId, _ := logutils.CorrelationID(ctx)

	s.PatientEnrollmentTopic.Publish(ctx, &PatientEnrollmentEvent{
		CorrelationId: correlationId,
		RequestId:     requestId,
		ProviderId:    legacyProviderId,
		Request: PatientEnrollmentRequest{
			ID:              requestId, // request.RequestId isn't set
			FirstName:       request.FirstName,
			LastName:        request.LastName,
			OHIP:            request.Ohip,
			OHIPVersionCode: request.Ohipvc,
			SSN:             request.Ssn,
			IPN:             request.Ipn,
			MRN:             request.Mrn,
			AltID:           request.AltId,
			DOB:             request.Dob,
			Tel:             request.Tel,
			Email:           request.Email,
			Contents:        string(contentJson),
			AccountID:       accountId,
		},
	})
	lg.Info("patient enrollment request event published successfully")
}

func (s *RequestsApiService) publishRequestCreatedEvent(
	ctx context.Context,
	requestId int64,
	accountId string,
	providerId string,
	clinicId string,
) {
	lg := logutils.CtxLogger(ctx).WithField("request_id", requestId)
	if s.ServiceBusQueue == nil {
		return
	}

	client, err := s.ServiceBusQueue.GetRegionClient()
	if err != nil {
		lg.WithError(err).Info("service bus client is not valid")
		return
	}

	rid := strconv.FormatInt(requestId, 10)
	// publish request creation message
	s.ServiceBusQueue.Publish(
		ctx,
		&RequestCreatedEvent{
			RequestId:  rid,
			AccountId:  accountId,
			ProviderId: providerId,
			ClinicId:   clinicId,
			Region:     s.region,
			Created:    time.Now().UTC(),
		},
		"requestcreate",
		client,
	)

	lg.Info("request create event published successfully")
}

func (s *RequestsApiService) publishRequestStatusEvent(
	ctx context.Context,
	requestId int64,
	status string,
) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":     requestId,
		"request_status": status,
	})

	if s.ServiceBusQueue == nil {
		return
	}

	client, err := s.ServiceBusQueue.GetRegionClient()
	if err != nil {
		lg.WithError(err).Info("service bus client is not valid")
		return
	}
	rid := strconv.FormatInt(requestId, 10)
	// publish request status message
	s.ServiceBusQueue.PublishSession(
		&RequestStatusMessage{
			RequestId: rid,
			Status:    status,
			Region:    s.region,
			Created:   time.Now().UTC(),
		},
		s.requestStatusQueue,
		rid,
		client,
	)
	lg.Info("request status event published successfully")
}

func (s *RequestsApiService) publishRequestAccountCreationNotificationEvent(
	ctx context.Context,
	requestId int64,
	accountId string,
) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"request_id": requestId,
		"account_id": accountId,
	})

	if s.ServiceBusQueue == nil {
		return
	}

	client, err := s.ServiceBusQueue.GetGlobalClient()
	if err != nil {
		lg.WithError(err).Info("service bus client is not valid")
		return
	}
	s.ServiceBusQueue.PublishSession(
		&accountservice.AccountRequestNotificationMessage{
			RequestId: requestId,
			AccountId: accountId,
			EventType: accountservice.NEW_REQUEST_CREATED_EVENT_TYPE,
		},
		s.accountCreatedRequestQueue,
		accountId,
		client,
	)
	lg.Info("request account creation notification event published successfully")
}

func (s *RequestsApiService) storeDelegateDocuments(
	ctx context.Context,
	requestId int64,
	delegatePhotoId []*multipart.FileHeader,
	delegateDocuments []*multipart.FileHeader,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	if len(delegatePhotoId) > 0 {
		// pass only first image/pdf photo id file as FE allows only 1 file upload
		photoIdBytes, err := s.getBytesFromMultipartFile(delegatePhotoId[0])
		if err != nil {
			lg.WithError(err).Info("error loading delgate photoId bytes")
			go s.removeRequest(requestId)
			return err
		}
		// store the compressed photoId in Azure
		err = s.storeDelegatePhotoId(ctx, requestId, photoIdBytes)
		if err != nil {
			lg.WithError(err).Info("error storing delegate photoId file")
			go s.removeRequest(requestId)
			return err
		}
	}

	for i, delegateDocument := range delegateDocuments {
		delegateDocumentBytes, err := s.getBytesFromMultipartFile(
			delegateDocument,
		)
		if err != nil {
			lg.WithError(err).Error("loading delegate document bytes failed")
			go s.removeRequest(requestId)
			return err
		}

		// store the compressed photoId in Azure
		err = s.storeDelegateDocument(ctx, requestId, delegateDocumentBytes, i)
		if err != nil {
			lg.WithError(err).Error("delegate document storage failed")
			go s.removeRequest(requestId)
			return err
		}
	}
	return nil
}

func (s *RequestsApiService) processConsentDocuments(
	ctx context.Context,
	requestId int64,
	data models.CreateRequest,
	provider orgservice.Provider,
	language string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx)
	// process and store files
	file, err := s.generateConsentPDF(
		ctx,
		requestId,
		data.RequestDetails,
		data.SignatureImage,
		data.MinorSignatureImage,
		data.DelegForm,
		provider,
		language,
	)
	if err != nil {
		lg.WithError(err).Info("error processing consent PDF")
		go s.removeRequest(requestId)
		return nil, err
	}
	err = s.storeConsentPDFs(ctx, requestId, file)
	if err != nil {
		lg.WithError(err).Info("error storing consent PDF after creation")
		go s.removeRequest(requestId)
		return nil, err
	}

	if err := s.storeConsentSignature(ctx, requestId, []byte(data.SignatureImage)); err != nil {
		lg.WithError(err).Info("failed to store consent signature")
		return nil, err
	}

	if err := s.storeDelegateDocuments(
		ctx,
		requestId,
		data.DelegPhotoId,
		data.DelegForm,
	); err != nil {
		lg.WithError(err).Info("error storing delegate documents")
		return nil, err
	}

	return file, nil
}

func (s *RequestsApiService) processDuplicateNotification(
	ctx context.Context,
	requestEmail, requestLanguage, clinicName, duplicateStatus string,
	rvc roiservice.VerificationConfig,
	acct *accountservice.Account,
	duplicateRequestId int64,
	providerId int64,
	inAppRequest bool,
	duplicateRequestEmail PatientRequestEmail,
) {
	switch inAppRequest {
	case true:
		s.processInAppDuplicateNotification(
			ctx,
			requestEmail,
			duplicateStatus,
			acct,
			rvc,
			duplicateRequestId,
			clinicName,
			providerId,
			duplicateRequestEmail,
		)
	case false:
		// if request is a duplicate, account already exists - send continue to account email
		s.processOutAppDuplicateNotification(
			ctx,
			requestEmail,
			requestLanguage,
			acct.AccountId,
			rvc,
			duplicateRequestId,
			providerId,
			duplicateRequestEmail,
		)
	}
}

func (s *RequestsApiService) processOutAppDuplicateNotification(
	ctx context.Context,
	requestEmail, requestLanguage, accountId string,
	rvc roiservice.VerificationConfig,
	duplicateRequestId int64,
	providerId int64,
	duplicateRequestEmail PatientRequestEmail,
) {
	switch rvc.Active {
	case true:
		lang := s.getAccountProviderLanguageTag(ctx, accountId, providerId)
		s.handleVerificationPendingDuplicateRequestEmail(
			ctx,
			requestEmail,
			duplicateRequestId,
			lang,
			duplicateRequestEmail,
			accountId,
		)
	case false:
		// if request is a duplicate, account already exists - send continue to account email
		s.sendRequestContinueInAccountNotification(
			ctx,
			duplicateRequestId,
			accountId,
			requestEmail,
			requestLanguage,
		)
	}
}

func (s *RequestsApiService) processInAppDuplicateNotification(
	ctx context.Context,
	toEmail, duplicateStatus string,
	acct *accountservice.Account,
	rvc roiservice.VerificationConfig,
	duplicateRequestId int64,
	clinicName string,
	providerId int64,
	duplicateRequestEmail PatientRequestEmail,
) {
	lang := s.getAccountProviderLanguageTag(ctx, acct.AccountId, providerId)

	switch {
	case requests.IsRejectedStatus(duplicateStatus):
		s.handleRejectedDuplicateRequestEmail(
			ctx,
			toEmail,
			duplicateRequestId,
			clinicName,
			acct.AccountId,
			acct.IsPassSet,
		)
	case requests.IsPendingStatus(duplicateStatus):
		if rvc.Active {
			s.handleVerificationPendingDuplicateRequestEmail(
				ctx,
				toEmail,
				duplicateRequestId,
				lang,
				duplicateRequestEmail,
				acct.AccountId,
			)
		} else {
			s.handlePendingDuplicateRequestEmail(ctx, toEmail, lang, duplicateRequestEmail, acct.AccountId)
		}
	case requests.IsEnrolled(duplicateStatus):
		s.handleEnrolleddDuplicateRequestEmail(ctx, toEmail, acct.AccountId, clinicName)
	}
}

func (s *RequestsApiService) processOutappRequestSubmittedNotification(
	newAccount bool,
	requestId int64,
	accountId string,
	requestEmail string,
	language string,
	rvc roiservice.VerificationConfig,
	providerId int64,
	patientRequestEmail PatientRequestEmail,
	verificationToken string,
) string {
	ctx := context.Background()

	var verificationLink string

	if rvc.Active {
		verificationLink = s.notifyIdvPatient(
			ctx,
			accountId,
			requestId,
			requestEmail,
			providerId,
			false,
		)
	} else {
		s.notifyPatient(
			ctx,
			accountId,
			requestEmail,
			patientRequestEmail,
		)
	}

	if verificationToken == "" {
		switch newAccount {
		case true:
			s.publishRequestAccountCreationNotificationEvent(ctx, requestId, accountId)
		case false:
			s.sendRequestContinueInAccountNotification(
				ctx,
				requestId,
				accountId,
				requestEmail,
				language,
			)
		}
	}

	return verificationLink
}

// processInappRequestSubmittedNotification - send request submission notifications based on id verification configuration
func (s *RequestsApiService) processInappRequestSubmittedNotification(
	requestEmail PatientRequestEmail,
	request models.Request,
	requestId int64,
	acctId string,
	providerId int64,
	rvc roiservice.VerificationConfig,
	delegateIsAccountOwner bool,
) {
	ctx := context.Background()

	if rvc.Active {
		toEmail := setNotificationEmailForDelegateRequest(
			request.Email,
			request.Contents.Delegate,
			delegateIsAccountOwner,
		)
		s.notifyIdvPatient(
			ctx,
			acctId,
			requestId,
			toEmail,
			providerId,
			false,
		)
	} else {
		s.notifyPatient(
			ctx,
			acctId,
			request.Email,
			requestEmail,
		)
	}
}

// TODO: Delete this once the new delegate flow is the default
func setNotificationEmailForDelegateRequest(
	requestEmail string,
	requestDelegate *models.StudyRequestDelegate,
	delegateIsAccountOwner bool,
) string {
	if delegateIsAccountOwner {
		return requestEmail
	}

	if requestDelegate != nil {
		requestEmail = requestDelegate.Email
	}
	return requestEmail
}

func (s *RequestsApiService) sendRequestContinueInAccountNotification(
	ctx context.Context,
	requestId int64,
	acctId string,
	emailAddr string,
	language string,
) {
	// parse language for default en to ensure correct format is en-US
	if strings.ToLower(language) == "en" {
		language = "en-US"
	}

	link := fmt.Sprintf("%s/%s/login?rid=%d", s.pocketHealthAppUrl, language, requestId)
	err := s.cioEmailer.Send(
		ctx,
		emailAddr,
		acctId,
		cio_email.REQUEST_CONTINUE_TO_ACCOUNT,
		map[string]interface{}{
			"Link": link,
		},
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Info("failed to send request continue to account email notification")
	}

	logutils.DebugCtxLogger(ctx).
		Infof("send request continue to account email notification successfully for request ID %d", requestId)
}

func (s *RequestsApiService) removeRequest(requestId int64) {
	ctx := context.Background()
	removeErr := requests.Remove(ctx, s.sqldb, requestId)
	if removeErr != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("request_id", requestId).
			WithError(removeErr).
			Info("error removing request from DB")
	}
}

func (s *RequestsApiService) completeIncompleteRequests(requestId int64, requestEmail string) {
	ctx := context.Background()

	err := incompleteRequests.MarkRequestsAsCompleted(ctx, s.sqldb, requestId, requestEmail)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("request_id", requestId).
			WithError(err).
			Info("unable to mark incomplete requests as completed")
	}
}

func (s *RequestsApiService) checkRequestDuplicateStatus(
	ctx context.Context,
	provider orgservice.Provider,
	accountId string,
	patientId string,
	data models.CreateRequest,
) (isDuplicate bool, duplicateRequestId int64, duplicateStatus string) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"accountId":        accountId,
		"patientId":        patientId,
		"legacyProviderId": provider.LegacyId,
	})
	if provider.IsUph {
		duplicateRequestId, duplicateStatus = requests.GetRequestStatusV2(
			ctx,
			s.sqldb,
			accountId,
			data.RequestDetails,
			provider.LegacyId,
		)
		// TODO Toks: if duplicate request, check duplicate request exams, if same exams mark as duplicate
	} else {
		duplicateRequestId, duplicateStatus = requests.GetRequestStatusV2(
			ctx,
			s.sqldb,
			accountId,
			data.RequestDetails,
			provider.LegacyId,
		)
		// check enrollment
		if duplicateRequestId == -1 {
			isDuplicate, duplicateRequestId = enrollments.IsPatientEnrolled(ctx, s.sqldb, accountId, patientId, provider.LegacyId)
			lg.Infof("enrollment duplicate request check: %t", isDuplicate)
		} else {
			isDuplicate = true
		}
	}
	return isDuplicate, duplicateRequestId, duplicateStatus
}

func (s *RequestsApiService) requestAnalyticsTracking(
	ctx context.Context,
	paymentCompleted bool,
	priorPlanId int32,
	planId int32,
	accountId string,
	providerId int64,
	clinicId int64,
	requestEmail string,
	deviceID string,
	isDelegate bool,
) {
	lg := logutils.DebugCtxLogger(ctx)
	// amplitude tracking
	currency := ""
	revenue := 0
	if paymentCompleted {
		r, c, err := s.planSvcClient.GetPlanAmountAndCurrency(ctx, planId)
		if err != nil {
			lg.WithError(err).Info("could not get plan from plan svc")
		}
		currency = c
		revenue = r
	}
	s.identifyRequestAccount(ctx, accountId, deviceID, isDelegate)
	s.logRequestProcessedEvent(
		deviceID,
		accountId,
		providerId,
		clinicId,
		revenue,
		currency,
		priorPlanId,
		planId,
	)
	s.undoEmailCampaignExposure(
		ctx, requestEmail, clinicId, deviceID,
	)
}

func (s *RequestsApiService) notifyIdvPatient(
	ctx context.Context,
	accountId string,
	requestId int64,
	emailAddr string,
	orgId int64,
	newAccount bool,
) string {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"orgId":      orgId,
		"request_id": requestId,
	})
	link, err := s.roiSvcClient.ProcessVerificationInit(
		ctx,
		accountId,
		strconv.FormatInt(requestId, 10),
		emailAddr,
		strconv.FormatInt(orgId, 10),
		strconv.FormatBool(newAccount),
	)
	if err != nil {
		lg.Error("could not send process verification init to roisvc")
	}

	return link
}

func (s *RequestsApiService) notifyPatient(
	ctx context.Context,
	acctId string,
	emailAddr string,
	reqEmail PatientRequestEmail,
) {
	err := s.cioEmailer.Send(
		ctx,
		emailAddr,
		acctId,
		cio_email.REQUEST_CONFIRMATION,
		map[string]any{
			"RequestID":      reqEmail.RequestID,
			"IncludeReceipt": reqEmail.IncludeReceipt,
			"FeeAmount":      reqEmail.FeeAmount,
			"FeeTax":         reqEmail.FeeTax,
			"FeeTotal":       reqEmail.FeeTotal,
			"CardType":       reqEmail.CardType,
			"LastFour":       reqEmail.LastFour,
			"TaxName":        reqEmail.TaxName,
			"RegionID":       reqEmail.RegionID,
			"IsUPH":          reqEmail.IsUPH,
		},
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("failed to send request submitted email")
	}
}

func (s *RequestsApiService) generateConsentPDFString(
	ctx context.Context,
	requestId int64,
	request models.Request,
	signatureBase64 string,
	isUPH bool,
	provider orgservice.Provider,
) string {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id": requestId,
	})

	// may be empty, but check if the org has custom consent text
	var consentText string
	var delConsentText string
	configurationMap, err := s.orgSvcClient.GetFormConfigurationFieldsByLegacyProviderId(
		ctx,
		provider.LegacyId,
		"",
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("GetFormConfigurationFieldsByLegacyProviderId failed")
	}
	if consentTextField, exists := configurationMap["patient_consent_language"]; exists &&
		consentTextField.Value != nil {
		consentText = consentTextField.Value.(string)
	}
	if delConsentTextField, exists := configurationMap["delegate_consent_language"]; exists &&
		delConsentTextField.Value != nil {
		delConsentText = delConsentTextField.Value.(string)
	}

	lang := phlanguage.Select(ctx, s.supportedLanguages).
		WithOrganization(s.languageTagProviders.OrgId, provider.LegacyId).
		GetLanguageTag()

	var orgName string
	if isUPH {
		orgName = request.Contents.RecentExamDetails.Site
		// the provider specific UPH forms will not have the exam_site value present so use the org name in this case
		if orgName == "" {
			orgName = provider.Name
		}
		// UPH consent text is different from the DB but the orgName must be populated before as it's not part of the
		// general PDF JSON string creation
		consentText = strings.ReplaceAll(consentText, "_ORG_NAME_", orgName)
	} else {
		orgName = provider.Name
	}

	pdfJson := pdfs.CreateConsentPDFJsonString(
		ctx,
		s.i18nBundle,
		lang,
		time.Now(),
		consentText,
		delConsentText,
		requestId,
		request,
		orgName,
		provider.Logo,
		signatureBase64,
		isUPH,
	)
	_, err = pdfs.ConsentJSONValidator(pdfJson)
	if err != nil {
		lg.WithError(err).
			Error(fmt.Sprintf("Consent Lang Json Validator returned an error for providerID: %d", provider.LegacyId))
	}
	return pdfJson
}

func (s *RequestsApiService) generateBaseConsentPDFV1(
	ctx context.Context,
	requestId int64,
	request models.Request,
	signatureBase64 string,
	isUPH bool,
	provider orgservice.Provider,
	tempFilesDirectory string,
) chan struct {
	string
	error
} {
	channel := make(chan struct {
		string
		error
	})

	go func() {
		defer close(channel)

		lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"request_id": requestId,
		})

		pdfString := s.generateConsentPDFString(
			ctx,
			requestId,
			request,
			signatureBase64,
			isUPH,
			provider,
		)

		bytebuf := []byte(pdfString)

		err := os.WriteFile(tempFilesDirectory+"/DESCRIPTOR", bytebuf, 0o600)
		if err != nil {
			channel <- struct {
				string
				error
			}{"", errors.New(errormsgs.ERR_COPY_FAIL)}
			return
		}

		err, stdErrStr := script.RunNodeScript(
			"jsScripts/pdfHeader.js",
			[]string{tempFilesDirectory + "/DESCRIPTOR"},
		)
		if err != nil {
			lg.WithError(err).Error("pdf maker failed: " + stdErrStr)
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		channel <- struct {
			string
			error
		}{tempFilesDirectory + "/MAIN.pdf", nil}
	}()

	return channel
}

func (s *RequestsApiService) storeTemporaryFile(
	ctx context.Context,
	uniqueId string,
	delegateDocument *multipart.FileHeader,
	tempFilesDirectory string,
) chan struct {
	string
	error
} {
	channel := make(chan struct {
		string
		error
	})

	go func() {
		defer close(channel)
		lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"request_id": uniqueId,
		})

		var fileD *os.File
		fileDeleg, err := delegateDocument.Open()
		if err != nil {
			lg.WithField("delegate_file", delegateDocument.Filename).
				WithError(err).
				Error("Open delegate file failed")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}
		defer fileDeleg.Close()

		var detectedMimeType string
		if detectedMimeType, err = fileutil.DetectContentType(ctx, fileDeleg); err != nil {
			channel <- struct {
				string
				error
			}{"", err}
			return
		}
		if !fileutil.IsAllowedImageFileType(detectedMimeType) {
			channel <- struct {
				string
				error
			}{"", fmt.Errorf("unauthorized mimeType: %s", detectedMimeType)}
			return
		}

		lg.WithFields(logrus.Fields{
			"delegate_file": delegateDocument.Filename,
			"Content-Type":  detectedMimeType,
		}).Info("delegate authorization file type")

		tmpFile := filepath.Clean(fmt.Sprintf(
			"%s/TMPHOLDER_%s",
			tempFilesDirectory,
			delegateDocument.Filename,
		))
		fileD, err = os.OpenFile(tmpFile, os.O_WRONLY|os.O_CREATE, 0o600) // #nosec G304
		if err != nil {
			lg.WithField("delegate_file", delegateDocument.Filename).
				WithError(err).
				Error("Create temp consent image file failed: " + err.Error())
			/* #nosec G104 */
			fileD.Close()
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		defer func() {
			err = fileD.Close()
			if err != nil {
				lg.WithError(err).Error("error closing temp delegate consent file")
			}
		}()
		_, err = io.Copy(fileD, fileDeleg)
		if err != nil {
			lg.WithField("delegate_file", delegateDocument.Filename).
				WithError(err).
				Error("Copy delegate file failed: " + err.Error())
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		channel <- struct {
			string
			error
		}{tmpFile, err}
	}()
	return channel
}

func (s *RequestsApiService) generateConsentPDF(
	ctx context.Context,
	requestId int64,
	request models.Request,
	signatureBase64 string,
	minorSignatureBase64 string,
	delegateDocuments []*multipart.FileHeader,
	provider orgservice.Provider,
	language string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id": requestId,
	})

	// setup temporary directory for file processing
	// all contents to be torn down at the end of this function
	temporaryFileDirectory := fmt.Sprintf("vault/tmp/prep/%d/CONSENT", requestId) // #nosec G304
	if _, err := os.Stat(temporaryFileDirectory); err != nil {
		err = os.MkdirAll(temporaryFileDirectory, 0o700)
		if err != nil {
			return []byte{}, err
		}
	}
	defer pdfs.CleanupTmpFiles(ctx, temporaryFileDirectory)

	pdfChannels := []chan struct {
		string
		error
	}{}

	if rollout.Rollout(ctx, s.sqldb, "use_base_consent_pdf_v2") {
		pdfChannels = append(
			pdfChannels,
			s.generateBaseConsentPDFV2(
				ctx,
				requestId,
				request,
				signatureBase64,
				minorSignatureBase64,
				provider,
				language,
				temporaryFileDirectory,
			),
		)
	} else {
		pdfChannels = append(
			pdfChannels,
			s.generateBaseConsentPDFV1(
				ctx,
				requestId,
				request,
				signatureBase64,
				provider.IsUph,
				provider,
				temporaryFileDirectory,
			),
		)
	}

	for _, delegateDocument := range delegateDocuments {
		pdfChannels = append(pdfChannels, s.storeTemporaryFile(
			ctx,
			strconv.FormatInt(requestId, 10),
			delegateDocument,
			temporaryFileDirectory,
		))
	}

	pdfFileResults := make([]struct {
		string
		error
	}, len(pdfChannels))
	for i, pdfChannel := range pdfChannels {
		pdfFileResults[i] = <-pdfChannel
	}

	pdfFileNames := []string{}
	for _, pdfFileResult := range pdfFileResults {
		if pdfFileResult.error != nil {
			lg.WithError(pdfFileResult.error).Error("failed to generate consent pdf component")
			return []byte{}, pdfFileResult.error
		} else {
			pdfFileNames = append(pdfFileNames, pdfFileResult.string)
		}
	}

	finalFilePath := temporaryFileDirectory + "/FINAL.pdf"

	if len(pdfFileNames) == 1 {
		finalFilePath = pdfFileNames[0]
	} else {
		args := append(pdfFileNames, finalFilePath)
		cmd := exec.Command(
			"convert",
			args...,
		) // #nosec G204 hope this is ok :/

		var stderr2 bytes.Buffer
		cmd.Stderr = &stderr2

		err := cmd.Run()
		if err != nil {
			lg.WithError(err).Error(stderr2.String())
			return []byte{}, errors.New(errormsgs.ERR_COJOIN_FAIL)
		}
	}

	file, err := os.ReadFile(finalFilePath) // #nosec G304
	if err != nil {
		lg.WithError(err).Error("failed to read stored consent pdf")
		return []byte{}, err
	}

	return file, nil
}

func (s *RequestsApiService) generateBaseConsentPDFV2(
	ctx context.Context,
	requestId int64,
	request models.Request,
	signatureBase64 string,
	minorSignatureBase64 string,
	provider orgservice.Provider,
	language string,
	temporaryFileDirectory string,
) chan struct {
	string
	error
} {
	channel := make(chan struct {
		string
		error
	})

	go func() {
		defer close(channel)

		lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"request_id": requestId,
		})

		// 1. Get fields needed to create the consent document, e.g. custom consent text
		var consentText, minorConsentText, delegateFullName string
		fullName := fmt.Sprintf("%s %s", request.FirstName, request.LastName)
		requestIdString := fmt.Sprintf("%d", requestId)
		date := time.Now().Format("2006/01/02")
		providerLogoBase64 := provider.Logo
		pocketHealthLogoBase64 := pdfs.GetBase64PocketHealthLogo()
		if providerLogoBase64 == "" {
			providerLogoBase64 = pocketHealthLogoBase64
		}

		configurationMap, err := s.orgSvcClient.GetFormConfigurationFieldsByLegacyProviderId(
			ctx,
			provider.LegacyId,
			language,
		)
		if err != nil {
			lg.WithError(err).Error("GetFormConfigurationFieldsByLegacyProviderId failed")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		if request.Contents.Delegate == nil {
			if consentTextField, exists := configurationMap["patient_consent_language"]; exists &&
				consentTextField.Value != nil {
				consentText = consentTextField.Value.(string)
			}
		} else {
			delegateFullName = fmt.Sprintf(
				"%s %s",
				request.Contents.Delegate.FirstName,
				request.Contents.Delegate.LastName,
			)
			if consentTextField, exists := configurationMap["delegate_consent_language"]; exists && consentTextField.Value != nil {
				consentText = consentTextField.Value.(string)
			}
			if minorConsentTextField, exists := configurationMap["patient_consent_language"]; exists && minorConsentTextField.Value != nil {
				minorConsentText = minorConsentTextField.Value.(string)
				minorConsentText = strings.ReplaceAll(minorConsentText, "_PAT_NAME_", fullName)
				minorConsentText = strings.ReplaceAll(minorConsentText, "_ORG_NAME_", provider.Name)
				minorConsentText = strings.ReplaceAll(
					minorConsentText,
					"_COMMA__DEL_NAME__COMMA_",
					fmt.Sprintf(",%s,", delegateFullName),
				)
				minorConsentText = strings.ReplaceAll(minorConsentText, "_DEL_NAME_", delegateFullName)
			}
		}
		consentText = strings.ReplaceAll(consentText, "_PAT_NAME_", fullName)
		consentText = strings.ReplaceAll(consentText, "_ORG_NAME_", provider.Name)
		consentText = strings.ReplaceAll(
			consentText,
			"_COMMA__DEL_NAME__COMMA_",
			fmt.Sprintf(",%s,", delegateFullName),
		)
		consentText = strings.ReplaceAll(consentText, "_DEL_NAME_", delegateFullName)

		// 2. Organize template parameters
		parameters := consentPdf.GetConsentPdfParameters(
			ctx,
			date,
			consentText,
			minorConsentText,
			requestIdString,
			request,
			fullName,
			delegateFullName,
			provider.Name,
			providerLogoBase64,
			signatureBase64,
			minorSignatureBase64,
			pocketHealthLogoBase64,
		)

		// 3. Populate the html template
		tag, err := languageTag.Parse(language)
		if err != nil {
			lg.WithError(err).Error("Failed to parse language")
			tag = languageTag.English
		}
		populatedTemplate, err := consentPdf.PopulateLiquidTemplate(
			phlanguage.GetTemplateForLanguage(ctx, "consentform.html", tag),
			parameters,
		)
		if err != nil {
			lg.WithError(err).Error("Failed to populate consent template")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		// 4. Convert populated html template to pdf
		pdfBytes, err := consentPdf.HtmlToPdf(populatedTemplate, 0)
		if err != nil {
			lg.WithError(err).Error("Failed to convert html to pdf")
			channel <- struct {
				string
				error
			}{"", err}
			return
		}

		// 5. Store temporary file for ImageMagick
		//    TODO @james.shen: deprecate file stitching to avoid messy temporary file storage
		filePath := temporaryFileDirectory + "/MAIN.pdf"
		err = os.WriteFile(filePath, pdfBytes, 0o600)
		channel <- struct {
			string
			error
		}{filePath, err}
	}()

	return channel
}

func (s *RequestsApiService) getBytesFromMultipartFile(
	file *multipart.FileHeader,
) ([]byte, error) {
	fileDeleg, err := file.Open()
	if err != nil {
		return []byte{}, err
	}
	return io.ReadAll(fileDeleg)
}

func (s *RequestsApiService) storeConsentPDFs(
	ctx context.Context,
	requestId int64,
	pdfFile []byte,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":        requestId,
		"consent_file_size": len(pdfFile),
	})
	lg.Info("Request Consent File Storing to Blob")
	err := azureUtils.UploadBlobBytes(
		ctx,
		s.containerClient.Requests,
		strconv.FormatInt(requestId, 10),
		pdfFile,
	)
	if err != nil {
		lg.WithError(err).Error("Failed storing request consent file to blob")
		return errors.New("Unable to write file to storage")
	}
	return nil
}

func (s *RequestsApiService) storeDelegateDocument(
	ctx context.Context,
	requestId int64,
	data []byte,
	fileIndex int,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":                  requestId,
		"delegate_document_file_size": len(data),
	})
	id := fmt.Sprintf("delegateDocument%d_%d", fileIndex, requestId)
	err := azureUtils.UploadBlobBytes(
		ctx,
		s.containerClient.Requests,
		id,
		data,
	)
	if err != nil {
		lg.WithError(err).Error("Failed storing request delegate document file to blob")
		return err
	}

	return nil
}

// stores the consent signature file in Azure with ID : 'signature_<requestId>'
func (s *RequestsApiService) storeConsentSignature(
	ctx context.Context,
	requestId int64,
	data []byte,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":          requestId,
		"signature_file_size": len(data),
	})
	id := fmt.Sprintf("signature_%s", strconv.FormatInt(requestId, 10))
	err := azureUtils.UploadBlobBytes(
		ctx,
		s.containerClient.Requests,
		id,
		data,
	)
	if err != nil {
		lg.WithError(err).Error("Failed storing request signature to blob")
		return err
	}

	return nil
}

// stores the given delegate photoID file in Azure with ID : 'delegatePhotoId_<requestId>'
func (s *RequestsApiService) storeDelegatePhotoId(
	ctx context.Context,
	requestId int64,
	data []byte,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id":        requestId,
		"photoId_file_size": len(data),
	})
	id := fmt.Sprintf("delegatePhotoId_%s", strconv.FormatInt(requestId, 10))
	err := azureUtils.UploadBlobBytes(
		ctx,
		s.containerClient.Requests,
		id,
		data,
	)
	if err != nil {
		lg.WithError(err).Error("Failed storing request delegate id file to blob")
		return errors.New("unable to write file to storage")
	}

	return nil
}

func (s *RequestsApiService) getRequiredIdType(
	ctx context.Context,
	providerId int64,
) (requiredIdType string, err error) {
	configurationMap, err := s.orgSvcClient.GetFormConfigurationFieldsByLegacyProviderId(
		ctx,
		providerId,
		"",
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("GetFormConfigurationFieldsByLegacyProviderId failed")
		return "", err
	}
	if mrnField, exists := configurationMap["mrn"]; exists && mrnField.Value.(bool) {
		return "MRN", nil
	} else if ohipField, exists := configurationMap["ohip"]; exists && ohipField.Value.(bool) {
		return "OHIP", nil
	} else if ipnField, exists := configurationMap["ipn"]; exists && ipnField.Value.(bool) {
		return "IPN", nil
	} else if ssnField, exists := configurationMap["ssn"]; exists && ssnField.Value.(bool) {
		return "SSN", nil
	} else if bcphnField, exists := configurationMap["bc_phn"]; exists && bcphnField.Value.(bool) {
		return "BC PHN", nil
	}
	return "", nil
}

// getAccountProviderLanguageTag get the language for an account based on the provider context
func (s *RequestsApiService) getAccountProviderLanguageTag(
	ctx context.Context,
	accountId string,
	providerId int64,
) languageTag.Tag {
	return phlanguage.Select(ctx, s.supportedLanguages).
		WithAccountId(s.languageTagProviders.AccountId, accountId).
		WithOrganization(s.languageTagProviders.OrgId, providerId).
		GetLanguageTag()
}

// handleVerificationPendingDuplicateRequestEmail handles sending duplicate request emails for requests that are pending and requires identity verification
func (s *RequestsApiService) handleVerificationPendingDuplicateRequestEmail(
	ctx context.Context,
	email string,
	requestId int64,
	language languageTag.Tag,
	duplicateRequestEmail PatientRequestEmail,
	accountId string,
) {
	rId := strconv.FormatInt(requestId, 10)
	err := s.roiSvcClient.ProcessDuplicateVerification(ctx, rId, email)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Info("error processing duplicate verification in roisvc")
		// if duplicate request that needs verification fails, default to normal pending duplicate email processing
		s.handlePendingDuplicateRequestEmail(ctx, email, language, duplicateRequestEmail, accountId)
	}
}

// handlePendingDuplicateRequestEmail handles sending duplicate request emails for requests that are pending
func (s *RequestsApiService) handlePendingDuplicateRequestEmail(
	ctx context.Context,
	email string,
	language languageTag.Tag,
	duplicateRequestEmail PatientRequestEmail,
	accountId string,
) {
	s.sendDuplicateEmail(
		ctx,
		s.sqldb,
		s.i18nBundle,
		language,
		s,
		email,
		duplicateRequestEmail,
		accountId,
	)
}

// handleRejectedDuplicateRequest handles sending reject emails for a request that has been rejected and a user needs to verify details.
// This function resends the rejected email that providers service sends
func (s *RequestsApiService) handleRejectedDuplicateRequestEmail(
	ctx context.Context,
	email string,
	requestId int64,
	clinicName string,
	accountId string,
	isAcctPassSet bool,
) {
	token := ""
	if !isAcctPassSet {
		verificationToken, err := s.acctSvcClient.GetPasswordSetupVerificationToken(ctx, accountId)
		if err != nil {
			logutils.CtxLogger(ctx).WithError(err).Error("failed to retrieve password setup token")
		}
		token = verificationToken.Token
	}
	s.sendRejectedEmail(
		ctx,
		s.cioEmailer,
		email,
		requestId,
		clinicName,
		accountId,
		isAcctPassSet,
		token,
	)
}

// handleEnrolleddDuplicateRequest handles sending already enrolled emails for a request that exists and has been completed (assume account is enrolled with provider).
// This function sends an email similar to patient confirmation email, with language about repeating a request
func (s *RequestsApiService) handleEnrolleddDuplicateRequestEmail(
	ctx context.Context,
	email string,
	accountId string,
	clinicName string,
) {
	s.sendAlreadyEnrolledEmail(ctx, s.cioEmailer, email, accountId, clinicName)
}

func sendDuplicateEmail(
	ctx context.Context,
	db *sql.DB,
	i18nBundle *i18n.Bundle,
	language languageTag.Tag,
	s *RequestsApiService,
	emailAddr string,
	duplicateRequestEmail PatientRequestEmail,
	accountId string,
) {
	err := s.cioEmailer.Send(
		ctx,
		emailAddr,
		accountId,
		cio_email.REQUEST_DUPLICATE,
		map[string]any{
			"RequestID": duplicateRequestEmail.RequestID,
			"RegionID":  duplicateRequestEmail.RegionID,
			"IsUPH":     duplicateRequestEmail.IsUPH,
		},
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Info("failed to send duplicate request email")
	}

	logutils.DebugCtxLogger(ctx).
		Infof("sent duplicate request successfully for request ID %d", duplicateRequestEmail.RequestID)
}

func sendRejectedEmail(
	ctx context.Context,
	mailer cio_email.CIOEMailer,
	emailAddr string,
	requestId int64,
	clinicName string,
	accountId string,
	hasAccount bool,
	setupToken string,
) {
	err := mailer.Send(ctx, emailAddr, accountId, cio_email.REQUEST_REJECTED, map[string]interface{}{
		"request_id":  requestId,
		"region_id":   regions.GetRegionID(),
		"clinic_name": clinicName,
		"has_account": hasAccount,
		"setup_token": setupToken,
	})
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("request_id", requestId).
			WithError(err).
			Error("failed to send rejected request email")
	}
}

func sendAlreadyEnrolledEmail(
	ctx context.Context,
	mailer cio_email.CIOEMailer,
	emailAddr string,
	accountId string,
	clinicName string,
) {
	err := mailer.Send(ctx, emailAddr, accountId, cio_email.ALREADY_ENROLLED, map[string]interface{}{
		"clinic_name": clinicName,
		"region_id":   regions.GetRegionID(),
	})
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("failed to send already enrolled request email")
	}
}

func (s *RequestsApiService) sendDelegateReviewEmail(
	ctx context.Context,
	requestId int64,
	orgName string,
) error {
	err := s.cioEmailer.Send(ctx, s.supportEmailAddress, "", cio_email.DELEGATE_REVIEW, map[string]interface{}{
		"request_id": strconv.FormatInt(requestId, 10), "org_name": orgName,
	})
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("failed to send delegate review email")
	}

	return err
}

// checkEligibleIncompleteRequest checks whether the user should be sent reminder emails about their incomplete request.
// this includes checking if they have been enrolled w/ the clinic, have a request w/ the clinic, or have an un-expired incomplete request with the same clinic
func (s *RequestsApiService) checkEligibleIncompleteRequest(
	ctx context.Context,
	db *sql.DB,
	email string,
	clinicId int64,
	providerLegacyId int64,
) (enable bool, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("clinicId", clinicId)

	exists, err := incompleteRequests.ValidIncompleteRequestExistsByEmailAndClinic(
		ctx,
		db,
		email,
		clinicId,
	)
	if err != nil {
		lg.WithError(err).Error("unable to check if valid incomplete request exists for email")
		return false, err
	}
	if exists {
		lg.Info("incomplete request exists for email")
		return false, nil
	}
	acct, err := s.acctSvcClient.GetAccountInfoByEmail(ctx, email)
	// if no account, they are eligible (no point checking requests/enrols, if acct doesn't exist they won't either)
	if err != nil || acct == nil {
		lg.WithError(err).Error("couldn't get acct for email")
		return true, nil
	}

	hasRequested, err := s.checkEnrolledOrRequestExists(ctx, db, acct.AccountId, providerLegacyId)
	return !hasRequested, err
}

func (s *RequestsApiService) checkEnrolledOrRequestExists(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	providerLegacyId int64,
) (result bool, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("providerId", providerLegacyId)

	enrolled, err := enrollments.IsAcctEnrolled(ctx, db, acctId, providerLegacyId)
	if err != nil {
		lg.WithError(err).Error("unable to check if acct is enrolled with clinic")
		return false, err
	}
	if enrolled {
		lg.Info("acct is enrolled with clinic")
		return true, nil
	}

	exists, err := requests.ExistsForAccountAndOrg(ctx, db, acctId, providerLegacyId)
	if err != nil {
		lg.WithError(err).Error("unable to check if requests exists for acct")
		return false, err
	}
	if exists {
		lg.Info("request exists for acct")
		return true, nil
	}

	return false, nil
}

func (s *RequestsApiService) setRequestStatusToNull(
	ctx context.Context,
	requestId int64,
) (string, error) {
	// set up the status to null, so prov svc could pick it up
	lg := logutils.DebugCtxLogger(ctx).WithField("request_id", requestId)
	err := requests.SetRequestStatus(ctx, s.sqldb, "", requestId)
	if err != nil {
		lg.WithError(err).Info("finalize request status failed")
		return "", err
	}
	return requests.SUBMISSION_COMPLETE, nil
}

// Check if an account exists for the user in AccountSvc, create an account if not and add the patient to that account.
//
//   - If it does have an account, AND a user for this region, return those values
//   - If it does have an account, but no user for the region, create that user and return
//   - If the request contains a delgeate then we should try to add them as the account owner
//     then create the patient and return them.
//   - If the request includes a delegate and the account was successfully create but the patient
//     was not able to added to the account then we return the account information with that error.
//   - Otherwise, if no account exists, setup a user/profile and acct svc entry given request data
func (s *RequestsApiService) upsertAccountAndPatientForRequest(
	ctx context.Context,
	acctId string,
	req models.Request,
	language string,
	deviceId string,
	acctCreationSource string,
	ptCreationSource string,
) (*accountservice.Account, string, bool, error) {
	lg := logutils.DebugCtxLogger(ctx)

	patientProfile := coreapi.UserProfile{
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		DOB:         req.Dob,
		AltId:       req.AltId,
		AltLastName: req.AltLastName,
		Ohip:        req.Ohip,
		Ohipvc:      req.Ohipvc,
		Mrn:         req.Mrn,
		Ipn:         req.Ipn,
		Ssn:         req.Ssn,
		PatientId:   req.PatientId,
	}

	delegate := req.Contents.Delegate
	if delegate == nil {
		patientProfile.Email = req.Email
		patientProfile.Phone = req.Tel

		return accounts.GetOrCreateAccountIds(
			ctx,
			s.acctSvcClient,
			s.sqldb,
			acctId,
			req.Email,
			patientProfile,
			language,
			deviceId,
			acctCreationSource,
			ptCreationSource,
		)
	}

	delegateProfile := coreapi.UserProfile{
		FirstName: delegate.FirstName,
		LastName:  delegate.LastName,
		DOB:       delegate.Dob,
		Phone:     delegate.Phone,
		Email:     req.Email,
	}

	account, _, isNewAccount, err := accounts.GetOrCreateAccountIds(
		ctx,
		s.acctSvcClient,
		s.sqldb,
		acctId,
		req.Email,
		delegateProfile,
		language,
		deviceId,
		acctCreationSource,
		ptCreationSource,
	)
	if err != nil {
		lg.WithField("account_id", acctId).
			WithError(err).Error("Could not create account for delegate")
		return nil, "", false, err
	}

	lg = lg.WithField("account_id", account.AccountId)

	patientId, err := s.acctSvcClient.GetOrCreatePatient(
		ctx,
		account.AccountId,
		accountservice.Patient{
			Email:       account.Email,
			FirstName:   patientProfile.FirstName,
			LastName:    patientProfile.LastName,
			AltLastName: patientProfile.AltLastName,
			DOB:         req.FormatDob(),
			Phone:       patientProfile.Phone,
			Sex:         patientProfile.Sex,
			Source:      ptCreationSource,
		},
		false,
	)
	if err != nil {
		lg.
			WithError(err).
			Error("Could not create patient for delegate account")
		// We need to return the new account so that we can send them a verification link even
		// f we failed to add the patient.
		return account, "", isNewAccount, err
	}

	// only insert phi for patient
	res := phiHelpers.GetPhiInfoFromUserProfile(patientId, &patientProfile)
	err = phi.AddPhiProfiles(ctx, s.sqldb, res)
	if err != nil {
		lg.WithError(err).Error("failed to write phi_profile")
		// phi profiles aren't detrimental, continue
	}

	return account, patientId, isNewAccount, nil
}

func (s *RequestsApiService) hasActiveSubscription(
	ctx context.Context,
	accountId string,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", accountId)
	orders, err := s.acctSvcClient.GetOrders(ctx, accountId, map[string]bool{
		"active":    true,
		"recurring": true,
	})
	if err != nil {
		lg.WithError(err).Error("failed to get active recurring orders")
		return false, err
	}
	hasSub := false
	// TODO Toks/Bhavik - add new basic plan to this check
	for _, order := range orders {
		if order.PlanId == accountservice.FLEX_CA_PLAN ||
			order.PlanId == accountservice.FLEX_US_PLAN ||
			order.PlanId == accountservice.UNLIMITED_CA_PLAN ||
			order.PlanId == accountservice.UNLIMITED_US_PLAN {
			hasSub = true
			break
		}
	}
	return hasSub, nil
}

func (s *RequestsApiService) identifyRequestAccount(
	ctx context.Context,
	acctId string,
	deviceID string,
	isDelegate bool,
) {
	acct, err := s.acctSvcClient.GetAccountInfo(ctx, acctId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("unable to get acct by id to create order")
		return
	}

	// 1. Upload CIO profile attributes depending on the state of most recent request
	payload := map[string]any{"last_request_date": time.Now().Unix()}
	if isDelegate {
		payload["delegate"] = "true"
	}
	err = s.cioEventProducer.UpdateUserAttributes(
		acct.AccountId,
		acct.Email,
		payload,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("unable to update CIO profile with request submission attributes")
	}

	// 2. Update amplitude attributes
	//    TODO: this is currently flawed as it's updated on each request submission...
	//          we lose information on the actual time of account creation/pay-later
	s.ampliClient.Track(amplitude.Event{
		EventType: "$identify",
		UserID:    acctId,
		DeviceID:  deviceID,
		// date in format YYYY-MM-DDThh:mm:ss e.g. 2022-08-17T15:36:42
		UserProperties: map[amplitude.IdentityOp]map[string]interface{}{
			"$set": {
				"account_created_date": acct.CreatedAt[:len(acct.CreatedAt)-1],
			},
		},
	})
}

func (s *RequestsApiService) logRequestProcessedEvent(
	deviceId string,
	accountId string,
	orgId int64,
	clinicId int64,
	revenue int,
	currency string,
	priorPlanId int32,
	planId int32,
) {
	s.ampliClient.Track(amplitude.Event{
		EventType: "request processed",
		UserID:    accountId,
		DeviceID:  deviceId,
		// date in format YYYY-MM-DDThh:mm:ss e.g. 2022-08-17T15:36:42
		EventProperties: map[string]interface{}{
			"org_id":        orgId,
			"clinic_id":     clinicId,
			"$revenue":      revenue,
			"currency":      currency,
			"prior_plan_id": priorPlanId,
			"plan_id":       planId,
		},
	})
}

func (s *RequestsApiService) updatePatientUponResubmit(
	ctx context.Context,
	tx *sql.Tx,
	requestId int64,
	accountId string,
	requestBody models.Request,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id": requestId,
		"account_id": accountId,
	})

	ptId, err := requests.GetRequestPatientId(ctx, s.sqldb, requestId, accountId)
	if err != nil {
		lg.WithError(err).Info("error getting request patient id")
		return err
	}

	if ptId != "" {
		reqCount, err := requests.GetNumRequestsByPatientId(ctx, s.sqldb, accountId, ptId)
		if err != nil {
			lg.WithError(err).Info("error getting number of requests by patient id")
			// assume first request and continue
		}
		if reqCount > 1 {
			// don't update patient info if this is not their first
			return nil
		}
	}

	// requests have mm/dd/yyyy dob formats
	var dobStr string
	dob, err := time.Parse("01/02/2006", requestBody.Dob)
	if err != nil {
		lg.WithField("dob", requestBody.Dob).WithError(err).Error("invalid dob for patient")
		return err
	}
	dobStr = dob.Format("2006-01-02")

	if ptId == "" {
		// no patient id is attached to request
		// odd case, but it may happen? create a new patient or try to get the existing patient that matches info
		newPtId, err := s.acctSvcClient.GetOrCreatePatient(
			ctx,
			accountId,
			accountservice.Patient{
				Email:       requestBody.Email,
				FirstName:   requestBody.FirstName,
				LastName:    requestBody.LastName,
				AltLastName: requestBody.AltLastName,
				DOB:         dobStr,
				Phone:       requestBody.Tel,
				Source:      accountservice.PATIENT_CREATION_REQUEST,
			},
			false,
		)
		lg = lg.WithField("patient_id", newPtId)

		if err != nil {
			lg.WithError(err).Info("error getting or creating patient id")
			return err
		}

		err = requests.SetPatientIdTx(ctx, tx, requestId, newPtId)
		if err != nil {
			lg.WithError(err).Info("error setting patient id for request")
			return err
		}

		return nil
	}

	if requestBody.PatientId != "" && ptId != requestBody.PatientId {
		lg.Warn("db-stored patient id is different from passed in patient id")
	}

	err = s.acctSvcClient.UpdatePatient(ctx, accountId, ptId, accountservice.Patient{
		FirstName:   requestBody.FirstName,
		LastName:    requestBody.LastName,
		AltLastName: requestBody.AltLastName,
		DOB:         dobStr,
		Phone:       requestBody.Tel,
	})
	if err != nil {
		lg.WithField("patient_id", ptId).WithError(err).Info("error updating patient with updated request data")
		return err
	}

	return nil
}

// Create an account (if necessary) and add the request patient to it. If there is a delegate in the request then they will be
// made the account owner. If an account is created then send them a verification code, even if the patient fails to get added.
func (s *RequestsApiService) linkRequestToAccount(
	ctx context.Context,
	req models.CreateRequest,
	language string,
) (*accountservice.Account, string, bool, string, error) {
	lg := logutils.DebugCtxLogger(ctx)

	account, patientId, isNewAccount, err := s.upsertAccountAndPatientForRequest(
		ctx,
		req.AccountId,
		req.RequestDetails,
		language,
		req.DeviceId,
		accountservice.REQUEST_ACCOUNT_CREATION,
		accountservice.PATIENT_CREATION_REQUEST,
	)

	if isNewAccount {
		s.ampliClient.Track(amplitude.Event{
			EventType: "account created",
			UserID:    account.AccountId,
			DeviceID:  req.DeviceId,
			EventProperties: map[string]interface{}{
				"account_creation_source": "request form",
			},
		})
	}

	var token string
	// If it's an Out Of App request then we always want to generte a token, so we don't leak if the account
	// already exsits. So as long as there is an account send the code - even if there is an error because
	// that probablys means the delegate was added as the account owner but we failed to add the patient
	// from the request or something like that.
	if !req.InAppRequest && account != nil {
		token, err = s.acctSvcClient.PostAccountVerificationCode(ctx, account.AccountId)
		if err != nil {
			lg.WithError(err).
				Errorf("unable to get token for account verification for accountId: %s", account.AccountId)
			return nil, "", false, "", err
		}
	}

	return account, patientId, isNewAccount, token, err
}

// Find the Clinic and Provider for the legacy Provider Id in a Request
func (s *RequestsApiService) clinicAndProviderForProviderLegacyId(
	ctx context.Context,
	legacyId int64,
) (orgservice.Clinic, orgservice.Provider, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"provider_id": legacyId,
	})

	provider, err := s.getProviderByLegacyId(ctx, legacyId)
	if err != nil {
		lg.WithError(err).
			Infof("error retrieving provider info for provider legacyID: %d", legacyId)
		return orgservice.Clinic{}, orgservice.Provider{}, errors.New("cannot find request provider info")
	}
	lg.WithFields(logrus.Fields{"providerId": provider.LegacyId}).
		Info("new request provider identifier")

	// retrieve clinic info
	clinics := s.orgSvcClient.GetClinicsByProviderId(ctx, provider.Id)
	if len(clinics) == 0 {
		lg.Warnf("no clinics found for provide ID: %s", provider.Id)
		return orgservice.Clinic{}, orgservice.Provider{}, fmt.Errorf(
			"no clinics found for provider: %s",
			provider.Name,
		)
	}

	// set clinic to first clinic from returned list
	// TODO: repurpose HubClinic function for more stable/deterministic logic
	clinic := clinics[0]
	lg.WithFields(logrus.Fields{"clinicId": clinic.LegacyId}).Info("new request clinic identifier")

	return clinic, provider, nil
}
