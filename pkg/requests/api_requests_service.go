/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package requests

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"

	"golang.org/x/text/language"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	incompleteRequestBlobs "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequestblobs"
	incompleteRequests "gitlab.com/pockethealth/coreapi/pkg/mysql/incompleterequests"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	"gitlab.com/pockethealth/coreapi/pkg/orders"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/amplitude_util"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
)

// RequestsApiService is a service that implents the logic for the RequestsApiServicer
// This service should implement the business logic for every endpoint that requires authentication for the RequestsApi API.
// Include any external packages or services that will be required by this service.
type RequestsApiService struct {
	sqldb             *sql.DB
	containerClient   azureUtils.ContainerClient
	i18nBundle        *i18n.Bundle
	hostName          string
	frontEndHost      string
	acctSvcClient     accountservice.AccountService
	planSvcClient     planservice.PlanService
	orgSvcClient      orgservice.OrgService
	ampliClient       interfaces.AmplitudeEventClient
	waitGroup         *sync.WaitGroup
	roiSvcClient      roiservice.RoiService
	recordSvcClient   recordservice.RecordServiceClientInterface
	experimentsClient interfaces.AmplitudeExperimentClient
	cioEventProducer  interfaces.CIOProducerClient
	// service bus
	RequestsTopic              pubsub.Topic
	PatientEnrollmentTopic     pubsub.Topic
	ServiceBusQueue            pubsub.Queue
	accountCreatedRequestQueue string
	requestStatusQueue         string

	// injected functions (so we can mock them when testing)
	sendDuplicateEmail       SendDuplicateEmail
	sendRejectedEmail        SendRejectedEmail
	sendAlreadyEnrolledEmail SendAlreadyEnrolledEmail
	languageTagProviders     languageproviders.LanguageTagProviders
	supportedLanguages       map[string]string
	cioEmailer               cio_email.CIOEMailer

	// region info
	region string

	excludeSoftDeletedRequests bool
	pocketHealthAppUrl         string

	supportEmailAddress string
}

// NewRequestsApiService creates a default api service
func NewRequestsApiService(
	mysqlDB *sql.DB,
	cc azureUtils.ContainerClient,
	i18nBundle *i18n.Bundle,
	langProviders languageproviders.LanguageTagProviders,
	supportedLanguages map[string]string,
	hostName string,
	frontEndHost string,
	acctSvcUser accountservice.AccountService,
	planSvcClient planservice.PlanService,
	orgSvcClient orgservice.OrgService,
	amplitudeClient interfaces.AmplitudeEventClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
	cioEventProducer interfaces.CIOProducerClient,
	waitGroup *sync.WaitGroup,
	roiSvcClient roiservice.RoiService,
	recordSvcClient recordservice.RecordServiceClientInterface,
	cioEmailer cio_email.CIOEMailer,
	serviceBusConnString string,
	serviceBusName string,
	acctRequestCreatedQueue string,
	region string,
	excludeDeletedRequests bool,
	pocketHealthAppUrl string,
	requestStatusQueue string,
	supportAddr string,
) coreapi.RequestsApiServicer {
	return &RequestsApiService{
		sqldb:                    mysqlDB,
		containerClient:          cc,
		i18nBundle:               i18nBundle,
		hostName:                 hostName,
		frontEndHost:             frontEndHost,
		sendDuplicateEmail:       sendDuplicateEmail,
		sendRejectedEmail:        sendRejectedEmail,
		sendAlreadyEnrolledEmail: sendAlreadyEnrolledEmail,
		languageTagProviders:     langProviders,
		supportedLanguages:       supportedLanguages,
		acctSvcClient:            acctSvcUser,
		planSvcClient:            planSvcClient,
		orgSvcClient:             orgSvcClient,
		ampliClient:              amplitudeClient,
		experimentsClient:        experimentsClient,
		cioEventProducer:         cioEventProducer,
		waitGroup:                waitGroup,
		roiSvcClient:             roiSvcClient,
		recordSvcClient:          recordSvcClient,
		RequestsTopic: azureUtils.NewAZTopicWithConnectionString(
			serviceBusConnString,
			waitGroup,
			"requests",
		),
		PatientEnrollmentTopic: azureUtils.NewAZTopicWithConnectionString(
			serviceBusConnString,
			waitGroup,
			"patient-enrollment",
		),
		cioEmailer:                 cioEmailer,
		region:                     region,
		excludeSoftDeletedRequests: excludeDeletedRequests,
		ServiceBusQueue: azureUtils.NewServiceBusQueue(
			serviceBusConnString,
			serviceBusName,
		),
		accountCreatedRequestQueue: acctRequestCreatedQueue,
		pocketHealthAppUrl:         pocketHealthAppUrl,
		requestStatusQueue:         requestStatusQueue,
		supportEmailAddress:        supportAddr,
	}
}

// GetRequestStatusHistory get all request status history records for a specified request ID
func (s *RequestsApiService) GetRequestStatusHistory(
	ctx context.Context,
	requestId string,
) []roiservice.RequestStatusHistory {
	lg := logutils.DebugCtxLogger(ctx).WithField("request_id", requestId)
	if requestId == "" {
		lg.Warn("request identifier is invalid")
		return []roiservice.RequestStatusHistory{}
	}
	return s.roiSvcClient.GetRequestStatusHistory(requestId, roiservice.WithSetContext(ctx))
}

// GetIdVerificationConfigs - Get all id verification configuration for roisvc
func (s *RequestsApiService) GetIdVerificationConfigs(ctx context.Context) interface{} {
	return s.roiSvcClient.GetVerificationConfigs(roiservice.WithSetContext(ctx))
}

// GetRequests - Get requests
func (s *RequestsApiService) GetRequests(ctx context.Context, acctId string) (interface{}, error) {
	pendingRequests, err := requests.GetPendingRequests(
		ctx,
		s.sqldb,
		acctId,
		s.orgSvcClient,
		s.excludeSoftDeletedRequests,
	)
	if err != nil {
		return nil, err
	}

	// If any pending request is "SUPPRESS", it could be for a record
	// streaming request. If there are any record streaming instances
	// uploaded from that request, then it is not considered pending and
	// will not be returned.
	if pendingRequests.HasRequestWithStatus(requests.SUPPRESS) {
		filteredPendingRequests := []models.PendingRequest{}
		recordUploadStatuses, err := s.recordSvcClient.GetPatientStudiesWithUploadStatus(
			ctx,
			acctId,
		)
		if err != nil {
			return false, err
		}
		for _, pendingRequest := range pendingRequests {
			if pendingRequest.Status != requests.SUPPRESS ||
				!recordUploadStatuses.InstancesFoundForPatient(
					pendingRequest.OrgId,
					pendingRequest.PatientId,
				) {
				filteredPendingRequests = append(filteredPendingRequests, pendingRequest)
			}
		}
		return filteredPendingRequests, nil
	}
	return pendingRequests, nil
}

// AddRequest creates a new request, processes payment and processes plus stores request consent documents
func (s *RequestsApiService) AddRequest(
	ctx context.Context,
	data models.CreateRequest,
	language string,
) (consentFile []byte, id int64, verificationToken string, idVerificationLink string, err error) {
	lg := logutils.DebugCtxLogger(ctx)

	if !data.RequestDetails.IsValid() {
		return nil, -1, "", "", errors.New(errormsgs.ERR_DATA_NOT_VALID)
	}
	isDelegate := len(data.DelegPhotoId) > 0

	clinic, provider, err := s.clinicAndProviderForProviderLegacyId(ctx, data.RequestDetails.OrgId)
	if err != nil {
		lg.WithError(err).
			WithField("org_id", data.RequestDetails.OrgId).
			Error("Failed to find clinic and provider for request org id")
		return nil, -1, "", "", err
	}

	// get request verification config for provider
	rvc, _ := s.roiSvcClient.GetVerificationConfig(ctx, provider.LegacyId)
	var account *accountservice.Account
	var patientId string
	var isNewAccount bool
	var token string

	// TODO: Once we are fully over to the new delegate request flow delete this
	createDelegateAsAccountOwner := false
	formData, err := s.orgSvcClient.GetFormByLegacyProviderId(
		ctx,
		provider.LegacyId,
		data.InAppRequest,
		"",
	)
	if err != nil {
		lg.WithError(err).
			WithField("org_id", data.RequestDetails.OrgId).
			Error("Failed to get form for provider")
	} else {
		// Temporary magic number that we use to determine whether we should be making the delegate the account owner or not.
		createDelegateAsAccountOwner = formData.GraphId > 18
	}

	if createDelegateAsAccountOwner {
		// Create new account in acct svc, and send verification code for an outApp request.
		account, patientId, isNewAccount, token, err = s.linkRequestToAccount(
			ctx,
			data,
			language,
		) // Improvement - add has active sub bool to account model
	} else {
		req := data.RequestDetails
		patientProfile := coreapi.UserProfile{
			FirstName:   req.FirstName,
			LastName:    req.LastName,
			DOB:         req.Dob,
			AltId:       req.AltId,
			AltLastName: req.AltLastName,
			Ohip:        req.Ohip,
			Ohipvc:      req.Ohipvc,
			Mrn:         req.Mrn,
			Ipn:         req.Ipn,
			Ssn:         req.Ssn,
			PatientId:   req.PatientId,
			Email:       req.Email,
			Phone:       req.Tel,
		}
		account, patientId, isNewAccount, err = accounts.GetOrCreateAccountIds(
			ctx,
			s.acctSvcClient,
			s.sqldb,
			data.AccountId,
			req.Email,
			patientProfile,
			language,
			data.DeviceId,
			accountservice.REQUEST_ACCOUNT_CREATION,
			accountservice.PATIENT_CREATION_REQUEST,
		)
		if !data.InAppRequest && account != nil {
			token, err = s.acctSvcClient.PostAccountVerificationCode(ctx, account.AccountId)
			if err != nil {
				lg.WithError(err).
					Errorf("unable to get token for account verification for accountId: %s", account.AccountId)
				return nil, -1, "", "", err
			}
		}
		if isNewAccount {
			s.ampliClient.Track(amplitude.Event{
				EventType: "account created",
				UserID:    account.AccountId,
				DeviceID:  data.DeviceId,
				EventProperties: map[string]interface{}{
					"account_creation_source": "request form",
				},
			})
		}
	}

	if err != nil {
		lg.WithError(err).Info("acctsvc unable to handle create account and patient")
		return nil, -1, "", "", errors.New("cannot create account and patient for request")
	}

	lg.WithFields(logrus.Fields{
		"acctId":   account.AccountId,
		"pt_id":    patientId,
		"language": language,
	}).Info("add new request processing account params")

	// process order and payment if information passed
	if data.PaymentDetails.PlanId != 0 {
		err = orders.ProcessPayment(
			ctx,
			lg,
			s.acctSvcClient,
			s.planSvcClient,
			account,
			data.PaymentDetails,
			provider.LegacyId,
		)
		if err != nil {
			lg.WithError(err).Error("failed to process order")
			return nil, -1, "", "", err
		}
	}

	// check for duplicate request
	isDuplicate, duplicateRequestId, duplicateStatus := s.checkRequestDuplicateStatus(
		ctx,
		provider,
		account.AccountId,
		patientId,
		data,
	)
	isDuplicate = isDuplicate && provider.EnrollmentOrg
	if isDuplicate {
		lg.WithFields(logrus.Fields{
			"request_id":     duplicateRequestId,
			"request_status": duplicateStatus,
		},
		).Info("duplicate request detected")
		// Duplicate Request Email
		duplicateRequestEmail := PatientRequestEmail{
			RequestID:      duplicateRequestId,
			IncludeReceipt: false,
			IsUPH:          provider.IsUph,
		}

		if token == "" {
			s.processDuplicateNotification(
				ctx,
				data.RequestDetails.Email,
				language,
				clinic.Name,
				duplicateStatus,
				rvc,
				account,
				duplicateRequestId,
				provider.LegacyId,
				data.InAppRequest,
				duplicateRequestEmail,
			)
		}

		return nil, 0, token, "", fmt.Errorf("duplicate request detected")
	}
	// setup request status based on specific conditions
	requestStatus := requests.UNFINALIZED
	if !provider.IsUph && provider.DelegateReview && isDelegate &&
		data.RequestDetails.Contents.Delegate.RelationType != models.FAMILY_MEMBER {
		requestStatus = requests.DELEGATE_PENDING_AUTH
	}

	// create new request
	requestId, err := requests.Create(
		ctx,
		s.sqldb,
		clinic.LegacyId,
		provider.LegacyId,
		data.RequestDetails,
		account.AccountId,
		patientId,
		requestStatus,
	)
	if err != nil {
		lg.WithError(err).Info("failed to create request")
		return nil, -1, "", "", errors.New("unable to create request")
	}
	lg.WithField("request_id", requestId).Info("new request created identifier")

	// process consent and store files
	file, err := s.processConsentDocuments(ctx, requestId, data, provider, language)
	if err != nil {
		// still publish event to ensure request history is recorded
		s.publishRequestStatusEvent(ctx, requestId, requestStatus)
		lg.WithError(err).Error("failed to process consent documents")
		return nil, -1, "", "", err
	}
	// setup patient request email params
	patientRequestEmail := PatientRequestEmail{
		RequestID:      requestId,
		IncludeReceipt: false,
		IsUPH:          provider.IsUph,
	}

	// TODO Toks: move below status setting to initial request creation
	// set request status based on IDV config & request delegate params
	if rvc.Active {
		err := requests.SetRequestStatus(ctx, s.sqldb, requests.VERIFICATION_PENDING, requestId)
		if err != nil {
			lg.WithError(err).Info("unable to set request IDV status")
		} else {
			requestStatus = requests.VERIFICATION_PENDING
		}
	} else {
		// send PX email if request status is DELEGATE_PENDING_AUTH
		if requestStatus == requests.DELEGATE_PENDING_AUTH {
			err = s.sendDelegateReviewEmail(ctx, requestId, provider.Name)
			if err != nil {
				logutils.Infox(ctx, "error sending Delegate Review Email to PX")
			}
		} else {
			// set up the status to null, so provider service / deployed gateway can pick it up for processing
			// Note: Always set status to null in the end of the request to avoid picking up the request during processing
			resultStatus, _ := s.setRequestStatusToNull(ctx, requestId)
			if resultStatus != "" {
				requestStatus = resultStatus
			}
		}
	}

	var verificationLink string

	if !data.InAppRequest {
		// TODO: Delete this once the new delegate flow is the default
		toEmail := data.RequestDetails.Email
		if data.RequestDetails.Contents.Delegate != nil {
			toEmail = setNotificationEmailForDelegateRequest(
				data.RequestDetails.Email,
				data.RequestDetails.Contents.Delegate,
				createDelegateAsAccountOwner,
			)
		}
		// if new account, publish to queue for acctsvc processing
		// else send continue to account email
		verificationLink = s.processOutappRequestSubmittedNotification(
			isNewAccount,
			requestId,
			account.AccountId,
			toEmail,
			language,
			rvc,
			provider.LegacyId,
			patientRequestEmail,
			token,
		)
	} else {
		go s.processInappRequestSubmittedNotification(
			patientRequestEmail,
			data.RequestDetails,
			requestId,
			account.AccountId,
			provider.LegacyId,
			rvc,
			createDelegateAsAccountOwner,
		)
	}
	// complete incomplete requests
	go s.completeIncompleteRequests(requestId, data.RequestDetails.Email)

	// amplitude tracking TODO Toks - refactor analytics tracking when payment processing is removed from reques processing
	s.requestAnalyticsTracking(
		ctx,
		false, // TODO: paymentCompleted this will always be false based on existing logic after removing defer payment
		int32(
			0,
		), // TODO: int32(priorPlanId) this will always be 0 based on existing logic after removing defer payment
		int32(data.PaymentDetails.PlanId), // #nosec G115 not worrying about max_int32
		account.AccountId,
		provider.LegacyId,
		clinic.LegacyId,
		data.RequestDetails.Email,
		data.DeviceId,
		isDelegate,
	)
	// enqueue request metadata to queue for downstream consumption
	s.publishRequestMetadataEvent(
		ctx,
		requestId,
		account.AccountId,
		provider.LegacyId,
		clinic.LegacyId,
		provider.IsUph,
	)
	// publish request creation + request status events
	s.requestSubmissionPublishEvents(
		ctx,
		requestId,
		account.AccountId,
		provider.Id,
		clinic.Id,
		requestStatus,
	)
	if requestStatus == requests.SUBMISSION_COMPLETE {
		// publish patient enrollment event
		s.patientEnrollmentPublishEvents(
			ctx,
			requestId,
			provider.LegacyId,
			account.AccountId,
			data.RequestDetails,
		)
	}
	logutils.Infox(ctx, "add new request processed successfuly")
	// check for delegate request that is made by a family member that is a patient
	if data.RequestDetails.Contents.Delegate != nil {
		delegateExpiryCondition := provider.EnrolExpiryAge != 0 && !provider.IsUph && isDelegate
		isParent := strings.ToLower(data.RequestDetails.Contents.Delegate.Relation) == "parent"
		if delegateExpiryCondition &&
			data.RequestDetails.Contents.Delegate.RelationType == models.FAMILY_MEMBER &&
			isParent {
			requestDob := data.RequestDetails.Dob
			_, err := time.Parse("01/02/2006", requestDob)
			if err == nil {
				s.publishRequestDelegateExpiryEvent(
					ctx,
					requestId,
					account.AccountId,
					provider.Id,
					patientId,
					requestDob,
				)
			}
		}
	}

	if !data.InAppRequest {
		return file, requestId, token, verificationLink, nil
	}

	return file, requestId, "", "", nil
}

// Post verify rejected email
func (s *RequestsApiService) PostVerifyRejectedEmail(
	ctx context.Context,
	acctId string,
	requestId int64,
	rejectDetails coreapi.RejectVerifyDetails,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithField("acct_id", acctId).
		WithField("request_id", requestId)
	// confirm request belongs to acct
	auth, err := requests.BelongsToAcct(ctx, s.sqldb, acctId, requestId)
	if err != nil {
		lg.WithError(err).Error("unable to check if request belongs to user")
		return err
	}
	if !auth {
		return errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	// read request values used to populate email fields, with defaults
	request, err := requests.GetRequestById(ctx, s.sqldb, requestId)
	if err != nil {
		lg.WithError(err).Error("unable to get request info")
		return err
	}

	// details of clinic originally requested from
	clinicId := request.ProviderId // see comment in model_request.go
	clinic, err := s.getClinicByLegacyId(ctx, clinicId)
	if err != nil {
		return err
	}
	originalReqOrg := clinic.ProviderName
	originalReqClinic := clinic

	// has unlimited
	unlimited := false
	activeOrders, err := s.acctSvcClient.GetOrders(ctx, acctId, map[string]bool{
		"active":    true,
		"recurring": true,
	})
	if err != nil {
		// just send as false
	} else if len(activeOrders) > 0 &&
		(activeOrders[0].PlanId == accountservice.UNLIMITED_CA_PLAN ||
			activeOrders[0].PlanId == accountservice.UNLIMITED_US_PLAN) {
		unlimited = true
	}

	newOrgFound := false
	locationChange := false
	newClinic := orgservice.Clinic{}
	if !rejectDetails.ProviderConfirmed {
		// provider was changed, get details of the changed provider

		if rejectDetails.ProviderId > 0 {
			newClinic, err = s.getClinicByLegacyId(
				ctx,
				int64(rejectDetails.ProviderId),
			) // #nosec G115 provider ID > 0
			if err != nil {
				return err
			}
			newOrgFound = true
			if newClinic.ProviderId == originalReqClinic.ProviderId {
				// Org is the same, so the clinic location was changed
				locationChange = true
			}
		}
	}

	// exam date
	dateString := rejectDetails.RecentExamDate
	_, err = time.Parse("2006-01-02", dateString)
	if err != nil {
		lg.WithError(err).Error("unable to parse exam date")
		return err
	}

	// exam types
	examTypes := make([]string, 0)
	examTypes = append(examTypes, rejectDetails.ExamTypes...)
	if rejectDetails.OtherExamType != "" {
		examTypes = append(examTypes, fmt.Sprint("Other: ", rejectDetails.OtherExamType))
	}

	// patient email
	email := request.Email
	if email == "" {
		email = "UNKNOWN"
	}

	err = s.cioEmailer.Send(
		ctx,
		s.supportEmailAddress,
		"",
		cio_email.PVR_PX,
		map[string]interface{}{
			"org_id":                  originalReqClinic.Provider.LegacyId,
			"request_id":              requestId,
			"unlimited":               unlimited,
			"org_confirmed":           rejectDetails.ProviderConfirmed,
			"original_org_name":       originalReqOrg,
			"original_clinic_name":    originalReqClinic.Name,
			"original_clinic_address": originalReqClinic.FullAddress,
			"new_org_found":           newOrgFound,
			"location_change":         locationChange,
			"new_org_name":            newClinic.ProviderName,
			"new_clinic_name":         newClinic.Name,
			"new_clinic_address":      newClinic.FullAddress,
			"new_provider_text":       rejectDetails.ProviderText,
			"most_recent_exam_date":   dateString,
			"exam_types":              examTypes,
			"priors":                  rejectDetails.HasOlderExams,
			"rejection_reason":        requests.GetRejectionReasonById(request.RejectionReason),
			"patient_email":           email,
			"comments":                rejectDetails.Comments,
		},
	)
	if err != nil {
		lg.WithError(err).Error("unable to send PVR email")
		return err
	}
	err = requests.SetRequestStatus(ctx, s.sqldb, requests.UNDER_REVIEW, requestId)
	/*update request status*/ if err != nil {
		lg.WithError(err).Error("unable to set request status")
		return err
	}

	s.publishRequestStatusEvent(ctx, requestId, requests.UNDER_REVIEW)

	return nil
}

// PutResubmitRequests - Resubmit a request
func (s *RequestsApiService) PutResubmitRequest(
	ctx context.Context,
	acctId string,
	requestBody models.Request,
	signatureImg string,
	minorSignatureImg string,
	delegForm []*multipart.FileHeader,
	delegPhotoId []*multipart.FileHeader,
) (file []byte, requestId int64, err error) {
	lg := logutils.DebugCtxLogger(ctx)

	providerId := requestBody.OrgId

	// unfortunately, this is needed because clinicId is still a crucial part of request submission
	// and CIO campaigns, etc
	// when clinicId isn't provided by the form configuration (and why should it be? forms are per-provider)
	// look up the first clinic for the given provider
	// TODO @james: remove this once we store providerId (aka orgId) on the request and remove dependencies on clinicId
	provider, err := s.getProviderByLegacyId(ctx, providerId)
	if err != nil {
		return nil, -1, err
	}
	clinics := s.orgSvcClient.GetClinicsByProviderId(ctx, provider.Id)
	if len(clinics) == 0 {
		return nil, -1, fmt.Errorf("no clinics found for providerId: %s", provider.Id)
	}

	clinic := clinics[0]
	clinicId := clinic.LegacyId

	logutils.Infox(ctx, "request init params",
		"acctId", acctId,
		"providerId", providerId,
		"clinicId", clinicId,
		"requestId", requestBody.RequestId,
	)

	requestBody.NormalizeNames()

	// check that the request belongs to the user
	auth, err := requests.BelongsToAcct(ctx, s.sqldb, acctId, requestBody.RequestId)
	if err != nil {
		lg.WithError(err).Error("failed to check if request belongs to acct")
		return nil, -1, err
	}
	if !auth {
		lg.WithError(err).Error("request doesn't belong to acct")
		return nil, -1, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	// TODO check request status is REJECTED -> PVR
	requestId = requestBody.RequestId
	requestStatus := ""
	// determine if request provider needs identity verification
	// TODO if we are setting status to verification pending, we should probably trigger the email notification to send out a verification link
	rvc, _ := s.roiSvcClient.GetVerificationConfig(ctx, providerId)
	// TODO this is redundant, deprecate
	requestBody.NeedsIdVerification = rvc.Active

	tx, err := s.sqldb.BeginTx(ctx, nil)
	if err != nil {
		lg.WithError(err).Error("failed to begin transaction")
		return nil, -1, err
	}

	requestStatus, err = requests.UpdateRequestById(
		ctx,
		tx,
		requestId,
		requestBody,
		clinicId,
		providerId,
	)
	if err != nil {
		lg.WithError(err).Error("failed to update request")
		return nil, -1, err
	}
	if rvc.Active {
		requestStatus = requests.VERIFICATION_PENDING
	}

	// update patient
	err = s.updatePatientUponResubmit(ctx, tx, requestId, acctId, requestBody)
	if err != nil {
		lg.WithError(err).Info("error updating patient with resubmit data")
		errRb := tx.Rollback()
		if errRb != nil {
			lg.WithError(errRb).Error("failed to rollback transaction")
		}
		return nil, -1, err
	}

	err = tx.Commit()
	if err != nil {
		lg.WithError(err).Error("failed to commit transaction")
		return nil, -1, err
	}

	// TODO Might need to do some rollback here aswell
	file, err = s.generateConsentPDF(
		ctx,
		requestId,
		requestBody,
		signatureImg,
		minorSignatureImg,
		delegForm,
		provider,
		"",
	)
	if err != nil {
		s.publishRequestStatusEvent(ctx, requestId, requestStatus)
		logutils.Errorx(ctx, err.Error(), "info:", "error generating consent PDF")
		return nil, -1, err
	}
	err = s.storeConsentPDFs(ctx, requestId, file)
	if err != nil {
		s.publishRequestStatusEvent(ctx, requestId, requestStatus)
		logutils.Errorx(ctx, err.Error(), "info:", "error storing consent PDF")
		return nil, -1, err
	}

	err = s.storeConsentSignature(ctx, requestId, []byte(signatureImg))
	if err != nil {
		s.publishRequestStatusEvent(ctx, requestId, requestStatus)
		logutils.Errorx(ctx, err.Error(), "info:", "error storing consent signature")
		return nil, -1, err
	}

	if len(delegPhotoId) > 0 {
		if err := s.storeDelegateDocuments(
			ctx,
			requestId,
			delegPhotoId,
			delegForm,
		); err != nil {
			s.publishRequestStatusEvent(ctx, requestId, requestStatus)
			logutils.Errorx(ctx, err.Error(), "info:", "error storing delegate documents")
			return nil, -1, err
		}

		// send PX email only if delegate relation is not family member and if provider is configured for delegate review
		if !requestBody.NeedsIdVerification && !provider.IsUph && provider.DelegateReview &&
			requestBody.Contents.Delegate.RelationType != models.FAMILY_MEMBER {
			// set the status to DELEGATE_PENDING_AUTH if not custom delegate flow and delegate is not a family member
			status, err := requests.SetDelegateAuth(ctx, s.sqldb, requestId)
			if err != nil {
				s.publishRequestStatusEvent(ctx, requestId, requestStatus)
				logutils.Errorx(ctx, err.Error(), "info:", "failed setting delegate auth")
				return nil, -1, errors.New("setting delegate auth failed")
			}
			if status != "" {
				requestStatus = requests.DELEGATE_PENDING_AUTH
			}

			err = s.sendDelegateReviewEmail(ctx, requestId, provider.Name)
			if err != nil {
				lg.WithField("request_id", requestId).
					WithError(err).
					Error("Unable to send Delegate Review Email")
			}

		}
	}

	s.publishRequestStatusEvent(ctx, requestId, requestStatus)
	s.patientEnrollmentPublishEvents(ctx, requestId, providerId, acctId, requestBody)

	return file, requestId, nil
}

// PostIncompleteRequests - Save a new incomplete request
func (s *RequestsApiService) PostIncompleteRequests(
	ctx context.Context,
	r models.IncompleteRequest,
	deviceID string,
) (interface{}, error) {
	return s.postIncompleteRequestsHelper(
		ctx, r, deviceID,
	)
}

// PostIncompleteRequests - Save a new incomplete request
func (s *RequestsApiService) PostIncompleteRequestsV2(
	ctx context.Context,
	r models.IncompleteRequest,
	signatureImg string,
	delegForm []*multipart.FileHeader,
	delegPhotoId []*multipart.FileHeader,
	deviceID string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx)

	resp, err := s.postIncompleteRequestsHelper(
		ctx, r, deviceID,
	)
	if err != nil {
		return nil, err
	}

	if resp.IncompleteRequestId != "" {
		err = s.storeIncompleteRequestFiles(
			ctx,
			resp.IncompleteRequestId,
			signatureImg,
			delegForm,
			delegPhotoId,
		)
		if err != nil {
			lg.WithError(err).Error("store temporary files failed")
			return resp, err
		}
	}

	return resp, nil
}

// PutIncompleteRequests - Update an incomplete request
func (s *RequestsApiService) PutIncompleteRequests(
	ctx context.Context,
	incompleteReqID string,
	r models.IncompleteRequest,
	deviceID string,
) (err error) {
	return s.putIncompleteRequestsHelper(ctx, incompleteReqID, r, deviceID)
}

// PutIncompleteRequests - Update an incomplete request
func (s *RequestsApiService) PutIncompleteRequestsV2(
	ctx context.Context,
	incompleteReqID string,
	r models.IncompleteRequest,
	signatureImg string,
	delegForm []*multipart.FileHeader,
	delegPhotoId []*multipart.FileHeader,
	deviceID string,
) (err error) {
	lg := logutils.DebugCtxLogger(ctx)
	err = s.putIncompleteRequestsHelper(ctx, incompleteReqID, r, deviceID)
	if err != nil {
		return err
	}

	err = s.storeIncompleteRequestFiles(
		ctx,
		incompleteReqID,
		signatureImg,
		delegForm,
		delegPhotoId,
	)
	if err != nil {
		lg.WithError(err).Error("store temporary files failed")
		return err
	}
	return nil
}

// GetIncompleteRequestById - Get info on an Incomplete Request
func (s *RequestsApiService) GetIncompleteRequestById(
	ctx context.Context,
	incompleteReqID string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("incomplete_request_id", incompleteReqID)
	resp := models.IncompleteRequestMetadata{}

	clinicId, completed, expired, err := incompleteRequests.GetMetadata(
		ctx,
		s.sqldb,
		incompleteReqID,
	)
	if err != nil {
		if err != sql.ErrNoRows {
			lg.WithError(err).Error("unable to get incomplete request metadata")
			return nil, err
		}
		lg.Error("incomplete request not found")
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	}

	if completed {
		resp.Status = models.IncomplRequestCompleted
	} else if expired {
		resp.Status = models.IncomplRequestExpired
	} else {
		// otherwise it's pending completion
		resp.Status = models.IncomplRequestIncomplete
	}

	// get clinic name
	clinic, err := s.getClinicByLegacyId(ctx, clinicId)
	if err != nil {
		lg.WithError(err).Info("unable to get clinic info")
		return nil, err
	}
	resp.ClinicName = clinic.Name
	resp.ClinicId = clinicId

	return resp, nil
}

// PostIncompleteRequestVerify - Authenticate access to Incomplete Request and return request data
func (s *RequestsApiService) PostIncompleteRequestVerify(
	ctx context.Context,
	incompleteReqID string,
	dob string,
) (interface{}, error) {
	return s.postIncompleteRequestVerifyHelper(ctx, incompleteReqID, dob)
}

// PostIncompleteRequestVerify - Authenticate access to Incomplete Request and return request data
func (s *RequestsApiService) PostIncompleteRequestVerifyV2(
	ctx context.Context,
	incompleteReqID string,
	dob string,
) (
	response models.IncompleteRequestResponse,
	err error,
) {
	lg := logutils.DebugCtxLogger(ctx).WithField("incomplete_request_id", incompleteReqID)
	response, err = s.postIncompleteRequestVerifyHelper(ctx, incompleteReqID, dob)
	if err != nil {
		lg.WithError(err).Error("incomplete request verify v2 failed")
		return
	}

	signatureBase64s, delegateForms, delegatePhotos := s.getIncompleteRequestFiles(
		ctx,
		incompleteReqID,
	)

	if len(signatureBase64s) > 0 {
		response.SignatureImg = string(signatureBase64s[0].Content)
	}
	if len(delegateForms) > 0 {
		var delegateFormStrings []string
		for _, delegateForm := range delegateForms {
			delegateFormStrings = append(
				delegateFormStrings,
				fmt.Sprintf(
					"data:%s;base64,%s",
					delegateForm.ContentType,
					base64.StdEncoding.EncodeToString(delegateForm.Content),
				))
		}
		response.DelegateForms = delegateFormStrings
	}
	if len(delegatePhotos) > 0 {
		response.DelegatePhoto = fmt.Sprintf(
			"data:%s;base64,%s",
			delegatePhotos[0].ContentType,
			base64.StdEncoding.EncodeToString(delegatePhotos[0].Content),
		)
	}
	return response, nil
}

func (s *RequestsApiService) PostSubmitStep(
	ctx context.Context,
	stepBody orgservice.StepValidationBody,
	deviceId string,
) (orgservice.StepValidationResponse, error) {
	requestId := ksuid.New().String()
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"stepName":         stepBody.StepName,
		"deviceId":         deviceId,
		"LegacyProviderId": stepBody.LegacyProviderId,
		"requestId":        requestId,
	})
	stepBody.DeviceId = deviceId

	// 1. Validate request
	response, err := s.roiSvcClient.PostSubmitStep(
		ctx,
		stepBody,
	)
	if err != nil {
		lg.WithError(err).Error("failed to post submit step")
		return orgservice.StepValidationResponse{}, err
	}
	response.RequestId = requestId

	// 2. Log validation errors
	s.createBadInputEntry(ctx, stepBody, response)

	// TODO @james: pull the requestId from roi and set it here
	// currently, this will only be useful as a correlationID
	return response, err
}

func (s *RequestsApiService) PutSubmitStep(
	ctx context.Context,
	stepBody orgservice.StepValidationBody,
	deviceId string,
) (orgservice.StepValidationResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"stepName":         stepBody.StepName,
		"deviceId":         deviceId,
		"LegacyProviderId": stepBody.LegacyProviderId,
		"requestId":        stepBody.RequestId,
	})
	stepBody.DeviceId = deviceId

	// 1. Validate request
	response, err := s.roiSvcClient.PutSubmitStep(
		ctx,
		stepBody,
	)
	if err != nil {
		lg.WithError(err).Error("failed to post submit step")
		return orgservice.StepValidationResponse{}, err
	}
	response.RequestId = stepBody.RequestId

	// 2. Log validation errors
	s.createBadInputEntry(ctx, stepBody, response)

	return response, err
}

func (s *RequestsApiService) PatchCancelRequest(
	ctx context.Context,
	acctId string,
	requestId int64,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	auth, err := requests.BelongsToAcct(ctx, s.sqldb, acctId, requestId)
	if err != nil {
		lg.WithError(err).Error("unable to check if request belongs to user")
		if err == sql.ErrNoRows {
			return errors.New(errmsg.ERR_NOT_FOUND)
		}
	}
	if !auth {
		return errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	err = requests.SetRequestStatus(ctx, s.sqldb, requests.VOID, requestId)
	/*update request status*/ if err != nil {
		lg.WithError(err).Error("unable to set request status")
		return err
	}

	s.publishRequestStatusEvent(ctx, requestId, requests.VOID)
	return nil
}

/*****************************************************************
---------------------- BEGIN HELPER METHODS ----------------------
******************************************************************/

func (s *RequestsApiService) getProviderByLegacyId(
	ctx context.Context,
	providerId int64,
) (orgservice.Provider, error) {
	lg := logutils.CtxLogger(ctx)
	provider, err := s.orgSvcClient.GetProviderByLegacyId(ctx, providerId)
	if err != nil {
		lg.WithError(err).Info("error retrieving provider info")
		return provider, errors.New("cannot find request provider")
	}
	return provider, nil
}

func (s *RequestsApiService) getClinicByLegacyId(
	ctx context.Context,
	clinicId int64,
) (orgservice.Clinic, error) {
	lg := logutils.CtxLogger(ctx)
	clinic, err := s.orgSvcClient.GetClinicByLegacyId(ctx, clinicId)
	if err != nil {
		lg.WithError(err).Info("error retrieving clinic info")
		return clinic, errors.New("cannot find request clinic")
	}
	return clinic, nil
}

func (s *RequestsApiService) createBadInputEntry(
	ctx context.Context,
	stepBody orgservice.StepValidationBody,
	stepResponse orgservice.StepValidationResponse,
) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"stepName":         stepBody.StepName,
		"deviceId":         stepBody.DeviceId,
		"LegacyProviderId": stepBody.LegacyProviderId,
		"requestId":        stepBody.RequestId,
	})
	lg.Infof("step validation result: %t", stepResponse.Valid)

	if !stepResponse.Valid {
		for fieldName, errorMessage := range stepResponse.FieldValidationErrorMessageCodes {
			lg.Infof("failed input validation: [%s]: %s", fieldName, errorMessage)
			value := "-- MISSING INPUT --"
			if fieldValue, exists := stepBody.InputFields[fieldName]; exists {
				valueJson, err := json.Marshal(fieldValue)
				if err != nil {
					lg.WithError(err).Infof("could not marshal bad input value")
				} else {
					value = string(valueJson)
				}
			}
			err := requests.CreateBadInputEntry(
				ctx,
				s.sqldb,
				strconv.FormatInt(stepBody.LegacyProviderId, 10),
				stepResponse.RequestId,
				stepBody.StepName,
				fieldName,
				value,
				errorMessage,
				stepBody.DeviceId,
			)
			if err != nil {
				lg.WithError(err).Infof("failed to persist bad input")
			}
		}
	}
}

func (s *RequestsApiService) encryptDataToken(
	ctx context.Context,
	r models.IncompleteRequest,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)
	jwt, err := auth.MakeIncomplReqDataToken(r)
	if err != nil {
		lg.WithError(err).Error("unable to make incomplete request jwt")
		return "", err
	}

	encryptionKey := r.RequestData.Dob
	dataToken, err := auth.EncryptJWETwoPartKey([]byte(jwt), encryptionKey)
	if err != nil {
		lg.WithError(err).Error("unable to make incomplete request jwe")
		return "", err
	}
	return dataToken, nil
}

func (s *RequestsApiService) getEmailExperimentInfo(
	ctx context.Context,
	experimentVariants map[string]experiment.Variant,
) (
	experimentName string, variantName string, exposureRequestSteps []models.RequestFormStep,
	emailParams map[string]any,
) {
	lg := logutils.DebugCtxLogger(ctx)
	emailParams = make(map[string]any)
	experimentName = "default"
	emailCampaignFlag := experimentVariants["email-campaign-params-flag"]
	if emailCampaignFlag.Payload == nil {
		return
	}
	payload := emailCampaignFlag.Payload.(map[string]interface{})
	experimentName, ok := payload["experiment_name"].(string)
	if !ok {
		lg.Error("failed to cast experiment name to string")
	}
	if experimentName != "default" {
		variantName = experimentVariants[experimentName].Value
		variantPayload := experimentVariants[experimentName].Payload.(map[string]any)
		requestStepInterfacesPayload := variantPayload["exposure_request_steps"]
		if requestStepInterfacesPayload != nil {
			for _, requestStepInterface := range requestStepInterfacesPayload.([]any) {
				if exposureRequestStep, ok := requestStepInterface.(string); ok {
					exposureRequestSteps = append(
						exposureRequestSteps,
						models.RequestFormStep(exposureRequestStep),
					)
				}
			}
		}
		emailParamsPayload := variantPayload["email_params"]
		if emailParamsPayload != nil {
			emailParams = emailParamsPayload.(map[string]any)
		}
	}
	return
}

func (s *RequestsApiService) createPreregistrationCIOUser(
	ctx context.Context,
	r models.IncompleteRequest,
	incompleteRequestId string,
	providerName string,
	deviceID string,
	lang language.Tag,
	requiredIdType string,
	experimentName string,
	variantName string,
	emailParams map[string]any,
) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"func": "createPreregistrationCIOUser",
	})
	lg.Info("preregistration cio user create attempt")
	if r.RequestData.Email == "" {
		lg.Error("missing email")
		return
	}
	// Either no experiment is running, or we're in the control group
	if variantName == "control" {
		experimentName = "default"
	}
	emailParams["org_name"] = providerName
	emailParams["host"] = s.frontEndHost
	emailParams["incomplete_request_id"] = incompleteRequestId
	emailParams["region_id"] = regions.GetRegionID()
	emailParams["device_id"] = deviceID
	emailParams["required_id_type"] = requiredIdType

	abandoned_cart_treatment := map[string]any{
		"treatment":    experimentName,
		"variant_name": variantName,
		"email_params": emailParams,
	}

	// Setting the accountID = email actually leaves the accountID field empty in CIO
	// It will be populated later upon request completion and account creation
	err := s.cioEventProducer.UpdateUserAttributes(
		r.RequestData.Email,
		r.RequestData.Email,
		map[string]any{
			"abandoned_cart_treatment": abandoned_cart_treatment,
			"language":                 lang.String(),
		},
	)
	if err != nil {
		lg.WithError(err).Error("failed to update cio user attribute")
	} else {
		lg.Info("preregistration cio user create success")
	}

	// Send init event containing the email and experiment parameters
	s.trackCIOFunnelEvents(
		ctx,
		r.RequestData.Email,
		incompleteRequestId,
		models.RequestStepInit,
		"",
		map[string]any{"abandoned_cart_treatment": abandoned_cart_treatment},
	)
}

func (s *RequestsApiService) shouldExposeForRequestStep(
	step models.RequestFormStep,
	exposureSteps []models.RequestFormStep,
) bool {
	// empty means we should expose on any request step
	if len(exposureSteps) == 0 {
		return true
	}
	for _, exposureStep := range exposureSteps {
		if step == exposureStep {
			return true
		}
	}
	return false
}

func (s *RequestsApiService) shouldSendRequestStepCIOEvent(step models.RequestFormStep) bool {
	return step == models.RequestStepInit || step == models.RequestStepComplete ||
		step == models.RequestStepHealthId || step == models.RequestStepPaymentDetails ||
		step == models.RequestStepPaymentSelection || step == models.RequestStepPaymentSelectionAndHandling
}

func (s *RequestsApiService) trackCIOFunnelEvents(
	ctx context.Context,
	email string,
	incompleteRequestID string,
	lastCompletedStep models.RequestFormStep,
	currentStep models.RequestFormStep,
	eventAttributes map[string]any,
) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"func": "trackCIOFunnelEvents",
	})
	lg.Info("cio request funnel event create attempt")
	if email == "" {
		return
	}

	eventAttributes["incomplete_request_id"] = incompleteRequestID

	if s.shouldSendRequestStepCIOEvent(currentStep) {
		err := s.cioEventProducer.ProduceEvent(
			email,
			fmt.Sprintf("request_current_step_%s", strings.ToLower(string(currentStep))),
			eventAttributes,
		)
		if err != nil {
			lg.WithError(err).Error("failed to send request funnel event")
		} else {
			lg.Info("cio request funnel event create success")
		}
	}
	// cio tracking
	if s.shouldSendRequestStepCIOEvent(lastCompletedStep) {
		err := s.cioEventProducer.ProduceEvent(
			email,
			fmt.Sprintf("request_step_%s", strings.ToLower(string(lastCompletedStep))),
			eventAttributes,
		)
		if err != nil {
			lg.WithError(err).Error("failed to send request funnel event")
		} else {
			lg.Info("cio request funnel event create success")
		}
	}
}

func (s *RequestsApiService) undoEmailCampaignExposure(
	ctx context.Context,
	email string,
	clinicId int64,
	deviceID string,
) {
	lg := logutils.DebugCtxLogger(ctx)
	incompleteRequestCreationTimestamp, err := incompleteRequests.GetIncompleteRequestCreationTimestamp(
		ctx,
		s.sqldb,
		email,
		clinicId,
	)
	// Remove patient from email exposure group if they completed the request in under 1 hour
	// because they could not have received an email and thus were not affected by the experiment
	if err == nil {
		if incompleteRequestCreationTimestamp.Add(time.Hour).After(time.Now().UTC()) {
			variants, err := s.experimentsClient.Fetch(&experiment.User{DeviceId: deviceID})
			if err != nil {
				lg.WithError(err).Infof("unable to fetch experiment for deviceID: %s", deviceID)
			}
			experimentName, variantName, _, _ := s.getEmailExperimentInfo(ctx, variants)
			if variantName != "" {
				amplitude_util.ExposeExperimentVariant(
					ctx,
					s.ampliClient,
					"",
					deviceID,
					experimentName,
					"",
				)
			}
		}
	} else {
		lg.Info("failed to fetch incomplete request entry")
	}
}

func (s *RequestsApiService) isAllowedContentType(contentType string) bool {
	allowed := false
	for _, t := range hrs.AllowedFileTypes {
		if t == contentType {
			allowed = true
			break
		}
	}
	return allowed
}

func (s *RequestsApiService) getBlobId(
	fileType incompleteRequestBlobs.FileType,
	incompleteRequestId string,
	counter int,
) string {
	return fmt.Sprintf("%s_%s_%d", fileType.String(), incompleteRequestId, counter)
}

func (s *RequestsApiService) storeMultipartFiles(
	ctx context.Context,
	incompleteRequestId string,
	files []*multipart.FileHeader,
	fileType incompleteRequestBlobs.FileType,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	for i, file := range files {
		contentType := file.Header["Content-Type"][0]
		if !s.isAllowedContentType(contentType) {
			lg.Warningf("attempted to upload unknown content type: %s", contentType)
		}

		blobId := s.getBlobId(fileType, incompleteRequestId, i)
		fileBytes, err := s.getBytesFromMultipartFile(
			file,
		)
		if err != nil {
			lg.WithError(err).Errorf("%s read file bytes failed", fileType.String())
			return err
		}
		err = azureUtils.UploadBlobBytes(
			ctx,
			s.containerClient.Requests,
			blobId,
			fileBytes,
		)
		if err != nil {
			lg.WithError(err).Errorf("failed storing %s file to blob", fileType.String())
			return err
		}
		err = incompleteRequestBlobs.Create(
			ctx,
			s.sqldb,
			blobId,
			incompleteRequestId,
			fileType,
			contentType,
		)
		if err != nil {
			lg.WithError(err).Errorf("failed to created %s blob entry", fileType.String())
			return err
		}
	}
	return nil
}

func (s *RequestsApiService) storeIncompleteRequestFiles(
	ctx context.Context,
	incompleteRequestId string,
	signatureBase64 string,
	delegForm []*multipart.FileHeader,
	delegPhotoId []*multipart.FileHeader,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	if signatureBase64 != "" {
		blobId := s.getBlobId(incompleteRequestBlobs.SIGNATURE, incompleteRequestId, 0)
		err := azureUtils.UploadBlobBytes(
			ctx,
			s.containerClient.Requests,
			blobId,
			[]byte(signatureBase64),
		)
		if err != nil {
			lg.WithError(err).Error("Failed storing request delegate id file to blob")
			return err
		}
		err = incompleteRequestBlobs.Create(
			ctx,
			s.sqldb,
			blobId,
			incompleteRequestId,
			incompleteRequestBlobs.SIGNATURE,
			"image/base64",
		)
		if err != nil {
			lg.WithError(err).Error("failed to created signature blob entry")
			return err
		}
	}

	if len(delegForm) > 0 {
		err := s.storeMultipartFiles(
			ctx,
			incompleteRequestId,
			delegForm,
			incompleteRequestBlobs.DELEGATE_FORM,
		)
		if err != nil {
			return err
		}
	}

	if len(delegPhotoId) > 0 {
		err := s.storeMultipartFiles(
			ctx,
			incompleteRequestId,
			delegPhotoId,
			incompleteRequestBlobs.DELEGATE_PHOTO,
		)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *RequestsApiService) getBlobBytes(
	ctx context.Context,
	blobId string,
) ([]byte, error) {
	blobReader, _ := azureUtils.DownloadBlobReader(
		ctx,
		s.containerClient.Requests,
		blobId,
	)
	return io.ReadAll(blobReader)
}

func (s *RequestsApiService) getBlobsByIncompleteRequestId(
	ctx context.Context,
	incompleteRequestId string,
) []incompleteRequestBlobs.IncompleteRequestBlob {
	lg := logutils.DebugCtxLogger(ctx)
	var result []incompleteRequestBlobs.IncompleteRequestBlob
	blobs, err := incompleteRequestBlobs.GetBlobsByIncompleteRequestId(
		ctx,
		s.sqldb,
		incompleteRequestId,
	)
	if err != nil {
		lg.WithError(err).
			Errorf("failed to fetch blob entries for incomplete request: %s", incompleteRequestId)
	} else {
		for _, blob := range blobs {
			bytes, err := s.getBlobBytes(ctx, blob.BlobId)
			if err == nil {
				blob.Content = bytes
				result = append(result, blob)
			} else {
				lg.WithError(err).Errorf("failed to fetch blobs from azure for incomplete request: %s", incompleteRequestId)
			}
		}
	}
	return result
}

func (s *RequestsApiService) getIncompleteRequestFiles(
	ctx context.Context,
	incompleteRequestId string,
) (
	signatureBlobs []incompleteRequestBlobs.IncompleteRequestBlob,
	delegateFormBlobs []incompleteRequestBlobs.IncompleteRequestBlob,
	delegatePhotoBlobs []incompleteRequestBlobs.IncompleteRequestBlob,
) {
	blobs := s.getBlobsByIncompleteRequestId(ctx, incompleteRequestId)
	for _, blob := range blobs {
		switch blob.FileType {
		case incompleteRequestBlobs.SIGNATURE:
			signatureBlobs = append(signatureBlobs, blob)
		case incompleteRequestBlobs.DELEGATE_FORM:
			delegateFormBlobs = append(delegateFormBlobs, blob)
		case incompleteRequestBlobs.DELEGATE_PHOTO:
			delegatePhotoBlobs = append(delegatePhotoBlobs, blob)
		}
	}
	return
}

func (s *RequestsApiService) postIncompleteRequestsHelper(
	ctx context.Context,
	r models.IncompleteRequest,
	deviceID string,
) (models.IncompleteRequestInitResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	resp := models.IncompleteRequestInitResponse{}
	var err error

	variants, err := s.experimentsClient.Fetch(&experiment.User{DeviceId: deviceID})
	if err != nil {
		lg.WithError(err).Errorf("unable to fetch experiment for deviceID: %s", deviceID)
	}

	orgId := r.RequestData.OrgId
	// unfortunately, this is needed because clinicId is still a crucial part of request submission
	// and CIO campaigns, etc
	// when clinicId isn't provided by the form configuration (and why should it be? forms are per-provider)
	// look up the first clinic for the given provider
	// TODO @james: remove this once we store providerId (aka orgId) on the request and remove dependencies on clinicId
	provider, err := s.getProviderByLegacyId(ctx, orgId)
	if err != nil {
		return resp, err
	}
	clinics := s.orgSvcClient.GetClinicsByProviderId(ctx, provider.Id)
	if len(clinics) == 0 {
		return resp, fmt.Errorf("no clinics found for providerId: %s", provider.Id)
	}

	clinic := clinics[0]
	clinicId := clinic.LegacyId

	requiredIdType, err := s.getRequiredIdType(ctx, orgId)
	lg = lg.WithFields(logrus.Fields{
		"orgId":    orgId,
		"clinicId": clinicId,
	})
	if err != nil {
		lg.WithError(err).Error("unable to parse request clinic and org ids")
		return resp, err
	}

	r.RequestData.NormalizeNames()

	enabled, err := s.checkEligibleIncompleteRequest(
		ctx,
		s.sqldb,
		r.RequestData.Email,
		int64(clinicId),
		provider.LegacyId,
	)
	if err != nil {
		lg.WithError(err).Error("unable to check if incomplete requests enabled for email")
		return resp, err
	}
	if !enabled {
		lg.Info("incomplete requests not enabled for user")
		return resp, nil
	}

	dataToken, err := s.encryptDataToken(ctx, r)
	if err != nil {
		lg.WithError(err).Error("failed to encrypt data token")
		return resp, err
	}

	// create incomplete request
	resp.IncompleteRequestId = ksuid.New().String()
	err = incompleteRequests.Create(
		ctx,
		s.sqldb,
		resp.IncompleteRequestId,
		r.RequestData.Email,
		int64(clinicId),
		dataToken,
		r.LastCompletedStep,
	)
	if err != nil {
		lg.WithError(err).Error("unable to create incomplete requests entry")
		return resp, err
	}

	authTok, err := auth.MakeIncompleteReqEmailAuthToken(resp.IncompleteRequestId)
	if err != nil {
		lg.WithError(err).Error("unable to create callback URL auth tok")
		return resp, err
	}
	// add auth token to response so subsequent requests can be authorized
	resp.AuthToken = authTok

	lang := phlanguage.Select(ctx, s.supportedLanguages).
		WithOrganization(s.languageTagProviders.OrgId, orgId).
		GetLanguageTag()

	experimentName, variantName, exposureRequestSteps, emailParams := s.getEmailExperimentInfo(
		ctx,
		variants,
	)
	s.createPreregistrationCIOUser(
		ctx,
		r,
		resp.IncompleteRequestId,
		provider.Name,
		deviceID,
		lang,
		requiredIdType,
		experimentName,
		variantName,
		emailParams,
	)
	if variantName != "" && s.shouldExposeForRequestStep(r.CurrentStep, exposureRequestSteps) {
		amplitude_util.ExposeExperimentVariant(
			ctx,
			s.ampliClient,
			"",
			deviceID,
			experimentName,
			variantName,
		)
	}
	s.trackCIOFunnelEvents(
		ctx,
		r.RequestData.Email,
		resp.IncompleteRequestId,
		r.LastCompletedStep,
		r.CurrentStep,
		map[string]any{},
	)
	return resp, nil
}

func (s *RequestsApiService) putIncompleteRequestsHelper(
	ctx context.Context,
	incompleteReqID string,
	r models.IncompleteRequest,
	deviceID string,
) (err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("incomplete_request_id", incompleteReqID)

	variants, err := s.experimentsClient.Fetch(&experiment.User{DeviceId: deviceID})
	if err != nil {
		lg.WithError(err).Errorf("unable to fetch experiment for deviceID: %s", deviceID)
	}
	exists, err := incompleteRequests.RequestExists(ctx, s.sqldb, incompleteReqID)
	if err != nil {
		lg.WithError(err).Error("unable to check if incomplete request exists")
		return err
	} else if !exists {
		lg.WithError(err).Error("request id not found")
		return errors.New(errmsg.ERR_NOT_FOUND)
	}

	r.RequestData.NormalizeNames()

	dataToken, err := s.encryptDataToken(ctx, r)
	if err != nil {
		lg.WithError(err).Error("failed to encrypt data token")
		return err
	}

	// update incomplete request
	err = incompleteRequests.UpdateRequestById(
		ctx,
		s.sqldb,
		incompleteReqID,
		dataToken,
		r.LastCompletedStep,
	)
	if err != nil {
		lg.WithError(err).Error("unable to update incomplete request")
		return err
	}

	experimentName, variantName, exposureRequestSteps, _ := s.getEmailExperimentInfo(ctx, variants)
	if variantName != "" && s.shouldExposeForRequestStep(r.CurrentStep, exposureRequestSteps) {
		amplitude_util.ExposeExperimentVariant(
			ctx,
			s.ampliClient,
			"",
			deviceID,
			experimentName,
			variantName,
		)
	}
	s.trackCIOFunnelEvents(
		ctx,
		r.RequestData.Email,
		incompleteReqID,
		r.LastCompletedStep,
		r.CurrentStep,
		map[string]any{},
	)
	return nil
}

func (s *RequestsApiService) postIncompleteRequestVerifyHelper(
	ctx context.Context,
	incompleteReqID string,
	dob string,
) (response models.IncompleteRequestResponse, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("incomplete_request_id", incompleteReqID)

	// get data
	dataToken, lastCompletedStep, err := incompleteRequests.GetDataTokenAndLastCompletedStepById(
		ctx,
		s.sqldb,
		incompleteReqID,
	)
	if err != nil && err != sql.ErrNoRows {
		lg.WithError(err).Error("unable to check if incomplete request exists")
		return
	} else if dataToken == "" {
		lg.WithError(err).Error("request id not found")
		err = errors.New(errmsg.ERR_NOT_FOUND)
		return
	}

	// Try to decrypt with only DOB first
	jwt, err := auth.DecryptJWETwoPartKey(dataToken, dob)
	if err != nil {
		lg.WithError(err).Error("unable to decrypt incomplete request data token")
		err = errors.New(errmsg.ERR_BAD_CREDENTIALS)
		return
	}

	decodedRequest, err := auth.DecodeIncomplReqDataToken(string(jwt))
	if err != nil {
		lg.WithError(err).Error("unable to decode incomplete request data token")
		err = errors.New(errmsg.ERR_BAD_CREDENTIALS)
		return
	}

	// create a JWT token to authorize subsequent requests
	authTok, err := auth.MakeIncompleteReqEmailAuthToken(incompleteReqID)
	if err != nil {
		lg.WithError(err).Error("unable to create auth tok")
		return
	}

	return models.IncompleteRequestResponse{
		RequestData:       decodedRequest.RequestData,
		LastCompletedStep: lastCompletedStep,
		AuthToken:         authTok,
	}, nil
}
