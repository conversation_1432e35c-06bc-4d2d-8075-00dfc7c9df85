/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package requests

import (
	"encoding/json"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	authUtils "gitlab.com/pockethealth/phutils/v10/pkg/auth"
)

type NewRequestResponse struct {
	Pdf                []byte `json:"consentPdf"`
	RequestId          int64  `json:"requestId"`
	VerificationToken  string `json:"verificationToken"`
	IdVerificationLink string `json:"idVerificationLink"`
}

// A PrivateRequestsApiController binds http requests to an api service and writes the service results to the http response
type PrivateRequestsApiController struct {
	service coreapi.RequestsApiServicer
}

// NewPrivateRequestsApiController creates a default api controller
func NewPrivateRequestsApiController(
	s coreapi.RequestsApiServicer,
) coreapi.PrivateRequestsApiRouter {
	return &PrivateRequestsApiController{service: s}
}

// Routes returns all of the api route for the RequestsApiController
func (c *PrivateRequestsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostVerifyRejectedEmail",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/{id}/rejectVerify",
			HandlerFunc: c.PostVerifyRejectedEmail,
		},
		{
			Name:        "PutResubmitRequest",
			Method:      strings.ToUpper("PUT"),
			Pattern:     "/{id}/resubmit",
			HandlerFunc: c.PutResubmitRequest,
		},
		{
			Name:        "GetRequests",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetRequests,
		},
		{
			Name:        "PatchCancelRequest",
			Method:      http.MethodPatch,
			Pattern:     "/{id}/cancel",
			HandlerFunc: c.PatchCancelRequest,
		},
	}
}
func (c *PrivateRequestsApiController) GetPathPrefix() string {
	return "/v1/requests"
}

func (c *PrivateRequestsApiController) GetMiddleware() [](func(http.Handler) http.Handler) {
	return [](func(http.Handler) http.Handler){auth.ValidateAuth}
}

// GetRequests - Get requests
func (c *PrivateRequestsApiController) GetRequests(w http.ResponseWriter, r *http.Request) {
	//token should have already been validated in auth middleware so claims can be checked
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetRequests(r.Context(), acctId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostVerifyRejectedEmail - send PX email to investigate rejected & verified request
func (c *PrivateRequestsApiController) PostVerifyRejectedEmail(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	requestId, err := coreapi.ParseIntParameter(params["id"])
	if err != nil {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}
	//token should have already been validated in auth middleware so claims can be checked
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	rejectDetails := coreapi.RejectVerifyDetails{}
	err = json.NewDecoder(r.Body).Decode(&rejectDetails)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err = c.service.PostVerifyRejectedEmail(r.Context(), acctId, requestId, rejectDetails)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// PutResubmitRequest - update request details and submit time
func (c *PrivateRequestsApiController) PutResubmitRequest(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	requestId, err := coreapi.ParseIntParameter(params["id"])
	if err != nil {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	//TODO: how big should this be?
	//TODO: to prevent abuse, consider something like https://medium.com/@owlwalks/dont-parse-everything-from-client-multipart-post-golang-9280d23cd4ad
	err = r.ParseMultipartForm(32 << 20) //32MB
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	m := r.MultipartForm
	var delegForm []*multipart.FileHeader
	var delegatePhotoId []*multipart.FileHeader
	for key, files := range m.File {
		if strings.Contains(key, "delegatePhotoId") {
			delegatePhotoId = append(delegatePhotoId, files[0])
		} else {
			delegForm = append(delegForm, files[0])
		}

	}

	var requestBody models.Request
	requestBodyStr := r.FormValue("requestBody")
	errReqJson := json.Unmarshal([]byte(requestBodyStr), &requestBody)
	signatureImg := r.FormValue("signatureImg")
	minorSignatureImg := r.FormValue("minorSignatureImg")

	if errReqJson != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	//the request Id in the body, if set, should not be different from the id in the url
	if requestBody.RequestId != 0 && requestBody.RequestId != requestId {
		httperror.ErrorWithLog(w, r, "request ids don't match", http.StatusBadRequest)
		return
	}

	if requestBody.RequestId == 0 {
		requestBody.RequestId = requestId
	}

	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	pdf, id, err := c.service.PutResubmitRequest(
		r.Context(),
		acctId,
		requestBody,
		signatureImg,
		minorSignatureImg,
		delegForm,
		delegatePhotoId,
	)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), NewRequestResponse{pdf, id, "", ""}, nil, w)
}

func (c *PrivateRequestsApiController) PatchCancelRequest(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	requestId, err := coreapi.ParseIntParameter(params["id"])
	if err != nil {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}
	ctx := r.Context()
	acctId, err := authUtils.AccountIDOrEmpty(ctx)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	err = c.service.PatchCancelRequest(r.Context(), acctId, requestId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}
	w.WriteHeader(http.StatusOK)
}
