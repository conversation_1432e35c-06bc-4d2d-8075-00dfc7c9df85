/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package requests

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
)

// A PublicRequestsApiController binds http requests to an api service and writes the service results to the http response
type PublicRequestsApiController struct {
	service         coreapi.RequestsApiServicer
	lt              lockouttracker.LockoutTracker
	ampCookieHeader string
	rateLimiter     coreapi.RateLimiter
}

// NewUnauthRequestsApiController creates a default api controller
func NewPublicRequestsApiController(
	s coreapi.RequestsApiServicer,
	lockout lockouttracker.LockoutTracker,
	ampCookieHeader string,
) coreapi.PublicRequestsApiRouter {
	return &PublicRequestsApiController{
		service:         s,
		lt:              lockout,
		ampCookieHeader: ampCookieHeader,
		rateLimiter: ratelimit.NewRateLimiter(
			5,   // period in minutes
			300, // max hits per period
		),
	}
}

// Routes returns all of the api route for the PublicRequestsApiController
func (c *PublicRequestsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostIncompleteRequests",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/incomplete",
			HandlerFunc: c.PostIncompleteRequests,
		},
		{
			Name:        "PutIncompleteRequests",
			Method:      strings.ToUpper("PUT"),
			Pattern:     "/incomplete/{id}",
			HandlerFunc: c.PutIncompleteRequests,
		},
		{
			Name:        "GetIncompleteRequestById",
			Method:      strings.ToUpper("GET"),
			Pattern:     "/incomplete/{id}",
			HandlerFunc: c.GetIncompleteRequestById,
		},
		{
			Name:        "PostIncompleteRequestVerify",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/incomplete/{id}/verify",
			HandlerFunc: c.PostIncompleteRequestVerify,
		},
		{
			Name:        "GetRequestVerificationConfig",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/verification/config",
			HandlerFunc: c.GetRequestVerificationConfig,
		},
	}
}
func (c *PublicRequestsApiController) GetPathPrefix() string {
	return "/v1/requests"
}

func (c *PublicRequestsApiController) GetMiddleware() [](func(http.Handler) http.Handler) {
	return [](func(http.Handler) http.Handler){c.rateLimiter}
}

// PostIncompleteRequests - Create a new incomplete request
func (c *PublicRequestsApiController) PostIncompleteRequests(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	request := &models.IncompleteRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	res, err := c.service.PostIncompleteRequests(r.Context(), *request, deviceID)

	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

// PutIncompleteRequests - Update an incomplete request with new request data
func (c *PublicRequestsApiController) PutIncompleteRequests(
	w http.ResponseWriter,
	r *http.Request,
) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	params := mux.Vars(r)
	requestId := params["id"]
	if requestId == "" {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	authTok := r.Header.Get("Authorization")
	if authTok == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	requestIdClaim, err := auth.DecodeIncompleteReqEmailToken(authTok)
	if err != nil || requestId != requestIdClaim {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	request := &models.IncompleteRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PutIncompleteRequests(r.Context(), requestId, *request, deviceID)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// GetIncompleteRequestById - Get info on an Incomplete Request
func (c *PublicRequestsApiController) GetIncompleteRequestById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	requestId := params["id"]
	if requestId == "" {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	res, err := c.service.GetIncompleteRequestById(r.Context(), requestId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

// PostIncompleteRequestVerify - Authenticate access to Incomplete Request and return request data
func (c *PublicRequestsApiController) PostIncompleteRequestVerify(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	requestId := params["id"]
	if requestId == "" {
		httperror.ErrorWithLog(w, r, "invalid request id", http.StatusBadRequest)
		return
	}

	verify := &models.IncompleteRequestVerify{}

	if err := json.NewDecoder(r.Body).Decode(&verify); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if verify.Dob == "" {
		httperror.ErrorWithLog(w, r, "missing dob field", http.StatusBadRequest)
	}

	if c.lt.IsLocked(r.Context(), requestId) {
		httperror.ErrorWithLog(w, r, errmsg.ERR_TOO_MANY_ATTEMPTS, http.StatusForbidden)
		return
	}

	res, err := c.service.PostIncompleteRequestVerify(
		r.Context(),
		requestId,
		verify.Dob,
	)

	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else if err.Error() == errmsg.ERR_BAD_TOKEN {
			status = http.StatusBadRequest
		} else if err.Error() == errmsg.ERR_BAD_CREDENTIALS {
			c.lt.IncrementAttempts(r.Context(), requestId)
			status = http.StatusUnauthorized
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

// GetRequestVerificationConfig - get list of all id verification configurations
func (c *PublicRequestsApiController) GetRequestVerificationConfig(
	w http.ResponseWriter,
	r *http.Request,
) {
	result := c.service.GetIdVerificationConfigs(r.Context())
	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
