package v2users

import (
	"context"
	"database/sql"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	sqlbetas "gitlab.com/pockethealth/coreapi/pkg/mysql/betas"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// HandleLoginInAcctSvc is extracted into a separate func so that the V1 UsersApiServicer can access it.
// Also Physician Accounts Login
func HandleLoginInAcctSvc(
	ctx context.Context,
	acctsvc accountservice.AccountService,
	roisvc roiservice.RoiService,
	db *sql.DB,
	accountTypeEndpoint accountservice.AccountTypeEndpoint,
	email string,
	password string,
	ip string,
) (interface{}, string, error) {
	lg := logutils.DebugCtxLogger(ctx)
	loginResp, err := acctsvc.AuthenticateAcctAndGetToken(
		ctx,
		accountTypeEndpoint,
		email,
		password,
		ip,
	)
	if err != nil {
		lg.WithError(err).Error("error authenticating account")
		return nil, "", err
	}

	//if loginResp has a token, all good. If not, return
	acct, err := acctsvc.GetAccountInfoByEmail(ctx, email)
	if err != nil {
		lg.WithError(err).Error("could not get account by email")
	}

	var betas []string
	if acct != nil && accountTypeEndpoint == accountservice.PatientAccountEndpoint {
		betas = sqlbetas.GetBetas(ctx, db, acct.AccountId)
		// sync patient jacket for account enrolments
		SyncPatientJacketOnLogin(ctx, lg, db, roisvc, acct.AccountId)
	}

	return coreapi.LoginResponse{
		Token: loginResp.Token,
		Betas: betas,
	}, loginResp.RefreshToken, nil
}

// SyncPatientJacketOnLogin handles syncing a patient jacket asynchronously if the feature is enabled
func SyncPatientJacketOnLogin(
	ctx context.Context,
	lg logrus.FieldLogger,
	db *sql.DB,
	roiClient roiservice.RoiService,
	accountId string,
) {
	// Only start goroutine if accountId is not empty
	if accountId == "" {
		return
	}

	// Create a background context for the goroutine to prevent cancelation
	// when the parent context is canceled
	go func() {
		bgCtx := context.WithoutCancel(ctx)
		if err := roiClient.PostSyncPatientJacket(bgCtx, accountId); err != nil {
			lg.WithError(err).
				WithField("account_id", accountId).
				Error("error syncing patient jacket for login")
		}
	}()
}
