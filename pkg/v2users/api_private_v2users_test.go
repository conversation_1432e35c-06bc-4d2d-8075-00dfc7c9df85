//go:build integration
// +build integration

package v2users

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/mock"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

const (
	localClaimIp = "127.0.0.1"
)

func TestGetAccountState(t *testing.T) {
	svc := mockcoreapi.NewMockV2UsersApiServicer(t)
	testcases := []struct {
		name           string
		auth           string
		expectedStatus int
	}{
		{
			name:           "missing auth",
			auth:           "",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "good request",
			auth: "Bearer " + auth.MakeAccountAuthToken(
				"2G0MUc3o7CtYvHGVsgjuzIDZ5zw",
				localClaimIp,
			),
			expectedStatus: http.StatusOK,
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc.EXPECT().GetAccountState(mock.Anything, mock.Anything).Return(coreapi.AccountState{}, nil)
			ctrl := NewPrivateV2UsersApiController(
				svc)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/users/state"
			req, err := http.NewRequest("GET", path, nil)
			if err != nil {
				t.Fatal(err)
			}
			if c.auth != "" {
				req.Header.Set("Authorization", c.auth)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestPostEmailUpdateInit(t *testing.T) {
	svc := mockcoreapi.NewMockV2UsersApiServicer(t)
	testcases := []struct {
		name           string
		auth           bool
		expectedStatus int
	}{
		{
			name:           "missing auth",
			auth:           false,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "good request",
			auth:           true,
			expectedStatus: http.StatusOK,
		},
	}
	auth.SetAcctSvcPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErShKPNb7tq+gvBqS4k9ZQQVfi1K8NAN4
NMRMiZW8oKU1ZGb6cs9FArI1DCIGy25XePxQ766BFdQD79Unl//UJ42ZJf4Pw+A2
nU//FhsKfJ11rXDJevBqvjJBh97RriSi
-----END PUBLIC KEY-----
`))
	auth.SetAcctSvcJWTPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEysQM5kZGOKETROw2gB36V9dQYyqU
JRniiniTuy83t0v73acv/GQRMCmIOa0IxwedYUveijyOW8AB6k4B1+wIsA==
-----END PUBLIC KEY-----
`))
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc.EXPECT().PostUsersEmailUpdateInit(mock.Anything, mock.Anything, mock.Anything).Return(nil)
			ctrl := NewPrivateV2UsersApiController(
				svc)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/users/email/update/init"
			req, err := http.NewRequest(
				"POST",
				path,
				bytes.NewBufferString(`{"new_email":"<EMAIL>"}`),
			)
			if err != nil {
				t.Fatal(err)
			}
			if !c.auth {
				req.Header.Set("Authorization", "")
			} else {
				// need to call out to actual acctsvc to get a real token
				qaAcctSvcUrl := cfg.AcctSvcUrl
				acctServiceUser := "apptest_rw"
				acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
				httpClient := httpclient.NewHTTPClient(&http.Client{}, nil)
				service := accountservice.NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)
				login, err := service.AuthenticateAcctAndGetToken(context.TODO(), accountservice.PatientAccountEndpoint, "<EMAIL>", "test1234", localClaimIp)
				if err != nil {
					t.Fatalf("could not login: %v", err)
				}
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", login.Token))
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}
