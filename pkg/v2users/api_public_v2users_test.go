package v2users

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/mock"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
)

func TestPostUsersSetup(t *testing.T) {
	testcases := []struct {
		name           string
		body           interface{}
		expectedStatus int
		setupFn        func(svc *mockcoreapi.MockV2UsersApiServicer)
	}{
		{
			name:           "bad request - no body",
			body:           nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "bad request - only token",
			body: json.RawMessage(`
			{
				"token": "test-token"
			}
			`),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "bad request - only passwword",
			body: json.RawMessage(`
			{
				"password": "test-password"
			}
			`),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "good request",
			body: json.RawMessage(`
			{
				"token": "test-token",
				"password": "test-pass"
			}
			`),
			expectedStatus: http.StatusOK,
			setupFn: func(svc *mockcoreapi.MockV2UsersApiServicer) {
				svc.EXPECT().PostUsersSetup(mock.Anything, mock.Anything).Return(accountservice.PasswordSetup{}, nil)
			},
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc := mockcoreapi.NewMockV2UsersApiServicer(t)
			if c.setupFn != nil {
				c.setupFn(svc)
			}
			ctrl := NewPublicV2UsersApiController(
				svc, "", 0)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			body, err := json.Marshal(c.body)
			if err != nil {
				t.Fatal(err)
			}

			buf := bytes.NewBuffer(body)
			path := "/v2/users/setup"
			req, err := http.NewRequest("POST", path, buf)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestPostUsersVerify(t *testing.T) {
	testcases := []struct {
		name           string
		body           interface{}
		expectedStatus int
		setupFn        func(svc *mockcoreapi.MockV2UsersApiServicer)
	}{
		{
			name:           "bad request - no body",
			body:           nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "bad request - only passwword",
			body: json.RawMessage(`
			{
				"password": "test-password"
			}
			`),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "good request - only token",
			body: json.RawMessage(`
			{
				"token": "test-token"
			}
			`),
			expectedStatus: http.StatusOK,
			setupFn: func(svc *mockcoreapi.MockV2UsersApiServicer) {
				svc.EXPECT().
					PostUsersVerify(mock.Anything, mock.Anything).
					Return(accountservice.VerifyEmailResponse{}, nil)
			},
		},
		{
			name: "good request - both token and password",
			body: json.RawMessage(`
			{
				"token": "test-token",
				"password": "test-pass"
			}
			`),
			expectedStatus: http.StatusOK,
			setupFn: func(svc *mockcoreapi.MockV2UsersApiServicer) {
				svc.EXPECT().
					PostUsersVerify(mock.Anything, mock.Anything).
					Return(accountservice.VerifyEmailResponse{}, nil)
			},
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc := mockcoreapi.NewMockV2UsersApiServicer(t)
			if c.setupFn != nil {
				c.setupFn(svc)
			}
			ctrl := NewPublicV2UsersApiController(
				svc, "", 0)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			body, err := json.Marshal(c.body)
			if err != nil {
				t.Fatal(err)
			}

			buf := bytes.NewBuffer(body)
			path := "/v2/users/verify"
			req, err := http.NewRequest("POST", path, buf)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestPutUsersEmail(t *testing.T) {
	testcases := []struct {
		name           string
		body           interface{}
		putFn          func(context.Context, accountservice.UsersEmailUpdate) error
		expectedStatus int
		setupFn        func(svc *mockcoreapi.MockV2UsersApiServicer)
	}{
		{
			name:           "bad request - no body",
			body:           nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "bad request - invalid body",
			body: json.RawMessage(`
			{
				"invalidToken": "test-token"
			}
			`),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "good request",
			body: json.RawMessage(`
			{
				"token": "test-token"
			}
			`),
			expectedStatus: http.StatusOK,
			setupFn: func(svc *mockcoreapi.MockV2UsersApiServicer) {
				svc.EXPECT().PutUsersEmail(mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc := mockcoreapi.NewMockV2UsersApiServicer(t)
			if c.setupFn != nil {
				c.setupFn(svc)
			}
			ctrl := NewPublicV2UsersApiController(
				svc, "", 0)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			body, err := json.Marshal(c.body)
			if err != nil {
				t.Fatal(err)
			}

			buf := bytes.NewBuffer(body)
			path := "/v2/users/email/update"
			req, err := http.NewRequest("PUT", path, buf)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestLockAccount(t *testing.T) {
	testcases := []struct {
		name           string
		body           interface{}
		expectedStatus int
		setupFn        func(svc *mockcoreapi.MockV2UsersApiServicer)
	}{
		{
			name:           "bad request - empty token",
			body:           "",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "good request",
			body:           "test-token",
			expectedStatus: http.StatusOK,
			setupFn: func(svc *mockcoreapi.MockV2UsersApiServicer) {
				svc.EXPECT().PostLockAccount(mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			svc := mockcoreapi.NewMockV2UsersApiServicer(t)
			if c.setupFn != nil {
				c.setupFn(svc)
			}
			ctrl := NewPublicV2UsersApiController(
				svc, "", 0)

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			body, err := json.Marshal(c.body)
			if err != nil {
				t.Fatal(err)
			}

			buf := bytes.NewBuffer(body)
			path := "/v2/users/lockAccount"
			req, err := http.NewRequest("POST", path, buf)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}
