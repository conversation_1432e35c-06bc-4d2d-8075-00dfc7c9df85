package v2users

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	authUtils "gitlab.com/pockethealth/phutils/v10/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateV2UsersApiController binds http requests to an api service and writes the service results to the http response
type PrivateV2UsersApiController struct {
	service coreapi.V2UsersApiServicer
}

// NewPrivateUsersApiController creates a default api controller
func NewPrivateV2UsersApiController(s coreapi.V2UsersApiServicer) coreapi.PrivateV2UsersApiRouter {
	return &PrivateV2UsersApiController{service: s}
}

// Routes returns all of the api route for the V2UsersApiController
func (c *PrivateV2UsersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostUsersEmailUpdateInit",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/email/update/init",
			HandlerFunc: c.PostUsersEmailUpdateInit,
		},
		{
			Name:        "GetAccountState",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/state",
			HandlerFunc: c.GetAccountState,
		},
		{
			Name:        "GetAccountEnrolmentProviders",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/enrolment",
			HandlerFunc: c.GetAccountEnrolmentProviders,
		},
		{
			Name:        "GetEnrolmentByAccountId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/enrolment-info",
			HandlerFunc: c.GetEnrolmentByAccountId,
		},
		{
			Name:        "PatchDeactivateAccount",
			Method:      strings.ToUpper("Patch"),
			Pattern:     "/deactivate",
			HandlerFunc: c.PatchDeactivateAccount,
		},
		{
			Name:        "PatchSetAccountOwner",
			Method:      strings.ToUpper("Patch"),
			Pattern:     "/owner",
			HandlerFunc: c.PatchSetAccountOwner,
		},
		{
			Name:        "GetUserExamLookup",
			Method:      http.MethodGet,
			Pattern:     "/exams/lookup",
			HandlerFunc: c.GetUserExamLookup,
		},
	}
}

func (c *PrivateV2UsersApiController) GetPathPrefix() string {
	return "/v2/users"
}

func (c *PrivateV2UsersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateV2UsersApiController) PostUsersEmailUpdateInit(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	//check token age - need a fresh token (5 minutes or less) to update email
	token := r.Header.Get("Authorization")
	valid, err := auth.IsFreshAcctsToken(token, time.Minute*5)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	} else if !valid {
		lg.WithError(err).Error("stale token")
		httperror.ErrorWithLog(w, r, "Need a more recent token to update email", http.StatusUnauthorized)
		return
	}

	acctsClaims, err := auth.ParseAcctsToken(token)
	if err != nil {
		lg.WithError(err).Error("couldn't parse accts token")
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		lg.WithError(err).Error("error reading body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	var update coreapi.EmailUpdate
	err = json.Unmarshal(body, &update)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PostUsersEmailUpdateInit(r.Context(), acctsClaims.AcctID, update)
	if err != nil {
		//should always return 200 for this EP
		lg.WithError(err).Error("failed to init email change process")
	}

	w.WriteHeader(http.StatusOK)
}

func (c *PrivateV2UsersApiController) GetAccountState(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithFields(logrus.Fields{
		"account_id": claims.AccountID,
	})

	resp, err := c.service.GetAccountState(r.Context(), claims.AccountID)
	if err != nil {
		lg.WithError(err).Error("unable to retrieve account state from acctsvc")
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), resp, nil, w)

}

func (c *PrivateV2UsersApiController) GetAccountEnrolmentProviders(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithFields(logrus.Fields{
		"account_id": claims.AccountID,
	})

	resp, err := c.service.GetAccountEnrolmentProviders(r.Context(), claims.AccountID)
	if err != nil {
		lg.WithError(err).Error("unable to retrieve enrolment providers")
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), resp, nil, w)

}

func (c *PrivateV2UsersApiController) GetEnrolmentByAccountId(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithFields(logrus.Fields{
		"account_id": claims.AccountID,
	})

	resp, err := c.service.GetEnrolmentByAccountId(r.Context(), claims.AccountID)
	if err != nil {
		lg.WithError(err).Error("unable to retrieve enrolment info")
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), resp, nil, w)

}

// PatchDeactivateAccount
func (c *PrivateV2UsersApiController) PatchDeactivateAccount(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithFields(logrus.Fields{
		"account_id": accountId,
	})

	err = c.service.PatchDeactivateAccount(r.Context(), accountId, token)
	var status int
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else {
			status = http.StatusInternalServerError
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	status = http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), nil, &status, w)
}

// PatchSetAccountOwner
func (c *PrivateV2UsersApiController) PatchSetAccountOwner(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithFields(logrus.Fields{
		"account_id": accountId,
	})

	request := models.SetAccountOwnerRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		lg.WithError(err).Error("error unmarchaling json")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !request.IsValid() {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PatchSetAccountOwner(r.Context(), accountId, request)
	var status int
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else {
			status = http.StatusInternalServerError
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	status = http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), nil, &status, w)
}

// GetUserExamsLookup - Lookup an Exam UUID
func (c *PrivateV2UsersApiController) GetUserExamLookup(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()
	accession := query.Get("accession")
	ssoToken := query.Get("token")

	if accession == "" || ssoToken == "" {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	ctx := r.Context()
	acctId, err := authUtils.AccountIDOrEmpty(ctx)
	if err != nil || acctId == "" {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}
	result, err := c.service.GetUserExamLookup(ctx, ssoToken, acctId, accession)

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			// exam still being transferred
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusNotFound)
			return
		} else if err.Error() == errmsg.ERR_BAD_TOKEN {
			// sso token bad / expired
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
