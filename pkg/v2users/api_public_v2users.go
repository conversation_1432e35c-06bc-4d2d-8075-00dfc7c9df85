package v2users

import (
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PublicV2UsersApiController binds http requests to an api service and writes the service results to the http response
type PublicV2UsersApiController struct {
	service coreapi.V2UsersApiServicer

	// sets refresh_token cookie domain to the current env's frontend host
	refreshCookieDomain string

	// delay(in ms) added to login requests
	badLoginDelay time.Duration
}

// NewPublicUsersApiController creates a default api controller
func NewPublicV2UsersApiController(
	s coreapi.V2UsersApiServicer,
	refreshCookieDomain string,
	badLoginDelayMs int) coreapi.PublicV2UsersApiRouter {
	return &PublicV2UsersApiController{
		service:             s,
		refreshCookieDomain: refreshCookieDomain,
		badLoginDelay:       time.Millisecond * time.Duration(badLoginDelayMs),
	}
}

// Routes returns all of the api route for the V2UsersApiController
func (c *PublicV2UsersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostUsersSetup",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/setup",
			HandlerFunc: c.PostUsersSetup,
		},
		{
			Name:        "PostUsersVerify",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/verify",
			HandlerFunc: c.PostUsersVerify,
		},
		{
			Name:        "PutUsersEmail",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/email/update",
			HandlerFunc: c.PutUsersEmail,
		},
		{
			Name:        "PostUsersLogin",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/login",
			HandlerFunc: c.PostUsersLogin,
		},
		{
			Name:        "PostUserRefresh",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/refresh",
			HandlerFunc: c.PostUsersRefresh,
		},
		{
			Name:        "PostUserResetPasswordInit",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/resetpassword/init",
			HandlerFunc: c.PostUserResetPasswordInit,
		},
		{
			Name:        "PostUserPasswordReset",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/resetpassword",
			HandlerFunc: c.PostUserPasswordReset,
		},
		{
			Name:        "PostLockAccount",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/lockAccount",
			HandlerFunc: c.PostLockAccount,
		},
		{
			Name:        "PostVerificationCode",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/verificationCode",
			HandlerFunc: c.PostVerificationCode,
		},
		{
			Name:        "PostEmailVerification",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/email-verification",
			HandlerFunc: c.PostEmailVerification,
		},
		{
			Name:        "PostVerifyDateOfBirth",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/verify/dob",
			HandlerFunc: c.PostVerifyDateOfBirth,
		},
	}
}

func (c *PublicV2UsersApiController) GetPathPrefix() string {
	return "/v2/users"
}

func (c *PublicV2UsersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

// PostUsersSetup
func (c *PublicV2UsersApiController) PostUsersSetup(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		lg.WithError(err).Error("error reading body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	var verification accountservice.Verification
	err = json.Unmarshal(body, &verification)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if verification.Token == "" || verification.Password == "" {
		lg.WithError(err).Error("token or/and password missing in the body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]
	verification.IP = ip

	res, err := c.service.PostUsersSetup(r.Context(), verification)
	if err != nil {
		// if it's an error with the client given password we can let the client know
		var epr accountservice.ErrPasswordRequirement
		var eve accountservice.ErrVerifyEmail
		if errors.As(err, &epr) || errors.As(err, &eve) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know about it
		// so only log it for now and return a 200
		lg.WithError(err).Error("acctsvc failed to verify email with some error")
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), res, &status, w)
}

// PostUsersVerify
func (c *PublicV2UsersApiController) PostUsersVerify(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		lg.WithError(err).Error("error reading body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	query := r.URL.Query()
	source := query.Get("source")

	var verification accountservice.Verification
	err = json.Unmarshal(body, &verification)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if verification.Token == "" {
		lg.WithError(err).Error("token missing in the body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]
	verification.IP = ip

	result, err := c.service.PostUsersVerify(r.Context(), verification)

	if err != nil {
		// if it's an error with the client given password we can let the client know
		var epr accountservice.ErrPasswordRequirement
		var eve accountservice.ErrVerifyEmail
		if errors.As(err, &epr) || errors.As(err, &eve) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know about it
		// so only log it for now and return a 200
		lg.WithError(err).Error("acctsvc failed to verify email with some error")
	}

	// if we're not verifying from register page, empty the response
	if source != accountservice.REGISTER_PAGE {
		result = accountservice.VerifyEmailResponse{}
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}

// PutUsersEmail
func (c *PublicV2UsersApiController) PutUsersEmail(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		lg.WithError(err).Error("error reading body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	var emailUpdate accountservice.UsersEmailUpdate
	err = json.Unmarshal(body, &emailUpdate)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if emailUpdate.Token == "" {
		lg.WithError(err).Error("token missing in the body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PutUsersEmail(r.Context(), emailUpdate)
	if err != nil {
		// we don't want to ever let the client know there was an issue updating
		// their email since they can brute force tokens otherwise so only log for now
		lg.WithError(err).Error("acctsvc failed to update email")
	}

	w.WriteHeader(http.StatusOK)
}

// PostUsersLogin - Login
func (c *PublicV2UsersApiController) PostUsersLogin(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	loginCredentials := &coreapi.LoginCredentials{}
	if err := json.NewDecoder(r.Body).Decode(&loginCredentials); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	email := loginCredentials.Email
	password := loginCredentials.Password

	if email == "" || password == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_BAD_USER_CREDENTIALS, http.StatusBadRequest)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]

	result, refreshToken, err := c.service.PostUsersLogin(r.Context(), email, password, ip)
	if err != nil {
		lg.WithError(err).Error(err.Error())
		accountservice.HandleLoginError(w, r, err, c.badLoginDelay)
		return
	}

	if refreshToken != "" {
		http.SetCookie(
			w,
			&http.Cookie{
				Name:     "refresh_token",
				Value:    refreshToken,
				Path:     "/",
				Domain:   c.refreshCookieDomain,
				Secure:   true,
				HttpOnly: true,
				SameSite: http.SameSiteStrictMode,
			},
		)
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PublicV2UsersApiController) PostUsersRefresh(w http.ResponseWriter, r *http.Request) {
	refreshToken, err := r.Cookie("refresh_token")
	if err != nil || refreshToken.Value == "" {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]

	resp, err := c.service.PostUsersRefresh(r.Context(), refreshToken.Value, ip)
	if err != nil {
		if err.Error() == string(accountservice.InvalidRefreshToken) ||
			err.Error() == string(accountservice.ExpiredRefreshToken) {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
			return
		}

		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), resp, nil, w)
}

// PostUserResetPasswordInit - Send password reset email
func (c *PublicV2UsersApiController) PostUserResetPasswordInit(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	var email string

	if err := json.NewDecoder(r.Body).Decode(&email); err != nil || email == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err := c.service.PostUserResetPasswordInit(r.Context(), email)
	if err != nil {
		lg.WithError(err).Error("failed to initialize reset password using acctsvc")
	}

	w.WriteHeader(http.StatusOK)
}

// PostUserPasswordReset - Reset password
func (c *PublicV2UsersApiController) PostUserPasswordReset(w http.ResponseWriter, r *http.Request) {
	resetInfo := coreapi.PasswordResetInfo{}
	if err := json.NewDecoder(r.Body).Decode(&resetInfo); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err := c.service.PostUserPasswordReset(r.Context(), resetInfo)
	if err != nil {
		// if it's an error with the client given password we can let the client know
		var badRequest accountservice.ErrBadRequestAccountEdit
		var badPw accountservice.ErrBadNewPassword
		if errors.As(err, &badRequest) || errors.As(err, &badPw) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know about it
		// so only log it for now and return a 200
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("failed to reset user password with some error")
	}

	w.WriteHeader(http.StatusOK)
}

// PostLockAccount
func (c *PublicV2UsersApiController) PostLockAccount(w http.ResponseWriter, r *http.Request) {
	var lockToken string
	if err := json.NewDecoder(r.Body).Decode(&lockToken); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if lockToken == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	err := c.service.PostLockAccount(r.Context(), lockToken)
	var badRequest accountservice.ErrBadRequest
	if err != nil {
		if errors.As(err, &badRequest) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	w.WriteHeader(http.StatusOK)
}

// PostVerificationCode
func (c *PublicV2UsersApiController) PostVerificationCode(w http.ResponseWriter, r *http.Request) {
	lg := logutils.CtxLogger(r.Context())
	var verificationCode accountservice.VerificationCode
	if err := json.NewDecoder(r.Body).Decode(&verificationCode); err != nil {
		lg.WithError(err).Error("cannot verify code: invalid request token/code")
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if !verificationCode.IsValid() {
		lg.Error("cannot verify code: missing token/code")
		httperror.ErrorWithLog(w, r, "missing required data", http.StatusBadRequest)
		return
	}

	query := r.URL.Query()
	source := query.Get("source")

	result, err := c.service.PostVerificationCode(r.Context(), verificationCode, source)

	if err != nil {
		var status int
		var errorMessage string
		if err.Error() == errormsgs.ERR_INVALID_CODE {
			status = http.StatusForbidden
		} else {
			status = http.StatusInternalServerError
			errorMessage = err.Error()
		}
		httperror.ErrorWithLog(w, r, errorMessage, status)
		return
	}
	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}

func (c *PublicV2UsersApiController) PostEmailVerification(w http.ResponseWriter, r *http.Request) {
	lg := logutils.CtxLogger(r.Context())

	var token string
	if err := json.NewDecoder(r.Body).Decode(&token); err != nil {
		lg.WithError(err).Error("cannot verify email: invalid request token/code")
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	result, err := c.service.PostEmailVerification(r.Context(), token)
	if err != nil {
		lg.WithError(err).Error("error verifying email")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}

func (c *PublicV2UsersApiController) PostVerifyDateOfBirth(w http.ResponseWriter, r *http.Request) {
	lg := logutils.CtxLogger(r.Context())

	var dobVerification accountservice.DateOfBirthVerification
	if err := json.NewDecoder(r.Body).Decode(&dobVerification); err != nil {
		lg.WithError(err).Error("cannot verify dob: invalid dob input")
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	result, err := c.service.PostVerifyDateOfBirth(r.Context(), dobVerification)
	if err != nil {
		lg.WithError(err).Error("error verifying dob")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(r.Context(), result, &status, w)
}
