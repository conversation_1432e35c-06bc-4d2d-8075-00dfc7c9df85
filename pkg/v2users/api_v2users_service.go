package v2users

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	consents "gitlab.com/pockethealth/coreapi/pkg/mysql/consents"
	enrollments "gitlab.com/pockethealth/coreapi/pkg/mysql/enrollments"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	sqlPriority "gitlab.com/pockethealth/coreapi/pkg/mysql/prioritylookup"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	sqlSso "gitlab.com/pockethealth/coreapi/pkg/mysql/ssotokens"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/amplitude_util"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// V2UsersApiService is a service that implents the logic for the V2UsersApiServicer
// This service should implement the business logic for every endpoint for the V2Users API.
// Include any external packages or services that will be required by this service.
type V2UsersApiService struct {
	acctService       accountservice.AccountService
	orgService        orgs.OrgService
	lt                lockouttracker.LockoutTracker
	sqldb             *sql.DB
	experimentsClient interfaces.AmplitudeExperimentClient
	ampliClient       interfaces.AmplitudeEventClient
	roiSvcClient      roiservice.RoiService
}

// NewV2UsersApiService creates a default api service
func NewV2UsersApiService(
	acctService accountservice.AccountService,
	orgService orgs.OrgService,
	lockout lockouttracker.LockoutTracker,
	db *sql.DB,
	experimentsClient interfaces.AmplitudeExperimentClient,
	amplitudeClient interfaces.AmplitudeEventClient,
	roiSvcClient roiservice.RoiService,
) coreapi.V2UsersApiServicer {
	return &V2UsersApiService{
		acctService:       acctService,
		orgService:        orgService,
		lt:                lockout,
		sqldb:             db,
		experimentsClient: experimentsClient,
		ampliClient:       amplitudeClient,
		roiSvcClient:      roiSvcClient,
	}
}

// PostUsersSetup
func (s *V2UsersApiService) PostUsersSetup(
	ctx context.Context,
	verification accountservice.Verification,
) (accountservice.PasswordSetup, error) {
	lg := logutils.DebugCtxLogger(ctx)
	accountId, err := s.acctService.VerifyEmail(
		ctx,
		accountservice.PatientAccountEndpoint,
		verification,
	)
	if err != nil {
		return accountservice.PasswordSetup{}, err
	}

	experiments, err := s.experimentsClient.Fetch(
		&experiment.User{UserId: accountId},
	)
	if err != nil {
		lg.WithError(err).
			Infof("unable to fetch experiment for accountId: %s", accountId)
		return accountservice.PasswordSetup{}, err
	}

	if variant, ok := experiments[amplitude_util.AUTO_LOGIN]; ok {
		amplitude_util.ExposeExperimentVariant(
			ctx,
			s.ampliClient,
			accountId,
			"",
			amplitude_util.AUTO_LOGIN,
			variant.Value,
		)
		if variant.Value == "treatment" {
			authRedirectToken, err := s.acctService.GenerateAccountLoginSSOToken(
				ctx,
				accountId,
				time.Now().UTC().Add(time.Minute),
			)
			if err != nil {
				lg.WithError(err).Info("failed to generate account login sso token")
				return accountservice.PasswordSetup{}, err
			}
			return accountservice.PasswordSetup{
				Token:     authRedirectToken,
				AccountId: accountId,
			}, nil
		}
	}

	return accountservice.PasswordSetup{}, err
}

// PostUsersVerify
func (s *V2UsersApiService) PostUsersVerify(
	ctx context.Context,
	verification accountservice.Verification,
) (accountservice.VerifyEmailResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	accountId, err := s.acctService.VerifyEmail(
		ctx,
		accountservice.PatientAccountEndpoint,
		verification,
	)
	if err != nil {
		lg.WithError(err).Info("failed to verify email")
		return accountservice.VerifyEmailResponse{}, err
	}

	authRedirectToken, err := s.acctService.GenerateAccountLoginSSOToken(
		ctx,
		accountId,
		time.Now().UTC().Add(time.Minute),
	)
	if err != nil {
		lg.WithError(err).Info("failed to generate account login sso token")
		return accountservice.VerifyEmailResponse{}, err
	}

	return accountservice.VerifyEmailResponse{
		Token:     authRedirectToken,
		AccountId: accountId,
	}, nil
}

// PutUsersEmail
func (s *V2UsersApiService) PutUsersEmail(
	ctx context.Context,
	emailUpdate accountservice.UsersEmailUpdate,
) error {
	return s.acctService.UpdateEmail(ctx, emailUpdate)
}

func (s *V2UsersApiService) PostUsersRefresh(
	ctx context.Context,
	refreshToken string,
	ip string,
) (interface{}, error) {
	return s.acctService.RefreshToken(ctx, refreshToken, ip)
}

// PostUsersLogin - Login
func (s *V2UsersApiService) PostUsersLogin(
	ctx context.Context,
	email string,
	password string,
	ip string,
) (interface{}, string, error) {
	return HandleLoginInAcctSvc(
		ctx,
		s.acctService,
		s.roiSvcClient,
		s.sqldb,
		accountservice.PatientAccountEndpoint,
		email,
		password,
		ip,
	)
}

// PostUserResetPasswordInit - Send password reset email
func (s *V2UsersApiService) PostUserResetPasswordInit(ctx context.Context, email string) error {
	return s.acctService.ResetPasswordInit(ctx, accountservice.PatientAccountEndpoint, email)
}

// PostUsersEmailUpdateInit - Send email change emails
func (s *V2UsersApiService) PostUsersEmailUpdateInit(
	ctx context.Context,
	accountId string,
	update coreapi.EmailUpdate,
) error {
	return s.acctService.InitUpdateEmail(ctx, accountId, update.NewEmail)
}

// PostUserPasswordReset - Reset password
func (s *V2UsersApiService) PostUserPasswordReset(
	ctx context.Context,
	resetInfo coreapi.PasswordResetInfo,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	acctEdit := accountservice.AccountEdit{
		Token:    resetInfo.Token,
		Password: resetInfo.NewPassword,
		Code:     resetInfo.SecurityCode,
	}

	err := s.acctService.ResetPassword(ctx, accountservice.PatientAccountEndpoint, acctEdit)
	if err != nil {
		lg.WithError(err).Error("error resetting password")
		return err
	}

	return nil
}

func (s *V2UsersApiService) PostLockAccount(ctx context.Context, lockToken string) error {
	lg := logutils.DebugCtxLogger(ctx)

	err := s.acctService.LockAccountWithToken(ctx, lockToken)
	if err != nil {
		lg.WithError(err).Error("error locking account")
		return err
	}

	return nil
}

func (s *V2UsersApiService) PatchDeactivateAccount(
	ctx context.Context,
	accountId string,
	token string,
) error {
	lg := logutils.CtxLogger(ctx)

	err := s.acctService.DeactivateAccount(ctx, accountId, token)
	if err != nil {
		lg.WithError(err).Error("error deactivating account")
	}

	return err

}

func (s *V2UsersApiService) PatchSetAccountOwner(
	ctx context.Context,
	accountId string,
	request models.SetAccountOwnerRequest,
) error {
	lg := logutils.CtxLogger(ctx)

	err := s.acctService.SetAccountOwner(ctx, accountId, request)
	if err != nil {
		lg.WithError(err).Error("error setting account owner")
	}

	return err
}

func (s *V2UsersApiService) PostVerificationCode(
	ctx context.Context,
	verificationCode accountservice.VerificationCode,
	source string,
) (string, error) {
	lg := logutils.CtxLogger(ctx)
	if s.lt.IsLocked(ctx, verificationCode.Token) {
		lg.Error("token locked for too many bad attemps")
		return "", errors.New("too many bad attemps")
	}

	verificationCodeResp, err := s.acctService.GetAccountVerificationCode(
		ctx,
		verificationCode.Token,
	)
	if err != nil {
		lg.WithError(err).Error("error fetching verification code")
		return "", err
	}

	if verificationCodeResp.Code != verificationCode.Code {
		s.lt.IncrementAttempts(ctx, verificationCode.Token)
		lg.WithError(err).Error("invalid code provided")
		return "", errors.New(errormsgs.ERR_INVALID_CODE)
	}

	acct, err := s.acctService.GetAccountInfo(ctx, verificationCodeResp.AccountId)
	if err != nil {
		lg.WithError(err).Error("could not fetch acct")
		return "", err
	}

	passwordSetupToken, err := s.acctService.GetPasswordSetupVerificationToken(
		ctx,
		verificationCodeResp.AccountId,
	)

	if err != nil {
		lg.WithError(err).Error("error fetching password setup token")
		return "", err
	}

	// If password is set and it's from registration page, return the verification code since it's the account verification code
	if acct.IsPassSet {
		if source == accountservice.REGISTER_PAGE {
			return passwordSetupToken.Token, nil
		}
		return "", nil
	}

	return passwordSetupToken.Token, nil
}

func (s *V2UsersApiService) PostEmailVerification(
	ctx context.Context,
	verificationToken string,
) (accountservice.EmailVerification, error) {
	lg := logutils.CtxLogger(ctx)

	emailVerification, err := s.acctService.GetAccountEmailVerification(ctx, verificationToken)
	if err != nil {
		lg.WithError(err).Error("error fetching email verification")
		return accountservice.EmailVerification{}, err
	}

	accountId := emailVerification.AccountId
	requestDateOfBirth, err := requests.GetRequestDateOfBirth(ctx, s.sqldb, accountId)
	if err != nil {
		lg.WithError(err).Error("error fetching request dob for account")
		return accountservice.EmailVerification{}, err
	}
	consentDateOfBirth, err := consents.GetConsentDateOfBirth(ctx, s.sqldb, accountId)
	if err != nil {
		lg.WithError(err).Error("error fetching consent dob for account")
		return accountservice.EmailVerification{}, err
	}
	// if there is no request or consent entry, then the user is either sso, manual enrol, or signed up via /join,
	// and we will consider the account as verified
	if requestDateOfBirth.IsZero() && consentDateOfBirth.IsZero() {
		return accountservice.EmailVerification{AccountId: accountId, IsVerified: true}, nil
	}

	return emailVerification, nil
}

func (s *V2UsersApiService) PostVerifyDateOfBirth(
	ctx context.Context,
	dobVerification accountservice.DateOfBirthVerification,
) (bool, error) {
	lg := logutils.CtxLogger(ctx)

	answer, err := time.Parse("********", dobVerification.DateOfBirth)
	if err != nil {
		lg.WithField("dob", answer).Warn("invalid dob answer")
	}
	// check if there is a request associated with the email
	requestDateOfBirth, err := requests.GetRequestDateOfBirth(
		ctx,
		s.sqldb,
		dobVerification.AccountId,
	)
	if err != nil {
		lg.WithError(err).Error("error fetching dob answer")
		return false, err
	}
	if requestDateOfBirth.Equal(answer) {
		return true, nil
	}
	// if there was no request associated with the email, check consents to verify dob
	consentDateOfBirth, err := consents.GetConsentDateOfBirth(
		ctx,
		s.sqldb,
		dobVerification.AccountId,
	)
	if err != nil {
		lg.WithError(err).Error("error fetching dob answer")
		return false, err
	}
	if consentDateOfBirth.Equal(answer) {
		return true, nil
	}

	return false, nil
}

func (s *V2UsersApiService) GetAccountState(
	ctx context.Context,
	acctId string,
) (coreapi.AccountState, error) {
	lg := logutils.DebugCtxLogger(ctx)

	acctInfo, err := s.acctService.GetAccountInfo(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("error getting acct info")
		return coreapi.AccountState{}, err
	}

	acctSettings, err := s.acctService.GetAccountSettings(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("error getting acct settings")
		return coreapi.AccountState{}, err
	}

	//Will add more fields in the future
	acctResp := coreapi.AccountState{
		CreatedAt: acctInfo.CreatedAt,
		Language:  acctSettings.Language,
	}
	return acctResp, nil
}

func (s *V2UsersApiService) GetAccountEnrolmentProviders(
	ctx context.Context,
	acctId string,
) ([]int64, error) {
	lg := logutils.DebugCtxLogger(ctx)

	orgIds, err := enrollments.GetAccountEnrolmentProviders(ctx, s.sqldb, acctId)
	if err != nil {
		lg.WithError(err).Error("error getting enrolment providers")
		return []int64{}, err
	}

	return orgIds, nil
}

func (s *V2UsersApiService) GetEnrolmentByAccountId(
	ctx context.Context,
	acctId string,
) ([]coreapi.Enrollment, error) {
	lg := logutils.DebugCtxLogger(ctx)

	enrolments, err := enrollments.GetEnrolmentByAccountId(ctx, s.sqldb, acctId)
	if err != nil {
		lg.WithError(err).Error("error getting enrolments")
		return []coreapi.Enrollment{}, err
	}
	for i := range enrolments {
		provider, err := s.orgService.GetProviderByLegacyId(ctx, enrolments[i].OrgId)
		if err != nil {
			lg.WithError(err).Error("error getting provider for enrolment org_id")
			return []coreapi.Enrollment{}, err
		}
		enrolments[i].ProviderName = provider.Name
	}

	return enrolments, nil
}

func (s *V2UsersApiService) GetUserExamLookup(
	ctx context.Context,
	ssoToken string,
	acctId string,
	accession string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"accession": accession,
		"accountId": acctId,
	})

	// trigger adding the accession number to priority lookup for transfer
	orgId, mrn, err := sqlSso.QueryOrgIDAndMRNBySSOToken(ctx, s.sqldb, ssoToken)
	if err != nil {
		if err == sql.ErrNoRows {
			lg.WithError(err).Error("sso token incorrect or expired")
			return "", errors.New(errmsg.ERR_BAD_TOKEN)
		}
		lg.WithError(err).Error("failed to retrieve orgID and mrn via sso token")
		return "", errors.New(errmsg.ERR_NOT_FOUND)
	}
	lg = lg.WithFields(logrus.Fields{
		"orgId": orgId,
		"mrn":   mrn,
	})

	// check if it has been added to priority lookup
	exists, err := sqlPriority.CheckIncompleteLookup(ctx, s.sqldb, acctId, accession)
	if err != nil {
		lg.WithError(err).Error("failed to check priority lookup table for accession")
	}

	// look up the uuid
	uuid, err := exams.LookupExamUUID(ctx, s.sqldb, acctId, accession, mrn)
	if err != nil {
		// return a 404 for all cases here
		if err == sql.ErrNoRows {
			// exists in the lookup table, skip
			if exists {
				return "", errors.New(errmsg.ERR_NOT_FOUND)
			}

			// get enrollment id for priority lookup
			enrollment, err := enrollments.GetEnrollment(ctx, s.sqldb, acctId, orgId, mrn)
			if err != nil {
				lg.WithError(err).Error("failed to retrieve enrollment ID")
				return "", errors.New(errmsg.ERR_NOT_FOUND)
			}
			lg = lg.WithFields(logrus.Fields{
				"enrollmentId": enrollment.Id,
			})

			// store priority lookup entry for gateway
			err = sqlPriority.InsertIntoPriorityLookupAccession(
				ctx,
				s.sqldb,
				enrollment.Id,
				acctId,
				accession,
				ssoToken,
			)
			if err != nil {
				lg.WithError(err).Error("failed to insert into priority lookup")
				return "", errors.New(errmsg.ERR_NOT_FOUND)
			}
			// return an error due to examUUID not existing
			return "", errors.New(errmsg.ERR_NOT_FOUND)
		}

		return "", err
	}

	// if it was added to the priority lookup table, mark it as completed.
	if exists {
		err = sqlPriority.MarkPriorityLookupAccessionAsCompleted(ctx, s.sqldb, acctId, accession)
		if err != nil {
			lg.WithError(err).Error("failed to mark lookup as complete")
		}
	}

	return uuid, nil
}
