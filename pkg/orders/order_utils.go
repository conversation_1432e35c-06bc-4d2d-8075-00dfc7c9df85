package orders

import (
	"context"
	"errors"
	"slices"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/phutils/v8/pkg/logutils"
)

func GetLatestGeneralAccessOrder(
	plans map[uint64]planservice.PlanV2,
	orders []accountservice.Order,
) accountservice.Order {
	var lastEligibleGeneralAccessOrder accountservice.Order
	for _, order := range orders {
		plan, exists := plans[order.PlanId]
		if !exists || !plan.GeneralProviderAccess {
			continue
		}

		if plan.FullUnlockAccess && isMoreRecentOrder(lastEligibleGeneralAccessOrder, order) {
			lastEligibleGeneralAccessOrder = order
		}
	}
	return lastEligibleGeneralAccessOrder
}

func isMoreRecentOrder(
	orderA accountservice.Order,
	orderB accountservice.Order,
) bool {
	// return true if order B is more recent than order A
	// with priority for currently active orders
	if orderB.IsActive != nil && *orderB.IsActive {
		return true
	}

	orderCondition := ((orderA.IsActive == nil || !*orderA.IsActive) && orderB.ExpiresAt.After(orderA.ExpiresAt))
	if orderA.OrderId == "" || orderCondition {
		return true
	}
	return false
}

func PickGeneralAccessOrder(
	ctx context.Context,
	plansClient planservice.PlanService,
	orders []accountservice.Order,
) (accountservice.Order, error) {
	if len(orders) == 0 {
		return accountservice.Order{}, nil
	}
	planIdToOrderMapping := make(map[uint64]accountservice.Order, len(orders))
	planIds := []uint64{}
	for _, order := range orders {
		planIdToOrderMapping[order.PlanId] = order
		planIds = append(planIds, order.PlanId)

	}
	plans, err := plansClient.GetPlansByIds(ctx, planIds)
	if err != nil {
		return accountservice.Order{}, err
	}

	if i := slices.IndexFunc(plans, func(p planservice.PlanV2) bool { return p.GeneralProviderAccess }); i != -1 {
		return planIdToOrderMapping[uint64(plans[i].Id)], nil // #nosec G115 plans.id is bigint so > 0
	}

	return accountservice.Order{}, nil
}

func IsPlanUpgrade(
	ctx context.Context,
	acctId string,
	generalAccessOrder accountservice.Order, // current general access order on account
	plan planservice.PlanV2, // plan to check for upgrade
	accountsClient accountservice.AccountService,
	plansClient planservice.PlanService,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx)
	var planUpgrade bool
	currentPlanID := int(
		generalAccessOrder.PlanId,
	) // #nosec G115 generalAccessOrder.PlanId is bigint so > 0
	existingPlan, err := plansClient.GetPlanById(
		ctx,
		int32(currentPlanID), // #nosec G115 currentPlanID is int so > 0
	)
	if err != nil {
		lg.WithError(err).Error("failed to fetch plan")
		return false, err
	} else if plan.Amount > existingPlan.Amount {
		planUpgrade = true
	}
	return planUpgrade, nil
}

/*
------------------------------------------------------------------
 ORDER CREATION
------------------------------------------------------------------
*/

// creates subscription order, sub type depends on paymentDetails.PlanId
func HandleGenericOrder(
	ctx context.Context,
	accountsClient accountservice.AccountService,
	accountID string,
	planId uint64,
	paymentToken string,
	providerId uint64,
	requestId string,
	requiresPayment bool,
	disableAutoRenew bool,
	source string,
) (accountservice.CreateOrderResponse, error) {
	if planId == 0 {
		return accountservice.CreateOrderResponse{}, errors.New("planId cannot be zero")
	}

	// TODO: accountservice expects a payment token always, even if the plan doesn't require payment
	if paymentToken == "" && !requiresPayment {
		paymentToken = "noop"
	}

	order, err := accountsClient.CreateOrder(ctx, accountID, accountservice.NewOrder{
		PlanId:       planId,
		PaymentToken: paymentToken,
		RegionId:     regions.GetRegionID(),
		Country:      regions.GetRegion(),
		// TODO: fill zip code from coreFe
		ZipCode:          "A1A1A1",
		OrgId:            providerId,
		RequestId:        requestId,
		DisableAutoRenew: disableAutoRenew,
		Source:           source,
	})
	return order, err
}

func ProcessPayment(
	ctx context.Context,
	lg logrus.FieldLogger,
	accountsClient accountservice.AccountService,
	plansClient planservice.PlanService,
	account *accountservice.Account,
	paymentDetails models.PaymentDetails,
	providerID int64,
) error {
	activeOrders, err := accountsClient.GetOrders(
		ctx,
		account.AccountId,
		map[string]bool{"active": true},
	)
	if err != nil {
		lg.WithError(err).Info("could not get active orders for the account")
		return errors.New("cannot get active orders for the account")
	}

	generalAccessOrder, err := PickGeneralAccessOrder(ctx, plansClient, activeOrders)
	if err != nil {
		lg.WithError(err).Info("could not pick generalAccessOrder")
		return errors.New("could not pick generalAccessOrder")
	}

	upgradePlan, err := plansClient.GetPlanById(
		ctx,
		int32(
			paymentDetails.PlanId,
		),
	) // #nosec G115 data.PaymentDetails.PlanId is int so > 0

	if err != nil {
		lg.WithError(err).Error("failed to fetch upgrade plan")
		return errors.New("failed to fetch upgrade plan")
	}

	processOrder := true // create order by default

	if generalAccessOrder.PlanId != 0 {
		isPlanUpgrade, err := IsPlanUpgrade(
			ctx,
			account.AccountId,
			generalAccessOrder,
			upgradePlan,
			accountsClient,
			plansClient,
		)

		if err != nil {
			lg.WithError(err).Error("failed to compute isPlanUpgrade")
			return errors.New("failed to compute isPlanUpgrade")
		}

		processOrder = isPlanUpgrade // create order only if it's a valid upgrade
	}

	// process payment/order creation only if planID passed is different and it's a plan upgrade
	if generalAccessOrder.PlanId != paymentDetails.PlanId && processOrder {
		_, err := HandleGenericOrder(
			ctx,
			accountsClient,
			account.AccountId,
			paymentDetails.PlanId,
			paymentDetails.Token,  // paymentToken
			uint64(providerID),    // #nosec G115 provider.LegacyId is int so > 0
			"",                    // requestID - not required to be passed in
			true,                  // requiresPayment
			false,                 // disableAutoRenew
			paymentDetails.Source, // source
		)
		if err != nil {
			lg.WithError(err).Info("could not create order for the selected plan")
			return errors.New("could not create order for the selected plan")
		}
	}
	return nil
}
