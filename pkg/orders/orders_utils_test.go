package orders

import (
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

func TestGetLatestGeneralAccessOrders(t *testing.T) {
	dateFormat := "2006-01-02"
	plans := map[uint64]planservice.PlanV2{
		2:  {GeneralProviderAccess: true, Amount: 50, FullUnlockAccess: true},
		16: {GeneralProviderAccess: true, Amount: 0, FullUnlockAccess: false},
	}
	lastPaidGeneralAccessOrderId := "123"
	lastUnpaidGeneralAccessOrderId := "456"
	orderIsActive := true

	t.Run("latest active paid + unpaid order exists", func(t *testing.T) {
		expiresAt, _ := time.Parse(dateFormat, "2000-01-01")
		orders := []accountservice.Order{
			{OrderId: "abc", PlanId: 2, ExpiresAt: expiresAt},
			{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, IsActive: &orderIsActive},
			{OrderId: "def", PlanId: 16, ExpiresAt: expiresAt},
			{OrderId: lastUnpaidGeneralAccessOrderId, PlanId: 16, IsActive: &orderIsActive},
		}
		lastPaidGeneralAccessOrder := GetLatestGeneralAccessOrder(
			plans, orders,
		)
		if lastPaidGeneralAccessOrder.OrderId != lastPaidGeneralAccessOrderId {
			t.Errorf("expected lastPaidGeneralAccessOrder to have orderID: %s, got: %s",
				lastPaidGeneralAccessOrderId,
				lastPaidGeneralAccessOrder.OrderId,
			)
		}
	})

	t.Run("latest paid order exists", func(t *testing.T) {
		expiresAt1, _ := time.Parse(dateFormat, "2000-01-01")
		expiresAt2, _ := time.Parse(dateFormat, "2000-01-02")
		expiresAt3, _ := time.Parse(dateFormat, "2000-01-03")
		orders := []accountservice.Order{
			{OrderId: "abc", PlanId: 2, ExpiresAt: expiresAt1},
			{OrderId: "def", PlanId: 2, ExpiresAt: expiresAt2},
			{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, ExpiresAt: expiresAt3},
		}
		lastPaidGeneralAccessOrder := GetLatestGeneralAccessOrder(
			plans, orders,
		)
		if lastPaidGeneralAccessOrder.OrderId != lastPaidGeneralAccessOrderId {
			t.Errorf(
				"expected lastPaidGeneralAccessOrder to have orderID: %s",
				lastPaidGeneralAccessOrderId,
			)
		}
	})
}
