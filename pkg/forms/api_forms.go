/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package forms

import (
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
)

// A TransfersApiController binds http requests to an api service and writes the service results to the http response
type RequestFormConfigurationApiController struct {
	service         coreapi.RequestFormConfigurationApiServicer
	ampCookieHeader string
}

// NewTransfersApiController creates a default api controller
func NewRequestFormConfigurationApiController(
	s coreapi.RequestFormConfigurationApiServicer,
	ampCookieHeader string,
) coreapi.Router {
	return &RequestFormConfigurationApiController{service: s, ampCookieHeader: ampCookieHeader}
}

// Routes returns all of the api route for the TransfersApiController
func (c *RequestFormConfigurationApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		coreapi.Route{
			Name:        "GetForm",
			Method:      "GET",
			Pattern:     "",
			HandlerFunc: c.GetForm,
		},
	}
}

func (c *RequestFormConfigurationApiController) GetPathPrefix() string {
	return "/v1/request-form-config"
}

func (c *RequestFormConfigurationApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *RequestFormConfigurationApiController) GetForm(
	w http.ResponseWriter,
	r *http.Request,
) {
	query := r.URL.Query()
	language := r.Header.Get("Language")
	inApp, err := convertBoolString(query.Get("inApp"))
	if err != nil {
		makeBadReqResp(w, r)
		return
	}
	formId := query.Get("formId")
	providerUrl := query.Get("providerUrl")
	deviceId := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)
	providerIdString := query.Get("providerId")
	var providerId int64
	if providerIdString != "" {
		providerId, err = strconv.ParseInt(query.Get("providerId"), 10, 64)
		if err != nil {
			makeBadReqResp(w, r)
			return
		}
	}

	if formId == "" && providerId == 0 {
		if providerUrl == "" {
			makeBadReqResp(w, r)
			return
		} else if strings.Contains(providerUrl, "?") || strings.Contains(providerUrl, "=") {
			// Probably a query string that has been errorneously passed
			makeBadReqResp(w, r)
			return
		}
	}

	var result orgs.FormResponse
	if providerUrl != "" {
		result, err = c.service.GetFormByProviderUrl(r.Context(), providerUrl, inApp, deviceId, language)
	} else if formId != "" {
		result, err = c.service.GetFormById(r.Context(), formId, inApp, language)
	} else {
		result, err = c.service.GetFormByProviderId(
			r.Context(), providerId, inApp, deviceId, language,
		)
	}
	c.handleResponse(w, r, result, err)
}

func (c *RequestFormConfigurationApiController) handleResponse(
	w http.ResponseWriter,
	r *http.Request,
	result any,
	err error,
) {
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_EXISTS {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusNotFound),
				http.StatusNotFound,
			)
			return
		} else {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusInternalServerError),
				http.StatusInternalServerError,
			)
			return
		}
	}

	coreapi.EncodeJSONResponseNoHTMLEscape(r.Context(), result, nil, w)
}

func makeBadReqResp(w http.ResponseWriter, r *http.Request) {
	httperror.ErrorWithLog(
		w,
		r,
		http.StatusText(http.StatusBadRequest),
		http.StatusBadRequest,
	)
}

func convertBoolString(param string) (bool, error) {
	if param == "" {
		return false, nil
	}
	result, err := strconv.ParseBool(param)
	if err != nil {
		return false, err
	}
	return result, nil
}
