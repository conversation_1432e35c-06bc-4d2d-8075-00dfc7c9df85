/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package forms

import (
	"context"
	"database/sql"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/amplitude_util"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RequestFormConfigurationApiService struct {
	orgSvc            orgs.OrgService
	sqldb             *sql.DB
	eventClient       interfaces.AmplitudeEventClient
	experimentsClient interfaces.AmplitudeExperimentClient
}

func NewRequestFormConfigurationApiService(
	orgSvc orgs.OrgService,
	sqldb *sql.DB,
	eventClient interfaces.AmplitudeEventClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
) coreapi.RequestFormConfigurationApiServicer {
	return &RequestFormConfigurationApiService{
		orgSvc:            orgSvc,
		sqldb:             sqldb,
		eventClient:       eventClient,
		experimentsClient: experimentsClient,
	}
}

func (s *RequestFormConfigurationApiService) GetFormById(
	ctx context.Context,
	formId string,
	inApp bool,
	language string,
) (orgs.FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	formResponse, err := s.orgSvc.GetFormById(ctx, formId, inApp, language)
	if err != nil {
		lg.WithError(err).Error("error fetching form by ID")
		return orgs.FormResponse{}, err
	}
	return formResponse, nil
}

func (s *RequestFormConfigurationApiService) GetFormByProviderId(
	ctx context.Context,
	providerId int64,
	inApp bool,
	deviceId string,
	language string,
) (orgs.FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	formResponse, err := s.orgSvc.GetFormByLegacyProviderId(ctx, providerId, inApp, language)
	if err != nil {
		lg.WithError(err).Error("error fetching form by providerID")
		return orgs.FormResponse{}, err
	}

	formResponse.Provider.Region = s.GetRegion(ctx, formResponse, deviceId)
	formResponse.Provider.IsFacilityFunded = formResponse.Provider.GetIsFacilityFunded()

	return formResponse, nil
}

func (s *RequestFormConfigurationApiService) GetFormByProviderUrl(
	ctx context.Context,
	providerUrl string,
	inApp bool,
	deviceId string,
	language string,
) (orgs.FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	formResponse, err := s.orgSvc.GetFormByProviderUrl(ctx, providerUrl, inApp, language)
	if err != nil {
		lg.WithError(err).Error("error fetching form by providerUrl")
		return orgs.FormResponse{}, err
	}
	formResponse.Provider.Region = s.GetRegion(ctx, formResponse, deviceId)
	formResponse.Provider.IsFacilityFunded = formResponse.Provider.GetIsFacilityFunded()

	return formResponse, nil
}

// Override region for US providers not yet migrated from CA db
func (s *RequestFormConfigurationApiService) GetRegion(
	ctx context.Context,
	formResponse orgs.FormResponse,
	deviceId string,
) string {
	lg := logutils.DebugCtxLogger(ctx)

	if formResponse.Provider.Region == "US" {
		experiments, err := s.experimentsClient.Fetch(
			&experiment.User{DeviceId: deviceId},
		)
		if err != nil {
			lg.WithError(err).
				Infof("unable to fetch experiment for deviceID: %s", deviceId)
		}

		if variant, ok := experiments[amplitude_util.MISMATCH_REGION_PROVIDERS]; ok &&
			amplitude_util.IsEligibleProvider(
				uint64(formResponse.ProviderId), // #nosec G115 provider ID > 0
				variant.Payload.(map[string]any),
			) {
			return "CA"
		}
	}

	return formResponse.Provider.Region
}
