package forms

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
)

func TestGetForm(t *testing.T) {
	service := mockcoreapi.NewMockRequestFormConfigurationApiServicer(t)
	controller := NewRequestFormConfigurationApiController(service, "")
	router, err := coreapi.NewRouter(controller)
	require.NoError(t, err)

	t.Run("succeeds with valid provider ID", func(t *testing.T) {
		service.EXPECT().GetFormByProviderId(mock.Anything, int64(168), false, "", "").
			Return(orgs.FormResponse{}, nil)

		req, err := http.NewRequest(http.MethodGet, "/v1/request-form-config?providerId=168", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("succeeds with valid provider Url", func(t *testing.T) {
		service.EXPECT().GetFormByProviderUrl(mock.Anything, "kitchener", false, "", "").
			Return(orgs.FormResponse{}, nil)

		req, err := http.NewRequest(http.MethodGet, "/v1/request-form-config?providerUrl=kitchener", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("succeeds with valid form ID", func(t *testing.T) {
		service.EXPECT().GetFormById(mock.Anything, "2alh8oMuPGAAFjkqm7QtfdyT956", false, "").
			Return(orgs.FormResponse{}, nil)

		req, err := http.NewRequest(http.MethodGet, "/v1/request-form-config?formId=2alh8oMuPGAAFjkqm7QtfdyT956", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("responds with Bad Request if bad provider URL", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodGet, "/v1/request-form-config?providerUrl=viewexam?examId=test-id&reports=false", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusBadRequest, rr.Code)
	})

	t.Run("responds with Bad Request if no provider identifier", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodGet, "/v1/request-form-config", nil)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusBadRequest, rr.Code)
	})
}
