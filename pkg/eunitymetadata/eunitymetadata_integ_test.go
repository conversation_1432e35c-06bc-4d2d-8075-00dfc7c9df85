//go:build integration
// +build integration

package eunitymetadata

import (
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestGetMetadata(t *testing.T) {
	t.Skip("this test is skipped for now")
	// db := testutils.SetupTestDB(t)

	// t.Run("more than 100 objects", func(t *testing.T) {
	// 	shareId := "q6l3uFGy3eF_--TKnx-f0q8C4xc1s4P3tZ8g_1Vlcwk="
	// 	rows, err := db.Query("select object_id from share_objects2 where share_id = ?", shareId)
	// 	if err != nil {
	// 		t.Fatalf("could not get object ids for share: %v", err)
	// 	}
	// 	defer rows.Close()
	// 	objIds := make([]string, 0)
	// 	for rows.Next() {
	// 		var id string
	// 		rows.Scan(&id)
	// 		objIds = append(objIds, id)
	// 	}
	// 	metadata, err := generateShareMetadataFromSQL(context.Background(), db, objIds, shareId)
	// 	if err != nil {
	// 		t.Fatalf("got err generating metadata when expected none: %v", err)
	// 	}
	// 	for _, ex := range metadata.StudyList {
	// 		for _, ser := range ex.SeriesList {
	// 			sort.SliceStable(ser.ImageList, func(i, j int) bool {
	// 				return ser.ImageList[i].ImageId < ser.ImageList[j].ImageId
	// 			})
	// 		}
	// 		sort.SliceStable(ex.SeriesList, func(i, j int) bool {
	// 			return ex.SeriesList[i].SeriesUID < ex.SeriesList[j].SeriesUID
	// 		})
	// 	}
	// 	sort.SliceStable(metadata.StudyList, func(i, j int) bool {
	// 		if metadata.StudyList[i].UID == metadata.StudyList[j].UID {
	// 			return metadata.StudyList[i].StudyDate < metadata.StudyList[j].StudyDate
	// 		}
	// 		return metadata.StudyList[i].UID < metadata.StudyList[j].UID
	// 	})
	// 	expectedBytes, err := ioutil.ReadFile("testresults/expected.json")
	// 	if err != nil {
	// 		t.Fatalf("could not read expected metadata result: %v", err)
	// 	}
	// 	var expectedMetadata Metadata
	// 	err = json.Unmarshal(expectedBytes, &expectedMetadata)
	// 	if err != nil {
	// 		t.Fatalf("could not unmarshal expected metadata result: %v", err)
	// 	}

	// 	// ---------- REMOVE------------
	// 	f, err := os.Create("testresults/potential.json")
	// 	if err != nil {
	// 		fmt.Println(err)
	// 	}
	// 	// close the file with defer
	// 	defer f.Close()

	// 	// do operations
	// 	res, err := json.Marshal(metadata)
	// 	if err != nil {
	// 		t.Fatalf("err marshalling JSON: %s", err)
	// 	}

	// 	_, err = f.Write(res)
	// 	if err != nil {
	// 		t.Fatalf("err writing to file: %s", err)
	// 	}
	// 	// ---------- REMOVE------------

	// 	assert.ElementsMatch(t, expectedMetadata.StudyList, metadata.StudyList)

	// })
}
