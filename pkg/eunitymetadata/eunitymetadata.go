package eunitymetadata

import (
	"context"
	"database/sql"
	"encoding/xml"
	"fmt"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/helpers"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	sqlShareObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

const (
	maxObjBatchSize = 100
	DEFAULT_DCM_UID = "DEFAULT"
)

type Metadata struct {
	XMLName   xml.Name `xml:"metadata"`
	StudyList []Study  `xml:"StudyList>Study"`
}

type Study struct {
	UID                string   `xml:"uid,attr"`
	Description        string   `xml:"StudyDescription,omitempty"`
	AccessionNumber    string   `xml:"AccessionNumber,omitempty"`
	StudyDate          string   `xml:"StudyDate,omitempty"`
	StudyTime          string   `xml:"StudyTime,omitempty"`
	ReferringPhysician string   `xml:"ReferringPhysicianName,omitempty"`
	Patient            Patient  `xml:"Patient,omitempty"`
	ViewPosition       string   `xml:"ViewPosition,omitempty"`
	SeriesList         []Series `xml:"SeriesList>Series"`
}

func (s *Study) AddNewSeriesWithObject(modality string, obj Image, isReport bool) {
	var newSeries Series
	newSeries.SeriesUID = obj.SeriesUid
	newSeries.SeriesNumber = obj.SeriesNum
	if isReport {
		newSeries.Modality = "DOC"
		// Appended PDF reports don't have a native seriesUID or imageUID
		// We need to explicitly set them to a default dummy value so that these attributes are included in the eunity XML
		// EUnity requires these UIDs to be set to consider the XML valid for report rendering
		if newSeries.SeriesUID == "" {
			newSeries.SeriesUID = DEFAULT_DCM_UID
		}
		if obj.ImageUID == "" {
			obj.ImageUID = DEFAULT_DCM_UID
		}
	} else {
		newSeries.Modality = modality
	}
	newSeries.ImageList = append(newSeries.ImageList, obj)
	s.SeriesList = append(s.SeriesList, newSeries)
}

type Patient struct {
	ID       string `xml:"id,attr,omitempty"`
	Name     string `xml:"PatientName,omitempty"`
	Birthday string `xml:"PatientBirthDate,omitempty"`
	Sex      string `xml:"PatientSex,omitempty"`
}

type Series struct {
	SeriesUID    string  `xml:"uid,attr,omitempty"`
	SeriesNumber int     `xml:"SeriesNumber,omitempty"`
	Modality     string  `xml:"Modality,omitempty"`
	ImageList    []Image `xml:"ImageList>Image"`
}

type Image struct {
	StudyUid            string `xml:"-"`
	ImageId             string `xml:"-"`
	ImagePath           string `xml:"Path"`
	SeriesNum           int    `xml:"-"`
	SeriesUid           string `xml:"-"`
	ImageUID            string `xml:"uid,attr,omitempty"`
	ImageLaterality     string `xml:"ImageLaterality,omitempty"`
	InstanceNumber      int    `xml:"InstanceNumber,omitempty"`
	ContentDate         string `xml:"ContentDate,omitempty"`
	ContentTime         string `xml:"ContentTime,omitempty"`
	AcquisitionDate     string `xml:"AcquisitionDate,omitempty"`
	AcquisitionTime     string `xml:"AcquisitionTime,omitempty"`
	ImageType           string `xml:"ImageType,omitempty"`
	NumberOfFrames      int    `xml:"NumberOfFrames,omitempty"`
	Rows                int    `xml:"Rows,omitempty"`
	Columns             int    `xml:"Columns,omitempty"`
	PixelSpacing        string `xml:"PixelSpacing,omitempty"`
	ImagePosition       string `xml:"ImagePosition,omitempty"`
	ImageOrientation    string `xml:"ImageOrientation,omitempty"`
	FrameOfReferenceUID string `xml:"FrameOfReferenceUID,omitempty"`
	SopClassUID         string `xml:"SopClassUID,omitempty"`
}

func GenerateShareMetadata(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (StudyXML []byte, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
	})
	share, err := sqlShareObjects.GetShare(ctx, db, shareId)
	if err != nil {
		return StudyXML, err
	}
	imgIds := share.Images
	reportIds := share.Reports
	var studyXml []byte
	data, err := generateShareMetadataFromSQL(ctx, db, append(imgIds, reportIds...), shareId)
	if err != nil {
		lg.WithError(err).Error("sql metadata error")
	}
	studyXml, err = xml.Marshal(data)
	if err != nil {
		return studyXml, err
	}
	studyXml = []byte(xml.Header + string(studyXml))
	logutils.DebugCtxLogger(ctx).
		WithField("share_id", shareId).
		Info("generated eUnity metadata from sql")
	return studyXml, err
}

func generateShareMetadataFromSQL(
	ctx context.Context,
	db *sql.DB,
	objIds []string,
	shareId string,
) (shareMetadata Metadata, err error) {
	var data Metadata
	//build studies as we iterate into a map for easy reference
	studyMap := make(map[string]Study, 0)
	totalObjs := len(objIds)

	//batch up objs -> the query has a big WHERE object_id IN () caluse, so want to avoid large lists
	for start := 0; start < totalObjs; start += maxObjBatchSize {
		var end int
		if totalObjs < start+maxObjBatchSize {
			end = totalObjs
		} else {
			end = start + maxObjBatchSize
		}
		rows, err := doMetadataQuery(ctx, db, objIds[start:end])
		if err != nil {
			return data, err
		}

		defer rows.Close()
		for rows.Next() {
			im, ex, isReport, isDnd, modality, err := buildStructs(rows)
			if err != nil {
				return data, err
			}
			im.ImagePath = "http://REPLACE_ON_SERVE/v1/" + shareId + "/dcm/" + im.ImageId
			if isReport && isDnd {
				continue
				//Don't include drag n drop reports
			}

			//already added this exam
			if study, ok := studyMap[im.StudyUid]; ok {
				if isReport {
					study.AddNewSeriesWithObject("", im, isReport)
					studyMap[im.StudyUid] = study
				} else {
					//check if series is already added.
					var added bool
					for i := range study.SeriesList {
						if study.SeriesList[i].SeriesUID == im.SeriesUid {
							//add image to series
							study.SeriesList[i].ImageList = append(study.SeriesList[i].ImageList, im)
							added = true
							break
						}
					}
					if !added {
						//add new series with image
						study.AddNewSeriesWithObject(modality, im, isReport)
						studyMap[im.StudyUid] = study
					}
				}
			} else {
				study := ex
				study.SeriesList = make([]Series, 0)
				study.AddNewSeriesWithObject(modality, im, isReport)
				studyMap[study.UID] = study
			}
		}
	}
	studyList := make([]Study, 0)
	for _, study := range studyMap {
		studyList = append(studyList, study)
	}
	data.StudyList = studyList

	return data, err
}

func buildStructs(
	sqlRows *sql.Rows,
) (im Image, ex Study, isReport bool, isDnd bool, modality string, err error) {
	var exDesc, accessionNum, exDate, ptId, ptName, ptDob, ptSex string
	var serUid, dcmUid, lat, contentDate, contentTime, acquisitionDate, acquisitionTime, imType, pixSpacing,
		imOrientation, imPos, forUid, sopUid, exTime, viewPos sql.NullString
	var serNum, numFrames, rows, cols sql.NullInt64
	err = sqlRows.Scan(
		&isReport,
		&isDnd,
		&im.ImageId,
		&serNum,
		&serUid,
		&lat,
		&im.InstanceNumber,
		&contentDate,
		&contentTime,
		&acquisitionDate,
		&acquisitionTime,
		&imType,
		&numFrames,
		&rows,
		&cols,
		&forUid,
		&sopUid,
		&im.StudyUid,
		&exDesc,
		&accessionNum,
		&exDate,
		&exTime,
		&ptId,
		&ptName,
		&ptDob,
		&ptSex,
		&viewPos,
		&modality,
		&dcmUid,
	)
	if err != nil {
		return im, ex, false, false, "", err
	}

	if serUid.Valid {
		im.SeriesUid = serUid.String
	}
	if serNum.Valid {
		im.SeriesNum = int(serNum.Int64)
	}
	if lat.Valid {
		im.ImageLaterality = lat.String
	}
	if contentDate.Valid {
		im.ContentDate = contentDate.String
	}
	if contentTime.Valid {
		im.ContentTime = contentTime.String
	}
	if acquisitionDate.Valid {
		im.AcquisitionDate = acquisitionDate.String
	}
	if acquisitionTime.Valid {
		im.AcquisitionTime = acquisitionTime.String
	}
	if imType.Valid {
		im.ImageType = imType.String
	}
	if numFrames.Valid {
		im.NumberOfFrames = int(numFrames.Int64)
	}
	if rows.Valid {
		im.Rows = int(rows.Int64)
	}
	if cols.Valid {
		im.Columns = int(cols.Int64)
	}
	if pixSpacing.Valid {
		im.PixelSpacing = pixSpacing.String
	}
	if imPos.Valid {
		im.ImagePosition = imPos.String
	}
	if imOrientation.Valid {
		im.ImageOrientation = imOrientation.String
	}
	if forUid.Valid {
		im.FrameOfReferenceUID = forUid.String
	}
	if sopUid.Valid {
		im.SopClassUID = sopUid.String
	}
	if dcmUid.Valid {
		im.ImageUID = dcmUid.String
	}

	pt := Patient{ID: ptId, Name: ptName, Birthday: ptDob, Sex: ptSex}
	ex = Study{
		UID:             im.StudyUid,
		Patient:         pt,
		AccessionNumber: accessionNum,
		StudyDate:       exDate,
		Description:     exDesc,
	}
	if exTime.Valid {
		ex.StudyTime = exTime.String
	}
	if viewPos.Valid {
		ex.ViewPosition = viewPos.String
	}
	return im, ex, isReport, isDnd, modality, nil
}

func doMetadataQuery(ctx context.Context, db *sql.DB, objIds []string) (*sql.Rows, error) {
	sqlArgStr := mysql.CreateSqlArgString(len(objIds))
	args := make([]interface{}, len(objIds))
	for i, o := range objIds {
		args[i] = o
	}
	return mysqlWithLog.Query(
		ctx,
		db,
		fmt.Sprintf(
			`SELECT o.is_report, g.user_type IS NOT NULL AND g.user_type = 'dnd', o.object_id, s.series_num, s.series_uid, vm.image_laterality, o.instance_number, vm.content_date, vm.content_time,
	vm.acquisition_date, vm.acquisition_time, vm.image_type, vm.number_frames, vm.rows, vm.columns, vm.frame_of_reference_uid, vm.sop_class_uid, e.exam_uid, 
	e.description, e.accession_number, e.date, vm.study_time, e.patient_mrn, e.patient_name, e.dob, e.sex, vm.view_position, o.modality, vm.dcm_uid
	FROM objects o
	JOIN object_mappings om ON o.object_id = om.object_id
	JOIN exams e ON om.exam_uuid = e.uuid
	JOIN scans sc ON e.transfer_id = sc.scan_id
    LEFT JOIN gateway_users g on sc.origin_id = g.user_id
	LEFT JOIN view_metadata vm ON o.object_id = vm.object_id
	LEFT JOIN series s ON om.series_uid = s.series_uid and e.uuid = s.exam_uuid
	WHERE o.object_id IN (%s) AND %s 
	ORDER BY e.uuid, s.series_uid`,
			sqlArgStr,
			exams.ExamNotRevokedWhere,
		),
		args...)
}
