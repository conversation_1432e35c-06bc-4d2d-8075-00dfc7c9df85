package v2features

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PublicFeaturesApiController binds http requests to an api service and writes the service results to the http response
type PublicFeaturesApiController struct {
	service coreapi.V2FeatureApiServicer
}

// NewPublicFeaturesApiController creates a default api controller
func NewPublicFeaturesApiController(
	s coreapi.V2FeatureApiServicer,
) coreapi.PublicV2FeatureApiRouter {
	return &PublicFeaturesApiController{service: s}
}

// Routes returns all of the api route for the V2UsersApiController
func (c *PublicFeaturesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "AuthorizeFeature",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/authorize",
			HandlerFunc: c.AuthorizeFeature,
		},
	}
}

func (c *PublicFeaturesApiController) GetPathPrefix() string {
	return "/v2/features"
}

func (c *PublicFeaturesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

// AuthorizeFeature
func (c *PublicFeaturesApiController) AuthorizeFeature(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	var err error
	pathParams := mux.Vars(r)

	featureIdStr := pathParams["id"]
	var featureId uint64
	if featureIdStr != "" {
		featureId, err = strconv.ParseUint(featureIdStr, 10, 0)
		if err != nil {
			lg.WithField("feature_id_param", featureIdStr).Error("invalid id route param")
			w.WriteHeader(http.StatusBadRequest)
			return
		}
	}

	params := r.URL.Query()

	acctId := params.Get("account_id")

	var orgId uint64
	orgIdStr := params.Get("org_id")
	if orgIdStr != "" {
		orgId, err = strconv.ParseUint(orgIdStr, 10, 0)
		if err != nil {
			lg.WithField("org_id_param", orgIdStr).Error("invalid org_id")
			w.WriteHeader(http.StatusBadRequest)
			return
		}
	}

	if acctId == "" && orgId <= 0 {
		lg.Error("at least one of acctId, org_id must be defined")
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// if acctId is used as a param, validate that the user has an auth token with that acctId
	if acctId != "" {
		token := r.Header.Get("Authorization")
		if token == "" {
			lg.Error("acctId param provided, but missing auth token")
			w.WriteHeader(http.StatusUnauthorized)
			return
		}
		acctIdClaim, err := auth.DecodeAccountToken(token)
		if err != nil {
			lg.WithError(err).Error("unable to decode auth token")
			w.WriteHeader(http.StatusUnauthorized)
			return
		}
		if acctIdClaim != acctId {
			lg.
				WithField("acct_id_param", acctId).
				WithField("acct_id_claim", acctIdClaim).
				Error("acct in token and param don't match")
			w.WriteHeader(http.StatusForbidden)
			return
		}
	}

	features, err := c.service.AuthorizeFeature(
		r.Context(),
		planservice.FeatureId(featureId),
		acctId,
		uint(orgId),
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), features, nil, w)
}
