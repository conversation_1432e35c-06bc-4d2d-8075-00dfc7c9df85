package v2features

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// FeaturesApiService is a service that implents the logic for the FeatureApiServicer
// This service should implement the business logic for every endpoint for the Features API.
// Include any external packages or services that will be required by this service.
type FeaturesApiService struct {
	planService planservice.PlanService
	acctService accountservice.AccountService
}

// NewFeaturesApiService creates a default api service
func NewFeaturesApiService(
	planSvc planservice.PlanService,
	acctService accountservice.AccountService,
) coreapi.V2FeatureApiServicer {
	return &FeaturesApiService{
		planService: planSvc,
		acctService: acctService,
	}
}

// GetFeatures
func (s *FeaturesApiService) AuthorizeFeature(
	ctx context.Context,
	featureId planservice.FeatureId,
	acctId string,
	orgId uint,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).
		WithField("feature_id", featureId).
		WithField("acct_id", acctId).
		WithField("org_id", orgId)

	result, err := s.planService.AuthorizeFeature(
		ctx,
		featureId,
		planservice.FeatureAuthorizationParams{
			AccountId:  acctId,
			ProviderId: uint64(orgId),
		},
	)
	if err != nil {
		lg.WithError(err).Error("unable to authorize feature")
		return nil, err
	}

	return result, nil
}
