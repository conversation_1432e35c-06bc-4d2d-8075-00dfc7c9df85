package exams

import (
	"context"
	"database/sql"
	"strconv"

	"github.com/samber/lo"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/examutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

type ExamService struct {
	db                   *sql.DB
	orgServiceClient     orgservice.OrgService
	recordServiceClient  recordservice.RecordServiceClientInterface
	planServiceClient    planservice.PlanService
	accountServiceClient accountservice.AccountService
	providersService     providersservice.ProvidersService
	migrationHelper      MigrationHelperInterface
}

func NewExamService(
	db *sql.DB,
	orgServiceClient orgservice.OrgService,
	recordServiceClient recordservice.RecordServiceClientInterface,
	planServiceClient planservice.PlanService,
	accountServiceClient accountservice.AccountService,
	providersService providersservice.ProvidersService,
	migrationHelper MigrationHelperInterface,
) ExamServiceInterface {
	return &ExamService{
		db:                   db,
		orgServiceClient:     orgServiceClient,
		recordServiceClient:  recordServiceClient,
		planServiceClient:    planServiceClient,
		accountServiceClient: accountServiceClient,
		providersService:     providersService,
		migrationHelper:      migrationHelper,
	}
}

func (e *ExamService) GetExam(
	ctx context.Context,
	accountID string,
	UUID string,
) (coreapi.ExamRaw, error) {
	return e.getRecordServiceExamRaw(
		ctx,
		accountID,
		UUID,
		true, // includeReports
		true, // includeInstances
	)
}

func (e *ExamService) GetExamBasic(
	ctx context.Context,
	accountID string,
	UUID string,
) (coreapi.ExamRawBasic, error) {
	exam, err := e.getRecordServiceExamRaw(
		ctx,
		accountID,
		UUID,
		false, // includeReports
		false, // includeInstances
	)
	if err != nil {
		return coreapi.ExamRawBasic{}, err
	}
	if exam.UUID == "" {
		return coreapi.ExamRawBasic{}, sql.ErrNoRows
	}
	return exam.ExamRawBasic, nil

}

func (e *ExamService) GetLastNameFromExam(ctx context.Context, UUID string) (string, error) {
	return sqlExams.GetLastNameFromExam(ctx, e.db, UUID)
}

func (e *ExamService) GetExamsResponse(
	ctx context.Context,
	accountID string,
	UUIDs []string,
	q queries.Queries,
	includeReports bool,
	excludeDeleted bool,
) ([]coreapi.Exam, error) {
	activated, UUIDs := e.getQueryParamsFromContext(ctx, UUIDs)
	exams, err := e.getRecordServiceExamsList(
		ctx,
		accountID,
		UUIDs,
		includeReports,
	)
	if err != nil {
		return []coreapi.Exam{}, err
	}
	exams = e.getExamsFilteredByActivated(activated, exams)
	return exams, nil
}

func (e *ExamService) GetExamSummaryData(
	ctx context.Context,
	accountID string,
	UUIDs []string,
	q queries.Queries,
	includeReports bool,
	excludeDeleted bool,
) (exams []coreapi.ExamRaw, err error) {
	if e.migrationHelper.MigrateGetExamSummaryData(ctx) {
		_, UUIDs := e.getQueryParamsFromContext(ctx, UUIDs)
		return e.getRecordServiceExamsRawList(
			ctx,
			accountID,
			UUIDs,
			includeReports,
			false, // includeInstances
			true,  // excludeIncompleteStudies
		)
	}
	return sqlExams.GetExamSummaryData(
		ctx,
		e.db,
		accountID,
		UUIDs,
		q,
		includeReports,
		excludeDeleted,
		e.orgServiceClient,
	)
}

func (e *ExamService) GetExamSummaryDataForRecordStreamingStudy(
	ctx context.Context,
	examUUID string,
	providerID int64,
) (coreapi.ExamRaw, error) {
	return sqlExams.GetExamSummaryDataForRecordStreamingStudy(ctx, e.db, examUUID, providerID)
}

func (e *ExamService) GetFirstImageID(
	ctx context.Context,
	accountID string,
	UUID string,
) (string, error) {
	if e.migrationHelper.MigrateGetFirstImageID(ctx) {
		exam, err := e.getRecordServiceExamRaw(
			ctx,
			accountID,
			UUID,
			false, // includeReports
			true,  // includeInstances
		)
		if err != nil {
			return "", err
		}
		if len(exam.Series) > 0 && len(exam.Series[0].Instances) > 0 {
			return exam.Series[0].Instances[0].ImageId, nil
		}
		return "", nil
	}
	return sqlExams.GetFirstImageID(ctx, e.db, UUID)
}

func (e *ExamService) GetUserExamsSize(
	ctx context.Context,
	accountID string,
) (size int64, err error) {
	exams, err := e.getRecordServiceExamsRawList(
		ctx,
		accountID,
		[]string{}, // UUIDs
		true,       // excludeIncompleteStudies
		true,       // includeInstances
		false,      // includeReports
	)
	if err != nil {
		return 0, err
	}
	var totalSize int64 = 0
	for _, exam := range exams {
		totalSize += int64(exam.Size)
	}
	return totalSize, nil

}

func (e *ExamService) GetObjectIDsByExamUUIDs(
	ctx context.Context,
	accountID string,
	UUIDs []string,
	activated bool,
) ([]string, error) {
	exams, err := e.getRecordServiceExamsRawList(
		ctx,
		accountID,
		UUIDs,
		true,  // includeReports
		true,  // includeInstances
		false, // excludeIncompleteStudies
	)
	if err != nil {
		return []string{}, err
	}
	objectIDs := []string{}
	for _, exam := range exams {
		for _, report := range exam.Reports {
			objectIDs = append(objectIDs, report.ReportId)
		}
		for _, series := range exam.Series {
			for _, instance := range series.Instances {
				objectIDs = append(objectIDs, instance.ImageId)
			}
		}
	}
	return objectIDs, nil
}

func (e *ExamService) ExamsBelongToAccount(
	ctx context.Context,
	accountID string,
	UUIDs []string,
) (bool, error) {
	return e.recordServiceClient.MatchPatientStudies(ctx, accountID, UUIDs)
}

func (e *ExamService) GetExamUUIDListForShare(
	ctx context.Context,
	shareID string,
) ([]string, error) {
	return sqlExams.GetExamUUIDListForShare(ctx, e.db, shareID)
}

func (e *ExamService) GetUnassociatedReports(
	ctx context.Context,
	accountID string,
	activated bool,
	excludeDeleted bool,
) ([]coreapi.Report, error) {
	return sqlExams.GetUnassociatedReports(
		ctx,
		e.db,
		accountID,
		activated,
		excludeDeleted,
		e.orgServiceClient,
	)
}

func (e *ExamService) GetPatientNameAndDOB(
	ctx context.Context,
	accountID string,
) (patientName string, dob string, err error) {
	return sqlExams.GetPatientNameAndDOB(ctx, e.db, accountID)
}

func (e *ExamService) GetExamPatientID(
	ctx context.Context,
	accountID string,
	UUID string,
) (string, error) {
	exam, err := e.getRecordServiceExamRaw(
		ctx,
		accountID,
		UUID,
		false, // includeReports
		false, // includeInstances
	)
	if err != nil {
		return "", err
	}
	if exam.UUID == "" {
		return "", sql.ErrNoRows
	}
	return exam.PatientId, nil
}

func (e *ExamService) GetPatientDataAndReferringPhysicianByExamUUID(
	UUID string,
) (string, string, string, error) {
	return sqlExams.GetPatientDataAndReferringPhysicianByExamUUID(e.db, UUID)
}

func (e *ExamService) GetReferringPhysiciansByShareID(
	shareID string,
) ([]string, error) {
	return sqlExams.GetReferringPhysiciansByShareID(e.db, shareID)
}

func (e *ExamService) GetPatientBirthDateByPatientID(
	ctx context.Context,
	accountID string,
	patientID string,
) (string, error) {
	exams, err := e.getRecordServiceExamsRawList(
		ctx,
		accountID,
		[]string{}, // UUIDs
		false,      // excludeIncompleteStudies
		false,      // includeInstances
		false,      // includeReports
	)
	if err != nil {
		return "", err
	}
	for _, exam := range exams {
		if exam.PatientId == patientID {
			return exam.DICOMBirthDate, nil
		}
	}
	return "", sql.ErrNoRows
}

func (e *ExamService) GetExamInfoForSubdictionary(
	ctx context.Context,
	accountID string,
	UUID string,
) (string, string, string, error) {
	exam, err := e.getRecordServiceExamRaw(
		ctx,
		accountID,
		UUID,
		false, // includeReports
		false, // includeInstances
	)
	if err != nil {
		return "", "", "", err
	}
	if exam.UUID == "" {
		return "", "", "", sql.ErrNoRows
	}
	return exam.Modality, exam.BodyPart, exam.Description, nil
}

func (e *ExamService) GetAllReportIDsForPatient(
	ctx context.Context,
	accountID string,
	patientID string,
) (map[string][]string, error) {
	examUUIDToReportIDs := map[string][]string{}
	exams, err := e.getRecordServiceExamsRawList(
		ctx,
		accountID,
		[]string{}, // UUIDs
		true,       // includeReports
		false,      // includeInstances
		false,      // excludeIncompleteStudies
	)
	if err != nil {
		return examUUIDToReportIDs, err
	}

	for _, exam := range exams {
		// only include reports for the given patientID
		// TODO - introduce a record service filter on patientID
		if patientID != "" && exam.PatientId != patientID {
			continue
		}

		reportIDs := []string{}
		for _, report := range exam.Reports {
			reportIDs = append(reportIDs, report.ReportId)
		}
		if len(reportIDs) > 0 {
			examUUIDToReportIDs[exam.UUID] = reportIDs
		}
	}
	return examUUIDToReportIDs, nil

}

// -------------------- record service integration  --------------------

func (s *ExamService) getProviderMetadata(
	ctx context.Context,
	orgIDs map[int64]any,
) (map[int64]orgservice.Provider, error) {
	providerMap := map[int64]orgservice.Provider{}

	// TODO: @jamesshen create batch call to orgsvc
	for orgID := range orgIDs {
		provider, err := s.orgServiceClient.GetProviderByLegacyId(ctx, orgID)
		if err != nil {
			logutils.CtxLogger(ctx).
				WithError(err).
				Error("cannot get provider by orgID", orgID)
			return map[int64]orgservice.Provider{}, err
		}
		providerMap[orgID] = provider
	}

	return providerMap, nil
}

func (s *ExamService) studiesToExamsRaw(
	studies []recordservice.PatientStudy,
) ([]coreapi.ExamRaw, error) {
	var exams []coreapi.ExamRaw
	for _, study := range studies {
		exam, err := study.ToExamRaw()
		if err != nil {
			return []coreapi.ExamRaw{}, err
		}
		exams = append(exams, exam)
	}
	return exams, nil
}

func (s *ExamService) getRecordServiceExamRaw(
	ctx context.Context,
	accountID string,
	UUID string,
	includeReports bool,
	includeInstances bool,
) (coreapi.ExamRaw, error) {
	exams, err := s.getRecordServiceExamsRawList(
		ctx,
		accountID,
		[]string{UUID},
		includeReports,
		includeInstances,
		false, // excludeIncompleteStudies=false for point query
	)
	if err != nil {
		return coreapi.ExamRaw{}, err
	}
	if len(exams) == 0 {
		return coreapi.ExamRaw{}, nil
	}
	return exams[0], nil
}

func (s *ExamService) getRecordServiceExamsRawList(
	ctx context.Context,
	accountID string,
	UUIDs []string,
	includeReports bool,
	includeInstances bool,
	excludeIncompleteStudies bool,
) ([]coreapi.ExamRaw, error) {
	studies, err := s.recordServiceClient.GetStudies(
		ctx,
		accountID,
		includeReports,   // includeReports
		includeInstances, // includeInstances
		nil,
		UUIDs,
	)
	if err != nil {
		return []coreapi.ExamRaw{}, err
	}

	var filteredStudies []recordservice.PatientStudy
	for _, study := range studies {
		// Filter by incomplete uploads
		if excludeIncompleteStudies && study.InstanceUploadProgressPercent != 100 {
			continue
		}
		filteredStudies = append(filteredStudies, study)
	}
	if len(filteredStudies) == 0 {
		return []coreapi.ExamRaw{}, nil
	}

	exams, err := s.studiesToExamsRaw(filteredStudies)
	if err != nil {
		return []coreapi.ExamRaw{}, err
	}

	// gather organizations to avoid redundant requests to orgsvc
	orgIDs := map[int64]any{}
	for _, study := range filteredStudies {
		if study.OrganizationID > 0 {
			orgIDs[study.OrganizationID] = nil
		}
	}
	providerMap, err := s.getProviderMetadata(
		ctx,
		orgIDs,
	)
	if err != nil {
		return []coreapi.ExamRaw{}, err
	}

	for i := range exams {
		if provider, ok := providerMap[exams[i].OrgId]; ok {
			exams[i].SetProviderMetadata(provider)
		}
	}

	// apply unlock status
	err = examutils.ApplyExamUnlockStatus(
		ctx,
		s.planServiceClient,
		s.accountServiceClient,
		accountID,
		exams,
	)
	if err != nil {
		return []coreapi.ExamRaw{}, err
	}

	return exams, nil
}

func (s *ExamService) getRecordServiceExamsList(
	ctx context.Context,
	accountID string,
	UUIDs []string,
	includeReports bool,
) ([]coreapi.Exam, error) {
	exams, err := s.getRecordServiceExamsRawList(
		ctx,
		accountID,
		UUIDs,
		includeReports, // includeReports
		false,          // includeInstances
		true,           // excludeIncompleteStudies
	)
	if err != nil {
		return []coreapi.Exam{}, err
	}

	return coreapi.RawToExams(ctx, exams), nil
}

func (e *ExamService) getQueryParamsFromContext(
	ctx context.Context,
	UUIDs []string,
) (bool, []string) {
	fields := logutils.Fields(ctx)

	// Default values
	activated := true

	// Extract and parse "activated" field
	if queryActivated, ok := fields["activated"].([]string); ok && len(queryActivated) > 0 {
		if value, err := strconv.ParseBool(queryActivated[0]); err == nil {
			activated = value
		}
	}

	// Extract "uid" field
	if queryUUIDs, ok := fields["uid"].([]string); ok {
		UUIDs = e.getMergedUUIDs(UUIDs, queryUUIDs)
	}

	return activated, UUIDs
}

func (e *ExamService) getExamsFilteredByActivated(
	activated bool,
	exams []coreapi.Exam,
) []coreapi.Exam {
	return lo.Filter(exams, func(e coreapi.Exam, index int) bool {
		return e.Activated == activated
	})
}

func (e *ExamService) getMergedUUIDs(UUIDsA []string, UUIDsB []string) []string {
	uniqueUUIDs := map[string]any{}

	for _, UUID := range append(UUIDsA, UUIDsB...) {
		uniqueUUIDs[UUID] = nil
	}

	mergedUUIDs := []string{}
	for UUID := range uniqueUUIDs {
		mergedUUIDs = append(mergedUUIDs, UUID)
	}

	return mergedUUIDs
}
