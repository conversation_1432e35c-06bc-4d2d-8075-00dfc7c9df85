package exams

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

type ExamServiceInterface interface {
	GetExam(ctx context.Context, accountID string, UUID string) (coreapi.ExamRaw, error)
	GetExamBasic(ctx context.Context, accountID string, UUID string) (coreapi.ExamRawBasic, error)
	GetLastNameFromExam(ctx context.Context, UUID string) (string, error)
	GetExamSummaryData(ctx context.Context, accountID string, UUIDs []string, q queries.Queries, needReports bool, excludeDeleted bool) ([]coreapi.ExamRaw, error)
	GetExamSummaryDataForRecordStreamingStudy(ctx context.Context, UUID string, providerID int64) (coreapi.ExamRaw, error)
	GetFirstImageID(ctx context.Context, accountID, UUID string) (string, error)
	GetUserExamsSize(ctx context.Context, accountID string) (int64, error)
	GetObjectIDsByExamUUIDs(ctx context.Context, accountID string, UUIDs []string, activated bool) ([]string, error)
	ExamsBelongToAccount(ctx context.Context, accountID string, UUIDs []string) (bool, error)
	GetExamUUIDListForShare(ctx context.Context, shareID string) ([]string, error)
	GetUnassociatedReports(ctx context.Context, accountID string, activated bool, excludeDeleted bool) ([]coreapi.Report, error)
	GetPatientNameAndDOB(ctx context.Context, accountID string) (string, string, error)
	GetExamPatientID(ctx context.Context, accountID, UUID string) (string, error)
	GetPatientDataAndReferringPhysicianByExamUUID(UUID string) (string, string, string, error)
	GetReferringPhysiciansByShareID(shareID string) ([]string, error)
	GetPatientBirthDateByPatientID(ctx context.Context, accountID string, patientID string) (string, error)
	GetExamInfoForSubdictionary(ctx context.Context, accountID string, UUID string) (string, string, string, error)
	GetExamsResponse(ctx context.Context, accountID string, UUIDs []string, q queries.Queries, includeReports bool, excludeDeleted bool) ([]coreapi.Exam, error)
	GetAllReportIDsForPatient(ctx context.Context, accountID string, patientID string) (map[string][]string, error)
}
