package exams

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
)

func TestGetRecordServiceExamsList(t *testing.T) {
	orgServiceMock := &orgservice.OrgServiceMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	planServiceMock := &planservice.PlanSvcMock{}

	t.Run("activated full availability exam with report", func(t *testing.T) {
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  // instanceUploadProgressPercent
		)

		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "account123", true, false, mock.Anything, []string{"uuid1"}).
			Return([]recordservice.PatientStudy{study}, nil)

		examService := ExamService{
			db:                   nil, // db
			orgServiceClient:     orgServiceMock,
			recordServiceClient:  recordServiceMock,
			planServiceClient:    planServiceMock,
			accountServiceClient: accountServiceMock,
		}

		exams, err := examService.getRecordServiceExamsList(
			context.Background(),
			"account123",
			[]string{"uuid1"},
			true, // includeReports
		)
		assert.NoError(t, err)

		for i := range exams {
			recordservice.NormalizeExam(exams[i])
		}
		assert.Equal(t, 1, len(exams))

		expectedExam := recordservice.GetExpectedExam(
			t,
			true, // hasReport
			true, // hasProviderMetadata
			true, // isActivated
		)
		recordservice.AssertSameExam(t, expectedExam, exams[0])
	})

	t.Run("activated limited availability exam with report", func(t *testing.T) {
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.LIMITED_AVAILABILITY,
			true, // hasReport
			100,  // instanceUploadProgressPercent
		)

		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "account123", true, false, mock.Anything, []string{"uuid1"}).
			Return([]recordservice.PatientStudy{study}, nil)

		examService := ExamService{
			db:                   nil, // db
			orgServiceClient:     orgServiceMock,
			recordServiceClient:  recordServiceMock,
			planServiceClient:    planServiceMock,
			accountServiceClient: accountServiceMock,
		}

		exams, err := examService.getRecordServiceExamsList(
			context.Background(),
			"account123",
			[]string{"uuid1"},
			true, // includeReports
		)
		assert.NoError(t, err)

		for i := range exams {
			recordservice.NormalizeExam(exams[i])
		}

		expectedExam := recordservice.GetExpectedExam(
			t,
			true, // hasReport
			true, // hasProviderMetadata
			true, // isActivated
		)
		expectedExam.UnlockStatus = models.EXAM_LIMITED_AVAILABILITY
		recordservice.AssertSameExam(t, expectedExam, exams[0])
	})

	t.Run("inactivated exam with report", func(t *testing.T) {
		study := recordservice.FormatPatientStudy(
			false, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  // instanceUploadProgressPercent
		)

		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "account123", true, false, mock.Anything, []string{"uuid1"}).
			Return([]recordservice.PatientStudy{study}, nil)

		examService := ExamService{
			db:                   nil, // db
			orgServiceClient:     orgServiceMock,
			recordServiceClient:  recordServiceMock,
			planServiceClient:    planServiceMock,
			accountServiceClient: accountServiceMock,
		}

		exams, err := examService.getRecordServiceExamsList(
			context.Background(),
			"account123",
			[]string{"uuid1"},
			true, // includeReports
		)
		assert.NoError(t, err)

		for i := range exams {
			recordservice.NormalizeExam(exams[i])
		}

		expectedExam := recordservice.GetExpectedExam(
			t,
			true,  // hasReport
			true,  // hasProviderMetadata
			false, // isActivated
		)
		recordservice.AssertSameExam(t, expectedExam, exams[0])
	})

	t.Run("incomplete exam with report", func(t *testing.T) {
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			50,   // instanceUploadProgressPercent
		)

		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "account123", true, false, mock.Anything, []string{"uuid1"}).
			Return([]recordservice.PatientStudy{study}, nil)

		examService := ExamService{
			db:                   nil, // db
			orgServiceClient:     orgServiceMock,
			recordServiceClient:  recordServiceMock,
			planServiceClient:    planServiceMock,
			accountServiceClient: accountServiceMock,
		}

		exams, err := examService.getRecordServiceExamsList(
			context.Background(),
			"account123",
			[]string{"uuid1"},
			true, // includeReports
		)
		assert.NoError(t, err)
		assert.Equal(t, 0, len(exams))
	})

	t.Run("activated full availability exam with report - partially uploaded", func(t *testing.T) {
		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			50,   // instanceUploadProgressPercent
		)

		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, "account123", true, false, mock.Anything, []string{"uuid1"}).
			Return([]recordservice.PatientStudy{study}, nil)

		examService := ExamService{
			db:                   nil, // db
			orgServiceClient:     orgServiceMock,
			recordServiceClient:  recordServiceMock,
			planServiceClient:    planServiceMock,
			accountServiceClient: accountServiceMock,
		}

		exams, err := examService.getRecordServiceExamsList(
			context.Background(),
			"account123",
			[]string{"uuid1"},
			true, // includeReports
		)
		assert.NoError(t, err)
		assert.Equal(t, 0, len(exams))
	})
}

func TestExamsBelongToAccount(t *testing.T) {
	orgServiceMock := &orgservice.OrgServiceMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	providerServiceMock := providersservice.NewProviderServiceMock()
	migrationHelperMock := &MockMigrationHelper{}

	t.Run("returns true if record service finds a match", func(t *testing.T) {
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			"test-accountID",
			[]string{"test-uuid-1"},
		).Return(true, nil)
		examService := NewExamService(
			nil, // db
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providerServiceMock,
			migrationHelperMock,
		)

		match, err := examService.ExamsBelongToAccount(
			context.Background(), "test-accountID", []string{"test-uuid-1"},
		)

		assert.NoError(t, err)
		assert.True(t, match)
	})

	t.Run("returns false if record service does not find a match", func(t *testing.T) {
		recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
		recordServiceMock.EXPECT().MatchPatientStudies(
			mock.Anything,
			"test-accountID",
			[]string{"test-uuid-1"},
		).Return(false, nil)
		examService := NewExamService(
			nil, // db
			orgServiceMock,
			recordServiceMock,
			planServiceMock,
			accountServiceMock,
			providerServiceMock,
			migrationHelperMock,
		)

		match, err := examService.ExamsBelongToAccount(
			context.Background(), "test-accountID", []string{"test-uuid-1"},
		)

		assert.NoError(t, err)
		assert.False(t, match)
	})
}
