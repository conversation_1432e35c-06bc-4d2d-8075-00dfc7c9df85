package exams

import (
	"context"
	"database/sql"

	"gitlab.com/pockethealth/coreapi/pkg/util/rollout"
)

const MIGRATION_FLAG_PREFIX string = "use_record_service_"

type MigrationFlag string

const (
	GetExam              MigrationFlag = "GetExam"
	ExamsBelongToAccount MigrationFlag = "ExamsBelongToAccount"
)

type MigrationHelperInterface interface {
	MigrateGetExamSummaryData(ctx context.Context) bool
	MigrateGetFirstImageID(ctx context.Context) bool
}

type MigrationHelper struct {
	db *sql.DB
}

func NewMigrationHelper(db *sql.DB) MigrationHelperInterface {
	return &MigrationHelper{
		db: db,
	}
}

func (mh *MigrationHelper) getMigrationFlag(flag string) string {
	return MIGRATION_FLAG_PREFIX + flag
}

func (mh *MigrationHelper) shouldMigrate(ctx context.Context, flag string) bool {
	return rollout.Rollout(ctx, mh.db, mh.getMigrationFlag(flag))
}

func (mh *MigrationHelper) MigrateGetExamSummaryData(ctx context.Context) bool {
	return mh.shouldMigrate(ctx, "GetExamSummaryData")
}

func (mh *MigrationHelper) MigrateGetFirstImageID(ctx context.Context) bool {
	return mh.shouldMigrate(ctx, "GetFirstImageID")
}
