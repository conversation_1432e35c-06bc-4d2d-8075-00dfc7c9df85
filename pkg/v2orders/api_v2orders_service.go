package v2orders

import (
	"context"
	"database/sql"
	"strings"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// V2OrdersApiService is a service that implents the logic for the V2OrdersApiServicer
type V2OrdersApiService struct {
	sqldb       *sql.DB
	acctsClient accountservice.AccountService
	ampliClient interfaces.AmplitudeEventClient
	plansClient planservice.PlanService
}

// NewV2OrdersApiService creates a default api service
func NewV2OrdersApiService(
	db *sql.DB,
	as accountservice.AccountService,
	ac interfaces.AmplitudeEventClient,
	pc planservice.PlanService,
) coreapi.V2OrdersApiServicer {
	return &V2OrdersApiService{
		sqldb:       db,
		acctsClient: as,
		ampliClient: ac,
		plansClient: pc,
	}
}

// PostOrder - Create a new order for an account
// General purpose - can be used for any plan that requires charging a card
// If it needs to be opened up later for FA codes or FacilityFunded, need to do validation on the code itself or org settings.
func (s *V2OrdersApiService) PostOrder(
	ctx context.Context,
	accountId string,
	orderRequest coreapi.OrderRequest,
	deviceId string,
) (accountservice.CreateOrderResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", accountId)
	// get account country
	regionId, country, err := s.acctsClient.GetAccountMainRegion(ctx, accountId)
	if err != nil {
		lg.WithError(err).Error("could not get account country")
		return accountservice.CreateOrderResponse{}, err
	}

	order := accountservice.NewOrder{
		PlanId:           orderRequest.PlanId,
		RegionId:         regionId,
		PaymentToken:     orderRequest.PaymentToken,
		Country:          country,
		ZipCode:          "A1A1A1",
		DisableAutoRenew: orderRequest.DisableAutoRenew,
		DiscountName:     orderRequest.DiscountName,
		Source:           orderRequest.Source,
	}
	resp, err := s.acctsClient.CreateOrder(ctx, accountId, order)

	if err == nil {
		s.trackNewSubscription(
			ctx,
			accountId,
			orderRequest.PlanId,
			orderRequest.Source,
			deviceId,
		)
	}

	return resp, err
}

// GetOrders - get orders for an account, filtering on active and recurring
func (s *V2OrdersApiService) GetOrders(
	ctx context.Context,
	accountId string,
	filters map[string]bool,
) ([]coreapi.Order, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", accountId)

	coreOrders := []coreapi.Order{}
	acctSvcOrders, err := s.acctsClient.GetOrders(ctx, accountId, filters)
	if err != nil {
		return coreOrders, err
	}

	for i := range acctSvcOrders {
		// convert acctsvc Order to what we expose to the client
		o := coreapi.Order{
			OrderId:   acctSvcOrders[i].OrderId,
			PlanId:    acctSvcOrders[i].PlanId,
			AutoRenew: *acctSvcOrders[i].IsAutoRenewing,
			CreatedAt: acctSvcOrders[i].CreatedAt,
			ExpiresAt: acctSvcOrders[i].ExpiresAt,
		}

		// fetch payment method info
		details, err := s.acctsClient.GetOrderById(ctx, accountId, o.OrderId)
		if err != nil {
			lg.WithError(err).Error("unable to get order details")
		}

		// details has card number fmt as `**** **** **** 1234`, so extract out the last 4 digits
		last4 := details.Card.MaskedCard[strings.LastIndex(details.Card.MaskedCard, " ")+1:]
		o.PaymentMethod = coreapi.PaymentCard{
			LastFour:    last4,
			ExpiryYear:  uint16(details.Card.CardExpiryYear), // #nosec G115 future Y2^16 problem
			ExpiryMonth: uint8(details.Card.CardExpiryMonth), // #nosec G115 12 << 255
			Brand:       details.Card.Type,
		}
		coreOrders = append(coreOrders, o)
	}
	return coreOrders, nil
}

// PutOrderPaymentDetails - Update payment details of order
func (s *V2OrdersApiService) PutOrderPaymentDetails(
	ctx context.Context,
	accountId string,
	orderId string,
	mainRegion uint16,
	paymentDetails models.PaymentDetails,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithField("acct_id", accountId).
		WithField("order_id", orderId).
		WithField("main_region", mainRegion)

	country := "CA"
	if mainRegion == 2 {
		country = "US" // TODO: This should not be hardcoded
	}

	request := accountservice.OrderPaymentDetailsRequest{
		PaymentToken: paymentDetails.Token,
		Country:      country,
		ZipCode:      "A1A1A1", // TODO: fill zipcode from FE
	}
	err := s.acctsClient.UpdateOrderPaymentDetails(ctx, accountId, orderId, request)
	if err != nil {
		lg.WithError(err).Error("error updateing order payment details")
		return err
	}

	return nil
}

// PutOrderPaymentDetails - Update payment details of order
func (s *V2OrdersApiService) PostOrderActionReason(
	ctx context.Context,
	accountId string,
	orderId string,
	oar accountservice.OrderActionReason,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithField("acct_id", accountId).
		WithField("order_id", orderId)

	if err := s.acctsClient.CreateOrderActionReason(ctx, accountId, orderId, oar); err != nil {
		lg.WithError(err).Error("error adding order reason")
		return err
	}

	return nil
}

func (s *V2OrdersApiService) trackNewSubscription(
	ctx context.Context,
	accountId string,
	planId uint64,
	source string,
	deviceId string,
) {
	// get the plan information
	plan, err := s.plansClient.GetPlanById(ctx, int32(planId)) // #nosec G115 not worrying about max_int32
	lg := logutils.DebugCtxLogger(ctx).
		WithField("plan_id", planId)
	if err != nil {
		lg.WithError(err).
			Error("unable to get plan information from plan id; skipping Amplitude tracking")
		return
	}

	if source == "rho" {
		s.trackRhoUpgrade(lg, accountId, deviceId, plan)
	}
}

// Track RHO-specific upgrade
func (s *V2OrdersApiService) trackRhoUpgrade(
	lg *logrus.Entry,
	accountId string,
	deviceId string,
	plan planservice.PlanV2,
) {
	planType := strings.ToLower(plan.DisplayName)
	term := getSubscriptionTerm(planType)
	if term == "" {
		lg.Warnf("skipping Amplitude tracking for plan type: %s", planType)
		return
	}

	s.ampliClient.Track(amplitude.Event{
		EventType: "subscription upgraded via rho",
		UserID:    accountId,
		DeviceID:  deviceId,
		EventProperties: map[string]interface{}{
			"plan_type":         planType,
			"$revenue":          int(plan.Amount) / 100,
			"subscription_term": term,
			// todo: add subscription renewal date and expiry
			// todo: tax
			// todo: total_purchase_amount
		},
	})
}

// Determine subscription term based on plan type
func getSubscriptionTerm(planType string) string {
	switch planType {
	case "unlimited":
		return "yearly"
	case "flex":
		return "monthly"
	default:
		return ""
	}
}
