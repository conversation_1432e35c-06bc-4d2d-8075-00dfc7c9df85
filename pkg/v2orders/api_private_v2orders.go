/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package v2orders

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateV2OrdersController binds http requests to an api service and writes the service results to the http response
type PrivateV2OrdersController struct {
	service         coreapi.V2OrdersApiServicer
	ampCookieHeader string
}

// NewPrivateV2OrdersController creates a default api controller
func NewPrivateV2OrdersController(
	s coreapi.V2OrdersApiServicer,
	ampCookieHeader string,
) coreapi.PrivateV2OrdersApiRouter {
	return &PrivateV2OrdersController{service: s}
}

// Routes returns all of the api route for the Orders controller
func (c *PrivateV2OrdersController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostOrder",
			Method:      strings.ToUpper("POST"),
			Pattern:     "",
			HandlerFunc: c.PostOrder,
		},
		{
			Name:        "GetOrders",
			Method:      strings.ToUpper("GET"),
			Pattern:     "",
			HandlerFunc: c.GetOrders,
		},
		{
			Name:        "PutOrderPaymentDetails",
			Method:      strings.ToUpper("PUT"),
			Pattern:     "/paymentDetails",
			HandlerFunc: c.PutOrderPaymentDetails,
		},
		{
			Name:        "PostOrderActionReason",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/{order_id}/reason",
			HandlerFunc: c.PostOrderActionReason,
		},
	}
}

func (c *PrivateV2OrdersController) GetPathPrefix() string {
	return "/v2/orders"
}

func (c *PrivateV2OrdersController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateV2OrdersController) PostOrder(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	var accountID string
	//get account id from token
	token := r.Header.Get("Authorization")
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)
	claims, err := auth.ParseAcctsToken(token)

	if claims.AcctID == "" {
		_, accountID, err = auth.DecodeChallengeUnlockToken(token)
	} else {
		accountID = claims.AcctID
	}

	if err != nil || accountID == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithField("account_id", accountID)
	decoder := json.NewDecoder(r.Body)
	var orderReq coreapi.OrderRequest
	err = decoder.Decode(&orderReq)
	if err != nil {
		lg.WithError(err).Error("error decoding request body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !orderReq.Valid() {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	orderResp, err := c.service.PostOrder(
		r.Context(),
		accountID,
		orderReq,
		deviceID,
	)
	if err != nil {
		if badReqErr, ok := err.(accountservice.ErrBadCreateOrderRequest); ok {
			lg.WithError(badReqErr).Error("bad order request")
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusBadRequest),
				http.StatusBadRequest,
			)
			return
		} else if payErr, ok := err.(accountservice.ErrPayment); ok {
			lg.WithError(payErr).Error("payment err")
			httperror.ErrorWithLog(
				w,
				r,
				payErr.ClientError(),
				http.StatusPaymentRequired,
			)
			return
		}

		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), orderResp, nil, w)
}

func (c *PrivateV2OrdersController) GetOrders(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	//get account id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.ParseAcctsToken(token)

	if err != nil || claims.AcctID == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithField("account_id", claims.AcctID)

	//for now, we only need to expose getting the active subscription. Can add these as
	//query params later if needed
	ordersResp, err := c.service.GetOrders(r.Context(), claims.AcctID, map[string]bool{
		"active":    true,
		"recurring": true,
	})
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), ordersResp, nil, w)

}

func (c *PrivateV2OrdersController) PutOrderPaymentDetails(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())
	// get account id and order id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.ParseAcctsToken(token)

	if err != nil || claims.OrderID == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	lg = lg.WithField("account_id", claims.AcctID).WithField("order_id", claims.OrderID)

	decoder := json.NewDecoder(r.Body)
	var paymentDetails models.PaymentDetails
	err = decoder.Decode(&paymentDetails)
	if err != nil {
		lg.WithError(err).Error("error decoding request body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if paymentDetails.Token == "" {
		lg.Error("empty payment token")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PutOrderPaymentDetails(
		r.Context(),
		claims.AcctID,
		claims.OrderID,
		claims.MainRegion,
		paymentDetails,
	)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	w.WriteHeader(http.StatusOK)
}

func (c *PrivateV2OrdersController) PostOrderActionReason(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	params := mux.Vars(r)
	// get account id and order id from token
	token := r.Header.Get("Authorization")
	claims, err := auth.ParseAcctsToken(token)

	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	orderId := params["order_id"]
	lg = lg.WithField("account_id", claims.AcctID).WithField("order_id", orderId)

	decoder := json.NewDecoder(r.Body)
	var oar accountservice.OrderActionReason
	err = decoder.Decode(&oar)
	if err != nil {
		lg.WithError(err).Error("error decoding request body")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !oar.Valid() {
		lg.Error("invalid order reason request")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PostOrderActionReason(
		r.Context(),
		claims.AcctID,
		orderId,
		oar,
	)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	w.WriteHeader(http.StatusOK)
}
