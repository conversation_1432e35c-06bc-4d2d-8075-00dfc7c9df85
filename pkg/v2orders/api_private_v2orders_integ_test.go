//go:build integration
// +build integration

package v2orders

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

const (
	localClaimIp = "127.0.0.1"
)

func TestPostOrder(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	testcases := []struct {
		name           string
		auth           bool
		body           coreapi.OrderRequest
		expectedStatus int
	}{
		{
			name:           "missing auth",
			auth:           false,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "invalid body",
			auth: true,
			body: coreapi.OrderRequest{
				PlanId:       0,
				PaymentToken: "test_token",
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "good request",
			auth: true,
			body: coreapi.OrderRequest{
				PlanId:       1,
				PaymentToken: "test_token",
			},
			expectedStatus: http.StatusOK,
		},
	}

	auth.SetAcctSvcPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErShKPNb7tq+gvBqS4k9ZQQVfi1K8NAN4
NMRMiZW8oKU1ZGb6cs9FArI1DCIGy25XePxQ766BFdQD79Unl//UJ42ZJf4Pw+A2
nU//FhsKfJ11rXDJevBqvjJBh97RriSi
-----END PUBLIC KEY-----
`))
	auth.SetAcctSvcJWTPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEysQM5kZGOKETROw2gB36V9dQYyqU
JRniiniTuy83t0v73acv/GQRMCmIOa0IxwedYUveijyOW8AB6k4B1+wIsA==
-----END PUBLIC KEY-----
`))

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			ctrl := NewPrivateV2OrdersController(&MockV2OrdersService{}, "")

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/orders"
			reqBody, err := json.Marshal(c.body)
			if err != nil {
				t.Fatal(err)
			}
			req, err := http.NewRequest("POST", path, bytes.NewBuffer(reqBody))
			if err != nil {
				t.Fatal(err)
			}
			if !c.auth {
				req.Header.Set("Authorization", "")
			} else {
				// call actual acctsvc to get real token
				// set up acctsvc
				qaAcctSvcUrl := cfg.AcctSvcUrl
				acctServiceUser := "apptest_rw"
				acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
				httpClient := httpclient.NewHTTPClient(&http.Client{}, nil)
				service := accountservice.NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

				login, err := service.AuthenticateAcctAndGetToken(context.TODO(), accountservice.PatientAccountEndpoint, "<EMAIL>", "test1234", localClaimIp)

				if err != nil {
					t.Fatalf("could not login: %v", err)
				}
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", login.Token))
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v, want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestGetOrders(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	testcases := []struct {
		name           string
		auth           bool
		expectedStatus int
	}{
		{
			name:           "missing auth",
			auth:           false,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "good request",
			auth:           true,
			expectedStatus: http.StatusOK,
		},
	}

	auth.SetAcctSvcPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErShKPNb7tq+gvBqS4k9ZQQVfi1K8NAN4
NMRMiZW8oKU1ZGb6cs9FArI1DCIGy25XePxQ766BFdQD79Unl//UJ42ZJf4Pw+A2
nU//FhsKfJ11rXDJevBqvjJBh97RriSi
-----END PUBLIC KEY-----
`))
	auth.SetAcctSvcJWTPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEysQM5kZGOKETROw2gB36V9dQYyqU
JRniiniTuy83t0v73acv/GQRMCmIOa0IxwedYUveijyOW8AB6k4B1+wIsA==
-----END PUBLIC KEY-----
`))

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			ctrl := NewPrivateV2OrdersController(&MockV2OrdersService{}, "")

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/orders"
			req, err := http.NewRequest("GET", path, nil)
			if err != nil {
				t.Fatal(err)
			}
			if !c.auth {
				req.Header.Set("Authorization", "")
			} else {
				// call actual acctsvc to get real token
				// set up acctsvc
				qaAcctSvcUrl := cfg.AcctSvcUrl
				acctServiceUser := "apptest_rw"
				acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
				httpClient := httpclient.NewHTTPClient(&http.Client{}, nil)
				service := accountservice.NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

				login, err := service.AuthenticateAcctAndGetToken(context.TODO(), accountservice.PatientAccountEndpoint, "<EMAIL>", "test1234", localClaimIp)

				if err != nil {
					t.Fatalf("could not login: %v", err)
				}
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", login.Token))
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v, want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}

func TestPutOrderPaymentDetails(t *testing.T) {

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	testcases := []struct {
		name           string
		auth           bool
		order          bool
		body           string
		expectedStatus int
	}{
		{
			name:           "missing auth",
			auth:           false,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "missing order id",
			auth:           true,
			order:          false,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "missing payment token",
			auth:           true,
			order:          true,
			body:           "",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "good request",
			auth:           true,
			order:          true,
			body:           `{"token": "test_token"}`,
			expectedStatus: http.StatusOK,
		},
	}

	auth.SetAcctSvcPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErShKPNb7tq+gvBqS4k9ZQQVfi1K8NAN4
NMRMiZW8oKU1ZGb6cs9FArI1DCIGy25XePxQ766BFdQD79Unl//UJ42ZJf4Pw+A2
nU//FhsKfJ11rXDJevBqvjJBh97RriSi
-----END PUBLIC KEY-----
`))
	auth.SetAcctSvcJWTPublicKey([]byte(`-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEysQM5kZGOKETROw2gB36V9dQYyqU
JRniiniTuy83t0v73acv/GQRMCmIOa0IxwedYUveijyOW8AB6k4B1+wIsA==
-----END PUBLIC KEY-----
`))

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			// setup router
			ctrl := NewPrivateV2OrdersController(&MockV2OrdersService{}, "")

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/orders/paymentDetails"
			req, err := http.NewRequest("PUT", path, bytes.NewBufferString(c.body))
			if err != nil {
				t.Fatal(err)
			}
			if !c.auth {
				req.Header.Set("Authorization", "")
			} else {
				// call actual acctsvc to get real token
				// set up acctsvc
				qaAcctSvcUrl := cfg.AcctSvcUrl
				acctServiceUser := "apptest_rw"
				acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
				httpClient := httpclient.NewHTTPClient(&http.Client{}, nil)
				service := accountservice.NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

				// login with different accounts to get different session orders
				var login accountservice.LoginSession
				if c.order {
					login, err = service.AuthenticateAcctAndGetToken(context.TODO(), accountservice.PatientAccountEndpoint, "<EMAIL>", "test1234", localClaimIp)
				} else {
					// test account that has no orders
					login, err = service.AuthenticateAcctAndGetToken(context.TODO(), accountservice.PatientAccountEndpoint, "<EMAIL>", "test1234!", localClaimIp)
				}

				if err != nil {
					t.Fatalf("could not login: %v", err)
				}
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", login.Token))
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v, want %v",
					status,
					c.expectedStatus,
				)
			}
		})
	}
}
