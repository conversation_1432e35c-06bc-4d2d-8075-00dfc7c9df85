package v2orders

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
)

type MockV2OrdersService struct{}

func (m *MockV2OrdersService) PostOrder(
	ctx context.Context,
	accountId string,
	orerRequest coreapi.OrderRequest,
	deviceId string,
) (accountservice.CreateOrderResponse, error) {
	return accountservice.CreateOrderResponse{}, nil
}

func (m *MockV2OrdersService) GetOrders(
	ctx context.Context,
	accountId string,
	filters map[string]bool,
) ([]coreapi.Order, error) {
	return []coreapi.Order{}, nil
}

func (m *MockV2OrdersService) PutOrderPaymentDetails(
	ctx context.Context,
	accountId string,
	orderId string,
	mainRegion uint16,
	paymentDetails models.PaymentDetails,
) error {
	return nil
}

func (m *MockV2OrdersService) PostOrderActionReason(
	ctx context.Context,
	accountId string,
	orderId string,
	oar accountservice.OrderActionReason,
) error {
	return nil
}
