//go:build integration
// +build integration

package v2shares

import (
	"context"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestGetShareExamsByShareId(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// since we aren't testing the cases for a HRS user, don't bother setting up a new user for this and/or mocking
	s := NewV2SharesApiService(
		db,
		hrs.HlthRecSvcUser{},
		&accountservice.AcctSvcMock{},
		&orgs.OrgServiceMock{},
		false,
	)

	// TODO: add more test cases! this might mean table tests might not make sense, but we should still try!
	cases := []struct {
		name        string
		setupFunc   func()
		cleanupFunc func()
		expectedRes bool
	}{
		{
			name: "successfully returns true for contains_deleted_exams if the exam is deleted",
			setupFunc: func() {
				db.Exec(
					"INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES ('TestGetShareExamsByShareId(): some-share-id', 'some-object-id', 1)",
				)
				db.Exec(
					"INSERT INTO shares ( account_id, share_id) VALUES ('abc123', 'TestGetShareExamsByShareId(): some-share-id')",
				)
			},
			cleanupFunc: func() {
				db.Exec(
					"DELETE FROM share_objects2 where share_id='TestGetShareExamsByShareId(): some-share-id'",
				)
				db.Exec(
					"DELETE FROM shares where share_id='TestGetShareExamsByShareId(): some-share-id'",
				)
			},
			expectedRes: true,
		},
		{
			name: "successfully returns false for contains_deleted_exams if the exam is not deleted",
			setupFunc: func() {
				db.Exec(
					"INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES ('TestGetShareExamsByShareId(): some-share-id', 'some-object-id', 0)",
				)
				db.Exec(
					"INSERT INTO shares (account_id, share_id) VALUES ('abc123', 'TestGetShareExamsByShareId(): some-share-id')",
				)
			},
			cleanupFunc: func() {
				db.Exec(
					"DELETE FROM share_objects2 where share_id='TestGetShareExamsByShareId(): some-share-id'",
				)
				db.Exec(
					"DELETE FROM shares where share_id='TestGetShareExamsByShareId(): some-share-id'",
				)
			},
			expectedRes: false,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			c.setupFunc()

			res, err := s.GetShareExamsByShareId(
				context.TODO(),
				"abc123",
				"TestGetShareExamsByShareId(): some-share-id",
			)
			if err != nil {
				t.Logf("got an error when expected none: %s", err)
			}
			if res["contains_deleted_exams"] != c.expectedRes {
				t.Fatalf(
					"error getting GetShareExamsByShareId(): got %v but wanted %v",
					res["contains_deleted_exams"],
					c.expectedRes,
				)
			}

			c.cleanupFunc()
		})
	}
}
