package v2shares

import (
	"context"
	"database/sql"
	"errors"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	sqlPhysicians "gitlab.com/pockethealth/coreapi/pkg/mysql/physicians"
	sqlShares "gitlab.com/pockethealth/coreapi/pkg/mysql/shares"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// SharesApiService is a service that implents the logic for the SharesApiServicer
// This service should implement the business logic for every endpoint for the SharesApi API.
// Include any external packages or services that will be required by this service.
type V2SharesApiService struct {
	sqldb                *sql.DB
	hlthRecSvcUser       hrs.HlthRecSvcUser
	acctSvcClient        accountservice.AccountService
	orgSvcClient         orgsvc.OrgService
	excludeDeletedShares bool
}

// NewSharesApiService creates a default api service
func NewV2SharesApiService(
	db *sql.DB,
	hrServiceUser hrs.HlthRecSvcUser,
	acctSvcClient accountservice.AccountService,
	orgsvc orgsvc.OrgService,
	excludeDeletedShares bool,
) coreapi.V2SharesApiServicer {
	return &V2SharesApiService{
		sqldb:                db,
		hlthRecSvcUser:       hrServiceUser,
		acctSvcClient:        acctSvcClient,
		orgSvcClient:         orgsvc,
		excludeDeletedShares: excludeDeletedShares,
	}
}

// GetShareExamsByshareId - Get exams within a share
func (s *V2SharesApiService) GetShareExamsByShareId(
	ctx context.Context,
	acctId string,
	shareId string,
) (map[string]interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("share_id", shareId)

	// account doesn't share or account is not authorized to view
	if !sqlShares.BelongsToAcct(ctx, s.sqldb, acctId, shareId) {
		canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.sqldb, acctId, shareId)
		if err != nil || !canView {
			return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		// physician has access to the share
		lg = lg.WithField("account_type", coreapi.PhysicianAccount)
	}

	examIdList, err := sqlExams.GetExamUUIDListForShare(ctx, s.sqldb, shareId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve share exam ids")
		return nil, err
	}

	cde, err := sqlShares.ContainsDeletedExams(ctx, s.sqldb, shareId)
	if err != nil {
		lg.WithError(err).Error("could not check whether share contains a deleted exam or not")
		// don't error out; this will mean the client will have `false` returned
	}

	hrIdList, err := sqlShares.GetHRIDList(ctx, s.sqldb, shareId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve share health record ids")
		return nil, err
	}

	var exams []coreapi.Exam
	if len(examIdList) > 0 {
		q, err := queries.NewWhere(map[string][]any{"e.activated": {true}})
		if err != nil {
			return nil, err
		}
		examsRaw, err := sqlExams.GetExamSummaryData(
			ctx,
			s.sqldb,
			acctId,
			examIdList,
			q,
			false,
			s.excludeDeletedShares,
			s.orgSvcClient,
		)
		if err != nil || len(examsRaw) <= 0 {
			lg.WithError(err).Error("could not retrieve share exams")
			return nil, err
		}
		exams = coreapi.RawToExams(ctx, examsRaw)
	}

	var records []coreapi.Record
	var hrPatient coreapi.HRPatient
	if len(hrIdList) > 0 {
		records, err = s.hlthRecSvcUser.GetMetadataByIds(ctx, hrIdList)
		if err != nil {
			lg.WithError(err).Error("could not retrieve share health records")
			return nil, err
		}

		pt, err := s.acctSvcClient.GetPatient(ctx, acctId, records[0].PatientId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithField("patient_id", records[0].PatientId).
				WithError(err).
				Error("could not retrieve patient info")

			//don't fail, just return empty pt.
		} else {
			hrPatient = coreapi.HRPatient{FirstAndMiddleNames: pt.FirstName, LastName: pt.LastName, PhPatientId: pt.PatientId, AccountId: pt.AccountId}
		}
	}

	return map[string]interface{}{
		"exams":                  exams,
		"hrs":                    records,
		"hrPatient":              hrPatient,
		"contains_deleted_exams": cde,
	}, nil
}
