package v2shares

import (
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

// A PrivateV2SharesApiController binds http requests to an api service and writes the service results to the http response
type PrivateV2SharesApiController struct {
	service coreapi.V2SharesApiServicer
}

// NewV2SharesApiController creates a default api controller
func NewPrivateV2SharesApiController(
	s coreapi.V2SharesApiServicer,
) coreapi.PrivateV2SharesApiRouter {
	return &PrivateV2SharesApiController{service: s}
}

// Routes returns all of the api route for the V2SharesApiController
func (c *PrivateV2SharesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetShareExamsByshareId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{shareId}/exams",
			HandlerFunc: c.GetShareExamsByshareId,
		},
	}
}

func (c *PrivateV2SharesApiController) GetPathPrefix() string {
	return "/v2/shares"
}

func (c *PrivateV2SharesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

// GetShareExamsByshareId - Get exams within a share
func (c *PrivateV2SharesApiController) GetShareExamsByshareId(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	shareId := params["shareId"]
	tokenHeader := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	result, err := c.service.GetShareExamsByShareId(r.Context(), acctId, shareId)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
