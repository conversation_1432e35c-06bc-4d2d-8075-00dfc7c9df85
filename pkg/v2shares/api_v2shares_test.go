package v2shares

import (
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// when you test locally this ends up being what clientAddr[0] looks like
const (
	localClaimIp = ""
)

func TestGetShareExamsById(t *testing.T) {
	//mock get exams service function
	mockGetShares := func(acctId string, shareId string) (map[string]interface{}, error) {
		return make(map[string]interface{}), nil
	}

	service := NewMockV2SharesApiService(mockGetShares)

	controller := NewPrivateV2SharesApiController(service)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	t.Run("test proper response", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v2/shares/thisisashareid/exams", nil)
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		//should return a body
		body, _ := ioutil.ReadAll(rr.Result().Body)
		if string(body) == "" {
			t.Errorf("no body returned")
		}

	})
}
