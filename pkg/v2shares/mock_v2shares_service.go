package v2shares

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// V2SharesApiService is a service that implents the logic for the V2SharesApiServicer
// This service should implement the business logic for every endpoint for the V2SharesApi API.
// Include any external packages or services that will be required by this service.
type MockV2SharesApiService struct {
	MockGetShareExams func(acctId string, shareId string) (map[string]interface{}, error)
}

// NewSharesApiService creates a default api service
func NewMockV2SharesApiService(
	mockGetExams func(string, string) (map[string]interface{}, error),
) coreapi.V2SharesApiServicer {
	return &MockV2SharesApiService{MockGetShareExams: mockGetExams}
}

// GetShareExamsByshareId - Get exams within a share
func (s *MockV2SharesApiService) GetShareExamsByShareId(
	ctx context.Context,
	acctId string,
	shareId string,
) (map[string]interface{}, error) {
	return s.MockGetShareExams(acctId, shareId)
}
