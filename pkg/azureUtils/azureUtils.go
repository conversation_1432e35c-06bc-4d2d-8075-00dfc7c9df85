package azureUtils

import (
	"context"
	"encoding/json"
	"io"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A ContainerClient consists of individual container clients
type ContainerClient struct {
	Objects       *container.Client // client for `prod` container
	ShareMetadata *container.Client // client for `sharemetadata` container
	Consents      *container.Client // client for `consents` container
	Requests      *container.Client // client for `requests` container
	ReportInsight *container.Client // client for `reportinsightsdata` container
	Pprof         *container.Client // client for storing pprof dumps
}

// delete a blob and all of its snapshots
func DeleteBlob(
	ctx context.Context, container *container.Client,
	blobId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("blob_id", blobId)
	blobClient := container.NewBlobClient(blobId)
	deleteSnapshotOption := blob.DeleteSnapshotsOptionTypeInclude
	_, err := blobClient.Delete(
		ctx,
		&blob.DeleteOptions{
			DeleteSnapshots: &deleteSnapshotOption,
		},
	)
	if err != nil {
		lg.WithError(err).Error("delete blob failed")
		return err
	}
	return nil
}

// only return reader, some cases we don't want to read the bytes into memory
func DownloadBlobReader(
	ctx context.Context, container *container.Client,
	blobId string,
) (io.ReadCloser, int64) {
	lg := logutils.DebugCtxLogger(ctx).WithField("blob_id", blobId)

	blobClient := container.NewBlobClient(blobId)
	br, err := blobClient.DownloadStream(
		ctx,
		nil,
	)
	if err != nil {
		lg.WithError(err).Error("get blob failed")
		return nil, 0 //not found or db problem
	}

	size := int64(-1)
	if br.ContentLength == nil {
		lg.Warn("downloaded blob size is unknown")
	} else {
		size = *br.ContentLength
	}

	return br.Body, size
}

// returns true if the blob reader can be accessed
// a blob can be inaccessible if it doesn't exist or if there's an azure db problem
func CanAccessBlob(
	ctx context.Context, container *container.Client,
	blobId string,
) bool {

	blobClient := container.NewBlobClient(blobId)
	_, err := blobClient.DownloadStream(
		ctx,
		nil,
	)
	return err == nil
}

func UploadBlobBytes(
	ctx context.Context,
	container *container.Client,
	blobId string,
	uploadBytes []byte,
) error {
	blobClient := container.NewBlockBlobClient(blobId)
	_, err := blobClient.UploadBuffer(
		ctx,
		uploadBytes,
		nil,
	)
	return err
}

func UploadBlobStream(
	ctx context.Context,
	container *container.Client,
	blobId string,
	body io.Reader,
) error {
	blobClient := container.NewBlockBlobClient(blobId)
	_, err := blobClient.UploadStream(
		ctx,
		body,
		nil,
	)
	return err
}

// send/push message
func SendEvent(ctx context.Context, sender pubsub.TopicSender, event pubsub.Event) error {
	// get correlation id
	corId := ""
	if ctx.Value(logutils.CorrelationIdContextKey) != nil {
		corId = ctx.Value(logutils.CorrelationIdContextKey).(string)
	}
	// marshal message body body
	jsonEvt, err := json.Marshal(event)
	if err != nil {
		return err
	}
	// send message to queue or topic
	err = sender.SendMessage(
		ctx,
		&azservicebus.Message{
			CorrelationID:         &corId,
			Body:                  jsonEvt,
			ApplicationProperties: event.ApplicationProperties(),
		},
		nil,
	)
	if err != nil {
		return err
	}
	return nil
}
