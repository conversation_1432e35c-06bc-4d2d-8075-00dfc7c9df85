package azureUtils

import (
	"context"
	"encoding/json"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/servicebus"
)

type AZServiceBusQueue struct {
	busConnectionString string
	regionclient        *azservicebus.Client
	globalclient        *azservicebus.Client
	serviceBusName      string
}

func NewServiceBusQueue(connectionString, serviceBusName string) pubsub.Queue {
	return &AZServiceBusQueue{busConnectionString: connectionString, serviceBusName: serviceBusName}
}

// GetClient creates a new azure service client
func (t *AZServiceBusQueue) GetGlobalClient() (*azservicebus.Client, error) {
	if t.globalclient != nil {
		return t.globalclient, nil
	}
	// Get namespace for client with Service Bus Namespace and connection string.
	result, err := azservicebus.NewClientFromConnectionString(t.busConnectionString, nil)
	if err != nil {
		return nil, err
	}

	t.globalclient = result
	return t.globalclient, nil
}

func (t *AZServiceBusQueue) GetRegionClient() (*azservicebus.Client, error) {
	if t.regionclient != nil {
		return t.regionclient, nil
	}
	// Get namespace for client with Service Bus Namespace and identity.
	result, err := servicebus.GetServiceBusClient(t.serviceBusName, nil)
	if err != nil {
		return nil, err
	}

	t.regionclient = result
	return t.regionclient, nil
}

func (t *AZServiceBusQueue) Publish(
	ctx context.Context,
	message pubsub.Message,
	queue string,
	client *azservicebus.Client,
) {
	ctxBackground := bgctx.GetBGCtxWithCorrelation(ctx)
	defer t.recoverHelper(ctxBackground)
	lg := logutils.DebugCtxLogger(ctxBackground).WithField("queue", queue)

	if client == nil {
		lg.Error("service bus client is not valid for processing queue messages")
		return
	}

	request, err := json.Marshal(message)
	if err != nil {
		lg.WithError(err).Error("could not marshal message params")
		return
	}

	sender, err := client.NewSender(queue, nil)
	if err != nil {
		lg.WithError(err).Info("error creating service bus sender for event publish")
		return
	}
	defer sender.Close(context.Background())

	corId := ""
	if ctx.Value(logutils.CorrelationIdContextKey) != nil {
		corId = ctx.Value(logutils.CorrelationIdContextKey).(string)
	}
	//send/push message
	err = sender.SendMessage(context.Background(), &azservicebus.Message{CorrelationID: &corId, Body: request}, nil)
	if err != nil {
		lg.WithError(err).Info("error pubishing message to queue")
		return
	}

}

func (t *AZServiceBusQueue) PublishSession(
	message pubsub.Message,
	queue string,
	sessionId string,
	client *azservicebus.Client,
) {
	ctx := context.Background()
	defer t.recoverHelper(ctx)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"queue":      queue,
		"session_id": sessionId,
	})

	if client == nil {
		lg.Error("service bus client is not valid for processing queue messages")
		return
	}

	request, err := json.Marshal(message)
	if err != nil {
		lg.WithError(err).Error("could not marshal message params")
		return
	}

	sender, err := client.NewSender(queue, nil)
	if err != nil {
		lg.WithError(err).Info("error creating service bus sender for publish session event")
		return
	}
	defer sender.Close(ctx)

	corId := ""
	if ctx.Value(logutils.CorrelationIdContextKey) != nil {
		corId = ctx.Value(logutils.CorrelationIdContextKey).(string)
	}
	//send/push message
	err = sender.SendMessage(
		ctx,
		&azservicebus.Message{CorrelationID: &corId, Body: request, SessionID: &sessionId},
		nil,
	)
	if err != nil {
		lg.WithError(err).Info("error pubishing message to queue")
		return
	}
}

func (t *AZServiceBusQueue) recoverHelper(ctx context.Context) {
	lg := logutils.CtxLogger(ctx)
	if r := recover(); r != nil {
		lg.Errorf("Caught panic in service bus queue event sender: %v", r)
	}
}
