package azureUtils

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/servicebus"
)

type AZTopic struct {
	busConnectionString string
	serviceBusName      string
	client              *azservicebus.Client
	topicStr            string
	wg                  *sync.WaitGroup
}

func NewAZTopicWithConnectionString(connectionString string, waitGroup *sync.WaitGroup, topic string) pubsub.Topic {
	return &AZTopic{busConnectionString: connectionString, wg: waitGroup, topicStr: topic}
}

func NewAZTopic(serviceBusName string, waitGroup *sync.WaitGroup, topic string) pubsub.Topic {
	return &AZTopic{serviceBusName: serviceBusName, wg: waitGroup, topicStr: topic}
}

func (t *AZTopic) Publish(
	ctx context.Context,
	event pubsub.Event,
) {
	t.wg.Add(1)
	ctxBackground := bgctx.GetBGCtxWithCorrelation(ctx)
	defer t.recoverHelper(ctxBackground)
	go func() {
		defer t.wg.Done()
		start := time.Now()
		lg := logutils.DebugCtxLogger(ctxBackground).WithField("topic", t.topicStr)

		err := t.sendEvent(ctxBackground, event)
		if err != nil {
			lg.WithError(err).Error("error publishing event")
		}
		lg.WithField("time_taken_ms", time.Since(start).Milliseconds()).Info("topic publish time")
	}()
}

// GetClient creates a new azure service client
func (t *AZTopic) getClient(ctx context.Context) *azservicebus.Client {
	if t.client != nil {
		return t.client
	}

	lg := logutils.CtxLogger(ctx)

	var result *azservicebus.Client
	var err error
	if t.serviceBusName != "" {
		result, err = servicebus.GetServiceBusClient(t.serviceBusName, nil)
	} else if t.busConnectionString != "" {
		// Get namespace for client with Service Bus Namespace and connection string.
		result, err = azservicebus.NewClientFromConnectionString(t.busConnectionString, nil)
	}

	if err != nil {
		lg.WithError(err).Error("error creating service bus client")
		return nil
	}
	t.client = result
	return t.client
}

func (t *AZTopic) sendEvent(ctx context.Context, event pubsub.Event) error {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	lg := logutils.DebugCtxLogger(ctx).WithField("topic", t.topicStr)

	//validate
	if event.IsDefault() {
		lg.Error("validation error: event cannot be empty")
		return errors.New("event cannot be an empty ")
	}

	// Create a client to communicate with the service bus
	if client := t.getClient(ctx); client != nil {
		sender, err := client.NewSender(t.topicStr, nil)
		if err != nil {
			lg.WithError(err).
				Error("error creating service bus sender for event publish")
			return err
		}

		defer sender.Close(ctx)

		err = SendEvent(ctx, sender, event)
		if err != nil {
			lg.WithError(err).Error("error publishing event to topic")
			return err
		}
	}
	return nil
}

func (t *AZTopic) recoverHelper(ctx context.Context) {
	lg := logutils.CtxLogger(ctx)
	if r := recover(); r != nil {
		lg.Errorf("Caught panic in Topic event sender: %v", r)
	}
}
