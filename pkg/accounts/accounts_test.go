package accounts

import (
	"context"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"

	"github.com/DATA-DOG/go-sqlmock"
	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetOrCreateAcctIds(t *testing.T) {

	regions.SetRegionID(1)
	t.Run("acct and pt exists", func(t *testing.T) {
		acctId := "abc"
		email := "<EMAIL>"
		expectedPtId := "xyz"
		as := &accountservice.AcctSvcMock{
			GetAccountInfoReturn: accountservice.Account{
				AccountId:  acctId,
				Email:      email,
				MainRegion: 1,
			},
			GetOrCreatePatientReturn: expectedPtId,
		}
		//if acct/pt exists, expect 0 db calls
		db, _, _ := sqlmock.New()
		acct, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			acctId,
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1994"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)
		assert.Equal(t, acctId, acct.AccountId)
		assert.Equal(t, expectedPtId, ptId)
	})

	t.Run("acct exists, with region, but not pt", func(t *testing.T) {

		acctId := "abc"
		email := "<EMAIL>"
		pt1 := "xyz"
		as := &accountservice.AcctSvcMock{
			GetPatientsReturn: []accountservice.Patient{
				{
					PatientId: pt1,
					FirstName: "Grace",
					LastName:  "Cochrane",
					DOB:       "1994-03-20",
				},
			},
			GetAccountInfoReturn: accountservice.Account{
				AccountId:  acctId,
				Email:      email,
				MainRegion: 1,
			},
		}
		//if acct/pt exists, expect 0 db calls
		db, _, _ := sqlmock.New()
		acct, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			acctId,
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1996"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)

		//expect new ksuid for new pt created
		_, err = ksuid.Parse(ptId)
		assert.NoError(t, err)
		assert.Equal(t, acctId, acct.AccountId)
	})

	t.Run("acct and pt exists but new region", func(t *testing.T) {
		acctId := "abc"
		email := "<EMAIL>"
		expectedPtId := "xyz"
		as := &accountservice.AcctSvcMock{
			GetAccountInfoReturn: accountservice.Account{
				AccountId:  acctId,
				Email:      email,
				MainRegion: 2,
			},
			GetOrCreatePatientReturn: expectedPtId,
		}

		db, mock, _ := sqlmock.New()
		//expect db write for multi-region notif
		mock.ExpectExec(`INSERT INTO user_notifications`).
			WithArgs(sqlmock.AnyArg(), acctId, coreapi.NotificationMultiRegion, 0, nil, []byte{}).
			WillReturnResult(sqlmock.NewResult(4, 1))
		acct, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			acctId,
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1994"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)
		require.NoError(t, mock.ExpectationsWereMet(), "db expectations not met")

		assert.Equal(t, expectedPtId, ptId)
		assert.Equal(t, acctId, acct.AccountId)
	})

	t.Run(
		"acct and pt exists with two regions, but this isn't their main region",
		func(t *testing.T) {
			acctId := "abc"
			email := "<EMAIL>"
			expectedPtId := "xyz"
			as := &accountservice.AcctSvcMock{
				GetAccountInfoReturn: accountservice.Account{
					AccountId:         acctId,
					Email:             email,
					MainRegion:        2,
					AdditionalRegions: map[uint16]uint64{1: 0},
				},
				GetOrCreatePatientReturn: expectedPtId,
			}
			db, _, _ := sqlmock.New()
			acct, ptId, _, err := GetOrCreateAccountIds(
				context.TODO(),
				as,
				db,
				acctId,
				email,
				coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1994"},
				"",
				"",
				"",
				"",
			)
			require.NoError(t, err)

			assert.Equal(t, acctId, acct.AccountId)
			assert.Equal(t, expectedPtId, ptId)
		},
	)

	t.Run("acct exists, but needs new pt and region", func(t *testing.T) {
		acctId := "abc"
		email := "<EMAIL>"
		as := &accountservice.AcctSvcMock{
			GetAccountInfoReturn: accountservice.Account{
				AccountId:  acctId,
				Email:      email,
				MainRegion: 2,
			},
		}

		db, mock, _ := sqlmock.New()
		//expect db write for multi-region notif
		mock.ExpectExec(`INSERT INTO user_notifications`).
			WithArgs(sqlmock.AnyArg(), acctId, coreapi.NotificationMultiRegion, 0, nil, []byte{}).
			WillReturnResult(sqlmock.NewResult(4, 1))
		acct, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			acctId,
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1996"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)
		require.NoError(t, mock.ExpectationsWereMet(), "db expectations not met")

		assert.NotEmpty(t, ptId)
		//expect new ksuid for new pt created
		_, err = ksuid.Parse(ptId)
		assert.NoError(t, err)

		assert.Equal(t, acctId, acct.AccountId)
	})

	t.Run("acct/pt exists, but id not passed in", func(t *testing.T) {
		acctId := "abc"
		email := "<EMAIL>"
		expectedPtId := "xyz"
		testacct := accountservice.Account{
			AccountId:  acctId,
			Email:      email,
			MainRegion: 1,
		}
		as := &accountservice.AcctSvcMock{
			GetAccountInfoByEmailReturn: &testacct,
			GetOrCreatePatientReturn:    expectedPtId,
		}

		db, _, _ := sqlmock.New()
		acct, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			"",
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1994"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)
		assert.Equal(t, acctId, acct.AccountId)
		assert.Equal(t, expectedPtId, ptId)
	})

	t.Run("acct dne", func(t *testing.T) {
		email := "<EMAIL>"
		as := &accountservice.AcctSvcMock{
			GetAccountInfoByEmailReturn: nil,
		}

		db, mock, _ := sqlmock.New()
		//expect to do a query for language on the org
		mock.ExpectQuery("SELECT default_language FROM organizations WHERE id=").
			WithArgs(2).
			WillReturnRows(sqlmock.NewRows([]string{"en"}))
		_, ptId, _, err := GetOrCreateAccountIds(
			context.TODO(),
			as,
			db,
			"",
			email,
			coreapi.UserProfile{FirstName: "Grace", LastName: "Cochrane", DOB: "03/20/1994"},
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)

		//expect new ksuid for new pt created
		assert.NotEmpty(t, ptId)
		_, err = ksuid.Parse(ptId)
		assert.NoError(t, err)

		assert.True(t, as.CreateAccountCalled, "expected create account to have been called")
	})

	t.Run("acct and patient id provided", func(t *testing.T) {
		acctId := "abc"
		email := "<EMAIL>"
		expectedPtId := "xyz"
		testacct := accountservice.Account{
			AccountId:  acctId,
			Email:      email,
			MainRegion: 1,
		}
		as := &accountservice.AcctSvcMock{
			GetPatientsReturn: []accountservice.Patient{
				{
					PatientId: "definitely messed up if you get this",
					FirstName: "Grace",
					LastName:  "Cochrane",
					DOB:       "1994-03-20",
				},
			},
			GetAccountInfoByEmailReturn: &testacct,
			GetOrCreatePatientReturn:    expectedPtId,
		}

		db, mock, _ := sqlmock.New()
		//expect to do a query for language on the org
		mock.ExpectQuery("SELECT default_language FROM organizations WHERE id=").
			WithArgs(2).
			WillReturnRows(sqlmock.NewRows([]string{"en"}))
		profile := coreapi.UserProfile{
			FirstName: "Grace",
			LastName:  "Cochrane",
			DOB:       "03/20/1994",
			PatientId: expectedPtId,
		}
		_, ptId, _, err := GetOrCreateAccountIds(
			context.Background(),
			as,
			db,
			"",
			email,
			profile,
			"",
			"",
			"",
			"",
		)
		require.NoError(t, err)

		//expect new ksuid for new pt created
		assert.Equal(
			t,
			expectedPtId,
			ptId,
			"with patient ID provided should not have called acctsvc",
		)
	})

}
