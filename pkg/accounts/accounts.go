package accounts

import (
	"context"
	"crypto/sha1" // #nosec G505
	"database/sql"
	"fmt"
	"time"

	"golang.org/x/text/language"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	notifs "gitlab.com/pockethealth/coreapi/pkg/mysql/notifications"
	phi "gitlab.com/pockethealth/coreapi/pkg/mysql/phi_profiles"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/phiHelpers"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func GetOrCreateAccountIds(
	ctx context.Context,
	acctsClient accountservice.AccountService,
	db *sql.DB,
	acctId string,
	email string,
	prof coreapi.UserProfile,
	language string,
	deviceId string,
	acctCreationSource string,
	ptCreationSource string,
) (account *accountservice.Account, patientId string, isNewAccount bool, funcerr error) {
	var acct *accountservice.Account
	var err error
	newAccount := false

	if acctId != "" {
		acct, err = acctsClient.GetAccountInfo(ctx, acctId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithError(err).
				Error("acctsvc unable to get account info")
			return nil, "", newAccount, err
		}
	} else {
		// check if account already exists
		acct, err = acctsClient.GetAccountInfoByEmail(ctx, email)
		if err != nil {
			// `GetAccountInfoByEmail` does two lookups, see if any of them returned something other than 'Not Found'
			_, noAcctOk := err.(accountservice.ErrNoAccountForEmail)
			_, notFoundOk := err.(accountservice.ErrAccountNotFound)
			if !noAcctOk && !notFoundOk {
				// some other error occurred, return
				logutils.DebugCtxLogger(ctx).WithError(err).Error("acctsvc unable to get account info by email")
				return nil, "", newAccount, err
			}
		}
	}

	if acct != nil {
		// acct already exists
		lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acct.AccountId)
		//check existing patients and add new if missing match
		//check if we need to add this region to the account
		region := regions.GetRegionID()
		lg = lg.WithField("region_id", region)
		lg.Debug("account exists")
		// get/create patient
		tDOB, err := time.Parse("01/02/2006", prof.DOB)
		if err != nil {
			lg.WithField("dob", prof.DOB).Warn("invalid dob for pt")
		}
		lg.Info("get/create pt")
		ptId := prof.PatientId
		if ptId == "" {
			ptId, err = acctsClient.GetOrCreatePatient(
				ctx,
				acct.AccountId,
				accountservice.Patient{
					Email:       email,
					FirstName:   prof.FirstName,
					LastName:    prof.LastName,
					AltLastName: prof.AltLastName,
					DOB:         tDOB.Format("2006-01-02"),
					Phone:       prof.Phone,
					Sex:         prof.Sex,
					Source:      ptCreationSource,
				},
				false,
			)
			if err != nil {
				lg.WithError(err).Error("could not create pt with given PII")
			}
		}
		_, ok := acct.AdditionalRegions[region]
		if acct.MainRegion == region || ok {
			lg.WithField("is_main_region", !ok).Debug("acct has this region already")
			return acct, ptId, newAccount, nil
		}

		// else we need to add region to this account
		lg.Debug("account exists, but doesn't have this region")

		err = addRegion(ctx, db, acctsClient, acct.AccountId)
		if err != nil {
			lg.WithError(err).Error("unable to add region to acct")
			return nil, "", newAccount, err
		}
		lg.Info("added region to acct")

		return acct, ptId, newAccount, nil
	}
	// if we get here, acct doesn't exist, so we need to create a new account
	newAccount = true
	prof.IsAccountOwner = true
	lg := logutils.DebugCtxLogger(ctx).WithField("is_owner", prof.IsAccountOwner)
	acctId, ptId, err := RegisterNewAccount(
		ctx,
		db,
		acctsClient,
		lg,
		email,
		"",
		prof,
		language,
		deviceId,
		acctCreationSource,
		ptCreationSource,
	)
	if err != nil {
		newAccount = false
		return nil, "", newAccount, err
	}

	acct, err = acctsClient.GetAccountInfo(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("unable to get account in acct svc")
		return nil, "", newAccount, err
	}
	return acct, ptId, newAccount, nil
}

// registerNewAccount creates an account and pt in acctsvc, and a phi profile regionally
func RegisterNewAccount(
	ctx context.Context,
	db *sql.DB,
	acctServiceClient accountservice.AccountService,
	lg *logrus.Entry,
	email string,
	password string,
	profile coreapi.UserProfile,
	language string,
	deviceId string,
	acctCreationSource string,
	ptCreationSource string,
) (string, string, error) {
	emailHash := sha1.Sum([]byte(email)) // #nosec G401
	lg = lg.WithField("email_sha1", fmt.Sprintf("%x", emailHash))

	newAccountRequest := accountservice.NewAccount{
		Email:                 email,
		Password:              password,
		DeviceId:              deviceId,
		AccountCreationSource: acctCreationSource,
	}

	if language != "" {
		newAccountRequest.Settings = accountservice.UserSettingsRequest{
			Language: language,
		}
	}

	// create account in acct svc
	acctId, err := acctServiceClient.CreateAccountAndSendVerifyEmail(ctx, newAccountRequest)
	if err != nil {
		lg.WithError(err).Error("unable to create account in acct svc")
		return "", "", err
	}
	lg.WithField("acct_id", acctId).Info("created account")

	tDOB, err := time.Parse("01/02/2006", profile.DOB)
	if err != nil {
		lg.Warn("pt will be created with bad dob")
	}
	rb := accountservice.Patient{
		FirstName:      profile.FirstName,
		LastName:       profile.LastName,
		DOB:            tDOB.Format("2006-01-02"),
		Phone:          profile.Phone,
		Email:          email,
		IsAccountOwner: &profile.IsAccountOwner,
		Source:         ptCreationSource,
	}

	pId, err := acctServiceClient.GetOrCreatePatient(ctx, acctId, rb, false)
	if err != nil {
		lg.WithError(err).Error("add patient failed in acctsvc")
	}

	lg.WithField("pt_id", pId).Info("created pt")

	res := phiHelpers.GetPhiInfoFromUserProfile(pId, &profile)
	err1 := phi.AddPhiProfiles(ctx, db, res)
	if err1 != nil {
		lg.WithError(err1).Error("failed to write phi_profile")
	}

	return acctId, pId, nil
}

// add a region to the account in acctsvc and add a multi-region notification
func addRegion(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	accountId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithField("acct_id", accountId).
		WithField("region", regions.GetRegionID())

	lg.Info("acct svc add new region")
	err := acctSvcClient.AddNewRegion(ctx, regions.GetRegionID(), accountId)
	if err != nil {
		lg.WithError(err).Error("unable to add new region in acctsvc")
		return err
	}

	// Create a new Multi-Region notification
	notifID := ksuid.New().String()
	_, err = notifs.CreateAccountNotification(
		ctx,
		db,
		notifID,
		accountId,
		coreapi.NotificationMultiRegion,
		"",
		nil,
	)
	if err != nil {
		lg.WithError(err).Error("failed to create multi-region notification")
		return err
	}

	return nil
}

func UpdateMissingPatientPII(
	ctx context.Context,
	acctClient accountservice.AccountService,
	p accountservice.Patient,
	newp accountservice.Patient,
) {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", p.AccountId)
	if (p.Phone == "" && newp.Phone != "") || (p.DOB == "" && newp.DOB != "") ||
		(p.FirstName == "" && newp.FirstName != "") ||
		(p.LastName == "" && newp.LastName != "") {
		lg.WithField("patient_id", p.PatientId).Info("backfilling patient PII")
		go func(ctx context.Context) {
			err := acctClient.UpdatePatient(ctx, p.AccountId, p.PatientId, newp)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithError(err).Error("could not backfill patient")
			}
		}(bgctx.GetBGCtxWithCorrelation(ctx))
	}
}

// GetAccountIdLanguageTagProvider returns a LanguageTagProvider suitable for phlanguage.languageSelector.WithAccountid
func GetAccountIdLanguageTagProvider(
	as accountservice.AccountService,
) phlanguage.LanguageTagProvider {
	p := func(ctx context.Context, acctId interface{}) language.Tag {
		acctIdString, ok := acctId.(string)
		if !ok {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"acct_id": acctId,
			}).Error("could not get acct language")
			return language.Tag{}
		}
		settings, err := as.GetAccountSettings(ctx, acctIdString)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"acct_id": acctId,
			}).WithError(err).Error("could not get acct language settings")
			return language.Tag{}
		}
		if settings.Language != "" {
			langTag, err := language.Parse(settings.Language)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
					"language": settings.Language,
				}).WithError(err).Error("error parsing language from string")
			}
			return langTag
		}
		return language.Tag{}
	}
	return phlanguage.LanguageTagProvider{
		GetLanguageTag: p,
	}
}
