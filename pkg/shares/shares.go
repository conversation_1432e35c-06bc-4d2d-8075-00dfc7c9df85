package shares

import (
	"bytes"
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/eunitymetadata"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	sqlFax "gitlab.com/pockethealth/coreapi/pkg/mysql/fax"
	sqlHandler "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/shareanalytics"
	sqlShares "gitlab.com/pockethealth/coreapi/pkg/mysql/shares"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v2/pkg/httpclient"
	"golang.org/x/text/language"
)

type ShareEmail struct {
	Name         string
	ResultsLink  string
	SecurityCode string
	Dob          string
}

type NewShareResult struct {
	ID  string
	PDF []byte //nil for download shares, email shares
}

//TODO: this file has miles of duplicate code. Refactor.

func (s *SharesApiService) getImageAndReportIds(
	ctx context.Context,
	acctId string,
	examList []coreapi.Exam,
) (objectIds []string, err error) {
	objs := make([]string, 0)
	if len(examList) > 0 {
		uuids := make([]string, len(examList))
		for i := range examList {
			uuids[i] = examList[i].UUID
		}
		objs, err = s.examService.GetObjectIDsByExamUUIDs(ctx, acctId, uuids, true)
		if err != nil {
			return nil, err
		}
	}

	return objs, nil
}

func (s *SharesApiService) finishEmailShare(
	ctx context.Context,
	mode coreapi.ShareMode,
	ccUser bool,
	hrIds []string,
	patientId string,
	examList []coreapi.Exam,
	shareId string,
	recipient string,
	DOB string,
	patientName string,
	pin string,
	acctId string,
	delayShare bool,
) (err error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"shareId":     shareId,
		"acctId":      acctId,
		"shareMethod": coreapi.EMAIL,
	})
	lg.Info("async finish share")

	if !delayShare {
		err = s.finishShareObjects(
			ctx,
			mode,
			coreapi.EMAIL,
			acctId,
			examList,
			hrIds,
			shareId,
		)
		if err != nil {
			lg.WithError(err).Error("get objects failed")
			return
		}
	}

	err = s.share(
		ctx,
		shareId,
		mode,
		coreapi.EMAIL,
		recipient,
		patientName,
		pin,
		acctId,
		patientId,
	)
	if err != nil {
		lg.WithError(err).Error("Share create failed")
		return
	}

	acct, err := s.acctSvcClient.GetAccountInfo(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve user email ")
		return
	}
	userEmail := acct.Email
	if ccUser {
		err = sendEmailShare(ctx, s.mailer, shareId, pin, patientName, recipient, DOB, s.frontEndHost, userEmail)
	} else {
		err = sendEmailShare(ctx, s.mailer, shareId, pin, patientName, recipient, DOB, s.frontEndHost, "")
	}
	if err != nil {
		lg.WithError(err).Error("Email share send failed")
		return
	}
	err = sendConfirmationEmail(
		ctx,
		s.mailer,
		acctId,
		userEmail,
		cio_email.SHARE_EMAIL_CONFIRM,
		recipient,
	)
	if err != nil {
		lg.WithError(err).Error("Email share send confirmation failed")
		return
	}
	return
}

func (s *SharesApiService) finishFaxShare(
	ctx context.Context,
	mode coreapi.ShareMode,
	hrIds []string,
	patientId string,
	examList []coreapi.Exam,
	originSecureCode string,
	shareId string,
	recipient string,
	DOB string,
	expiry string,
	pdfJsonString string,
	patientName string,
	acctId string,
	pdfBytes []byte,
	delayShare bool,
) (err error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"shareId":     shareId,
		"shareMethod": coreapi.ACCESS_PAGE_FAX,
	})
	lg.Info("async finish share")

	// (v3 transfer) delay share
	if !delayShare {
		err = s.finishShareObjects(
			ctx,
			mode,
			coreapi.ACCESS_PAGE_FAX,
			acctId,
			examList,
			hrIds,
			shareId,
		)
		if err != nil {
			lg.WithError(err).Error("get objects failed")
			return
		}
	}

	err = sqlShares.InsertViewShare(
		ctx,
		s.sqldb,
		originSecureCode,
		shareId,
		DOB[:4]+DOB[5:7]+DOB[8:],
		expiry,
		pdfJsonString,
	)
	if err != nil {
		lg.WithError(err).Error("Failed to insert to view share table")
		return
	}

	err = s.share(
		ctx,
		shareId,
		mode,
		coreapi.ACCESS_PAGE_FAX,
		recipient,
		patientName,
		"",
		acctId,
		patientId,
	)
	if err != nil {
		lg.WithError(err).Error("Share create failed")
		return
	}

	err = s.sendFaxShare(ctx, shareId, recipient, pdfJsonString, true, bytes.NewReader(pdfBytes))
	if err != nil {
		lg.WithError(err).Error("Fax share failed")
		return
	}
	acct, err := s.acctSvcClient.GetAccountInfo(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve user email for share confirmation notification")
		return
	}
	userEmail := acct.Email
	err = sendConfirmationEmail(
		ctx,
		s.mailer,
		acctId,
		userEmail,
		cio_email.SHARE_FAX_CONFIRMED,
		recipient,
	)
	if err != nil {
		lg.WithError(err).Error("Fax share send confirmation failed")
		return
	}
	return
}

func (s *SharesApiService) finishPrintShare(
	ctx context.Context,
	mode coreapi.ShareMode,
	hrIds []string,
	patientId string,
	examList []coreapi.Exam,
	originSecureCode string,
	shareId string,
	DOB string,
	expiry string,
	pdfJsonString string,
	patientName string,
	acctId string,
	delayShare bool,
) (err error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"shareId":     shareId,
		"acctId":      acctId,
		"shareMethod": coreapi.ACCESS_PAGE_PRINT,
	})
	lg.Info("async finish share")

	if !delayShare {
		err = s.finishShareObjects(
			ctx,
			mode,
			coreapi.EMAIL,
			acctId,
			examList,
			hrIds,
			shareId,
		)
		if err != nil {
			lg.WithError(err).Error("get objects failed")
			return
		}
	}

	err = sqlShares.InsertViewShare(
		ctx,
		s.sqldb,
		originSecureCode,
		shareId,
		DOB[:4]+DOB[5:7]+DOB[8:],
		expiry,
		pdfJsonString,
	)
	if err != nil {
		lg.WithError(err).Error("Failed to insert to view share table")
		return
	}

	err = s.share(
		ctx,
		shareId,
		mode,
		coreapi.ACCESS_PAGE_PRINT,
		"",
		patientName,
		"",
		acctId,
		patientId,
	)
	if err != nil {
		lg.WithError(err).Error("Share create failed")
		return
	}
	acct, err := s.acctSvcClient.GetAccountInfo(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve user email")
		return
	}
	userEmail := acct.Email
	err = sendConfirmationEmail(
		ctx,
		s.mailer,
		acctId,
		userEmail,
		cio_email.SHARE_PAPER_CONFIRM,
		"",
	)
	if err != nil {
		lg.WithError(err).Error("Print share send confirmation failed")
		return
	}
	return
}

func (s *SharesApiService) finishOfflineShare(
	ctx context.Context,
	mode coreapi.ShareMode,
	method coreapi.ShareMethod,
	hrIds []string,
	patientId string,
	examList []coreapi.Exam,
	shareId string,
	patientName string,
	acctId string,
) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"shareId":     shareId,
		"acctId":      acctId,
		"shareMethod": method,
	})
	lg.Info("finish offline share")

	objIds, err := s.getImageAndReportIds(ctx, acctId, examList)
	if err != nil {
		lg.WithError(err).Error("get objects failed")
		return
	}
	err = shareanalytics.LogShare(
		ctx,
		s.sqldb,
		method,
		objIds,
		hrIds,
		shareId,
		acctId,
		mode == coreapi.ALL,
	)
	if err != nil {
		lg.WithError(err).Error("Log share failed")
		return
	}

	err = s.share(ctx, shareId, mode, method, "", patientName, "", acctId, patientId)
	if err != nil {
		lg.WithError(err).Error("Share create failed")
		return
	}
}

func (s *SharesApiService) generateAndStoreEUnityMetadata(
	ctx context.Context,
	shareId string,
) error {
	xml, err := eunitymetadata.GenerateShareMetadata(ctx, s.sqldb, shareId)
	if err != nil {
		return err
	}
	err = azureUtils.UploadBlobBytes(
		ctx,
		s.containerClient.ShareMetadata,
		shareId,
		[]byte(xml),
	)

	if err != nil {
		return err
	}
	return nil
}

func generateSecurityCode() (og string, formatted string, err error) {
	const letters = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
	bytesrandom, err := secure.GenerateRandomBytes(9)
	if err != nil {
		return "", "", err
	}
	for i, b := range bytesrandom {
		bytesrandom[i] = letters[b%byte(len(letters))]
	}
	og = string(bytesrandom)

	re := regexp.MustCompile("^[a-zA-Z0-9]*$")
	formatted = og
	if len(formatted) == 9 && re.MatchString(formatted) {
		formatted = formatted[:3] + "-" + formatted[3:]
		formatted = formatted[:7] + "-" + formatted[7:]
	} else {
		return "", "", errors.New("Security Code format wrong")
	}

	return og, formatted, nil
}

func (s *SharesApiService) share(
	ctx context.Context,
	shareId string,
	mode coreapi.ShareMode,
	shareMethod coreapi.ShareMethod,
	shareRecipient string,
	fullname string,
	sharePin string,
	acctId string,
	patientId string,
) error {
	var err error
	var scanId, recipient, pin string
	switch shareMethod {
	case coreapi.ACCESS_PAGE_PRINT, coreapi.ACCESS_PAGE_FAX:
		scanId = "link"
		recipient = "pdf"
		pin = ""
	case coreapi.EMAIL:
		scanId = string(mode)
		recipient = shareRecipient
		pin = sharePin
	case coreapi.ZIP, coreapi.ISO:
		scanId = "offline"
		recipient = ""
		pin = ""
	}

	err = sqlShares.InsertShare(
		ctx,
		s.sqldb,
		acctId,
		shareId,
		scanId,
		recipient,
		pin,
		patientId,
	)
	if err != nil {
		return err
	}

	return nil
}

func (s *SharesApiService) handleShare(
	ctx context.Context,
	share coreapi.Share,
	acctId string,
) (pdfBytes []byte, shareId string, err error) {
	shareId, _ = secure.GenerateRandomString(32)
	if share.Method.IsOfflineShare() {
		shareId = "offline_" + shareId
	}
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
		"acct_id":  acctId,
	})
	lg.Info("handling share")

	var patientName string
	var displayPatientName string
	var DOB string
	//TODO: when revamping sharing, a patient should be chosen for a share. That will simplify the below logic.
	if len(share.ExamList) > 0 {
		//look up exam for pt info
		exam, err := s.examService.GetExamBasic(ctx, acctId, share.ExamList[0].UUID)
		if err != nil {
			lg.WithField("exam_uuid", share.ExamList[0].UUID).WithError(err).Error("could not fetch exam for share")
			return nil, "", err
		}
		DOB = exam.GetFormattedPatientDOB()
		formattedPatientName := exam.GetFormattedPatientName(ctx)
		displayPatientName = formattedPatientName.GetLastCommaFirst()
		patientName = formattedPatientName.GetFullName()
	}
	// get DOB from patient if sharing healthrecords, or exam doesn't have proper DOB
	// the latter is just for legacy transfers:
	// some exams are DICOM-SR reports, which has its own exam row, but DOB is empty.
	// new transfers/studies/exams shouldn't need this
	if len(share.ExamList) == 0 || len(DOB) != 10 {
		var patientId string
		if len(share.HealthRecords) > 0 {
			patientId = share.HealthRecords[0].PatientId
		} else {
			patientId = share.ExamList[0].PatientId
		}

		p, err := s.acctSvcClient.GetPatient(ctx, acctId, patientId)
		if err != nil {
			lg.WithField("patient_id", patientId).WithError(err).Error("couldn't get patient for share")
			return nil, "", err
		}

		patientName = fmt.Sprintf("%s %s", p.FirstName, p.LastName)
		displayPatientName = fmt.Sprintf("%s, %s", p.LastName, p.FirstName)

		//dob is stored differently in patients than we use it for shares.
		//patient stores as 1994-03-20
		// Need 1994/03/20
		dt, err := time.Parse("2006-01-02", p.DOB)
		if err != nil {
			//without valid dob cannot create a share
			lg.WithField("patient_id", p.PatientId).Error("cannot parse patient dob for HR only share")
			return nil, "", err
		}

		DOB = dt.Format("2006/01/02")
	}

	if parsed, err := time.Parse("2006/01/02", DOB); err != nil || parsed.IsZero() {
		err = errors.New("Security DOB format wrong")
		lg.WithError(err).Error()
		return nil, "", err
	}

	ogSecurityCode, formattedSecurityCode, err := addViewcodeRR(
		ctx,
		DOB,
		s.regionrouterClient,
		generateSecurityCode,
	)
	if err != nil {
		return nil, "", err
	}

	validityPeriod := 30
	expiryTime, expiryDate := datetime.GenerateExpiry(validityPeriod)
	var pdfJsonString string
	var pin string

	lang := phlanguage.Select(ctx, s.supportedLanguages).
		WithAccountId(s.languageTagProviders.AccountId, acctId).
		GetLanguageTag()

	var faxPdfBytes []byte
	switch share.Method {
	case coreapi.ACCESS_PAGE_FAX:
		pdfJsonString, faxPdfBytes, err = createSharePDF(
			share.Method,
			"FAXLINK",
			acctId,
			share.Recipient,
			displayPatientName,
			DOB,
			formattedSecurityCode,
			share.GetRecordSummaryJson(),
			strconv.Itoa(len(share.ExamList)+len(share.HealthRecords)),
			expiryDate,
			expiryTime,
			strconv.Itoa(validityPeriod),
			false,
			lang,
		)
		if err != nil {
			lg.WithError(err).Error("failed to generate fax page")
			return nil, "", err
		}

		//create printable version for patient (different pdf than the fax)
		_, pdfBytes, err = createSharePDF(
			coreapi.ACCESS_PAGE_PRINT,
			"SIMPLELINK",
			acctId,
			share.Recipient,
			displayPatientName,
			DOB,
			formattedSecurityCode,
			share.GetRecordSummaryJson(),
			strconv.Itoa(len(share.ExamList)+len(share.HealthRecords)),
			expiryDate,
			expiryTime,
			strconv.Itoa(validityPeriod),
			false,
			lang,
		)

		if err != nil {
			lg.WithError(err).Error("failed to patient version fax page")
			return nil, "", err
		}

	case coreapi.ACCESS_PAGE_PRINT:
		pdfJsonString, pdfBytes, err = createSharePDF(
			share.Method,
			"SIMPLELINK",
			acctId,
			share.Recipient,
			displayPatientName,
			DOB,
			formattedSecurityCode,
			share.GetRecordSummaryJson(),
			strconv.Itoa(len(share.ExamList)+len(share.HealthRecords)),
			expiryDate,
			expiryTime,
			strconv.Itoa(validityPeriod),
			true,
			lang,
		)

		if err != nil {
			lg.WithError(err).Error("failed to generate print page")
			return nil, "", err
		}
	case coreapi.EMAIL:
		pin, err = secure.GenerateRandomDigits(4)
		if err != nil {
			lg.WithError(err).Error("could not generate pin")
			return nil, "", err
		}
	}

	//for offline shares, front end will immediately call a GET to download, so need to make sure finishCreateShare is done before returning.
	//for all other shares, finish the share in the background and return.
	if share.Method.IsOfflineShare() {
		s.finishOfflineShare(
			ctx,
			share.Mode,
			share.Method,
			share.GetHRIds(),
			share.GetPatientId(),
			share.ExamList,
			shareId,
			patientName,
			acctId,
		)
	} else {
		delayShare := false
		if len(share.ExamList) > 0 {
			// v3 transfer: delay share if exams haven't been transferred (no object IDs)
			delayShare, err = sqlExams.CheckExamsNotFullyTransferred(ctx, s.sqldb, acctId, share.ExamList)
			if err != nil {
				lg.WithError(err).Error("could not check if exams were fully transferred")
			}
			if delayShare {
				err = sqlShares.StoreDelayedShare(
					ctx,
					s.sqldb,
					share,
					shareId,
					acctId,
					ogSecurityCode,
					DOB[:4]+DOB[5:7]+DOB[8:],
					pin,
				)
				if err != nil {
					return nil, "", err
				}
			}
		}

		// create a new context for functions performed async
		newCtx := bgctx.GetBGCtxWithCorrelation(ctx)

		switch share.Method {
		case coreapi.EMAIL:
			s.waitGroup.Add(1)
			go func() {
				defer s.waitGroup.Done()
				err = s.finishEmailShare(newCtx, share.Mode, share.CCUser, share.GetHRIds(), share.GetPatientId(), share.ExamList, shareId, share.Recipient, DOB, patientName, pin, acctId, delayShare)
				if err != nil {
					acct, err := s.acctSvcClient.GetAccountInfo(newCtx, acctId)
					if err != nil {
						lg.WithError(err).Error("could not retrieve user email for share failed notification")
					}
					userEmail := acct.Email
					err = sendShareFailedEmail(newCtx, s.mailer, userEmail, acctId, share.Method, share.Recipient)
					if err != nil {
						lg.WithError(err).Error("send share failure notification failed")
					}
				}
				// auto attach share if it was shared with a physician's account
				err = s.attachShareToPhysicianAccount(newCtx, shareId, accountservice.NOTIFICATION_METHOD_EMAIL, share.Recipient)
				if err != nil {
					logutils.DebugCtxLogger(newCtx).
						WithField("share_id", shareId).
						Error("failed attach share to a physician account")
				}
			}()
		case coreapi.ACCESS_PAGE_FAX:
			s.waitGroup.Add(1)
			go func() {
				defer s.waitGroup.Done()
				err = s.finishFaxShare(newCtx, share.Mode, share.GetHRIds(), share.GetPatientId(), share.ExamList, ogSecurityCode, shareId, share.Recipient, DOB, expiryTime, pdfJsonString, patientName, acctId, faxPdfBytes, delayShare)
				if err != nil {
					acct, err := s.acctSvcClient.GetAccountInfo(newCtx, acctId)
					if err != nil {
						lg.WithError(err).Error("could not retrieve user email for share failed notification")
					}
					userEmail := acct.Email
					err = sendShareFailedEmail(newCtx, s.mailer, userEmail, acctId, share.Method, share.Recipient)
					if err != nil {
						lg.WithError(err).Error("send share failure notification failed")
					}
				}
				// auto attach share if it was shared with a physician's account
				err = s.attachShareToPhysicianAccount(newCtx, shareId, accountservice.NOTIFICATION_METHOD_FAX, share.Recipient)
				if err != nil {
					logutils.DebugCtxLogger(newCtx).
						WithField("share_id", shareId).
						Error("failed attach share to a physician account")
				}
			}()
		case coreapi.ACCESS_PAGE_PRINT:
			s.waitGroup.Add(1)
			go func() {
				defer s.waitGroup.Done()
				err = s.finishPrintShare(newCtx, share.Mode, share.GetHRIds(), share.GetPatientId(), share.ExamList, ogSecurityCode, shareId, DOB, expiryTime, pdfJsonString, patientName, acctId, delayShare)
				if err != nil {
					acct, err := s.acctSvcClient.GetAccountInfo(newCtx, acctId)
					if err != nil {
						lg.WithError(err).Error("could not retrieve user email for share failed notification")
					}
					userEmail := acct.Email
					err = sendShareFailedEmail(newCtx, s.mailer, userEmail, acctId, share.Method, share.Recipient)
					if err != nil {
						lg.WithError(err).Error("send share failure notification failed")
					}
				}
			}()
		}

	}

	return pdfBytes, shareId, nil
}

// (v3 transfer) This is skipped if the exams have not been fully transferred.
// (v3 transfer) Provider Service will handle these steps after the transfer is finalized
func (s *SharesApiService) finishShareObjects(
	ctx context.Context,
	mode coreapi.ShareMode,
	method coreapi.ShareMethod,
	acctId string,
	examList []coreapi.Exam,
	hrIds []string,
	shareId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
		"acct_id":  acctId,
	})
	objIds, err := s.getImageAndReportIds(ctx, acctId, examList)
	if err != nil {
		lg.WithError(err).Error("get objects failed")
		return err
	}
	err = shareanalytics.LogShare(
		ctx,
		s.sqldb,
		method,
		objIds,
		hrIds,
		shareId,
		acctId,
		mode == coreapi.ALL,
	)
	if err != nil {
		lg.WithError(err).Error("Log share failed")
		return err
	}
	if len(objIds) > 0 {
		err = s.generateAndStoreEUnityMetadata(ctx, shareId)
		if err != nil {
			lg.WithError(err).Error("Share generate metadata failed")
			return err
		}
	}
	return nil
}

func (s *SharesApiService) attachShareToPhysicianAccount(
	ctx context.Context,
	shareId string,
	method accountservice.NotificationMethod,
	recipient string,
) error {
	lg := logutils.CtxLogger(ctx).WithField("share_id", shareId)

	req := accountservice.PhysicianNotificationRequest{
		NotificationMethod: method,
		Value:              recipient,
	}
	phyAcctIds, err := s.acctSvcClient.SearchPhysicianAccount(ctx, req)
	if err != nil {
		// no physician account found, ignore and skip attach step
		return nil
	}
	for _, id := range phyAcctIds {
		rhId, err := sqlHandler.LookupHandlerId(ctx, s.sqldb, coreapi.PhysicianAccount, id)
		if err != nil {
			lg.WithError(err).Infof("Fail to lookup Handler Id, phys_acct :%s", id)
			return err
		}
		// should not create handler when attached to a P2P share.
		if rhId != "" {
			err = sqlHandler.CreateHandlerShareMapping(ctx, s.sqldb, rhId, shareId)
			if err != nil {
				lg.WithError(err).Infof("Fail to create handle share mapping, rhId :%s", rhId)
				return err
			}
		}
	}

	return nil
}

func sendEmailShare(
	ctx context.Context,
	mailer cio_email.CIOEMailer,
	shareId string,
	pin string,
	patientName string,
	recipient string,
	dob string,
	host string,
	ccEmail string,
) error {
	return mailer.Send(ctx, recipient, "", cio_email.PATIENT_SHARE, map[string]interface{}{
		"patient_name":  patientName,
		"security_code": pin,
		"results_link": fmt.Sprintf(
			"https://%s/shares?id=%s&regionId=%d",
			host,
			shareId,
			regions.GetRegionID()),
		"dob": dob,
		"bcc": ccEmail,
	})
}

func (s *SharesApiService) sendFaxShare(
	ctx context.Context,
	shareId string,
	faxNum string,
	pdfJsonString string,
	isNew bool,
	faxPdf io.Reader,
) error {

	resp, err := s.faxServiceUser.PostFax(ctx, faxNum, faxPdf)
	if err != nil {
		return fmt.Errorf("error sending fax: %s", err)
	}

	if resp.IsError {
		return fmt.Errorf("error from faxservice while sending fax: %s", resp.ErrorMessage)
	}
	if isNew {
		err := sqlFax.InsertFaxRequest(
			ctx,
			s.sqldb,
			shareId,
			faxNum,
			resp.FaxSid,
			resp.Id,
			pdfJsonString,
		)
		if err != nil {
			return fmt.Errorf("error inserting new fax request in DB: %s", err)
		}
	} else { //resend, update id only
		err := sqlFax.UpdateFaxRequest(ctx, s.sqldb, resp.Id, shareId)
		if err != nil {
			return fmt.Errorf("error updating fax request in DB: %s", err)
		}
	}

	return nil
}

func sendConfirmationEmail(
	ctx context.Context,
	ciomail cio_email.CIOEMailer,
	acctId string,
	userEmail string,
	emailType cio_email.NotificationType,
	recipient string,
) error {
	messageData := map[string]interface{}{
		"region_id": regions.GetRegionID(),
	}
	switch emailType {
	case cio_email.SHARE_FAX_CONFIRMED:
		messageData["fax_num"] = recipient
	case cio_email.SHARE_EMAIL_CONFIRM:
		messageData["recipient"] = recipient
	}

	return ciomail.Send(ctx, userEmail, acctId, emailType, messageData)
}

func sendShareFailedEmail(
	ctx context.Context,
	ciomail cio_email.CIOEMailer,
	userEmail string,
	accountId string,
	method coreapi.ShareMethod,
	recipient string,
) error {
	switch method {
	case coreapi.ACCESS_PAGE_FAX:
		err := ciomail.Send(ctx, userEmail, accountId, cio_email.SHARE_FAX_FAILED, map[string]interface{}{
			"recipient": recipient,
			"region_id": regions.GetRegionID()})
		if err != nil {
			return err
		}
		return nil

	case coreapi.EMAIL:
		err := ciomail.Send(ctx, userEmail, accountId, cio_email.SHARE_EMAIL_FAILED, map[string]interface{}{
			"recipient": recipient,
			"region_id": regions.GetRegionID()})
		if err != nil {
			return err
		}
		return nil
	default:
		err := ciomail.Send(ctx, userEmail, accountId, cio_email.SHARE_FAILED, map[string]interface{}{
			"region_id": regions.GetRegionID()})
		if err != nil {
			return err
		}
		return nil
	}
}

func createSharePDF(
	shareMethod coreapi.ShareMethod,
	folderName string,
	acctId string,
	recipient string,
	dispName string,
	dob string,
	formattedSecCode string,
	recordJson string,
	recordCount string,
	date string,
	time string,
	validPeriod string,
	removeTmp bool,
	lang language.Tag,
) (string, []byte, error) {
	//TODO: don't need to write this to disk

	var faxNum, faxSubj, faxFrom string
	if shareMethod == coreapi.ACCESS_PAGE_FAX {
		faxNum = recipient
		faxSubj = "Transfer from " + dispName
		faxFrom = dispName
	}

	pdfDynamicVariables := pdfs.SharePdfDynamicVariables{
		FaxAttn:                     faxNum,
		FaxSubject:                  faxSubj,
		FaxFrom:                     faxFrom,
		FaxNote:                     "",
		InstitutionLogoPathOrBase64: "",
		InstitutionName:             "",
		PatientName:                 dispName,
		DOB:                         dob,
		SecurityCode:                formattedSecCode,
		RecordJson:                  recordJson,
		TotalRecord:                 recordCount,
		ExpiryDate:                  date,
		ExpiryTime:                  time,
		ValidityPeriod:              validPeriod,
	}
	printOrFax := "print"
	if shareMethod == coreapi.ACCESS_PAGE_FAX {
		printOrFax = "fax"
	}
	return pdfs.CreateSharePDF(
		folderName,
		"patient",
		printOrFax,
		acctId,
		pdfDynamicVariables,
		removeTmp,
		lang,
	)
}

func getShareMethodExpiryAndActivation(
	shareBase sqlShares.ShareBase,
) (shareMethod coreapi.ShareMethod, expiry string, active bool, err error) {
	if shareBase.Recipient == "pdf" {
		//check expiry
		if shareBase.Expiry == "" {
			return "", "", false, errors.New("no expiry")
		} else {
			var active bool
			if datetime.IsExpired(shareBase.Expiry) {
				active = false
			} else {
				active = shareBase.Active
			}

			if shareBase.FaxNumber == "" {
				//printalink
				return coreapi.ACCESS_PAGE_PRINT, shareBase.Expiry[0:10], active, nil
			} else {
				//fax
				if shareBase.FaxStatus == "" {
					return "", "", false, errors.New("fax share has no fax status")
				} else {
					return coreapi.ACCESS_PAGE_FAX, shareBase.Expiry[0:10], active, nil
				}
			}
		}
	} else {
		if shareBase.ScanId == "offline" {
			return coreapi.ZIP, "", shareBase.Active, nil
		}
		return coreapi.EMAIL, "", shareBase.Active, nil
	}
}

func sanitizePatientName(lastName string) (validLastName string) {
	validLastName = strings.ToUpper(lastName)
	reg, err := regexp.Compile("[^a-zA-Z]+")
	if err != nil {
		return
	}
	validLastName = reg.ReplaceAllString(lastName, "")
	if len(validLastName) > 10 {
		validLastName = validLastName[:10]
	}
	return validLastName
}

// try adding hash of viewcode+DOB to RR, if duplicate exists at RR retry up to {max} times
func addViewcodeRR(
	ctx context.Context,
	DOB string,
	regionrouterClient regions.RegionRouterClient,
	generateSecurityCode func() (string, string, error),
) (ogSecurityCode string, formattedSecurityCode string, err error) {
	lg := logutils.DebugCtxLogger(ctx)

	DOB = strings.ReplaceAll(DOB, "/", "")

	viewcodeAdded := false
	attempts, max := 0, 5
	for !viewcodeAdded && attempts <= max {
		ogSecurityCode, formattedSecurityCode, err = generateSecurityCode()
		if err != nil {
			//TODO: look into err
			return "", "", err
		}

		attempts += 1

		// hash concatenation of viewcode+DOB before adding to RR
		hash := sha256.New()
		viewcodeDOB := ogSecurityCode + DOB
		_, err = io.WriteString(hash, viewcodeDOB)
		if err != nil {
			lg.WithError(err).Error("addViewcodeRR(): error hashing viewcode+DOB")
		}
		hashString := base64.URLEncoding.EncodeToString(hash.Sum(nil)) //#nosec G401

		err = regionrouterClient.AddNewId(ctx, regions.ViewCodeIdType, hashString)
		if err != nil && err != httpclient.ErrRequestConflict {
			// got an error unrelated to duplicate viewcode+DOB hash at RR, quit
			lg.WithError(err).
				Error("addViewcodeRR(): error updating regionrouter with viewcode+DOB hash to region mapping")
			break
		} else if err == nil {
			viewcodeAdded = true
		}
	}

	lg.WithFields(logrus.Fields{
		"attempts":      attempts,
		"viewcodeAdded": viewcodeAdded,
	}).Debug("addViewcodeRR(): attempt to add viewcode+DOB hash to RR")

	return ogSecurityCode, formattedSecurityCode, err
}

func SharePinValidation(
	ctx context.Context,
	db *sql.DB,
	shareId string,
	pin string,
	ip string,
) (coreapi.ShareValidateResponse, error) {
	active, err := sqlShares.IsShareActive(ctx, db, shareId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("error checking share active during pin verification")
	}
	if !active {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_REVOKED)
	}
	if !sqlShares.IsValidPin(ctx, db, pin, shareId) {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_BAD_CREDENTIALS)
	} else if sqlShares.IsShareDelayed(ctx, db, shareId) {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_ID_NULL)
	}
	return generateAuthTokenResponse(ctx, shareId, ip, "", "", false)
}

func ShareViewcodeValidation(
	ctx context.Context,
	db *sql.DB,
	orgSvcClient orgsvc.OrgService,
	viewcode string,
	dob string,
	ip string,
) (coreapi.ShareValidateResponse, error) {
	shareId, expiry, err := sqlShares.IsValidViewCodeDOB(ctx, db, viewcode, dob)
	if err != nil && err.Error() != "Extended" {
		return coreapi.ShareValidateResponse{}, err
	} else if shareId == "" {
		if sqlShares.IsRevokedProviderShare(ctx, db, viewcode) {
			return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_REVOKED)
		} else {
			return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_ID_NULL)
		}
	} else if sqlShares.IsShareDelayed(ctx, db, shareId) {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_ID_NULL)
	}
	active, err2 := sqlShares.IsShareActive(ctx, db, shareId)
	if err2 != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err2).
			Error("error checking share active during viewcode verification")
	}
	if !active {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_REVOKED)
	}

	providerName, url, errP2P := GetP2PShareProvider(ctx, db, shareId, orgSvcClient)
	if errP2P != nil {
		logutils.DebugCtxLogger(ctx).WithError(errP2P).Error("Error while validating P2P Share")
	}
	if datetime.IsExpired(expiry) {
		return coreapi.ShareValidateResponse{
				RequiresNameAuth: true,
				Provider:         providerName,
				ProviderURL:      url,
			}, errors.New(
				errmsg.ERR_LINK_EXPIRED,
			)
	}
	if err != nil && err.Error() == "Extended" {
		return generateAuthTokenResponse(ctx, shareId, ip, providerName, url, true)
	}
	return generateAuthTokenResponse(ctx, shareId, ip, providerName, url, false)
}

func ShareLastnameValidation(
	ctx context.Context,
	db *sql.DB,
	shareId string,
	lastname string,
	ip string,
) (coreapi.ShareValidateResponse, error) {
	active, err := sqlShares.IsShareActive(ctx, db, shareId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("error checking share active during lastname verification")
	}
	if !active {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_REVOKED)
	}
	patientLastNames, err := sqlShares.GetPatientLastNameByShareId(ctx, db, shareId)
	if err != nil {
		return coreapi.ShareValidateResponse{}, err
	}
	nameMatched := false
	for _, l := range patientLastNames {
		if stringhelpers.CompareAlphabeticOnly(lastname, l) {
			nameMatched = true
			break
		}
	}
	if !nameMatched {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_BAD_CREDENTIALS)
	} else if sqlShares.IsShareDelayed(ctx, db, shareId) {
		return coreapi.ShareValidateResponse{}, errors.New(errmsg.ERR_SHARE_ID_NULL)
	}
	return generateAuthTokenResponse(ctx, shareId, ip, "", "", false)
}

func generateAuthTokenResponse(
	ctx context.Context,
	shareId string,
	ip string,
	providerName string,
	url string,
	requiresNameAuth bool,
) (coreapi.ShareValidateResponse, error) {
	token, err := auth.MakeShareAuthToken(shareId, ip)
	if err != nil {
		return coreapi.ShareValidateResponse{}, err
	}
	return coreapi.ShareValidateResponse{
		Token:            token,
		RequiresNameAuth: requiresNameAuth,
		Provider:         providerName,
		ProviderURL:      url,
	}, nil
}
