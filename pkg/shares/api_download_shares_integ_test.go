//go:build integration
// +build integration

package shares

import (
	"fmt"
	"net/http"
	"sync"
	"testing"

	"github.com/samber/lo"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/download"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	"gitlab.com/pockethealth/coreapi/pkg/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	recordstreamingtest "gitlab.com/pockethealth/coreapi/pkg/testutils/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"

	"github.com/amplitude/analytics-go/amplitude"
	"github.com/amplitude/experiment-go-server/pkg/experiment"
	_ "github.com/go-sql-driver/mysql"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestGetRecordStreamingDownloadAuthToken(t *testing.T) {
	db := testutils.SetupTestDB(t)
	t.Cleanup(func() {
		db.Close()
	})
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	recordStreamingService := recordstreaming.NewRecordStreamingService(
		db,
		orgServiceMock,
		recordServiceMock,
	)

	sharesService := NewSharesApiService(
		db,
		azureUtils.ContainerClient{},
		fax.FaxSvcUser{},
		"", // frontHost,
		&sync.WaitGroup{},
		nil, // i18nBundle,
		languageproviders.LanguageTagProviders{},
		map[string]string{}, // supportedLanguages
		nil,                 // RegionRouterClient
		hrs.HlthRecSvcUser{},
		accountServiceMock, // AccountService
		orgServiceMock,     // OrgService
		false,              // excludeDeletedShares,
		nil,                // experimentsClient,
		nil,                // amplitudeClient,
		nil,                // downloadPrepareFunc
		nil,                // cioemail
		examService,        // examService
		recordStreamingService,
	)
	sharesController := NewPrivateSharesApiController(sharesService)

	t.Run("get download auth token success", func(t *testing.T) {
		// 1. Setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreamingtest.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			studyUID,
			"",   // objectID
			true, // is report uploaded
		)
		recordServiceMock.EXPECT().
			V1PhysiciansAccountIDStudiesMatchGet(mock.Anything, generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(true)), nil)

		// 2. Make authorized request to PostShareDLAuth with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodPost,
			fmt.Sprintf("/v1/shares/%s/dlauth?providerId=%d", studyUID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"shareId": studyUID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			sharesController.PostShareDLAuth,
		)

		// 3. Verify status OK
		if status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}
	})

	t.Run(
		"get download auth token with incorrect providerID should be unauthorized",
		func(t *testing.T) {
			// 1. Setup test data
			providerID := phtestutils.GenerateRandomInt64(t)
			incorrectProviderID := phtestutils.GenerateRandomInt64(t)
			physicianAccountID := phtestutils.GenerateRandomString(t, 10)
			studyUID := phtestutils.GenerateRandomString(t, 10)
			recordstreamingtest.SetupRecordStreamingData(
				t,
				db,
				providerID,
				physicianAccountID,
				studyUID,
				"",   // objectID
				true, // is report uploaded
			)

			// 2. Make authorized request to PostShareDLAuth with providerId
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodPost,
				fmt.Sprintf("/v1/shares/%s/dlauth?providerId=%d", studyUID, incorrectProviderID),
				"", // payload
			)
			request = mux.SetURLVars(request, map[string]string{
				"shareId": studyUID,
			})
			status, _ := testutils.MakeRequestToHandler(
				t,
				request,
				sharesController.PostShareDLAuth,
			)

			// 3. Verify status StatusUnauthorized
			if status != http.StatusUnauthorized {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusUnauthorized)
			}
		},
	)

	t.Run("get download auth token without providerID should be unauthorized", func(t *testing.T) {
		// 1. Setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		recordstreamingtest.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			studyUID,
			"",   // objectID
			true, // is report uploaded
		)

		// 2. Make authorized request to PostShareDLAuth without providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodPost,
			fmt.Sprintf("/v1/shares/%s/dlauth", studyUID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"shareId": studyUID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			sharesController.PostShareDLAuth,
		)

		// 3. Verify status StatusUnauthorized
		if status != http.StatusUnauthorized {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusUnauthorized)
		}
	})

	t.Run(
		"get download auth token without account auth token should be unauthorized",
		func(t *testing.T) {
			// 1. Setup test data
			providerID := phtestutils.GenerateRandomInt64(t)
			physicianAccountID := phtestutils.GenerateRandomString(t, 10)
			studyUID := phtestutils.GenerateRandomString(t, 10)
			recordstreamingtest.SetupRecordStreamingData(
				t,
				db,
				providerID,
				physicianAccountID,
				studyUID,
				"",   // objectID
				true, // is report uploaded
			)

			// 2. Make authorized request to PostShareDLAuth with providerId
			request := testutils.CreateRequest(
				t,
				http.MethodPost,
				fmt.Sprintf("/v1/shares/%s/dlauth?providerId=%d", studyUID, providerID),
				"", // payload
			)
			request = mux.SetURLVars(request, map[string]string{
				"shareId": studyUID,
			})
			status, _ := testutils.MakeRequestToHandler(
				t,
				request,
				sharesController.PostShareDLAuth,
			)

			// 3. Verify status StatusUnauthorized
			if status != http.StatusUnauthorized {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusUnauthorized)
			}
		},
	)

	t.Run(
		"get download auth token without physician permission should be unauthorized",
		func(t *testing.T) {
			// 1. Setup test data
			providerID := phtestutils.GenerateRandomInt64(t)
			physicianAccountID := phtestutils.GenerateRandomString(t, 10)
			studyUID := phtestutils.GenerateRandomString(t, 10)
			uuid, _ := recordstreamingtest.SetupRecordStreamingData(
				t,
				db,
				providerID,
				physicianAccountID,
				studyUID,
				"",   // objectID
				true, // is report uploaded
			)
			recordServiceMock.EXPECT().
				V1PhysiciansAccountIDStudiesMatchGet(mock.Anything, generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
					AccountID: physicianAccountID,
					UUID:      []string{uuid},
				}).
				Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(false)), nil)

			// 2. Make authorized request to PostShareDLAuth with providerId
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodPost,
				fmt.Sprintf("/v1/shares/%s/dlauth?providerId=%d", studyUID, providerID),
				"", // payload
			)
			request = mux.SetURLVars(request, map[string]string{
				"shareId": studyUID,
			})
			status, _ := testutils.MakeRequestToHandler(
				t,
				request,
				sharesController.PostShareDLAuth,
			)

			// 3. Verify status StatusUnauthorized
			if status != http.StatusUnauthorized {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusUnauthorized)
			}
		},
	)
}

func TestDownloadRecordStreamingStudy(t *testing.T) {
	db := testutils.SetupTestDB(t)
	t.Cleanup(func() {
		db.Close()
	})

	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	recordStreamingService := recordstreaming.NewRecordStreamingService(
		db,
		orgServiceMock,
		recordServiceMock,
	)

	sharesService := NewSharesApiService(
		db,
		azureUtils.ContainerClient{}, // containerClient
		fax.FaxSvcUser{},
		"", // frontHost,
		&sync.WaitGroup{},
		nil, // i18nBundle,
		languageproviders.LanguageTagProviders{},
		map[string]string{}, // supportedLanguages
		nil,                 // RegionRouterClient
		hrs.HlthRecSvcUser{},
		accountServiceMock, // AccountService
		orgServiceMock,
		false, // excludeDeletedShares,
		&mocks.MockAmplitudeExperimentClientData{
			Variants: map[string]experiment.Variant{},
		}, // experimentsClient,
		&mocks.MockAmplitudeEventData{
			EventLogs: make([]amplitude.Event, 0),
		}, // amplitudeClient,
		download.MockDownloadPrepare, // downloadPrepareFunc
		nil,                          // cio email
		examService,                  // examService
		recordStreamingService,
	)

	downloadSharesController := NewDLSharesApiController(
		sharesService,
		recordStreamingService,
	)

	cases := []struct {
		name   string
		accept string
	}{
		{
			name:   "download octect stream success",
			accept: "application/octet-stream",
		},
		{
			name:   "download zip success",
			accept: "application/zip",
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			// 1. Setup test data
			providerID := phtestutils.GenerateRandomInt64(t)
			physicianAccountID := phtestutils.GenerateRandomString(t, 10)
			studyUID := phtestutils.GenerateRandomString(t, 10)
			uuid, _ := recordstreamingtest.SetupRecordStreamingData(
				t,
				db,
				providerID,
				physicianAccountID,
				studyUID,
				"",   // objectID
				true, // is report uploaded
			)
			recordServiceMock.EXPECT().V1PhysiciansAccountIDStudiesMatchGet(
				mock.Anything,
				generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
					AccountID: physicianAccountID,
					UUID:      []string{uuid},
				}).Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(true)), nil)

			// 2. Make authorized request to GetSharesById with providerId
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodPost,
				fmt.Sprintf("/v1/shares/%s/dlauth?providerId=%d", studyUID, providerID),
				"", // payload
			)
			request.Header.Set("accept", c.accept)
			request = mux.SetURLVars(request, map[string]string{
				"shareId": studyUID,
			})
			status, body := testutils.MakeRequestToHandler(
				t,
				request,
				downloadSharesController.GetSharesById,
			)

			// 3. Verify status OK
			if status != http.StatusOK {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusOK)
			}

			assert.Equal(t, string(body), download.MOCK_DOWNLOAD_BYTES)
		})
	}
}
