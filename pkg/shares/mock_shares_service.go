package shares

import (
	"context"
	"errors"
	"os"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// SharesApiService is a service that implents the logic for the SharesApiServicer
// This service should implement the business logic for every endpoint for the SharesApi API.
// Include any external packages or services that will be required by this service.
type MockSharesApiService struct {
	MockGetShares           func(acctId string, limit int, offset int) (interface{}, error)
	MockValidateShareViewer func(shareId string, pin string, viewcode string, dob string, ip string, lastName string) (interface{}, error)
	MockCreateShare         func(acctId string, share coreapi.Share) (interface{}, error)
}

// NewSharesApiService creates a default api service
func NewMockSharesApiService(
	mockGetExams func(uint, string) (interface{}, error),
	mockGetShares func(string, int, int) (interface{}, error),
	mockValidate func(string, string, string, string, string, string) (interface{}, error),
	mockCreateShare func(string, coreapi.Share) (interface{}, error),
) coreapi.SharesApiServicer {
	return &MockSharesApiService{
		MockGetShares:           mockGetShares,
		MockValidateShareViewer: mockValidate,
		MockCreateShare:         mockCreateShare,
	}
}

// GetShareExam - Get exam details
func (s *MockSharesApiService) GetShareExam(
	ctx context.Context,
	shareId string,
	examId string,
) (interface{}, error) {
	// TODO - update GetShareExam with the required logic for this service method.
	// Add api_shares_service.go to the .openapi-generator-ignore to avoid overwriting this service implementation when updating open api generation.
	return nil, errors.New("service method 'GetShareExam' not implemented")
}

// GetShares - Get shares for a user
func (s *MockSharesApiService) GetShares(
	ctx context.Context,
	acctId string,
	limit int,
	offset int,
) (interface{}, error) {
	return s.MockGetShares(acctId, limit, offset)
}

// GetSharesById - Get share files (zip or disk image)
func (s *MockSharesApiService) GetSharesById(
	ctx context.Context,
	acctId string,
	shareId string,
	format string,
	examIds []string,
	ip string,
	includeViwer bool,
) (interface{}, error) {
	file, _ := os.Create("text.txt")
	defer os.Remove("text.txt")
	return Download{file: file, filename: "text.txt"}, nil
}

// GetShareHealthRecordsById - Get share healthrecord files
func (s *MockSharesApiService) GetShareHealthRecordsById(
	ctx context.Context,
	acctId string,
	shareId string,
	selectedHealthRecords []string,
) ([]byte, string, error) {
	return []byte(`some data`), "Lastname", nil
}

// PostReshare - Reshare
func (s *MockSharesApiService) PostReshare(
	ctx context.Context,
	acctId string,
	shareId string,
) (interface{}, error) {
	// TODO - update PostReshare with the required logic for this service method.
	// Add api_shares_service.go to the .openapi-generator-ignore to avoid overwriting this service implementation when updating open api generation.
	return nil, errors.New("service method 'PostReshare' not implemented")
}

// PostShares - Create a new share
func (s *MockSharesApiService) PostShares(
	ctx context.Context,
	acctId string,
	share coreapi.Share,
) (interface{}, error) {
	return s.MockCreateShare(acctId, share)
}

// PutRevokeShare - Revoke share permissions
func (s *MockSharesApiService) PutRevokeShare(
	ctx context.Context,
	acctId string,
	shareId string,
) error {
	// TODO - update PutRevokeShare with the required logic for this service method.
	// Add api_shares_service.go to the .openapi-generator-ignore to avoid overwriting this service implementation when updating open api generation.
	return errors.New("service method 'PutRevokeShare' not implemented")
}

// PostSharesValidate - Validate share viewer
func (s *MockSharesApiService) PostSharesValidate(
	ctx context.Context,
	shareId string,
	pin string,
	viewcode string,
	dob string,
	ip string,
	lastName string,
) (interface{}, error) {
	return s.MockValidateShareViewer(shareId, pin, viewcode, dob, ip, lastName)
}

func (s *MockSharesApiService) PostAttachShare(
	ctx context.Context,
	acctId string,
	shareCredentials coreapi.ShareCredentials,
) error {
	return errors.New("method 'AttachShareToPhysician' not implemented")
}

// PutExtendShare - Extend share expiry
func (s *MockSharesApiService) PutExtendShare(ctx context.Context, shareId string) error {
	return errors.New("service method 'PutRevokeShare' not implemented")
}

// PostShareDLAuth
func (s *MockSharesApiService) PostShareDLAuth(
	ctx context.Context,
	shareId string,
	acctId string,
) (interface{}, error) {
	return nil, errors.New("service method 'PostShareDLAuth' not implemented")
}

func (s *MockSharesApiService) PostRecordStreamingDLAuth(ctx context.Context, providerID int64, physicianAccountID string, studyUID string) (any, error) {
	return nil, errors.New("service method 'PostRecordStreamingDLAuth' not implemented")
}

func (s *MockSharesApiService) DownloadRecordStreamingStudy(
	ctx context.Context,
	providerID int64,
	physicianAccountID string,
	studyUID string,
	format string,
	ip string,
	includeViewer bool,
) (any, error) {
	return nil, errors.New("service method 'DownloadRecordStreamingStudy' not implemented")
}
