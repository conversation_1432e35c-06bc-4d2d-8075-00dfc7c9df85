/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package shares

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateSharesApiController binds http requests to an api service and writes the service results to the http response
type PrivateSharesApiController struct {
	service coreapi.SharesApiServicer
}

// NewSharesApiController creates a default api controller
func NewPrivateSharesApiController(s coreapi.SharesApiServicer) coreapi.PrivateSharesApiRouter {
	return &PrivateSharesApiController{service: s}
}

// Routes returns all of the api route for the SharesApiController
func (c *PrivateSharesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetShares",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetShares,
		},
		{
			Name:        "PostReshare",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{shareId}/reshare",
			HandlerFunc: c.PostReshare,
		},
		{
			Name:        "PostShares",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostShares,
		},
		{
			Name:        "PostAttachShare",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/attach",
			HandlerFunc: c.PostAttachShare,
		},
		{
			Name:        "PutRevokeShare",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/{shareId}/revoke",
			HandlerFunc: c.PutRevokeShare,
		},
		{
			Name:        "PutExtendShare",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/{shareId}/extend",
			HandlerFunc: c.PutExtendShare,
		},
		{
			Name:        "PostShareDLAuth",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{shareId}/dlauth",
			HandlerFunc: c.PostShareDLAuth,
		},
	}
}

func (c *PrivateSharesApiController) GetPathPrefix() string {
	return "/v1/shares"
}

func (c *PrivateSharesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

// GetShares - Get shares for a user
func (c *PrivateSharesApiController) GetShares(w http.ResponseWriter, r *http.Request) {
	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	//pagination
	limit := -1
	offset := 0
	exists, limitQuery, err := coreapi.ParseQueryParamInt(r, "limit")
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}
	if exists {
		if limitQuery < 0 {
			httperror.ErrorWithLog(
				w,
				r,
				errmsg.ERR_BAD_QUERY_PARAM+": limit",
				http.StatusBadRequest,
			)
			return
		}
		limit = limitQuery
	}
	exists, offsetQuery, err := coreapi.ParseQueryParamInt(r, "offset")
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}
	if exists {
		if offsetQuery < 0 {
			httperror.ErrorWithLog(
				w,
				r,
				errmsg.ERR_BAD_QUERY_PARAM+": offset",
				http.StatusBadRequest,
			)
			return
		}
		offset = offsetQuery
	}
	result, err := c.service.GetShares(r.Context(), acctId, limit, offset)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), httperror.ErrormsgToStatus(err.Error()))
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostReshare - Reshare
func (c *PrivateSharesApiController) PostReshare(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareId := params["shareId"]
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil || claims.AccountID == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, err := c.service.PostReshare(
		r.Context(),
		claims.AccountID,
		shareId,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	//if printing, need to send back the pdf
	if result != nil {
		pdfBytes, ok := result.([]byte)
		if !ok {
			logutils.DebugCtxLogger(r.Context()).Error("unexpected result format")
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/pdf")
		_, err = w.Write(pdfBytes)
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).WithError(err).Error("couldn't write response bytes")
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}
	w.WriteHeader(http.StatusOK)
}

// PostShares - Create a new share
func (c *PrivateSharesApiController) PostShares(w http.ResponseWriter, r *http.Request) {
	share := &coreapi.Share{}
	if err := json.NewDecoder(r.Body).Decode(&share); err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
	}
	switch share.Method {
	case coreapi.ACCESS_PAGE_FAX:
		share.Recipient = stringhelpers.CleanUp(share.Recipient, `[^0-9]`, "")
	}
	if err := share.IsValid(); err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}

	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil || claims.AccountID == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, err := c.service.PostShares(r.Context(), claims.AccountID, *share)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	shareResult, ok := result.(NewShareResult)
	if !ok {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}

	idOnlyStruct := struct {
		Id string `json:"id"`
	}{
		Id: shareResult.ID,
	}

	idBytes, err := json.Marshal(idOnlyStruct)
	if err != nil {
		logutils.CtxLogger(r.Context()).Error("could not marshal share ID, continue anyway")
	}

	//regardless of type, return share id in header
	w.Header().Set("Pockethealth-Api-Result", string(idBytes))

	//if printing/faxing, need to send back the pdf
	if share.Method == coreapi.ACCESS_PAGE_PRINT || share.Method == coreapi.ACCESS_PAGE_FAX {
		w.Header().Set("Content-Type", "application/pdf")
		pdfBytes := shareResult.PDF
		if pdfBytes == nil {
			logutils.CtxLogger(r.Context()).Error("bytes not returned for pdf")
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		_, err = w.Write(pdfBytes)
		if err != nil {
			logutils.CtxLogger(r.Context()).WithError(err).Error("could not write pdf bytes to response")
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
	} else if share.Method.IsOfflineShare() {
		shareId := shareResult.ID
		if shareId == "" {
			logutils.CtxLogger(r.Context()).Error("no share id")
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		_, err = w.Write([]byte(shareId))
		if err != nil {
			logutils.CtxLogger(r.Context()).WithError(err).Error("could not write share id")
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
	}

	w.WriteHeader(http.StatusOK)
}

// PutRevokeShare - Revoke share permissions
func (c *PrivateSharesApiController) PutRevokeShare(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareId := params["shareId"]
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	err = c.service.PutRevokeShare(r.Context(), acctId, shareId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	w.WriteHeader(http.StatusOK)
}

// PostAttachShare - Validate and attach a pin + dob share to a physician account
func (c *PrivateSharesApiController) PostAttachShare(w http.ResponseWriter, r *http.Request) {
	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	shareCredentials := coreapi.ShareCredentials{}
	if err := json.NewDecoder(r.Body).Decode(&shareCredentials); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PostAttachShare(r.Context(), acctId, shareCredentials)
	if err != nil {
		var errStatus int
		if err.Error() == errmsg.ERR_BAD_CREDENTIALS {
			errStatus = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_LINK_EXPIRED {
			errStatus = http.StatusGone
		} else if err.Error() == errmsg.ERR_SHARE_ID_NULL {
			errStatus = http.StatusNotFound
		} else if err.Error() == errmsg.ERR_SHARE_REVOKED {
			errStatus = http.StatusForbidden
		} else {
			errStatus = http.StatusInternalServerError
			httperror.ErrorWithLog(w, r, http.StatusText(errStatus), errStatus)
			return
		}
		httperror.ErrorWithLog(w, r, err.Error(), errStatus)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// PutExtendShare - Extend share expiry date
func (c *PrivateSharesApiController) PutExtendShare(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareId := params["shareId"]
	token := r.Header.Get("Authorization")
	tokenShareId, _, err := auth.DecodeShareViewerToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	// Token and shareID should be matched
	if tokenShareId != shareId {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	err = c.service.PutExtendShare(r.Context(), shareId)
	if err != nil {
		if err.Error() == errmsg.ERR_UNABLE_EXTEND {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusForbidden)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// PostShareDLAuth - Get a limited token for downloading a share
func (c *PrivateSharesApiController) PostShareDLAuth(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareId := params["shareId"]
	token := r.Header.Get("Authorization")

	// optional query parameter provider id for getting shares for record streaming studies
	hasProviderID, providerID, _ := coreapi.ParseQueryParamInt64(r, "providerId")

	//This endpoint is accessible to both inportal users and share viewers. Check that authorization is one of those.
	tokenShareId, _, errS := auth.DecodeShareViewerToken(token)
	accountID, err := auth.DecodeAccountToken(token)
	if errS != nil && err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	//if being accessed by a share user, check that the share id in the token and the path param match.
	if errS == nil && tokenShareId != shareId {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	var result any
	var authError error

	// First try to authorize study download using record streaming physician account logic
	if hasProviderID {
		result, authError = c.service.PostRecordStreamingDLAuth(r.Context(), providerID, accountID, shareId)
	}

	// If the record streaming logic failed due to authorization
	// Then this is likely a non-record streamed share
	// Try to auth using traditional share logic instead
	if result == nil || (authError != nil && authError.Error() == errmsg.ERR_NOT_AUTHORIZED) {
		result, authError = c.service.PostShareDLAuth(r.Context(), shareId, accountID)
	}

	if authError != nil {
		if authError.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, authError.Error(), http.StatusUnauthorized)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
