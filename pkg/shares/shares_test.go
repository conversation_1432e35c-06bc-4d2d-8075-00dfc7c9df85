package shares

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"gitlab.com/pockethealth/coreapi/pkg/util/testutil"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"golang.org/x/text/language"
)

// TODO: this only tests a single case
func TestHandleShare(t *testing.T) {

	t.Run("failed pdf generation fails the share", func(t *testing.T) {

		//unsupported pdf language will fail generation
		getLanguageTag := func(ctx context.Context, id interface{}) language.Tag {
			return language.Georgian
		}
		langProviders := languageproviders.LanguageTagProviders{
			AccountId: phlanguage.LanguageTagProvider{GetLanguageTag: getLanguageTag},
		}
		db, _, err := sqlmock.New()
		require.NoError(t, err)
		uuid := "123abc"
		acctId := "123abc"
		var activated *bool
		mockRecSvc := mockrecordservice.NewMockRecordServiceClientInterface(t)
		mockRecSvc.EXPECT().GetStudies(
			mock.Anything,
			acctId,
			false,
			false,
			activated,
			[]string{uuid},
		).Return(
			[]recordservice.PatientStudy{
				{
					UUID:               uuid,
					AvailabilityStatus: recordservice.FULL_AVAILABILITY,
					DicomPatientTags: recordservice.DicomPatientTags{
						PatientName:      "LAST^FIRST",
						PatientBirthDate: "********",
						PatientSex:       "F",
					},
					AccountID: acctId,
				},
			},
			nil,
		)

		s := SharesApiService{
			sqldb:                db,
			supportedLanguages:   map[string]string{"ka": "Georgian"},
			languageTagProviders: langProviders,
			regionrouterClient:   &testutil.MockRRClient{ExpectedIDType: regions.ViewCodeIdType},
			examService: examService.NewExamService(
				db,
				&orgs.OrgServiceMock{},
				mockRecSvc,
				&planservice.PlanSvcMock{},
				&accountservice.AcctSvcMock{},
				providersservice.NewProviderServiceMock(),
				&examService.MockMigrationHelper{},
			),
		}
		share := coreapi.Share{
			Method: coreapi.ACCESS_PAGE_PRINT,
			ExamList: []coreapi.Exam{
				{
					UUID: uuid,
					PatientName: models.PatientName{
						FirstAndMiddleName: "Test Middle",
						LastName:           "Last",
					},
					Dob: "1994-03-20",
				},
			},
		}

		b, _, err := s.handleShare(context.Background(), share, acctId)
		if err == nil {
			t.Fatalf("expected lack of pdf generation to fail share")
		}

		if _, ok := err.(pdfs.PDFGenerationError); !ok {
			t.Fatalf("expected PDF generation error, got %v", err)
		}

		if b != nil {
			t.Fatalf("expect bytes to be nil when PDF fails")
		}
	})
}
