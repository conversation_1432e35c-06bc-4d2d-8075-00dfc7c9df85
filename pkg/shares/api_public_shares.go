/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package shares

import (
	"encoding/json"
	"net/http"
	"strings"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

// A SharesApiController binds http requests to an api service and writes the service results to the http response
type PublicSharesApiController struct {
	service coreapi.SharesApiServicer
	lt      lockouttracker.LockoutTracker
}

// NewSharesApiController creates a default api controller
func NewPublicSharesApiController(
	s coreapi.SharesApiServicer,
	lockout lockouttracker.LockoutTracker,
) coreapi.PublicSharesApiRouter {
	return &PublicSharesApiController{service: s, lt: lockout}
}

// Routes returns all of the api route for the SharesApiController
func (c *PublicSharesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostSharesValidate",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostSharesValidate,
		},
	}
}

func (c *PublicSharesApiController) GetPathPrefix() string {
	return "/v1/shares/validate"
}

func (c *PublicSharesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//none
	return []func(next http.Handler) http.Handler{}
}

// PostSharesValidate - Validate share viewer
func (c *PublicSharesApiController) PostSharesValidate(w http.ResponseWriter, r *http.Request) {
	shareCredentials := &coreapi.ShareCredentials{}
	if err := json.NewDecoder(r.Body).Decode(&shareCredentials); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	shareId := shareCredentials.ShareId
	pin := shareCredentials.Pin
	viewcode := shareCredentials.Viewcode
	dob := shareCredentials.Dob
	lastName := shareCredentials.LastName
	var token string
	var err error

	idOrViewcodeOrLastName := ""
	if shareId != "" && pin != "" {
		idOrViewcodeOrLastName = shareId
	} else if viewcode != "" && dob != "" {
		idOrViewcodeOrLastName = viewcode
	} else if lastName != "" {
		token = r.Header.Get("Authorization")
		shareId, _, err = auth.DecodeShareViewerToken(token)
		if err != nil || auth.IsInBlacklist(r.Context(), token) {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}
		idOrViewcodeOrLastName = shareId
	}

	//one pair of (shareId, pin) OR (viewcode, dob) needs to be populated
	if idOrViewcodeOrLastName == "" {
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_INVALID_REQ_BODY+": shareId and pin or viewcode and dob or lastName must be populated on request",
			http.StatusBadRequest,
		)
		return
	}

	if c.lt.IsLocked(r.Context(), idOrViewcodeOrLastName) {
		httperror.ErrorWithLog(w, r, errmsg.ERR_TOO_MANY_ATTEMPTS, http.StatusForbidden)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]
	result, err := c.service.PostSharesValidate(
		r.Context(),
		shareId,
		pin,
		viewcode,
		dob,
		ip,
		lastName,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_BAD_CREDENTIALS {
			c.lt.IncrementAttempts(r.Context(), idOrViewcodeOrLastName)
			httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_CREDENTIALS, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_LINK_EXPIRED {
			var statusCode int = http.StatusGone
			coreapi.EncodeJSONResponse(r.Context(), result, &statusCode, w)
			c.lt.Reset(r.Context(), idOrViewcodeOrLastName)
		} else if err.Error() == errmsg.ERR_SHARE_ID_NULL {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusNotFound)
		} else if err.Error() == errmsg.ERR_SHARE_REVOKED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusForbidden)
		} else {
			logutils.CtxLogger(r.Context()).WithError(err).Error("unknown share validate error")
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	// Black list the token if it's the token + lastName validation
	if lastName != "" && token != "" {
		err := auth.AddToBlacklist(r.Context(), token)
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).
				WithError(err).
				Warn("failed to blacklist share token")
		}
	}
	go c.lt.Reset(bgctx.GetBGCtxWithCorrelation(r.Context()), idOrViewcodeOrLastName)

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)

}
