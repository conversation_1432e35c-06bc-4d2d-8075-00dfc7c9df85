package shares

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/util/testutil"
	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"
	"gitlab.com/pockethealth/phutils/v2/pkg/httpclient"
)

// when you test locally this ends up being what clientAddr[0] looks like
const (
	localClaimIp = ""
)

func TestValidateViewer(t *testing.T) {
	//mock validate service function
	mockValidate := func(shareId string, pin string, viewcode string, dob string, ip string, lastName string) (interface{}, error) {
		token, err := auth.MakeShareAuthToken(shareId, localClaimIp)
		if err != nil {
			return nil, err
		}
		return token, nil
	}

	service := NewMockSharesApiService(nil, nil, mockValidate, nil)

	controller := NewPublicSharesApiController(service, lockouttracker.NewMockLockoutTracker())
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}
	// happy cases
	t.Run("id and pin", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(
			`	{
					"shareId": "thisismyid",
					"pin": "1234"
				}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		//should return a token
		body, err := ioutil.ReadAll(rr.Result().Body)
		if string(body) == "" {
			t.Errorf("no token returned")
		}
	})

	t.Run("code and dob", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(
			`	{
				"viewcode": "thisismycode",
				"dob": "2004/12/23"
			}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		//should return a token
		body, err := ioutil.ReadAll(rr.Result().Body)
		if string(body) == "" {
			t.Errorf("no token returned")
		}
	})

	//unhappy cases
	t.Run("shareId only", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(`	{
			"shareId": "thisismyid",
		}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("shareId and dob", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(
			`	{
				"shareId": "thisismyid",
				"dob": "2004/12/23"
			}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestExpiredShareViewer(t *testing.T) {
	//mock validate service function
	mockValidate := func(shareId string, pin string, viewcode string, dob string, ip string, lastName string) (interface{}, error) {
		return nil, errors.New(errmsg.ERR_LINK_EXPIRED)
	}

	service := NewMockSharesApiService(nil, nil, mockValidate, nil)

	controller := NewPublicSharesApiController(service, lockouttracker.NewMockLockoutTracker())
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}
	t.Run("link expired", func(t *testing.T) {
		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(
			`	{
				"shareId": "thisismyid",
				"pin": "1234"
			}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusGone
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

	})

}

func TestPostShare(t *testing.T) {
	//mock create share service function
	mockShare := func(acctId string, share coreapi.Share) (interface{}, error) {
		return NewShareResult{
			ID:  "abc123",
			PDF: []byte("These are some bytes"),
		}, nil
	}

	service := NewMockSharesApiService(nil, nil, nil, mockShare)

	controller := NewPrivateSharesApiController(service)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}
	//share validity is unit tested in model_share_test.go so not testing it here
	exampleShare := coreapi.Share{
		Method:   coreapi.ACCESS_PAGE_PRINT,
		Mode:     coreapi.MULTIPLE,
		ExamList: []coreapi.Exam{{}},
	}

	//if print share, expect a pdf body to be returned in the response
	t.Run("print share", func(t *testing.T) {
		jsonShare, err := json.Marshal(exampleShare)
		req, err := http.NewRequest("POST", "/v1/shares", bytes.NewBuffer(jsonShare))
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != "application/pdf" {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				"application/pdf",
			)
		}

		//should return a body
		body, err := ioutil.ReadAll(rr.Result().Body)
		if string(body) == "" {
			t.Errorf("no body returned")
		}

	})

	//if fax or email share, don't expect response body
	t.Run("email share", func(t *testing.T) {
		exampleShare.Method = coreapi.EMAIL
		exampleShare.Recipient = "thisis@email"
		jsonShare, err := json.Marshal(exampleShare)
		req, err := http.NewRequest("POST", "/v1/shares", bytes.NewBuffer(jsonShare))
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		//should not return a body
		body, err := ioutil.ReadAll(rr.Result().Body)
		if string(body) != "" {
			t.Errorf("body returned")
		}

	})

	//if fax, expect pdf in response
	t.Run("fax share", func(t *testing.T) {
		exampleShare.Method = coreapi.ACCESS_PAGE_FAX
		exampleShare.Recipient = "**********"
		jsonShare, err := json.Marshal(exampleShare)
		req, err := http.NewRequest("POST", "/v1/shares", bytes.NewBuffer(jsonShare))
		if err != nil {
			t.Fatal(err)
		}
		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		//should return a body
		body, err := ioutil.ReadAll(rr.Result().Body)
		if string(body) == "" {
			t.Errorf("no body returned")
		}

	})
}

func TestShareFailedEmail(t *testing.T) {

	client, mockapi := cioEmail.NewMock()
	mailer := cio_email.ConfigureMail(client, map[string]string{"share_failed": "72", "share_email_failed": "71", "share_fax_failed": "73"})

	methods := []coreapi.ShareMethod{coreapi.EMAIL, coreapi.ACCESS_PAGE_FAX, coreapi.ACCESS_PAGE_PRINT}
	for _, m := range methods {
		t.Run("email share", func(t *testing.T) {
			mockapi.Reset()
			expectedSharerEmail := "sharerEmailAddress"
			err := sendShareFailedEmail(
				context.Background(),
				mailer,
				expectedSharerEmail,
				"accountId",
				m,
				"sharee",
			)
			require.NoError(t, err)
			msg, err := cioEmail.GetCIOMessageFromCallLog(mockapi.CallLogs.Get("SendEmail").History[0])
			require.NoError(t, err)
			require.Equal(t, expectedSharerEmail, msg.To)
		})
	}
}

func TestGetShares(t *testing.T) {
	//mock get shares service function
	mockGetShares := func(acctId string, limit int, offset int) (interface{}, error) {
		return []coreapi.Share{}, nil
	}

	service := NewMockSharesApiService(nil, mockGetShares, nil, nil)

	controller := NewPrivateSharesApiController(service)
	router, err := coreapi.NewRouter(controller)
	require.NoError(t, err)

	//only a user token is valid to list shares
	t.Run("use share token", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/shares", nil)
		require.NoError(t, err)

		token, _ := auth.MakeShareAuthToken("thisisashareid", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

	})

	//test validating limit and offset
	t.Run("no limit/offset", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/shares", nil)
		require.NoError(t, err)

		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("bad offset", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/shares?offset=-1", nil)
		require.NoError(t, err)

		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("bad limit", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/shares?limit=-1", nil)
		require.NoError(t, err)

		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("good limit and offset", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/shares?limit=10&offset=10", nil)
		require.NoError(t, err)

		token := auth.MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestExtendedShareViewer(t *testing.T) {
	auth.InitializeBlacklist(nil)
	testShareId := "myshareid"
	ip := ""
	//mock validate service function
	mockValidate := func(shareId string, pin string, viewcode string, dob string, ip string, lastName string) (interface{}, error) {
		if shareId != testShareId || lastName != "examplelastname" {
			return nil, errors.New("service method called with wrong inputs")
		}
		return nil, nil
	}
	service := NewMockSharesApiService(nil, nil, mockValidate, nil)

	controller := NewPublicSharesApiController(service, lockouttracker.NewMockLockoutTracker())
	router, err := coreapi.NewRouter(controller)
	require.NoError(t, err)

	t.Run("last name validation", func(t *testing.T) {

		//create tmp share token (ie, as if first round validation is done)
		token, _ := auth.MakeShareAuthToken(testShareId, ip)
		token = "Bearer " + token

		req, err := http.NewRequest("POST", "/v1/shares/validate", strings.NewReader(
			`	{
				"lastname": "examplelastname"
			}`))
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", token)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)
		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf(
				"handler returned wrong status code: got %v want %v. Response: %s",
				status,
				want,
				rr.Body,
			)
		}

		//token should be blacklisted
		if !auth.IsInBlacklist(req.Context(), token) {
			t.Errorf("token should have been blacklisted after authentication")
		}

	})

}

func TestAddViewcodeRR(t *testing.T) {
	testViewcode := "123456789"
	testFormattedViewcode := "123-456-789"
	testDOB := "19990614"
	viewcodeDOB := testViewcode + testDOB
	hash := sha256.New()
	_, err := io.WriteString(hash, viewcodeDOB)
	if err != nil {
		t.Fatal("error hashing viewcode+DOB")
	}
	testViewcodeDOBHash := base64.URLEncoding.EncodeToString(hash.Sum(nil))

	mockGenerateSecurityCode := func() (string, string, error) {
		og := "123456789"
		formatted := "123-456-789"
		return og, formatted, nil
	}
	ctx := context.Background()

	t.Run(
		"AddViewCodeRR returns without err when unique hash of viewcode+DOB in RR",
		func(t *testing.T) {
			mockRRClient := testutil.MockRRClient{
				ExpectedIDType: int16(1),
				ExpectedID:     testViewcodeDOBHash,
			}

			viewcode, formattedViewcode, err := addViewcodeRR(
				ctx,
				testDOB,
				mockRRClient,
				mockGenerateSecurityCode,
			)
			if err != nil {
				t.Fatal("unexpected test error:", err)
			}

			if viewcode != testViewcode {
				t.Fatalf("expected viewcode to be %s, got %s", testViewcode, viewcode)
			}

			if formattedViewcode != testFormattedViewcode {
				t.Fatalf(
					"expected formattedViewCode to be %s, got %s",
					testFormattedViewcode,
					formattedViewcode,
				)
			}
		},
	)

	t.Run("AddViewCodeRR reformats DOB before hashing and adding to RR", func(t *testing.T) {
		DOB := "1994/03/20"
		hash = sha256.New()
		_, err = io.WriteString(hash, testViewcode+"19940320")
		if err != nil {
			t.Fatal("error hashing viewcode+DOB")
		}
		testViewcodeDOBHash = base64.URLEncoding.EncodeToString(hash.Sum(nil))

		mockRRClient := testutil.MockRRClient{
			ExpectedIDType: int16(1),
			ExpectedID:     testViewcodeDOBHash,
		}

		// function recieves DOB with '/'
		viewcode, formattedViewcode, err := addViewcodeRR(
			ctx,
			DOB,
			mockRRClient,
			mockGenerateSecurityCode,
		)
		if err != nil {
			t.Fatal("unexpected test error:", err)
		}

		if viewcode != testViewcode {
			t.Fatalf("expected viewcode to be %s, got %s", testViewcode, viewcode)
		}

		if formattedViewcode != testFormattedViewcode {
			t.Fatalf(
				"expected formattedViewCode to be %s, got %s",
				testFormattedViewcode,
				formattedViewcode,
			)
		}
	})

	// when duplicate hash at RR, AddViewCodeRR should still return a generated viewcode
	t.Run(
		"AddViewCodeRR returns non-empty viewcode & err when duplicate viewcode in RR",
		func(t *testing.T) {
			mockRRClient := testutil.MockRRClient{
				ExpectedIDType: int16(1),
				ExpectedID:     testViewcodeDOBHash,
				ExpectError:    true,

				MockError: httpclient.ErrRequestConflict,
			}

			viewcode, formattedViewcode, err := addViewcodeRR(
				ctx,
				testDOB,
				mockRRClient,
				mockGenerateSecurityCode,
			)
			if err == nil {
				t.Fatal("expected error from regionrouter client got nil")
			}

			if viewcode != testViewcode {
				t.Fatalf("expected viewcode to be %s, got %s", testViewcode, viewcode)
			}

			if formattedViewcode != testFormattedViewcode {
				t.Fatalf(
					"expected formattedViewCode to be %s, got %s",
					testFormattedViewcode,
					formattedViewcode,
				)
			}
		},
	)

}
