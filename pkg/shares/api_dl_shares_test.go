package shares

import (
	"net/http"
	"net/http/httptest"
	"testing"

	mockrecordstreaming "gitlab.com/pockethealth/coreapi/generated/mocks/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func TestGetSharesAuth(t *testing.T) {
	service := NewMockSharesApiService(nil, nil, nil, nil)
	recordStreamingService := mockrecordstreaming.NewMockRecordStreamingServicer(t)

	controller := NewDLSharesApiController(service, recordStreamingService)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// happy cases
	t.Run("user token in auth header", func(t *testing.T) {
		token := auth.MakeAccountAuthToken("23ZIHzTbIX9TNHdGaR4ZYU9P15V", "************")

		req, err := http.NewRequest("GET", "/v1/shares/12345", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("share token in auth header", func(t *testing.T) {
		token, err := auth.MakeShareAuthToken("12345", "************")
		if err != nil {
			t.Fatalf("unexpected error creating share token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("dl token in query param, type = zip", func(t *testing.T) {
		token, err := auth.MakeShareDLToken("12345")
		if err != nil {
			t.Fatalf("unexpected error creating dl token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/zip")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	// disallowed cases
	t.Run("dl token in query param, type = json", func(t *testing.T) {
		token, err := auth.MakeShareDLToken("12345")
		if err != nil {
			t.Fatalf("unexpected error creating dl token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/json")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("user token in query param, type = zip", func(t *testing.T) {
		token := auth.MakeAccountAuthToken("23ZIHzTbIX9TNHdGaR4ZYU9P15V", "*************")

		req, err := http.NewRequest("GET", "/v1/shares/12345?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/zip")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("share token in query param, type = zip", func(t *testing.T) {
		token, err := auth.MakeShareAuthToken("12345", "*************")
		if err != nil {
			t.Fatalf("unexpected error creating dl token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/zip")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestGetHealthRecordSharesAuth(t *testing.T) {
	service := NewMockSharesApiService(nil, nil, nil, nil)
	recordStreamingService := mockrecordstreaming.NewMockRecordStreamingServicer(t)

	controller := NewDLSharesApiController(service, recordStreamingService)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// happy cases
	t.Run("user token in auth header", func(t *testing.T) {
		token := auth.MakeAccountAuthToken("23ZIHzTbIX9TNHdGaR4ZYU9P15V", "************")

		req, err := http.NewRequest("GET", "/v1/shares/12345/healthrecords", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Authorization", "Bearer "+token)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("dl token in query param", func(t *testing.T) {
		token, err := auth.MakeShareDLToken("12345")
		if err != nil {
			t.Fatalf("unexpected error creating dl token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345/healthrecords?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	// disallowed cases
	t.Run("user token in query param, type = zip", func(t *testing.T) {
		token := auth.MakeAccountAuthToken("23ZIHzTbIX9TNHdGaR4ZYU9P15V", "*************")

		req, err := http.NewRequest("GET", "/v1/shares/12345/healthrecords?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/zip")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("share token in query param, type = zip", func(t *testing.T) {
		token, err := auth.MakeShareAuthToken("12345", "*************")
		if err != nil {
			t.Fatalf("unexpected error creating dl token: %v", err)
		}

		req, err := http.NewRequest("GET", "/v1/shares/12345/healthrecords?token="+token, nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Accept", "application/zip")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}
