/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package shares

import (
	"context"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A DLSharesApiController binds http requests to an api service and writes the service results to the http response
type DLSharesApiController struct {
	service                coreapi.SharesApiServicer
	recordStreamingService recordstreaming.RecordStreamingServicer
}

// NewDLSharesApiController creates a default api controller
func NewDLSharesApiController(
	s coreapi.SharesApiServicer,
	recordStreamingServicer recordstreaming.RecordStreamingServicer,
) coreapi.DLSharesApiRouter {
	return &DLSharesApiController{
		service:                s,
		recordStreamingService: recordStreamingServicer,
	}
}

// Routes returns all of the api route for the SharesApiController
func (c *DLSharesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetSharesById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{shareId}",
			HandlerFunc: c.GetSharesById,
		},
		{
			Name:        "GetShareHealthRecordsById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{shareId}/healthrecords",
			HandlerFunc: c.GetShareHealthRecordsById,
		},
	}
}

func (c *DLSharesApiController) GetPathPrefix() string {
	return "/v1/shares"
}

func (c *DLSharesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{ValidateAuth}
}

// custom auth middleware for this route - can't use headers for downloads,
// so allowing the token to be in a query param. Since this route has one non-download route
// though, allow it to continue using the regular authentication.
func ValidateAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		_, _, token, err := getTokenClaims(r)
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
				"reason": "couldn't decode token",
			}).Error("Get share unauthorized")
			httperror.ErrorWithLog(w, r, "Not authorized", http.StatusUnauthorized)
			return
		} else if auth.IsInBlacklist(r.Context(), token) || auth.IsInBlacklist(r.Context(), "Bearer "+token) {
			logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
				"reason": "token is blacklisted",
			}).Error("Get share unauthorized")
			httperror.ErrorWithLog(w, r, "Not authorized", http.StatusUnauthorized)
			return
		}
		// Pass down the request to the next middleware (or final handler)
		next.ServeHTTP(w, r)
	})
}

func getTokenClaims(
	r *http.Request,
) (claims auth.Claims, isQueryToken bool, token string, err error) {
	token = r.Header.Get("Authorization")
	if token != "" {
		claims, err := auth.DecodeBearerToken(token)
		return claims, false, token, err
	} else {
		//check for token in query param
		//expect token=sometoken (ie, no "bearer")
		query := r.URL.Query()
		token = query.Get("token")
		claims, err := auth.DecodeToken(token)
		return claims, true, token, err
	}
}

func getFromQueryOrHeader(param string, r *http.Request) string {
	val := r.URL.Query().Get(param)
	if val == "" {
		val = r.Header.Get(param)
	}
	return val
}

func validateOptionalQueryShareAuth(
	r *http.Request,
	shareId string,
	accept string,
) (auth.Claims, bool) {
	var err error

	claims, isQueryToken, _, err := getTokenClaims(r)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
			"reason": "could not decode token",
		}).Error("Get share unauthorized")
		return claims, false
	}

	//can authenticate this endpoint via auth header OR query param.
	if isQueryToken {
		if !claims.DL {
			logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
				"reason": "not download token",
			}).Error("Get share unauthorized")
			return claims, false
		}
		//query param auth should only be used for downloads, ie, accept is iso or zip
		if accept != "application/zip" && accept != "application/octet-stream" {
			logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
				"reason": "using a dl token for json",
			}).Error("Get share unauthorized")
			return claims, false
		}
	}

	//verify that the share Id in the token is the same as the shareId in the url,
	//or if it's a user token
	if claims.ShareID == "" && claims.AccountID == "" {
		logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
			"reason": "invalid token type",
		}).Error("Get share unauthorized")
		return claims, false
	} else if claims.ShareID != "" && shareId != claims.ShareID {
		logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
			"reason": "share token has incorrect shareID",
		}).Error("Get share unauthorized")
		return claims, false
	}
	return claims, true
}

// GetSharesById - Get share files
func (c *DLSharesApiController) GetSharesById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareID := params["shareId"]
	includeViewer, err := strconv.ParseBool(getFromQueryOrHeader("viewer", r))
	if err != nil {
		includeViewer = false
	}
	// optional query parameter provider id for getting shares for record streaming studies
	hasProviderID, providerID, _ := coreapi.ParseQueryParamInt64(r, "providerId")

	accept := getFromQueryOrHeader("accept", r)
	if accept != "application/zip" && accept != "application/octet-stream" &&
		accept != "application/json" {
		accept = "application/json"
	}
	claims, valid := validateOptionalQueryShareAuth(r, shareID, accept)
	if !valid {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	accountID := claims.AccountID
	ip := claims.IP

	hasRecordStreamingAccess := false
	// check if this is valid attempt by physician to access record streaming study
	if hasProviderID {
		hasRecordStreamingAccess, err = c.recordStreamingService.PhysicianCanAccessStudy(
			r.Context(),
			accountID,
			shareID,
			providerID,
		)
		if err != nil {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusInternalServerError),
				http.StatusInternalServerError,
			)
			return
		}
	}
	if hasRecordStreamingAccess {
		// execute record streaming workflow for physician
		c.handleGetRecordStreamingStudyByID(
			w,
			r,
			providerID,
			accountID,
			shareID,
			ip,
			accept,
			includeViewer,
		)
	} else {
		// Otherwise, execute traditional share logic
		c.handleGetSharesById(w, r, accountID, ip, shareID, accept, includeViewer)
	}
}

// GetShareHealthRecordsById - Get share health record files
func (c *DLSharesApiController) GetShareHealthRecordsById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	shareId := params["shareId"]

	claims, valid := validateOptionalQueryShareAuth(r, shareId, "application/zip")
	if !valid {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	hrs := make([]string, 0)
	query := r.URL.Query()
	idListStr := query.Get("hrids") //optional parameter for list of HR ids
	if idListStr != "" {
		hrs = strings.Split(idListStr, ",")
	}
	//else assume all health records included

	//Limiting to 30 ids due to character limit on query parameters.
	if len(hrs) > 30 {
		http.Error(w, "can only select up to 30 records, or all records", http.StatusBadRequest)
		return
	}

	result, lastName, err := c.service.GetShareHealthRecordsById(
		r.Context(),
		claims.AccountID,
		shareId,
		hrs,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	setDownloadHeaders(w, "application/zip", "HEALTHRECORDS_"+lastName+".zip", int64(len(result)))
	_, err = w.Write(result)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).WithError(err).Error("couldn't write zip result")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
}

func writeDownloadResponse(
	ctx context.Context,
	contentType string,
	result interface{},
	w http.ResponseWriter,
) {
	dl, ok := result.(Download)
	if !ok {
		logutils.DebugCtxLogger(ctx).Error("no download object returned")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	stats, err := dl.file.Stat()
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't get file stat to set headers")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}
	setDownloadHeaders(w, contentType, dl.filename, stats.Size())

	_, err = io.Copy(w, dl.file)
	if err != nil {
		if strings.Contains(err.Error(), "connection reset by peer") ||
			strings.Contains(err.Error(), "broken pipe") {
			//client closed the connection, don't bother writing response.
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
	}
}

func setDownloadHeaders(w http.ResponseWriter, contentType string, filename string, size int64) {
	w.Header().Add("Content-Type", contentType)
	w.Header().Set("Content-Length", strconv.FormatInt(size, 10))
	w.Header().Set("Content-Disposition", "attachment; filename="+filename)
	w.Header().Set("Strict-Transport-Security", "max-age=********; preload")
	w.WriteHeader(http.StatusOK)
}

func (c *DLSharesApiController) handleGetSharesById(
	w http.ResponseWriter,
	r *http.Request,
	accountID string,
	ip string,
	shareID string,
	accept string,
	includeViewer bool,
) {
	examIds := make([]string, 0)
	query := r.URL.Query()
	idListStr := query.Get("eids") //optional parameter for list of exam ids
	if idListStr != "" {
		//eids query param should be a list of comma separated exam ids.
		examIds = strings.Split(idListStr, ",")
	}
	//else assume all exams included

	//Limiting to 30 ids due to character limit on query parameters.
	if len(examIds) > 30 {
		http.Error(w, "can only select up to 30 records, or all records", http.StatusBadRequest)
		return
	}

	result, err := c.service.GetSharesById(
		r.Context(),
		accountID,
		shareID,
		accept,
		examIds,
		ip,
		includeViewer,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	if accept == "application/json" {
		coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
	} else {
		writeDownloadResponse(r.Context(), accept, result, w)
	}
}

func (c *DLSharesApiController) handleGetSharesByIdForRecordStreaming(
	w http.ResponseWriter,
	r *http.Request,
	accountID string,
	shareID string,
	providerID int64,
) {
	result, err := c.recordStreamingService.GetShareForRecordStreamingStudiesForPhysician(
		r.Context(),
		accountID,
		shareID,
		providerID,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *DLSharesApiController) handleDownloadStudyForRecordStreaming(
	w http.ResponseWriter,
	r *http.Request,
	providerID int64,
	physicianAccountID string,
	studyUID string,
	format string,
	ip string,
	includeViewer bool,
) {
	result, err := c.service.DownloadRecordStreamingStudy(
		r.Context(),
		providerID,
		physicianAccountID,
		studyUID,
		format,
		ip,
		includeViewer,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	if format == "application/json" {
		coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
	} else {
		writeDownloadResponse(r.Context(), format, result, w)
	}
}

func (c *DLSharesApiController) handleGetRecordStreamingStudyByID(
	w http.ResponseWriter,
	r *http.Request,
	providerID int64,
	physicianAccountID string,
	studyUID string,
	ip string,
	accept string,
	includeViewer bool,
) bool {
	lg := logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
		"account_id":  physicianAccountID,
		"study_uid":   studyUID,
		"provider_id": providerID,
	})

	switch accept {
	case "application/json":
		// get share metadata for record streaming streaming study
		c.handleGetSharesByIdForRecordStreaming(w, r, physicianAccountID, studyUID, providerID)
		return true
	case "application/zip", "application/octet-stream":
		// download share blob for record streaming study
		c.handleDownloadStudyForRecordStreaming(
			w,
			r,
			providerID,
			physicianAccountID,
			studyUID,
			accept,
			ip,
			includeViewer,
		)
		return true
	default:
		lg.Errorf("unknown accept type: %s", accept)
		return false
	}
}
