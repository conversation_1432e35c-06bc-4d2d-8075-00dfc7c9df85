/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package shares

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/download"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	"gitlab.com/pockethealth/coreapi/pkg/recordstreaming"

	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	eunitytokens "gitlab.com/pockethealth/coreapi/pkg/mysql/eunityTokens"
	sqlPhysicians "gitlab.com/pockethealth/coreapi/pkg/mysql/physicians"
	sqlStudyPermissions "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	provider "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	sqlHandler "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	scans "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	sqlShareObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	sqlShares "gitlab.com/pockethealth/coreapi/pkg/mysql/shares"
	"gitlab.com/pockethealth/coreapi/pkg/pdfs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// SharesApiService is a service that implents the logic for the SharesApiServicer
// This service should implement the business logic for every endpoint for the SharesApi API.
// Include any external packages or services that will be required by this service.
type SharesApiService struct {
	sqldb                  *sql.DB
	containerClient        azureUtils.ContainerClient
	faxServiceUser         fax.FaxSvcUser
	frontEndHost           string
	waitGroup              *sync.WaitGroup
	i18nBundle             *i18n.Bundle
	languageTagProviders   languageproviders.LanguageTagProviders
	supportedLanguages     map[string]string
	regionrouterClient     regions.RegionRouterClient
	acctSvcClient          accountservice.AccountService
	hlthRecSvcUser         hrs.HlthRecSvcUser
	orgSvcClient           orgsvc.OrgService
	excludeDeletedShares   bool
	experimentsClient      interfaces.AmplitudeExperimentClient
	ampliClient            interfaces.AmplitudeEventClient
	downloadPrepareFunc    download.DownloadPrepare
	mailer                 cio_email.CIOEMailer
	examService            exams.ExamServiceInterface
	recordstreamingService recordstreaming.RecordStreamingServicer
}

type Download struct {
	file     *os.File
	filename string
}

// NewSharesApiService creates a default api service
func NewSharesApiService(
	db *sql.DB,
	cc azureUtils.ContainerClient,
	faxServiceUser fax.FaxSvcUser,
	frontHost string,
	waitGroup *sync.WaitGroup,
	i18nBundle *i18n.Bundle,
	langProviders languageproviders.LanguageTagProviders,
	supportedLanguages map[string]string,
	rrClient regions.RegionRouterClient,
	hrServiceUser hrs.HlthRecSvcUser,
	as accountservice.AccountService,
	orgSvc orgsvc.OrgService,
	excludeDeletedShares bool,
	experimentsClient interfaces.AmplitudeExperimentClient,
	amplitudeClient interfaces.AmplitudeEventClient,
	downloadPrepareFunc download.DownloadPrepare,
	ciomail cio_email.CIOEMailer,
	examService exams.ExamServiceInterface,
	recordstreamingService recordstreaming.RecordStreamingServicer,
) coreapi.SharesApiServicer {
	return &SharesApiService{
		sqldb:                  db,
		containerClient:        cc,
		faxServiceUser:         faxServiceUser,
		frontEndHost:           frontHost,
		waitGroup:              waitGroup,
		i18nBundle:             i18nBundle,
		languageTagProviders:   langProviders,
		supportedLanguages:     supportedLanguages,
		regionrouterClient:     rrClient,
		hlthRecSvcUser:         hrServiceUser,
		acctSvcClient:          as,
		orgSvcClient:           orgSvc,
		excludeDeletedShares:   excludeDeletedShares,
		experimentsClient:      experimentsClient,
		ampliClient:            amplitudeClient,
		downloadPrepareFunc:    downloadPrepareFunc,
		mailer:                 ciomail,
		examService:            examService,
		recordstreamingService: recordstreamingService,
	}
}

// GetShares - Get shares for a user
func (s *SharesApiService) GetShares(
	ctx context.Context,
	acctId string,
	limit int,
	offset int,
) (interface{}, error) {

	// get shares directly from sql using acct id
	baseShareInfoList, err := sqlShares.GetSharesBaseDataByAccountPaginated(
		ctx,
		s.sqldb,
		acctId,
		limit,
		offset,
		s.excludeDeletedShares,
	)
	if err != nil {
		return nil, err
	}

	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", acctId)
	var shareList = make([]coreapi.Share, 0)
	for _, shareBase := range baseShareInfoList {
		var hrPatient coreapi.HRPatient
		// Include deleted metadata records
		includeDeleted := true
		hrs := s.hlthRecSvcUser.GetRecordMetadataByIds(
			ctx,
			true,
			shareBase.HRIdList,
			includeDeleted,
		)
		if len(hrs) > 0 {
			pt, err := s.acctSvcClient.GetPatient(ctx, acctId, hrs[0].PatientId)
			if err != nil {
				lg.WithField("patient_id", hrs[0].PatientId).
					WithError(err).
					Error("could not retrieve patient info")

				//don't fail, just return empty pt.
			} else {
				hrPatient = coreapi.HRPatient{FirstAndMiddleNames: pt.FirstName, LastName: pt.LastName, AccountId: pt.AccountId, PhPatientId: pt.PatientId}
			}
		}

		cde, err := sqlShares.ContainsDeletedExams(ctx, s.sqldb, shareBase.ShareId)
		if err != nil {
			lg.WithError(err).
				Errorf("could not check whether share `%s` contains a deleted exam or not", shareBase.ShareId)

			// don't error out; this will mean the client will have `false` returned
		}

		method, expiry, active, err := getShareMethodExpiryAndActivation(shareBase)
		if err == nil && method != coreapi.ZIP { //don't include offline shares
			shareList = append(
				shareList,
				coreapi.Share{
					ShareId:             shareBase.ShareId,
					Status:              shareBase.FaxStatus,
					ExamList:            nil,
					Recipient:           shareBase.Recipient,
					Method:              method,
					Mode:                "",
					Expiry:              expiry,
					Active:              active,
					Date:                shareBase.Date[0:10],
					EUnityToken:         "",
					CCUser:              false,
					ExtendedExpiry:      shareBase.ExtendedExpiry,
					HealthRecords:       hrs,
					HRPatient:           hrPatient,
					ContainsDeletedExam: cde,
				},
			)
		}
	}

	return shareList, nil
}

func (s *SharesApiService) getJsonShare(
	ctx context.Context,
	userAccess bool,
	acctId string,
	shareId string,
	includeDeleted bool,
) (coreapi.Share, error) {
	var share coreapi.Share
	acctFromShare, baseShareInfoList, err := sqlShares.GetSharesBaseDataByShareIds(
		ctx,
		s.sqldb,
		[]interface{}{shareId},
	)
	if err != nil {
		return coreapi.Share{}, err
	}

	if len(baseShareInfoList) == 0 {
		return coreapi.Share{}, errors.New(errmsg.ERR_NOT_FOUND)
	}
	baseShare := baseShareInfoList[0]
	//create share object and return with eunity token
	method, expiry, active, err := getShareMethodExpiryAndActivation(baseShare)
	if err != nil {
		return coreapi.Share{}, err
	}

	lg := logutils.DebugCtxLogger(ctx)

	// get all exam ids in the share
	shareExamIds, err := s.examService.GetExamUUIDListForShare(ctx, shareId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve share exam ids")
		return coreapi.Share{}, err
	}

	cde, err := sqlShares.ContainsDeletedExams(ctx, s.sqldb, shareId)
	if err != nil {
		lg.WithError(err).Error("could not check whether share contains a deleted exam or not")
		// don't error out; this will mean the client will have `false` returned
	}

	var exams []coreapi.Exam
	var eunityToken string
	if len(shareExamIds) > 0 {
		q, err := queries.NewWhere(map[string][]any{"e.activated": {true}})
		if err != nil {
			return coreapi.Share{}, err
		}
		rawExams, err := s.examService.GetExamSummaryData(
			ctx,
			acctId,
			shareExamIds,
			q,
			true,
			s.excludeDeletedShares,
		)
		exams = coreapi.RawToExams(ctx, rawExams)
		if err != nil || len(exams) <= 0 {
			lg.WithError(err).Error("could not retrieve share exam json")
			return coreapi.Share{}, err
		}
		//generate eUnity token
		eunityToken, err = eunitytokens.Create(ctx, s.sqldb, shareId)
		if err != nil {
			return coreapi.Share{}, err
		}
	}

	//get HRs details
	hrList := s.hlthRecSvcUser.GetRecordMetadataByIds(
		ctx,
		userAccess,
		baseShare.HRIdList,
		includeDeleted,
	)
	if len(hrList) != len(baseShare.HRIdList) {
		lg.WithFields(logrus.Fields{
			"num_hr_ids": len(baseShare.HRIdList),
			"num_hrs":    len(hrList),
		}).Error("did not retrieve all hrs in share")
	}
	var hrPatient coreapi.HRPatient
	if len(hrList) > 0 {
		pt, err := s.acctSvcClient.GetPatient(ctx, acctFromShare, hrList[0].PatientId)
		if err != nil {
			lg.WithField("patient_id", hrList[0].PatientId).WithField("account_id", acctFromShare).
				WithError(err).
				Error("could not retrieve patient info")

			//don't fail, just return empty pt.
		} else {
			hrPatient = coreapi.HRPatient{FirstAndMiddleNames: pt.FirstName, LastName: pt.LastName, PhPatientId: pt.PatientId, AccountId: pt.AccountId}
		}
	}

	shareInitiator, shareType, err := getShareSenderInfo(
		ctx,
		s.sqldb,
		baseShare.ShareId,
		s.orgSvcClient,
	)
	if err != nil {
		lg.WithError(err).Error("failed to get share sender info")
	}
	share = coreapi.Share{
		ShareId:             baseShare.ShareId,
		Status:              "",
		ExamList:            exams,
		Recipient:           baseShare.Recipient,
		Method:              method,
		Mode:                "",
		Expiry:              expiry,
		Active:              active,
		Date:                baseShare.Date[0:10],
		EUnityToken:         eunityToken,
		CCUser:              false,
		ExtendedExpiry:      baseShare.ExtendedExpiry,
		HealthRecords:       hrList,
		HRPatient:           hrPatient,
		ContainsDeletedExam: cde,
		ShareInitiator:      shareInitiator,
		ShareType:           shareType,
	}
	return share, nil
}

func (s *SharesApiService) getObjectIdsFromShare(
	ctx context.Context,
	shareId string,
	shareAccountID string,
	acctId string,
	selectedExamIds []string,
) (objectIds []string, err error) {
	// if selected specific exams/reports, get object ids for the selected items
	if len(selectedExamIds) != 0 && selectedExamIds[0] != "" {
		if shareAccountID == "" {
			// no share accountID means it did not originate from a patient
			objectIds, err = sqlExams.GetObjectIdsByExamUUIDs(
				ctx,
				s.sqldb,
				acctId,
				selectedExamIds,
				true,
			)
		} else {
			// shares originating from a patient should have patient-viewer access rules applied
			// via record service for the share recipient
			objectIds, err = s.examService.GetObjectIDsByExamUUIDs(
				ctx,
				shareAccountID,
				selectedExamIds,
				true,
			)
		}
		if err != nil {
			return nil, err
		}

		//verify that all the selected object ids are part of the share
		shareObjectIds, err := sqlShareObjects.GetShareObjectIdsAsMap(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}
		var selectedObjectIds []string

		for _, selectedId := range objectIds {
			if _, ok := shareObjectIds[selectedId]; ok {
				selectedObjectIds = append(selectedObjectIds, selectedId)
			}
		}
		objectIds = selectedObjectIds
	} else {
		// otherwise, get all objects within a share
		objectIds, err = sqlShareObjects.GetShareObjectIds(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}
	}

	return objectIds, nil
}

func (s *SharesApiService) handleShareDownload(
	ctx context.Context,
	shareId string,
	objectIds []string,
	userName string,
	ip string,
	format string,
	includeViewer bool,
	isBasicPlanAccount bool,
	acctId string,
) (Download, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("share_id", shareId)

	path := "vault/tmp/prep/" + ip + shareId
	if filepath.Dir(path) != "vault/tmp/prep" {
		logrus.WithFields(logrus.Fields{
			"shareId": shareId,
			"reason":  "path traversal detected",
			"path":    path,
		}).Error("Get share unauthorized")
		return Download{}, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}
	err := s.downloadPrepareFunc(
		ctx,
		path,
		objectIds,
		true,
		format,
		ip+shareId,
		s.containerClient,
		s.experimentsClient,
		s.ampliClient,
		userName,
		includeViewer,
		isBasicPlanAccount,
		acctId,
	)
	if err != nil {
		lg.WithError(err).Error("failed to download share")
		return Download{}, err
	}

	dl := Download{}
	defer os.RemoveAll(path)
	var file *os.File
	if format == "application/octet-stream" {
		/* userName is sanitized */
		/* #nosec G304 */
		if file, err = os.Open(path + "/ISO_" + userName + ".iso"); err != nil { //#nosec G304
			lg.WithError(err).Error("failed to open ISO share")
			return Download{}, err
		}
		dl.filename = "ISO_" + userName + ".iso"
	} else if format == "application/zip" {
		/* #nosec G304 */
		if file, err = os.Open(path + "/" + ip + shareId + ".exe"); err != nil {
			if file, err = os.Open(path + "/" + "IMAGES_" + userName + ".zip"); err != nil {
				lg.WithError(err).Error("failed to open zip share")
				return Download{}, err
			}
			dl.filename = "IMAGES_" + userName + ".zip"
		} else {
			dl.filename = "dicoms.exe"
		}
	}
	dl.file = file
	return dl, nil
}

func (s *SharesApiService) getShareDownloadBasicFlag(
	ctx context.Context,
	shareId string,
) bool {
	accountID := sqlShares.GetAcctId(ctx, s.sqldb, shareId)
	if accountID != "" {
		return s.getDownloadBasicFlag(ctx, accountID)
	}
	return false
}

func (s *SharesApiService) getDownloadBasicFlag(
	ctx context.Context,
	accountID string,
) bool {

	order, err := s.acctSvcClient.GetOrders(
		ctx,
		accountID,
		map[string]bool{"active": true},
	)

	if err == nil && len(order) > 0 {
		planId := order[0].PlanId

		if planId == accountservice.BASIC_PLAN {
			return true
		}
	}
	return false
}

// GetSharesById - Get share files (zip or disk image) or json share with eUnity token
func (s *SharesApiService) GetSharesById(
	ctx context.Context,
	acctId string,
	shareId string,
	format string,
	selectedExamIds []string,
	ip string,
	includeViewer bool,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("share_id", shareId)
	lg.WithField("claim_accountId", acctId).
		Infof("get shares for download claim account identifier")

	userAccess := acctId != ""

	shareAccountID := sqlShares.GetAcctId(ctx, s.sqldb, shareId)
	belongsToAccount := acctId == shareAccountID
	// if user authenticated check that share belongs to user
	if userAccess && !belongsToAccount {
		// or check if its a physician with access to the share
		if canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.sqldb, acctId, shareId); err != nil ||
			!canView {
			lg.WithFields(logrus.Fields{
				"reason": "share doesn't belong to user",
			}).Error("Get share unauthorized")
			return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		// physician has access to share
		lg = lg.WithField("account_type", coreapi.PhysicianAccount)
		// set acctId to empty for lookup, since physician accounts don't own any exams of their shares
		acctId = ""
	}

	if format == "application/json" {
		// include all records including deleted ones
		includeDeleted := true
		s, err := s.getJsonShare(ctx, userAccess, acctId, shareId, includeDeleted)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't retrieve share json")
		}
		return s, err
	} else {
		// get all exam ids in the share
		shareExamIds, err := s.examService.GetExamUUIDListForShare(ctx, shareId)
		if err != nil {
			lg.WithError(err).Error("could not retrieve share exam ids")
			return nil, err
		}

		//if there are no exams in the share, nothing to download
		if len(shareExamIds) <= 0 {
			lg.Error("no exams included in share to download")
			return nil, nil
		}

		// if there are no selected exams, default to all share exams
		examIdList := selectedExamIds
		if len(examIdList) == 0 {
			examIdList = shareExamIds
		}

		//get patient's lastname from the first exam only
		userName, err := s.getDownloadUsernameFromExamID(ctx, lg, examIdList[0])

		if len(selectedExamIds) != 0 {
			//check that each exam id provided is actually included in the share
			for _, reqId := range selectedExamIds {
				if !sliceContains(shareExamIds, reqId) {
					lg.WithFields(logrus.Fields{
						"reason": "1+ exams in list are not in share",
					}).Error("Get share unauthorized")
					return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
				}
			}
		}

		objectIds, err := s.getObjectIdsFromShare(ctx, shareId, shareAccountID, acctId, selectedExamIds)
		if err != nil {
			lg.WithError(err).Error("failed to get share object ids")
			return nil, err
		}

		isBasic := s.getShareDownloadBasicFlag(ctx, shareId)

		d, err := s.handleShareDownload(ctx, shareId, objectIds, userName, ip, format, includeViewer, isBasic, acctId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't handle download")
		}

		return d, err
	}
}

// GetShareHealthRecordsById - Get share healthrecord files
func (s *SharesApiService) GetShareHealthRecordsById(
	ctx context.Context,
	acctId string,
	shareId string,
	selectedHealthRecords []string,
) ([]byte, string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("share_id", shareId)

	//if user authenticated, check that share belongs to user
	if acctId != "" && !sqlShares.BelongsToAcct(ctx, s.sqldb, acctId, shareId) {
		// check if its a physician with access
		if canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.sqldb, acctId, shareId); err != nil ||
			!canView {
			lg.WithFields(logrus.Fields{
				"reason": "share doesn't belong to user",
			}).Error("Get share unauthorized")
			return nil, "", errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		// physician has access to share
		lg = lg.WithField("account_type", coreapi.PhysicianAccount)
		// set acctId to empty for lookup, since physician accounts don't own any exams of their shares
		acctId = ""
	}

	shareHRIDs, err := sqlShares.GetHRIDList(ctx, s.sqldb, shareId)
	if err != nil {
		lg.WithError(err).Error("could not retrieve share record ids")
		return nil, "", err
	}

	// if there are no records in the share, nothing to download
	if len(shareHRIDs) <= 0 {
		lg.Error("no records included in share to download")
		return nil, "", nil
	}

	if len(selectedHealthRecords) > 0 {
		// check that all the ID's we want are actually included in the share
		for _, reqId := range selectedHealthRecords {
			if !sliceContains(shareHRIDs, reqId) {
				lg.WithFields(logrus.Fields{
					"reason": "1+ records in list are not in share",
				}).Error("Get share unauthorized")
				return nil, "", errors.New(errmsg.ERR_NOT_AUTHORIZED)
			}
		}
	} else if len(selectedHealthRecords) == 0 {
		// if there are no selected records, default to all share records
		selectedHealthRecords = shareHRIDs
	}

	// all records part of a share have the same patient ID, so just use the first one
	acctId, ptid, err := sqlShares.GetPatientHealthRecordPatientIdByShareId(
		ctx,
		s.sqldb,
		shareId,
	)
	if err != nil {
		lg.WithError(err).Error("error getting patient ID from share ID")
		return nil, "", err
	}

	pt, err := s.acctSvcClient.GetPatient(ctx, acctId, ptid)
	if err != nil {
		lg.WithError(err).Error("error getting patient from acctsvc")
		return nil, "", err
	}

	res, err := s.hlthRecSvcUser.GetHealthRecordsByIds(ctx, lg, selectedHealthRecords)

	return res, sanitizePatientName(pt.LastName), err
}

// PostReshare - Reshare
func (s *SharesApiService) PostReshare(
	ctx context.Context,
	acctId string,
	shareId string,
) (interface{}, error) {
	if !sqlShares.BelongsToAcct(ctx, s.sqldb, acctId, shareId) {
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	_, baseShareInfoList, err := sqlShares.GetSharesBaseDataByShareIds(
		ctx,
		s.sqldb,
		[]interface{}{shareId},
	)
	if err != nil {
		return nil, err
	}
	if len(baseShareInfoList) == 0 {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	}
	method, _, _, _ := getShareMethodExpiryAndActivation(baseShareInfoList[0])

	if method == coreapi.EMAIL {
		pin, recipient, err := sqlShares.RetrieveShareData(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}
		var ptName, dob string
		ptName, dob, err = s.examService.GetPatientNameAndDOB(ctx, acctId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithField("acct_id", acctId).
				Error("failed to get pt info for reshare")
			return nil, err
		}

		err = sendEmailShare(
			ctx,
			s.mailer,
			shareId,
			pin,
			ptName,
			recipient,
			dob,
			s.frontEndHost,
			"",
		)
		return nil, err

	} else if method == coreapi.ACCESS_PAGE_FAX {
		data, err := sqlShares.RetrievePDFData(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}
		faxNum, _, err := sqlShares.RetrieveFaxPDFData(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}

		pdfJsonString := data.PdfJsonString

		lang := phlanguage.Select(ctx, s.supportedLanguages).WithAccountId(s.languageTagProviders.AccountId, acctId).GetLanguageTag()

		_, err = pdfs.RecreateSharePDF(ctx, "FAXLINK", "patient", "fax", acctId, pdfJsonString, false, lang)
		if err != nil {
			return nil, err
		}
		pdfBytes, err := pdfs.RecreateSharePDF(ctx, "SIMPLELINK", "patient", "print", acctId, pdfJsonString, false, lang)
		if err != nil {
			return nil, err
		}
		err = s.sendFaxShare(ctx, shareId, faxNum, pdfJsonString, false, bytes.NewReader(pdfBytes))
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't resend fax share")
			return nil, err
		}
		return pdfBytes, nil

	} else {
		data, err := sqlShares.RetrievePDFData(ctx, s.sqldb, shareId)
		if err != nil {
			return nil, err
		}

		lang := phlanguage.Select(ctx, s.supportedLanguages).WithAccountId(s.languageTagProviders.AccountId, acctId).GetLanguageTag()

		pdfBytes, err := pdfs.RecreateSharePDF(ctx, "SIMPLELINK", "patient", "print", acctId, data.PdfJsonString, true, lang)
		if err != nil {
			return nil, err
		}
		return pdfBytes, nil
	}
}

// PostShares - Create a new share
func (s *SharesApiService) PostShares(
	ctx context.Context,
	acctId string,
	share coreapi.Share,
) (interface{}, error) {

	if share.Mode != coreapi.ALL {
		//if exams are specified, verify that they belong to the user
		shareExams := share.GetExamIds()
		if len(shareExams) > 0 {
			belongs, err := s.examService.ExamsBelongToAccount(ctx, acctId, shareExams)
			if err != nil {
				logutils.DebugCtxLogger(ctx).
					WithField("acct_id", acctId).
					WithError(err).
					Error("error checking exam ownership for share")
				return nil, err
			}

			if !belongs {
				return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
			}
		}
		//if health records specified, verify that they belong to the user
		if len(share.HealthRecords) > 0 {
			lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"acctId": acctId,
				"numHRs": len(share.HealthRecords),
			})
			//query HRS for user's record ids
			recordIdList, err := s.hlthRecSvcUser.GetRecordsList(ctx, acctId, "")
			if err != nil {
				lg.WithError(err).Error("could not get hr records list")
				return nil, err
			}

			for _, hr := range share.HealthRecords {
				found := false
				for _, id := range recordIdList {
					if hr.FHIRId == id {
						found = true
						break
					}
				}
				if !found {
					lg.WithField("id", hr.FHIRId).
						WithError(err).
						Error("hr id did not belong to user")
					return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
				}
			}
		}
	}

	pdfBytes, shareId, err := s.handleShare(ctx, share, acctId)
	if err != nil {
		return nil, err
	}

	if share.Method.IsOfflineShare() {
		return NewShareResult{
			ID: shareId,
		}, nil
	}

	return NewShareResult{
		ID:  shareId,
		PDF: pdfBytes,
	}, nil
}

// PutRevokeShare - Revoke share permissions
func (s *SharesApiService) PutRevokeShare(
	ctx context.Context,
	acctId string,
	shareId string,
) error {
	if !sqlShares.BelongsToAcct(ctx, s.sqldb, acctId, shareId) {
		return errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}
	return sqlShares.DeactivateShare(ctx, s.sqldb, shareId)
}

// PostSharesValidate - Validate share viewer
func (s *SharesApiService) PostSharesValidate(
	ctx context.Context,
	shareId string,
	pin string,
	viewcode string,
	dob string,
	ip string,
	lastname string,
) (interface{}, error) {
	if shareId != "" && pin != "" {
		return SharePinValidation(ctx, s.sqldb, shareId, pin, ip)
	} else if viewcode != "" && dob != "" && lastname == "" {
		return ShareViewcodeValidation(ctx, s.sqldb, s.orgSvcClient, viewcode, dob, ip)
	} else if shareId != "" && lastname != "" {
		return ShareLastnameValidation(ctx, s.sqldb, shareId, lastname, ip)
	}
	return generateAuthTokenResponse(ctx, shareId, ip, "", "", false)
}

// PostAttachShare - Gives handler access to a share (currently only supports physicians)
func (s *SharesApiService) PostAttachShare(
	ctx context.Context,
	acctId string,
	shareCredentials coreapi.ShareCredentials,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{
			"acct_id": acctId,
		})

	// get physician's handlerId
	handlerId, err := sqlHandler.LookupHandlerId(ctx, s.sqldb, coreapi.PhysicianAccount, acctId)
	if err != nil {
		lg.WithError(err).Error("failed to lookup physician handler id")
		return err
	}

	shareId := shareCredentials.ShareId
	// if shareId is not provided, lookup viewcode + dob
	if shareId == "" && shareCredentials.Viewcode != "" && shareCredentials.Dob != "" {
		shareId, _, err = sqlShares.IsValidViewCodeDOB(
			ctx,
			s.sqldb,
			shareCredentials.Viewcode,
			shareCredentials.Dob,
		)
		if err != nil && err.Error() != "Extended" {
			lg.WithError(err).Error("error in validate viewcode dob for attach share")
			return err
		} else if shareId == "" {
			return errors.New(errmsg.ERR_SHARE_ID_NULL)
		}
		// delayed shares can still attach
	}

	// Create a mapping between handler and share
	err = sqlHandler.CreateHandlerShareMapping(ctx, s.sqldb, handlerId, shareId)
	if err != nil {
		lg.WithFields(logrus.Fields{
			"handler_id": handlerId,
			"share_id":   shareId,
		}).WithError(err).Error("failed to attach share to physician")
		return err
	}
	return nil
}

func GetP2PShareProvider(
	ctx context.Context,
	db *sql.DB,
	shareId string,
	orgSvc orgsvc.OrgService,
) (providerName string, url string, err error) {
	acctId := sqlShares.GetAcctId(ctx, db, shareId)
	if acctId == "" { // is p2p share
		transferId, err := sqlShares.GetP2PTransferId(ctx, db, shareId)
		if err != nil {
			return "", "", err
		}
		data, err := scans.GetScan(ctx, db, transferId)
		if err != nil {
			return "", "", err
		}

		clinicId, err := provider.GetClinicIdFromOriginId(ctx, db, data.OriginId)
		if err != nil || clinicId == 0 {
			return "", "", fmt.Errorf("error retrieving clinic ID for origin ID %d", data.OriginId)
		}

		clinic, err := orgSvc.GetClinicByLegacyId(ctx, clinicId)
		if err != nil {
			return "", "", fmt.Errorf("error retrieving clinic info for ID: %d", clinicId)
		}

		url = clinic.Provider.Url
		providerName = clinic.Name
		return providerName, url, nil
	}

	return "", "", nil
}

func getShareSenderInfo(
	ctx context.Context,
	db *sql.DB,
	shareId string,
	orgSvc orgsvc.OrgService,
) (shareInitiator string, shareType string, err error) {
	acctId := sqlShares.GetAcctId(ctx, db, shareId)
	if acctId != "" {
		// patient share
		return acctId, "patient", nil
	}
	transferId, err := sqlShares.GetP2PTransferId(ctx, db, shareId)
	if err != nil {
		return "", "", err
	}
	data, err := scans.GetScan(ctx, db, transferId)
	if err != nil {
		return "", "", err
	}
	clinicInfo, err := provider.GetClinicDisplayInfoFromOriginId(ctx, db, data.OriginId, orgSvc)
	if err != nil {
		return "", "", err
	}

	return clinicInfo.Name, "provider", nil
}

func sliceContains(slice []string, search string) bool {
	for _, s := range slice {
		if s == search {
			return true
		}
	}
	return false
}

func (s *SharesApiService) PutExtendShare(ctx context.Context, shareId string) error {
	validExtend, err := sqlShares.ExtendViewShare(ctx, s.sqldb, shareId)
	if err != nil {
		return err
	}
	if !validExtend {
		// Already extended before or It's a email share
		return errors.New(errmsg.ERR_UNABLE_EXTEND)
	}
	return nil
}

func (s *SharesApiService) PostShareDLAuth(
	ctx context.Context,
	shareId string,
	acctId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("share_id", shareId)

	//if user authenticated, check that the share belongs to the user.
	if acctId != "" && !sqlShares.BelongsToAcct(ctx, s.sqldb, acctId, shareId) {
		// check if its a physician with access
		if canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.sqldb, acctId, shareId); err != nil ||
			!canView {
			return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		// physician has access to share
		lg = lg.WithField("account_type", coreapi.PhysicianAccount)
	}

	//otherwise, generate a dl token and return
	token, err := auth.MakeShareDLToken(shareId)
	if err != nil {
		lg.WithError(err).Error("failed to generate share dl token")
		return nil, err
	}
	return coreapi.DLToken{Token: token}, nil
}

func (s *SharesApiService) PostRecordStreamingDLAuth(
	ctx context.Context,
	providerID int64,
	physicianAccountID string,
	studyUID string,
) (any, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("studyUID", studyUID)

	hasAccess, err := s.recordstreamingService.PhysicianCanAccessStudy(
		ctx,
		physicianAccountID,
		studyUID,
		providerID,
	)
	if err != nil {
		lg.WithError(err).
			Error("unauthorized physician access: could not verify that physician can download study")
		return nil, err
	}
	if !hasAccess {
		lg.Error("unauthorized physician access: physician is not authorized to download study")
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	//otherwise, generate a dl token and return
	token, err := auth.MakeShareDLRecordStreamingToken(studyUID, physicianAccountID)
	if err != nil {
		lg.WithError(err).Error("failed to generate share dl token")
		return nil, err
	}
	return coreapi.DLToken{Token: token}, nil
}

// DownloadRecordStreamingStudy - Get share files (zip or disk image) or json share with eUnity token
func (s *SharesApiService) DownloadRecordStreamingStudy(
	ctx context.Context,
	providerID int64,
	physicianAccountID string,
	studyUID string,
	format string,
	ip string,
	includeViewer bool,
) (any, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"providerID":         providerID,
		"studyUID":           studyUID,
		"physicianAccountID": physicianAccountID,
	})

	examID, err := sqlStudyPermissions.GetExamUUIDForRecordStreamingStudy(
		s.sqldb,
		studyUID,
		providerID,
	)
	if err != nil {
		lg.WithError(err).Error("failed to lookup examID by studyUID")
		return nil, err
	}

	userName, err := s.getDownloadUsernameFromExamID(ctx, lg, examID)
	if err != nil {
		lg.WithError(err).Error("failed to get username for download")
		return nil, err
	}

	objectIDs, err := sqlExams.GetObjectIdsByExamUUIDs(
		ctx,
		s.sqldb,
		"", // accountID
		[]string{examID},
		true,
	)
	if err != nil {
		lg.WithError(err).Error("failed to get studyUID object ids")
		return nil, err
	}

	isBasic := s.getDownloadBasicFlag(
		ctx,
		physicianAccountID,
	)

	download, err := s.handleShareDownload(
		ctx,
		studyUID,
		objectIDs,
		userName,
		ip,
		format,
		includeViewer,
		isBasic,
		physicianAccountID,
	)
	if err != nil {
		lg.WithError(err).Error("failed to download study")
	}

	return download, err
}

func (s *SharesApiService) getDownloadUsernameFromExamID(
	ctx context.Context,
	lg *logrus.Entry,
	examID string,
) (string, error) {
	userName, err := s.examService.GetLastNameFromExam(ctx, examID)
	if err != nil {
		lg.WithError(err).Error("could not retrieve patient lastname from exam")
		return "", err
	}
	return sanitizePatientName(userName), nil
}
