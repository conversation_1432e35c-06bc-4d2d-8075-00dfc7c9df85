//go:build integration
// +build integration

package regions

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestAddNewId(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	rrUrl := cfg.RRUrl
	httpClient := &http.Client{}
	testRegionID := uint16(1)
	ctx := context.Background()
	SetRegionID(testRegionID)

	t.Run("AddNewId should return without error if valid API key", func(t *testing.T) {
		rrAPIKey := "RPUvzrjqHvDrzy1fhfLQ7cNcEzjSGnGNjU3ux7EepwDlF9e2/j9p6/9oQ916KLC4HGhFe42qC+EjrclDrXDLvw=="
		rrClient := NewRegionRouterClient(rrUrl, rrAPIKey, httpClient)

		testIDKind := int16(1)
		testID := fmt.Sprint(uint(time.Now().UnixNano()))

		err := rrClient.AddNewId(ctx, testIDKind, testID)
		if err != nil {
			t.Error("expected id to region mapping to be added, got err:", err)
		}

		// check id to region mapping really stored
		gotRegionID, err := getIdRegion(ctx, testID, testIDKind, rrUrl, rrAPIKey, httpClient)
		if err != nil {
			t.Fatal("error checking viewcode to region mapping:", err)
		}

		if gotRegionID != testRegionID {
			t.Fatalf("unexpected region, got %d expected %d", gotRegionID, testRegionID)
		}
	})

	t.Run("AddNewId should return error if invalid API key", func(t *testing.T) {
		rrAPIKey := "RPUvzrjqHvDrzy1fhfLQ7cNcEz"
		rrClient := NewRegionRouterClient(rrUrl, rrAPIKey, httpClient)

		testIDKind := int16(1)
		testID := "testViewCode"

		err := rrClient.AddNewId(ctx, testIDKind, testID)
		if err == nil {
			t.Error("expected err to be returned got nil")
		}
	})
}

func getIdRegion(
	ctx context.Context,
	id string,
	idType int16,
	rrUrl string,
	rrAPIKey string,
	client *http.Client,
) (uint16, error) {
	endpoint := fmt.Sprintf("%s/v2/sid/%d/%s", rrUrl, idType, id)
	req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
	if err != nil {
		return 0, err
	}
	req.Header.Set("Authorization", "Bearer "+rrAPIKey)
	req.Header.Set("Content-type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf(
			"unexpected status code, got %d expected %d",
			resp.StatusCode,
			http.StatusOK,
		)
	}
	gotBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	var regionID uint16
	err = json.Unmarshal(gotBody, &regionID)
	if err != nil {
		return 0, fmt.Errorf("can't unmarshal response: %s", string(gotBody))
	}
	return regionID, nil
}
