package regions

// Region represents a region.
// source: https://gitlab.com/pockethealth/regionrouter/-/blob/master/pkg/region/region.go#L14-24
type Region struct {
	// Region name; e.g. Canada
	RegionName string `json:"region_name"`
	// Human-readable shorthand; e.g. CA
	RegionCode string `json:"region_code"`
	// ID for region internal to the RegionRouter.  Region IDs
	// should sequentially increase and be in [1, 2^16-1]
	RegionID uint16 `json:"region_id"`
	// DNS suffix; e.g. "cactrl.prod.pocket.health"
	RegionDNSSuffix string `json:"dns_suffix"`
}
