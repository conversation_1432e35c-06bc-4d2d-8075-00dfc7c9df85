package regions

import (
	"context" // #nosec G505
	"fmt"
	"io"
	"io/ioutil"
	"net/http"

	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// {id_type} for adding viewcodes to regionrouter
const ViewCodeIdType = int16(1)

// region ID of this coreapi instance
var regionID uint16

// regionID can only be set once, subsequent calls to this function will be of no effect
func SetRegionID(newRegionID uint16) {
	if regionID == 0 {
		regionID = newRegionID
	}
}

func GetRegionID() uint16 {
	return regionID
}

func GetRegion() string {
	regionId := GetRegionID()
	if regionId == 1 {
		return "CA"
	} else if regionId == 2 {
		return "US"
	}
	return ""
}

type HTTPRegionRouterClient struct {
	regionrouterUrl    string
	regionrouterAPIKey string
	httpClient         *http.Client
}

// interactions with region router
type RegionRouterClient interface {
	AddNewId(ctx context.Context, id_type int16, id string) error
}

func NewRegionRouterClient(rrUrl string, rrAPIKey string, client *http.Client) RegionRouterClient {
	return HTTPRegionRouterClient{
		regionrouterUrl:    rrUrl,
		regionrouterAPIKey: rrAPIKey,
		httpClient:         client,
	}
}

// update RR with id to region mapping, id is usually a viewcode
func (rr HTTPRegionRouterClient) AddNewId(ctx context.Context, id_type int16, id string) error {
	endpoint := fmt.Sprintf("%s/v2/sid/%d/%s", rr.regionrouterUrl, id_type, id)
	reqBody := []byte(fmt.Sprint(GetRegionID()))

	httpClient := httpclient.NewHTTPClient(
		rr.httpClient,
		nil,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "PUT",
		TargetURL:  endpoint,
		ReqBody:    reqBody,
		AuthScheme: httpclient.Bearer,
		AuthKey:    rr.regionrouterAPIKey,
	}

	respBody, _, err := httpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}
	// ensure http.Client connection reuse
	_, err = io.Copy(ioutil.Discard, respBody)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("error reading response body")
	}
	defer func() {
		err := respBody.Close()
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("error closing response body")
		}
	}()

	return err
}
