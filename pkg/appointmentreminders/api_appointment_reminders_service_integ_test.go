//go:build integration
// +build integration

package appointmentreminders

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"

	_ "github.com/go-sql-driver/mysql"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

func TestPostPatientStatus(t *testing.T) {
	// initialize service
	provSvcUser := providersservice.ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	// initialize cache for auth token
	provSvcUser.AuthTokenCache.SetCacheLength(time.Duration(60))

	s := AppointmentRemindersApiService{
		provUser: provSvcUser,
	}

	t.Run(
		"should send patient status update request",
		func(t *testing.T) {
			patientStatusRequest := coreapi.AppointmentReminderPatientStatusUpdate{
				ReminderId:    "id",
				PatientStatus: coreapi.PATIENT_CONFIRMED,
			}

			// prov svc response data
			token, _ := secure.GenerateRandomString(16)
			authBody := providersservice.ProvAuthenticateResponse{
				Token: token,
			}

			// mock prov svc requests
			doRequestWithCtxMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
				body, _ := ioutil.ReadAll(req.Body)
				var jsonMap map[string]json.RawMessage
				json.Unmarshal(body, &jsonMap)

				provSvcRes := http.Response{
					StatusCode: http.StatusOK,
				}

				if req.Method == "POST" &&
					req.URL.String() == "https://"+provSvcUser.URL+"/v1/authenticate" {
					// authenticate request

					// validate request fields
					var username string
					json.Unmarshal(jsonMap["username"], &username)
					if username != provSvcUser.User {
						t.Errorf("expected %s, got %s", provSvcUser.User, username)
					}
					var password string
					json.Unmarshal(jsonMap["password"], &password)
					if password != provSvcUser.PW {
						t.Errorf("expected %s, got %s", provSvcUser.User, password)
					}

					// setup respopnse
					body, _ := json.Marshal(authBody)
					provSvcRes.Body = ioutil.NopCloser(bytes.NewReader(body))
				} else if req.Method == "POST" && req.URL.String() == "https://"+provSvcUser.URL+"/v1/reminders/patientstatus" {
					// patient status update request

					// validate authorization
					authToken := req.Header.Get("X-Auth-Token")
					if authToken != token {
						t.Errorf("bad authorization: expected %s, got %s", token, authToken)
					}

					// validate request fields
					var patientStatus coreapi.AppointmentReminderPatientStatus
					json.Unmarshal(jsonMap["patient_status"], &patientStatus)
					if patientStatus != patientStatusRequest.PatientStatus {
						t.Errorf("expected %s, got %s", patientStatusRequest.PatientStatus, patientStatus)
					}

					var reminderId string
					json.Unmarshal(jsonMap["reminder_id"], &reminderId)
					if reminderId != patientStatusRequest.ReminderId {
						t.Errorf("expected %s, got %s", patientStatusRequest.ReminderId, reminderId)
					}
				} else {
					t.Errorf("unexpected request to prov svc: %s %s", req.Method, req.URL.String())
				}

				return &provSvcRes, nil
			}
			s.doRequestWithCtx = doRequestWithCtxMock

			// run method
			err := s.PostPatientStatus(
				context.Background(),
				patientStatusRequest,
				0,
			)
			if err != nil {
				t.Errorf("got error expected none: %q", err.Error())
			}

		},
	)
}
