package appointmentreminders

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

// A PublicAppointmentRemindersApiController binds http requests to an api service and writes the service results to the http response
type PublicAppointmentRemindersApiController struct {
	service coreapi.AppointmentRemindersApiServicer
}

// NewPublicAppointmentRemindersApiController creates a default api controller
func NewPublicAppointmentRemindersApiController(
	s coreapi.AppointmentRemindersApiServicer,
) coreapi.Router {
	return &PublicAppointmentRemindersApiController{service: s}
}

// Routes returns all of the api route for the AppointmentRemindersApiController
func (c *PublicAppointmentRemindersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostPatientStatusEmail",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/patientstatus",
			HandlerFunc: c.PostPatientStatus,
		},
	}
}

func (c *PublicAppointmentRemindersApiController) GetPathPrefix() string {
	return "/v1/appointments"
}

func (c *PublicAppointmentRemindersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *PublicAppointmentRemindersApiController) PostPatientStatus(
	w http.ResponseWriter,
	r *http.Request,
) {
	var patientStatusUpdate coreapi.AppointmentReminderPatientStatusUpdate
	if err := json.NewDecoder(r.Body).Decode(&patientStatusUpdate); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	// Right now for v2 email reminders, in the email itself there will be a confirm/cancel button which will contain a providerID only for v2 reminders. That's what this flag is for.
	// Ideally in the future state of appointment reminders we won't need this if block to determine v2/v1 etc.
	var providerId int64
	var err error
	if patientStatusUpdate.ProviderId != "" {
		providerId, err = strconv.ParseInt(patientStatusUpdate.ProviderId, 10, 64)
		if err != nil {
			httperror.ErrorWithLog(w, r, fmt.Sprintf("%v: invalid provider_id", errmsg.ERR_INVALID_REQ_BODY), http.StatusBadRequest)
			return
		}
	}

	err = c.service.PostPatientStatus(r.Context(), patientStatusUpdate, providerId)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func handleError(w *http.ResponseWriter, r *http.Request, err error) {
	status := http.StatusInternalServerError
	if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
		status = http.StatusUnauthorized
	} else if err.Error() == errmsg.ERR_NOT_FOUND {
		status = http.StatusNotFound
	} else if err.Error() == errmsg.ERR_INVALID_REQ_BODY {
		status = http.StatusBadRequest
	}
	httperror.ErrorWithLog(*w, r, err.Error(), status)
}
