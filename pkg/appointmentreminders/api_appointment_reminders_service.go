package appointmentreminders

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// AppointmentRemindersApiService is a service that implents the logic for the AppointmentRemindersApiServicer
// This service should implement the business logic for every endpoint for the AppointmentReminders API.
// Include any external packages or services that will be required by this service.
type AppointmentRemindersApiService struct {
	provUser         providersservice.ProvSvcUser
	doRequestWithCtx requestwithcontext.RequestWithCtx
}

// NewAppointmentRemindersApiService creates a default api service
func NewAppointmentRemindersApiService(
	provSvcUser providersservice.ProvSvcUser,
) coreapi.AppointmentRemindersApiServicer {
	return &AppointmentRemindersApiService{
		provUser:         provSvcUser,
		doRequestWithCtx: requestwithcontext.DoRequestWithCtx,
	}
}

// PostPatientStatus
//   - Sends update request for patient status of appointment reminder to ProvidersService
func (ars *AppointmentRemindersApiService) PostPatientStatus(
	ctx context.Context,
	patientStatusUpdate coreapi.AppointmentReminderPatientStatusUpdate,
	providerId int64,
) error {
	authToken, err := ars.provUser.GetAuthToken(ctx, ars.doRequestWithCtx)
	if err != nil || authToken == "" {
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	postBody, err := json.Marshal(patientStatusUpdate.ToRequestBody(providerId))
	if err != nil {
		return errors.New(errormsgs.ERR_JSON_UNMARSHAL)
	}
	b := bytes.NewBuffer(postBody)

	//send http POST request to providers' service  /v1/reminders/patientstatus
	req, err := http.NewRequest(
		"POST",
		"https://"+ars.provUser.URL+"/v1/reminders/patientstatus",
		b,
	)
	if err != nil {
		return errors.New(errormsgs.ERR_CREATE_REQUEST)
	}

	req.Header.Set("X-Auth-Token", authToken)
	resp, err := ars.doRequestWithCtx(ctx, ars.provUser.Client, req)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"reminder_id": patientStatusUpdate.ReminderId,
		}).WithError(err).Error("Prov svc post patient status email update failed")
		return err
	}

	return httperror.MapToError(resp.StatusCode, "Unable to update patient status for appointment reminder")
}

func (ars *AppointmentRemindersApiService) GetAppointments(
	ctx context.Context,
	providerID int64,
	consentID string,
) (coreapi.AppointmentDetails, error) {
	appointmentDetails, err := ars.provUser.GetAppointments(ctx, providerID, consentID)
	if err != nil {
		return coreapi.AppointmentDetails{}, err
	}

	if appointmentDetails.Appointments == nil {
		appointmentDetails.Appointments = []coreapi.Appointment{}
	}

	return appointmentDetails, nil
}
