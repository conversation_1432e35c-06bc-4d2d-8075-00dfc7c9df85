package appointmentreminders

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/pockethealth/coreapi/generated/api"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type PrivateAppointmentsApiController struct {
	service coreapi.AppointmentRemindersApiServicer
}

func NewPrivateAppointmentsApiController(
	s coreapi.AppointmentRemindersApiServicer,
) coreapi.Router {
	return &PrivateAppointmentsApiController{service: s}
}

// Routes returns all of the api route for the AppointmentRemindersApiController
func (c *PrivateAppointmentsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetAppointments",
			Method:      http.MethodGet,
			Pattern:     "",
			HandlerFunc: c.GetAppointments,
		},
	}
}

func (c *PrivateAppointmentsApiController) GetPathPrefix() string {
	return "/v1/appointments"
}

func (c *PrivateAppointmentsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all reports paths require authentication
	return []func(next http.Handler) http.Handler{auth.ValidateAuth, verifyClaim}
}

func (c *PrivateAppointmentsApiController) GetAppointments(w http.ResponseWriter, r *http.Request) {
	errorObj := api.Error{}

	providerIDStr := r.URL.Query().Get("provider_id")
	providerID, err := strconv.ParseInt(providerIDStr, 10, 64)
	if providerID == 0 || err != nil {
		errorObj.Code = httperror.BAD_QUERY_STRING

		providerIDValidationErr := api.ValidationError{
			Field: "provider_id",
			Code:  httperror.PARSE_FAILED,
			Message: api.NewOptString(
				fmt.Sprintf("%v: invalid provider_id", errormsgs.ERR_BAD_QUERY_PARAM),
			),
		}
		errorObj.ValidationErrors = append(errorObj.ValidationErrors, providerIDValidationErr)
	}

	consentID := r.URL.Query().Get("consent_id")
	if consentID == "" {
		errorObj.Code = httperror.BAD_QUERY_STRING

		consentIDValidationErr := api.ValidationError{
			Field:   "consent_id",
			Code:    httperror.EMPTY_PARAM,
			Message: api.NewOptString(fmt.Sprintf("%v: consent_id is empty", errormsgs.ERR_BAD_QUERY_PARAM)),
		}
		errorObj.ValidationErrors = append(errorObj.ValidationErrors, consentIDValidationErr)
	}

	if errorObj.Code != "" {
		status := http.StatusBadRequest
		coreapi.EncodeJSONResponse(r.Context(), errorObj, &status, w)
		return
	}

	result, err := c.service.GetAppointments(r.Context(), providerID, consentID)
	if err != nil {
		errorObj.Code = httperror.RETRIEVAL_FAILED
		status := httperror.ErrormsgToStatus(err.Error())
		if status == http.StatusInternalServerError {
			errorObj.Message = api.NewOptString("Internal Server Error")
		}

		coreapi.EncodeJSONResponse(r.Context(), errorObj, &status, w)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func verifyClaim(next http.Handler) http.Handler {
	writeUnauthorizedResponse := func(ctx context.Context, w http.ResponseWriter) {
		errorObj := api.Error{
			Code:    httperror.UNAUTHORIZED_TOKEN,
			Message: api.NewOptString(errormsgs.ERR_BAD_TOKEN),
		}
		status := http.StatusUnauthorized
		coreapi.EncodeJSONResponse(ctx, errorObj, &status, w)
	}

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		consentID := r.URL.Query().Get("consent_id")
		token := r.Header.Get("Authorization")

		claimedConsentID, err := auth.DecodeVerifiedConsentToken(token)
		if err != nil {
			writeUnauthorizedResponse(r.Context(), w)
			return
		} else if claimedConsentID != consentID {
			writeUnauthorizedResponse(r.Context(), w)
			return
		}

		next.ServeHTTP(w, r)
	})
}
