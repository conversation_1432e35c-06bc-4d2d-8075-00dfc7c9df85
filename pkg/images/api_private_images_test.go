package images

import (
	"bytes"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// when you test locally this ends up being what clientAddr[0] looks like
const (
	localClaimIp = ""
)

func TestGetImageById(t *testing.T) {

	//mock get user exams function
	mockFn := func(imageid string, accept string, acctId string, shareId string, auth bool) (io.ReadCloser, int64, error) {
		return io.NopCloser(&bytes.Buffer{}), 0, nil
	}
	service := NewMockImagesApiService(mockFn, nil)

	controller := NewPrivateImagesApiController(service)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// happy cases
	t.Run("image png request - without token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "image/png"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("image dicom request - without token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "application/dicom"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("image bad type request - without token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "notarealtype"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		wantType := "image/png" //anything that's not application/dicom should default to png
		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != wantType {
			t.Errorf("handler set wrong Content-Type header: got %q want %q", contentType, wantType)
		}
	})

	// happy cases
	t.Run("image png request - with token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "image/png"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		imageToken, err := auth.MakeImageAuthToken("-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=")
		req.Header.Set("X-Image-Token", imageToken)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("image dicom request - with token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "application/dicom"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		imageToken, err := auth.MakeImageAuthToken("-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=")
		req.Header.Set("X-Image-Token", imageToken)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("image bad type request - with token in header", func(t *testing.T) {
		req, err := http.NewRequest(
			"GET",
			"/v1/images/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "notarealtype"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		imageToken, err := auth.MakeImageAuthToken("-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=")
		req.Header.Set("X-Image-Token", imageToken)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		wantType := "image/png" //anything that's not application/dicom should default to png
		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != wantType {
			t.Errorf("handler set wrong Content-Type header: got %q want %q", contentType, wantType)
		}
	})
}
