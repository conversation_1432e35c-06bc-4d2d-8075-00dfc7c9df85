/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package images

import (
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateImagesApiController binds http requests to an api service and writes the service results to the http response
type PrivateImagesApiController struct {
	service coreapi.ImagesApiServicer
}

// NewPrivateImagesApiController creates a default api controller
func NewPrivateImagesApiController(s coreapi.ImagesApiServicer) coreapi.PrivateImagesApiRouter {
	return &PrivateImagesApiController{service: s}
}

// Routes returns all of the api route for the ImagesApiController
func (c *PrivateImagesApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetImageByID",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{imageID}",
			HandlerFunc: c.GetImageByID,
		},
		{
			Name:        "GetImageMetadataByID",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{imageid}/metadata",
			HandlerFunc: c.GetImageMetadataByID,
		},
	}
}

func (c *PrivateImagesApiController) GetPathPrefix() string {
	return "/v1/images"
}

func (c *PrivateImagesApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all images paths require authentication
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func checkAuthentication(token string, imageId string) (bool, error) {

	if len(token) < 1 || len(imageId) < 1 {
		return false, nil
	}

	claim, err := auth.DecodeToken(token)
	if err != nil {
		return false, err
	}

	if claim.ImageID != imageId {
		return false, nil
	}

	return true, nil

}

// GetImageByID - Get image
func (c *PrivateImagesApiController) GetImageByID(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	imageID := params["imageID"]
	accept := r.Header.Get("accept")
	//TODO: handle Range header

	token := r.Header.Get("Authorization")
	//both users and share viewers can access images
	acctId, errAcc := auth.DecodeAccountToken(token)
	shareId, _, errSV := auth.DecodeShareViewerToken(token)
	if errAcc != nil && errSV != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	imageToken := r.Header.Get("X-Image-Token")

	//valid image token
	auth, err := checkAuthentication(imageToken, imageID)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	}

	result, size, err := c.service.GetImageByID(r.Context(), imageID, accept, acctId, shareId, auth)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if strings.HasPrefix(err.Error(), errmsg.ERR_NEEDS_PURCHASE) {
			status = http.StatusPaymentRequired
		} else if err.Error() == errmsg.ERR_NOT_IMAGE {
			status = http.StatusBadRequest
		} else if err.Error() == errmsg.ERR_TOO_LARGE {
			status = http.StatusInsufficientStorage
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}
	defer result.Close()

	w.Header().Set("X-Is-Encapsulated-PDF", "false")
	w.Header().Set("Content-Length", fmt.Sprint(size))
	if accept == "application/dicom" {
		w.Header().Set("Content-Type", "application/dicom")
	} else {
		w.Header().Set("Content-Type", "image/png")
	}
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, result)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithField("image_id", imageID).
			WithError(err).
			Error("failed to write GET /image/{id} response")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
}

// GetImageMetadataByID - Get image metadata
func (c *PrivateImagesApiController) GetImageMetadataByID(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	imageid := params["imageid"]
	token := r.Header.Get("Authorization")

	//both users and share viewers can access images
	acctId, errAcc := auth.DecodeAccountToken(token)
	shareId, _, errSV := auth.DecodeShareViewerToken(token)
	if errAcc != nil && errSV != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	result, err := c.service.GetImageMetadataByID(r.Context(), imageid, acctId, shareId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
