package images

import (
	"context"
	"io"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// MockImagesApiService is a service that implents the logic for the UsersApiServicer for testing
type MockImagesApiService struct {
	MockGetImageByID    func(string, string, string, string, bool) (io.ReadCloser, int64, error)
	MockGetMetadataByID func(string, string, string) (interface{}, error)
}

// NewMockImagesApiService creates a default api service
func NewMockImagesApiService(
	mockImageByID func(string, string, string, string, bool) (io.ReadCloser, int64, error),
	mockMetadata func(string, string, string) (interface{}, error),
) coreapi.ImagesApiServicer {
	return &MockImagesApiService{MockGetImageByID: mockImageByID, MockGetMetadataByID: mockMetadata}
}

// GetImageByID - Get image
func (s *MockImagesApiService) GetImageByID(
	ctx context.Context,
	imageID string,
	accept string,
	acctId string,
	shareId string,
	auth bool,
) (io.ReadCloser, int64, error) {
	return s.MockGetImageByID(imageID, accept, acctId, shareId, auth)
}

// GetImageMetadataByID - Get image metadata
func (s *MockImagesApiService) GetImageMetadataByID(
	ctx context.Context,
	imageid string,
	acctId string,
	shareId string,
) (interface{}, error) {
	return s.MockGetMetadataByID(imageid, acctId, shareId)
}
