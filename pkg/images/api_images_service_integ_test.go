//go:build integration
// +build integration

package images

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func setupScanObject(
	t *testing.T,
	db *sql.DB,
	scanId string,
	acctId string,
	originId uint,
	objectId string,
	examUuid string,
	activated bool,
	source string,
) {
	var err error
	_, err = db.Exec(
		"INSERT INTO scans (scan_id, origin_id, source, modality) VALUES (?, ?, ?, ?)",
		scanId,
		originId,
		source,
		"modality",
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}
	_, err = db.Exec("INSERT INTO objects (object_id, scan_id) VALUES (?, ?)", objectId, scanId)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}
	_, err = db.Exec(
		`INSERT INTO exams (uuid, account_id, activated, transfer_id, exam_uid, description, date, body_part, referring_physician)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		examUuid,
		acctId,
		activated,
		scanId,
		"exam_uid",
		"description",
		"date",
		"body_part",
		"referring_physician",
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}
	_, err = db.Exec(
		"INSERT INTO object_mappings (object_id, exam_uuid) VALUES (?, ?)",
		objectId,
		examUuid,
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}
	t.Cleanup(func() {
		db.Exec("DELETE FROM exams WHERE uuid=?", examUuid)
		db.Exec("DELETE FROM scans WHERE scan_id=?", scanId)
		db.Exec("DELETE FROM objects WHERE object_id=?", objectId)
		db.Exec("DELETE FROM object_mappings WHERE object_id=?", objectId)
	})
}

func setupPhysicianShare(
	t *testing.T,
	db *sql.DB,
	objectId string,
	shareId string,
	physicianAcctId string,
) {
	handlerId := "imagesIntegTest_" + strconv.FormatInt(time.Now().UnixNano(), 10) + physicianAcctId

	_, err := db.Exec(
		`INSERT INTO record_handlers
		(id,
		handler_type,
		handler_type_id,
		upload,
		own,
		share,
		view)
		VALUES (?,?,?,?,?,?,?)`,
		handlerId,
		coreapi.PhysicianAccount,
		physicianAcctId,
		false,
		false,
		false,
		true,
	)
	if err != nil {
		t.Fatalf("unable to set insert physician: %s", err)
	}

	_, err = db.Exec(
		"INSERT INTO shares (account_id, share_id) VALUES ('abc123', ?)",
		shareId,
	)
	if err != nil {
		t.Fatalf("unable to set insert share for physician: %s", err)
	}

	_, err = db.Exec(
		"INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES (?,?,?)",
		shareId,
		objectId,
		0,
	)
	if err != nil {
		t.Fatalf("unable to set insert share object for physician: %s", err)
	}

	_, err = db.Exec(
		"INSERT INTO record_handlers_share_map (handler_id, share_id) VALUES (?,?)",
		handlerId,
		shareId,
	)
	if err != nil {
		t.Fatalf("unable to set insert share physician mapping: %s", err)
	}

	t.Cleanup(func() {
		db.Exec("DELETE FROM record_handlers WHERE id=?", handlerId)
		db.Exec("DELETE FROM record_handlers_share_map WHERE handler_id=?", handlerId)
		db.Exec("DELETE FROM share_objects2 where share_id=?", shareId)
		db.Exec("DELETE FROM shares where share_id=?", shareId)
	})
}

func setupTmpDirs(t *testing.T) {
	// create tmp directory for temporary files during file conversion
	if _, err := os.Stat("vault"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault", 0700)
		if mkdirErr != nil {
			t.Fatal("failed to set up temp dirs")
		}
	}
	if _, err := os.Stat("vault/tmp"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault/tmp", 0700)
		if mkdirErr != nil {
			t.Fatal("failed to set up temp dirs")
		}
	}
}

func TestGetImageMetadata(t *testing.T) {
	db := testutils.SetupTestDB(t)

	s := ImagesApiService{
		waitGroup:       &sync.WaitGroup{},
		sqldb:           db,
		containerClient: azureUtils.ContainerClient{},
		orgSvc:          &orgservice.OrgServiceMock{},
	}

	t.Run("clinic uploaded", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := "imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		meta, err := s.GetImageMetadataByID(context.Background(), objectId, acctId, "")
		if err != nil {
			t.Fatalf("got error when expected none for image %s: %q", objectId, err.Error())
		}
		if _, ok := meta.(coreapi.ImageMetadata); !ok {
			t.Fatal("expected ImageMetadata struct to be returned")
		}
	})

	t.Run("self uploaded", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := "imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		setupScanObject(t, db, scanId, acctId, 123456, objectId, examUuid, true, exams.CDUpload)
		meta, err := s.GetImageMetadataByID(context.Background(), objectId, acctId, "")
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if _, ok := meta.(coreapi.ImageMetadata); !ok {
			t.Error("expected ImageMetadata struct to be returned")
		}
	})

	t.Run("user can't access", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := "imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.Background()
		_, err := s.GetImageMetadataByID(ctx, objectId, "nottheaccountid", shareId)
		if err == nil {
			t.Error("expected error but got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected error to be %q but it was %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})

	t.Run("image id does not exist", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		shareId := "" // unused
		imageId := rand
		ctx := context.Background()
		_, err := s.GetImageMetadataByID(ctx, imageId, rand, shareId)
		if err == nil {
			t.Error("expected error but got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected error to be %q but it was %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})
}

func TestGetImage(t *testing.T) {
	// go test uses package location as working directory; need to change to project dir so ImagesApiService can locate vault and jsScripts dirs
	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}
	setupTmpDirs(t)
	db := testutils.SetupTestDB(t)

	t.Run("account doesn't have access to image", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "application/dicom"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			orgSvc:          &orgservice.OrgServiceMock{},
		}
		_, _, err := s.GetImageByID(ctx, objectId, accept, "nottheacctid", shareId, false)
		if err == nil {
			t.Error("expected error but got none")
		}
		if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf(
				"expected error to be %q but it was %q",
				errormsgs.ERR_NOT_AUTHORIZED,
				err.Error(),
			)
		}
	})

	t.Run("physician account has access to image", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		physAcctId := acctId + "_phys"
		scanId := " imagesIntegTestScan_" + physAcctId
		objectId := "imagesIntegTestObject_" + physAcctId
		examUuid := "imagesIntegTestExam_" + physAcctId
		accept := "application/dicom"
		shareId := "imagesIntegTestShare_" + physAcctId
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+physAcctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		expected := []byte("file bytes")
		mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
			if imageId != objectId {
				t.Errorf(
					"ConvertImage() called with wrong args: expected imageId to be %s but got %s",
					objectId,
					imageId,
				)
			}
			if CiAccept != accept {
				t.Errorf(
					"ConvertImage() called with wrong args: expected accept to be %s but got %s",
					accept,
					CiAccept,
				)
			}
			return io.NopCloser(bytes.NewBuffer(expected)), int64(len(expected)), nil
		}
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			orgSvc:          &orgservice.OrgServiceMock{},
			convertImage:    mockConvertImage,
		}

		// physician should not have access yet
		_, _, err := s.GetImageByID(ctx, objectId, accept, physAcctId, shareId, false)
		if err == nil {
			t.Error("expected error but got none")
		}
		if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf(
				"expected error to be %q but it was %q",
				errormsgs.ERR_NOT_AUTHORIZED,
				err.Error(),
			)
		}

		// share with physician to give access
		setupPhysicianShare(t, db, objectId, shareId, physAcctId)
		_, _, err = s.GetImageByID(ctx, objectId, accept, physAcctId, shareId, false)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}

		// test to make sure a different physician doesn't have acces
		physAcctId2 := acctId + "_phys2"
		objectId2 := "imagesIntegTestObject_" + physAcctId2
		shareId2 := "imagesIntegTestShare_" + physAcctId2

		setupPhysicianShare(t, db, objectId2, shareId2, physAcctId2)
		_, _, err = s.GetImageByID(ctx, objectId, accept, physAcctId2, shareId, false)
		if err == nil {
			t.Error("expected error but got none")
		}
		if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf(
				"expected error to be %q but it was %q",
				errormsgs.ERR_NOT_AUTHORIZED,
				err.Error(),
			)
		}
	})

	t.Run("image not paid", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "application/dicom"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, false, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			orgSvc:          &orgservice.OrgServiceMock{},
		}
		_, _, err := s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		if err == nil {
			t.Error("expected error but got none")
		}
		if !strings.HasPrefix(err.Error(), errormsgs.ERR_NEEDS_PURCHASE) {
			t.Errorf(
				"expected error to be %q but it was %q",
				errormsgs.ERR_NEEDS_PURCHASE,
				err.Error(),
			)
		}
	})

	t.Run("image id doesn't exist", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		objectId := "imagesIntegTestObject_" + acctId
		accept := "application/dicom"
		shareId := "" // unused
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			orgSvc:          &orgservice.OrgServiceMock{},
		}
		_, _, err := s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		if err == nil {
			t.Error("expected error but got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected error to be %q but it was %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})

	t.Run("file type set to DICOM", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "application/dicom"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		expected := []byte("file bytes")
		mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
			if imageId != objectId {
				t.Errorf(
					"ConvertImage() called with wrong args: expected imageId to be %s but got %s",
					objectId,
					imageId,
				)
			}
			if CiAccept != accept {
				t.Errorf(
					"ConvertImage() called with wrong args: expected accept to be %s but got %s",
					accept,
					CiAccept,
				)
			}
			return io.NopCloser(bytes.NewBuffer(expected)), int64(len(expected)), nil
		}
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			convertImage:    mockConvertImage,
			orgSvc:          &orgservice.OrgServiceMock{},
		}

		file, _, err := s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		require.NoError(t, err)

		fileContents, err := io.ReadAll(file)
		require.NoError(t, err)
		if bytes.Compare(fileContents, expected) != 0 {
			t.Error("actual file did not match expected")
		}
	})

	t.Run("file type set to PNG", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "image/png"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		if err != nil {
			t.Fatalf("error when opening file: %q", err.Error())
		}
		mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
			if imageId != objectId {
				t.Errorf(
					"ConvertImage() called with wrong args: expected imageId to be %s but got %s",
					objectId,
					imageId,
				)
			}
			if CiAccept != accept {
				t.Errorf(
					"ConvertImage() called with wrong args: expected accept to be %s but got %s",
					accept,
					CiAccept,
				)
			}
			bs := []byte("true")
			return io.NopCloser(bytes.NewBuffer(bs)), int64(len(bs)), nil
		}
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			convertImage:    mockConvertImage,
			orgSvc:          &orgservice.OrgServiceMock{},
		}

		_, _, err = s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
	})

	t.Run("file type not set", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "image/png"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
			if imageId != objectId {
				t.Errorf(
					"ConvertImage() called with wrong args: expected imageId to be %s but got %s",
					objectId,
					imageId,
				)
			}
			if CiAccept != accept {
				t.Errorf(
					"ConvertImage() called with wrong args: expected accept to be %s but got %s",
					accept,
					CiAccept,
				)
			}
			bs := []byte("true")
			return io.NopCloser(bytes.NewBuffer(bs)), int64(len(bs)), nil
		}
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			convertImage:    mockConvertImage,
			orgSvc:          &orgservice.OrgServiceMock{},
		}

		_, _, err = s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
	})

	t.Run("object blob not found", func(t *testing.T) {
		acctId := strconv.FormatInt(time.Now().UnixNano(), 10)
		scanId := " imagesIntegTestScan_" + acctId
		objectId := "imagesIntegTestObject_" + acctId
		examUuid := "imagesIntegTestExam_" + acctId
		accept := "application/dicom"
		shareId := "" // unused
		setupScanObject(t, db, scanId, acctId, 54, objectId, examUuid, true, exams.PatientSharing)
		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"images_integ_test"+acctId,
		)
		ctx = context.WithValue(
			context.Background(),
			"method",
			"GET",
		)
		ctx = context.WithValue(
			context.Background(),
			"request",
			"v1/images/"+objectId,
		)
		expected := errors.New("images integration test error: blob not found")
		mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
			if imageId != objectId {
				t.Errorf(
					"ConvertImage() called with wrong args: expected imageId to be %s but got %s",
					objectId,
					imageId,
				)
			}
			if CiAccept != accept {
				t.Errorf(
					"ConvertImage() called with wrong args: expected accept to be %s but got %s",
					accept,
					CiAccept,
				)
			}
			return nil, 0, expected
		}
		s := ImagesApiService{
			waitGroup:       &sync.WaitGroup{},
			sqldb:           db,
			containerClient: azureUtils.ContainerClient{},
			convertImage:    mockConvertImage,
			orgSvc:          &orgservice.OrgServiceMock{},
		}

		file, _, err := s.GetImageByID(ctx, objectId, accept, acctId, shareId, false)
		if file != nil {
			t.Error("expected file to be nil")
		}
		if err != expected {
			t.Errorf("expected error to be %s but it was %s", expected.Error(), err.Error())
		}
	})
}
