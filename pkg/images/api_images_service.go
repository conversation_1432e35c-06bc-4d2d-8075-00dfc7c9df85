/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package images

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"sync"

	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	converter "gitlab.com/pockethealth/coreapi/pkg/images/converter"
	sqlObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// ImagesApiService is a service that implents the logic for the ImagesApiServicer
// This service should implement the business logic for every endpoint for the ImagesApi API.
// Include any external packages or services that will be required by this service.
type ImagesApiService struct {
	sqldb           *sql.DB
	containerClient azureUtils.ContainerClient
	waitGroup       *sync.WaitGroup

	// injected functions (so we can mock them when testing)
	convertImage ConvertImageFunc
	orgSvc       orgs.OrgService
}

type ConvertImageFunc func(ctx context.Context, imageId string, accept string, auth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error)

// NewImagesApiService creates a default api service
func NewImagesApiService(
	mysqlDB *sql.DB,
	containerCli azureUtils.ContainerClient,
	waitGroup *sync.WaitGroup,
	orgSvc orgs.OrgService,
) coreapi.ImagesApiServicer {
	return &ImagesApiService{
		sqldb:           mysqlDB,
		containerClient: containerCli,
		waitGroup:       waitGroup,
		convertImage:    converter.ConvertImage,
		orgSvc:          orgSvc,
	}
}

// GetImageByID - Get image
func (s *ImagesApiService) GetImageByID(
	ctx context.Context,
	imageId string,
	accept string,
	acctId string,
	shareId string,
	auth bool,
) (io.ReadCloser, int64, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("imageId", imageId)

	// bypass auth check if image present valid auth token in header
	if !auth {
		valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, imageId, acctId, shareId)
		if err != nil {
			lg.WithError(err).Error("get image access failed")
			return nil, 0, err
		}
		if !valid {
			return nil, 0, errors.New(errmsg.ERR_NOT_AUTHORIZED)
		}

		if !sqlObjects.IsActivated(ctx, s.sqldb, imageId) {
			return nil, 0, fmt.Errorf("%s: %s", errmsg.ERR_NEEDS_PURCHASE, imageId)
		}
	}

	return s.convertImage(
		ctx,
		imageId,
		accept,
		auth,
		s.containerClient,
		s.sqldb,
		s.waitGroup,
		false,
	)
}

// GetImageMetadataByID - Get image metadata
func (s *ImagesApiService) GetImageMetadataByID(
	ctx context.Context,
	imageId string,
	acctId string,
	shareId string,
) (interface{}, error) {
	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, imageId, acctId, shareId)
	if err != nil {
		return nil, err
	}
	if !valid {
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}
	var imgMetadata coreapi.ImageMetadata
	imgMetadata, err = sqlObjects.GetImageMetadata(ctx, s.sqldb, imageId, s.orgSvc)
	if err != nil {
		return nil, err
	}

	return imgMetadata, nil
}
