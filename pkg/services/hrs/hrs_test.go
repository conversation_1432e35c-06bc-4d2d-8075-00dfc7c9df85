package hrs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
)

var hrs = HlthRecSvcUser{
	URL:                 "heresanurl",
	ApiKey:              "heresakey",
	maxRecordsPerUpload: 5,
	maxFilesPerRecord:   15,
}

func generateGoodReqBody(
	t *testing.T,
	numFilesInJson int,
	numActualFiles int,
	filedata []byte,
) *multipart.Reader {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	records := `{"patient":{},
			"records":[
				{
					"name":"my lab report",
					"filenames":[
						"someotherfile"`
	for i := 1; i < numFilesInJson; i++ {
		records += fmt.Sprintf(`,"someotherfile%d"`, i)
	}
	records += `],
					"source":{
						"type":"upload",
						"description":"Dr.K"
					},
					"recorddate":"20200404",
					"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
					"tag": "LabReport"
				}
			]
		}`
	if !json.Valid([]byte(records)) {
		t.Fatal("bad json in test data")
	}
	var fw io.Writer
	var err error
	if fw, err = w.CreateFormField("records"); err != nil {
		t.Fatalf("failed to create test multipart record field: %q", err)
	}
	if _, err = io.WriteString(fw, records); err != nil {
		t.Fatalf("failed to write test multipart record field: %q", err)
	}
	for i := 0; i < numActualFiles; i++ {
		if fw, err = w.CreateFormFile("someotherfile"+fmt.Sprint(i), "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %q", err)
		}
		if _, err = io.Copy(fw, bytes.NewReader(filedata)); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
	}
	w.Close()
	return multipart.NewReader(&b, w.Boundary())
}

func checkAuthAndURL(expectedURL string, req *http.Request, t *testing.T) {
	auth := req.Header.Get("Authorization")
	if auth == "" {
		t.Fatal("request missing authorization")
	}
	if auth != hrs.ApiKey {
		t.Fatalf("expected auth to be %s, got %s", hrs.ApiKey, auth)
	}

	if req.URL.String() != expectedURL {
		t.Fatalf("expected request URL %s, got %s", expectedURL, req.URL.String())
	}
}

func TestGetRecordsList(t *testing.T) {

	idListBody := ioutil.NopCloser(strings.NewReader(`
	[
		"id1",
		"id2"
	]`))
	emptyidListBody := ioutil.NopCloser(strings.NewReader(`[]`))

	t.Run("good request with ids", func(t *testing.T) {
		acctId := "acct_id"
		expectedURL := fmt.Sprintf(
			"https://%s/v1/records/list?accountId=%s",
			hrs.URL,
			acctId,
		)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedURL, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       idListBody,
			}, nil
		}
		hrs.SetDoRequest(doReqMock)
		ids, err := hrs.GetRecordsList(context.Background(), acctId, "")
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if len(ids) != 2 || ids[0] != "id1" || ids[1] != "id2" {
			t.Fatalf("got unexpected results for id list: %v", ids)
		}
	})

	t.Run("good request with no ids", func(t *testing.T) {
		acctId := "acct_id"
		expectedURL := fmt.Sprintf(
			"https://%s/v1/records/list?accountId=%s",
			hrs.URL,
			acctId,
		)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedURL, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       emptyidListBody,
			}, nil
		}
		hrs.SetDoRequest(doReqMock)
		ids, err := hrs.GetRecordsList(context.Background(), acctId, "")
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if len(ids) != 0 {
			t.Fatalf("expected empty id list, got: %v", ids)
		}
	})

	t.Run("not ok status", func(t *testing.T) {
		acctId := "acct_id"
		expectedURL := fmt.Sprintf(
			"https://%s/v1/records/list?accountId=%s",
			hrs.URL,
			acctId,
		)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedURL, req, t)
			return &http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       ioutil.NopCloser(strings.NewReader(`some err returned`)),
			}, nil
		}
		hrs.SetDoRequest(doReqMock)
		_, err := hrs.GetRecordsList(context.Background(), acctId, "")
		if err == nil {
			t.Fatal("expected error from GetRecordsList, got none")
		}
	})

	t.Run("request error", func(t *testing.T) {
		acctId := "acct_id"
		expectedURL := fmt.Sprintf(
			"https://%s/v1/records/list?accountId=%s",
			hrs.URL,
			acctId,
		)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedURL, req, t)
			return nil, errors.New("HRS ERROR")
		}
		hrs.SetDoRequest(doReqMock)
		_, err := hrs.GetRecordsList(context.Background(), acctId, "")
		if err == nil {
			t.Fatal("expected error from GetRecordsList, got none")
		}
	})
}

func TestUploadForm(t *testing.T) {
	sampleJpeg, err := ioutil.ReadFile("../../../apptest/gotests/assets/samplejpeg.jpeg")
	if err != nil {
		t.Fatalf("couldn't read in sample file for tests: %v", err)
	}

	t.Run("json too large", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1, 10000)
		req := generateGoodReqBody(t, 1, 1, sampleJpeg)
		_, _, err := hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			req,
		)
		//expect bad request error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to large json")
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf(
				"expected buildPostHRSForm to have error %v, got %v",
				errormsgs.ERR_INVALID_REQ_BODY,
				err,
			)
		}
	})

	t.Run("too many files", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 10000)
		req := generateGoodReqBody(t, 2, 2, sampleJpeg)
		_, _, err := hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			req,
		)
		//expect bad request error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to too many files")
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf(
				"expected buildPostHRSForm to have error %v, got %v",
				errormsgs.ERR_INVALID_REQ_BODY,
				err,
			)
		}
	})

	t.Run("file too large", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 10)
		req := generateGoodReqBody(t, 1, 1, sampleJpeg)
		_, _, err := hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			req,
		)
		//expect bad request error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to large file")
		}
		if err.Error() != errormsgs.ERR_FILE_SIZE {
			t.Fatalf(
				"expected buildPostHRSForm to have error %v, got %v",
				errormsgs.ERR_FILE_SIZE,
				err,
			)
		}
	})

	t.Run("too many form parts", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 8000)
		req := generateGoodReqBody(t, 1, 8, sampleJpeg)
		_, _, err := hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			req,
		)
		//expect bad request error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to too many form parts")
		}
		expected := "upload request too large"
		if err.Error() != expected {
			t.Fatalf("expected buildPostHRSForm to have error %v, got %v", expected, err)
		}
	})

	t.Run("unknown part in form", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 10)
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `{}`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		var err error
		if fw, err = w.CreateFormField("UNKNOWN"); err != nil {
			t.Fatalf("failed to create test multipart record field: %q", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %q", err)
		}
		if fw, err = w.CreateFormFile("someotherfile", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %q", err)
		}
		if _, err = io.Copy(fw, bytes.NewReader(sampleJpeg)); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
		w.Close()

		_, _, err = hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			multipart.NewReader(&b, w.Boundary()),
		)
		//expect error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to unknown form field")
		}
		expected := "unexpected part in multipart form"
		if err.Error() != expected {
			t.Fatalf("expected buildPostHRSForm to have error %v, got %v", expected, err)
		}
	})

	t.Run("unnamed file in form", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 1000)
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `{}`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		var err error
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %q", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %q", err)
		}
		if fw, err = w.CreateFormFile("someotherfile", ""); err != nil {
			t.Fatalf("failed to create test multipart file: %q", err)
		}
		if _, err = io.Copy(fw, bytes.NewReader(sampleJpeg)); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
		w.Close()

		_, _, err = hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			multipart.NewReader(&b, w.Boundary()),
		)
		//expect error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to unnamed file")
		}
		expected := "unexpected part in multipart form"
		if err.Error() != expected {
			t.Fatalf("expected buildPostHRSForm to have error %v, got %v", expected, err)
		}
	})

	t.Run("disallowed file type", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 1000)
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `{}`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		var err error
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %q", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %q", err)
		}
		if fw, err = w.CreateFormFile("someotherfile", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %q", err)
		}
		if _, err = io.WriteString(fw, "test file contents"); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
		w.Close()

		_, _, err = hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			multipart.NewReader(&b, w.Boundary()),
		)
		//expect error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to unknown file type")
		}
		if err.Error() != errormsgs.ERR_FILE_TYPE {
			t.Fatalf(
				"expected buildPostHRSForm to have error %v, got %v",
				errormsgs.ERR_FILE_TYPE,
				err,
			)
		}
	})

	t.Run("no files", func(t *testing.T) {
		hrs.SetUploadLimits(1, 1, 1000, 1000)
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `{}`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		var err error
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %q", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %q", err)
		}
		w.Close()

		_, _, err = hrs.buildPostHRSForm(
			context.Background(),
			logrus.WithField("test", 1),
			"acct_id",
			"patient_id",
			"Babe",
			"Ruth",
			multipart.NewReader(&b, w.Boundary()),
		)
		//expect error
		if err == nil {
			t.Fatalf("expected buildPostHRSForm to throw an error due to unknown file type")
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf(
				"expected buildPostHRSForm to have error %v, got %v",
				errormsgs.ERR_INVALID_REQ_BODY,
				err,
			)
		}
	})
}

func TestGetRecordsMetadata(t *testing.T) {

	idListBody := ioutil.NopCloser(strings.NewReader(`
	[
		"id1",
	]`))

	t.Run("Request without includedFlag", func(t *testing.T) {
		ids := []string{"id1"}
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			expectedURL := fmt.Sprintf("https://%s/v1/records/id1/metadata", hrs.URL)
			checkAuthAndURL(expectedURL, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       idListBody,
			}, nil
		}

		hrs.SetDoRequest(doReqMock)
		hrs.GetRecordMetadataByIds(context.Background(), false, ids, false)
	})

	t.Run("Request with includedFlag", func(t *testing.T) {
		ids := []string{"id1"}
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			expectedURL := fmt.Sprintf(
				"https://%s/v1/records/id1/metadata?includeDeleted=true",
				hrs.URL,
			)
			checkAuthAndURL(expectedURL, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       idListBody,
			}, nil
		}

		hrs.SetDoRequest(doReqMock)
		hrs.GetRecordMetadataByIds(context.Background(), false, ids, true)
	})
}
