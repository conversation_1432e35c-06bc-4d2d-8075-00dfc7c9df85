package hrs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"strconv"
	"strings"

	"github.com/kennygrant/sanitize"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// To prevent uploads from becoming very large, set defaults on number of files per record and records per upload
// 5 records fits nicely on a screen in the frontend, and 15 files/record should be adequate for a single document
const DefaultMaxFileNumber = 15
const DefaultMaxRecordNumber = 5

// Set limits on json and file size to help prevent abuse.
// 500KB is loads of space for json, but not a big memory suck
// 8MB per file makes sure we can support uncompressed phone photos
const DefaultMaxJsonSize = 500 << 10 //500KB
const DefaultMaxFileSize = 8 << 20   //8MB

// only allow pdf, png, jpeg
var AllowedFileTypes = []string{
	"application/pdf",
	"image/png",
	"image/jpeg",
}

type KVString struct {
	Key   string
	Value string
}

type HlthRecSvcUser struct {
	URL                 string `json:"url"`
	ApiKey              string `json:"api_key"`
	ApiKeySecName       string `json:"api_key_sec_name"`
	Client              *http.Client
	doRequest           requestwithcontext.RequestWithCtx
	maxRecordsPerUpload int
	maxFilesPerRecord   int
	maxJsonSize         int
	maxFileSize         int
}

func (hrs *HlthRecSvcUser) SetDoRequest(doer requestwithcontext.RequestWithCtx) {
	if doer == nil {
		hrs.doRequest = requestwithcontext.DoRequestWithCtx
	} else {
		hrs.doRequest = doer
	}
}

func (hrs *HlthRecSvcUser) SetUploadLimits(
	recordsPerUpload int,
	filesPerRec int,
	jsonSize int,
	fileSize int,
) {
	hrs.maxRecordsPerUpload = recordsPerUpload
	hrs.maxFilesPerRecord = filesPerRec
	hrs.maxJsonSize = jsonSize
	hrs.maxFileSize = fileSize
}

// GetRecordsList returns the FHIR record ID's for the passed in acct or pt id
// pass in a "" value for the pt ID if to query by acct ID only.
func (hrs *HlthRecSvcUser) GetRecordsList(
	ctx context.Context,
	acctId string,
	patientId string,
) ([]string, error) {
	qsp := []KVString{}
	if patientId != "" {
		qsp = append(qsp, KVString{Key: "phPatientId", Value: patientId})
	}

	qsp = append(qsp, KVString{Key: "accountId", Value: acctId})
	resp, err := hrs.request(ctx, "GET", "v1/records/list", nil, qsp, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from hrs", resp.StatusCode)
	}

	var recordIdList []string
	err = json.NewDecoder(resp.Body).Decode(&recordIdList)
	if err != nil {
		return nil, err
	}

	return recordIdList, nil
}

// GetRecordsByUserAndProfile returns the metadata for every record for the passed in user ID or profile ID.
func (hrs *HlthRecSvcUser) GetRecordsByUserAndProfile(
	ctx context.Context,
	acctId string,
	patientId string,
) ([]json.RawMessage, error) {
	qsp := []KVString{
		{Key: "phPatientId", Value: patientId},
		{Key: "accountId", Value: acctId},
	}

	resp, err := hrs.request(ctx, "GET", "v1/records", nil, qsp, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v status from hrs", resp.StatusCode)
	}

	var jl []json.RawMessage
	err = json.NewDecoder(resp.Body).Decode(&jl)
	if err != nil {
		return nil, fmt.Errorf("could not decode response from HRS: %s", err)
	}

	return jl, nil
}

// GetRecordsByAcctAndPatient returns the metadata for every record for the passed in account ID or patient ID.
func (hrs *HlthRecSvcUser) GetRecordsByAcctAndPatient(
	ctx context.Context,
	acctId string,
	patientId string,
) ([]json.RawMessage, error) {
	qsp := []KVString{
		{Key: "phPatientId", Value: patientId},
		{Key: "accountId", Value: acctId},
	}

	resp, err := hrs.request(ctx, "GET", "v1/records", nil, qsp, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v status from hrs", resp.StatusCode)
	}

	var jl []json.RawMessage
	err = json.NewDecoder(resp.Body).Decode(&jl)
	if err != nil {
		return nil, fmt.Errorf("could not decode response from HRS: %s", err)
	}

	return jl, nil
}

// PostRecords attempts to create a new health record given a multipart form.
// The newly created records metadata will be returned minus the last_modified field.
func (hrs *HlthRecSvcUser) PostRecords(
	ctx context.Context,
	lg *logrus.Entry,
	acctId string,
	patientId string,
	firstName string,
	lastName string,
	reader *multipart.Reader,
) ([]json.RawMessage, error) {
	buf, boundary, err := hrs.buildPostHRSForm(
		ctx,
		lg,
		acctId,
		patientId,
		firstName,
		lastName,
		reader,
	)
	if err != nil {
		return nil, err
	}
	resp, err := hrs.request(
		ctx,
		"POST",
		"v1/records",
		buf,
		nil,
		[]KVString{{Key: "Content-Type", Value: "multipart/form-data; boundary=" + boundary}},
	)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v status from hrs", resp.StatusCode)
	}

	var jl []json.RawMessage
	err = json.NewDecoder(resp.Body).Decode(&jl)
	if err != nil {
		return nil, fmt.Errorf("could not decode response from HRS: %s", err)
	}

	return jl, nil
}

// TODO: post profiles migration clean-up
// no longer need to send regional ids
func (hrs *HlthRecSvcUser) PutRecord(
	ctx context.Context,
	lg *logrus.Entry,
	acctId string,
	patientId string,
	firstName string,
	lastName string,
	recordId string,
	reader *multipart.Reader,
) (json.RawMessage, error) {
	buf, boundary, err := hrs.buildPostHRSForm(
		ctx,
		lg,
		acctId,
		patientId,
		firstName,
		lastName,
		reader,
	)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/records/%s", recordId)
	resp, err := hrs.request(
		ctx,
		"PUT",
		url,
		buf,
		nil,
		[]KVString{{Key: "Content-Type", Value: "multipart/form-data; boundary=" + boundary}},
	)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v status from hrs", resp.StatusCode)
	}

	var jl json.RawMessage
	err = json.NewDecoder(resp.Body).Decode(&jl)
	if err != nil {
		return nil, fmt.Errorf("could not decode response from HRS: %s", err)
	}

	return jl, nil
}

// GetHealthRecordThumbnailById returns a PNG of the first file of a record.
// If the first file for a record is a PDF, a PNG of the first page will be returned.
// Calling function is responsible for closing the returned body
func (hrs *HlthRecSvcUser) GetHealthRecordThumbnailById(
	ctx context.Context,
	lg *logrus.Entry,
	recordID string,
) (io.ReadCloser, error) {
	resp, err := hrs.request(
		ctx,
		"GET",
		fmt.Sprintf("v1/records/%s/thumbnail", recordID),
		nil,
		[]KVString{},
		nil,
	)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			lg.WithError(err).Error("could not read response from hlth rec svc")
		} else {
			lg.WithError(err).Error(string(body))
		}
		return nil, fmt.Errorf("got %v back from hrs", resp.StatusCode)
	}

	return resp.Body, err
}

// GetHealthRecordsByIds returns a zip folder with all of the health records specified by their FHIR record ID's.
func (hrs *HlthRecSvcUser) GetHealthRecordsByIds(
	ctx context.Context,
	lg *logrus.Entry,
	recordIDs []string,
) ([]byte, error) {
	resp, err := hrs.request(
		ctx,
		"GET",
		"v1/records/download",
		nil,
		[]KVString{{Key: "hrids", Value: strings.Join(recordIDs, ",")}},
		nil,
	)
	if err != nil {
		return []byte{}, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		lg.WithError(err).Error("could not read response from hlth rec svc")
		return []byte{}, err
	}

	if resp.StatusCode != http.StatusOK {
		lg.WithError(err).Error(string(body))
		return []byte{}, fmt.Errorf("got %v back from hrs", resp.StatusCode)
	}

	return body, err
}

// GetHealthRecordById returns a zip folder with all of the files that are part of the given health record.
// The metadata will also be returned as a string that's encoded in JSON.
// Calling function is responsible for closing the returned response body
func (hrs *HlthRecSvcUser) GetHealthRecordById(
	ctx context.Context,
	lg *logrus.Entry,
	recordID string,
) (io.ReadCloser, string, error) {
	resp, err := hrs.request(
		ctx,
		"GET",
		fmt.Sprintf("v1/records/%s", recordID),
		nil,
		[]KVString{},
		nil,
	)
	if err != nil {
		return nil, "", err
	}

	if resp.StatusCode != http.StatusOK {
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			lg.WithError(err).Error("could not read response from hlth rec svc")
			return nil, "", err
		}
		lg.WithError(err).Error(string(body))
		return nil, "", fmt.Errorf("got %v back from hrs", resp.StatusCode)
	}

	return resp.Body, resp.Header.Get("PocketHealth-API-Result"), err
}

// GetMetadataByIds returns the JSON metadata for all given record IDs
func (hrs *HlthRecSvcUser) GetMetadataByIds(
	ctx context.Context,
	hrIds []string,
) ([]coreapi.Record, error) {
	resp, err := hrs.request(
		ctx,
		"GET",
		"v1/records/metadata",
		nil,
		[]KVString{{Key: "hrids", Value: strings.Join(hrIds, ",")}},
		nil,
	)
	metadata := make([]coreapi.Record, 0)

	if err != nil {
		return metadata, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		//if error here, just won't return error message
		body, _ := ioutil.ReadAll(resp.Body)

		return metadata, fmt.Errorf("got status %d from hrs: %s", resp.StatusCode, (body))
	}

	var records []RecordResponse
	err = json.NewDecoder(resp.Body).Decode(&records)
	if err != nil {
		return metadata, err
	}
	for _, record := range records {
		record.Record.FHIRId = record.Id
		record.Record.PatientId = record.PatientId
		metadata = append(metadata, record.Record)
	}

	return metadata, nil
}

// GetRecordMetadataByIds returns the JSON metadata for whatever record ID's it can retrieve successfully.
func (hrs *HlthRecSvcUser) GetRecordMetadataByIds(
	ctx context.Context,
	userAccess bool,
	hrIds []string,
	includeDeleted bool,
) []coreapi.Record {
	recordList := make([]coreapi.Record, 0)
	for _, id := range hrIds {
		record, err := hrs.getRecordMetadaById(ctx, id, includeDeleted)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithError(err).
				Errorf("could not get json for hr id %s", id)
		} else {
			if !userAccess {
				//tags and notes are exposed to users only (ie, not share accessors)
				record.Tag = ""
				record.Description = ""
			}
			recordList = append(recordList, record)
		}
	}

	return recordList
}

// DeleteRecordById returns the JSON metadata for whatever record ID's it can retrieve successfully.
func (hrs *HlthRecSvcUser) DeleteRecordById(
	ctx context.Context,
	id string,
) error {
	url := fmt.Sprintf("v1/records/%s", id)
	resp, err := hrs.request(ctx, "DELETE", url, nil, nil, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return errors.New(errmsg.ERR_NOT_FOUND)
	}
	if resp.StatusCode != http.StatusOK {
		//if error here, just won't return error message
		body, _ := ioutil.ReadAll(resp.Body)

		return fmt.Errorf("got status %d from hrs: %s", resp.StatusCode, (body))
	}

	return nil
}

// CreateMyChartIntegration sends a message to HRS to kickoff importing a patient's files from MyChart
func (hrs *HlthRecSvcUser) CreateMyChartIntegration(
	ctx context.Context,
	acctId string,
	ptId string,
	mc *coreapi.MyChartIntegrationRequest,
) error {
	request := DetailMyChartIntegrationRequest{
		AccountId:  acctId,
		PatientId:  ptId,
		AccessCode: mc.AccessCode,
		OrgId:      mc.OrgId,
	}
	reqBytes, err := json.Marshal(request)
	if err != nil {
		return err
	}
	url := "v1/integrations/mychart"
	resp, err := hrs.request(ctx, "POST", url, bytes.NewReader(reqBytes), nil, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("got status %d from hrs", resp.StatusCode)
	}

	return nil
}

// GenerateGailRiskResult sends the patient's answers to the Gail Model questionnaire to generate and store a risk score for breast cancer
func (hrs *HlthRecSvcUser) GenerateGailRiskResult(
	ctx context.Context,
	acctId string,
	ptId string,
	gr *coreapi.GailQuestionnairePatientResponses,
) ([]byte, error) {
	reqBytes, err := json.Marshal(gr)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("v1/account/%s/patient/%s/questionnaire/gail", acctId, ptId)
	resp, err := hrs.request(ctx, "POST", url, bytes.NewReader(reqBytes), nil, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("got status %d from hrs", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body %v", err)
	}

	return body, nil
}

// getRecordMetadaById returns the JSON metadata given the record ID
func (hrs *HlthRecSvcUser) getRecordMetadaById(
	ctx context.Context,
	hrId string,
	includeDeleted bool,
) (coreapi.Record, error) {
	url := fmt.Sprintf("v1/records/%s/metadata", hrId)
	if includeDeleted {
		url = url + "?includeDeleted=true"
	}
	resp, err := hrs.request(ctx, http.MethodGet, url, nil, nil, nil)
	if err != nil {
		return coreapi.Record{}, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return coreapi.Record{}, errors.New(errmsg.ERR_NOT_FOUND)
	}
	if resp.StatusCode != http.StatusOK {
		//if error here, just won't return error message
		body, _ := ioutil.ReadAll(resp.Body)

		return coreapi.Record{}, fmt.Errorf("got status %d from hrs: %s", resp.StatusCode, (body))
	}

	var rec RecordResponse
	err = json.NewDecoder(resp.Body).Decode(&rec)
	if err != nil {
		return coreapi.Record{}, err
	}
	rec.Record.FHIRId = rec.Id
	rec.Record.PatientId = rec.PatientId
	rec.Record.DeletedAt = rec.DeletedAt
	return rec.Record, nil
}

func (hrs *HlthRecSvcUser) GetRecordsByType(
	ctx context.Context,
	acctId string,
	ptId string,
	resourceType string,
	limit int) ([]coreapi.Record, error) {
	url := fmt.Sprintf("v1/records/type/%s", resourceType)
	queryParams := []KVString{
		{
			Key:   "accountId",
			Value: acctId,
		},
		{
			Key:   "phPatientId",
			Value: ptId,
		},
		{
			Key:   "limit",
			Value: strconv.Itoa(limit),
		},
	}
	resp, err := hrs.request(ctx, "GET", url, nil, queryParams, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got status %d from hrs", resp.StatusCode)
	}

	var recordResp []RecordResponse
	records := make([]coreapi.Record, 0)
	err = json.NewDecoder(resp.Body).Decode(&recordResp)
	if err != nil {
		return nil, fmt.Errorf("error reading response body %v", err)
	}

	for _, record := range recordResp {
		record.Record.FHIRId = record.Id
		record.Record.PatientId = record.PatientId
		records = append(records, record.Record)
	}

	return records, nil
}

func (hrs *HlthRecSvcUser) request(
	ctx context.Context,
	verb string,
	path string,
	body io.Reader,
	queryParams []KVString,
	headers []KVString,
) (*http.Response, error) {
	req, err := http.NewRequest(verb, fmt.Sprintf("https://%s/%s", hrs.URL, path), body)
	if err != nil {
		return nil, fmt.Errorf("could not create hrs request: %v", err)
	}
	req.Header.Add("Authorization", hrs.ApiKey)
	for _, kv := range headers {
		req.Header.Add(kv.Key, kv.Value)
	}

	if len(queryParams) > 0 {
		q := req.URL.Query()
		for _, kv := range queryParams {
			q.Add(kv.Key, kv.Value)
		}
		req.URL.RawQuery = q.Encode()
	}

	resp, err := hrs.doRequest(ctx, hrs.Client, req)
	if err != nil {
		return nil, fmt.Errorf("could not do hrs request: %v", err)
	}
	return resp, nil
}

func (hrs *HlthRecSvcUser) buildPostHRSForm(
	ctx context.Context,
	lg *logrus.Entry,
	acctId string,
	patientId string,
	firstName string,
	lastName string,
	reader *multipart.Reader,
) (*bytes.Buffer, string, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	totalFilesUploaded := 0

	var err error
	//allow max x records, y files/record + 1 part for json metadata
	for pn := 1; pn <= 1+(hrs.maxRecordsPerUpload*hrs.maxFilesPerRecord); pn += 1 {
		var fw io.Writer
		var part *multipart.Part
		part, err = reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
		}
		defer part.Close()

		lgPn := lg.WithFields(logrus.Fields{
			"pn":        pn,
			"part_name": part.FileName(),
		})
		if part.FileName() == "" {
			if part.FormName() == "records" {
				//should contain record metadata in json
				//limit the size to prevent abuse
				//if the json is longer than that, it won't throw an error but the json will be malformed and not unmarshal below
				metadataBytes, err := ioutil.ReadAll(io.LimitReader(part, int64(hrs.maxJsonSize)))
				if err != nil {
					lgPn.WithError(err).Error("could not read metadata")
					return nil, "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
				}

				//unmarshal into NewRecords object to do some validation and add on pt info
				recordMetadata := coreapi.NewRecords{}
				if err = json.Unmarshal(metadataBytes, &recordMetadata); err != nil {
					lgPn.WithError(err).Error("could not decode metadata")
					return nil, "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
				}

				if len(recordMetadata.Records) > hrs.maxRecordsPerUpload {
					lgPn.Error("too many records")
					return nil, "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
				}
				for i := range recordMetadata.Records {

					if len(recordMetadata.Records[i].Filenames) > hrs.maxFilesPerRecord {
						lgPn.Error("too many files in one record")
						return nil, "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
					}

					for j, filename := range recordMetadata.Records[i].Filenames {
						recordMetadata.Records[i].Filenames[j] = sanitize.Name(filename)
					}
				}
				recordMetadata.Patient = coreapi.HRPatient{
					AccountId:           acctId,
					PhPatientId:         patientId,
					FirstAndMiddleNames: firstName,
					LastName:            lastName,
				}

				metadataBytes, err = json.Marshal(recordMetadata)
				if err != nil {
					return nil, "", err
				}
				if fw, err = w.CreateFormField("recordMetadata"); err != nil {
					lgPn.WithError(err)
					return nil, "", err
				}
				if _, err = io.WriteString(fw, string(metadataBytes)); err != nil {
					lgPn.WithError(err)
					return nil, "", err
				}
				continue
			} else {
				err := fmt.Errorf("unexpected part in multipart form")
				lgPn.WithError(err).Error("filename blank and formname not 'records'")
				return nil, "", err
			}
		}

		fileBytes := make([]byte, hrs.maxFileSize)
		//only read in the max file size.
		bytesRead, fileReadErr := io.ReadFull(part, fileBytes)
		//error checking looks counterintuitive here because ReadFull returns an unexpectedEOF if the file is smaller than maxFileBytes (which is what we want)
		//and doesn't return an error if it doesn't read the whole file
		if fileReadErr == nil {
			err = errors.New(errmsg.ERR_FILE_SIZE)
			lg.WithError(err)
			return nil, "", err
		}
		fileBytes = fileBytes[:bytesRead] //don't want to store maxFileSize if its less than that
		mimetype := http.DetectContentType(fileBytes)
		allowed := false
		for _, t := range AllowedFileTypes {
			if t == mimetype {
				allowed = true
				break
			}
		}
		if !allowed {
			err = errors.New(errmsg.ERR_FILE_TYPE)
			lg.WithField("mimetype", mimetype).WithError(err).Error("file type not allowed")
			return nil, "", err
		}
		lgPn.WithFields(logrus.Fields{
			"file_size": len(fileBytes),
			"mime_type": mimetype,
		}).Info("HR upload stats")

		// the form name matches what's set in the records metadata above
		// whereas the filename is the actual name of the source file. both
		// can be sanitized!
		if fw, err = w.CreateFormFile(sanitize.Name(part.FormName()), sanitize.Name(part.FileName())); err != nil {
			lgPn.WithError(err)
			return nil, "", err
		}

		_, err = io.Copy(fw, bytes.NewReader(fileBytes))
		if err != nil {
			lgPn.WithError(err)
			return nil, "", err
		}

		totalFilesUploaded += 1
	}

	//left for loop before reading whole request body
	if err != io.EOF {
		//then body was too large
		err = w.Close()
		if err != nil {
			lg.WithError(err).Error("error closing multipart writer on large request")
		}
		return nil, "", fmt.Errorf("upload request too large")
	}

	if totalFilesUploaded > hrs.maxFilesPerRecord*hrs.maxRecordsPerUpload {
		err = errors.New(errmsg.ERR_NUMBER_OF_FILES)
		lg.WithError(err).Error("too many files")
		return nil, "", err
	}
	if totalFilesUploaded == 0 {
		err = errors.New(errmsg.ERR_INVALID_REQ_BODY)
		lg.WithError(err).Error("must include one+ file")
		return nil, "", err
	}

	// Don't forget to close the multipart writer.
	err = w.Close()
	if err != nil {
		lg.WithError(err).Error("error closing multipart writer")
		return nil, "", err
	}

	return &b, w.Boundary(), nil
}

// the CreateFormFile function that comes with multipart package always sets content type to application/octet-stream
// This is exactly the same except it allows the caller to set contentType
func CreateFormFile(
	w *multipart.Writer,
	fieldname, filename, contentType string,
) (io.Writer, error) {
	h := make(textproto.MIMEHeader)
	h.Set("Content-Disposition",
		fmt.Sprintf(`form-data; name="%s"; filename="%s"`,
			stringhelpers.EscapeQuotes(fieldname), stringhelpers.EscapeQuotes(filename)))
	h.Set("Content-Type", contentType)
	return w.CreatePart(h)
}

func (hrs *HlthRecSvcUser) SearchMyChartOrgs(
	ctx context.Context,
	query string,
) ([]coreapi.FHIROrg, error) {
	orgs := []coreapi.FHIROrg{}

	queryParams := []KVString{
		{Key: "query", Value: query},
	}

	resp, err := hrs.request(ctx, "GET", "v1/integrations/mychart/org", nil, queryParams, nil)
	if err != nil {
		return []coreapi.FHIROrg{}, err
	}

	err = json.NewDecoder(resp.Body).Decode(&orgs)
	if err != nil {
		return nil, err
	}

	return orgs, nil
}
