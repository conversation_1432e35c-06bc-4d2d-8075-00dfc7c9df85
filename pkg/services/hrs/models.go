package hrs

import "gitlab.com/pockethealth/coreapi/pkg/coreapi"

// from hrs api
type RecordResponse struct {
	Id string `json:"id,omitempty"`

	LastModified string `json:"lastModified,omitempty"`

	AccountId string `json:"acctId,omitempty"`

	PatientId string `json:"phPatientId,omitempty"`

	Record coreapi.Record `json:"record,omitempty"`

	DeletedAt string `json:"deletedAt,omitempty"`
}

type DetailMyChartIntegrationRequest struct {
	AccountId string `json:"account_id"`

	PatientId string `json:"patient_id"`

	AccessCode string `json:"code"`
	OrgId      int    `json:"org_id"`
}
