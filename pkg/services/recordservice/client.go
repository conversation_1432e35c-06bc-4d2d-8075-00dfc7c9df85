// Package recordservice defines a client that calls
// recordservice.
//
// It started off as a hand written client and evolved in to a client that
// wraps a generated client. Moving forward, any new client calls should
// use the generated client instead of adding to the corresponding
// interface. Once all the interface calls have been migrated to the
// generated client, this package can be deprecated and simply replaced by
// the generated recordservice Invoker interface.
package recordservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"

	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services"
	"gitlab.com/pockethealth/coreapi/pkg/util/examutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

type RecordServiceClient struct {
	*services.BaseClient
	*generatedrecordservice.Client
}

func NewClient(url string, apiUser string, apiKey string) *RecordServiceClient {
	baseClient := services.NewClient(
		url,
		apiUser,
		apiKey,
		httpclient.NewHTTPClient(&http.Client{}, nil),
		httpclient.Basic,
	)
	genClient, err := generatedrecordservice.NewClient(url, NewSecuritySource(apiUser, apiKey))
	if err != nil {
		log.Fatalf("failed to create record service client. error: %+v\n", err)
	}

	return &RecordServiceClient{
		BaseClient: baseClient,
		Client:     genClient,
	}
}

// generate a meddream token based on a token request
func (rc *RecordServiceClient) GetMeddreamToken(
	ctx context.Context,
	tokenRequest TokenRequest,
) (string, error) {
	reqBody, err := json.Marshal(tokenRequest)
	if err != nil {
		return "", fmt.Errorf("marshalling token request failed: %v", err)
	}
	body, code, err := rc.Post(
		ctx,
		"/v1/meddream/generate",
		reqBody,
		nil,
		[]int{http.StatusOK, http.StatusBadRequest},
	)

	if err != nil || code != http.StatusOK {
		return "", fmt.Errorf(
			"generating token from record service failed: %s - %v",
			http.StatusText(code),
			err,
		)
	}
	var tokenStr string
	err = json.Unmarshal(body, &tokenStr)
	if err != nil {
		return "", fmt.Errorf("unpacking response failed: %v", err)
	}
	return tokenStr, nil
}

func (rc *RecordServiceClient) GetStudies(
	ctx context.Context,
	accountID string,
	includeReports bool,
	includeInstances bool,
	activated *bool,
	UUIDs []string,
) ([]PatientStudy, error) {
	queryParams := generatedrecordservice.V1PatientsAccountIDStudiesGetParams{
		AccountID:        accountID,
		IncludeReports:   generatedrecordservice.NewOptBool(includeReports),
		IncludeInstances: generatedrecordservice.NewOptBool(includeInstances),
		UUID:             UUIDs,
	}

	if activated != nil {
		queryParams.Activated = generatedrecordservice.NewOptBool(*activated)
	}

	response, err := rc.V1PatientsAccountIDStudiesGet(ctx, queryParams)
	if err != nil {
		return []PatientStudy{}, err
	}

	body, ok := response.(*generatedrecordservice.V1PatientsAccountIDStudiesGetOKApplicationJSON)
	if !ok {
		return []PatientStudy{}, errors.New("failed to query record service")
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return []PatientStudy{}, err
	}

	var studies []PatientStudy
	err = json.Unmarshal(bodyBytes, &studies)
	if err != nil {
		return []PatientStudy{}, err
	}
	return studies, nil
}

func (rc *RecordServiceClient) GetPatientStudiesWithUploadStatus(
	ctx context.Context,
	accountID string,
) (models.RecordUploadStatuses, error) {
	body, code, err := rc.Get(
		ctx,
		fmt.Sprintf("/v1/patients/%s/studies/upload-status", accountID),
		nil,
		[]int{http.StatusOK, http.StatusBadRequest, http.StatusUnauthorized, http.StatusBadRequest},
	)
	if err != nil || code != http.StatusOK {
		return []models.RecordUploadStatus{}, fmt.Errorf(
			"failed to get studies with status for patient: %s - %v",
			http.StatusText(code),
			err,
		)
	}
	var studies []generatedrecordservice.UploadStatus
	err = json.Unmarshal(body, &studies)
	if err != nil {
		return []models.RecordUploadStatus{}, fmt.Errorf("unpacking response failed: %v", err)
	}

	// translate upload status from recordService to internal upload status
	records := make([]models.RecordUploadStatus, len(studies))
	for i := range studies {
		records = append(records, models.ToRecordUploadStatus(studies[i]))
	}
	// filter out any transfer-based studies without unlock status
	filteredRecords := examutils.GetRecordsWithUnlockStatus(ctx, records)
	return filteredRecords, nil
}

func (rc *RecordServiceClient) MatchPatientStudies(
	ctx context.Context,
	accountID string,
	UUIDs []string,
) (bool, error) {
	recordServiceURL := fmt.Sprintf("/v1/patients/%s/studies/match", accountID)

	// Bake repeated query parameters into the target URL
	if len(UUIDs) > 0 {
		queryParameters := []string{}
		for _, UUID := range UUIDs {
			queryParameters = append(queryParameters, fmt.Sprintf("uuid=%s", url.QueryEscape(UUID)))
		}
		recordServiceURL = fmt.Sprintf(
			"%s?%s",
			recordServiceURL,
			strings.Join(queryParameters, "&"),
		)
	}

	body, code, err := rc.Get(
		ctx,
		recordServiceURL,
		nil,
		[]int{http.StatusOK, http.StatusUnauthorized},
	)
	if err != nil || code != http.StatusOK {
		return false, fmt.Errorf(
			"failed to match studies with provided accountID: %s - %v",
			http.StatusText(code),
			err,
		)
	}
	var match bool
	err = json.Unmarshal(body, &match)
	if err != nil {
		return false, fmt.Errorf("unpacking response failed: %v", err)
	}
	return match, nil
}

func (rc *RecordServiceClient) PostActivateStudies(
	ctx context.Context,
	accountID string,
	activationKey coreapi.ActivationKey,
	studyAvailabilityStatuses map[string]models.UnlockStatus,
	orderID string,
) ([]PatientStudy, error) {
	activateStudyAvailabilityStatuses := make(
		map[string]generatedrecordservice.StudyAvailabilityStatus,
		len(studyAvailabilityStatuses),
	)
	for uuid := range studyAvailabilityStatuses {
		activateStudyAvailabilityStatuses[uuid] = generatedrecordservice.StudyAvailabilityStatus(
			studyAvailabilityStatuses[uuid],
		)
	}

	payload := generatedrecordservice.ActivateStudy{
		DateOfBirth: generatedrecordservice.NewOptString(activationKey.DateOfBirth),
		OrderId:     generatedrecordservice.NewOptString(orderID),
		StudyAvailabilityStatuses: generatedrecordservice.NewOptActivateStudyStudyAvailabilityStatuses(
			activateStudyAvailabilityStatuses,
		),
	}
	response, err := rc.V1PatientsAccountIDStudiesActivatePost(
		ctx,
		generatedrecordservice.NewOptActivateStudy(payload),
		generatedrecordservice.V1PatientsAccountIDStudiesActivatePostParams{
			AccountID: accountID,
		},
	)
	if err != nil {
		return []PatientStudy{}, err
	}

	body, ok := response.(*generatedrecordservice.V1PatientsAccountIDStudiesActivatePostOKApplicationJSON)
	if !ok {
		return []PatientStudy{}, err
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return []PatientStudy{}, err
	}

	var patientStudies []PatientStudy
	err = json.Unmarshal(bodyBytes, &patientStudies)
	if err != nil {
		return []PatientStudy{}, fmt.Errorf("unpacking response failed: %v", err)
	}
	return patientStudies, nil
}
