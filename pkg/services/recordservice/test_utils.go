package recordservice

import (
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

const expectedExam = `
{
   "series":[
      {
         "seriesId":"*******.5",
         "description":"Bicep lateral",
         "instances":[
            {
               "imageId":"instanceUUID1",
               "token":"dummytoken"
            },
            {
               "imageId":"instanceUUID2",
               "token":"dummytoken"
            }
         ]
      },
      {
         "seriesId":"*******.5",
         "description":"Bicep lateral",
         "instances":[
            {
               "imageId":"instanceUUID3",
               "token":"dummytoken"
            },
            {
               "imageId":"instanceUUID4",
               "token":"dummytoken"
            }
         ]
      }
   ],
   "reports":[
      {
         "reportId":"reportUUID",
         "activated":false,
         "size":2000000,
         "patientName":{
            
         },
         "definitions":true
      },
      {
         "reportId":"reportUUID2",
         "activated":false,
         "size":2000000,
         "patientName":{
            
         },
         "definitions":true,
		 "protocol":"RHO"
      }
   ],
   "uuid":"examUUID",
   "examId":"*******.5",
   "patientName":{
      "dicomName":"Semaj^Middle^James",
      "firstAndMiddleName":"Middle James",
      "lastName":"Semaj"
   },
   "examType":"CT Scan",
   "examDate":"Jan 01, 1900",
   "activated":true,
   "description":"Bicep CT",
   "dob":"1900/01/01",
   "sex":"M",
   "transferId":"transferID",
   "bodypart":"arm",
   "reportDelay":0,
   "referringPhysician":"Dr. Jim Jimmyson",
   "size":600000,
   "orgId":123,
   "facilityFunded":false,
   "modality":"CT",
   "allow_pt_png_dls":false,
   "patientId":"patientID456",
   "unlockStatus":"fullAccess"
}
`

func GetExpectedExam(
	t *testing.T,
	hasReport bool,
	hasProviderMetadata bool,
	isActivated bool,
) coreapi.Exam {
	var exam coreapi.Exam
	err := json.Unmarshal([]byte(NormalizeJSONString(expectedExam)), &exam)
	assert.NoError(t, err)

	if !hasReport || !isActivated {
		removeExamReport(&exam)
	}
	if hasProviderMetadata {
		setExamProviderMetadata(&exam)
	}
	if !isActivated {
		setExamInactivated(&exam)
		removeExamReport(&exam)
		removeSeries(&exam)
	}
	return exam
}

func removeExamReport(exam *coreapi.Exam) {
	exam.Reports = nil
}

func removeSeries(exam *coreapi.Exam) {
	exam.Series = nil
	exam.Size = 0
}

func setExamProviderMetadata(exam *coreapi.Exam) {
	exam.Provider = "Demo Medical Imaging"
	exam.OrgName = "Demo Medical Imaging"
	exam.ReportDelay = 7
}

func setExamInactivated(exam *coreapi.Exam) {
	exam.Activated = false
	exam.PatientName = models.PatientName{}
	exam.ReferringPhysician = ""
	exam.Description = ""
	exam.Dob = ""
	exam.Sex = ""
	exam.BodyPart = ""
}

func AssertSameExam(t *testing.T, a coreapi.Exam, b coreapi.Exam) {
	diff := cmp.Diff(a, b)
	if diff != "" {
		assert.Fail(
			t,
			"exams don't match",
			diff,
		)
	}
}

func NormalizeJSONString(a string) string {
	noNewlines := strings.Replace(a, "\n", "", -1)
	return strings.Replace(noNewlines, "\t", "", -1)
}

func NormalizeExam(exam coreapi.Exam) {
	for i := range exam.Series {
		for j := range exam.Series[i].Instances {
			if exam.Series[i].Instances[j].Token != "" {
				exam.Series[i].Instances[j].Token = "dummytoken"
			}
		}
	}
}

func NormalizeExamRaw(examRaw coreapi.ExamRaw) {
	for i := range examRaw.Series {
		for j := range examRaw.Series[i].Instances {
			if examRaw.Series[i].Instances[j].Token != "" {
				examRaw.Series[i].Instances[j].Token = "dummytoken"
			}
		}
	}
}

// Create a unique dummy study to be used for all queries
// This study has 2 series
// Series1 has 2 instances
// Series2 has 3 instances
func FormatPatientStudy(
	activated bool,
	availabilityStatus PatientStudyAvailabilityStatus,
	hasReport bool,
	instanceUploadProgressPercent int64,
) PatientStudy {
	reports := []Report{}
	if hasReport {
		reports = []Report{
			{
				UUID:            "reportUUID",
				FileSizeBytes:   2_000_000,
				EncapsulatedPDF: true,
			},
			{
				UUID:            "reportUUID2",
				FileSizeBytes:   2_000_000,
				EncapsulatedPDF: true,
				Type:            "RHO",
			},
		}
	}

	var activatedTimestamp time.Time
	if activated {
		// normalize time object so that it is returned from the DB in the same format
		activatedTimestamp, _ = time.Parse(
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05Z",
		)
	}

	return PatientStudy{
		AccountID:                     "accountID123",
		PatientID:                     "patientID456",
		OrganizationID:                123,
		ActivatedTimestamp:            activatedTimestamp,
		UUID:                          "examUUID",
		AvailabilityStatus:            availabilityStatus,
		OrderID:                       "orderID",
		TransferID:                    "transferID",
		InstanceUploadProgressPercent: instanceUploadProgressPercent,
		DicomStudyTags: DicomStudyTags{
			StudyInstanceUID:   "*******.5",
			StudyDate:          "********",
			StudyDescription:   "Bicep CT",
			AccessionNumber:    "ACD0453",
			ReferringPhysician: "Jimmyson^Jim",
			Modality:           "CT",
			BodyPart:           "arm",
		},
		DicomPatientTags: DicomPatientTags{
			PatientName:            "Semaj^Middle^James",
			PatientID:              "123",
			PatientBirthDate:       "********",
			PatientSex:             "M",
			PatientTelephoneNumber: "**********",
		},
		Series: []DicomSeries{
			{
				DicomTags: DicomSeriesTags{
					SeriesInstanceUID: "*******.5",
					SeriesNumber:      "1",
					SeriesDescription: "Bicep lateral",
				},
				Instances: []DicomInstance{
					{
						UUID: "instanceUUID1",
						DicomTags: DicomInstanceTags{
							SOPInstanceUID:      "432.234.234.234",
							Protocol:            "CT Bicep",                  // Sample protocol name
							InstanceNumber:      "5",                         // Arbitrary instance number
							ImageType:           "ORIGINAL\\PRIMARY",         // Common DICOM image type
							SOPClassUID:         "1.2.840.10008.*******.1.2", // CT Image Storage SOP Class UID
							ContentDate:         "20060102",
							ContentTime:         "150405",
							AcquisitionDate:     "20060102",
							AcquisitionTime:     "150405",
							ViewPosition:        "AP",                      // Anterior-Posterior
							ImagePosition:       "-100\\-200\\-150",        // Example coordinates
							ImageOrientation:    "1\\0\\0\\0\\1\\0",        // Example orientation
							FrameOfReferenceUID: "1.2.840.10008.*******.1", // Example frame UID
							ImageLaterality:     "R",                       // Right side
							NumberOfFrames:      "1",                       // Single frame
							Rows:                "512",                     // Typical CT/MRI resolution
							Columns:             "512",
							PixelSpacing:        "0.5\\0.5",               // 0.5 mm spacing
							TransferSyntaxUID:   "1.2.840.10008.********", // JPEG 2000 Transfer Syntax UID
						},
						FileSizeBytes: 150_000,
					},
					{
						UUID: "instanceUUID2",
						DicomTags: DicomInstanceTags{
							SOPInstanceUID:      "2.23.23.123.12",
							Protocol:            "CT Bicep",                  // Sample protocol name
							InstanceNumber:      "6",                         // Arbitrary instance number
							ImageType:           "ORIGINAL\\PRIMARY",         // Common DICOM image type
							SOPClassUID:         "1.2.840.10008.*******.1.2", // CT Image Storage SOP Class UID
							ContentDate:         "20060102",
							ContentTime:         "150405",
							AcquisitionDate:     "20060102",
							AcquisitionTime:     "150405",
							ViewPosition:        "AP",                      // Anterior-Posterior
							ImagePosition:       "-100\\-200\\-150",        // Example coordinates
							ImageOrientation:    "1\\0\\0\\0\\1\\0",        // Example orientation
							FrameOfReferenceUID: "1.2.840.10008.*******.1", // Example frame UID
							ImageLaterality:     "R",                       // Right side
							NumberOfFrames:      "1",                       // Single frame
							Rows:                "512",                     // Typical CT/MRI resolution
							Columns:             "512",
							PixelSpacing:        "0.5\\0.5",               // 0.5 mm spacing
							TransferSyntaxUID:   "1.2.840.10008.********", // JPEG 2000 Transfer Syntax UID
						},
						FileSizeBytes: 150_000,
					},
				},
			},
			{
				DicomTags: DicomSeriesTags{
					SeriesInstanceUID: "*******.5",
					SeriesNumber:      "2",
					SeriesDescription: "Bicep lateral",
				},
				Instances: []DicomInstance{
					{
						UUID: "instanceUUID3",
						DicomTags: DicomInstanceTags{
							SOPInstanceUID:      "432.234.234.234234",
							Protocol:            "CT Bicep",                  // Sample protocol name
							InstanceNumber:      "5",                         // Arbitrary instance number
							ImageType:           "ORIGINAL\\PRIMARY",         // Common DICOM image type
							SOPClassUID:         "1.2.840.10008.*******.1.2", // CT Image Storage SOP Class UID
							ContentDate:         "20060102",
							ContentTime:         "150405",
							AcquisitionDate:     "20060102",
							AcquisitionTime:     "150405",
							ViewPosition:        "AP",                      // Anterior-Posterior
							ImagePosition:       "-100\\-200\\-150",        // Example coordinates
							ImageOrientation:    "1\\0\\0\\0\\1\\0",        // Example orientation
							FrameOfReferenceUID: "1.2.840.10008.*******.1", // Example frame UID
							ImageLaterality:     "R",                       // Right side
							NumberOfFrames:      "1",                       // Single frame
							Rows:                "512",                     // Typical CT/MRI resolution
							Columns:             "512",
							PixelSpacing:        "0.5\\0.5",               // 0.5 mm spacing
							TransferSyntaxUID:   "1.2.840.10008.********", // JPEG 2000 Transfer Syntax UID
						},
						FileSizeBytes: 150_000,
					},
					{
						UUID: "instanceUUID4",
						DicomTags: DicomInstanceTags{
							SOPInstanceUID:      "2.23.23.123.1432",
							Protocol:            "CT Bicep",                  // Sample protocol name
							InstanceNumber:      "6",                         // Arbitrary instance number
							ImageType:           "ORIGINAL\\PRIMARY",         // Common DICOM image type
							SOPClassUID:         "1.2.840.10008.*******.1.2", // CT Image Storage SOP Class UID
							ContentDate:         "20060102",
							ContentTime:         "150405",
							AcquisitionDate:     "20060102",
							AcquisitionTime:     "150405",
							ViewPosition:        "AP",                      // Anterior-Posterior
							ImagePosition:       "-100\\-200\\-150",        // Example coordinates
							ImageOrientation:    "1\\0\\0\\0\\1\\0",        // Example orientation
							FrameOfReferenceUID: "1.2.840.10008.*******.1", // Example frame UID
							ImageLaterality:     "R",                       // Right side
							NumberOfFrames:      "1",                       // Single frame
							Rows:                "512",                     // Typical CT/MRI resolution
							Columns:             "512",
							PixelSpacing:        "0.5\\0.5",               // 0.5 mm spacing
							TransferSyntaxUID:   "1.2.840.10008.********", // JPEG 2000 Transfer Syntax UID
						},
						FileSizeBytes: 150_000,
					},
				},
			},
		},
		Reports: reports,
	}
}
