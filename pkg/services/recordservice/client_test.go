package recordservice

import (
	"context"
	"net/http"
	"testing"
)

func TestGetMeddreamToken(t *testing.T) {
	goodTokenReq := TokenRequest{
		ExamUuid:  "exists",
		StudyUid:  "123",
		AccountId: "some-account",
	}
	// set up a mock server to pretend to be record service
	mockRecSvc := MockRecordService(
		t,
		`/v1/meddream/generate`,
		goodTokenReq,
		http.StatusOK,
		`"good-token"`,
	)
	t.Cleanup(func() {
		mockRecSvc.Close()
	})

	// test the service client
	recordSvcClient := NewClient(mockRecSvc.URL, "", "")

	t.Run("gets a bad request when request body is empty", func(t *testing.T) {
		tokenStr, err := recordSvcClient.GetMeddreamToken(context.Background(), TokenRequest{})
		if err == nil {
			t.Fatalf("expected an error but got nothing")
		}
		if tokenStr != "" {
			t.Fatalf("expected an empty response but got: %s", tokenStr)
		}
	})

	t.Run("gets a token successfully", func(t *testing.T) {
		tokenStr, err := recordSvcClient.GetMeddreamToken(context.Background(), goodTokenReq)
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if tokenStr == "" {
			t.Fatal("expected a string token response but got empty")
		}
	})
}
