package recordservice

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRecordServiceStudiesToExamsRaw(t *testing.T) {
	t.Run("activated exam with report", func(t *testing.T) {
		study := FormatPatientStudy(
			true, // activated
			FULL_AVAILABILITY,
			true, // hasReport
			100,  //instanceUploadProgressPercent
		)
		examRaw, err := study.ToExamRaw()
		assert.NoError(t, err)
		NormalizeExamRaw(examRaw)

		expectedExam := GetExpectedExam(
			t,
			true,  // hasReport
			false, // hasProviderMetadata
			true,  // isActivated
		)
		AssertSameExam(
			t,
			examRaw.ToExam(context.Background()),
			expectedExam,
		)
	})

	t.Run("activated exam no report", func(t *testing.T) {
		study := FormatPatientStudy(
			true, // activated
			FULL_AVAILABILITY,
			false, // hasReport
			100,   //instanceUploadProgressPercent
		)
		examRaw, err := study.ToExamRaw()
		assert.NoError(t, err)
		NormalizeExamRaw(examRaw)

		expectedExam := GetExpectedExam(
			t,
			false, // hasReport
			false, // hasProviderMetadata
			true,  // isActivated
		)
		AssertSameExam(
			t,
			examRaw.ToExam(context.Background()),
			expectedExam,
		)
	})

	t.Run("unactivated exam with report", func(t *testing.T) {
		study := FormatPatientStudy(
			false, // activated
			FULL_AVAILABILITY,
			true, // hasReport
			100,  //instanceUploadProgressPercent
		)
		examRaw, err := study.ToExamRaw()
		assert.NoError(t, err)
		NormalizeExamRaw(examRaw)

		expectedExam := GetExpectedExam(
			t,
			true,  // hasReport
			false, // hasProviderMetadata
			false, // isActivated
		)
		AssertSameExam(
			t,
			examRaw.ToExam(context.Background()),
			expectedExam,
		)
	})

	t.Run("unactivated exam no report", func(t *testing.T) {
		study := FormatPatientStudy(
			false, // activated
			FULL_AVAILABILITY,
			false, // hasReport
			100,   //instanceUploadProgressPercent
		)
		examRaw, err := study.ToExamRaw()
		assert.NoError(t, err)
		NormalizeExamRaw(examRaw)

		expectedExam := GetExpectedExam(
			t,
			false, // hasReport
			false, // hasProviderMetadata
			false, // isActivated
		)
		AssertSameExam(
			t,
			examRaw.ToExam(context.Background()),
			expectedExam,
		)
	})
}
