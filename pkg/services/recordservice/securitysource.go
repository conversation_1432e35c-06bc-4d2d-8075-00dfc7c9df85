package recordservice

import (
	"context"

	"gitlab.com/pockethealth/coreapi/generated/services/recordservice"
)

// Only Basic auth is implemented because that's the only security needed for
// the endpoints we currently access
type securitySource struct {
	username string
	password string
}

func NewSecuritySource(username string, password string) *securitySource {
	return &securitySource{username: username, password: password}
}

func (s *securitySource) BasicAuth(
	ctx context.Context,
	operationName string,
) (recordservice.BasicAuth, error) {
	return recordservice.BasicAuth{Username: s.username, Password: s.password}, nil
}

func (*securitySource) JwtBearer(
	_ context.Context,
	_ string,
) (recordservice.JwtBearer, error) {
	return recordservice.JwtBearer{}, nil
}

func (*securitySource) ApiKey1(
	_ context.Context,
	_ string,
) (recordservice.ApiKey1, error) {
	return recordservice.ApiKey1{}, nil
}
