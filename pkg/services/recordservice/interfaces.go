package recordservice

import (
	"context"

	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

// RecordServiceClientInterface defines a client that calls recordservice. It
// started off as a hand written client and evolved in to a client that wraps a
// generated client. Moving forward, any new client calls should use the
// generated client instead of adding to this interface. Once all the interface
// calls have been migrated to the generated client, this interface can simply
// be replaced by the generated recordservice Invoker interface.
type RecordServiceClientInterface interface {
	GetMeddreamToken(ctx context.Context, tokenRequest TokenRequest) (string, error)
	GetStudies(
		ctx context.Context,
		accountID string,
		includeReports bool,
		includeInstances bool,
		activated *bool,
		UUIDs []string,
	) ([]PatientStudy, error)
	GetPatientStudiesWithUploadStatus(
		ctx context.Context,
		accountID string,
	) (models.RecordUploadStatuses, error)
	MatchPatientStudies(ctx context.Context, accountID string, UUIDs []string) (bool, error)
	PostActivateStudies(
		ctx context.Context,
		accountID string,
		activationKey coreapi.ActivationKey,
		studyAvailabilityStatuses map[string]models.UnlockStatus,
		orderID string,
	) ([]PatientStudy, error)
	generatedrecordservice.Invoker
}
