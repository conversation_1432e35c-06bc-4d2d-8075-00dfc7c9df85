package recordservice

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
)

// set up a mock server to pretend to be record service
// handles URIs '/v1/meddream/generate' and '/v1/physicians/{id}/studies/upload-status'
// returns given status and response, any ids in the uri need to be 'id'
// must be closed at the end of the test
func MockRecordService(
	t *testing.T,
	uri string,
	request any,
	status int,
	response string,
) *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch uri {
		case `/v1/meddream/generate`:
			handlePostMeddreamGenerate(t, w, r, request.(TokenRequest), status, response)
			return
		case `/v1/physicians/id/studies/upload-status`:
			writeResponse(t, w, status, response)
			return
		default:
			writeResponse(t, w, status, response)
			return
		}
	}))
}

func handlePostMeddreamGenerate(
	t *testing.T,
	w http.ResponseWriter,
	r *http.Request,
	request TokenRequest,
	status int,
	response string,
) {
	body, _ := io.ReadAll(r.Body)
	reqBody, _ := json.Marshal(request)

	switch string(body) {
	case `{}`:
	case "":
	case `{"exam_uuid":"","account_id":"","study_uid":""}`:
		w.WriteHeader(http.StatusBadRequest)
		_, err := w.Write([]byte(``))
		if err != nil {
			t.Fatal(err)
		}
	case string(reqBody):
		writeResponse(t, w, status, response)
	}
}

func writeResponse(
	t *testing.T,
	w http.ResponseWriter,
	status int,
	response string,
) {
	w.WriteHeader(status)
	_, err := w.Write([]byte(response))
	if err != nil {
		t.Fatal(err)
	}
}
