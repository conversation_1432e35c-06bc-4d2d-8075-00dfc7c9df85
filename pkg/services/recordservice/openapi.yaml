openapi: 3.0.0
info:
  title: records
  version: '1.0'
servers:
  - url: http://localhost:3000
tags:
  - name: shares
    description: Patient and Provider Shares
  - name: imaging
    description: Imaging Records
  - name: transfers
    description: Imaging Transfers
paths:
  /v0/imaging:
    get:
      summary: Get imaging exams for an account
      tags:
        - imaging
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImagingExam'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal Server Error
      operationId: get-imaging
      description: Get all the exams for an account. One of account_id, request_id, transfer_id params is required. Sorted by exam date desc.
      security:
        - apiKey1: []
      parameters:
        - schema:
            type: integer
          in: query
          required: false
          name: account_id
          description: id for the account to get exams
        - schema:
            type: integer
          in: query
          required: false
          name: request_id
          description: id for the request to get exams
        - schema:
            type: integer
            format: int32
            minimum: 0
          in: query
          name: limit
          description: limit of results to return
        - schema:
            type: integer
            format: int32
            minimum: 0
          in: query
          name: offset
          description: pagination offset
        - schema:
            type: string
          in: query
          name: offset_ids
          deprecated: true
          description: exam uuid to offset results by, should be the last exam uuid of the previous results
        - schema:
            type: string
          in: query
          name: transfer_id
          description: id for the transfer to get exams
  /v0/shares/provider/search:
    parameters: []
    post:
      summary: Search Provider Shares
      operationId: post-v0-shares-provider-search
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProviderShare'
      description: Search provider shares by their access code and/or DOB.
      security:
        - apiKey1: []
      tags:
        - shares
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProviderShareSearch'
  /v0/shares/{shareId}/extend:
    put:
      summary: Extend Share
      parameters:
        - schema:
            type: string
          name: shareId
          in: path
          required: true
      operationId: put-v0-shares-shareId-extend
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
        '500':
          description: Internal Server Error
      description: Extend the expiration of a Provider or Patient Share.
      tags:
        - shares
      security:
        - apiKey1: []
  /v0/imaging/transfers:
    get:
      summary: Get transfers for account
      tags:
        - transfers
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImagingTransfer'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      operationId: get-v0-imaging-transfers
      description: Get list of transfers for acct. Default results limit is 10 if not provided. Sorted by exam date desc.
      security:
        - apiKey1: []
      parameters:
        - schema:
            type: integer
          in: query
          name: account_id
          description: account id
          required: true
        - schema:
            type: integer
          in: query
          name: limit
          description: number of results to return (default 10)
        - schema:
            type: string
          in: query
          name: offset
          description: last transfer fetched, all transfers returned will have been uploaded before this one
  /v0/imaging/transfers/{id}/revoke:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    patch:
      summary: Revoke transfer
      operationId: patch-v0-imaging-transfers-id-revoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      description: Revoke patient access to all exams within transfer
      security:
        - apiKey1: []
      tags:
        - transfers
  /v0/imaging/{uuid}/revoke:
    parameters:
      - schema:
          type: string
        name: uuid
        in: path
        required: true
    patch:
      summary: Revoke exam
      operationId: patch-v0-imaging-uuid-revoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      description: Revoke patient access to exam
      security:
        - apiKey1: []
      tags:
        - imaging
  /v0/imaging/{uuid}/unrevoke:
    parameters:
      - schema:
          type: string
        name: uuid
        in: path
        required: true
    patch:
      summary: Restore access to exam
      operationId: patch-v0-imaging-uuid-unrevoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
      description: Restore access to an exam IFF it has been revoked. If not, this is a no-op (will still return 200 if so)
      security:
        - apiKey1: []
      tags:
        - imaging
  /v0/imaging/transfers/{id}/unrevoke:
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    patch:
      summary: Restore transfer access
      operationId: patch-v0-imaging-transfers-id-unrevoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      description: Restore access to all exams in a transfer that have been revoked.
      security:
        - apiKey1: []
      tags:
        - transfers
  /v1/dicoweb/studies/images/{instance_uid}:
    get:
      summary: Return Dicom file with StudyInstanceUID tag (0020,000D)
      operationId: v1-dicomweb-retrieve-instance
      parameters:
        - schema:
            type: string
          name: instance_uid
          in: path
          required: true
        - schema:
            type: string
          in: query
          name: custom1
          description: return dicom file with {instance_uid}
          required: true
      tags:
        - meddream
      security:
        - apiKey1: []
      responses:
        '200':
          description: OK
          content:
            application/dicom:
              schema:
                format: binary
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
  /v1/dicoweb/studies/{study_uid}/metadata:
    get:
      summary: Get study metadata in Dicom Nema Json Model
      operationId: v1-dicomweb-retrieve-metadata
      parameters:
        - schema:
            type: string
          name: study_uid
          in: path
          required: true
        - schema:
            type: string
          in: query
          name: custom1
          description: return study metadata with {study_uid}
          required: true
      tags:
        - meddream
      security:
        - apiKey1: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DicomMetadata'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
  /v1/dicoweb/search/studies:
    get:
      summary: Search for study
      operationId: v1-dicomweb-qido-search-study
      parameters:
        - schema:
            type: string
          name: 0020000D
          description: study instance UID
          in: query
          required: true
        - schema:
            type: string
          in: query
          name: custom1
          description: search for study with study instanceUID and return metadata
          required: true
      tags:
        - meddream
      security:
        - apiKey1: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DicomMetadata'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
  /v1/meddream/generate/{exam_uuid}:
    post:
      summary: Generate a JWT meddream token
      operationId: get-v1-meddream-generate
      security:
        - basicAuth: []
      parameters:
        - schema:
            type: string
          name: exam_uuid
          in: path
          required: true
      tags:
        - meddream
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateMedreamToken'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: string
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
  /v1/meddream/validate:
    get:
      summary: Validate a JWT meddream token
      operationId: post-v1-meddream-validate
      security:
        - apiKey1: []
      parameters:
        - schema:
            type: string
          name: token
          in: query
          required: true
      tags:
        - meddream
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MeddreamToken'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
  /v1/studies/upload-status:
    get:
      description: Gets a list of upload statuses for studies that a pockethealth patient account with the given account_id has permission to access. Endpoint should be called only by support panel.
      operationId: get-v1-support-patients-upload-status
      tags:
        - support
        - patients
        - studies
        - upload-status
      security:
        - jwtBearer: []
      parameters:
        - in: query
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of patient.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/upload_status'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  /v1/studies/{id}/permissions:
    get:
      description: Gets user ids for users that have access permissions for a study with the given id that was shared by the given provider. User ids are grouped by user type. Only one user of type patient can be owner and therefore have access to a study. Multiple users of type physician can have access to a study.
      operationId: get-v1-studies-permissions
      tags:
        - studies
        - permissions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: DICOM UID of a study OR UUID of a study, if no provider_id is provided
        - in: query
          name: provider_id
          schema:
            type: integer
          description: legacy provider id (int64) that identifies provider that uploaded the study
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/study_access_permissions'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
  /v1/patients/{account_id}/studies:
    get:
      summary: Retrieve patient studies
      security:
        - basicAuth: []
      description: |
        Fetches studies for a patient account with optional filtering by fetch mode
        and optional uuids for study point-queries.
        Requires authentication and read permissions.
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID associated with the studies.
        - in: query
          name: include_reports
          schema:
            type: boolean
          description: Specify if report metadata associated with the study should be included
        - in: query
          name: include_instances
          schema:
            type: boolean
          description: Specify if instance metadata associated with the study should be included
        - in: query
          name: activated
          schema:
            type: boolean
          description: Specify if studies should be activated or not
        - in: query
          name: uuid
          schema:
            type: array
            items:
              type: string
              format: ksuid
          description: Specify studies by uuid to be fetched. If empty, all studies associated with the patient account are returned.
      responses:
        '200':
          description: List of patient studies retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/patient_study'
        '401':
          description: Unauthorized - insufficient permissions
        '500':
          description: Internal server error
  /v1/patients/{account_id}/studies/match:
    get:
      summary: Retrieve patient studies
      security:
        - basicAuth: []
      description: |
        Checks if studies belong to a patient account. Returns true if all studies provided belong to the patient.
        Requires authentication and read permissions.
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of patient.
        - in: query
          name: uuid
          schema:
            type: array
            items:
              type: string
              format: ksuid
          required: true
          description: Specify studies by uuid to be checked.
      responses:
        '200':
          description: Boolean to indicate if all study uuids belong to the patient's account.
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
        '401':
          description: Unauthorized - insufficient permissions
        '500':
          description: Internal server error
  /v1/patients/{account_id}/studies/activate:
    post:
      summary: Activates and returns list of studies eligible for the provided date of birth
      security:
        - basicAuth: []
      description: Activates and returns list of studies eligible for the provided date of birth
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of patient.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/activate_study'
      responses:
        '200':
          description: List of patient studies successfully activated
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/patient_study'
        '401':
          description: Unauthorized - insufficient permissions
        '500':
          description: Internal server error
  /v1/patients/{account_id}/studies/upload-status:
    get:
      description: Gets a list of upload statuses for studies that a pockethealth patient account with the given account_id has permission to access.
      operationId: get-v1-patients-upload-status
      tags:
        - support
        - patients
        - studies
        - upload-status
      security:
        - jwtBearer: []
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of patient.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/upload_status'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  /v1/accounts/{account_id}/studies/attribute:
    post:
      summary: Attribute the studies with the provided UUIDs and AvailabilityStatus
      security:
        - basicAuth: []
      description: Attribute the studies with the provided UUIDs and AvailabilityStatus
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of patient.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/study_attribution'
      responses:
        '200':
          description: Attribution done successfully
        '400':
          description: Bad Request - Invalid payload or param
        '401':
          description: Unauthorized - insufficient permissions
        '500':
          description: Internal server error
  /v1/physicians/{account_id}/studies:
    get:
      description: Gets a list of record streaming studies that a physician has access to. This endpoint also consolidates any group permissions that may apply to this physician account.
      operationId: get-v1-physician-studies
      tags:
        - physicians studies
      security:
        - basicAuth: []
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            pattern: ^[a-zA-Z0-9]{27}$
          required: true
          description: The KSUID of the physician account
          example: QhSpt1KG22Hnyp9Zrv4HHibm4Lx
        - in: query
          name: uuid
          schema:
            type: array
            items:
              type: string
              format: ksuid
          description: Specify studies by uuid to be checked.
        - in: query
          name: include_reports
          schema:
            type: boolean
          description: Specify if report metadata associated with the study should be included
        - in: query
          name: include_instances
          schema:
            type: boolean
          description: Specify if instance metadata associated with the study should be included
        - in: query
          name: patient_name
          required: false
          schema:
            type: string
          description: Name of patient whose studies should be retrieved in DICOM format. Studies for patients with other names should not be included.
        - in: query
          name: patient_birthdate
          required: false
          schema:
            type: string
          description: Date of birth of patient whose studies should be retrieved. Studies for patients with other dates of birth should not be included.
        - in: query
          name: provider_id
          required: false
          schema:
            type: integer
            format: int64
          description: ID of provider whose studies should be retrieved. Studies uploaded by other providers should not be included.
      responses:
        '200':
          description: List of studies the physician has access to. If filters were specified in query parameters, list will only include studies that match all filters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/physician_patient_studies'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  /v1/physicians/{account_id}/studies/match:
    get:
      summary: Retrieve patient studies accessible by physician.
      security:
        - basicAuth: []
      description: |
        Checks if studies are accessible to a physician account. Returns true
        if all studies provided are accessible to the physician.
        Requires authentication and read permissions.
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            format: ksuid
          required: true
          description: Account ID of physician.
        - in: query
          name: uuid
          schema:
            type: array
            items:
              type: string
              format: ksuid
          required: true
          description: Specify studies by uuid to be checked.
      responses:
        '200':
          description: Boolean to indicate if all study uuids are accessible to the physician account.
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
        '401':
          description: Unauthorized - insufficient permissions
        '500':
          description: Internal server error
  /v1/physicians/{account_id}/studies/upload-status:
    get:
      description: Gets a list of upload states for record streaming studies that a physician has access to. Return object is not a DICOM study or DICOM study tags, but instead general information on the study's upload state like image upload percentage, name of sharing provider, if study has an uploaded report.
      operationId: get-v1-studies-upload-status
      tags:
        - studies
      security:
        - basicAuth: []
      parameters:
        - in: path
          name: account_id
          schema:
            type: string
            pattern: ^[a-zA-Z0-9]{27}$
          required: true
          description: The KSUID of the physician account
          example: QhSpt1KG22Hnyp9Zrv4HHibm4Lx
      responses:
        '200':
          description: list of upload statuses for studies the physician has access to
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/study_upload_status'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  /v1/business-rulesets/evaluate:
    post:
      description: Evaluate business rules given a ruleset and dicom tags
      operationId: post-v1-business-rulesets-evaluate
      security:
        - basicAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ruleset:
                  $ref: '#/components/schemas/Ruleset'
                dicom_tags:
                  $ref: '#/components/schemas/any_value'
              required:
                - ruleset
                - dicom_tags
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostV1BusinessRulesetsEvaluateSuccessResponse'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostV1BusinessRulesetsEvaluateErrorResponse'
  /internal/v1/providers/{provider_id}/recent-study-upload-metadata:
    post:
      summary: Retrieve recent study upload metadata
      operationId: getRecentStudyUploadMetadata
      tags:
        - InternalAPI
      parameters:
        - in: path
          name: provider_id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StudyUploadMetadata'
        '400':
          description: Bad request (invalid provider_id)
        '401':
          description: Unauthorized (insufficient permissions)
        '500':
          description: Internal server error
  /internal/v1/patients/{account_id}/studies:
    get:
      summary: Retrieve all study data for an account ID with business rule result info
      operationId: getAllPatientStudies
      tags:
        - InternalAPI
      parameters:
        - in: path
          name: account_id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DebugPatientStudy'
        '400':
          description: Bad request (invalid account_id)
        '401':
          description: Unauthorized (insufficient permissions)
        '500':
          description: Internal server error
    delete:
      summary: Delete Patient Studies
      operationId: DeletePatientStudies
      description: |
        Deletes all studies for a patient by `account_id` and `patient_id`. If a `provider_id` is specified in the query parameters, only studies from that provider will be deleted.
      tags:
        - InternalAPI
      parameters:
        - name: account_id
          in: path
          required: true
          description: Account ID of the patient
          schema:
            type: string
        - name: provider_id
          in: query
          required: false
          description: Provider ID to filter studies to delete
          schema:
            type: integer
            format: int64
        - name: patient_id
          in: query
          required: false
          description: Optional patient ID for additional filtering
          schema:
            type: string
      responses:
        '200':
          description: No content
        '401':
          description: Unauthorized – insufficient permissions
        '499':
          description: Client closed request
        '500':
          description: Internal server error
  /internal/v1/providers/upload-overview:
    get:
      summary: Get Upload Overview
      operationId: GetUploadOverview
      description: |
        Generates a 1-hour time bucketed view of recent study uploads over the last 12 hours for record streaming providers. This provides a view into the average upload progress, study count, happy/sad user (patients + physicians) count per hour.
      tags:
        - InternalAPI
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProviderUploadOverview'
        '401':
          description: Unauthorized
  /support/v1/patients/{account_id}/studies/{uuid}/revoke:
    parameters:
      - schema:
          type: string
        name: account_id
        in: path
        required: true
      - schema:
          type: string
        name: uuid
        in: path
        required: true
    patch:
      summary: Revoke exam
      operationId: patch-support-v1-patients-account-id-studies-uuid-revoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      description: Revoke patient access to study
      security:
        - jwtBearer: []
      tags:
        - support
        - patients
        - studies
        - revoke
  /support/v1/patients/{account_id}/studies/{uuid}/unrevoke:
    parameters:
      - schema:
          type: string
        name: account_id
        in: path
        required: true
      - schema:
          type: string
        name: uuid
        in: path
        required: true
    patch:
      summary: Unrevoke study
      operationId: patch-support-v1-patients-account-id-studies-uuid-unrevoke
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      description: Unrevoke patient access to study
      security:
        - jwtBearer: []
      tags:
        - support
        - patients
        - studies
        - unrevoke
components:
  securitySchemes:
    apiKey1:
      name: API Key
      type: apiKey
      in: header
    basicAuth:
      type: http
      scheme: basic
    jwtBearer:
      type: http
      scheme: bearer
  schemas:
    ImagingExam:
      title: ImagingExam
      type: object
      properties:
        exam_uuid:
          type: string
        transfer_id:
          type: string
        upload_time:
          type: string
        patient_name:
          type: string
        patient_dob:
          type: string
        date:
          type: string
        modality:
          type: string
        provider:
          type: string
        source:
          $ref: '#/components/schemas/ImagingSource'
        activated:
          type: boolean
        revoked_at:
          type: string
        unrevoked_at:
          type: string
        reports:
          type: array
          items:
            $ref: '#/components/schemas/Report'
        series:
          $ref: '#/components/schemas/Series'
    Series:
      title: Series
      type: object
      description: ''
      properties:
        series_uid:
          type: string
        description:
          type: string
        num_images:
          type: number
    Report:
      title: Report
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum:
            - ER
            - ORIGINAL
        date:
          type: string
    ImagingSource:
      type: string
      title: ImagingSource
      enum:
        - SELF_UPLOAD
        - REQUEST
        - SUBSEQUENT
        - UPH
      description: ''
    ProviderShareSearch:
      title: ProviderShareSearch
      type: object
      properties:
        access_code:
          type: string
          pattern: '[A-Z0-9]\{9\}'
          description: Raw Code without the dashes.
        dob:
          type: string
          format: date
          description: ISO8601 formatted
    GenerateMedreamToken:
      title: GenerateMedreamToken
      type: object
      properties:
        exam_uuid:
          type: string
        study_uid:
          type: string
        account_id:
          type: string
    MeddreamToken:
      title: MeddreamToken
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              studies:
                type: object
                properties:
                  study:
                    type: string
                  storage:
                    type: string
        storageConfiguration:
          type: array
          items:
            type: object
            properties:
              storage:
                type: string
              params:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                    value:
                      type: string
    ProviderShare:
      title: ProviderShare
      type: object
      properties:
        id:
          type: string
        access_code:
          type: string
          description: Raw Code without the dashes.
        sending_org:
          type: string
        recipient:
          type: string
          description: Fax Number or Email
        num_exams:
          type: string
        dob:
          type: string
          description: ISO8601 Formatted
          format: date
        expires_at:
          type: string
          format: date-time
        extended_expires_at:
          type: string
          format: date-time
        method:
          type: string
          enum:
            - EMAIL
            - FAX
            - PRINT
            - DOWNLOAD
      required:
        - id
        - access_code
        - sending_org
        - dob
        - expires_at
        - method
      x-examples:
        example-1:
          id: string
          access_code: string
          sending_org: string
          recipient: string
          num_exams: string
          dob: '2019-08-24'
          expires_at: '2019-08-24T14:15:22Z'
          extended_expires_at: '2019-08-24T14:15:22Z'
          method: EMAIL
    ImagingTransfer:
      title: ImagingTransfer
      type: object
      properties:
        transfer_id:
          type: string
        patient_name:
          type: string
        provider:
          type: string
        uploaded:
          type: string
        request_id:
          type: integer
        activated:
          type: boolean
        order_id:
          type: string
        org_id:
          type: number
        has_revoked_exams:
          type: boolean
    DicomMetadata:
      title: DicomMetadata
      type: object
      description: Key is the dicom tag
      additionalProperties:
        type: object
        properties:
          vr:
            type: string
          Value:
            type: string
      example:
        '80060':
          vr: CS
          Value: US
        0020000D:
          vr: UI
          Value: 1.2.345.1
    study_availability_status:
      type: string
      enum:
        - locked
        - fullAccess
        - limitedAvailability
      description: |
        Study availability status from the perspective of a patient:

          * `locked` - Study is currently locked
          * `fullAccess` - Study is currently fully available
          * `limitedAvailability` - Study is partially available (unlocked with the Basic plan)
    upload_status:
      type: object
      description: Upload status of a study. Contains some study metadata, id and name of provider and upload progress percentage (% of instances uploaded for the study).
      required:
        - orgId
        - examId
        - progressPercent
        - modality
        - examType
        - examDate
        - description
        - orgName
      properties:
        orgId:
          type: integer
          format: int64
          description: provider id
        examId:
          type: string
          description: DICOM UID of study
        uuid:
          type: string
          format: ksuid
          description: ksuid of exam object created during study upload, if available
          nullable: true
        progressPercent:
          type: integer
          minimum: 0
          maximum: 100
          description: upload percentage of study - determined by how many images have been uploaded compared to total number of images in the study
        modality:
          type: string
          description: modality of the study - determined by the modality of the first series within the study
        examType:
          type: string
          description: type of exam - based on modality
        examDate:
          type: string
          description: date of exam, in format 'Mon dd, yyyy'
        description:
          type: string
          description: description of study
        orgName:
          type: string
          description: name of provider, 'Unknown' if no name is available
        patientId:
          type: string
          description: id of patient within pockethealth patient account that owns this study
          nullable: true
        study_availability_status:
          $ref: '#/components/schemas/study_availability_status'
    patient_account_permission:
      type: object
      description: Describes an access permission of a pockethealth patient account to a specific study. Only set if a patient has access to the study. Contains account id of pockethealth patient account that has access permission to study, patient id of patient within pockethealth patient account that owns study and the availability of the study for that patient, based on their plan (e.g. locked, full access, ...)
      required:
        - account_id
        - patient_id
        - study_availability_status
      properties:
        account_id:
          type: string
          description: The KSUID associated with a pockethealth patient account
          pattern: ^[a-zA-Z0-9]{27}$
          example: x4BHsQja1HRnKJzSAm3YEywZ8qa
        patient_id:
          type: string
          description: The KSUID associated with a patient within a pockethealth patient account
          pattern: ^[a-zA-Z0-9]{27}$
          example: x4BHsQja1HRnKJzSAm3YEywZ8qa
        study_availability_status:
          $ref: '#/components/schemas/study_availability_status'
    study_access_permissions:
      type: object
      description: Permission object for a study. Contains ids of users that have access to a given study. Ids are grouped by user type.
      required:
        - physician
      properties:
        physicians:
          type: array
          description: A list of KSUIDs for physicians that have access to a study
          items:
            type: string
            description: The KSUID associated with a pockethealth physician account
            example: x4BHsQja1HRnKJzSAm3YEywZ8qa
        patients:
          type: array
          description: A list of permissions for patients that have access to a study
          items:
            $ref: '#/components/schemas/patient_account_permission'
    dicom_patient_tags:
      type: object
      properties:
        patient_name:
          type: string
        patient_id:
          type: string
        patient_birth_date:
          type: string
        patient_sex:
          type: string
          nullable: true
        patient_telephone_number:
          type: string
          nullable: true
    dicom_study_tags:
      type: object
      properties:
        study_instance_uid:
          type: string
        study_date:
          type: string
        study_description:
          type: string
          nullable: true
        accession_number:
          type: string
          nullable: true
        referring_physician:
          type: string
          nullable: true
        modality:
          type: string
          nullable: true
        body_part:
          type: string
          nullable: true
    dicom_series_tags:
      type: object
      properties:
        series_instance_uid:
          type: string
        series_number:
          type: string
          nullable: true
        series_description:
          type: string
          nullable: true
    dicom_instance_tags:
      type: object
      properties:
        sop_instance_uid:
          type: string
        protocol:
          type: string
          nullable: true
        instance_number:
          type: string
          nullable: true
        image_type:
          type: string
          nullable: true
        sop_class_uid:
          type: string
          nullable: true
        content_date:
          type: string
          nullable: true
        content_time:
          type: string
          nullable: true
        acquisition_date:
          type: string
          nullable: true
        acquisition_time:
          type: string
          nullable: true
        view_position:
          type: string
          nullable: true
        image_position:
          type: string
          nullable: true
        image_orientation:
          type: string
          nullable: true
        frame_of_reference_uid:
          type: string
          nullable: true
        image_laterality:
          type: string
          nullable: true
        number_of_frames:
          type: string
          nullable: true
        rows:
          type: string
          nullable: true
        columns:
          type: string
          nullable: true
        pixel_spacing:
          type: string
          nullable: true
        transfer_syntax_uid:
          type: string
          nullable: true
    dicom_instance:
      type: object
      properties:
        uuid:
          type: string
        dicom_tags:
          $ref: '#/components/schemas/dicom_instance_tags'
        file_size_bytes:
          type: integer
          format: int64
    dicom_series:
      type: object
      properties:
        dicom_tags:
          $ref: '#/components/schemas/dicom_series_tags'
        instances:
          type: array
          items:
            $ref: '#/components/schemas/dicom_instance'
          nullable: true
    study_report:
      type: object
      properties:
        uuid:
          type: string
        file_size_bytes:
          type: integer
          format: int64
        encapsulated_pdf:
          type: boolean
        type:
          type: string
    patient_study:
      type: object
      properties:
        uuid:
          type: string
        patient_id:
          type: string
        organization_id:
          type: integer
          format: int64
        activated_timestamp:
          type: string
          format: date-time
        availability_status:
          type: string
        order_id:
          type: string
          format: ksuid
        transfer_id:
          type: string
          format: ksuid
        instance_upload_progress_percent:
          type: integer
          minimum: 0
          maximum: 100
        dicom_patient_tags:
          $ref: '#/components/schemas/dicom_patient_tags'
        dicom_study_tags:
          $ref: '#/components/schemas/dicom_study_tags'
        series:
          type: array
          items:
            $ref: '#/components/schemas/dicom_series'
        reports:
          type: array
          items:
            $ref: '#/components/schemas/study_report'
          nullable: true
        has_report:
          type: boolean
    activate_study:
      type: object
      properties:
        dateOfBirth:
          type: string
        orderId:
          type: string
        studyAvailabilityStatuses:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/study_availability_status'
    study_attribution:
      type: object
      properties:
        uuids:
          type: array
          description: A list of UUIDs for a study
          items:
            type: string
            description: UUID for a study
        orderId:
          type: string
        availabilityStatus:
          type: string
    permission_group:
      description: Info on the permission group associated with a study.
      type: object
      properties:
        group_id:
          type: integer
          format: int64
        groupName:
          type: string
    physician_patient_study:
      description: A patient study that a physician has access to. Wraps the patient study object.
      allOf:
        - $ref: '#/components/schemas/patient_study'
        - type: object
          properties:
            physician_account_id:
              type: string
            permission_groups:
              type: array
              items:
                $ref: '#/components/schemas/permission_group'
              nullable: true
    physician_patient_studies:
      type: array
      items:
        $ref: '#/components/schemas/physician_patient_study'
    study_identifier:
      type: object
      description: Uniquely identifies a study uploaded via record streaming. ID consists of DICOM UID of study and int64 legacy provider id (also called org id) of the provider who uploaded the study.
      required:
        - study_uid
        - provider_id
      properties:
        study_uid:
          type: string
          description: The DICOM identifier of the study
          example: 1.2.276.********.2.*************.*****************.90864
        provider_id:
          type: integer
          format: int64
          description: The int64 identifier of a provider that uploaded a study (also known as legacy provider id or org id)
          example: 42
    study_upload_status:
      type: object
      description: Upload status for a study. Contains ids of study (DICOM study UID and ID of uploading provider, unique ksuid created for study on upload) and information on the study's upload state (has uploaded report, percentage of images uploaded).
      required:
        - study_identifier
        - uuid
        - has_report
        - progress_percent
      properties:
        study_identifier:
          $ref: '#/components/schemas/study_identifier'
        uuid:
          type: string
          description: A unique KSUID associated with the study
          pattern: ^[a-zA-Z0-9]{27}$
          example: x4BHsQja1HRnKJzSAm3YEywZ8qa
        has_report:
          type: boolean
          description: Boolean value, true if a report has been successfully uploaded for the study. False if the study has no report OR if no report has been uploaded yet.
          example: true
        progress_percent:
          type: integer
          min: 0
          max: 100
          description: Percentage of instances of the study that have been uploaded.
    Rule:
      type: object
      properties:
        logic:
          description: Logical operator to combine rules (AND/OR).
          type: string
          enum:
            - AND
            - OR
        rules:
          description: Nested rules
          type: array
          items:
            $ref: '#/components/schemas/Rule'
        tag:
          description: DICOM tag, e.g. (0008,0080) Instituion Name
          type: string
        tagName:
          description: DICOM tag name for tag hex code auto populated from the endpoint
          type: string
        description:
          description: human readable description of the intent of the rule
          type: string
        operator:
          description: SQL comparison operator to use for the where condition
          type: string
          enum:
            - '='
            - '!='
            - <>
            - '>'
            - <
            - '>='
            - <=
            - '->'
            - '->>'
            - IN
            - NOT IN
            - LIKE
            - NOT LIKE
            - IS
            - IS NOT
            - REGEXP
            - NOT REGEXP
            - RLIKE
        value:
          description: Value for the where condition following rules for MySQL pattern matching
          oneOf:
            - type: string
            - type: number
    Block:
      type: object
      properties:
        study:
          $ref: '#/components/schemas/Rule'
        series:
          $ref: '#/components/schemas/Rule'
        report:
          $ref: '#/components/schemas/Rule'
        instance:
          $ref: '#/components/schemas/Rule'
    Ruleset:
      type: object
      properties:
        block:
          $ref: '#/components/schemas/Block'
    any_value: {}
    BusinessRuleResult:
      type: object
      properties:
        rule:
          type: string
        result:
          type: string
          enum:
            - blocked
            - allowed
        dicom_tags:
          $ref: '#/components/schemas/any_value'
    PostV1BusinessRulesetsEvaluateSuccessResponse:
      type: object
      properties:
        result:
          type: string
          enum:
            - blocked
            - allowed
        query:
          type: string
        business_rule_results:
          type: array
          items:
            $ref: '#/components/schemas/BusinessRuleResult'
          nullable: true
        error:
          type: string
      required:
        - result
        - query
        - business_rule_results
    PostV1BusinessRulesetsEvaluateErrorResponse:
      type: object
      properties:
        error:
          type: string
      required:
        - error
    StudyUploadMetadata:
      type: object
      properties:
        account_type:
          type: string
          enum:
            - PATIENT
            - PHYSICIAN
        account_id:
          type: string
        provider_id:
          type: integer
          format: int64
        study_uid:
          type: string
        instance_count:
          type: integer
          format: int64
        instances_uploaded:
          type: integer
          format: int64
        created_timestamp_utc:
          type: string
          format: date-time
    DebugPatientStudy:
      allOf:
        - $ref: '#/components/schemas/patient_study'
        - type: object
          properties:
            business_rule_results:
              type: array
              items:
                $ref: '#/components/schemas/BusinessRuleResult'
              nullable: true
            is_record_streaming:
              type: boolean
    ProviderUploadStatus:
      type: object
      properties:
        time_bucket:
          type: string
          format: date-time
        average_instance_upload_progress:
          type: integer
          format: int64
        study_count:
          type: integer
          format: int64
        happy_user_count:
          type: integer
          format: int64
        sad_user_count:
          type: integer
          format: int64
      required:
        - time_bucket
        - average_instance_upload_progress
        - study_count
        - happy_user_count
        - sad_user_count
    ProviderUploadOverview:
      type: object
      properties:
        provider_id:
          type: integer
          format: int64
        provider_name:
          type: string
        provider_upload_overviews:
          type: array
          items:
            $ref: '#/components/schemas/ProviderUploadStatus'
      required:
        - provider_id
        - provider_name
        - provider_upload_overviews
