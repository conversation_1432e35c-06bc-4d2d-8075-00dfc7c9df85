package recordservice

import (
	"time"

	"gitlab.com/pockethealth/coreapi/generated/api"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/examutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
)

type TokenRequest struct {
	ExamUuid  string `json:"exam_uuid"`
	AccountId string `json:"account_id"`
	StudyUid  string `json:"study_uid"`
}

type studyIdentifier struct {
	StudyUID   string `json:"study_uid"`
	ProviderID int64  `json:"provider_id"`
}
type studyUploadStatus struct {
	StudyIdentifier studyIdentifier `json:"study_identifier"`
	UUID            string          `json:"uuid"`
	HasReport       bool            `json:"has_report"`
	ProgressPercent int             `json:"progress_percent"`
}

func (o *studyUploadStatus) ToAPIModel() *api.PhysicianRecordUploadStatus {
	return &api.PhysicianRecordUploadStatus{
		StudyUID:        o.StudyIdentifier.StudyUID,
		ProviderID:      o.StudyIdentifier.ProviderID,
		UUID:            o.UUID,
		HasReport:       o.HasReport,
		ProgressPercent: o.ProgressPercent,
	}
}

type PatientStudyAvailabilityStatus string

const (
	NO_AVAILABILITY      PatientStudyAvailabilityStatus = "locked"
	FULL_AVAILABILITY    PatientStudyAvailabilityStatus = "fullAccess"
	LIMITED_AVAILABILITY PatientStudyAvailabilityStatus = "limitedAvailability"
)

type DicomPatientTags struct {
	// patient-level DICOM attributes
	PatientName            string `json:"patient_name"`                       // (0010,0010) Patient's Name
	PatientID              string `json:"patient_id"`                         // (0010,0020) Patient ID
	PatientBirthDate       string `json:"patient_birth_date"`                 // (0010,0030) Patient's Birth Date
	PatientSex             string `json:"patient_sex,omitempty"`              // (0010,0040) Patient's Sex
	PatientTelephoneNumber string `json:"patient_telephone_number,omitempty"` // (0010,2154) Patient's Telephone Numbers
}

type DicomStudyTags struct {
	// study-level DICOM attributes
	StudyInstanceUID   string `json:"study_instance_uid"`            // (0020,000D) Study Instance UID
	StudyDate          string `json:"study_date"`                    // (0008,0020) Study Date
	StudyDescription   string `json:"study_description,omitempty"`   // (0008,1030) Study Description
	AccessionNumber    string `json:"accession_number,omitempty"`    // (0008,0050) Accession Number
	ReferringPhysician string `json:"referring_physician,omitempty"` // (0008,0090) Referring Physician's Name

	// series-level DICOM attributes
	// this is pulled from a single series in the study for preview purposes
	// sadly, modality and body part are not stored in the series table
	Modality string `json:"modality,omitempty"`  // (0008,0060) Modality
	BodyPart string `json:"body_part,omitempty"` // (0018,0015) Body Part Examined
}

type PatientStudy struct {
	// exam KSUID
	UUID                          string    `json:"uuid"`
	AccountID                     string    `json:"account_id,omitempty"`
	PatientID                     string    `json:"patient_id"`
	OrganizationID                int64     `json:"organization_id"`
	ActivatedTimestamp            time.Time `json:"activated_timestamp"`
	InstanceUploadProgressPercent int64     `json:"instance_upload_progress_percent"`

	// For now, only record streamed studies have a determinate availability status
	// Transfer studies' availability status is inferred from orderID attribution
	AvailabilityStatus PatientStudyAvailabilityStatus `json:"availability_status"`
	OrderID            string                         `json:"order_id"`
	TransferID         string                         `json:"transfer_id"`

	// DICOM data
	DicomPatientTags DicomPatientTags `json:"dicom_patient_tags"`
	DicomStudyTags   DicomStudyTags   `json:"dicom_study_tags"`

	Series    []DicomSeries `json:"series"`
	Reports   []Report      `json:"reports,omitempty"`
	HasReport bool          `json:"has_report,omitempty"`
}

type DicomSeriesTags struct {
	SeriesInstanceUID string `json:"series_instance_uid"`          // (0020,000E) Series Instance UID
	SeriesNumber      string `json:"series_number,omitempty"`      // (0020,0011) Series Number
	SeriesDescription string `json:"series_description,omitempty"` // (0008,103E) Series Description
}

type DicomSeries struct {
	DicomTags DicomSeriesTags `json:"dicom_tags"`
	Instances []DicomInstance `json:"instances,omitempty"`
}

type DicomInstanceTags struct {
	SOPInstanceUID      string `json:"sop_instance_uid"`                 // (0008,0018) SOP Instance UID
	Protocol            string `json:"protocol,omitempty"`               // (0018,1030) Protocol Name
	InstanceNumber      string `json:"instance_number,omitempty"`        // (0020,0013) Instance Number
	ImageType           string `json:"image_type,omitempty"`             // (0008,0008) Image Type
	SOPClassUID         string `json:"sop_class_uid,omitempty"`          // (0008,0016) SOP Class UID
	ContentDate         string `json:"content_date,omitempty"`           // (0008,0023) Content Date
	ContentTime         string `json:"content_time,omitempty"`           // (0008,0033) Content Time
	AcquisitionDate     string `json:"acquisition_date,omitempty"`       // (0008,0022) Acquisition Date
	AcquisitionTime     string `json:"acquisition_time,omitempty"`       // (0008,0032) Acquisition Time
	ViewPosition        string `json:"view_position,omitempty"`          // (0018,5101) View Position
	ImagePosition       string `json:"image_position,omitempty"`         // (0020,0032) Image Position (Patient)
	ImageOrientation    string `json:"image_orientation,omitempty"`      // (0020,0037) Image Orientation (Patient)
	FrameOfReferenceUID string `json:"frame_of_reference_uid,omitempty"` // (0020,0052) Frame of Reference UID
	ImageLaterality     string `json:"image_laterality,omitempty"`       // (0020,0062) Image Laterality
	NumberOfFrames      string `json:"number_of_frames,omitempty"`       // (0028,0008) Number of Frames
	Rows                string `json:"rows,omitempty"`                   // (0028,0010) Rows
	Columns             string `json:"columns,omitempty"`                // (0028,0011) Columns
	PixelSpacing        string `json:"pixel_spacing,omitempty"`          // (0028,0030) Pixel Spacing
	TransferSyntaxUID   string `json:"transfer_syntax_uid,omitempty"`    // (0002,0010) Transfer Syntax UID
}

type DicomInstance struct {
	// object KSUID
	UUID          string            `json:"uuid"`
	DicomTags     DicomInstanceTags `json:"dicom_tags"`
	FileSizeBytes int64             `json:"file_size_bytes"`
}

type Report struct {
	// object KSUID
	UUID            string `json:"uuid"`
	FileSizeBytes   int64  `json:"file_size_bytes"`
	EncapsulatedPDF bool   `json:"encapsulated_pdf"`
	Type            string `json:"type"`
}

// --------------------- record service -> exam model translation ---------------------

var excludedSOPClassUIDs = map[string]any{
	// Presentation State Storage SOP Classes
	"1.2.840.10008.5.1.4.1.1.11.1": nil, // Grayscale Softcopy Presentation State Storage
	"1.2.840.10008.5.1.4.1.1.11.2": nil, // Color Softcopy Presentation State Storage
	"1.2.840.10008.5.1.4.1.1.11.3": nil, // Pseudocolor Softcopy Presentation State Storage
	"1.2.840.10008.5.1.4.1.1.11.4": nil, // Blending Softcopy Presentation State Storage
	"1.2.840.10008.5.1.4.1.1.11.5": nil, // Volume Rendering Softcopy Presentation State Storage

	// Structured Reporting (SR) SOP Classes
	"1.2.840.10008.5.1.4.1.1.88.11": nil, // Basic Text SR
	"1.2.840.10008.5.1.4.1.1.88.22": nil, // Enhanced SR
	"1.2.840.10008.5.1.4.1.1.88.33": nil, // Comprehensive SR
	"1.2.840.10008.5.1.4.1.1.88.40": nil, // Procedure Log Storage
	"1.2.840.10008.5.1.4.1.1.88.50": nil, // Mammography CAD SR
	"1.2.840.10008.5.1.4.1.1.88.59": nil, // Key Object Selection Document Storage
	"1.2.840.10008.5.1.4.1.1.88.67": nil, // Chest CAD SR

	// Waveform Storage SOP Classes
	"1.2.840.10008.5.1.4.1.1.9.1.1": nil, // General ECG Waveform Storage
	"1.2.840.10008.5.1.4.1.1.9.1.2": nil, // Ambulatory ECG Waveform Storage
	"1.2.840.10008.5.1.4.1.1.9.1.3": nil, // Hemodynamic Waveform Storage
	"1.2.840.10008.5.1.4.1.1.9.1.4": nil, // EEG Waveform Storage
	"1.2.840.10008.5.1.4.1.1.9.1.5": nil, // General Audio Waveform Storage

	// Raw Data Storage
	"1.2.840.10008.5.1.4.1.1.66": nil, // Raw Data Storage

	// Encapsulated Document Storage
	"1.2.840.10008.5.1.4.1.1.104.1": nil, // Encapsulated PDF Storage
	"1.2.840.10008.5.1.4.1.1.104.2": nil, // Encapsulated CDA Storage

	// Other Non-Image SOP Classes
	"1.2.840.10008.5.1.4.1.2.2.1": nil, // Study Root Query/Retrieve Information Model – FIND
	"1.2.840.10008.5.1.4.1.2.2.2": nil, // Study Root Query/Retrieve Information Model – MOVE
	"1.2.840.10008.5.1.4.1.2.2.3": nil, // Study Root Query/Retrieve Information Model – GET
	"1.2.840.10008.3.1.2.3.3":     nil, // MPPS Create
	"1.2.840.10008.3.1.2.3.4":     nil, // MPPS Set
	"1.2.840.113619.4.27":         nil, // GE-specific UID (Proprietary)
}

func (i *DicomInstance) ToExamInstance() (coreapi.Image, error) {
	token, err := examutils.GetImageToken(i.UUID)
	if err != nil {
		return coreapi.Image{}, err
	}
	return coreapi.Image{
		ImageId: i.UUID,
		Token:   token,
	}, nil
}

func (i *DicomInstance) ShouldExcludeFromExamSeries() bool {
	_, exclude := excludedSOPClassUIDs[i.DicomTags.SOPClassUID]
	return exclude
}

func (r *Report) ToExamReport() coreapi.Report {
	return coreapi.Report{
		ReportId:    r.UUID,
		Size:        r.FileSizeBytes,
		Definitions: true,
		Protocol:    r.Type,
	}
}

func (s *DicomSeries) ToExamSeries() (coreapi.Series, int, error) {
	var instances []coreapi.Image
	fileSizeBytes := 0

	for _, instance := range s.Instances {
		if instance.ShouldExcludeFromExamSeries() {
			continue
		}
		image, err := instance.ToExamInstance()
		if err != nil {
			return coreapi.Series{}, 0, err
		}
		instances = append(instances, image)
		fileSizeBytes += int(instance.FileSizeBytes)
	}

	return coreapi.Series{
		SeriesId:    s.DicomTags.SeriesInstanceUID,
		Description: s.DicomTags.SeriesDescription,
		Instances:   instances,
	}, fileSizeBytes, nil
}

func (s *PatientStudy) ToUnlockedStudy() coreapi.UnlockedStudy {
	date, _ := time.Parse(coreapi.DICOMDateFormat, s.DicomStudyTags.StudyDate)
	examDate := date.Format(coreapi.ExamDateFormat)
	return coreapi.UnlockedStudy{
		UUID:        s.UUID,
		Description: s.DicomStudyTags.StudyDescription,
		ExamDate:    examDate,
		HasReport:   s.HasReport,
		ExamType:    dcmtools.ParseType(s.DicomStudyTags.Modality),
		ExamId:      s.DicomStudyTags.StudyInstanceUID,
		PatientId:   s.PatientID,
		OrgId:       s.OrganizationID,
	}
}

func (s *PatientStudy) ToExamRaw() (coreapi.ExamRaw, error) {
	exam := coreapi.ExamRaw{}
	exam.Activated = !s.ActivatedTimestamp.IsZero()

	var reports []coreapi.Report
	var series []coreapi.Series
	fileSizeBytes := 0
	if exam.Activated {
		for _, report := range s.Reports {
			reports = append(reports, report.ToExamReport())
		}

		for _, dicomSeries := range s.Series {
			convertedSeries, seriesFileSizeBytes, err := dicomSeries.ToExamSeries()
			if err != nil {
				return coreapi.ExamRaw{}, err
			}
			// skip series without instances
			if len(convertedSeries.Instances) > 0 {
				series = append(series, convertedSeries)
				fileSizeBytes += seriesFileSizeBytes
			}
		}
	}

	// Series and reports
	exam.Series = series
	exam.Reports = reports

	// Exam non-dicom metadata
	exam.UUID = s.UUID
	exam.OrgId = s.OrganizationID
	exam.PatientId = s.PatientID
	exam.Size = fileSizeBytes
	exam.UnlockStatus = models.UnlockStatus(s.AvailabilityStatus)
	exam.AttributeOrderId = s.OrderID
	exam.AttributedAt = s.ActivatedTimestamp
	exam.TransferId = s.TransferID

	// Patient DICOM metadata
	exam.DICOMPatientName = s.DicomPatientTags.PatientName
	exam.DICOMBirthDate = s.DicomPatientTags.PatientBirthDate
	exam.Sex = s.DicomPatientTags.PatientSex

	// Study DICOM metadata
	exam.ExamId = s.DicomStudyTags.StudyInstanceUID
	exam.Modality = s.DicomStudyTags.Modality
	exam.DICOMExamDate = s.DicomStudyTags.StudyDate
	exam.Description = s.DicomStudyTags.StudyDescription
	exam.BodyPart = s.DicomStudyTags.BodyPart
	exam.DICOMReferringPhysician = s.DicomStudyTags.ReferringPhysician

	return exam, nil
}
