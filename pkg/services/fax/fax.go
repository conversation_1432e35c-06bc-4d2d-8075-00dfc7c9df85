package fax

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
)

type FaxSvcUser struct {
	URL           string `json:"url"`
	User          string `json:"name"`
	ApiKey        string `json:"api_key"`
	ApiKeySecName string `json:"api_key_sec_name"`
	Client        *http.Client
	doRequest     requestwithcontext.RequestWithCtx
}

func (f *FaxSvcUser) SetDoRequest(doer requestwithcontext.RequestWithCtx) {
	if doer == nil {
		f.doRequest = requestwithcontext.DoRequestWithCtx
	} else {
		f.doRequest = doer
	}
}

// PostFax sneds a fax document to the specified number
func (f *FaxSvcUser) PostFax(
	ctx context.Context,
	tel string,
	faxpdf io.Reader,
) (FaxInstance, error) {
	resp, err := f.request(ctx, "POST", fmt.Sprintf("v2/fax/+1%s", tel), faxpdf)
	if err != nil {
		return FaxInstance{}, fmt.Errorf("error making the request: %s", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return FaxInstance{}, fmt.Errorf(
			"got response code `%v` back from fax svc",
			resp.StatusCode,
		)
	}

	// ensure http.Client connection reuse; https://github.com/golang/go/issues/26095 and https://golang.cafe/blog/how-to-reuse-http-connections-in-go.html
	var respBytes []byte
	buf := bytes.NewBuffer(respBytes)
	_, err = io.Copy(buf, resp.Body)
	if err != nil {
		return FaxInstance{}, fmt.Errorf("error reading response body: %s", err)
	}

	var fi FaxInstance
	err = json.Unmarshal(buf.Bytes(), &fi)
	if err != nil {
		return FaxInstance{}, fmt.Errorf("error unmarshalling response bytes: %s", err)
	}

	return fi, nil
}

func (f *FaxSvcUser) request(
	ctx context.Context,
	verb string,
	path string,
	body io.Reader,
) (*http.Response, error) {
	req, err := http.NewRequest(verb, fmt.Sprintf("https://%s/%s", f.URL, path), body)
	if err != nil {
		return nil, fmt.Errorf("could not create fax request: %v", err)
	}

	req.Header.Add(
		"Authorization",
		fmt.Sprintf(
			"Basic %s",
			base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", f.User, f.ApiKey))),
		),
	)
	req.Header.Set("Content-Type", "application/pdf")

	resp, err := f.doRequest(ctx, f.Client, req)
	if err != nil {
		return nil, fmt.Errorf("could not do fax request: %v", err)
	}

	return resp, nil
}
