//go:build integration
// +build integration

package fax

import (
	"context"
	"net/http"
	"os"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestPostFax(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	t.Run(
		"fax request with valid api key and tel num should get back status 200",
		func(t *testing.T) {
			var fs = FaxSvcUser{
				URL:    cfg.FaxUrl,
				User:   "ca-dev",
				ApiKey: "BnyKRapTbfgnuIkVGQFrfvzW3SWObwjHAAnhr3NCDyvd82to2YbJYk411RltagmK",
				Client: &http.Client{},
			}
			fs.SetDoRequest(nil)

			f, err := os.Open("test_data/testfax.pdf")
			if err != nil {
				t.Fatalf("setup error: can't read test pdf file: %s", err)
			}
			defer f.Close()

			resp, err := fs.PostFax(context.Background(), "**********", f)
			if err != nil {
				t.Fatalf("error sending fax when wanted none: %s", err)
			}

			if resp.IsError {
				t.Fatalf("got an error when expected none: %s", resp.ErrorMessage)
			}
		},
	)
}
