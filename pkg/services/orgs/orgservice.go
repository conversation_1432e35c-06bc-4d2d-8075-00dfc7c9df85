package orgs

import (
	"context" // #nosec G505
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

const FacilityFundedPlanId = 10

type OrgServiceClient struct {
	OrgServiceUrl    string `json:"url"`
	OrgServiceUser   string `json:"name"`
	OrgServiceAPIKey string
	HttpClient       *httpclient.Client
	OrgsvcCache      IOrgsvcCache
}

type OrgService interface {
	GetPlans(ctx context.Context, providerId uint) ([]Plan, error)
	IsFacilityFunded(ctx context.Context, providerId uint) (bool, error)
	GetFormById(ctx context.Context, formId string, inApp bool, language string) (FormResponse, error)
	GetFormConfigurationFieldsByLegacyProviderId(
		ctx context.Context,
		providerId int64,
		language string,
	) (ConfigurationFieldMap, error)
	GetFormByLegacyProviderId(
		ctx context.Context,
		providerId int64,
		inApp bool,
		language string,
	) (FormResponse, error)
	GetFormByProviderUrl(
		ctx context.Context,
		providerUrl string,
		inApp bool,
		language string,
	) (FormResponse, error)
	GetProviderById(ctx context.Context, id string) (Provider, error)
	GetClinicById(ctx context.Context, id string) (Clinic, error)
	GetProviderByLegacyId(ctx context.Context, id int64) (Provider, error)
	GetClinicByLegacyId(ctx context.Context, id int64) (Clinic, error)
	GetProviderByUrl(ctx context.Context, url string) (Provider, error)
	GetClinicsByProviderId(ctx context.Context, providerId string) []Clinic
	GetClinicEmailsByLegacyId(ctx context.Context, legacyClinicId int64) []ClinicEmails
	GetProvidersByIsUph(
		ctx context.Context,
		isUph bool,
	) ([]Provider, error)
	GetSearchProviders(ctx context.Context, searchTerm string) ([]SearchProvider, error)
	GetProviderTransferConfig(ctx context.Context, id string) (ProviderTransferConfig, error)
}

func NewHTTPOrgServiceClient(url, user, apiKey string, client *http.Client) *OrgServiceClient {
	cache, err := NewOrgsvcCache()
	if err != nil {
		logrus.WithError(err).Fatal("unable to set up orgsvc cache")
	}
	return &OrgServiceClient{
		OrgServiceUrl:    url,
		OrgServiceUser:   user,
		OrgServiceAPIKey: apiKey,
		HttpClient: httpclient.NewHTTPClient(
			client,
			nil,
		),
		OrgsvcCache: cache,
	}
}

func (os *OrgServiceClient) setupBasicAuth() string {
	cred := fmt.Sprintf("%s:%s", os.OrgServiceUser, os.OrgServiceAPIKey)
	return base64.StdEncoding.EncodeToString([]byte(cred))
}

func (os *OrgServiceClient) GetPlans(ctx context.Context, providerId uint) ([]Plan, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("org_id", providerId)

	endpoint := fmt.Sprintf("%s/v1/providers/%d/plans", os.OrgServiceUrl, providerId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "GET",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    os.setupBasicAuth(),
	}

	respBody, _, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get plans request")
		return []Plan{}, err
	}
	defer respBody.Close()

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get plans body")
		return []Plan{}, err
	}

	var plans []Plan
	err = json.Unmarshal(gotBody, &plans)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return nil, err
	}

	return plans, nil
}

func (os *OrgServiceClient) IsFacilityFunded(ctx context.Context, providerId uint) (bool, error) {
	plans, err := os.GetPlans(ctx, providerId)
	if err != nil {
		return false, err
	}

	for _, p := range plans {
		if p.Id == FacilityFundedPlanId {
			return true, nil
		}
	}
	return false, nil
}

// GetProviderById gets a provider by its key identifier
func (os *OrgServiceClient) GetProviderById(ctx context.Context, id string) (Provider, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("provider_id", id)
	//try to get from cache
	provider, err := os.OrgsvcCache.GetProvider(ctx, id)
	if err == nil {
		return provider, nil
	}

	endpoint := fmt.Sprintf("%s/v1/providers/%s", os.OrgServiceUrl, id)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Provider
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}
	//set cache
	os.OrgsvcCache.SetProvider(id, result, 1)

	return result, nil
}

func (os *OrgServiceClient) GetProviderByUrl(ctx context.Context, url string) (Provider, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("url", url)

	endpoint := fmt.Sprintf("%s/v1/providers/url/%s", os.OrgServiceUrl, url)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Provider
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}

	return result, nil
}

// GetClinicById gets a clinic by its key identifier
func (os *OrgServiceClient) GetClinicById(ctx context.Context, id string) (Clinic, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("clinic_id", id)
	//try to get from cache
	clinic, err := os.OrgsvcCache.GetClinic(ctx, id)
	if err == nil {
		return clinic, nil
	}

	endpoint := fmt.Sprintf("%s/v1/clinics/%s", os.OrgServiceUrl, id)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Clinic
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}
	os.OrgsvcCache.SetClinic(id, result, 1)

	return result, nil
}

// GetProviderByLegacyId gets a provider by a legacy organization identifier
func (os *OrgServiceClient) GetProviderByLegacyId(ctx context.Context, id int64) (Provider, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("provider_legacy_id", id)
	//try to get from cache
	cacheId := strconv.FormatInt(id, 10)
	if os.OrgsvcCache != nil {
		provider, err := os.OrgsvcCache.GetProvider(ctx, cacheId)
		if err == nil {
			return provider, nil
		}
	}

	endpoint := fmt.Sprintf("%s/v1/providers/legacy/%d", os.OrgServiceUrl, id)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Provider
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}
	//set cache
	if os.OrgsvcCache != nil {
		os.OrgsvcCache.SetProvider(cacheId, result, 1)
	}

	return result, nil
}

// GetClinicByLegacyId gets a clinic by a legacy clinic identifier
func (os *OrgServiceClient) GetClinicByLegacyId(ctx context.Context, id int64) (Clinic, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("clinic_legacy_id", id)
	//try to get from cache
	cacheId := strconv.FormatInt(id, 10)
	if os.OrgsvcCache != nil {
		clinic, err := os.OrgsvcCache.GetClinic(ctx, cacheId)
		if err == nil {
			return clinic, nil
		}
	}

	endpoint := fmt.Sprintf("%s/v1/clinics/legacy/%d", os.OrgServiceUrl, id)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Clinic
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Info("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Info("error unmarshaling resp body")
		return result, err
	}
	if os.OrgsvcCache != nil {
		os.OrgsvcCache.SetClinic(cacheId, result, 1)
	}

	return result, nil
}

// GetClinicsByProviderId gets all clinics for a provider identifier
func (os *OrgServiceClient) GetClinicsByProviderId(
	ctx context.Context,
	providerId string,
) []Clinic {
	lg := logutils.DebugCtxLogger(ctx).WithField("provider_id", providerId)

	endpoint := fmt.Sprintf("%s/v1/clinics/provider/%s", os.OrgServiceUrl, providerId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result []Clinic
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Info("error reading resp body")
		return result
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Info("error unmarshaling resp body")
		return result
	}

	return result
}

// GetClinicEmailsByLegacyId gets all clinic email records for a legacy clinic identifier
func (os *OrgServiceClient) GetClinicEmailsByLegacyId(
	ctx context.Context,
	legacyClinicId int64,
) []ClinicEmails {
	lg := logutils.DebugCtxLogger(ctx).WithField("clinic_legacy_id", legacyClinicId)

	endpoint := fmt.Sprintf("%s/v1/clinics/emails/%d/legacy", os.OrgServiceUrl, legacyClinicId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result []ClinicEmails
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Info("error reading resp body")
		return result
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Info("error unmarshaling resp body")
		return result
	}

	return result
}

func (os *OrgServiceClient) GetFormById(
	ctx context.Context,
	formId string,
	inApp bool,
	language string,
) (FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("formId", formId)

	endpoint := fmt.Sprintf("%s/v1/forms/%s", os.OrgServiceUrl, formId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		QueryParams:   map[string]string{"inApp": fmt.Sprintf("%v", inApp)},
		ExpectedCodes: []int{http.StatusOK, http.StatusNotFound},
		Headers:       map[string]string{"Language": language},
	}

	respBody, status, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get form request")
		return FormResponse{}, err
	}
	defer respBody.Close()

	if status == http.StatusNotFound {
		return FormResponse{}, errors.New(errormsgs.ERR_NOT_EXISTS)
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get plans body")
		return FormResponse{}, err
	}

	var formResponse FormResponse
	err = json.Unmarshal(gotBody, &formResponse)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return FormResponse{}, err
	}

	return formResponse, nil
}

func (os *OrgServiceClient) GetFormByLegacyProviderId(
	ctx context.Context,
	providerId int64,
	inApp bool,
	language string,
) (FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("providerId", providerId)

	endpoint := fmt.Sprintf(
		"%s/v1/forms/legacyProviderId/%d",
		os.OrgServiceUrl,
		providerId,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "GET",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    os.setupBasicAuth(),
		QueryParams: map[string]string{
			"inApp": fmt.Sprintf("%v", inApp),
		},
		ExpectedCodes: []int{http.StatusOK, http.StatusNotFound},
		Headers:       map[string]string{"Language": language},
	}

	respBody, status, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get form request")
		return FormResponse{}, err
	}
	defer respBody.Close()

	if status == http.StatusNotFound {
		return FormResponse{}, errors.New(errormsgs.ERR_NOT_EXISTS)
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get form body")
		return FormResponse{}, err
	}

	var formResponse FormResponse
	err = json.Unmarshal(gotBody, &formResponse)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return FormResponse{}, err
	}

	return formResponse, nil
}

func (os *OrgServiceClient) GetFormByProviderUrl(
	ctx context.Context,
	providerUrl string,
	inApp bool,
	language string,
) (FormResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("providerUrl", providerUrl)

	endpoint := fmt.Sprintf(
		"%s/v1/forms/providerUrl/%s",
		os.OrgServiceUrl,
		providerUrl,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "GET",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    os.setupBasicAuth(),
		QueryParams: map[string]string{
			"inApp": fmt.Sprintf("%v", inApp),
		},
		ExpectedCodes: []int{http.StatusOK, http.StatusNotFound},
		Headers:       map[string]string{"Language": language},
	}

	respBody, status, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get form request")
		return FormResponse{}, err
	}
	defer respBody.Close()

	if status == http.StatusNotFound {
		return FormResponse{}, errors.New(errormsgs.ERR_NOT_EXISTS)
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get form body")
		return FormResponse{}, err
	}

	var formResponse FormResponse
	err = json.Unmarshal(gotBody, &formResponse)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return FormResponse{}, err
	}

	return formResponse, nil
}

func (os *OrgServiceClient) GetFormConfigurationFieldsByLegacyProviderId(
	ctx context.Context,
	providerId int64,
	language string,
) (ConfigurationFieldMap, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("providerId", providerId)

	endpoint := fmt.Sprintf(
		"%s/v1/forms/configurationFields/%d",
		os.OrgServiceUrl,
		providerId,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:  "GET",
		TargetURL:   endpoint,
		AuthScheme:  httpclient.Basic,
		AuthKey:     os.setupBasicAuth(),
		QueryParams: map[string]string{},
		Headers:     map[string]string{"Language": language},
	}

	respBody, _, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get form configuration map request")
		return ConfigurationFieldMap{}, err
	}
	defer respBody.Close()

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get form configuration map body")
		return ConfigurationFieldMap{}, err
	}

	var configurationMap ConfigurationFieldMap
	err = json.Unmarshal(gotBody, &configurationMap)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return ConfigurationFieldMap{}, err
	}

	return configurationMap, nil
}

func (os *OrgServiceClient) GetProvidersByIsUph(
	ctx context.Context,
	isUph bool,
) ([]Provider, error) {
	lg := logutils.CtxLogger(ctx).WithField("isUph", isUph)

	endpoint := fmt.Sprintf(
		"%s/v1/providers/isUph/%t",
		os.OrgServiceUrl,
		isUph,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:  "GET",
		TargetURL:   endpoint,
		AuthScheme:  httpclient.Basic,
		AuthKey:     os.setupBasicAuth(),
		QueryParams: map[string]string{},
	}

	respBody, _, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending get providers by isUph request")
		return []Provider{}, err
	}
	defer respBody.Close()

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("could not read get providers by isUph body")
		return []Provider{}, err
	}

	var providers []Provider
	err = json.Unmarshal(gotBody, &providers)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return []Provider{}, err
	}

	return providers, nil
}

func (os *OrgServiceClient) GetSearchProviders(
	ctx context.Context,
	searchTerm string,
) ([]SearchProvider, error) {
	lg := logutils.CtxLogger(ctx).WithField("searchTerm", searchTerm)

	endpoint := fmt.Sprintf("%s/v1/search", os.OrgServiceUrl)
	queryParams := map[string]string{"searchTerm": searchTerm}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		QueryParams:   queryParams,
		ExpectedCodes: []int{http.StatusOK},
	}

	var providers []SearchProvider
	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error getting providers")
		return []SearchProvider{}, err
	}
	defer respBody.Close()

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Info("could not read search body")
		return []SearchProvider{}, err
	}

	err = json.Unmarshal(body, &providers)
	if err != nil {
		lg.WithField("resp_body", string(body)).
			WithError(err).
			Info("error unmarshaling response body")
		return []SearchProvider{}, err
	}

	return providers, nil
}

// GetProviderTransferConfig gets a tranfer configuration for a provider by its key identifier
func (os *OrgServiceClient) GetProviderTransferConfig(ctx context.Context, providerId string) (ProviderTransferConfig, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("provider_id", providerId)
	var result ProviderTransferConfig

	endpoint := fmt.Sprintf("%s/v1/providers/%s/transfer", os.OrgServiceUrl, providerId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       os.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	respBody, code, err := os.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}

	return result, nil
}
