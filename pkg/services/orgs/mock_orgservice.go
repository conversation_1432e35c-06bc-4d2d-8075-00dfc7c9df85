package orgs

import (
	"context"
	"errors"
)

type OrgServiceMock struct {
	IsFacilityFundedReturn bool
	FormConfigurationMap   ConfigurationFieldMap
	ReturnDemoClinicData   bool
	ReturnEmptyDataList    bool
	UPHProviders           []Provider
	ExpectError            bool
	ExpectClinicDataError  bool
	GetPlansReturn         []Plan
}

func (m *OrgServiceMock) GetPlans(ctx context.Context, providerId uint) ([]Plan, error) {
	return m.GetPlansReturn, nil
}

func (m *OrgServiceMock) IsFacilityFunded(ctx context.Context, providerId uint) (bool, error) {
	return m.IsFacilityFundedReturn, nil
}

func (m *OrgServiceMock) GetProviderById(ctx context.Context, id string) (Provider, error) {
	return Provider{Id: id}, nil
}

func (m *OrgServiceMock) GetProviderByUrl(ctx context.Context, url string) (Provider, error) {
	return Provider{Url: url}, nil
}

func (m *OrgServiceMock) GetClinicById(ctx context.Context, id string) (Clinic, error) {
	return Clinic{Id: id}, nil
}

func (m *OrgServiceMock) GetProviderByLegacyId(ctx context.Context, id int64) (Provider, error) {
	if m.ExpectError {
		return Provider{}, errors.New("mock error")
	}

	if id == 0 {
		return Provider{}, errors.New("provider not found")
	}
	result := Provider{
		Name:           "Demo Medical Imaging",
		SendReports:    true,
		ReportSendTime: 7,
		Language:       "en",
		LegacyId:       id,
	}
	if m.ReturnDemoClinicData {
		result.Name = "Demo Medical Imaging"
		result.LegacyId = 9
		result.Id = "2RChLpEgTUEgKAJpceb35wwPiV9"
	}
	return result, nil
}

func (m *OrgServiceMock) GetClinicByLegacyId(ctx context.Context, id int64) (Clinic, error) {
	if m.ExpectClinicDataError {
		return Clinic{}, errors.New("mock clinic data error")
	}
	if id == 0 {
		return Clinic{}, errors.New("clinic not found")
	}
	result := Clinic{LegacyId: id, Provider: Provider{Language: "en", Id: "testproviderid"}}
	if m.ReturnDemoClinicData {
		result.Name = "Demo Medical Imaging"
		result.ProviderName = "Demo Medical Imaging"
		result.Provider.LegacyId = 9
		result.Provider.Name = "Demo Medical Imaging"
		result.Provider.Id = "2RChLpEgTUEgKAJpceb35wwPiV9"
	}
	return result, nil
}

func (m *OrgServiceMock) GetClinicsByProviderId(ctx context.Context, providerId string) []Clinic {
	results := []Clinic{}
	if providerId == "" {
		return results
	}
	if m.ReturnEmptyDataList {
		return results
	}
	//To pass integ tests, hardcode clinic...
	result := Clinic{}
	result.LegacyId = 365
	result.Provider.Id = providerId
	if m.ReturnDemoClinicData {
		result.Name = "Demo Medical Imaging"
		result.LegacyId = 13
		result.ProviderName = "Demo Medical Imaging"
		result.Provider.LegacyId = 9
		result.Provider.Name = "Demo Medical Imaging"
		result.Provider.Id = "2RChLpEgTUEgKAJpceb35wwPiV9"
	}
	results = append(results, result)

	return results
}

func (m *OrgServiceMock) GetClinicEmailsByLegacyId(
	ctx context.Context,
	legacyClinicId int64,
) []ClinicEmails {
	return []ClinicEmails{}
}

func (m *OrgServiceMock) GetFormById(
	ctx context.Context,
	formId string,
	inApp bool,
	language string,
) (FormResponse, error) {
	return FormResponse{}, nil
}

func (m *OrgServiceMock) GetFormByLegacyProviderId(
	ctx context.Context,
	providerId int64,
	inApp bool,
	language string,
) (FormResponse, error) {
	return FormResponse{}, nil
}

func (os *OrgServiceMock) GetFormByProviderUrl(
	ctx context.Context,
	providerUrl string,
	inApp bool,
	language string,
) (FormResponse, error) {
	return FormResponse{}, nil
}

func (m *OrgServiceMock) GetFormConfigurationFieldsByLegacyProviderId(
	ctx context.Context,
	providerId int64,
	language string,
) (ConfigurationFieldMap, error) {
	return m.FormConfigurationMap, nil
}

func (os *OrgServiceMock) GetProvidersByIsUph(
	ctx context.Context,
	isUph bool,
) ([]Provider, error) {
	if isUph {
		return os.UPHProviders, nil
	}
	return []Provider{}, nil
}

func (m *OrgServiceMock) GetSearchProviders(
	ctx context.Context,
	searchTerm string,
) ([]SearchProvider, error) {
	// return error for meilisearch so fuzzysearch can be called in providers service integ tests
	return []SearchProvider{}, errors.New("test error")
}

func (m *OrgServiceMock) GetProviderTransferConfig(
	ctx context.Context,
	providerId string,
) (ProviderTransferConfig, error) {
	if m.ExpectError {
		return ProviderTransferConfig{}, errors.New("mock error")
	}
	if providerId == "" {
		return ProviderTransferConfig{}, errors.New("provider transfer config not found")
	}

	return ProviderTransferConfig{
		ProviderId:     providerId,
		ReportSendTime: 1,
		SendReports:    true,
	}, nil
}
