// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/orgs/orgservice.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/orgs/orgservice.go -destination=pkg/services/orgs/mock/orgservice_mock.go -package=mock_orgservice
//

// Package mock_orgservice is a generated GoMock package.
package mock_orgservice

import (
	context "context"
	reflect "reflect"

	orgs "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	gomock "go.uber.org/mock/gomock"
)

// MockOrgService is a mock of OrgService interface.
type MockOrgService struct {
	ctrl     *gomock.Controller
	recorder *MockOrgServiceMockRecorder
	isgomock struct{}
}

// MockOrgServiceMockRecorder is the mock recorder for MockOrgService.
type MockOrgServiceMockRecorder struct {
	mock *MockOrgService
}

// NewMockOrgService creates a new mock instance.
func NewMockOrgService(ctrl *gomock.Controller) *MockOrgService {
	mock := &MockOrgService{ctrl: ctrl}
	mock.recorder = &MockOrgServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrgService) EXPECT() *MockOrgServiceMockRecorder {
	return m.recorder
}

// GetClinicById mocks base method.
func (m *MockOrgService) GetClinicById(ctx context.Context, id string) (orgs.Clinic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClinicById", ctx, id)
	ret0, _ := ret[0].(orgs.Clinic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClinicById indicates an expected call of GetClinicById.
func (mr *MockOrgServiceMockRecorder) GetClinicById(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClinicById", reflect.TypeOf((*MockOrgService)(nil).GetClinicById), ctx, id)
}

// GetClinicByLegacyId mocks base method.
func (m *MockOrgService) GetClinicByLegacyId(ctx context.Context, id int64) (orgs.Clinic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClinicByLegacyId", ctx, id)
	ret0, _ := ret[0].(orgs.Clinic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClinicByLegacyId indicates an expected call of GetClinicByLegacyId.
func (mr *MockOrgServiceMockRecorder) GetClinicByLegacyId(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClinicByLegacyId", reflect.TypeOf((*MockOrgService)(nil).GetClinicByLegacyId), ctx, id)
}

// GetClinicEmailsByLegacyId mocks base method.
func (m *MockOrgService) GetClinicEmailsByLegacyId(ctx context.Context, legacyClinicId int64) []orgs.ClinicEmails {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClinicEmailsByLegacyId", ctx, legacyClinicId)
	ret0, _ := ret[0].([]orgs.ClinicEmails)
	return ret0
}

// GetClinicEmailsByLegacyId indicates an expected call of GetClinicEmailsByLegacyId.
func (mr *MockOrgServiceMockRecorder) GetClinicEmailsByLegacyId(ctx, legacyClinicId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClinicEmailsByLegacyId", reflect.TypeOf((*MockOrgService)(nil).GetClinicEmailsByLegacyId), ctx, legacyClinicId)
}

// GetClinicsByProviderId mocks base method.
func (m *MockOrgService) GetClinicsByProviderId(ctx context.Context, providerId string) []orgs.Clinic {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClinicsByProviderId", ctx, providerId)
	ret0, _ := ret[0].([]orgs.Clinic)
	return ret0
}

// GetClinicsByProviderId indicates an expected call of GetClinicsByProviderId.
func (mr *MockOrgServiceMockRecorder) GetClinicsByProviderId(ctx, providerId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClinicsByProviderId", reflect.TypeOf((*MockOrgService)(nil).GetClinicsByProviderId), ctx, providerId)
}

// GetFormById mocks base method.
func (m *MockOrgService) GetFormById(ctx context.Context, formId string, inApp bool, language string) (orgs.FormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormById", ctx, formId, inApp, language)
	ret0, _ := ret[0].(orgs.FormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormById indicates an expected call of GetFormById.
func (mr *MockOrgServiceMockRecorder) GetFormById(ctx, formId, inApp, language any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormById", reflect.TypeOf((*MockOrgService)(nil).GetFormById), ctx, formId, inApp, language)
}

// GetFormByLegacyProviderId mocks base method.
func (m *MockOrgService) GetFormByLegacyProviderId(ctx context.Context, providerId int64, inApp bool, language string) (orgs.FormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormByLegacyProviderId", ctx, providerId, inApp, language)
	ret0, _ := ret[0].(orgs.FormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormByLegacyProviderId indicates an expected call of GetFormByLegacyProviderId.
func (mr *MockOrgServiceMockRecorder) GetFormByLegacyProviderId(ctx, providerId, inApp, language any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormByLegacyProviderId", reflect.TypeOf((*MockOrgService)(nil).GetFormByLegacyProviderId), ctx, providerId, inApp, language)
}

// GetFormByProviderUrl mocks base method.
func (m *MockOrgService) GetFormByProviderUrl(ctx context.Context, providerUrl string, inApp bool, language string) (orgs.FormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormByProviderUrl", ctx, providerUrl, inApp, language)
	ret0, _ := ret[0].(orgs.FormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormByProviderUrl indicates an expected call of GetFormByProviderUrl.
func (mr *MockOrgServiceMockRecorder) GetFormByProviderUrl(ctx, providerUrl, inApp, language any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormByProviderUrl", reflect.TypeOf((*MockOrgService)(nil).GetFormByProviderUrl), ctx, providerUrl, inApp, language)
}

// GetFormConfigurationFieldsByLegacyProviderId mocks base method.
func (m *MockOrgService) GetFormConfigurationFieldsByLegacyProviderId(ctx context.Context, providerId int64, language string) (orgs.ConfigurationFieldMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormConfigurationFieldsByLegacyProviderId", ctx, providerId, language)
	ret0, _ := ret[0].(orgs.ConfigurationFieldMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormConfigurationFieldsByLegacyProviderId indicates an expected call of GetFormConfigurationFieldsByLegacyProviderId.
func (mr *MockOrgServiceMockRecorder) GetFormConfigurationFieldsByLegacyProviderId(ctx, providerId, language any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormConfigurationFieldsByLegacyProviderId", reflect.TypeOf((*MockOrgService)(nil).GetFormConfigurationFieldsByLegacyProviderId), ctx, providerId, language)
}

// GetPlans mocks base method.
func (m *MockOrgService) GetPlans(ctx context.Context, providerId uint) ([]orgs.Plan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlans", ctx, providerId)
	ret0, _ := ret[0].([]orgs.Plan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlans indicates an expected call of GetPlans.
func (mr *MockOrgServiceMockRecorder) GetPlans(ctx, providerId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlans", reflect.TypeOf((*MockOrgService)(nil).GetPlans), ctx, providerId)
}

// GetProviderById mocks base method.
func (m *MockOrgService) GetProviderById(ctx context.Context, id string) (orgs.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProviderById", ctx, id)
	ret0, _ := ret[0].(orgs.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProviderById indicates an expected call of GetProviderById.
func (mr *MockOrgServiceMockRecorder) GetProviderById(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProviderById", reflect.TypeOf((*MockOrgService)(nil).GetProviderById), ctx, id)
}

// GetProviderByLegacyId mocks base method.
func (m *MockOrgService) GetProviderByLegacyId(ctx context.Context, id int64) (orgs.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProviderByLegacyId", ctx, id)
	ret0, _ := ret[0].(orgs.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProviderByLegacyId indicates an expected call of GetProviderByLegacyId.
func (mr *MockOrgServiceMockRecorder) GetProviderByLegacyId(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProviderByLegacyId", reflect.TypeOf((*MockOrgService)(nil).GetProviderByLegacyId), ctx, id)
}

// GetProviderByUrl mocks base method.
func (m *MockOrgService) GetProviderByUrl(ctx context.Context, url string) (orgs.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProviderByUrl", ctx, url)
	ret0, _ := ret[0].(orgs.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProviderByUrl indicates an expected call of GetProviderByUrl.
func (mr *MockOrgServiceMockRecorder) GetProviderByUrl(ctx, url any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProviderByUrl", reflect.TypeOf((*MockOrgService)(nil).GetProviderByUrl), ctx, url)
}

// GetProviderTransferConfig mocks base method.
func (m *MockOrgService) GetProviderTransferConfig(ctx context.Context, id string) (orgs.ProviderTransferConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProviderTransferConfig", ctx, id)
	ret0, _ := ret[0].(orgs.ProviderTransferConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProviderTransferConfig indicates an expected call of GetProviderTransferConfig.
func (mr *MockOrgServiceMockRecorder) GetProviderTransferConfig(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProviderTransferConfig", reflect.TypeOf((*MockOrgService)(nil).GetProviderTransferConfig), ctx, id)
}

// GetProvidersByIsUph mocks base method.
func (m *MockOrgService) GetProvidersByIsUph(ctx context.Context, isUph bool) ([]orgs.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProvidersByIsUph", ctx, isUph)
	ret0, _ := ret[0].([]orgs.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProvidersByIsUph indicates an expected call of GetProvidersByIsUph.
func (mr *MockOrgServiceMockRecorder) GetProvidersByIsUph(ctx, isUph any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProvidersByIsUph", reflect.TypeOf((*MockOrgService)(nil).GetProvidersByIsUph), ctx, isUph)
}

// GetSearchProviders mocks base method.
func (m *MockOrgService) GetSearchProviders(ctx context.Context, searchTerm string) ([]orgs.SearchProvider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchProviders", ctx, searchTerm)
	ret0, _ := ret[0].([]orgs.SearchProvider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchProviders indicates an expected call of GetSearchProviders.
func (mr *MockOrgServiceMockRecorder) GetSearchProviders(ctx, searchTerm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchProviders", reflect.TypeOf((*MockOrgService)(nil).GetSearchProviders), ctx, searchTerm)
}

// IsFacilityFunded mocks base method.
func (m *MockOrgService) IsFacilityFunded(ctx context.Context, providerId uint) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsFacilityFunded", ctx, providerId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsFacilityFunded indicates an expected call of IsFacilityFunded.
func (mr *MockOrgServiceMockRecorder) IsFacilityFunded(ctx, providerId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsFacilityFunded", reflect.TypeOf((*MockOrgService)(nil).IsFacilityFunded), ctx, providerId)
}
