package orgs

import (
	"encoding/json"
	"time"
)

type Plan struct {
	Id int `json:"id,omitempty"`

	Name string `json:"name"`

	DisplayName string `json:"display_name"`

	// Unix Timestamp (seconds)
	CreatedAt string `json:"created_at,omitempty"`

	IsActive bool `json:"is_active"`

	IsRecurring bool `json:"is_recurring"`

	// The cost of the plan, in regional currency. Denominated in cents (or equivalent).
	Amount int `json:"amount"`

	PeriodUnit string `json:"period_unit"`

	PeriodLength int `json:"period_length"`

	RegionId uint16 `json:"region_id"`

	ChargeTax bool `json:"charge_tax"`

	// Indicates if a provider plan option should be collapsed in the FE
	IsCollapsed bool `json:"is_collapsed"`
}

type ProviderPlan struct {
	PlanId      uint64 `json:"id,omitempty"`
	ProviderId  string `json:"providerId"`
	IsCollapsed bool   `json:"isCollapsed"`
}

type Provider struct {
	Id                   string            `json:"id"`
	Name                 string            `json:"name"`
	Language             string            `json:"language"`
	AdminEmail           string            `json:"adminEmail"`
	Active               bool              `json:"active"`
	TaxName              string            `json:"taxName"`
	Region               string            `json:"region"`
	Logo                 string            `json:"logo"`
	EnrollmentOrg        bool              `json:"enrollmentOrg"`
	Synonyms             string            `json:"synonyms"`
	DelegateReview       bool              `json:"delegateReview"` //PX manual delegate request review
	AllowDownload        bool              `json:"allowDownload"`
	IsUph                bool              `json:"isUph"`
	Url                  string            `json:"url"`
	AltUrl               string            `json:"altUrl"`
	SendReports          bool              `json:"sendReports"`
	ReportSendTime       int               `json:"reportSendTime"`
	LegacyId             int64             `json:"legacyId,omitempty"`
	RetentionPeriodYears int               `json:"retentionPeriodYears"`
	PatientsServed       int               `json:"patientsServed"`
	Modalities           map[string]string `json:"modalities"`
	CreatedAt            time.Time         `json:"createdAt"`
	UpdatedAt            time.Time         `json:"updatedAt"`
	Plans                []ProviderPlan    `json:"plans"`
	EnrolExpiryAge       int               `json:"enrolExpiryAge"`
}

type ProviderDetails struct {
	Name             string            `json:"name"`
	Region           string            `json:"region"`
	Logo             string            `json:"logo"`
	IsUph            bool              `json:"isUph"`
	Url              string            `json:"url"`
	Modalities       map[string]string `json:"modalities"`
	Plans            []ProviderPlan    `json:"plans"`
	IsFacilityFunded bool              `json:"isFacilityFunded"`
}

func (p *Provider) IsValid() bool {
	return p.Id != "" && p.Name != "" && p.LegacyId != 0
}

func (p *Provider) GetIsFacilityFunded() bool {
	for _, plan := range p.Plans {
		if plan.PlanId == FacilityFundedPlanId {
			return true
		}
	}
	return false
}

func (p *ProviderDetails) GetIsFacilityFunded() bool {
	for _, plan := range p.Plans {
		if plan.PlanId == FacilityFundedPlanId {
			return true
		}
	}
	return false
}

func (p Provider) MarshalJSON() ([]byte, error) {
	type alias Provider // alias to avoid infinite recursion
	return json.Marshal(&struct {
		alias
		IsFacilityFunded bool `json:"isFacilityFunded"`
	}{
		alias:            (alias)(p),
		IsFacilityFunded: p.GetIsFacilityFunded(),
	})
}

type Clinic struct {
	Id             string    `json:"id"`
	Name           string    `json:"name"`
	DisplayName    string    `json:"displayName"`
	AlternateNames string    `json:"altNames"`
	ProviderId     string    `json:"providerId"`
	ProviderName   string    `json:"providerName"`
	Address1       string    `json:"address1"`
	Address2       string    `json:"address2"`
	City           string    `json:"city"`
	State          string    `json:"state"`
	PostalCode     string    `json:"postalCode"`
	Country        string    `json:"country"`
	LegacyId       int64     `json:"legacyId"`
	Active         bool      `json:"active"`
	HubClinic      bool      `json:"hubClinic"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
	FullAddress    string    `json:"fullAddress"`
	Provider       Provider  `json:"provider"`
}

func (c *Clinic) IsValid() bool {
	return c.Id != "" && c.Name != ""
}

type ClinicEmails struct {
	LegacyId  int64     `json:"legacyId,omitempty"`
	IsPrimary bool      `json:"isPrimary"`
	ClinicId  string    `json:"clinicId"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"createdAt"`
}

func (i *ClinicEmails) IsValid() bool {
	return *i != ClinicEmails{}
}

type SearchProvider struct {
	ClinicLegacyId int64 `json:"clinicLegacyId"`

	ProviderLegacyId int64 `json:"providerLegacyId"`

	ClinicId string `json:"clinicId"`

	ProviderId string `json:"providerId"`

	DisplayName string `json:"displayName"`

	ClinicName string `json:"clinicName"`

	ProviderName string `json:"providerName"`

	Address string `json:"address"`

	City string `json:"city"`

	State string `json:"state"`

	Country string `json:"country"`

	PostalCode string `json:"postalCode"`

	ProviderUrl string `json:"providerUrl"`

	ProviderAltUrls string `json:"providerAltUrls"`

	ProviderRegion string `json:"providerRegion"`
}

type ProviderTransferConfig struct {
	ProviderId     string `json:"providerId"`
	SendReports    bool   `json:"sendReports"`
	ReportSendTime int    `json:"reportSendTime"`
}

type SyncProvidersDataMessage struct {
	ProviderLegacyId int64                `json:"legacyId"`
	Type             SyncProviderDataType `json:"dataType"`
}

func (i *SyncProvidersDataMessage) IsDefault() bool {
	return *i == SyncProvidersDataMessage{}
}

type SyncProviderDataType string

const (
	ProviderMetadataInfoType SyncProviderDataType = "metadata"
)
