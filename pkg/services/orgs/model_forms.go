/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package orgs

type StepResponse struct {
	Configurations      map[string]any        `json:"configurations,omitempty"`
	ConditionalProperty string                `json:"conditionalProperty,omitempty"`
	ConditionalNext     []ConditionalNextStep `json:"conditionalNext,omitempty"`
	Next                string                `json:"next,omitempty"`
	LeastRemainingSteps int                   `json:"leastRemainingSteps,omitempty"`
}

type ConditionalNextStep struct {
	Operator string `json:"operator,omitempty"`
	Value    any    `json:"value,omitempty"`
	Next     string `json:"next,omitempty"`
}

type FormResponse struct {
	GraphId           int64                   `json:"graphId,omitempty"`
	ProviderId        int64                   `json:"providerId,omitempty"`
	Provider          ProviderDetails         `json:"provider,omitempty"`
	RootStep          string                  `json:"rootStep,omitempty"`
	FormSteps         map[string]StepResponse `json:"formSteps,omitempty"`
	OfflineModalTitle string                  `json:"offlineModalTitle,omitempty"`
	OfflineModalBody  string                  `json:"offlineModalBody,omitempty"`
}

type ConfigurationFieldInfo struct {
	Name        string
	Type        string
	Optional    bool
	Description string
	Value       any
}

type ConfigurationFieldMap map[string]ConfigurationFieldInfo

type StepValidationBody struct {
	StepName         string         `json:"stepName"`
	LegacyProviderId int64          `json:"legacyProviderId"`
	RequestId        string         `json:"requestId"`
	InputFields      map[string]any `json:"inputFields"`
	DeviceId         string         `json:"deviceId"`
}

type StepValidationResponse struct {
	Valid                            bool              `json:"valid"`
	FieldValidationErrorMessageCodes map[string]string `json:"fieldValidationErrorMessageCodes"`
	RequestId                        string            `json:"requestId"`
}

type InputFieldInfo struct {
	Name           string `json:"name"`
	Type           string `json:"type"`
	ValidationType string `json:"validationType"`
	Optional       bool   `json:"optional"`
}

type StepInputFieldInfos struct {
	InputFieldInfos []InputFieldInfo `json:"inputFieldInfos"`
}
