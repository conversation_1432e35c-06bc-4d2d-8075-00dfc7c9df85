package orgs

import (
	"context"
	"errors"

	"github.com/dgraph-io/ristretto"
	"github.com/sirupsen/logrus"
)

type IOrgsvcCache interface {
	GetProvider(context.Context, string) (Provider, error)
	SetProvider(string, Provider, int64)
	GetClinic(ctx context.Context, clinicId string) (Clinic, error)
	SetClinic(clinicId string, clinic Clinic, cost int64)
}

type OrgsvcCache struct {
	cache *ristretto.Cache
}

func NewOrgsvcCache() (IOrgsvcCache, error) {
	cache, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1000,
		MaxCost:     100,
		BufferItems: 64,
	})
	if err != nil {
		return nil, err
	}

	return &OrgsvcCache{cache: cache}, nil
}

func (p *OrgsvcCache) GetProvider(ctx context.Context, providerId string) (Provider, error) {
	data, found := p.cache.Get(providerId)
	if found {
		cast, ok := data.(Provider)
		if !ok {
			return Provider{}, errors.New("unable to cast cache resp to provider")
		}
		return cast, nil
	}

	return Provider{}, errors.New("provider not in cache")
}

func (p *OrgsvcCache) SetProvider(providerId string, provider Provider, cost int64) {
	ok := p.cache.Set(providerId, provider, cost)
	if !ok {
		logrus.WithField("provider_id", providerId).Warn("not added to cache")
	}
	p.cache.Wait()
}

func (p *OrgsvcCache) GetClinic(ctx context.Context, clinicId string) (Clinic, error) {
	data, found := p.cache.Get(clinicId)
	if found {
		cast, ok := data.(Clinic)
		if !ok {
			return Clinic{}, errors.New("unable to cast cache resp to clinic")
		}
		return cast, nil
	}
	return Clinic{}, errors.New("clinic not in cache")
}

func (p *OrgsvcCache) SetClinic(clinicId string, clinic Clinic, cost int64) {
	ok := p.cache.Set(clinicId, clinic, cost)
	if !ok {
		logrus.WithField("clinic_id", clinicId).Warn("not added to cache")
	}
	p.cache.Wait()
}
