package planservice

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PlanServiceClient struct {
	PlanServiceUrl    string `json:"url"`
	PlanServiceUser   string `json:"name"`
	PlanServiceAPIKey string
	HttpClient        *httpclient.Client
	PlansCache        PlanCacher
}

type PlanService interface {
	GetPlans(ctx context.Context, recurring bool) ([]PlanV1, error)
	GetFeatures(ctx context.Context, planId int32) ([]Feature, error)
	AuthorizeFeature(
		ctx context.Context,
		featureId FeatureId,
		params FeatureAuthorizationParams,
	) (FeatureAuthorizationResponse, error)
	GetPlanById(ctx context.Context, planId int32) (PlanV2, error)
	GetPlanAmountAndCurrency(ctx context.Context, planId int32) (int, string, error)
	GetPlansByIds(ctx context.Context, planIds []uint64) ([]PlanV2, error)
}

func NewHTTPPlanServiceClient(url, user, apiKey string, client *http.Client) *PlanServiceClient {
	cacher, err := NewPlanCache()
	if err != nil {
		logrus.WithError(err).Fatal("unable to set up plans cache")
	}
	return &PlanServiceClient{
		PlanServiceUrl:    url,
		PlanServiceUser:   user,
		PlanServiceAPIKey: apiKey,
		HttpClient: httpclient.NewHTTPClient(
			client,
			nil,
		),
		PlansCache: cacher,
	}
}

func (ps *PlanServiceClient) setupBasicAuth() string {
	cred := fmt.Sprintf("%s:%s", ps.PlanServiceUser, ps.PlanServiceAPIKey)
	return base64.StdEncoding.EncodeToString([]byte(cred))
}

// GetPlansByIds get plans for all request plan IDs
func (ps *PlanServiceClient) GetPlansByIds(ctx context.Context, planIds []uint64) ([]PlanV2, error) {
	lg := logutils.DebugCtxLogger(ctx)

	var results []PlanV2
	body, err := json.Marshal(planIds)
	if err != nil {
		lg.WithError(err).Info("error unmarshaling response body")
		return results, err
	}
	endpoint := fmt.Sprintf("%s/v1/plans/search", ps.PlanServiceUrl)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       body,
		AuthScheme:    httpclient.Basic,
		AuthKey:       ps.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	respBody, code, err := ps.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return results, err
	}
	defer respBody.Close()

	responseBytes, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Info("error reading resp body")
		return results, err
	}

	err = json.Unmarshal(responseBytes, &results)
	if err != nil {
		lg.WithError(err).Info("error unmarshaling resp body")
		return results, err
	}

	return results, nil
}

func (ps *PlanServiceClient) GetPlans(ctx context.Context, recurring bool) ([]PlanV1, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("recurring", recurring)

	endpoint := fmt.Sprintf("%s/v1/plans", ps.PlanServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		QueryParams:   map[string]string{"recurring": fmt.Sprint(recurring), "active": fmt.Sprint(true)},
		AuthScheme:    httpclient.Basic,
		AuthKey:       ps.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest},
	}

	respBody, code, err := ps.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}

	if code == http.StatusBadRequest {
		return nil, ErrGetPlans{Recurring: recurring}
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		return nil, err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var plans []PlanV1
	err = json.Unmarshal(gotBody, &plans)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return nil, err
	}

	return plans, nil
}

func (ps *PlanServiceClient) GetFeatures(ctx context.Context, planId int32) ([]Feature, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("plan_id", planId)

	endpoint := fmt.Sprintf("%s/v1/plans/%d/features", ps.PlanServiceUrl, planId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       ps.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest},
	}

	respBody, code, err := ps.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}

	if code == http.StatusBadRequest {
		return nil, ErrGetFeatures{PlanId: planId}
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		return nil, err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var features []Feature
	err = json.Unmarshal(gotBody, &features)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return nil, err
	}

	return features, nil
}

func (ps *PlanServiceClient) AuthorizeFeature(
	ctx context.Context,
	featureId FeatureId,
	params FeatureAuthorizationParams,
) (FeatureAuthorizationResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"feature_id":  featureId,
		"account_id":  params.AccountId,
		"provider_id": params.ProviderId,
	})

	if !params.Valid() {
		lg.Error("invalid params")
		return FeatureAuthorizationResponse{}, errors.New(http.StatusText(http.StatusBadRequest))
	}

	endpoint := fmt.Sprintf("%s/v1/features/%d/authorize", ps.PlanServiceUrl, featureId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "GET",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		QueryParams: map[string]string{
			"account_id":  params.AccountId,
			"provider_id": fmt.Sprint(params.ProviderId),
		},
		AuthKey:       ps.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}

	respBody, status, err := ps.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send authorize feature request")
		return FeatureAuthorizationResponse{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusNotFound:
		return FeatureAuthorizationResponse{}, ErrFeatureNotFound{FeatureId: featureId}
	case http.StatusBadRequest:
		return FeatureAuthorizationResponse{}, ErrBadAuthorizeFeatureRequest{
			FeatureId:  featureId,
			AccountId:  params.AccountId,
			ProviderId: params.ProviderId,
		}
	case http.StatusOK:
		body, err := io.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return FeatureAuthorizationResponse{}, err
		}

		var response FeatureAuthorizationResponse
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing authorize feature request")
			return response, err
		}

		return response, nil
	default:
		return FeatureAuthorizationResponse{}, fmt.Errorf("got %v back from acct svc", status)
	}
}

func (ps *PlanServiceClient) GetPlanAmountAndCurrency(
	ctx context.Context,
	planId int32,
) (int, string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("plan_id", planId)
	var revenue int
	var currency string
	plan, err := ps.GetPlanById(ctx, planId)
	if err != nil {
		lg.WithError(err).Error("could not get plan from plan svc")
		return revenue, currency, err
	}
	if plan.RegionId == 1 {
		currency = "CAD"
	} else if plan.RegionId == 2 {
		currency = "USD"
	}
	return int(plan.Amount) / 100, currency, nil
}

func (ps *PlanServiceClient) GetPlanById(ctx context.Context, planId int32) (PlanV2, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("plan_id", planId)

	plan, err := ps.PlansCache.Get(ctx, planId)
	if err == nil {
		return plan, nil
	}

	lg.WithError(err).Error("error getting plan from cache, fall back to getting from planSvc")

	// fall back to getting from planSvc
	endpoint := fmt.Sprintf("%s/v1/plans/%d", ps.PlanServiceUrl, planId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       ps.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := ps.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send get plan by id request")
		return PlanV2{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusNotFound:
		return PlanV2{}, ErrPlanNotFound{PlanId: planId}
	case http.StatusOK:
		body, err := io.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return PlanV2{}, err
		}

		var plan PlanV2
		err = json.Unmarshal(body, &plan)
		if err != nil {
			lg.WithError(err).Error("error unmarshaling response body")
			return PlanV2{}, err
		}

		// set plan to plan cache
		ps.PlansCache.Set(planId, plan, 1)
		return plan, nil
	default:
		return PlanV2{}, fmt.Errorf("got %v back from plan svc", status)
	}
}
