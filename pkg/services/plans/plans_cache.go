package planservice

import (
	"context"
	"errors"

	"github.com/dgraph-io/ristretto"
	"github.com/sirupsen/logrus"
)

type PlanCacher interface {
	Get(context.Context, int32) (PlanV2, error)
	Set(int32, PlanV2, int64)
}

type PlanCache struct {
	cache *ristretto.Cache
}

func NewPlanCache() (PlanCacher, error) {
	cache, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1000,
		MaxCost:     100,
		BufferItems: 64,
	})
	if err != nil {
		return nil, err
	}

	return &PlanCache{cache: cache}, nil
}

func (p *PlanCache) Get(ctx context.Context, planId int32) (PlanV2, error) {
	planRaw, found := p.cache.Get(planId)
	if found {
		cast, ok := planRaw.(PlanV2)
		if !ok {
			return PlanV2{}, errors.New("unable to cast cache resp to Plan")
		}
		return cast, nil
	}
	return PlanV2{}, errors.New("plan not in cacher")
}

func (p *PlanCache) Set(planId int32, plan PlanV2, cost int64) {
	ok := p.cache.Set(planId, plan, cost)
	if !ok {
		logrus.WithField("plan", planId).Warn("not added to cache")
	}
	p.cache.Wait()
}
