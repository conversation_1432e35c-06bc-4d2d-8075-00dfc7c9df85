package planservice

type FeatureAuthorizationParams struct {
	AccountId  string
	ProviderId uint64 // i.e. OrgId
}

func (p FeatureAuthorizationParams) Valid() bool {
	return p.AccountId != "" || p.ProviderId > 0
}

type FeatureAuthorizationResponse struct {
	Authorized bool   `json:"authorized"`
	OrderId    string `json:"order_id,omitempty"`
}

type PlanV2 struct {
	Id                    int32  `json:"id,omitempty"`
	Name                  string `json:"name"`
	DisplayName           string `json:"display_name"`
	CreatedAt             string `json:"created_at,omitempty"` // Unix Timestamp (seconds)
	IsActive              bool   `json:"is_active"`
	IsRecurring           bool   `json:"is_recurring"`
	Amount                int32  `json:"amount"` // The cost of the plan, in regional currency. Denominated in cents (or equivalent).
	PeriodUnit            string `json:"period_unit"`
	PeriodLength          int32  `json:"period_length"`
	RegionId              uint16 `json:"region_id"`
	ChargeTax             bool   `json:"charge_tax"`
	GeneralProviderAccess bool   `json:"general_provider_access"`
	FullUnlockAccess      bool   `json:"full_unlock_access"`

	// not returned from plansvc
	Features []Feature `json:"features,omitempty"`
}

type Feature struct {
	Id          int32  `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	CreatedAt   string `json:"created_at,omitempty"`
}

type FeatureId uint

const (
	REQUEST_RECORDS     FeatureId = 1
	STORAGE             FeatureId = 2
	SHARING             FeatureId = 3
	REPORT_READER       FeatureId = 4
	FAMILY_MEMBERS      FeatureId = 5
	CD_UPLOADING        FeatureId = 6
	HEALTH_RECORDS      FeatureId = 7
	SECOND_OPINION      FeatureId = 8 // deprecated
	UPH                 FeatureId = 9
	CD_DOWNLOAD         FeatureId = 10
	USB_DOWNLOAD        FeatureId = 11
	PRINT_ACCESS_PAGE   FeatureId = 12
	SHARE_EMAIL         FeatureId = 13
	SHARE_FAX           FeatureId = 14
	RHO                 FeatureId = 15
	INSTANT_DICOM       FeatureId = 16
	MYCARE_NAVIGATOR    FeatureId = 17
	AUTO_VERIFY_EXAM    FeatureId = 18
	FOLLOW_UP           FeatureId = 19
	ASK_MY_DOCTOR       FeatureId = 20
	BREAST_RISK_SCORING FeatureId = 21
	IMAGE_READER        FeatureId = 22
	REPORT_EXPLANATIONS FeatureId = 23
)

type GetPlansRequest struct {
	Email string `json:"email"`
}

type PlanV1 struct {
	Id          int    `json:"id,omitempty"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	// Unix Timestamp (seconds)
	CreatedAt   string `json:"created_at,omitempty"`
	IsActive    bool   `json:"is_active"`
	IsRecurring bool   `json:"is_recurring"`
	// The cost of the plan, in regional currency. Denominated in cents (or equivalent).
	Amount       int    `json:"amount"`
	PeriodUnit   string `json:"period_unit"`
	PeriodLength int    `json:"period_length"`
	RegionId     uint16 `json:"region_id"`
	ChargeTax    bool   `json:"charge_tax"`
	// Indicates if a provider plan option should be collapsed in the FE
	IsCollapsed bool `json:"is_collapsed"`
}
