package planservice

import "fmt"

// to express that there was an error getting plans
type ErrGetPlans struct {
	Recurring bool
}

func (e ErrGetPlans) Error() string {
	return fmt.Sprintf(
		"planservice did not accept get plans request with with recurring:%t",
		e.Recurring,
	)
}

// to express that there was an error getting features for a plan
type ErrGetFeatures struct {
	PlanId int32
}

func (e ErrGetFeatures) Error() string {
	return fmt.Sprintf(
		"planservice did not accept get features request with with plan_id:%d",
		e.PlanId,
	)
}

// to express that the feature was not found
type ErrFeatureNotFound struct {
	FeatureId FeatureId
}

func (e ErrFeatureNotFound) Error() string {
	return fmt.Sprintf(
		"planservice did not find feature in authorize feature request with feature_id:%d",
		e.FeatureId,
	)
}

// to express that there was an error authorizing a feature
type ErrBadAuthorizeFeatureRequest struct {
	FeatureId  FeatureId
	AccountId  string
	ProviderId uint64
}

func (e ErrBadAuthorizeFeatureRequest) Error() string {
	return fmt.Sprintf(
		"planservice did not accept authorize feature request with feature_id:%d,account_id:%s,provider_id:%d",
		e.FeatureId,
		e.AccountId,
		e.ProviderId,
	)
}

// to express that the plan was not found
type ErrPlanNotFound struct {
	PlanId int32
}

func (e ErrPlanNotFound) Error() string {
	return fmt.Sprintf(
		"planservice did not find plan in get plan by id request with plan_id:%d",
		e.PlanId,
	)
}
