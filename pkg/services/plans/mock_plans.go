package planservice

import (
	"context"
	"fmt"
)

type PlanSvcMock struct {
	// general
	ReturnError bool
	MockError   error

	// func-specific
	GetPlansReturn []PlanV1

	ExpectedPlanId      int32
	GetFeaturesReturn   []Feature
	GetPlanByIdReturn   PlanV2
	GetPlansByIdsReturn []PlanV2
}

func (m PlanSvcMock) GetPlansByIds(ctx context.Context, planIds []uint64) ([]PlanV2, error) {
	return m.GetPlansByIdsReturn, nil
}

func (m PlanSvcMock) GetPlans(ctx context.Context, recurring bool) ([]PlanV1, error) {
	if m.ReturnError {
		return nil, m.MockError
	}
	return m.GetPlansReturn, nil
}

func (m PlanSvcMock) GetFeatures(ctx context.Context, planId int32) ([]Feature, error) {
	if m.ReturnError {
		return nil, m.MockError
	}
	if m.ExpectedPlanId != planId {
		return nil, fmt.Errorf(
			"unexpected param(s); expected planId: %d, got %d",
			m.ExpectedPlanId,
			planId,
		)
	}
	return m.GetFeaturesReturn, nil
}

func (m PlanSvcMock) AuthorizeFeature(
	ctx context.Context,
	featureId FeatureId,
	params FeatureAuthorizationParams,
) (FeatureAuthorizationResponse, error) {
	return FeatureAuthorizationResponse{}, nil
}

func (m PlanSvcMock) GetPlanById(ctx context.Context, planId int32) (PlanV2, error) {
	if m.ReturnError {
		return PlanV2{}, m.MockError
	}
	return m.GetPlanByIdReturn, nil
}

func (m PlanSvcMock) GetPlanAmountAndCurrency(
	ctx context.Context,
	planId int32,
) (int, string, error) {
	plan, err := m.GetPlanById(ctx, planId)
	if err != nil {
		return 0, "CAD", err
	}
	var currency string
	if plan.RegionId == 1 {
		currency = "CAD"
	} else if plan.RegionId == 2 {
		currency = "USD"
	}
	return int(plan.Amount) / 100, currency, nil
}
