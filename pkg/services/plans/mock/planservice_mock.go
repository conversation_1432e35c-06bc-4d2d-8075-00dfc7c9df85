// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/plans/plans.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/plans/plans.go -destination=pkg/services/plans/mock/planservice_mock.go -package=mock_planservice
//

// Package mock_planservice is a generated GoMock package.
package mock_planservice

import (
	context "context"
	reflect "reflect"

	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	gomock "go.uber.org/mock/gomock"
)

// MockPlanService is a mock of PlanService interface.
type MockPlanService struct {
	ctrl     *gomock.Controller
	recorder *MockPlanServiceMockRecorder
	isgomock struct{}
}

// MockPlanServiceMockRecorder is the mock recorder for MockPlanService.
type MockPlanServiceMockRecorder struct {
	mock *MockPlanService
}

// NewMockPlanService creates a new mock instance.
func NewMockPlanService(ctrl *gomock.Controller) *MockPlanService {
	mock := &MockPlanService{ctrl: ctrl}
	mock.recorder = &MockPlanServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlanService) EXPECT() *MockPlanServiceMockRecorder {
	return m.recorder
}

// AuthorizeFeature mocks base method.
func (m *MockPlanService) AuthorizeFeature(ctx context.Context, featureId planservice.FeatureId, params planservice.FeatureAuthorizationParams) (planservice.FeatureAuthorizationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthorizeFeature", ctx, featureId, params)
	ret0, _ := ret[0].(planservice.FeatureAuthorizationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthorizeFeature indicates an expected call of AuthorizeFeature.
func (mr *MockPlanServiceMockRecorder) AuthorizeFeature(ctx, featureId, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthorizeFeature", reflect.TypeOf((*MockPlanService)(nil).AuthorizeFeature), ctx, featureId, params)
}

// GetFeatures mocks base method.
func (m *MockPlanService) GetFeatures(ctx context.Context, planId int32) ([]planservice.Feature, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeatures", ctx, planId)
	ret0, _ := ret[0].([]planservice.Feature)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFeatures indicates an expected call of GetFeatures.
func (mr *MockPlanServiceMockRecorder) GetFeatures(ctx, planId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeatures", reflect.TypeOf((*MockPlanService)(nil).GetFeatures), ctx, planId)
}

// GetPlanAmountAndCurrency mocks base method.
func (m *MockPlanService) GetPlanAmountAndCurrency(ctx context.Context, planId int32) (int, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlanAmountAndCurrency", ctx, planId)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPlanAmountAndCurrency indicates an expected call of GetPlanAmountAndCurrency.
func (mr *MockPlanServiceMockRecorder) GetPlanAmountAndCurrency(ctx, planId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlanAmountAndCurrency", reflect.TypeOf((*MockPlanService)(nil).GetPlanAmountAndCurrency), ctx, planId)
}

// GetPlanById mocks base method.
func (m *MockPlanService) GetPlanById(ctx context.Context, planId int32) (planservice.PlanV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlanById", ctx, planId)
	ret0, _ := ret[0].(planservice.PlanV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlanById indicates an expected call of GetPlanById.
func (mr *MockPlanServiceMockRecorder) GetPlanById(ctx, planId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlanById", reflect.TypeOf((*MockPlanService)(nil).GetPlanById), ctx, planId)
}

// GetPlans mocks base method.
func (m *MockPlanService) GetPlans(ctx context.Context, recurring bool) ([]planservice.PlanV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlans", ctx, recurring)
	ret0, _ := ret[0].([]planservice.PlanV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlans indicates an expected call of GetPlans.
func (mr *MockPlanServiceMockRecorder) GetPlans(ctx, recurring any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlans", reflect.TypeOf((*MockPlanService)(nil).GetPlans), ctx, recurring)
}

// GetPlansByIds mocks base method.
func (m *MockPlanService) GetPlansByIds(ctx context.Context, planIds []uint64) ([]planservice.PlanV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlansByIds", ctx, planIds)
	ret0, _ := ret[0].([]planservice.PlanV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlansByIds indicates an expected call of GetPlansByIds.
func (mr *MockPlanServiceMockRecorder) GetPlansByIds(ctx, planIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlansByIds", reflect.TypeOf((*MockPlanService)(nil).GetPlansByIds), ctx, planIds)
}
