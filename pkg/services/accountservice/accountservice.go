package accountservice

import (
	"context"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

type AccountService interface {
	AddPatient(ctx context.Context, acctId string, rB Patient) (string, error)
	UpdatePatient(ctx context.Context, acctId string, patientId string, rB Patient) error
	GetPatients(ctx context.Context, acctId string) ([]Patient, error)
	GetPatient(ctx context.Context, acctId string, patientId string) (Patient, error)
	DeletePatient(ctx context.Context, acctId string, patientId string) error
	UpdateAccountSettings(
		ctx context.Context,
		acctId string,
		accountSettings UserSettingsRequest,
	) error
	GetAccountSettings(ctx context.Context, acctId string) (*UserSettings, error)
	CreateAccountAndSendVerifyEmail(ctx context.Context, newAcct NewAccount) (string, error)
	AddNewRegion(ctx context.Context, regionID uint16, acctID string) error
	SetPasswordAndVerify(ctx context.Context, acctID string, password string) error
	VerifyEmail(
		ctx context.Context,
		accountTypeEndpoint AccountTypeEndpoint,
		verification Verification,
	) (string, error)
	GetPasswordSetupVerificationToken(
		ctx context.Context,
		accountId string,
	) (VerificationToken, error)
	LockAccount(ctx context.Context, acctID string) error
	AuthenticateAcctAndGetToken(
		ctx context.Context,
		accountType AccountTypeEndpoint,
		email string,
		password string,
		ip string,
	) (LoginSession, error)
	LookupAccountIdByEmail(ctx context.Context, email string) (string, error)
	LookupAccountIdByPatientId(ctx context.Context, patientId string) (string, error)
	GetAccountInfo(ctx context.Context, acctId string) (*Account, error)
	GetAccountInfoByEmail(ctx context.Context, email string) (*Account, error)
	UpdateEmail(ctx context.Context, emailUpdate UsersEmailUpdate) error
	InitUpdateEmail(ctx context.Context, oldEmail string, newEmail string) error
	RefreshToken(ctx context.Context, refreshToken string, ip string) (LoginSession, error)
	DeleteAccount(ctx context.Context, acctId string) error
	DeleteOrder(ctx context.Context, acctId string, orderId string) error
	CancelSubscription(ctx context.Context, orderId string) error
	ResetPasswordInit(
		ctx context.Context,
		accountTypeEndpoint AccountTypeEndpoint,
		email string,
	) error
	ResetPassword(
		ctx context.Context,
		accountTypeEndpoint AccountTypeEndpoint,
		resetInfo AccountEdit,
	) error
	CreateOrder(ctx context.Context, acctId string, newOrder NewOrder) (CreateOrderResponse, error)
	CreateOrderIntent(
		ctx context.Context,
		orderIntent NewOrderIntent,
	) (CreateOrderIntentResponse, error)
	UpdateOrderIntent(ctx context.Context, updateOrderIntent UpdateOrderIntent) error
	RefundOrder(ctx context.Context, acctId string, orderId string) error
	ToggleAutoRenew(ctx context.Context, acctId string, orderId string, autoRenew bool) error
	CreateOrderWithoutEmail(
		ctx context.Context,
		acctId string,
		newOrder NewOrder,
	) (CreateOrderResponse, error)
	AccountLoginSSO(
		ctx context.Context,
		ssoToken string,
		ip string,
	) (AccountSSOLoginResponse, error)
	AccountLoginGoogle(
		ctx context.Context,
		jwt string,
		ip string,
	) (GoogleSSOLoginResponse, error)
	AccountLoginViaSSO(
		ctx context.Context,
		token, accountId, ipAddress string,
	) (AccountSSOLoginResponse, error)
	GenerateAccountLoginSSOToken(
		ctx context.Context,
		accountId string,
		expiresAt time.Time,
	) (string, error)
	GetAccountMainRegion(ctx context.Context, acctId string) (uint16, string, error)
	UpdateOrderPaymentDetails(
		ctx context.Context,
		acctId string,
		orderId string,
		paymentDetails OrderPaymentDetailsRequest,
	) error
	GetOrders(ctx context.Context, acctId string, filters map[string]bool) ([]Order, error)
	GetOrderById(ctx context.Context, acctId string, orderId string) (OrderDetails, error)
	GetOrderPlanId(ctx context.Context, acctId, orderId string) uint64
	GetInactiveOrderPlanId(ctx context.Context, acctId string) uint64
	LockAccountWithToken(ctx context.Context, lockToken string) error
	DeactivateAccount(ctx context.Context, accountId string, token string) error
	SetAccountOwner(ctx context.Context, accountId string, request models.SetAccountOwnerRequest) error
	CreateOrderActionReason(
		ctx context.Context,
		acctId string,
		orderId string,
		or OrderActionReason,
	) error
	IsSSOEnabled(ctx context.Context, accountId string) bool
	CreatePhysicianAccount(
		ctx context.Context,
		newAcct PhysicianRegisterData,
	) (string, error)
	GetPhysicianAccount(
		ctx context.Context,
		accountId string,
	) (PhysicianAccount, error)
	PatchPhysician(
		ctx context.Context,
		accountId string,
		physicianId string,
		request PhysicianRequest,
	) (PhysicianVerificationData, error)
	PostPhysicianLicense(
		ctx context.Context,
		accountId string,
		physicianId string,
		request PhysicianLicenceRequest,
	) error
	PostVerifyPhysicianNotificationMethod(
		ctx context.Context,
		accountId string,
		physicianId string,
		request PhysicianNotificationRequest,
	) error
	SearchPhysicianAccount(
		ctx context.Context,
		request PhysicianNotificationRequest,
	) ([]string, error)
	GetOrderForId(ctx context.Context, orderId string) (Order, error)
	GetAllPhysicianPermissions(
		ctx context.Context,
		accountId string,
	) (permission map[int64][]PhysicianAccountPermission, err error)
	GetPhysicianPermissionsByPermissionName(
		ctx context.Context,
		accountId string,
		permissionName string,
	) (permission map[int64][]PhysicianAccountPermission, err error)
	GetPhysicianPermissionsByProviderIdAndPermissionName(
		ctx context.Context,
		accountId string,
		providerId int64,
		permissionName string,
	) (permission map[int64][]PhysicianAccountPermission, err error)
	PostAccountVerificationCode(ctx context.Context, accountId string) (string, error)
	GetAccountVerificationCode(ctx context.Context, token string) (VerificationCodeResponse, error)
	GetAccountEmailVerification(ctx context.Context, token string) (EmailVerification, error)
	PatchOrder(ctx context.Context, accountID string, orderID string, por PatchOrderRequest) error
	AccountHasAccessToPatient(ctx context.Context, accountId, patientId string) bool
	GetAllPatientProfileValidationsForAccount(ctx context.Context, accountId string) (map[string]PatientProfileValidation, error)
	GetAccountPatientProfileValidation(ctx context.Context, accountId, patientId string) (PatientProfileValidation, error)
	GetOrCreatePatient(ctx context.Context, acctId string, pt Patient, createOnConflict bool) (string, error)
}
