package accountservice

import (
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

const (
	REQUEST_ACCOUNT_CREATION = "request_account_creation"
	REGISTER_PAGE            = "register_page"
	PROVIDER_CONSENT         = "provider_consent"

	// patient creation sources
	PATIENT_CREATION_ACCOUNT         = "account"
	PATIENT_CREATION_REQUEST         = "request"
	PATIENT_CREATION_CONSENT         = "consent"
	PATIENT_CREATION_UI              = "ui"
	PATIENT_CREATION_EXAM_ACTIVATION = "exam_activation"
	PATIENT_CREATION_PAYMENT_INTENT  = "payment_intent" // express checkout
)

type CheckCodeResponse struct {
	Valid bool `json:"valid"`
}

type NewAccount struct {
	AccountId             string              `json:"account_id"` //ksuid generated by RR. This will be deprecated as RR's functionality moves to AS
	Email                 string              `json:"email"`
	Password              string              `json:"password"`
	PasswordHash          string              `json:"hashed_password"`
	MainRegion            uint16              `json:"main_region"`
	Settings              UserSettingsRequest `json:"settings"`
	DeviceId              string              `json:"device_id"`
	AccountCreationSource string              `json:"source"`
}

func (na *NewAccount) Valid() bool {
	return na.Email != ""
}

type AccountUpdate struct {
	Email        string `json:"email,omitempty"`
	Password     string `json:"password,omitempty"`
	PasswordHash string `json:"hashed_password,omitempty"`
	Verified     bool   `json:"verified,omitempty"`
	Locked       bool   `json:"locked,omitempty"`
}

type AccountLogin struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	IP       string `json:"ip"`
}

// This model is used for updating user settings, use the corresponding field to change report notification setting
// or complete a single onboarding task by passing in the enum of the task
type UserSettingsRequest struct {
	ReportNotification *bool `json:"reportNotification,omitempty"`

	Task OnboardingTask `json:"task,omitempty"`

	Language string `json:"language,omitempty"`

	ShowOnboarding *bool `json:"showOnboarding,omitempty"`
}

type OnboardingTask uint

// List of OnboardingTask
const (
	TaskVideo OnboardingTask = 1 << iota
	TaskRequest
	TaskUpload
	TaskConnect
)

type UserSettings struct {
	ReportNotification bool `json:"reportNotification,omitempty"`

	ShowOnboarding bool `json:"showOnboarding,omitempty"`

	OnboardingTasks *OnboardingTaskCompletion `json:"onboardingTasks,omitempty"`

	Language string `json:"language,omitempty"`
}

type OnboardingTaskCompletion struct {
	CompleteVideo bool `json:"completeVideo,omitempty"`

	CompleteRequest bool `json:"completeRequest,omitempty"`

	CompleteUpload bool `json:"completeUpload,omitempty"`

	CompleteConnect bool `json:"completeConnect,omitempty"`
}

type Account struct {
	AccountId         string            `json:"account_id"`
	Email             string            `json:"email"`
	IsPassSet         bool              `json:"is_pass_set"`
	MainRegion        uint16            `json:"main_region"`
	AdditionalRegions map[uint16]uint64 `json:"additional_regions"`
	IsLocked          bool              `json:"is_locked"`
	IsVerified        bool              `json:"is_verified"`
	CreatedAt         string            `json:"created_at,omitempty"` // ISO8601 format
	UpdatedAt         string            `json:"updated_at,omitempty"` // ISO8601 format
	LoginAttempts     uint64            `json:"login_attempts,omitempty"`
}
type Verification struct {
	Token    string `json:"token"`
	Password string `json:"password,omitempty"`
	IP       string `json:"ip"`
}

type VerificationToken struct {
	Token string `json:"token,omitempty"`
}

type VerificationResponse struct {
	Token     string `json:"token"`
	Refresh   string `json:"refresh"`
	AccountId string `json:"accountId"`
}

type PasswordSetup struct {
	Token     string `json:"token"`
	AccountId string `json:"accountId"`
}

type VerifyEmailResponse struct {
	Token     string `json:"token"`
	AccountId string `json:"accountId"`
}

type UsersEmailUpdate struct {
	Token string `json:"token"`
}

type LoginSession struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refresh,omitempty"`
}

type LoginRefresh struct {
	RefreshToken string `json:"refresh"`
	IP           string `json:"ip"`
}

type AccountEdit struct {
	Token    string `json:"token"`
	Email    string `json:"email"`
	Password string `json:"password"`
	Code     string `json:"code"`
}

type NewOrder struct {
	PlanId           uint64 `json:"plan_id"`
	OrgId            uint64 `json:"org_id,omitempty"`
	RegionId         uint16 `json:"region_id"`
	PaymentToken     string `json:"token"`
	Country          string `json:"country"` // "CA" or "US"
	ZipCode          string `json:"zip"`
	RequestId        string `json:"request_id"`
	IsActive         bool   `json:"is_active,omitempty"`
	DisableAutoRenew bool   `json:"disable_auto_renew,omitempty"`
	DiscountName     string `json:"discount_name,omitempty"`
	Source           string `json:"source"`
}

type NewOrderIntent struct {
	AccountId        string `json:"accountId"`
	PlanId           uint64 `json:"planId"`
	ProviderId       uint64 `json:"providerId,omitempty"`
	PaymentType      string `json:"paymentType"`
	Country          string `json:"country"`
	ZipCode          string `json:"zip"`
	IsActive         bool   `json:"isActive"`
	DisableAutoRenew bool   `json:"disableAutoRenew,omitempty"`
	DiscountName     string `json:"discount_name,omitempty"`
}

type UpdateOrderIntent struct {
	AccountId       string `json:"accountId"`
	OrderId         string `json:"orderId"`
	Country         string `json:"country"`
	ZipCode         string `json:"zip"`
	PaymentIntentId string `json:"paymentIntentId"`
	PaymentMethod   string `json:"paymentMethod"`
	Source          string `json:"source"`
}

type Order struct {
	OrderId        string    `json:"order_id"`
	AccountId      string    `json:"account_id"`
	PlanId         uint64    `json:"plan_id"`
	OrgId          uint64    `json:"org_id"`
	LastInvoiceId  string    `json:"last_invoice_id"`
	IsRefunded     *bool     `json:"is_refunded"`      // needs to be a pointer since empty value of bool is `false` but that's a valid value
	IsActive       *bool     `json:"is_active"`        // needs to be a pointer since empty value of bool is `false` but that's a valid value
	IsAutoRenewing *bool     `json:"is_auto_renewing"` // needs to be a pointer since empty value of bool is `false` but that's a valid value
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	ExpiresAt      time.Time `json:"expires_at"`
	CancelledAt    time.Time `json:"cancelled_at"`
}

type OrderDetails struct {
	Type       string      `json:"type,omitempty"`
	Members    []string    `json:"members,omitempty"`
	StartDate  time.Time   `json:"start_date,omitempty"`
	ExpiryDate time.Time   `json:"expiry_date,omitempty"`
	AutoRenew  bool        `json:"autorenew,omitempty"`
	Card       CardDetails `json:"payment_card,omitempty"`
}

type CardDetails struct {
	Country         string `json:"country"`
	MaskedCard      string `json:"card_num_masked"`
	CardExpiryMonth int    `json:"card_exp_month"`
	CardExpiryYear  int    `json:"card_exp_year"`
	Zip             string `json:"zip"`
	Type            string `json:"card_type"`
}

func (no *NewOrder) Valid() bool {
	if no.PlanId == 0 || no.RegionId == 0 {
		return false
	}
	return true
}

func (noi *NewOrderIntent) Valid() bool {
	if noi.PlanId == 0 || noi.AccountId == "" {
		return false
	}
	return true
}

func (uoi *UpdateOrderIntent) Valid() bool {
	if uoi.OrderId == "" ||
		uoi.AccountId == "" ||
		uoi.PaymentIntentId == "" ||
		uoi.PaymentMethod == "" ||
		uoi.Country == "" ||
		uoi.ZipCode == "" {
		return false
	}
	return true
}

type CreateOrderResponse struct {
	OrderId string `json:"order_id"`
}

type CreateOrderIntentResponse struct {
	ClientSecret string `json:"clientSecret"`
	OrderId      string `json:"orderId"`
	IsDuplicate  bool   `json:"isDuplicate"`
	Process      bool   `json:"process"`
}

type OrderRefundRequest struct {
	RefundAmount int `json:"refundAmount"`
	RefundCycle  int `json:"refundCycle"`
}

type AccountSSOLoginRequest struct {
	Token     string `json:"token"`
	IpAddress string `json:"ip"`
}

type SSOLoginResponse interface {
	GetAccountID() string
}

type AccountSSOLoginResponse struct {
	Token                 string  `json:"token"`
	AccExists             bool    `json:"accExists"`
	RefreshToken          string  `json:"refresh"`
	UserExists            bool    `json:"userExists"`
	IsNewlyCreatedAccount bool    `json:"isNewlyCreatedAccount"`
	PatientProfile        Patient `json:"patientProfile"`
}

func (r AccountSSOLoginResponse) GetAccountID() string {
	return r.PatientProfile.AccountId
}

// massage patientProfile to camel case
type GoogleSSOLoginResponse struct {
	Token                 string            `json:"token"`
	AccExists             bool              `json:"accExists"`
	RefreshToken          string            `json:"refresh"`
	UserExists            bool              `json:"userExists"`
	IsNewlyCreatedAccount bool              `json:"isNewlyCreatedAccount"`
	PatientProfile        models.PhiPatient `json:"patientProfile"`
}

func (r GoogleSSOLoginResponse) GetAccountID() string {
	return r.PatientProfile.AccountId
}

type OrderPaymentDetailsRequest struct {
	PaymentToken string `json:"token"`

	Country string `json:"country"`

	ZipCode string `json:"zip"`
}

// Plan IDs
const (
	TRANSFER_FEE_CA_PLAN            = 1
	UNLIMITED_CA_PLAN               = 2
	TRANSFER_FEE_US_PLAN            = 3
	UNLIMITED_US_PLAN               = 4
	FREE_CA_PLAN                    = 5
	FREE_US_PLAN                    = 6
	FLEX_CA_PLAN                    = 7
	FLEX_US_PLAN                    = 8
	FINANCIAL_ASSISTANCE_PLAN       = 9
	FACILITY_FUNDED_PLAN            = 10
	PREPAID_PLAN                    = 11
	SIX_DOLLAR_TRANSFER_FEE_CA_PLAN = 12
	SIX_DOLLAR_TRANSFER_FEE_US_PLAN = 13
	BASIC_PLAN                      = 16
	FREE_FLEX                       = 17
	FREE_UNLIMITED                  = 18
	CORE_CA_PLAN                    = 19
	CORE_US_PLAN                    = 20
)

type ErrorResponse struct {
	Version string `json:"ver"`
	Message string `json:"msg"`
}

type OrderActionReasonType string

const (
	Unsubscribe OrderActionReasonType = "UNSUBSCRIBE"
)

type OrderActionReason struct {
	ReasonType OrderActionReasonType `json:"reason_type"`
	Reason     string                `json:"reason"`
	OtherText  string                `json:"other_text,omitempty"`
}

func (r *OrderActionReason) Valid() bool {
	return r.ReasonType != ""
}

type Patient struct {
	PatientId      string `json:"patient_id,omitempty"`
	AccountId      string `json:"account_id,omitempty"`
	FirstName      string `json:"first_name,omitempty"`
	LastName       string `json:"last_name,omitempty"`
	AltLastName    string `json:"alt_last_name,omitempty"`
	DOB            string `json:"dob,omitempty"`
	Phone          string `json:"phone,omitempty"`
	Email          string `json:"email,omitempty"`
	Sex            string `json:"sex,omitempty"`
	Country        string `json:"country,omitempty"`
	Subdivision    string `json:"subdivision,omitempty"`
	PostalCode     string `json:"postal_code,omitempty"`
	IsAccountOwner *bool  `json:"is_account_owner,omitempty"`
	Source         string `json:"source,omitempty"`
}

type PatientProfileValidation struct {
	Valid     bool `json:"valid"`
	FirstName bool `json:"first_name"`
	LastName  bool `json:"last_name"`
	Dob       bool `json:"dob"`
	Sex       bool `json:"sex"`
	Location  bool `json:"location"`
}

func GetCamelCaseGoogleSSOLoginResponse(response AccountSSOLoginResponse) GoogleSSOLoginResponse {
	return GoogleSSOLoginResponse{
		Token:                 response.Token,
		AccExists:             response.AccExists,
		RefreshToken:          response.RefreshToken,
		UserExists:            response.UserExists,
		IsNewlyCreatedAccount: response.IsNewlyCreatedAccount,
		PatientProfile:        GetCamelCasePatientStruct(response.PatientProfile),
	}
}

func GetCamelCasePatientStruct(patient Patient) models.PhiPatient {
	isAccountOwner := false
	if patient.IsAccountOwner != nil {
		isAccountOwner = *patient.IsAccountOwner
	}
	return models.PhiPatient{
		PatientId:      patient.PatientId,
		AccountId:      patient.AccountId,
		FirstName:      patient.FirstName,
		LastName:       patient.LastName,
		AltLastName:    patient.AltLastName,
		DOB:            patient.DOB,
		Phone:          patient.Phone,
		Email:          patient.Email,
		IsAccountOwner: isAccountOwner,
	}
}

func GetAcctOwner(pts []Patient) Patient {
	if len(pts) == 0 {
		return Patient{}
	}
	for _, p := range pts {
		if *p.IsAccountOwner {
			return p
		}
	}
	return pts[0]
}

// String enum for handler types
type AccountTypeEndpoint string

// Account types
const (
	PatientAccountEndpoint   AccountTypeEndpoint = "accounts"
	PhysicianAccountEndpoint AccountTypeEndpoint = "physician_accounts"
)

type CreateSSOTokenData struct {
	AccountId string    `json:"accountId"`
	ExpiresAt time.Time `json:"expiresAt"`
	Status    SSOStatus `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
}

type SSOStatus string

const (
	Generated SSOStatus = "GENERATED"
)

type VerificationCode struct {
	Token string `json:"token"`
	Code  string `json:"code"`
}

type VerificationCodeResponse struct {
	Code      string `json:"code"`
	AccountId string `json:"accountId"`
}

type EmailVerification struct {
	AccountId  string `json:"accountId"`
	IsVerified bool   `json:"isVerified"`
}

type DateOfBirthVerification struct {
	AccountId   string `json:"accountId"`
	DateOfBirth string `json:"dateOfBirth"`
}

func (vc *VerificationCode) IsValid() bool {
	if vc.Token == "" || vc.Code == "" {
		return false
	}
	return true
}

// PatchOrderRequest codifies the data model to patch requests. all values are ptrs
// since they can all them be omitted from the payload if they're not set
type PatchOrderRequest struct {
	ExpiresAt      *time.Time `json:"expires_at,omitempty"`
	IsRefunded     *bool      `json:"is_refunded,omitempty"`
	IsActive       *bool      `json:"is_active,omitempty"`
	IsAutoRenewing *bool      `json:"is_auto_renewing,omitempty"`
}
