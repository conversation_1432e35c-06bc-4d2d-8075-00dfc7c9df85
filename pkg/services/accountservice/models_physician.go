package accountservice

import "time"

type PhysicianRegisterData struct {
	Email      string `json:"email"`
	Password   string `json:"password"`
	FirstName  string `json:"firstName"`
	LastName   string `json:"lastName"`
	MainRegion uint16 `json:"mainRegion"`
}

type PhysicianAccount struct {
	AccountId    string `json:"id"`
	Email        string `json:"email"`
	PasswordHash string `json:"-"`
	MainRegion   uint16 `json:"mainRegion"`
	Region       string `json:"region,omitempty"` // human readable form of the main region
	// for historical reasons, this is a map, but only the keys are relevant. if an account has the region, the region id will be present as a key. If not, the region id will not be present.
	Regions map[uint16]uint64 `json:"additionalRegions"`

	IsLocked      bool   `json:"isLocked"`              // true if physician account was locked by user
	IsLockedOut   bool   `json:"isLockedOut,omitempty"` // true if there are too many invalid login attempts in lockout_tracker
	LoginAttempts uint64 `json:"loginAttempts,omitempty"`
	IsVerified    bool   `json:"isVerified"`

	CreatedAt string `json:"createdAt,omitempty"` // ISO8601 format
	UpdatedAt string `json:"updatedAt,omitempty"` // ISO8601 format

	Physicians     []Physician                            `json:"physicians,omitempty"`
	PermissionsMap map[int64][]PhysicianAccountPermission `json:"permissionsMap"` // map of physician permissions with provider id as key
}

type Physician struct {
	PhysicianId               string             `json:"id"`
	AccountId                 string             `json:"accountId"`
	FirstName                 string             `json:"firstName,omitempty"`
	LastName                  string             `json:"lastName,omitempty"`
	Email                     string             `json:"email,omitempty"`
	IsEmailVerified           bool               `json:"isEmailVerified,omitempty"`
	Phone                     string             `json:"phone,omitempty"`
	Fax                       string             `json:"fax,omitempty"`
	IsFaxVerified             bool               `json:"isFaxVerified,omitempty"`
	DefaultNotificationMethod NotificationMethod `json:"defaultNotificationMethod,omitempty"`
	Address                   string             `json:"address,omitempty"`
	OtherInfo                 string             `json:"otherInfo,omitempty"`

	IsDeleted bool `json:"isDeleted,omitempty"`

	CreatedAt time.Time `json:"createdAt,omitempty"` // ISO8601 format
	UpdatedAt time.Time `json:"updatedAt,omitempty"` // ISO8601 format

	PhysicianLicenses []PhysicianLicense `json:"physicianLicenses,omitempty"`
}

type PhysicianLicense struct {
	LicenseId       string `json:"id"`
	PhysicianId     string `json:"physicianId"`
	LicenseType     string `json:"licenseType,omitempty"`
	LicenseNumber   string `json:"licenseNo,omitempty"`
	StateOrProvince string `json:"stateOrProvince,omitempty"`

	ExpiryDate time.Time `json:"expiryDate,omitempty"` // ISO8601 format
	CreatedAt  time.Time `json:"createdAt,omitempty"`  // ISO8601 format
	UpdatedAt  time.Time `json:"updatedAt,omitempty"`  // ISO8601 format

	IsDeleted bool `json:"isDeleted,omitempty"`
}

type PhysicianLicenceRequest struct {
	LicenceType     string    `json:"licenseType,omitempty"`
	LicenseNo       string    `json:"licenseNo,omitempty"`
	StateOrProvince string    `json:"stateOrProvince,omitempty"`
	ExpiryDate      time.Time `json:"expiryDate,omitempty"`
}

type NotificationMethod string

const (
	NOTIFICATION_METHOD_FAX   NotificationMethod = "Fax"
	NOTIFICATION_METHOD_EMAIL NotificationMethod = "Email"
)

type PhysicianRequest struct {
	FirstName                 string             `json:"firstName,omitempty"`
	LastName                  string             `json:"lastName,omitempty"`
	Phone                     string             `json:"phone,omitempty"`
	Email                     string             `json:"email,omitempty"`
	Fax                       string             `json:"fax,omitempty"`
	Address                   string             `json:"address,omitempty"`
	DefaultNotificationMethod NotificationMethod `json:"defaultNotificationMethod,omitempty"`
	OtherInfo                 string             `json:"otherInfo,omitempty"`
}

type PhysicianVerificationData struct {
	NeedsFaxVerification bool   `json:"needsFaxVerification,omitempty"`
	FullName             string `json:"fullName,omitempty"`
	AccountName          string `json:"accountName,omitempty"`
	ContactData          string `json:"contactData,omitempty"`
	VerificationCode     string `json:"verificationCode,omitempty"`
}

func (u *PhysicianVerificationData) IsValid() bool {
	// full name, account name and contact data need to be present for fax verification
	if u.NeedsFaxVerification &&
		(u.FullName == "" || u.AccountName == "" || u.ContactData == "" || u.VerificationCode == "") {
		return false
	}
	return true
}

type PhysicianNotificationRequest struct {
	NotificationMethod NotificationMethod `json:"notificationMethod,omitempty"` //"email" or "fax"
	Value              string             `json:"value,omitempty"`              // either email address or fax number of physician
}

// Physician permission names
type PhysicianPermissionType string

const (
	PermissionBreakTheGlass = "break the glass" // enables physicians to search provider's pacs and request records
)

// physician account permission
type PhysicianAccountPermission struct {
	PermissionId          int64  `json:"permissionId"`
	PermissionName        string `json:"permissionName"`
	PermissionDescription string `json:"permissionDescription,omitempty"`
	GroupId               int64  `json:"groupId,omitempty"`
	GroupName             string `json:"groupName,omitempty"`
}
