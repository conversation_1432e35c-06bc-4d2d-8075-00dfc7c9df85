//go:build integration
// +build integration

package accountservice

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

func TestUpdateAccountSettings(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully update account settings", func(t *testing.T) {
		acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatalf("Failed to create test account in acct svc")
		}

		repNotif := true
		showOnboarding := false
		settingsToPut := UserSettingsRequest{
			ReportNotification: &repNotif,
			Task:               0,
			Language:           "en",
			ShowOnboarding:     &showOnboarding,
		}

		err = service.UpdateAccountSettings(ctx, acctId, settingsToPut)
		if err != nil {
			t.Errorf("Expected no error, but got %q", err)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)
		repNotif := true
		showOnboarding := false
		settingsToPut := UserSettingsRequest{
			ReportNotification: &repNotif,
			Task:               0,
			Language:           "en",
			ShowOnboarding:     &showOnboarding,
		}

		err := brokenService.UpdateAccountSettings(ctx, "acctId", settingsToPut)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Errorf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("account does not exist", func(t *testing.T) {
		repNotif := true
		showOnboarding := false
		settingsToPut := UserSettingsRequest{
			ReportNotification: &repNotif,
			Task:               0,
			Language:           "en",
			ShowOnboarding:     &showOnboarding,
		}

		err := service.UpdateAccountSettings(ctx, "fake_acct_id", settingsToPut)
		if err == nil {
			t.Error("Expected error to be not nil")
		}

		if _, ok := err.(ErrAccountNotFound); !ok {
			t.Errorf("Expected error to be ErrAccountNotFound, got %q", err)
		}
	})
}

func TestGetAccountSettings(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	// Create account and add settigns for it
	acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
		t,
		ctx,
		&http.Client{},
		1,
		qaAcctSvcUrl,
		acctServiceUser,
		acctSvcApiKey,
	)
	if err != nil {
		t.Fatalf("Failed to create test account in acctsvc")
	}

	repNotif := true
	settingsToPut := UserSettingsRequest{
		ReportNotification: &repNotif,
		Task:               7,
		Language:           "en",
	}

	err = service.UpdateAccountSettings(ctx, acctId, settingsToPut)
	if err != nil {
		t.Errorf("Expected no error, but got %q", err)
	}

	t.Run("Successfully retrieve account settings", func(t *testing.T) {

		settings, err := service.GetAccountSettings(ctx, acctId)
		if err != nil {
			t.Fatalf("Expected no error, but got %q", err)
		}

		if settings.Language != settingsToPut.Language {
			t.Errorf(
				"Received language setting is different from expected. Want: %s, got: %s",
				settingsToPut.Language,
				settings.Language,
			)
		}

		if settings.ReportNotification != *settingsToPut.ReportNotification {
			t.Errorf(
				"Received report notification setting is different from expected. Want: %v, got: %v",
				*settingsToPut.ReportNotification,
				settings.ReportNotification,
			)
		}

		if settings.OnboardingTasks == nil {
			t.Fatalf("Expected OnboardingTasks to be not nil")
		}

		if !settings.OnboardingTasks.CompleteVideo || !settings.OnboardingTasks.CompleteRequest ||
			!settings.OnboardingTasks.CompleteUpload {
			t.Errorf(
				"One or more the first 3 onboarding tasks status is incorrect. Expected all but Connect to be true, got: %v",
				settings.OnboardingTasks,
			)
		}

		if settings.OnboardingTasks.CompleteConnect {
			t.Errorf("CompleteConnect onboarding task should be false")
		}

		if !settings.ShowOnboarding {
			t.Errorf(
				"Expected ShowOnboarding to be true because 1 onboarding task is not completed yet",
			)
		}
	})

	t.Run(
		"Update account settings with all tasks completed and language set to fr and validate it got updated",
		func(t *testing.T) {
			settingsToPut.Language = "fr"
			settingsToPut.Task = 15

			err = service.UpdateAccountSettings(ctx, acctId, settingsToPut)
			if err != nil {
				t.Errorf("Expected no error, but got %q", err)
			}

			settings, err := service.GetAccountSettings(ctx, acctId)
			if err != nil {
				t.Fatalf("Expected no error, but got %q", err)
			}

			if settings.Language != settingsToPut.Language {
				t.Errorf(
					"Received language setting is different from expected. Want: %s, got: %s",
					settingsToPut.Language,
					settings.Language,
				)
			}

			if settings.ReportNotification != *settingsToPut.ReportNotification {
				t.Errorf(
					"Received report notification setting is different from expected. Want: %v, got: %v",
					*settingsToPut.ReportNotification,
					settings.ReportNotification,
				)
			}

			if settings.OnboardingTasks != nil {
				t.Errorf("Expected OnboardingTasks to be nil, got %v", settings.OnboardingTasks)
			}

			if settings.ShowOnboarding {
				t.Errorf(
					"Expected ShowOnboarding to be false because all onboarding task are completed",
				)
			}
		},
	)

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		_, err := brokenService.GetAccountSettings(ctx, "acctId")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Errorf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("Account does not exist", func(t *testing.T) {
		settings, err := service.GetAccountSettings(ctx, "fake_acct_id")
		if err == nil {
			t.Error("Expected error to be not nil")
		}

		if _, ok := err.(ErrAccountNotFound); !ok {
			t.Errorf("Expected error to be ErrAccountNotFound, got %q", err)
		}

		if settings != nil {
			t.Errorf("Expected settings to be nil, got: %v", settings)
		}
	})
}

func TestAddNewRegion(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("AddNewRegion returns without error if no conflict", func(t *testing.T) {
		// create account with main region ID=2
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		// add new region with regionID 1
		err = service.AddNewRegion(ctx, regions.GetRegionID(), testAcctID)
		if err != nil {
			t.Fatal("error adding new region:", err)
		}
	})

	t.Run("AddNewRegion returns error if conflict", func(t *testing.T) {
		// create account with main region ID=1
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		// add new region with regionID 1
		err = service.AddNewRegion(ctx, regions.GetRegionID(), testAcctID)
		if err != nil {
			t.Fatal("error adding new region:", err)
		}

		// try adding same region again
		err = service.AddNewRegion(ctx, regions.GetRegionID(), testAcctID)
		var conflictErr ErrNewRegionConflict
		if err == nil || !errors.As(err, &conflictErr) {
			t.Fatal("expected conflict error, got:", err)
		}
	})
}

func TestPatchAccount(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("patch new password returns no error", func(t *testing.T) {
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		err = service.SetPasswordAndVerify(ctx, testAcctID, "password")
		if err != nil {
			t.Fatal("error setting password:", err)
		}
	})

	t.Run("patch non existing account", func(t *testing.T) {
		acctId := "not-an-account"
		err := service.SetPasswordAndVerify(ctx, acctId, "password")

		assert.ErrorIs(t, err, ErrAccountNotFound{AccountID: acctId})
	})
}

func TestVerifyEmail(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Return 404 bad token error", func(t *testing.T) {
		var v = Verification{
			Token:    "test-token",
			Password: "test-password",
		}
		_, err := service.VerifyEmail(ctx, PatientAccountEndpoint, v)
		if err == nil {
			t.Errorf("Expected err, but got None")
		}
		if err.Error() != "got 404 back from acct svc" {
			t.Fatalf("Expected 404 error but got: %s", err.Error())
		}
	})

	t.Run("Return 400 missing token error", func(t *testing.T) {
		var v = Verification{
			Password: "test-password",
		}
		_, err := service.VerifyEmail(ctx, PatientAccountEndpoint, v)
		if err == nil {
			t.Errorf("Expected err, but got None")
		}
		if !strings.Contains(err.Error(), "accountservice did not accept verify email request") {
			t.Errorf("Expected 400 missing token err but got: %s", err.Error())
		}
	})
}

func TestUpdateEmail(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Return 500 bad token error", func(t *testing.T) {
		eu := UsersEmailUpdate{
			Token: "test-token",
		}
		err := service.UpdateEmail(ctx, eu)
		if err == nil {
			t.Errorf("Expected err, but got None")
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		eu := UsersEmailUpdate{
			Token: "test-token",
		}

		err := brokenService.UpdateEmail(ctx, eu)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestLockAccount(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("LockAccount success", func(t *testing.T) {
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		// update account email with testNewEmail
		err = service.LockAccount(ctx, testAcctID)
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		err := brokenService.LockAccount(ctx, "123123")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestLookupByEmail(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("existing email", func(t *testing.T) {

		// update account email with testNewEmail
		acctId, err := service.LookupAccountIdByEmail(ctx, "<EMAIL>")
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}

		expectedAcctId := "23ZIHzTbIX9TNHdGaR4ZYU9P15V"
		if acctId != expectedAcctId {
			t.Fatalf("expected acct %s, got %s", expectedAcctId, acctId)
		}
	})
	t.Run("non-existent email", func(t *testing.T) {

		// update account email with testNewEmail
		acctId, err := service.LookupAccountIdByEmail(
			ctx,
			"<EMAIL>",
		)
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}

		if acctId != "" {
			t.Fatalf("expected acct id to be blank, got %s", acctId)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		_, err := brokenService.LookupAccountIdByEmail(ctx, "123123")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestLookupByPatientId(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("existing email", func(t *testing.T) {

		// update account email with testNewEmail
		acctId, err := service.LookupAccountIdByPatientId(ctx, "2NvWzQXD4aw1KbL58RkwXMFlgKn")
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}

		expectedAcctId := "23ZIHzTbIX9TNHdGaR4ZYU9P15V"
		if acctId != expectedAcctId {
			t.Fatalf("expected acct %s, got %s", expectedAcctId, acctId)
		}
	})
	t.Run("non-existent patient id", func(t *testing.T) {

		// update account email with testNewEmail
		acctId, err := service.LookupAccountIdByPatientId(
			ctx,
			"thispatientiddoesnotexist",
		)
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}

		if acctId != "" {
			t.Fatalf("expected acct id to be blank, got %s", acctId)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		_, err := brokenService.LookupAccountIdByPatientId(ctx, "123123")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestLoginAndRefresh(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("valid login and refresh", func(t *testing.T) {
		acctId, email, password, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatalf("error creating test account: %v", err)
		}

		//set acct to verified so we can log in
		service.SetPasswordAndVerify(ctx, acctId, password)
		ip := "********"
		session, err := service.AuthenticateAcctAndGetToken(
			ctx,
			PatientAccountEndpoint,
			email,
			password,
			ip,
		)
		if err != nil {
			t.Fatalf("unable to log in: %v", err)
		}
		if session.Token == "" || session.RefreshToken == "" {
			t.Fatalf("expected both a token and refresh token from login")
		}

		_, err = service.RefreshToken(ctx, session.RefreshToken, ip)
		if err != nil {
			t.Fatalf("expected to be able to refresh account session: %v", err)
		}

	})

}

func TestResetPasswordInit(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("reset pssword init success", func(t *testing.T) {
		// reset password init with a test email
		testEmail := "<EMAIL>"
		err := service.ResetPasswordInit(ctx, PatientAccountEndpoint, testEmail)
		if err != nil {
			t.Fatal("expect no error but got:", err)
		}
	})

	t.Run("reset pssword failed because email does not exists", func(t *testing.T) {
		testNewEmail := "email_does_not_exist"
		// reset password init with testNewEmail
		err := service.ResetPasswordInit(ctx, PatientAccountEndpoint, testNewEmail)
		var notExistsErr ErrNoAccountForEmail
		if err == nil {
			t.Fatal("expect error but got none")
		}
		if !errors.As(err, &notExistsErr) {
			t.Fatal("expect email not exists error but got:", err)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		err := brokenService.ResetPasswordInit(ctx, PatientAccountEndpoint, "<EMAIL>")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestResetPassword(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("reset password code and token does not exists", func(t *testing.T) {
		editInfo := AccountEdit{Code: "1234", Token: "token_does_not_exists", Password: "test1234!"}
		// reset password init with testNewEmail
		err := service.ResetPassword(ctx, PatientAccountEndpoint, editInfo)
		var notfoundErr ErrResetPasswordNotFound
		if err == nil {
			t.Fatal("expect error but got none")
		}
		if !errors.As(err, &notfoundErr) {
			t.Fatal("expect not found error but got:", err)
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		editInfo := AccountEdit{}
		err := brokenService.ResetPassword(ctx, PatientAccountEndpoint, editInfo)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

// Test account service orders endpoints for Auth by sending a request with an invalid API key.
// This has the side effect of testing that an endpoint is actually hit (useful when refactoring which endpoints are hit).
func TestAccountServiceOrdersEndpointsAuth(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)

	invalidAuthService := NewHTTPAccountServiceClient(
		qaAcctSvcUrl,
		acctServiceUser,
		"incorrect_key",
		httpClient,
	)

	ctx := context.Background()

	t.Run("DeleteOrder", func(t *testing.T) {
		err := invalidAuthService.DeleteOrder(ctx, "acct_id", "order_id")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("CreateOrder", func(t *testing.T) {
		// NewOrder minimum validation is PlanId and RegionId are not 0.
		_, err := invalidAuthService.createOrder(ctx, "acct_id",
			NewOrder{
				PlanId:   1,
				RegionId: 1,
			},
			false,
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("RefundOrder", func(t *testing.T) {
		err := invalidAuthService.RefundOrder(ctx, "acct_id", "order_id")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("ToggleAutoRenew", func(t *testing.T) {
		err := invalidAuthService.ToggleAutoRenew(ctx, "acct_id", "order_id", true)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("UpdateOrderPaymentDetails", func(t *testing.T) {
		err := invalidAuthService.UpdateOrderPaymentDetails(
			ctx,
			"acct_id",
			"order_id",
			OrderPaymentDetailsRequest{},
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("CreateOrderActionReason", func(t *testing.T) {
		err := invalidAuthService.CreateOrderActionReason(
			ctx,
			"acct_id",
			"order_id",
			OrderActionReason{},
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("GetOrders", func(t *testing.T) {
		_, err := invalidAuthService.GetOrders(
			ctx,
			"acct_id",
			map[string]bool{},
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("GetOrderById", func(t *testing.T) {
		_, err := invalidAuthService.GetOrderById(
			ctx,
			"acct_id",
			"order_id",
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})

	t.Run("PatchOrder", func(t *testing.T) {
		err := invalidAuthService.PatchOrder(
			ctx,
			"acct_id",
			"order_id",
			PatchOrderRequest{},
		)
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}
func TestToggleAutoRenew(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("toggle autorenew success", func(t *testing.T) {
		err := service.ToggleAutoRenew(
			ctx,
			"2DUqFHgUDmntvW3VQqrFDV9MGQH",
			"2E2TYxNZHpd0ZbmBqy06Wawdavq",
			true,
		)
		if err != nil {
			t.Fatalf("unable to toggle autorenew: %v", err)
		}
	})
}

func TestUpdateOrderPaymentDetails(t *testing.T) {
	regions.SetRegionID(1)

	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)

	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)
	ctx := context.Background()

	t.Run("Update order payment details for order that does not exist", func(t *testing.T) {
		err := service.UpdateOrderPaymentDetails(
			ctx,
			"123",
			"345",
			OrderPaymentDetailsRequest{PaymentToken: "something", Country: "CA", ZipCode: "A1A1A1"},
		)
		if err == nil {
			t.Fatalf("expected error but got none")
		}
	})
}

func TestAccountLoginSSO(t *testing.T) {
	regions.SetRegionID(1)

	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)

	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)
	ctx := context.Background()

	t.Run("account login SSO for non-existant token and ip address", func(t *testing.T) {
		_, err := service.AccountLoginSSO(ctx, "some token", "123")
		if err == nil {
			t.Fatal("expect error but got none")
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		_, err := brokenService.AccountLoginSSO(ctx, "token", "ip")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}

	})
}

func TestAccountLoginGoogle(t *testing.T) {
	regions.SetRegionID(1)

	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)

	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)
	ctx := context.Background()

	t.Run("account login google for non-existent token and ip address", func(t *testing.T) {
		_, err := service.AccountLoginGoogle(ctx, "some token", "123")
		if err == nil {
			t.Fatal("expect error but got none")
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		_, err := brokenService.AccountLoginSSO(ctx, "token", "ip")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}

	})
}

func TestLockAccountWithToken(t *testing.T) {
	regions.SetRegionID(1)

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("LockAccount failed - token not found", func(t *testing.T) {
		err := service.LockAccountWithToken(ctx, "token not exists")
		if err == nil {
			t.Fatal("expect error but got none")
		}
	})

	t.Run("Authorization issue", func(t *testing.T) {
		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)

		err := brokenService.LockAccountWithToken(ctx, "123123")
		if err == nil || !strings.Contains(err.Error(), "401") {
			t.Fatalf("Expected authorization error, but got %q", err)
		}
	})
}

func TestUpdatePatient(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully Update Patient", func(t *testing.T) {
		acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)

		if err != nil {
			t.Fatalf("Failed to create test account in acct svc")
		}

		patientToAdd := Patient{
			FirstName: "Test",
			LastName:  "Patient",
			DOB:       "1980-01-01",
			Email:     "<EMAIL>",
			Phone:     "**********",
		}

		ptId, err := service.GetOrCreatePatient(ctx, acctId, patientToAdd, true)
		require.NoError(t, err)

		patientToUpdate := Patient{
			FirstName:   "Test-Updated",
			LastName:    "Patient",
			DOB:         "1980-01-01",
			Email:       "<EMAIL>",
			Phone:       "**********",
			Sex:         "O",
			Country:     "CA",
			Subdivision: "ON",
			PostalCode:  "M5S2E4",
		}

		err = service.UpdatePatient(ctx, acctId, ptId, patientToUpdate)
		require.NoError(t, err)

		patient, err := service.GetPatient(ctx, acctId, ptId)
		require.NoError(t, err)

		assert.Equal(t, patientToUpdate.FirstName, patient.FirstName)
		assert.Equal(t, patientToUpdate.Sex, patient.Sex)
		assert.Equal(t, patientToUpdate.Country, patient.Country)
		assert.Equal(t, patientToUpdate.Subdivision, patient.Subdivision)
		assert.Equal(t, patientToUpdate.PostalCode, patient.PostalCode)

	})

	t.Run("Patient update fails due to invalid patient Id", func(t *testing.T) {

		patientToUpdate := Patient{
			FirstName: "Test-update",
			LastName:  "Patient",
			DOB:       "1980-01-01",
			Email:     "<EMAIL>",
			Phone:     "**********",
		}

		err := service.UpdatePatient(ctx, "acctId_not_valid", "ptId_invalid", patientToUpdate)
		require.Error(t, err)
		assert.Equal(t, http.StatusText(http.StatusNotFound), err.Error())
	})
}

func TestGetPatientsAndPatient(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully GET single Patient", func(t *testing.T) {
		acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)

		if err != nil {
			t.Fatalf("Failed to create test account in acct svc")
		}

		patientToAdd := Patient{
			FirstName:   "Test",
			LastName:    "Patient",
			DOB:         "1980-01-01",
			Email:       "<EMAIL>",
			Phone:       "**********",
			Sex:         "F",
			Country:     "CA",
			Subdivision: "ON",
			PostalCode:  "M5S2E4",
		}

		ptId, err := service.GetOrCreatePatient(ctx, acctId, patientToAdd, true)
		if err != nil {
			t.Errorf("Expected no error, but got %q", err)
		}

		patient, err := service.GetPatient(ctx, acctId, ptId)
		if err != nil {
			t.Errorf("Expected no error, but got %q", err)
		}

		// don't test email since email is default to account email
		assert.Equal(t, patientToAdd.FirstName, patient.FirstName)
		assert.Equal(t, patientToAdd.LastName, patient.LastName)
		assert.Equal(t, patientToAdd.DOB, patient.DOB)
		assert.Equal(t, patientToAdd.Phone, patient.Phone)
		assert.Equal(t, patientToAdd.Sex, patient.Sex)
		assert.Equal(t, patientToAdd.Country, patient.Country)
		assert.Equal(t, patientToAdd.Subdivision, patient.Subdivision)
		assert.Equal(t, patientToAdd.PostalCode, patient.PostalCode)
	})

	t.Run("Successfully GET Patients", func(t *testing.T) {
		acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)

		if err != nil {
			t.Fatalf("Failed to create test account in acct svc")
		}

		patientsToAdd := []Patient{
			{
				FirstName: "Test One",
				LastName:  "Patient",
				DOB:       "1980-01-01",
				Email:     "<EMAIL>",
				Phone:     "**********",
			},
			{
				FirstName: "Test Two",
				LastName:  "Patient",
				DOB:       "1980-01-01",
				Email:     "<EMAIL>",
				Phone:     "**********",
			},
		}

		// add patients to account
		for _, patient := range patientsToAdd {
			_, err := service.GetOrCreatePatient(ctx, acctId, patient, true)
			require.NoError(t, err)
		}

		patients, err := service.GetPatients(ctx, acctId)
		require.NoError(t, err)
		assert.Len(t, patients, 2)
	})
}

func TestDeletePatient(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully Delete Patient", func(t *testing.T) {
		acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)

		if err != nil {
			t.Fatalf("Failed to create test account in acct svc")
		}

		patientsToAdd := []Patient{
			{
				FirstName: "Test One",
				LastName:  "Patient",
				DOB:       "1980-01-01",
				Email:     "<EMAIL>",
				Phone:     "**********",
			},
			{
				FirstName: "Test Two",
				LastName:  "Patient",
				DOB:       "1980-01-01",
				Email:     "<EMAIL>",
				Phone:     "**********",
			},
		}

		ptIds := make([]string, len(patientsToAdd))
		// add patients to account
		for i, patient := range patientsToAdd {
			ptid, err := service.GetOrCreatePatient(ctx, acctId, patient, true)
			ptIds[i] = ptid
			if err != nil {
				t.Errorf("Expected no error, but got %q", err)
			}
		}

		// the first patient created in an account becomes the account owner patient
		// account owner patient could not be deleted, so delete the non account owner
		err = service.DeletePatient(ctx, acctId, ptIds[1])
		require.NoError(t, err)
	})
}

func TestGetAllPhysicianPermissions(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully Get All Physician Permissions", func(t *testing.T) {
		// we do not have any endpoints to set permissions at the moment
		// this just tests if account service endpoint can be called successfully
		_, err := service.GetAllPhysicianPermissions(ctx, "accountId")
		if err != nil {
			t.Errorf("Expected no error, but got %v", err)
		}
	})
}

func TestGetPhysicianPermissionsByProviderIdAndPermissionName(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	t.Run("Successfully Get Physician Permissions", func(t *testing.T) {
		// we do not have any endpoints to set permissions at the moment
		// this just tests if account service endpoint can be called successfully
		_, err := service.GetPhysicianPermissionsByProviderIdAndPermissionName(
			ctx,
			"accountId",
			42,
			"permission name",
		)
		if err != nil {
			t.Errorf("Expected no error, but got %v", err)
		}
	})
}

func TestAccountHasAccessToPatient(t *testing.T) {
	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	t.Run("when the account has access to a patient", func(t *testing.T) {
		ok := service.AccountHasAccessToPatient(
			context.TODO(),
			"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
			"2cKYg6SWoRPRdRWpcv9kx79YCj7",
		)
		assert.True(t, ok)
	})

	t.Run("when the account does not have access to a patient", func(t *testing.T) {
		ok := service.AccountHasAccessToPatient(
			context.TODO(),
			"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
			ksuid.New().String(),
		)
		assert.False(t, ok)
	})

	t.Run("when the account and patient don't exist", func(t *testing.T) {
		ok := service.AccountHasAccessToPatient(
			context.TODO(),
			ksuid.New().String(),
			ksuid.New().String(),
		)
		assert.False(t, ok)
	})
}

func TestGetAllPatientProfileValidationsForAccount(t *testing.T) {
	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	t.Run("when the account exists", func(t *testing.T) {
		t.Run("", func(t *testing.T) {
			validations, err := service.GetAllPatientProfileValidationsForAccount(
				context.TODO(),
				"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
			)
			assert.NoError(t, err)
			assert.Equal(t, map[string]PatientProfileValidation{
				"2cKYg6SWoRPRdRWpcv9kx79YCj7": {
					Valid:     false,
					FirstName: true,
					LastName:  true,
					Dob:       true,
					Sex:       false,
				},
			}, validations)
		})
	})

	t.Run("when the account does not exists", func(t *testing.T) {
		_, err := service.GetAllPatientProfileValidationsForAccount(
			context.TODO(),
			ksuid.New().String(),
		)
		assert.EqualError(t, err, errormsgs.ERR_NOT_FOUND)
	})
}

func TestGetAccountPatientProfileValidation(t *testing.T) {
	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	t.Run("when the patient exists on the account", func(t *testing.T) {
		t.Run("fetches the profile validations", func(t *testing.T) {
			validations, err := service.GetAccountPatientProfileValidation(
				context.TODO(),
				"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
				"2cKYg6SWoRPRdRWpcv9kx79YCj7",
			)
			assert.NoError(t, err)
			assert.IsType(t, PatientProfileValidation{}, validations)
			assert.Equal(t, PatientProfileValidation{
				Valid:     false,
				FirstName: true,
				LastName:  true,
				Dob:       true,
				Sex:       false,
			}, validations)
		})
	})

	t.Run("when the patient does not exist on the account", func(t *testing.T) {
		_, err := service.GetAccountPatientProfileValidation(
			context.TODO(),
			"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
			ksuid.New().String(),
		)
		assert.EqualError(t, err, errormsgs.ERR_NOT_FOUND)
	})

	t.Run("when the account does not exist", func(t *testing.T) {
		_, err := service.GetAccountPatientProfileValidation(
			context.TODO(),
			ksuid.New().String(),
			"2cKYg6SWoRPRdRWpcv9kx79YCj7",
		)
		assert.EqualError(t, err, errormsgs.ERR_NOT_FOUND)
	})

	t.Run("when the account and patient do not exist", func(t *testing.T) {
		_, err := service.GetAccountPatientProfileValidation(
			context.TODO(),
			ksuid.New().String(),
			ksuid.New().String(),
		)
		assert.EqualError(t, err, errormsgs.ERR_NOT_FOUND)
	})
}

func TestDeactivateAccount(t *testing.T) {

	regions.SetRegionID(1)
	qaAcctSvcUrl, acctServiceUser, acctSvcApiKey := getTestHttpClientParams(t)
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	db := testutils.SetupTestDB(t)
	auth.InitializeBlacklist(db)

	t.Run("DeactivateAccount success", func(t *testing.T) {
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		// Deactivate account!
		err = service.DeactivateAccount(
			ctx,
			testAcctID,
			testutils.GetAccountAuthToken(testAcctID),
		)
		assert.NoError(t, err)
	})

	t.Run("Account Does Not Exist", func(t *testing.T) {
		acctId := "ACCOUNTTHATDOESNOTEXIST"
		err := service.DeactivateAccount(
			ctx,
			acctId,
			testutils.GetAccountAuthToken(acctId),
		)
		assert.Equal(t, errormsgs.ERR_NOT_FOUND, err.Error())
	})

	t.Run("Auth Issue", func(t *testing.T) {
		testAcctID, _, _, err := testutils.CreateAccountServiceTestAccount(
			t,
			ctx,
			&http.Client{},
			1,
			qaAcctSvcUrl,
			acctServiceUser,
			acctSvcApiKey,
		)
		if err != nil {
			t.Fatal("error creating test account:", err)
		}

		brokenService := NewHTTPAccountServiceClient(
			qaAcctSvcUrl,
			acctServiceUser,
			"incorrect_key",
			httpClient,
		)
		err = brokenService.DeactivateAccount(
			ctx,
			testAcctID,
			testutils.GetAccountAuthToken(testAcctID),
		)
		assert.Equal(t, errormsgs.ERR_NOT_AUTHORIZED, err.Error())
	})
}

func TestGetOrCreatePatient(t *testing.T) {
	regions.SetRegionID(1)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	service := NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)

	ctx := context.Background()

	acctId, _, _, err := testutils.CreateAccountServiceTestAccount(
		t,
		ctx,
		&http.Client{},
		1,
		qaAcctSvcUrl,
		acctServiceUser,
		acctSvcApiKey,
	)
	require.NoError(t, err)

	t.Cleanup(
		func() {
			err = testutils.DeleteAccountSvcTestAccount(
				&http.Client{},
				acctId,
				qaAcctSvcUrl,
				acctServiceUser,
				acctSvcApiKey,
			)
			assert.NoError(t, err)
		},
	)

	patientToAdd := Patient{
		FirstName:   "Test",
		LastName:    "Patient",
		DOB:         "1980-01-01",
		Email:       "<EMAIL>",
		Phone:       "**********",
		Country:     "CA",
		Subdivision: "ON",
		PostalCode:  "M5S2E4",
	}
	firstPatientId := ""

	t.Run(
		"add patient successfully", func(t *testing.T) {
			firstPatientId, err = service.GetOrCreatePatient(ctx, acctId, patientToAdd, false)
			require.NoError(t, err)

			// successful add should return patientId
			require.NotEmpty(t, firstPatientId)

			// check if patient created correctly
			pt, err := service.GetPatient(ctx, acctId, firstPatientId)
			require.NoError(t, err)
			assert.Equal(t, patientToAdd.FirstName, pt.FirstName)
			assert.Equal(t, patientToAdd.LastName, pt.LastName)
			assert.Equal(t, patientToAdd.DOB, pt.DOB)
			assert.Equal(t, patientToAdd.Phone, pt.Phone)
			assert.Equal(t, patientToAdd.Country, pt.Country)
			assert.Equal(t, patientToAdd.Subdivision, pt.Subdivision)
			assert.Equal(t, patientToAdd.PostalCode, pt.PostalCode)
			assert.True(t, *pt.IsAccountOwner)
		},
	)

	t.Run(
		"try to add the same patient should result in a fetch", func(t *testing.T) {
			secondPtId, err := service.GetOrCreatePatient(ctx, acctId, patientToAdd, false)
			require.NoError(t, err)
			assert.EqualValues(t, firstPatientId, secondPtId)
		},
	)

	t.Run(
		"trying to add dup patient with createOnDup=true should create a new patient", func(t *testing.T) {
			thirdPtId, err := service.GetOrCreatePatient(ctx, acctId, patientToAdd, true)
			require.NoError(t, err)
			assert.NotEqualValues(t, firstPatientId, thirdPtId)

			// check if patient created correctly
			pt, err := service.GetPatient(ctx, acctId, thirdPtId)
			require.NoError(t, err)
			assert.ObjectsAreEqualValues(patientToAdd, pt)
		},
	)
}

func getTestHttpClientParams(t *testing.T) (string, string, string) {
	t.Helper()
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"

	return qaAcctSvcUrl, acctServiceUser, acctSvcApiKey
}
