package accountservice

import (
	"context"
	"crypto/sha1" // #nosec G505
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type AccountServiceClient struct {
	AccountServiceUrl    string `json:"url"`
	AccountServiceUser   string `json:"name"`
	AccountServiceAPIKey string
	HttpClient           *httpclient.Client
}

func NewHTTPAccountServiceClient(
	url, user, apiKey string,
	client *httpclient.Client,
) *AccountServiceClient {
	return &AccountServiceClient{
		AccountServiceUrl:    url,
		AccountServiceUser:   user,
		AccountServiceAPIKey: apiKey,
		HttpClient:           client,
	}
}

func (as *AccountServiceClient) setupBasicAuth() string {
	cred := fmt.Sprintf("%s:%s", as.AccountServiceUser, as.AccountServiceAPIKey)
	return base64.StdEncoding.EncodeToString([]byte(cred))
}

// GetOrderForId Get all order details for the specified order identifier
func (as *AccountServiceClient) GetOrderForId(
	ctx context.Context,
	orderId string,
) (Order, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("order_id", orderId)

	endpoint := fmt.Sprintf("%s/v1/orders/%s", as.AccountServiceUrl, orderId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	var result Order
	respBody, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying acctsvc")
		return result, err
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return result, err
	}

	return result, nil
}

func (as *AccountServiceClient) UpdateAccountSettings(
	ctx context.Context,
	acctId string,
	accountSettings UserSettingsRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acctId)
	endpoint := fmt.Sprintf("%s/v1/accounts/%s/settings", as.AccountServiceUrl, acctId)

	reqBody, err := json.Marshal(accountSettings)
	if err != nil {
		return err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PUT",
		TargetURL:     endpoint,
		ReqBody:       reqBody,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}

	if status == http.StatusNotFound {
		return ErrAccountNotFound{AccountID: acctId}
	}

	// ensure http.Client connection reuse
	_, err = io.Copy(ioutil.Discard, respBody)
	if err != nil {
		lg.WithError(err).Error("error reading response body")
	}
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	return err
}

func (as *AccountServiceClient) GetAccountSettings(
	ctx context.Context,
	acctId string,
) (*UserSettings, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acctId)

	endpoint := fmt.Sprintf("%s/v1/accounts/%s/settings", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}

	if status == http.StatusNotFound {
		return nil, ErrAccountNotFound{AccountID: acctId}
	}

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		return nil, err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var acctSettings UserSettings
	err = json.Unmarshal(gotBody, &acctSettings)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return nil, err
	}

	return &acctSettings, nil
}

// needed to generate random numbers for rollout logic
func init() {
	rand.Seed(time.Now().UTC().UnixNano())
}

func (as *AccountServiceClient) CreateAccountAndSendVerifyEmail(
	ctx context.Context,
	newAcct NewAccount,
) (string, error) {
	return as.createAccount(ctx, newAcct, true)
}

// Create a new account in as
func (as *AccountServiceClient) createAccount(
	ctx context.Context,
	newAcct NewAccount,
	sendEmail bool,
) (string, error) {
	if !newAcct.Valid() {
		return "", errors.New(http.StatusText(http.StatusBadRequest))
	}
	if newAcct.MainRegion == 0 {
		// default to current core's region
		newAcct.MainRegion = regions.GetRegionID()
	}
	emailHash := fmt.Sprintf("%x", sha1.Sum([]byte(newAcct.Email))) // #nosec G401

	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"main_reg":   newAcct.MainRegion,
		"email_hash": emailHash,
	})
	reqBodyJson, err := json.Marshal(newAcct)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return "", err
	}

	endpoint := fmt.Sprintf("%s/v1/accounts", as.AccountServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		QueryParams:   map[string]string{"email": fmt.Sprint(sendEmail)},
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusBadRequest, http.StatusNotAcceptable},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}

	switch status {
	case http.StatusConflict:
		return "", ErrNewAcctConflict{
			EmailHash: emailHash,
		}
	case http.StatusBadRequest:
		return "", ErrBadRequest{
			RegionID:  newAcct.MainRegion,
			EmailHash: emailHash,
		}
	case http.StatusOK:
		var acctId string
		body, err := io.ReadAll(resp)
		if err != nil {
			return acctId, err
		}

		err = json.Unmarshal(body, &acctId)
		if err != nil {
			return acctId, err
		}
		return acctId, nil
	case http.StatusNotAcceptable:
		// acctsvc returns the unfulfilled requirement in the response body,
		// so read and return it.
		body, err := io.ReadAll(resp)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return "", ErrBadNewPassword{}
		}

		var errResp ErrorResponse
		err = json.Unmarshal(body, &errResp)
		if err != nil {
			lg.WithError(err).Error("error parsing error response")
			return "", ErrBadNewPassword{}
		}

		return "", ErrBadNewPassword{MissingRequirement: errResp.Message}
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

// Create a new user in the given region for the given account
func (as *AccountServiceClient) AddNewRegion(
	ctx context.Context,
	regionId uint16,
	accountId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":   accountId,
		"region_id": regionId,
	})

	// Used to need to pass in a region-userid mapping, user is no longer used
	reqBodyJson, err := json.Marshal(0)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/regions/%d/users",
		as.AccountServiceUrl,
		accountId,
		regionId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusNotFound, http.StatusBadRequest},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending create regional user request to acct svc")
		return err
	}

	switch status {
	case http.StatusNotFound:
		return ErrAccountNotFound{AccountID: accountId}
	case http.StatusConflict:
		return ErrNewRegionConflict{AccountId: accountId, RegionID: regionId}
	case http.StatusBadRequest:
		return ErrBadRequest{RegionID: regionId}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

// Set new plaintext password on account and mark the account as verified
func (as *AccountServiceClient) SetPasswordAndVerify(
	ctx context.Context,
	acctId string,
	password string,
) error {
	return as.patchAccount(ctx, acctId, AccountUpdate{Password: password, Verified: true})
}

func (as *AccountServiceClient) LockAccount(ctx context.Context, acctId string) error {
	return as.patchAccount(ctx, acctId, AccountUpdate{Locked: true})
}

func (as *AccountServiceClient) LockAccountWithToken(ctx context.Context, lockToken string) error {
	lg := logutils.DebugCtxLogger(ctx)
	reqBodyJson, err := json.Marshal(lockToken)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/accounts/lock", as.AccountServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending create regional user request to acct svc")
		return err
	}

	switch status {
	case http.StatusBadRequest:
		return ErrBadRequest{}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) DeactivateAccount(
	ctx context.Context,
	accountId string,
	token string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": accountId,
	})

	endpoint := fmt.Sprintf("%s/v1/accounts/%s/deactivate", as.AccountServiceUrl, accountId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: http.MethodPatch,
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusUnauthorized,
			http.StatusInternalServerError,
		},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending deactivate account request to acct svc")
		return err
	}

	// If the account was successfully deactivated, blacklist their token
	if status == http.StatusOK {
		err = auth.AddToBlacklist(ctx, token)
	} else if status == http.StatusUnauthorized {
		err = errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if status == http.StatusNotFound {
		err = errors.New(errmsg.ERR_NOT_FOUND)
	} else {
		err = errors.New(errmsg.ERR_FAILED_DEACTIVATION)
	}

	return err
}

func (as *AccountServiceClient) SetAccountOwner(
	ctx context.Context,
	accountId string,
	request models.SetAccountOwnerRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": accountId,
	})

	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/accounts/%s/owner", as.AccountServiceUrl, accountId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: http.MethodPatch,
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusUnauthorized,
			http.StatusInternalServerError,
		},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending set account owner request to acct svc")
		return err
	}

	switch status {
	case http.StatusOK:
		return nil
	case http.StatusUnauthorized:
		err = errors.New(errmsg.ERR_NOT_AUTHORIZED)
		break
	case http.StatusNotFound:
		err = errors.New(errmsg.ERR_NOT_FOUND)
		break
	default:
		err = errors.New(errmsg.ERR_FAILED_SET_ACCOUNT_OWNER)
	}

	return err
}

func (as *AccountServiceClient) patchAccount(
	ctx context.Context,
	acctId string,
	update AccountUpdate,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})
	reqBodyJson, err := json.Marshal(update)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/accounts/%s", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PATCH",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}

	switch status {
	case http.StatusBadRequest:
		return ErrBadRequest{}
	case http.StatusNotFound:
		return ErrAccountNotFound{AccountID: acctId}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) VerifyEmail(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	verification Verification,
) (string, error) {
	endpoint := fmt.Sprintf("%s/v1/%s/verify", as.AccountServiceUrl, accountTypeEndpoint)

	reqBody, err := json.Marshal(verification)
	if err != nil {
		return "", err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBody,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusBadRequest,
			http.StatusUnauthorized,
			http.StatusNotFound,
			http.StatusNotAcceptable,
			http.StatusInternalServerError,
		},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}
	defer func() {
		err := resp.Close()
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Warn("error closing http resp")
		}
	}()

	switch status {
	case http.StatusNotAcceptable:
		bytes, err := io.ReadAll(resp)
		if err != nil {
			return "", err
		}

		var errMessage ErrAcctSvc
		err = json.Unmarshal(bytes, &errMessage)
		if err != nil {
			return "", err
		}

		return "", ErrPasswordRequirement{ErrMessage: errMessage.Message}
	case http.StatusBadRequest:
		return "", ErrVerifyEmail{Token: verification.Token}
	case http.StatusOK:
		bytes, err := io.ReadAll(resp)
		if err != nil {
			return "", err
		}

		var verificationResp VerificationResponse
		err = json.Unmarshal(bytes, &verificationResp)
		if err != nil {
			return "", err
		}
		return verificationResp.AccountId, nil
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) PostAccountVerificationCode(
	ctx context.Context,
	accountId string,
) (string, error) {
	endpoint := fmt.Sprintf("%s/v1/accounts/%s/verification/code", as.AccountServiceUrl, accountId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusInternalServerError},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}
	defer resp.Close()

	switch status {
	case http.StatusInternalServerError:
		return "", err
	case http.StatusOK:
		var token string
		body, err := io.ReadAll(resp)
		if err != nil {
			return "", err
		}
		err = json.Unmarshal(body, &token)
		return token, err
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) GetAccountVerificationCode(
	ctx context.Context,
	token string,
) (VerificationCodeResponse, error) {
	endpoint := fmt.Sprintf("%s/v1/accounts/verification/code/%s", as.AccountServiceUrl, token)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusInternalServerError},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return VerificationCodeResponse{}, err
	}
	defer resp.Close()

	switch status {
	case http.StatusOK:
		var verificationCodeResp VerificationCodeResponse
		body, err := io.ReadAll(resp)
		if err != nil {
			return VerificationCodeResponse{}, err
		}
		err = json.Unmarshal(body, &verificationCodeResp)
		return verificationCodeResp, err
	default:
		return VerificationCodeResponse{}, err
	}
}

func (as *AccountServiceClient) GetAccountEmailVerification(
	ctx context.Context,
	token string,
) (EmailVerification, error) {
	lg := logutils.DebugCtxLogger(ctx)
	endpoint := fmt.Sprintf("%s/v1/accounts/verification/email/%s", as.AccountServiceUrl, token)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusInternalServerError},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return EmailVerification{}, err
	}
	defer resp.Close()

	switch status {
	case http.StatusOK:
		var emailVerification EmailVerification
		body, err := io.ReadAll(resp)
		if err != nil {
			lg.WithError(err).Error("error closing response body")
			return EmailVerification{}, err
		}
		err = json.Unmarshal(body, &emailVerification)
		if err != nil {
			lg.WithError(err).Error("error unmarshaling response body")
			return EmailVerification{}, err
		}
		return emailVerification, err
	default:
		return EmailVerification{}, err
	}
}

func (as *AccountServiceClient) GetPasswordSetupVerificationToken(
	ctx context.Context,
	accountId string,
) (VerificationToken, error) {
	endpoint := fmt.Sprintf("%s/v1/accounts/setup/%s/token", as.AccountServiceUrl, accountId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PATCH",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return VerificationToken{}, err
	}
	defer resp.Close()

	switch status {
	case http.StatusOK:
		var result VerificationToken
		body, err := io.ReadAll(resp)
		if err != nil {
			return VerificationToken{}, err
		}
		err = json.Unmarshal(body, &result)
		return result, err
	default:
		return VerificationToken{}, err
	}
}

func (as *AccountServiceClient) UpdateEmail(
	ctx context.Context,
	emailUpdate UsersEmailUpdate,
) error {
	endpoint := fmt.Sprintf("%s/v1/accounts", as.AccountServiceUrl)

	reqBody, err := json.Marshal(emailUpdate)
	if err != nil {
		return err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "PATCH",
		TargetURL:  endpoint,
		ReqBody:    reqBody,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusBadRequest,
			http.StatusUnauthorized,
			http.StatusNotFound,
			http.StatusInternalServerError,
		},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}
	defer resp.Close()

	switch status {
	case http.StatusBadRequest:
		return ErrVerifyEmail{Token: emailUpdate.Token}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) AuthenticateAcctAndGetToken(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	email string,
	password string,
	ip string,
) (LoginSession, error) {
	resp, err := as.authenticateAcct(ctx, accountTypeEndpoint, email, password, ip, true, true)
	if err != nil {
		return LoginSession{}, err
	}
	gotBody, err := ioutil.ReadAll(resp)
	if err != nil {
		return LoginSession{}, err
	}

	var sesh LoginSession
	err = json.Unmarshal(gotBody, &sesh)
	if err != nil {
		return LoginSession{}, err
	}
	return sesh, nil
}

func (as *AccountServiceClient) authenticateAcct(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	email string,
	password string,
	ip string,
	sendEmail bool,
	getToken bool,
) (io.ReadCloser, error) {
	reqBodyJson, err := json.Marshal(AccountLogin{Email: email, Password: password, IP: ip})
	if err != nil {
		return nil, err
	}

	endpoint := fmt.Sprintf("%s/v1/%s/login", as.AccountServiceUrl, accountTypeEndpoint)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusBadRequest,
			http.StatusInternalServerError,
			http.StatusForbidden,
		},
		QueryParams: map[string]string{
			"token": strconv.FormatBool(getToken),
			"email": strconv.FormatBool(sendEmail),
		},
		// email sends a verification email if the creds are correct but the account is not yet verified, and a Setup account email if pword not set
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}
	if status != http.StatusOK {
		gotBody, err := ioutil.ReadAll(respBody)
		if err != nil {
			return nil, err
		}
		var resp AccountErrorRespBody
		err = json.Unmarshal(gotBody, &resp)
		if err != nil {
			return nil, err
		}
		return nil, errors.New(string(resp.Message))
	}
	return respBody, nil
}

func (as *AccountServiceClient) LookupAccountIdByEmail(
	ctx context.Context,
	email string,
) (string, error) {
	endpoint := fmt.Sprintf("%s/v1/accounts/getbyemail", as.AccountServiceUrl)

	reqBody, err := json.Marshal(email)
	if err != nil {
		return "", fmt.Errorf("error marshaling request body")
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ReqBody:       reqBody,
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	body, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}

	switch status {
	case http.StatusBadRequest:
		return "", ErrBadRequest{RegionID: regions.GetRegionID()}
	case http.StatusNotFound:
		return "", nil
	case http.StatusOK:
		acctId := ""
		err = json.NewDecoder(body).Decode(&acctId)
		if err != nil {
			return "", fmt.Errorf("error reading body")
		}
		return acctId, nil
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) LookupAccountIdByPatientId(
	ctx context.Context,
	patientId string,
) (string, error) {
	endpoint := fmt.Sprintf("%s/v1/accounts/getbypatientid", as.AccountServiceUrl)

	reqBody, err := json.Marshal(patientId)
	if err != nil {
		return "", fmt.Errorf("error marshaling request body")
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ReqBody:       reqBody,
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	body, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}

	switch status {
	case http.StatusBadRequest:
		return "", ErrBadRequest{RegionID: regions.GetRegionID()}
	case http.StatusNotFound:
		return "", nil
	case http.StatusOK:
		acctId := ""
		err = json.NewDecoder(body).Decode(&acctId)
		if err != nil {
			return "", fmt.Errorf("error reading body")
		}
		return acctId, nil
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) GetAccountInfo(
	ctx context.Context,
	acctId string,
) (*Account, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})

	endpoint := fmt.Sprintf("%s/v1/accounts/%s", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}

	if code == http.StatusNotFound {
		return nil, ErrAccountNotFound{AccountID: acctId}
	}

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		return nil, err
	}

	var acctInfo Account
	err = json.Unmarshal(gotBody, &acctInfo)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return nil, err
	}

	return &acctInfo, nil
}

func (as *AccountServiceClient) GetAccountInfoByEmail(
	ctx context.Context,
	email string,
) (*Account, error) {
	acctId, err := as.LookupAccountIdByEmail(ctx, email)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("error looking up acct id in acct svc")
		return nil, err
	}

	acct, err := as.GetAccountInfo(ctx, acctId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("acct_id", acctId).
			WithError(err).
			Error("error looking up acct in acct svc")
		return nil, err
	}
	return acct, nil
}

func (as *AccountServiceClient) RefreshToken(
	ctx context.Context,
	refreshToken string,
	ip string,
) (LoginSession, error) {
	lg := logutils.DebugCtxLogger(ctx)
	endpoint := fmt.Sprintf("%s/v1/accounts/login/refresh", as.AccountServiceUrl)
	req := LoginRefresh{
		RefreshToken: refreshToken,
		IP:           ip,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		lg.WithError(err).Error("could not marshal refresh request")
		return LoginSession{}, err
	}

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBody,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusForbidden, http.StatusBadRequest},
	}

	respBody, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return LoginSession{}, err
	}
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return LoginSession{}, err
	}
	if code != http.StatusOK {
		var errMsg AccountErrorRespBody
		err = json.Unmarshal(body, &errMsg)
		if err != nil {
			lg.WithError(err).Error("unable to unmarshal refresh resp")
			return LoginSession{}, err
		}
		lg.Error(errMsg.Message)
		if code == http.StatusNotFound && errMsg.Message == InvalidRefreshToken {
			lg.Error("refresh token invalid")
			return LoginSession{}, errors.New(string(InvalidRefreshToken))
		} else if code == http.StatusForbidden && errMsg.Message == ExpiredRefreshToken {
			lg.Error("refresh token expired")
			return LoginSession{}, errors.New(string(ExpiredRefreshToken))
		}
	}

	var login LoginSession
	err = json.Unmarshal(body, &login)
	if err != nil {
		lg.WithError(err).Error("unable to read resp")
		return LoginSession{}, err
	}

	return login, nil
}

func (as *AccountServiceClient) InitUpdateEmail(
	ctx context.Context,
	acctId string,
	newEmail string,
) error {
	newEmailHash := fmt.Sprintf("%x", sha1.Sum([]byte(newEmail))) // #nosec G401

	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":        acctId,
		"new_email_hash": newEmailHash,
	})
	reqBodyJson, err := json.Marshal(newEmail)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/accounts/%s/email", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		QueryParams:   map[string]string{"email": fmt.Sprint(true)},
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return err
	}

	switch status {
	case http.StatusNotFound:
		return ErrAccountNotFound{AccountID: acctId}
	case http.StatusBadRequest:
		return ErrInitUpdateEmail{EmailHash: newEmailHash}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) DeleteAccount(ctx context.Context, acctId string) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acctId)
	endpoint := fmt.Sprintf("%s/v1/accounts/%s", as.AccountServiceUrl, acctId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "DELETE",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
	}

	_, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return err
	}
	if code/100 != 2 {
		lg.WithField("status", code).Error("error deleting account")
		return fmt.Errorf("expected 2xx status code, got: %q", code)
	}

	return nil
}

func (as *AccountServiceClient) DeleteOrder(
	ctx context.Context,
	acctId string,
	orderId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("acct_id", acctId)
	endpoint := fmt.Sprintf("%s/v1/orders/%s/account/%s", as.AccountServiceUrl, orderId, acctId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "DELETE",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
	}

	_, _, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return err
	}

	return nil
}

func (as *AccountServiceClient) CancelSubscription(ctx context.Context, orderId string) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("order_id", orderId)
	endpoint := fmt.Sprintf("%s/v1/orders/%s/subscription/cancel", as.AccountServiceUrl, orderId)

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if status/100 != 2 {
		lg.WithField("status", status).Error("error canceling subscription account")
		return fmt.Errorf("expected 2xx status code, got: %q", status)
	}
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return err
	}

	return nil
}

func (as *AccountServiceClient) ResetPasswordInit(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	email string,
) error {
	emailHash := fmt.Sprintf("%x", sha1.Sum([]byte(email))) // #nosec #G401
	lg := logutils.DebugCtxLogger(ctx).WithField("email_hash", emailHash)

	reqBody, err := json.Marshal(email)
	if err != nil {
		return fmt.Errorf("error marshaling request body")
	}

	// query param email=true to send email
	endpoint := fmt.Sprintf("%s/v1/%s/reset", as.AccountServiceUrl, accountTypeEndpoint)
	queryParams := map[string]string{"email": "true"}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBody,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
		QueryParams:   queryParams,
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending reset password init request to acct svc")
		return err
	}

	switch status {
	case http.StatusBadRequest:
		return ErrBadRequestForPasswordReset{EmailHash: emailHash}
	case http.StatusNotFound:
		return ErrNoAccountForEmail{EmailHash: emailHash}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

// edit account email/password
func (as *AccountServiceClient) editAccount(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	acctEditInfo AccountEdit,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	// TODO: when patch account with email, assign to a real email hash
	emailHash := ""

	reqBody, err := json.Marshal(acctEditInfo)
	if err != nil {
		return fmt.Errorf("error marshaling request body")
	}

	endpoint := fmt.Sprintf("%s/v1/%s", as.AccountServiceUrl, accountTypeEndpoint)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PATCH",
		TargetURL:     endpoint,
		ReqBody:       reqBody,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound, http.StatusNotAcceptable},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending account edit request to acct svc")
		return err
	}

	switch status {
	case http.StatusBadRequest:
		return ErrBadRequestAccountEdit{EmailHash: emailHash}
	case http.StatusNotFound:
		// TODO: implement email reset tokens email token not found
		if acctEditInfo.Email == "" {
			return ErrResetPasswordNotFound{}
		}
	case http.StatusNotAcceptable:
		// acctsvc returns the unfulfilled requirement in the response body,
		// so read and return it.
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return ErrBadNewPassword{}
		}

		var errResp ErrorResponse
		err = json.Unmarshal(body, &errResp)
		if err != nil {
			lg.WithError(err).Error("error parsing error response")
			return ErrBadNewPassword{}
		}

		return ErrBadNewPassword{MissingRequirement: errResp.Message}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
	return nil
}

// Set new plaintext password on account
func (as *AccountServiceClient) ResetPassword(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	resetInfo AccountEdit,
) error {
	return as.editAccount(ctx, accountTypeEndpoint, resetInfo)
}

func (as *AccountServiceClient) createOrder(
	ctx context.Context,
	acctId string,
	newOrder NewOrder,
	sendEmail bool,
) (CreateOrderResponse, error) {
	if !newOrder.Valid() {
		return CreateOrderResponse{}, errors.New(http.StatusText(http.StatusBadRequest))
	}
	if newOrder.RegionId == 0 {
		// default to current core's region
		newOrder.RegionId = regions.GetRegionID()
	}

	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":            acctId,
		"plan_id":            newOrder.PlanId,
		"region_id":          newOrder.RegionId,
		"country":            newOrder.Country,
		"request_id":         newOrder.RequestId,
		"disable_auto_renew": newOrder.DisableAutoRenew,
		"discount_name":      newOrder.DiscountName,
	})
	reqBodyJson, err := json.Marshal(newOrder)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return CreateOrderResponse{}, err
	}

	endpoint := fmt.Sprintf("%s/v1/orders/account/%s", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:  "POST",
		TargetURL:   endpoint,
		ReqBody:     reqBodyJson,
		QueryParams: map[string]string{"email": fmt.Sprint(sendEmail)},
		AuthScheme:  httpclient.Basic,
		AuthKey:     as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusBadRequest,
			http.StatusPaymentRequired,
		},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send create order request")
		return CreateOrderResponse{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusNotFound:
		return CreateOrderResponse{}, ErrAccountNotFound{AccountID: acctId}
	case http.StatusBadRequest:
		return CreateOrderResponse{}, ErrBadCreateOrderRequest{
			AccountID: acctId,
			PlanID:    newOrder.PlanId,
			RegionID:  newOrder.RegionId,
		}
	case http.StatusPaymentRequired:
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return CreateOrderResponse{}, err
		}

		var response ErrorResponse
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing boolean")
			return CreateOrderResponse{}, err
		}

		return CreateOrderResponse{}, ErrPayment{
			Reason:    response.Message,
			AccountID: acctId,
			PlanID:    newOrder.PlanId,
			RegionID:  newOrder.RegionId,
		}
	case http.StatusConflict:
		fallthrough
	case http.StatusOK:
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return CreateOrderResponse{}, err
		}

		var response CreateOrderResponse
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing boolean")
			return CreateOrderResponse{}, err
		}

		return response, nil
	}
	return CreateOrderResponse{}, fmt.Errorf("got %v back from acct svc", status)
}

func (as *AccountServiceClient) CreateOrderIntent(
	ctx context.Context,
	orderIntent NewOrderIntent,
) (CreateOrderIntentResponse, error) {
	if !orderIntent.Valid() {
		return CreateOrderIntentResponse{}, errors.New(http.StatusText(http.StatusBadRequest))
	}

	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": orderIntent.AccountId,
		"plan_id": orderIntent.PlanId,
	})
	reqBodyJson, err := json.Marshal(orderIntent)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return CreateOrderIntentResponse{}, err
	}

	endpoint := fmt.Sprintf("%s/v1/orders/intent", as.AccountServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusInternalServerError, http.StatusBadRequest},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send create order request")
		return CreateOrderIntentResponse{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusBadRequest:
		return CreateOrderIntentResponse{}, ErrBadCreateOrderRequest{
			AccountID: orderIntent.AccountId,
			PlanID:    orderIntent.PlanId,
		}
	case http.StatusInternalServerError:
		return CreateOrderIntentResponse{}, fmt.Errorf("got %v back from acct svc", status)
	case http.StatusCreated:
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return CreateOrderIntentResponse{}, err
		}

		var response CreateOrderIntentResponse
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing boolean")
			return CreateOrderIntentResponse{}, err
		}

		return response, nil
	}
	return CreateOrderIntentResponse{}, fmt.Errorf("got %v back from acct svc", status)
}

func (as *AccountServiceClient) UpdateOrderIntent(
	ctx context.Context,
	updateOrderIntent UpdateOrderIntent,
) error {
	if !updateOrderIntent.Valid() {
		return errors.New(http.StatusText(http.StatusBadRequest))
	}

	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  updateOrderIntent.AccountId,
		"order_id": updateOrderIntent.OrderId,
	})
	reqBodyJson, err := json.Marshal(updateOrderIntent)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/orders/intent", as.AccountServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PUT",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusInternalServerError, http.StatusBadRequest},
	}

	respBody, status, errRequest := as.HttpClient.SendRequest(ctx, reqParams)
	if errRequest != nil {
		lg.WithError(errRequest).Error("unable to send create order request")
		return errRequest
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusBadRequest:
		return ErrBadUpdateOrderRequest{
			AccountID: updateOrderIntent.AccountId,
		}
	case http.StatusInternalServerError:
		return fmt.Errorf("got %v back from acct svc", status)
	case http.StatusNoContent:

		return errRequest
	}
	return fmt.Errorf("got %v back from acct svc", status)
}

func (as *AccountServiceClient) RefundOrder(
	ctx context.Context,
	acctId string,
	orderId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"order_id": orderId,
	})

	lg.Info("Refund order in acctsvc")

	reqBodyJson, err := json.Marshal(OrderRefundRequest{})
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s/refund",
		as.AccountServiceUrl,
		orderId,
		acctId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send refund order request")
		return err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusBadRequest:
		return ErrBadRefundOrderRequest{AccountID: acctId, OrderID: orderId}
	case http.StatusNotFound:
		return ErrOrderNotFound{OrderID: orderId, AccountID: acctId}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) ToggleAutoRenew(
	ctx context.Context,
	acctId string,
	orderId string,
	autoRenew bool,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"order_id": orderId,
	})

	if acctId == "" || orderId == "" {
		return fmt.Errorf("invalid acctID or orderID when toggling auto renew")
	}

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s/autorenew",
		as.AccountServiceUrl,
		orderId,
		acctId,
	)

	reqBodyJson, err := json.Marshal(autoRenew)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PATCH",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}
	if err != nil {
		lg.WithError(err).Error("error marshalling response")
		return err
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return err
	}
	switch status {
	case http.StatusNotFound:
		return ErrOrderNotFound{OrderID: orderId, AccountID: acctId}
	case http.StatusBadRequest:
		return ErrAutoRenewBadRequest{OrderID: orderId}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) CreateOrder(
	ctx context.Context,
	acctId string,
	newOrder NewOrder,
) (CreateOrderResponse, error) {
	return as.createOrder(ctx, acctId, newOrder, true)
}

// TODO: once we send purchase email in acctsvc, we can remove this function
func (as *AccountServiceClient) CreateOrderWithoutEmail(
	ctx context.Context,
	acctId string,
	newOrder NewOrder,
) (CreateOrderResponse, error) {
	return as.createOrder(ctx, acctId, newOrder, false)
}

func (as *AccountServiceClient) UpdateOrderPaymentDetails(
	ctx context.Context,
	acctId string,
	orderId string,
	paymentDetails OrderPaymentDetailsRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"order_id": orderId,
	})

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s/payment",
		as.AccountServiceUrl,
		orderId,
		acctId,
	)

	reqBodyJson, err := json.Marshal(paymentDetails)
	if err != nil {
		lg.WithError(err).Error("error creating request body")
		return err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest, http.StatusUnauthorized},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error processing request from acct svc")
		return err
	}

	switch status {
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf(
			"error processing update order payment details: got %v back from acct svc",
			status,
		)
	}
}

func (as *AccountServiceClient) CreateOrderActionReason(
	ctx context.Context,
	acctId string,
	orderId string,
	or OrderActionReason,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"order_id": orderId,
	})

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s/reason",
		as.AccountServiceUrl,
		orderId,
		acctId,
	)

	reqBodyJson, err := json.Marshal(or)
	if err != nil {
		lg.WithError(err).Error("error creating request body")
		return err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest, http.StatusUnauthorized},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error processing request from acct svc")
		return err
	}

	switch status {
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf(
			"error processing order reason: got %v back from acct svc",
			status,
		)
	}
}

func (as *AccountServiceClient) IsSSOEnabled(
	ctx context.Context,
	acctId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/ssoEnabled",
		as.AccountServiceUrl,
		acctId,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	response, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error processing request from acct svc")
		return false
	}

	switch status {
	case http.StatusOK:
		body, err := io.ReadAll(response)
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return false
		}
		isSSOEnabled, err := strconv.ParseBool(strings.TrimSpace(string(body)))
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return false
		}
		return isSSOEnabled
	default:
		return false
	}
}

func (as *AccountServiceClient) AccountLoginSSO(
	ctx context.Context,
	ssoToken string,
	ip string,
) (AccountSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	var result AccountSSOLoginResponse

	if ssoToken == "" {
		return result, fmt.Errorf("invalid sso token")
	}

	request := AccountSSOLoginRequest{
		Token:     ssoToken,
		IpAddress: ip,
	}
	endpoint := fmt.Sprintf("%s/v1/accounts/login/sso", as.AccountServiceUrl)
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return result, err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusBadRequest,
			http.StatusUnauthorized,
			http.StatusForbidden,
		},
	}

	response, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return result, err
	}
	defer response.Close()

	switch status {
	case http.StatusOK:
		body, err := ioutil.ReadAll(response)
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return result, err
		}
		err = json.Unmarshal(body, &result)
		if err != nil {
			lg.WithError(err).Error("error parsing json result")
			return result, err
		}
		return result, nil
	case http.StatusForbidden:
		// for now, a 403 can only happen if the account is locked
		lg.Infof("sso token `%s` is forbidden; account is locked", ssoToken)
		return result, errors.New(errmsg.ERR_ACCOUNT_LOCKED)
	case http.StatusNotFound:
		// for now, a 404 can only happen if the account doesn't have a valid token
		lg.Infof("sso token `%s` is invalid and/or not found", ssoToken)
		return result, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	default:
		lg.WithError(err).Error("error processing login sso request in acct svc")
		return result, fmt.Errorf(
			"error processing login sso request: got %v back from acct svc",
			status,
		)
	}
}

func (as *AccountServiceClient) AccountLoginGoogle(
	ctx context.Context,
	jwt string,
	ip string,
) (GoogleSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	var result AccountSSOLoginResponse
	var response GoogleSSOLoginResponse

	if jwt == "" {
		return response, fmt.Errorf("missing Google JWT")
	}

	request := AccountSSOLoginRequest{
		Token:     jwt,
		IpAddress: ip,
	}
	endpoint := fmt.Sprintf("%s/v1/accounts/login/google", as.AccountServiceUrl)
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return response, err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusBadRequest,
			http.StatusUnauthorized,
			http.StatusForbidden,
		},
	}

	httpResponse, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("httpclient error")
		return response, err
	}
	defer httpResponse.Close()

	switch status {
	case http.StatusOK:
		body, err := io.ReadAll(httpResponse)
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return response, err
		}
		err = json.Unmarshal(body, &result)
		if err != nil {
			lg.WithError(err).Error("error parsing json result")
			return response, err
		}
		response := GetCamelCaseGoogleSSOLoginResponse(result)
		return response, nil
	case http.StatusForbidden:
		// for now, a 403 can only happen if the account is locked
		lg.Infof("account is locked")
		return response, errors.New(errmsg.ERR_ACCOUNT_LOCKED)
	case http.StatusBadRequest:
		lg.Infof("invalid google jwt")
		return response, errors.New(errmsg.ERR_BAD_TOKEN)
	default:
		lg.WithError(err).Error("error processing login google sso request in acct svc")
		return response, fmt.Errorf(
			"error processing login google sso request: got %v back from acct svc",
			status,
		)
	}
}

func (as *AccountServiceClient) AccountLoginViaSSO(
	ctx context.Context,
	token, accountId, ipAddress string,
) (AccountSSOLoginResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	var result AccountSSOLoginResponse

	if token == "" {
		return result, fmt.Errorf("invalid login sso token")
	}
	if accountId == "" {
		return result, fmt.Errorf("invalid account Identifier")
	}
	lg.WithField("account_id", accountId).Info("login via sso token account identifier")

	request := AccountSSOLoginRequest{
		Token:     token,
		IpAddress: ipAddress,
	}
	endpoint := fmt.Sprintf("%s/v1/accounts/login/%s/sso", as.AccountServiceUrl, accountId)

	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		lg.WithError(err).Error("error setting up json request body")
		return result, err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusOK,
			http.StatusNotFound,
			http.StatusBadRequest,
			http.StatusForbidden,
		},
	}

	response, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying accountsvc")
		return result, err
	}
	defer response.Close()

	switch code {
	case http.StatusOK:
		body, err := io.ReadAll(response)
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return result, err
		}
		err = json.Unmarshal(body, &result)
		if err != nil {
			lg.WithError(err).Error("error parsing json result")
			return result, err
		}
		return result, nil
	case http.StatusForbidden:
		lg.Infof("login via sso token request `%s` is forbidden", token)
		return result, errors.New(errmsg.ERR_ACCOUNT_LOCKED)
	case http.StatusNotFound:
		lg.Infof("login via sso token request `%s` is invalid and/or not found", token)
		return result, errors.New(errmsg.ERR_BAD_TOKEN)
	default:
		lg.WithError(err).Info("error processing login via account sso request in acct svc")
		return result, fmt.Errorf(
			"error processing login via account sso token: got %v back from acctsvc",
			code,
		)
	}
}

func (as *AccountServiceClient) GenerateAccountLoginSSOToken(
	ctx context.Context,
	accountId string,
	expiresAt time.Time,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)

	if accountId == "" {
		return "", fmt.Errorf("invalid account Identifier")
	}
	lg.WithField("account_id", accountId).Info("generate login sso token account identifier")

	endpoint := fmt.Sprintf("%s/v1/sso/login/%s/create", as.AccountServiceUrl, accountId)

	request := CreateSSOTokenData{
		AccountId: accountId,
		ExpiresAt: expiresAt,
		Status:    Generated,
		CreatedAt: time.Now().UTC(),
	}
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		lg.WithError(err).Info("error setting up json request body")
		return "", err
	}

	reqParams := httpclient.RequestParameters{
		HTTPMethod: "POST",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusCreated,
		},
	}

	response, code, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying accountsvc")
		return "", err
	}
	defer response.Close()

	result := ""
	body, err := io.ReadAll(response)
	if err != nil {
		lg.WithError(err).Info("error reading result body")
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Info("error parsing json result")
		return result, err
	}
	return result, nil
}

func (as *AccountServiceClient) GetAccountMainRegion(
	ctx context.Context,
	acctId string,
) (uint16, string, error) {
	acct, err := as.GetAccountInfo(ctx, acctId)
	if err != nil {
		return 0, "", err
	}
	regionCode := ""
	if acct.MainRegion == 1 {
		regionCode = "CA"
	} else if acct.MainRegion == 2 {
		regionCode = "US"
	}
	if regionCode == "" {
		return 0, "", errors.New("unrecognized region")
	}
	return acct.MainRegion, regionCode, nil
}

func (as *AccountServiceClient) GetOrders(
	ctx context.Context,
	acctId string,
	filters map[string]bool,
) ([]Order, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
		"filters": filters,
	})

	queryParams := map[string]string{}
	for key, value := range filters {
		queryParams[key] = fmt.Sprint(value)
	}

	endpoint := fmt.Sprintf("%s/v1/orders/account/%s", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		QueryParams:   queryParams,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send get orders request")
		return nil, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusNotFound:
		return nil, ErrAccountNotFound{AccountID: acctId}
	case http.StatusBadRequest:
		return nil, ErrBadGetOrders{AccountID: acctId, Active: filters["active"]}
	case http.StatusOK:
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return nil, err
		}

		var response []Order
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing get orders response")
			return nil, err
		}

		return response, nil
	default:
		return nil, fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) GetOrderPlanId(
	ctx context.Context,
	accountId string,
	orderId string,
) uint64 {
	var planId uint64
	// Fetch any existing order to do upgrade attribution from planIdA --> planIdB
	orders, err := as.GetOrders(
		ctx,
		accountId,
		map[string]bool{"active": true},
	)
	if err == nil {
		for _, order := range orders {
			if orderId == "" || orderId == order.OrderId {
				planId = order.PlanId
				break
			}
		}
	} else {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Errorf("unable to fetch account:%s orders for amplitude attribution", accountId)
	}
	return planId
}

/* fetches the planId of inactive order cancelled within last 10 minutes */
func (as *AccountServiceClient) GetInactiveOrderPlanId(
	ctx context.Context,
	accountId string,
) uint64 {
	var planId uint64
	// Fetch any existing order to do upgrade attribution from planIdA --> planIdB
	orders, err := as.GetOrders(
		ctx,
		accountId,
		map[string]bool{"active": false},
	)
	if err == nil {
		currentTime := time.Now()
		tenMinutesAgo := currentTime.Add(-10 * time.Minute)
		for _, order := range orders {
			// is within the last 10 minutes
			if order.CancelledAt.After(tenMinutesAgo) && order.CancelledAt.Before(currentTime) {
				planId = order.PlanId
				break
			}
		}
	} else {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Errorf("unable to fetch account:%s orders for amplitude attribution", accountId)
	}
	return planId
}

func (as *AccountServiceClient) GetOrderById(
	ctx context.Context,
	acctId string,
	orderId string,
) (OrderDetails, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"order_id": orderId,
	})

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s/billing",
		as.AccountServiceUrl,
		orderId,
		acctId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound, http.StatusBadRequest},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("unable to send get order billing info request")
		return OrderDetails{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	switch status {
	case http.StatusNotFound:
		return OrderDetails{}, ErrAccountNotFound{AccountID: acctId}
	case http.StatusBadRequest:
		return OrderDetails{}, ErrBadGetOrderById{AccountID: acctId, OrderID: orderId}
	case http.StatusOK:
		body, err := ioutil.ReadAll(respBody)
		if err != nil {
			lg.WithError(err).Error("error reading resp body")
			return OrderDetails{}, err
		}

		var response OrderDetails
		err = json.Unmarshal(body, &response)
		if err != nil {
			lg.WithError(err).Error("error parsing billing info")
			return OrderDetails{}, err
		}

		return response, nil
	default:
		return OrderDetails{}, fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as *AccountServiceClient) AddPatient(
	ctx context.Context,
	acctId string,
	rB Patient,
) (string, error) {
	return "", errors.New("v1 POST patient is deprecated, use GetOrCreatePatient instead for v2")
}

// GetOrCreatePatient calls acctsvc's POST /v2/accounts/:id/patients endpoint.
// `createOnDup` controls whether the endpoint would care that it is creating a duplicate patient
// within an account - if `createOnDup` is set to true, it wouldn't try to match existing patients.
// when set to false, the endpoint tries to find an existing patient matching the request, before
// adding a new one if none is found.
func (as *AccountServiceClient) GetOrCreatePatient(
	ctx context.Context,
	acctId string,
	pt Patient,
	createOnDup bool,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", acctId)

	endpoint := fmt.Sprintf("%s/v2/accounts/%s/patients", as.AccountServiceUrl, acctId)
	reqBodyJson, err := json.Marshal(pt)
	if err != nil {
		lg.WithError(err).Error("error creating request body")
		return "", err
	}

	queryParam := map[string]string{"create_on_duplicate": fmt.Sprint(createOnDup)}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		QueryParams:   queryParam,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound, http.StatusConflict},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}

	if status >= http.StatusBadRequest {
		lg.Errorf("got %d from acctsvc for v2 patient post", status)
		return "", errors.New(errmsg.ERR_FAILED_CREATE_PATIENT)
	}

	respBytes, err := io.ReadAll(respBody)
	if err != nil {
		return "", err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var patient Patient
	err = json.Unmarshal(respBytes, &patient)
	if err != nil {
		lg.WithField("resp_body", string(respBytes)).
			WithError(err).
			Error("error unmarshaling response body")
		return "", err
	}

	return patient.PatientId, nil
}

// performs a soft deletion on patient entity and hard delete on PHI
func (as *AccountServiceClient) DeletePatient(
	ctx context.Context,
	acctId string,
	patientId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"acct_id": acctId, "patient_id": patientId})
	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/patients/%s",
		as.AccountServiceUrl,
		acctId,
		patientId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "DELETE",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}

	if status != http.StatusOK {
		lg.Errorf("got %d from acctsvc for patient delete", status)
		return errors.New("patient delete failed")
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	return nil
}

func (as *AccountServiceClient) UpdatePatient(
	ctx context.Context,
	acctId string,
	patientId string,
	rB Patient,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"acct_id": acctId, "patient_id": patientId})
	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/patients/%s",
		as.AccountServiceUrl,
		acctId,
		patientId,
	)
	reqBodyJson, err := json.Marshal(rB)
	if err != nil {
		lg.WithError(err).Error("error creating request body")
		return errors.New(errormsgs.ERR_INVALID_REQ_BODY)
	}

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PATCH",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	respBody, statusCode, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("could not send request patient update request")
		return err
	}

	if statusCode >= http.StatusBadRequest {
		err = errors.New(http.StatusText(statusCode))
		lg.WithError(err).Error("Could not update patient")
		return err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	return nil
}

func (as *AccountServiceClient) GetPatients(
	ctx context.Context,
	acctId string,
) ([]Patient, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", acctId)

	endpoint := fmt.Sprintf("%s/v1/accounts/%s/patients", as.AccountServiceUrl, acctId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending request")
		return nil, err
	}

	if status != http.StatusOK {
		lg.Errorf("got %d from acctsvc for get patients", status)
		return nil, errors.New("get patients failed")
	}

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		return nil, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var patients []Patient
	err = json.Unmarshal(gotBody, &patients)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return patients, err
	}

	return patients, nil
}

func (as *AccountServiceClient) GetPatient(
	ctx context.Context,
	acctId string,
	patientId string,
) (Patient, error) {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"account_id": acctId, "patient_id": patientId})

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/patients/%s",
		as.AccountServiceUrl,
		acctId,
		patientId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		ReqBody:       nil,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusNotFound},
	}

	respBody, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error sending request")
		return Patient{}, err
	}
	if status != http.StatusOK {
		lg.Errorf("got %d from acctsvc for get patient", status)
		return Patient{}, errors.New("get patient failed")
	}

	gotBody, err := ioutil.ReadAll(respBody)
	if err != nil {
		return Patient{}, err
	}

	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	var patient Patient
	err = json.Unmarshal(gotBody, &patient)
	if err != nil {
		lg.WithField("resp_body", string(gotBody)).
			WithError(err).
			Error("error unmarshaling response body")
		return patient, err
	}

	return patient, nil
}

func (as *AccountServiceClient) GetMagicLinkToken(
	ctx context.Context,
	acctId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
	})

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/ssoEnabled",
		as.AccountServiceUrl,
		acctId,
	)

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	response, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Error("error processing request from acct svc")
		return false
	}

	switch status {
	case http.StatusOK:
		body, err := io.ReadAll(response)
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return false
		}
		isSSOEnabled, err := strconv.ParseBool(strings.TrimSpace(string(body)))
		if err != nil {
			lg.WithError(err).Error("error reading result body")
			return false
		}
		return isSSOEnabled
	default:
		return false
	}
}

func (as *AccountServiceClient) PatchOrder(
	ctx context.Context,
	accountID string,
	orderID string,
	por PatchOrderRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"acct_id": accountID, "order_id": orderID})

	endpoint := fmt.Sprintf(
		"%s/v1/orders/%s/account/%s",
		as.AccountServiceUrl,
		orderID,
		accountID,
	)
	reqBodyJson, err := json.Marshal(por)
	if err != nil {
		lg.WithError(err).Error("error creating request body")
		return err
	}

	reqParams := httpclient.RequestParameters{
		HTTPMethod:    http.MethodPatch,
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}

	respBody, statusCode, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	if statusCode != http.StatusOK {
		return ErrPatchOrdersFailed{AccountID: accountID, OrderID: orderID, StatusCode: statusCode}
	}

	return nil
}

func (as *AccountServiceClient) AccountHasAccessToPatient(
	ctx context.Context,
	accountId, patientId string,
) bool {
	log := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"patient_id": patientId,
	})

	patients, err := as.GetPatients(ctx, accountId)
	if err != nil {
		log.WithError(err).Error("Could not get patients for account")
		return false
	}

	for _, patient := range patients {
		if patient.PatientId == patientId {
			return true
		}
	}

	log.Error("Account does not have access to patient")
	return false
}

func (as *AccountServiceClient) GetAllPatientProfileValidationsForAccount(
	ctx context.Context,
	accountId string,
) (map[string]PatientProfileValidation, error) {
	logger := logutils.DebugCtxLogger(ctx).WithField("account_id", accountId)

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/patients/valid",
		as.AccountServiceUrl,
		accountId,
	)

	res, status, err := as.HttpClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodGet,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound, http.StatusForbidden},
	})
	if err != nil {
		logger.WithError(err).Error("Could not fetch patient profile validations")
		return nil, err
	}
	defer func() {
		err := res.Close()
		if err != nil {
			logger.WithError(err).Error("Could not close response body")
		}
	}()

	if status != 200 {
		var errMsg string
		switch status {
		case http.StatusForbidden:
			errMsg = errmsg.ERR_FORBIDDEN
		case http.StatusNotFound:
			errMsg = errmsg.ERR_NOT_FOUND
		default:
			errMsg = http.StatusText(status)
		}
		err = errors.New(errMsg)
		logger.WithError(err).Error("Could not fetch account profile validations")
		return nil, err
	}

	body, err := io.ReadAll(res)
	if err != nil {
		logger.WithError(err).
			Errorf("Could not read response body: %v", strings.TrimSpace(string(body)))
		return nil, err
	}
	profileValidations := map[string]PatientProfileValidation{}
	err = json.Unmarshal(body, &profileValidations)
	if err != nil {
		return nil, err
	}

	return profileValidations, err
}

func (as *AccountServiceClient) GetAccountPatientProfileValidation(
	ctx context.Context,
	accountId, patientId string,
) (PatientProfileValidation, error) {
	logger := logutils.DebugCtxLogger(ctx).WithField("account_id", accountId)

	endpoint := fmt.Sprintf(
		"%s/v1/accounts/%s/patients/%s/valid",
		as.AccountServiceUrl,
		accountId,
		patientId,
	)

	res, status, err := as.HttpClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodGet,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound, http.StatusForbidden},
	})
	if err != nil {
		logger.WithError(err).Error("Could not fetch patient profile validations")
		return PatientProfileValidation{}, err
	}
	defer func() {
		err := res.Close()
		if err != nil {
			logger.WithError(err).Error("Could not close response body")
		}
	}()

	if status != http.StatusOK {
		var errMsg string
		switch status {
		case http.StatusForbidden:
			errMsg = errmsg.ERR_FORBIDDEN
		case http.StatusNotFound:
			errMsg = errmsg.ERR_NOT_FOUND
		default:
			errMsg = http.StatusText(status)
		}
		err = errors.New(errMsg)
		logger.WithError(err).Error("Could not fetch account patient profile validations")
		return PatientProfileValidation{}, err
	}

	body, err := io.ReadAll(res)
	if err != nil {
		logger.WithError(err).Error("Could not read response body")
		return PatientProfileValidation{}, err
	}
	profileValidation := PatientProfileValidation{}
	err = json.Unmarshal(body, &profileValidation)
	if err != nil {
		logger.WithError(err).Error("Could not unmarshal response body")
		return PatientProfileValidation{}, errors.New(errmsg.ERR_JSON_UNMARSHAL)
	}

	return profileValidation, err
}
