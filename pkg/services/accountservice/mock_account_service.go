package accountservice

import (
	"context"
	"fmt"
	"time"

	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

type AcctSvcMock struct {
	// general (reused)
	ExpectError bool
	MockError   error
	Called      bool

	ExpectedAcctId string

	// function-specific
	UpdateAccountSettingsReturn                                error
	CreateAccountCalled                                        bool
	AddNewRegionReturn                                         error
	SetPasswordReturn                                          error
	SetPasswordHashReturn                                      error
	VerifyAccountReturn                                        error
	VerifyEmailReturn                                          error
	UpdateAccountEmailReturn                                   error
	LockAccountReturn                                          error
	AuthReturn                                                 bool
	GetAccountInfoReturn                                       Account
	GetAccountIDByEmailReturn                                  string
	GetAccountInfoByEmailReturn                                *Account
	GetAccountInfoByEmailErrRet                                error
	LookUpAccountByEmailReturn                                 string
	LookUpAccountByPatientIdReturn                             string
	UseAcctSvcEmailsReturn                                     bool
	ResetPasswordInitReturn                                    error
	ResetPasswordReturn                                        error
	CreateOrderReturn                                          CreateOrderResponse
	AccountLoginSSOReturn                                      AccountSSOLoginResponse
	GoogleSSOLoginResponse                                     GoogleSSOLoginResponse
	UpdatePatientReturn                                        error
	UpdatePatientCalled                                        bool
	DeletePatientReturn                                        error
	GetPatientReturn                                           Patient
	GetPatientsReturn                                          []Patient
	GetOrdersReturn                                            []Order
	phyAcctIds                                                 []string
	SearchPhysicianAccountCalled                               bool
	GetPhysicianAccountReturn                                  PhysicianAccount
	GetPhysicianPermissionsByProviderIdAndPermissionNameReturn map[int64][]PhysicianAccountPermission
	AccountHasAccessToPatientReturn                            bool
	GetAllPatientProfileValidationsForAccountReturn            map[string]PatientProfileValidation
	GetAllPatientProfileValidationsReturn                      PatientProfileValidation
	GetOrCreatePatientReturn                                   string

	CreateOrderRequests map[string]NewOrder
}

func (m *AcctSvcMock) AccountLoginViaSSO(
	ctx context.Context,
	token, accountId, ipAddress string,
) (AccountSSOLoginResponse, error) {
	return AccountSSOLoginResponse{AccExists: true}, nil
}

func (m *AcctSvcMock) GenerateAccountLoginSSOToken(
	ctx context.Context,
	accountId string,
	expiresAt time.Time,
) (string, error) {
	return "token", nil
}

func (m *AcctSvcMock) UpdateAccountSettings(
	ctx context.Context,
	acctId string,
	accountSettings UserSettingsRequest,
) error {
	return m.UpdateAccountSettingsReturn
}

func (m *AcctSvcMock) GetAccountSettings(
	ctx context.Context,
	acctId string,
) (*UserSettings, error) {
	panic("not implemented")
}

func (m *AcctSvcMock) CreateAccountAndSendVerifyEmail(
	ctx context.Context,
	newAcct NewAccount,
) (string, error) {
	m.CreateAccountCalled = true
	return ksuid.New().String(), m.MockError
}

func (m *AcctSvcMock) AddNewRegion(
	ctx context.Context,
	regionId uint16,
	accountId string,
) error {
	return m.AddNewRegionReturn
}

func (m *AcctSvcMock) SetPasswordAndVerify(
	ctx context.Context,
	accountId string,
	password string,
) error {
	return m.SetPasswordReturn
}

func (m *AcctSvcMock) VerifyEmail(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	verification Verification,
) (string, error) {
	return "", m.VerifyEmailReturn
}

func (m *AcctSvcMock) GetPasswordSetupVerificationToken(
	ctx context.Context,
	accountId string,
) (VerificationToken, error) {
	return VerificationToken{}, nil
}

func (m *AcctSvcMock) LockAccount(ctx context.Context, accountId string) error {
	return m.LockAccountReturn
}

func (m *AcctSvcMock) DeactivateAccount(ctx context.Context, accountId string, token string) error {
	return nil
}

func (m *AcctSvcMock) SetAccountOwner(ctx context.Context, accountId string, request models.SetAccountOwnerRequest) error {
	return nil
}

func (m *AcctSvcMock) AuthenticateAcctAndGetToken(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	email string,
	password string,
	ip string,
) (LoginSession, error) {
	return LoginSession{"1234", "1234"}, nil
}

func (m *AcctSvcMock) LookupAccountIdByEmail(ctx context.Context, email string) (string, error) {
	return m.LookUpAccountByEmailReturn, nil
}

func (m *AcctSvcMock) LookupAccountIdByPatientId(
	ctx context.Context,
	patientId string,
) (string, error) {
	return m.LookUpAccountByPatientIdReturn, nil
}

func (m *AcctSvcMock) GetAccountInfo(ctx context.Context, acctId string) (*Account, error) {
	return &m.GetAccountInfoReturn, nil
}

func (m *AcctSvcMock) GetAccountInfoByEmail(ctx context.Context, email string) (*Account, error) {
	if m.GetAccountInfoByEmailErrRet != nil {
		return nil, m.GetAccountInfoByEmailErrRet
	}
	return m.GetAccountInfoByEmailReturn, nil
}

func (m *AcctSvcMock) UpdateEmail(ctx context.Context, emailUpdate UsersEmailUpdate) error {
	return m.UpdateAccountEmailReturn
}

func (m *AcctSvcMock) InitUpdateEmail(ctx context.Context, acctId string, newEmail string) error {
	return nil
}

func (m *AcctSvcMock) RefreshToken(
	ctx context.Context,
	refreshToken string,
	ip string,
) (LoginSession, error) {
	return LoginSession{Token: "123", RefreshToken: "123"}, nil
}

func (m *AcctSvcMock) DeleteAccount(ctx context.Context, acctId string) error {
	return nil
}

func (m *AcctSvcMock) DeleteOrder(ctx context.Context, acctId string, orderId string) error {
	return nil
}

func (m *AcctSvcMock) CancelSubscription(ctx context.Context, orderId string) error {
	return nil
}

func (m *AcctSvcMock) ResetPasswordInit(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	email string,
) error {
	return m.ResetPasswordInitReturn
}

func (m *AcctSvcMock) ResetPassword(
	ctx context.Context,
	accountTypeEndpoint AccountTypeEndpoint,
	acctEditInfo AccountEdit,
) error {
	return m.ResetPasswordReturn
}

func (m *AcctSvcMock) CreateOrder(
	ctx context.Context,
	acctId string,
	newOrder NewOrder,
) (CreateOrderResponse, error) {
	if m.ExpectError {
		return CreateOrderResponse{}, m.MockError
	}
	m.CreateOrderRequests[m.CreateOrderReturn.OrderId] = newOrder
	return m.CreateOrderReturn, nil
}

func (m *AcctSvcMock) RefundOrder(ctx context.Context, acctId string, orderId string) error {
	m.Called = true
	if m.ExpectError {
		return m.MockError
	}
	return nil
}

func (m *AcctSvcMock) ToggleAutoRenew(
	ctx context.Context,
	acctId string,
	orderId string,
	autoRenew bool,
) error {
	return nil
}

func (m *AcctSvcMock) CreateOrderWithoutEmail(
	ctx context.Context,
	acctId string,
	newOrder NewOrder,
) (CreateOrderResponse, error) {
	if m.ExpectError {
		return CreateOrderResponse{}, m.MockError
	}
	return m.CreateOrderReturn, nil
}

func (m *AcctSvcMock) AccountLoginSSO(
	ctx context.Context,
	ssoToken string,
	ip string,
) (AccountSSOLoginResponse, error) {
	if m.ExpectError {
		return AccountSSOLoginResponse{}, m.MockError
	}
	return m.AccountLoginSSOReturn, nil
}

func (m *AcctSvcMock) AccountLoginGoogle(
	ctx context.Context,
	jwt string,
	ip string,
) (GoogleSSOLoginResponse, error) {
	if m.ExpectError {
		return GoogleSSOLoginResponse{}, m.MockError
	}
	return m.GoogleSSOLoginResponse, nil
}

func (m *AcctSvcMock) UpdateOrderPaymentDetails(
	ctx context.Context,
	acctId string,
	orderId string,
	paymentDetails OrderPaymentDetailsRequest,
) error {
	return nil
}

func (m *AcctSvcMock) GetAccountMainRegion(context.Context, string) (uint16, string, error) {
	return 1, "CA", nil
}

func (m *AcctSvcMock) GetOrders(
	ctx context.Context,
	acctId string,
	filters map[string]bool,
) ([]Order, error) {
	if m.ExpectError {
		return nil, m.MockError
	}
	return m.GetOrdersReturn, nil
}

func (m *AcctSvcMock) CreateOrderIntent(
	ctx context.Context,
	orderIntent NewOrderIntent,
) (CreateOrderIntentResponse, error) {
	return CreateOrderIntentResponse{}, nil
}

func (m *AcctSvcMock) UpdateOrderIntent(
	ctx context.Context,
	updateOrderIntent UpdateOrderIntent,
) error {
	return nil
}

func (m *AcctSvcMock) GetOrderById(
	ctx context.Context,
	acctId string,
	orderId string,
) (OrderDetails, error) {
	if m.ExpectError {
		return OrderDetails{}, m.MockError
	}
	return OrderDetails{}, nil
}

func (m *AcctSvcMock) GetOrderPlanId(
	ctx context.Context,
	acctId string,
	orderId string,
) uint64 {
	return 0
}

func (m *AcctSvcMock) GetInactiveOrderPlanId(
	ctx context.Context,
	acctId string,
) uint64 {
	return 0
}

func (m *AcctSvcMock) LockAccountWithToken(ctx context.Context, lockToken string) error {
	if m.ExpectError {
		return m.MockError
	}
	return nil
}

func (m *AcctSvcMock) CreateOrderActionReason(
	ctx context.Context,
	acctId string,
	orderId string,
	or OrderActionReason,
) error {
	if m.ExpectError {
		return m.MockError
	}
	return nil
}

func (m *AcctSvcMock) AddPatient(
	ctx context.Context,
	acctId string,
	rb Patient,
) (string, error) {
	if m.ExpectError {
		return "", m.MockError
	}
	return ksuid.New().String(), nil
}

func (m *AcctSvcMock) UpdatePatient(
	ctx context.Context,
	acctId string,
	patientId string,
	rb Patient,
) error {
	m.UpdatePatientCalled = true
	if m.ExpectError {
		return m.MockError
	}
	return m.UpdatePatientReturn
}

func (m *AcctSvcMock) DeletePatient(
	ctx context.Context,
	acctId string,
	patientId string,
) error {
	if m.ExpectError {
		return m.MockError
	}
	return m.DeletePatientReturn
}

func (m *AcctSvcMock) GetPatient(
	ctx context.Context,
	acctId string,
	patientId string,
) (Patient, error) {
	if m.ExpectError {
		return Patient{}, m.MockError
	}
	return m.GetPatientReturn, nil
}

func (m *AcctSvcMock) GetPatients(
	ctx context.Context,
	acctId string,
) ([]Patient, error) {
	if m.ExpectError {
		return nil, m.MockError
	}
	return m.GetPatientsReturn, nil
}

func (m *AcctSvcMock) CreatePhysicianAccount(
	ctx context.Context,
	newAcct PhysicianRegisterData,
) (string, error) {
	return ksuid.New().String(), m.MockError
}

func (m *AcctSvcMock) GetPhysicianAccount(
	ctx context.Context,
	accountId string,
) (PhysicianAccount, error) {
	return m.GetPhysicianAccountReturn, nil
}

func (m *AcctSvcMock) PatchPhysician(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianRequest,
) (PhysicianVerificationData, error) {
	return PhysicianVerificationData{}, m.MockError
}

func (m *AcctSvcMock) PostPhysicianLicense(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianLicenceRequest,
) error {
	return nil
}

func (m *AcctSvcMock) PostVerifyPhysicianNotificationMethod(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianNotificationRequest,
) error {
	return nil
}

func (m *AcctSvcMock) SearchPhysicianAccount(
	ctx context.Context,
	request PhysicianNotificationRequest,
) ([]string, error) {
	m.SearchPhysicianAccountCalled = true
	return m.phyAcctIds, nil
}

func (m *AcctSvcMock) GetAllPhysicianPermissions(
	ctx context.Context,
	accountId string,
) (map[int64][]PhysicianAccountPermission, error) {
	return m.GetPhysicianPermissionsByProviderIdAndPermissionName(ctx, accountId, 0, "")
}

func (m *AcctSvcMock) GetPhysicianPermissionsByPermissionName(
	ctx context.Context,
	accountID string,
	permissionName string,
) (map[int64][]PhysicianAccountPermission, error) {
	return m.GetPhysicianPermissionsByProviderIdAndPermissionName(ctx, accountID, 0, permissionName)
}

func (m *AcctSvcMock) GetPhysicianPermissionsByProviderIdAndPermissionName(
	ctx context.Context,
	accountId string,
	permissionId int64,
	permissionName string,
) (map[int64][]PhysicianAccountPermission, error) {
	return m.GetPhysicianPermissionsByProviderIdAndPermissionNameReturn, m.MockError
}

func (m *AcctSvcMock) IsSSOEnabled(
	ctx context.Context,
	acctId string,
) bool {
	return false
}

func (m *AcctSvcMock) GetOrderForId(ctx context.Context, orderId string) (Order, error) {
	if orderId == "" {
		return Order{}, fmt.Errorf("error querying acctsvc")
	}
	return Order{OrderId: orderId}, nil
}

func (m *AcctSvcMock) PostAccountVerificationCode(
	ctx context.Context,
	accountId string,
) (string, error) {
	return "", nil
}

func (m *AcctSvcMock) GetAccountVerificationCode(
	ctx context.Context,
	token string,
) (VerificationCodeResponse, error) {
	return VerificationCodeResponse{}, nil
}

func (m *AcctSvcMock) GetAccountEmailVerification(ctx context.Context, token string) (EmailVerification, error) {
	return EmailVerification{}, nil
}

func (m *AcctSvcMock) PatchOrder(
	ctx context.Context,
	accountID string,
	orderID string,
	por PatchOrderRequest,
) error {
	return nil
}

func (m *AcctSvcMock) AccountHasAccessToPatient(
	ctx context.Context,
	accountId string,
	orderId string,
) bool {
	return m.AccountHasAccessToPatientReturn
}

func (m *AcctSvcMock) GetAllPatientProfileValidationsForAccount(ctx context.Context, accountId string) (map[string]PatientProfileValidation, error) {
	return m.GetAllPatientProfileValidationsForAccountReturn, nil
}

func (m *AcctSvcMock) GetAccountPatientProfileValidation(ctx context.Context, accountId, patientId string) (PatientProfileValidation, error) {
	return m.GetAllPatientProfileValidationsReturn, nil
}

func (m *AcctSvcMock) GetOrCreatePatient(
	ctx context.Context,
	acctId string,
	pt Patient,
	createOnDup bool,
) (string, error) {
	if m.ExpectError {
		return "", m.MockError
	}
	if m.GetOrCreatePatientReturn != "" {
		return m.GetOrCreatePatientReturn, nil
	}
	return ksuid.New().String(), nil
}
