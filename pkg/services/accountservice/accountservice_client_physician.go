package accountservice

import (
	"context"
	"crypto/sha1" // #nosec G505
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

// Create a new physician account in as
func (as *AccountServiceClient) CreatePhysicianAccount(
	ctx context.Context,
	newAcct PhysicianRegisterData,
) (string, error) {
	if newAcct.MainRegion == 0 {
		//default to current core's region
		newAcct.MainRegion = regions.GetRegionID()
	}
	emailHash := fmt.Sprintf("%x", sha1.Sum([]byte(newAcct.Email))) // #nosec G401

	reqBodyJson, err := json.Marshal(newAcct)
	if err != nil {
		return "", err
	}

	endpoint := fmt.Sprintf("%s/v1/physician_accounts", as.AccountServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusBadRequest, http.StatusNotAcceptable},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return "", err
	}

	switch status {
	case http.StatusConflict:
		return "", ErrNewAcctConflict{
			EmailHash: emailHash,
		}
	case http.StatusBadRequest:
		return "", ErrBadRequest{
			EmailHash: emailHash,
		}
	case http.StatusNotAcceptable:
		// acctsvc returns the unfulfilled requirement in the response body,
		// so read and return it.
		body, err := io.ReadAll(resp)
		if err != nil {
			return "", ErrBadNewPassword{}
		}

		var errResp ErrorResponse
		err = json.Unmarshal(body, &errResp)
		if err != nil {
			return "", ErrBadNewPassword{}
		}
		return "", ErrBadNewPassword{MissingRequirement: errResp.Message}
	case http.StatusOK:
		var acctId string
		body, err := io.ReadAll(resp)
		if err != nil {
			return acctId, err
		}

		err = json.Unmarshal(body, &acctId)
		if err != nil {
			return acctId, err
		}
		return acctId, nil
	default:
		return "", fmt.Errorf("got %v back from acct svc", status)
	}
}

// Create a new physician account in as
func (as *AccountServiceClient) GetPhysicianAccount(
	ctx context.Context,
	accountId string,
) (PhysicianAccount, error) {
	var physicianAccount PhysicianAccount

	endpoint := fmt.Sprintf("%s/v1/physician_accounts/%s", as.AccountServiceUrl, accountId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusBadRequest},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return physicianAccount, err
	}

	switch status {
	case http.StatusNotFound:
		return physicianAccount, ErrAccountNotFound{AccountID: accountId}
	case http.StatusBadRequest:
		return physicianAccount, ErrBadRequest{}
	case http.StatusOK:
		body, err := io.ReadAll(resp)
		if err != nil {
			return physicianAccount, err
		}

		err = json.Unmarshal(body, &physicianAccount)
		if err != nil {
			return physicianAccount, err
		}

		return physicianAccount, nil
	default:
		return physicianAccount, fmt.Errorf("got %v back from acct svc", status)
	}
}

// update physician in physician account
func (as *AccountServiceClient) PatchPhysician(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianRequest,
) (PhysicianVerificationData, error) {
	var verificationData PhysicianVerificationData

	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		return verificationData, err
	}

	endpoint := fmt.Sprintf(
		"%s/v1/physician_accounts/%s/physicians/%s",
		as.AccountServiceUrl,
		accountId,
		physicianId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod: "PATCH",
		TargetURL:  endpoint,
		ReqBody:    reqBodyJson,
		AuthScheme: httpclient.Basic,
		AuthKey:    as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusNotFound,
			http.StatusBadRequest,
			http.StatusInternalServerError,
		},
	}

	resp, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return verificationData, err
	}

	switch status {
	case http.StatusNotFound:
		return verificationData, ErrAccountNotFound{AccountID: accountId}
	case http.StatusBadRequest:
		return verificationData, ErrBadRequest{}
	case http.StatusOK:
		var verificationData PhysicianVerificationData
		body, err := io.ReadAll(resp)
		if err != nil {
			return verificationData, err
		}

		err = json.Unmarshal(body, &verificationData)
		if err != nil {
			return verificationData, err
		}
		if !verificationData.IsValid() {
			return verificationData, errors.New("validation for physician verification data failed")
		}
		return verificationData, nil
	default:
		return verificationData, fmt.Errorf("got %v back from acct svc", status)
	}
}

// add a new physician license to a physician in a physician account
func (as *AccountServiceClient) PostPhysicianLicense(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianLicenceRequest,
) error {
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		return err
	}

	endpoint := fmt.Sprintf(
		"%s/v1/physician_accounts/%s/physicians/%s/licenses",
		as.AccountServiceUrl,
		accountId,
		physicianId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusBadRequest},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}

	switch status {
	case http.StatusNotFound:
		return ErrAccountNotFound{AccountID: accountId}
	case http.StatusBadRequest:
		return ErrBadRequest{}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

// add a new physician license to a physician in a physician account
func (as *AccountServiceClient) PostVerifyPhysicianNotificationMethod(
	ctx context.Context,
	accountId string,
	physicianId string,
	request PhysicianNotificationRequest,
) error {
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		return err
	}

	endpoint := fmt.Sprintf(
		"%s/v1/physician_accounts/%s/physicians/%s/verify",
		as.AccountServiceUrl,
		accountId,
		physicianId,
	)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusConflict, http.StatusBadRequest},
	}

	_, status, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return err
	}

	switch status {
	case http.StatusNotFound:
		return ErrAccountNotFound{AccountID: accountId}
	case http.StatusBadRequest:
		return ErrBadRequest{}
	case http.StatusOK:
		return nil
	default:
		return fmt.Errorf("got %v back from acct svc", status)
	}
}

func (as AccountServiceClient) SearchPhysicianAccount(
	ctx context.Context,
	request PhysicianNotificationRequest,
) (phyAccountIds []string, err error) {
	endpoint := fmt.Sprintf(
		"%s/v1/physician_accounts/search",
		as.AccountServiceUrl,
	)
	reqBodyJson, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		ReqBody:       reqBodyJson,
		AuthScheme:    httpclient.Basic,
		AuthKey:       as.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusBadRequest, http.StatusNotFound},
	}
	respBody, statusCode, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return nil, err
	}
	if statusCode != http.StatusOK {
		return nil, errors.New("physician account search failed")
	}

	gotBody, err := io.ReadAll(respBody)
	if err != nil {
		return phyAccountIds, err
	}
	err = json.Unmarshal(gotBody, &phyAccountIds)
	if err != nil {
		return phyAccountIds, err
	}
	return phyAccountIds, nil
}

// GetAllPhysicianPermissions returns a map of all physician permissions for a physician with provider Id as key
func (as AccountServiceClient) GetAllPhysicianPermissions(
	ctx context.Context,
	accountId string,
) (permissionMap map[int64][]PhysicianAccountPermission, err error) {
	return as.GetPhysicianPermissionsByProviderIdAndPermissionName(ctx, accountId, 0, "")
}

// GetPhysicianPermissionsByPermissionName returns a map of all physician permissions
// with a given permission name for a physician with the givne accountId.
// The keys of the map are ids of providers.
func (as AccountServiceClient) GetPhysicianPermissionsByPermissionName(
	ctx context.Context,
	accountID string,
	permissionName string,
) (permissionMap map[int64][]PhysicianAccountPermission, err error) {
	return as.GetPhysicianPermissionsByProviderIdAndPermissionName(
		ctx,
		accountID,
		0,
		permissionName,
	)
}

// GetPhysicianPermissionsByProviderIdAndPermissionName returns a map of physician permissions with provider Id as key
//
// Map can be filtered by specific provider id or permission name
// filters will only be applied if providerId is not 0 / permissionName is not ""
func (as AccountServiceClient) GetPhysicianPermissionsByProviderIdAndPermissionName(
	ctx context.Context,
	accountId string,
	providerId int64,
	permissionName string,
) (permissionMap map[int64][]PhysicianAccountPermission, err error) {
	endpoint := fmt.Sprintf(
		"%s/v1/physician_accounts/%s/permissions",
		as.AccountServiceUrl,
		accountId,
	)

	queryParams := map[string]string{}
	if providerId != 0 {
		queryParams["providerId"] = strconv.FormatInt(providerId, 10)
	}
	if permissionName != "" {
		queryParams["permissionName"] = permissionName
	}

	reqParams := httpclient.RequestParameters{
		HTTPMethod:  "GET",
		TargetURL:   endpoint,
		QueryParams: queryParams,
		AuthScheme:  httpclient.Basic,
		AuthKey:     as.setupBasicAuth(),
		ExpectedCodes: []int{
			http.StatusBadRequest,
			http.StatusNotFound,
			http.StatusUnauthorized,
			http.StatusInternalServerError,
		},
	}
	respBody, statusCode, err := as.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		return map[int64][]PhysicianAccountPermission{}, err
	}

	switch statusCode {
	case http.StatusBadRequest:
		return map[int64][]PhysicianAccountPermission{}, errors.New(errormsgs.ERR_INVALID_REQ_BODY)
	case http.StatusNotFound:
		return map[int64][]PhysicianAccountPermission{}, errors.New(errormsgs.ERR_NOT_FOUND)
	case http.StatusUnauthorized:
		return map[int64][]PhysicianAccountPermission{}, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	case http.StatusOK:
		var permissionMap map[int64][]PhysicianAccountPermission
		body, err := io.ReadAll(respBody)
		if err != nil {
			return map[int64][]PhysicianAccountPermission{}, err
		}
		err = json.Unmarshal(body, &permissionMap)
		if err != nil {
			return map[int64][]PhysicianAccountPermission{}, err
		}
		return permissionMap, nil
	default:
		return map[int64][]PhysicianAccountPermission{}, fmt.Errorf(
			"got %v back from acctsvc",
			statusCode,
		)
	}
}
