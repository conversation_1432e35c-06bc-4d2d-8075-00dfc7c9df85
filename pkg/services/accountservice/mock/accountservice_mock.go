// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/accountservice/accountservice.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/accountservice/accountservice.go -destination=pkg/services/accountservice/mock/accountservice_mock.go -package=mock_accountservice
//

// Package mock_accountservice is a generated GoMock package.
package mock_accountservice

import (
	context "context"
	reflect "reflect"
	time "time"

	models "gitlab.com/pockethealth/coreapi/pkg/models"
	accountservice "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	gomock "go.uber.org/mock/gomock"
)

// MockAccountService is a mock of AccountService interface.
type MockAccountService struct {
	ctrl     *gomock.Controller
	recorder *MockAccountServiceMockRecorder
	isgomock struct{}
}

// MockAccountServiceMockRecorder is the mock recorder for MockAccountService.
type MockAccountServiceMockRecorder struct {
	mock *MockAccountService
}

// NewMockAccountService creates a new mock instance.
func NewMockAccountService(ctrl *gomock.Controller) *MockAccountService {
	mock := &MockAccountService{ctrl: ctrl}
	mock.recorder = &MockAccountServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountService) EXPECT() *MockAccountServiceMockRecorder {
	return m.recorder
}

// AccountHasAccessToPatient mocks base method.
func (m *MockAccountService) AccountHasAccessToPatient(ctx context.Context, accountId, patientId string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccountHasAccessToPatient", ctx, accountId, patientId)
	ret0, _ := ret[0].(bool)
	return ret0
}

// AccountHasAccessToPatient indicates an expected call of AccountHasAccessToPatient.
func (mr *MockAccountServiceMockRecorder) AccountHasAccessToPatient(ctx, accountId, patientId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountHasAccessToPatient", reflect.TypeOf((*MockAccountService)(nil).AccountHasAccessToPatient), ctx, accountId, patientId)
}

// AccountLoginGoogle mocks base method.
func (m *MockAccountService) AccountLoginGoogle(ctx context.Context, jwt, ip string) (accountservice.GoogleSSOLoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccountLoginGoogle", ctx, jwt, ip)
	ret0, _ := ret[0].(accountservice.GoogleSSOLoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AccountLoginGoogle indicates an expected call of AccountLoginGoogle.
func (mr *MockAccountServiceMockRecorder) AccountLoginGoogle(ctx, jwt, ip any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountLoginGoogle", reflect.TypeOf((*MockAccountService)(nil).AccountLoginGoogle), ctx, jwt, ip)
}

// AccountLoginSSO mocks base method.
func (m *MockAccountService) AccountLoginSSO(ctx context.Context, ssoToken, ip string) (accountservice.AccountSSOLoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccountLoginSSO", ctx, ssoToken, ip)
	ret0, _ := ret[0].(accountservice.AccountSSOLoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AccountLoginSSO indicates an expected call of AccountLoginSSO.
func (mr *MockAccountServiceMockRecorder) AccountLoginSSO(ctx, ssoToken, ip any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountLoginSSO", reflect.TypeOf((*MockAccountService)(nil).AccountLoginSSO), ctx, ssoToken, ip)
}

// AccountLoginViaSSO mocks base method.
func (m *MockAccountService) AccountLoginViaSSO(ctx context.Context, token, accountId, ipAddress string) (accountservice.AccountSSOLoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccountLoginViaSSO", ctx, token, accountId, ipAddress)
	ret0, _ := ret[0].(accountservice.AccountSSOLoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AccountLoginViaSSO indicates an expected call of AccountLoginViaSSO.
func (mr *MockAccountServiceMockRecorder) AccountLoginViaSSO(ctx, token, accountId, ipAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountLoginViaSSO", reflect.TypeOf((*MockAccountService)(nil).AccountLoginViaSSO), ctx, token, accountId, ipAddress)
}

// AddNewRegion mocks base method.
func (m *MockAccountService) AddNewRegion(ctx context.Context, regionID uint16, acctID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNewRegion", ctx, regionID, acctID)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNewRegion indicates an expected call of AddNewRegion.
func (mr *MockAccountServiceMockRecorder) AddNewRegion(ctx, regionID, acctID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNewRegion", reflect.TypeOf((*MockAccountService)(nil).AddNewRegion), ctx, regionID, acctID)
}

// AddPatient mocks base method.
func (m *MockAccountService) AddPatient(ctx context.Context, acctId string, rB accountservice.Patient) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPatient", ctx, acctId, rB)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPatient indicates an expected call of AddPatient.
func (mr *MockAccountServiceMockRecorder) AddPatient(ctx, acctId, rB any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPatient", reflect.TypeOf((*MockAccountService)(nil).AddPatient), ctx, acctId, rB)
}

// AuthenticateAcctAndGetToken mocks base method.
func (m *MockAccountService) AuthenticateAcctAndGetToken(ctx context.Context, accountType accountservice.AccountTypeEndpoint, email, password, ip string) (accountservice.LoginSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthenticateAcctAndGetToken", ctx, accountType, email, password, ip)
	ret0, _ := ret[0].(accountservice.LoginSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthenticateAcctAndGetToken indicates an expected call of AuthenticateAcctAndGetToken.
func (mr *MockAccountServiceMockRecorder) AuthenticateAcctAndGetToken(ctx, accountType, email, password, ip any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthenticateAcctAndGetToken", reflect.TypeOf((*MockAccountService)(nil).AuthenticateAcctAndGetToken), ctx, accountType, email, password, ip)
}

// CancelSubscription mocks base method.
func (m *MockAccountService) CancelSubscription(ctx context.Context, orderId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelSubscription", ctx, orderId)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelSubscription indicates an expected call of CancelSubscription.
func (mr *MockAccountServiceMockRecorder) CancelSubscription(ctx, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelSubscription", reflect.TypeOf((*MockAccountService)(nil).CancelSubscription), ctx, orderId)
}

// CreateAccountAndSendVerifyEmail mocks base method.
func (m *MockAccountService) CreateAccountAndSendVerifyEmail(ctx context.Context, newAcct accountservice.NewAccount) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAccountAndSendVerifyEmail", ctx, newAcct)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAccountAndSendVerifyEmail indicates an expected call of CreateAccountAndSendVerifyEmail.
func (mr *MockAccountServiceMockRecorder) CreateAccountAndSendVerifyEmail(ctx, newAcct any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccountAndSendVerifyEmail", reflect.TypeOf((*MockAccountService)(nil).CreateAccountAndSendVerifyEmail), ctx, newAcct)
}

// CreateOrder mocks base method.
func (m *MockAccountService) CreateOrder(ctx context.Context, acctId string, newOrder accountservice.NewOrder) (accountservice.CreateOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", ctx, acctId, newOrder)
	ret0, _ := ret[0].(accountservice.CreateOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockAccountServiceMockRecorder) CreateOrder(ctx, acctId, newOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockAccountService)(nil).CreateOrder), ctx, acctId, newOrder)
}

// CreateOrderActionReason mocks base method.
func (m *MockAccountService) CreateOrderActionReason(ctx context.Context, acctId, orderId string, or accountservice.OrderActionReason) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrderActionReason", ctx, acctId, orderId, or)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrderActionReason indicates an expected call of CreateOrderActionReason.
func (mr *MockAccountServiceMockRecorder) CreateOrderActionReason(ctx, acctId, orderId, or any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderActionReason", reflect.TypeOf((*MockAccountService)(nil).CreateOrderActionReason), ctx, acctId, orderId, or)
}

// CreateOrderIntent mocks base method.
func (m *MockAccountService) CreateOrderIntent(ctx context.Context, orderIntent accountservice.NewOrderIntent) (accountservice.CreateOrderIntentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrderIntent", ctx, orderIntent)
	ret0, _ := ret[0].(accountservice.CreateOrderIntentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrderIntent indicates an expected call of CreateOrderIntent.
func (mr *MockAccountServiceMockRecorder) CreateOrderIntent(ctx, orderIntent any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderIntent", reflect.TypeOf((*MockAccountService)(nil).CreateOrderIntent), ctx, orderIntent)
}

// CreateOrderWithoutEmail mocks base method.
func (m *MockAccountService) CreateOrderWithoutEmail(ctx context.Context, acctId string, newOrder accountservice.NewOrder) (accountservice.CreateOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrderWithoutEmail", ctx, acctId, newOrder)
	ret0, _ := ret[0].(accountservice.CreateOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrderWithoutEmail indicates an expected call of CreateOrderWithoutEmail.
func (mr *MockAccountServiceMockRecorder) CreateOrderWithoutEmail(ctx, acctId, newOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderWithoutEmail", reflect.TypeOf((*MockAccountService)(nil).CreateOrderWithoutEmail), ctx, acctId, newOrder)
}

// CreatePhysicianAccount mocks base method.
func (m *MockAccountService) CreatePhysicianAccount(ctx context.Context, newAcct accountservice.PhysicianRegisterData) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePhysicianAccount", ctx, newAcct)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePhysicianAccount indicates an expected call of CreatePhysicianAccount.
func (mr *MockAccountServiceMockRecorder) CreatePhysicianAccount(ctx, newAcct any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePhysicianAccount", reflect.TypeOf((*MockAccountService)(nil).CreatePhysicianAccount), ctx, newAcct)
}

// DeactivateAccount mocks base method.
func (m *MockAccountService) DeactivateAccount(ctx context.Context, accountId, token string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateAccount", ctx, accountId, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateAccount indicates an expected call of DeactivateAccount.
func (mr *MockAccountServiceMockRecorder) DeactivateAccount(ctx, accountId, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateAccount", reflect.TypeOf((*MockAccountService)(nil).DeactivateAccount), ctx, accountId, token)
}

// DeleteAccount mocks base method.
func (m *MockAccountService) DeleteAccount(ctx context.Context, acctId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAccount", ctx, acctId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAccount indicates an expected call of DeleteAccount.
func (mr *MockAccountServiceMockRecorder) DeleteAccount(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAccount", reflect.TypeOf((*MockAccountService)(nil).DeleteAccount), ctx, acctId)
}

// DeleteOrder mocks base method.
func (m *MockAccountService) DeleteOrder(ctx context.Context, acctId, orderId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOrder", ctx, acctId, orderId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOrder indicates an expected call of DeleteOrder.
func (mr *MockAccountServiceMockRecorder) DeleteOrder(ctx, acctId, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrder", reflect.TypeOf((*MockAccountService)(nil).DeleteOrder), ctx, acctId, orderId)
}

// DeletePatient mocks base method.
func (m *MockAccountService) DeletePatient(ctx context.Context, acctId, patientId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePatient", ctx, acctId, patientId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePatient indicates an expected call of DeletePatient.
func (mr *MockAccountServiceMockRecorder) DeletePatient(ctx, acctId, patientId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePatient", reflect.TypeOf((*MockAccountService)(nil).DeletePatient), ctx, acctId, patientId)
}

// GenerateAccountLoginSSOToken mocks base method.
func (m *MockAccountService) GenerateAccountLoginSSOToken(ctx context.Context, accountId string, expiresAt time.Time) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateAccountLoginSSOToken", ctx, accountId, expiresAt)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAccountLoginSSOToken indicates an expected call of GenerateAccountLoginSSOToken.
func (mr *MockAccountServiceMockRecorder) GenerateAccountLoginSSOToken(ctx, accountId, expiresAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAccountLoginSSOToken", reflect.TypeOf((*MockAccountService)(nil).GenerateAccountLoginSSOToken), ctx, accountId, expiresAt)
}

// GetAccountEmailVerification mocks base method.
func (m *MockAccountService) GetAccountEmailVerification(ctx context.Context, token string) (accountservice.EmailVerification, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountEmailVerification", ctx, token)
	ret0, _ := ret[0].(accountservice.EmailVerification)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountEmailVerification indicates an expected call of GetAccountEmailVerification.
func (mr *MockAccountServiceMockRecorder) GetAccountEmailVerification(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountEmailVerification", reflect.TypeOf((*MockAccountService)(nil).GetAccountEmailVerification), ctx, token)
}

// GetAccountInfo mocks base method.
func (m *MockAccountService) GetAccountInfo(ctx context.Context, acctId string) (*accountservice.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountInfo", ctx, acctId)
	ret0, _ := ret[0].(*accountservice.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountInfo indicates an expected call of GetAccountInfo.
func (mr *MockAccountServiceMockRecorder) GetAccountInfo(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountInfo", reflect.TypeOf((*MockAccountService)(nil).GetAccountInfo), ctx, acctId)
}

// GetAccountInfoByEmail mocks base method.
func (m *MockAccountService) GetAccountInfoByEmail(ctx context.Context, email string) (*accountservice.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountInfoByEmail", ctx, email)
	ret0, _ := ret[0].(*accountservice.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountInfoByEmail indicates an expected call of GetAccountInfoByEmail.
func (mr *MockAccountServiceMockRecorder) GetAccountInfoByEmail(ctx, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountInfoByEmail", reflect.TypeOf((*MockAccountService)(nil).GetAccountInfoByEmail), ctx, email)
}

// GetAccountMainRegion mocks base method.
func (m *MockAccountService) GetAccountMainRegion(ctx context.Context, acctId string) (uint16, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountMainRegion", ctx, acctId)
	ret0, _ := ret[0].(uint16)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAccountMainRegion indicates an expected call of GetAccountMainRegion.
func (mr *MockAccountServiceMockRecorder) GetAccountMainRegion(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountMainRegion", reflect.TypeOf((*MockAccountService)(nil).GetAccountMainRegion), ctx, acctId)
}

// GetAccountPatientProfileValidation mocks base method.
func (m *MockAccountService) GetAccountPatientProfileValidation(ctx context.Context, accountId, patientId string) (accountservice.PatientProfileValidation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountPatientProfileValidation", ctx, accountId, patientId)
	ret0, _ := ret[0].(accountservice.PatientProfileValidation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountPatientProfileValidation indicates an expected call of GetAccountPatientProfileValidation.
func (mr *MockAccountServiceMockRecorder) GetAccountPatientProfileValidation(ctx, accountId, patientId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountPatientProfileValidation", reflect.TypeOf((*MockAccountService)(nil).GetAccountPatientProfileValidation), ctx, accountId, patientId)
}

// GetAccountSettings mocks base method.
func (m *MockAccountService) GetAccountSettings(ctx context.Context, acctId string) (*accountservice.UserSettings, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountSettings", ctx, acctId)
	ret0, _ := ret[0].(*accountservice.UserSettings)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountSettings indicates an expected call of GetAccountSettings.
func (mr *MockAccountServiceMockRecorder) GetAccountSettings(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountSettings", reflect.TypeOf((*MockAccountService)(nil).GetAccountSettings), ctx, acctId)
}

// GetAccountVerificationCode mocks base method.
func (m *MockAccountService) GetAccountVerificationCode(ctx context.Context, token string) (accountservice.VerificationCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountVerificationCode", ctx, token)
	ret0, _ := ret[0].(accountservice.VerificationCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountVerificationCode indicates an expected call of GetAccountVerificationCode.
func (mr *MockAccountServiceMockRecorder) GetAccountVerificationCode(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountVerificationCode", reflect.TypeOf((*MockAccountService)(nil).GetAccountVerificationCode), ctx, token)
}

// GetAllPatientProfileValidationsForAccount mocks base method.
func (m *MockAccountService) GetAllPatientProfileValidationsForAccount(ctx context.Context, accountId string) (map[string]accountservice.PatientProfileValidation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPatientProfileValidationsForAccount", ctx, accountId)
	ret0, _ := ret[0].(map[string]accountservice.PatientProfileValidation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPatientProfileValidationsForAccount indicates an expected call of GetAllPatientProfileValidationsForAccount.
func (mr *MockAccountServiceMockRecorder) GetAllPatientProfileValidationsForAccount(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPatientProfileValidationsForAccount", reflect.TypeOf((*MockAccountService)(nil).GetAllPatientProfileValidationsForAccount), ctx, accountId)
}

// GetAllPhysicianPermissions mocks base method.
func (m *MockAccountService) GetAllPhysicianPermissions(ctx context.Context, accountId string) (map[int64][]accountservice.PhysicianAccountPermission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPhysicianPermissions", ctx, accountId)
	ret0, _ := ret[0].(map[int64][]accountservice.PhysicianAccountPermission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPhysicianPermissions indicates an expected call of GetAllPhysicianPermissions.
func (mr *MockAccountServiceMockRecorder) GetAllPhysicianPermissions(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPhysicianPermissions", reflect.TypeOf((*MockAccountService)(nil).GetAllPhysicianPermissions), ctx, accountId)
}

// GetInactiveOrderPlanId mocks base method.
func (m *MockAccountService) GetInactiveOrderPlanId(ctx context.Context, acctId string) uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInactiveOrderPlanId", ctx, acctId)
	ret0, _ := ret[0].(uint64)
	return ret0
}

// GetInactiveOrderPlanId indicates an expected call of GetInactiveOrderPlanId.
func (mr *MockAccountServiceMockRecorder) GetInactiveOrderPlanId(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInactiveOrderPlanId", reflect.TypeOf((*MockAccountService)(nil).GetInactiveOrderPlanId), ctx, acctId)
}

// GetOrCreatePatient mocks base method.
func (m *MockAccountService) GetOrCreatePatient(ctx context.Context, acctId string, pt accountservice.Patient, createOnConflict bool) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreatePatient", ctx, acctId, pt, createOnConflict)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreatePatient indicates an expected call of GetOrCreatePatient.
func (mr *MockAccountServiceMockRecorder) GetOrCreatePatient(ctx, acctId, pt, createOnConflict any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreatePatient", reflect.TypeOf((*MockAccountService)(nil).GetOrCreatePatient), ctx, acctId, pt, createOnConflict)
}

// GetOrderById mocks base method.
func (m *MockAccountService) GetOrderById(ctx context.Context, acctId, orderId string) (accountservice.OrderDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderById", ctx, acctId, orderId)
	ret0, _ := ret[0].(accountservice.OrderDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderById indicates an expected call of GetOrderById.
func (mr *MockAccountServiceMockRecorder) GetOrderById(ctx, acctId, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderById", reflect.TypeOf((*MockAccountService)(nil).GetOrderById), ctx, acctId, orderId)
}

// GetOrderForId mocks base method.
func (m *MockAccountService) GetOrderForId(ctx context.Context, orderId string) (accountservice.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderForId", ctx, orderId)
	ret0, _ := ret[0].(accountservice.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderForId indicates an expected call of GetOrderForId.
func (mr *MockAccountServiceMockRecorder) GetOrderForId(ctx, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderForId", reflect.TypeOf((*MockAccountService)(nil).GetOrderForId), ctx, orderId)
}

// GetOrderPlanId mocks base method.
func (m *MockAccountService) GetOrderPlanId(ctx context.Context, acctId, orderId string) uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderPlanId", ctx, acctId, orderId)
	ret0, _ := ret[0].(uint64)
	return ret0
}

// GetOrderPlanId indicates an expected call of GetOrderPlanId.
func (mr *MockAccountServiceMockRecorder) GetOrderPlanId(ctx, acctId, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderPlanId", reflect.TypeOf((*MockAccountService)(nil).GetOrderPlanId), ctx, acctId, orderId)
}

// GetOrders mocks base method.
func (m *MockAccountService) GetOrders(ctx context.Context, acctId string, filters map[string]bool) ([]accountservice.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrders", ctx, acctId, filters)
	ret0, _ := ret[0].([]accountservice.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrders indicates an expected call of GetOrders.
func (mr *MockAccountServiceMockRecorder) GetOrders(ctx, acctId, filters any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrders", reflect.TypeOf((*MockAccountService)(nil).GetOrders), ctx, acctId, filters)
}

// GetPasswordSetupVerificationToken mocks base method.
func (m *MockAccountService) GetPasswordSetupVerificationToken(ctx context.Context, accountId string) (accountservice.VerificationToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPasswordSetupVerificationToken", ctx, accountId)
	ret0, _ := ret[0].(accountservice.VerificationToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPasswordSetupVerificationToken indicates an expected call of GetPasswordSetupVerificationToken.
func (mr *MockAccountServiceMockRecorder) GetPasswordSetupVerificationToken(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPasswordSetupVerificationToken", reflect.TypeOf((*MockAccountService)(nil).GetPasswordSetupVerificationToken), ctx, accountId)
}

// GetPatient mocks base method.
func (m *MockAccountService) GetPatient(ctx context.Context, acctId, patientId string) (accountservice.Patient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPatient", ctx, acctId, patientId)
	ret0, _ := ret[0].(accountservice.Patient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPatient indicates an expected call of GetPatient.
func (mr *MockAccountServiceMockRecorder) GetPatient(ctx, acctId, patientId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPatient", reflect.TypeOf((*MockAccountService)(nil).GetPatient), ctx, acctId, patientId)
}

// GetPatients mocks base method.
func (m *MockAccountService) GetPatients(ctx context.Context, acctId string) ([]accountservice.Patient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPatients", ctx, acctId)
	ret0, _ := ret[0].([]accountservice.Patient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPatients indicates an expected call of GetPatients.
func (mr *MockAccountServiceMockRecorder) GetPatients(ctx, acctId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPatients", reflect.TypeOf((*MockAccountService)(nil).GetPatients), ctx, acctId)
}

// GetPhysicianAccount mocks base method.
func (m *MockAccountService) GetPhysicianAccount(ctx context.Context, accountId string) (accountservice.PhysicianAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhysicianAccount", ctx, accountId)
	ret0, _ := ret[0].(accountservice.PhysicianAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicianAccount indicates an expected call of GetPhysicianAccount.
func (mr *MockAccountServiceMockRecorder) GetPhysicianAccount(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicianAccount", reflect.TypeOf((*MockAccountService)(nil).GetPhysicianAccount), ctx, accountId)
}

// GetPhysicianPermissionsByPermissionName mocks base method.
func (m *MockAccountService) GetPhysicianPermissionsByPermissionName(ctx context.Context, accountId, permissionName string) (map[int64][]accountservice.PhysicianAccountPermission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhysicianPermissionsByPermissionName", ctx, accountId, permissionName)
	ret0, _ := ret[0].(map[int64][]accountservice.PhysicianAccountPermission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicianPermissionsByPermissionName indicates an expected call of GetPhysicianPermissionsByPermissionName.
func (mr *MockAccountServiceMockRecorder) GetPhysicianPermissionsByPermissionName(ctx, accountId, permissionName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicianPermissionsByPermissionName", reflect.TypeOf((*MockAccountService)(nil).GetPhysicianPermissionsByPermissionName), ctx, accountId, permissionName)
}

// GetPhysicianPermissionsByProviderIdAndPermissionName mocks base method.
func (m *MockAccountService) GetPhysicianPermissionsByProviderIdAndPermissionName(ctx context.Context, accountId string, providerId int64, permissionName string) (map[int64][]accountservice.PhysicianAccountPermission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhysicianPermissionsByProviderIdAndPermissionName", ctx, accountId, providerId, permissionName)
	ret0, _ := ret[0].(map[int64][]accountservice.PhysicianAccountPermission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicianPermissionsByProviderIdAndPermissionName indicates an expected call of GetPhysicianPermissionsByProviderIdAndPermissionName.
func (mr *MockAccountServiceMockRecorder) GetPhysicianPermissionsByProviderIdAndPermissionName(ctx, accountId, providerId, permissionName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicianPermissionsByProviderIdAndPermissionName", reflect.TypeOf((*MockAccountService)(nil).GetPhysicianPermissionsByProviderIdAndPermissionName), ctx, accountId, providerId, permissionName)
}

// InitUpdateEmail mocks base method.
func (m *MockAccountService) InitUpdateEmail(ctx context.Context, oldEmail, newEmail string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitUpdateEmail", ctx, oldEmail, newEmail)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitUpdateEmail indicates an expected call of InitUpdateEmail.
func (mr *MockAccountServiceMockRecorder) InitUpdateEmail(ctx, oldEmail, newEmail any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitUpdateEmail", reflect.TypeOf((*MockAccountService)(nil).InitUpdateEmail), ctx, oldEmail, newEmail)
}

// IsSSOEnabled mocks base method.
func (m *MockAccountService) IsSSOEnabled(ctx context.Context, accountId string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsSSOEnabled", ctx, accountId)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsSSOEnabled indicates an expected call of IsSSOEnabled.
func (mr *MockAccountServiceMockRecorder) IsSSOEnabled(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSSOEnabled", reflect.TypeOf((*MockAccountService)(nil).IsSSOEnabled), ctx, accountId)
}

// LockAccount mocks base method.
func (m *MockAccountService) LockAccount(ctx context.Context, acctID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockAccount", ctx, acctID)
	ret0, _ := ret[0].(error)
	return ret0
}

// LockAccount indicates an expected call of LockAccount.
func (mr *MockAccountServiceMockRecorder) LockAccount(ctx, acctID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockAccount", reflect.TypeOf((*MockAccountService)(nil).LockAccount), ctx, acctID)
}

// LockAccountWithToken mocks base method.
func (m *MockAccountService) LockAccountWithToken(ctx context.Context, lockToken string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockAccountWithToken", ctx, lockToken)
	ret0, _ := ret[0].(error)
	return ret0
}

// LockAccountWithToken indicates an expected call of LockAccountWithToken.
func (mr *MockAccountServiceMockRecorder) LockAccountWithToken(ctx, lockToken any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockAccountWithToken", reflect.TypeOf((*MockAccountService)(nil).LockAccountWithToken), ctx, lockToken)
}

// LookupAccountIdByEmail mocks base method.
func (m *MockAccountService) LookupAccountIdByEmail(ctx context.Context, email string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LookupAccountIdByEmail", ctx, email)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LookupAccountIdByEmail indicates an expected call of LookupAccountIdByEmail.
func (mr *MockAccountServiceMockRecorder) LookupAccountIdByEmail(ctx, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LookupAccountIdByEmail", reflect.TypeOf((*MockAccountService)(nil).LookupAccountIdByEmail), ctx, email)
}

// LookupAccountIdByPatientId mocks base method.
func (m *MockAccountService) LookupAccountIdByPatientId(ctx context.Context, patientId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LookupAccountIdByPatientId", ctx, patientId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LookupAccountIdByPatientId indicates an expected call of LookupAccountIdByPatientId.
func (mr *MockAccountServiceMockRecorder) LookupAccountIdByPatientId(ctx, patientId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LookupAccountIdByPatientId", reflect.TypeOf((*MockAccountService)(nil).LookupAccountIdByPatientId), ctx, patientId)
}

// PatchOrder mocks base method.
func (m *MockAccountService) PatchOrder(ctx context.Context, accountID, orderID string, por accountservice.PatchOrderRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PatchOrder", ctx, accountID, orderID, por)
	ret0, _ := ret[0].(error)
	return ret0
}

// PatchOrder indicates an expected call of PatchOrder.
func (mr *MockAccountServiceMockRecorder) PatchOrder(ctx, accountID, orderID, por any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PatchOrder", reflect.TypeOf((*MockAccountService)(nil).PatchOrder), ctx, accountID, orderID, por)
}

// PatchPhysician mocks base method.
func (m *MockAccountService) PatchPhysician(ctx context.Context, accountId, physicianId string, request accountservice.PhysicianRequest) (accountservice.PhysicianVerificationData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PatchPhysician", ctx, accountId, physicianId, request)
	ret0, _ := ret[0].(accountservice.PhysicianVerificationData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PatchPhysician indicates an expected call of PatchPhysician.
func (mr *MockAccountServiceMockRecorder) PatchPhysician(ctx, accountId, physicianId, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PatchPhysician", reflect.TypeOf((*MockAccountService)(nil).PatchPhysician), ctx, accountId, physicianId, request)
}

// PostAccountVerificationCode mocks base method.
func (m *MockAccountService) PostAccountVerificationCode(ctx context.Context, accountId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostAccountVerificationCode", ctx, accountId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostAccountVerificationCode indicates an expected call of PostAccountVerificationCode.
func (mr *MockAccountServiceMockRecorder) PostAccountVerificationCode(ctx, accountId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostAccountVerificationCode", reflect.TypeOf((*MockAccountService)(nil).PostAccountVerificationCode), ctx, accountId)
}

// PostPhysicianLicense mocks base method.
func (m *MockAccountService) PostPhysicianLicense(ctx context.Context, accountId, physicianId string, request accountservice.PhysicianLicenceRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostPhysicianLicense", ctx, accountId, physicianId, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// PostPhysicianLicense indicates an expected call of PostPhysicianLicense.
func (mr *MockAccountServiceMockRecorder) PostPhysicianLicense(ctx, accountId, physicianId, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostPhysicianLicense", reflect.TypeOf((*MockAccountService)(nil).PostPhysicianLicense), ctx, accountId, physicianId, request)
}

// PostVerifyPhysicianNotificationMethod mocks base method.
func (m *MockAccountService) PostVerifyPhysicianNotificationMethod(ctx context.Context, accountId, physicianId string, request accountservice.PhysicianNotificationRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostVerifyPhysicianNotificationMethod", ctx, accountId, physicianId, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// PostVerifyPhysicianNotificationMethod indicates an expected call of PostVerifyPhysicianNotificationMethod.
func (mr *MockAccountServiceMockRecorder) PostVerifyPhysicianNotificationMethod(ctx, accountId, physicianId, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostVerifyPhysicianNotificationMethod", reflect.TypeOf((*MockAccountService)(nil).PostVerifyPhysicianNotificationMethod), ctx, accountId, physicianId, request)
}

// RefreshToken mocks base method.
func (m *MockAccountService) RefreshToken(ctx context.Context, refreshToken, ip string) (accountservice.LoginSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", ctx, refreshToken, ip)
	ret0, _ := ret[0].(accountservice.LoginSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockAccountServiceMockRecorder) RefreshToken(ctx, refreshToken, ip any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockAccountService)(nil).RefreshToken), ctx, refreshToken, ip)
}

// RefundOrder mocks base method.
func (m *MockAccountService) RefundOrder(ctx context.Context, acctId, orderId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefundOrder", ctx, acctId, orderId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefundOrder indicates an expected call of RefundOrder.
func (mr *MockAccountServiceMockRecorder) RefundOrder(ctx, acctId, orderId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefundOrder", reflect.TypeOf((*MockAccountService)(nil).RefundOrder), ctx, acctId, orderId)
}

// ResetPassword mocks base method.
func (m *MockAccountService) ResetPassword(ctx context.Context, accountTypeEndpoint accountservice.AccountTypeEndpoint, resetInfo accountservice.AccountEdit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetPassword", ctx, accountTypeEndpoint, resetInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetPassword indicates an expected call of ResetPassword.
func (mr *MockAccountServiceMockRecorder) ResetPassword(ctx, accountTypeEndpoint, resetInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetPassword", reflect.TypeOf((*MockAccountService)(nil).ResetPassword), ctx, accountTypeEndpoint, resetInfo)
}

// ResetPasswordInit mocks base method.
func (m *MockAccountService) ResetPasswordInit(ctx context.Context, accountTypeEndpoint accountservice.AccountTypeEndpoint, email string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetPasswordInit", ctx, accountTypeEndpoint, email)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetPasswordInit indicates an expected call of ResetPasswordInit.
func (mr *MockAccountServiceMockRecorder) ResetPasswordInit(ctx, accountTypeEndpoint, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetPasswordInit", reflect.TypeOf((*MockAccountService)(nil).ResetPasswordInit), ctx, accountTypeEndpoint, email)
}

// SearchPhysicianAccount mocks base method.
func (m *MockAccountService) SearchPhysicianAccount(ctx context.Context, request accountservice.PhysicianNotificationRequest) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchPhysicianAccount", ctx, request)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPhysicianAccount indicates an expected call of SearchPhysicianAccount.
func (mr *MockAccountServiceMockRecorder) SearchPhysicianAccount(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPhysicianAccount", reflect.TypeOf((*MockAccountService)(nil).SearchPhysicianAccount), ctx, request)
}

// SetAccountOwner mocks base method.
func (m *MockAccountService) SetAccountOwner(ctx context.Context, accountId string, request models.SetAccountOwnerRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAccountOwner", ctx, accountId, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAccountOwner indicates an expected call of SetAccountOwner.
func (mr *MockAccountServiceMockRecorder) SetAccountOwner(ctx, accountId, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAccountOwner", reflect.TypeOf((*MockAccountService)(nil).SetAccountOwner), ctx, accountId, request)
}

// SetPasswordAndVerify mocks base method.
func (m *MockAccountService) SetPasswordAndVerify(ctx context.Context, acctID, password string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPasswordAndVerify", ctx, acctID, password)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPasswordAndVerify indicates an expected call of SetPasswordAndVerify.
func (mr *MockAccountServiceMockRecorder) SetPasswordAndVerify(ctx, acctID, password any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPasswordAndVerify", reflect.TypeOf((*MockAccountService)(nil).SetPasswordAndVerify), ctx, acctID, password)
}

// ToggleAutoRenew mocks base method.
func (m *MockAccountService) ToggleAutoRenew(ctx context.Context, acctId, orderId string, autoRenew bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToggleAutoRenew", ctx, acctId, orderId, autoRenew)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToggleAutoRenew indicates an expected call of ToggleAutoRenew.
func (mr *MockAccountServiceMockRecorder) ToggleAutoRenew(ctx, acctId, orderId, autoRenew any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToggleAutoRenew", reflect.TypeOf((*MockAccountService)(nil).ToggleAutoRenew), ctx, acctId, orderId, autoRenew)
}

// UpdateAccountSettings mocks base method.
func (m *MockAccountService) UpdateAccountSettings(ctx context.Context, acctId string, accountSettings accountservice.UserSettingsRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountSettings", ctx, acctId, accountSettings)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountSettings indicates an expected call of UpdateAccountSettings.
func (mr *MockAccountServiceMockRecorder) UpdateAccountSettings(ctx, acctId, accountSettings any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountSettings", reflect.TypeOf((*MockAccountService)(nil).UpdateAccountSettings), ctx, acctId, accountSettings)
}

// UpdateEmail mocks base method.
func (m *MockAccountService) UpdateEmail(ctx context.Context, emailUpdate accountservice.UsersEmailUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEmail", ctx, emailUpdate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEmail indicates an expected call of UpdateEmail.
func (mr *MockAccountServiceMockRecorder) UpdateEmail(ctx, emailUpdate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmail", reflect.TypeOf((*MockAccountService)(nil).UpdateEmail), ctx, emailUpdate)
}

// UpdateOrderIntent mocks base method.
func (m *MockAccountService) UpdateOrderIntent(ctx context.Context, updateOrderIntent accountservice.UpdateOrderIntent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderIntent", ctx, updateOrderIntent)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderIntent indicates an expected call of UpdateOrderIntent.
func (mr *MockAccountServiceMockRecorder) UpdateOrderIntent(ctx, updateOrderIntent any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderIntent", reflect.TypeOf((*MockAccountService)(nil).UpdateOrderIntent), ctx, updateOrderIntent)
}

// UpdateOrderPaymentDetails mocks base method.
func (m *MockAccountService) UpdateOrderPaymentDetails(ctx context.Context, acctId, orderId string, paymentDetails accountservice.OrderPaymentDetailsRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderPaymentDetails", ctx, acctId, orderId, paymentDetails)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderPaymentDetails indicates an expected call of UpdateOrderPaymentDetails.
func (mr *MockAccountServiceMockRecorder) UpdateOrderPaymentDetails(ctx, acctId, orderId, paymentDetails any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderPaymentDetails", reflect.TypeOf((*MockAccountService)(nil).UpdateOrderPaymentDetails), ctx, acctId, orderId, paymentDetails)
}

// UpdatePatient mocks base method.
func (m *MockAccountService) UpdatePatient(ctx context.Context, acctId, patientId string, rB accountservice.Patient) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePatient", ctx, acctId, patientId, rB)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePatient indicates an expected call of UpdatePatient.
func (mr *MockAccountServiceMockRecorder) UpdatePatient(ctx, acctId, patientId, rB any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePatient", reflect.TypeOf((*MockAccountService)(nil).UpdatePatient), ctx, acctId, patientId, rB)
}

// VerifyEmail mocks base method.
func (m *MockAccountService) VerifyEmail(ctx context.Context, accountTypeEndpoint accountservice.AccountTypeEndpoint, verification accountservice.Verification) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyEmail", ctx, accountTypeEndpoint, verification)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyEmail indicates an expected call of VerifyEmail.
func (mr *MockAccountServiceMockRecorder) VerifyEmail(ctx, accountTypeEndpoint, verification any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyEmail", reflect.TypeOf((*MockAccountService)(nil).VerifyEmail), ctx, accountTypeEndpoint, verification)
}
