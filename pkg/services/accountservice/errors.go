package accountservice

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type AccountErrorString string

const (
	AccountSuspended     AccountErrorString = "account suspended"
	AccountLockedOut     AccountErrorString = "account locked out"
	InvalidRefreshToken  AccountErrorString = "token does not exist"
	ExpiredRefreshToken  AccountErrorString = "token has expired"
	BadAcctSvcRequest    AccountErrorString = "bad acctsvc request"
	AcctSvcNotFound      AccountErrorString = "not found in acctsvc"
	AcctPasswordNotSet   AccountErrorString = "account password not set"
	AcctPasswordNotValid AccountErrorString = "account password not valid"
	AcctDoesNotExist     AccountErrorString = "account does not exist"
	AcctNotVerified      AccountErrorString = "account not verified"
)

// TODO: this will be centralized and standardized across services
type AccountErrorRespBody struct {
	Version string             `json:"ver"`
	Message AccountErrorString `json:"msg"`
}

type ErrAcctSvc struct {
	Message string `json:"msg"`
}

// to represent that there was a password requirement error
type ErrPasswordRequirement struct {
	ErrMessage string
}

func (e ErrPasswordRequirement) Error() string {
	errRequirement := strings.SplitN(e.ErrMessage, ":", -1)[1]
	return fmt.Sprintf("%s:%s", errmsg.ERR_NEW_PASSWORD, errRequirement)
}

// to represent that no account exists for the given email
type ErrNoAccountForEmail struct {
	EmailHash string
}

func (e ErrNoAccountForEmail) Error() string {
	return fmt.Sprintf("%s: account with email hash:%s not found", AcctSvcNotFound, e.EmailHash)
}

// to represent that there was an issue verifying the email
type ErrVerifyEmail struct {
	Token string
}

func (e ErrVerifyEmail) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept verify email request with token of length: %d",
		len(e.Token),
	)
}

// to represent that there was an issue changing the email
type ErrInitUpdateEmail struct {
	EmailHash string
}

func (e ErrInitUpdateEmail) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept init update email request with email hash: %s",
		e.EmailHash,
	)
}

type ErrAccountNotFound struct {
	AccountID string
}

func (e ErrAccountNotFound) Error() string {
	return fmt.Sprintf("account with id:%s not found", e.AccountID)
}

// to represent there was conflicting email when creating account
type ErrNewAcctConflict struct {
	EmailHash string
}

func (e ErrNewAcctConflict) Error() string {
	return fmt.Sprintf(
		"accountservice acct with email hash: %s exists",
		e.EmailHash,
	)
}

// to represent there was conflicting acct/region when adding a new region to an account
type ErrNewRegionConflict struct {
	RegionID  uint16
	AccountId string
}

func (e ErrNewRegionConflict) Error() string {
	return fmt.Sprintf(
		"accountservice acct with id: %s exists with region %d ",
		e.AccountId,
		e.RegionID,
	)
}

// to represent there was an issue with the new account request in account service
type ErrBadRequest struct {
	RegionID  uint16
	EmailHash string
}

func (e ErrBadRequest) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept new account with email hash: %s,region_id:%d",
		e.EmailHash,
		e.RegionID,
	)
}

type ErrBadRequestForPasswordReset struct {
	EmailHash string
}

func (e ErrBadRequestForPasswordReset) Error() string {
	return fmt.Sprintf("%s for password reset init, email_hash: %s", BadAcctSvcRequest, e.EmailHash)
}

type ErrBadRequestAccountEdit struct {
	EmailHash string
}

func (e ErrBadRequestAccountEdit) Error() string {
	if e.EmailHash == "" {
		return "bad request for password reset to Account Service"
	} else {
		return "bad request account edit to Account Service"
	}
}

type ErrResetPasswordNotFound struct {
}

func (e ErrResetPasswordNotFound) Error() string {
	return "reset token and code not found or expired token"
}

type ErrBadNewPassword struct {
	MissingRequirement string
}

func (e ErrBadNewPassword) Error() string {
	if e.MissingRequirement != "" {
		return e.MissingRequirement
	}
	return "password requirement not met"
}

// to represent there was an issue with the new order request in account service
type ErrBadCreateOrderRequest struct {
	AccountID string
	RegionID  uint16
	PlanID    uint64
}

func (e ErrBadCreateOrderRequest) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept new order with acct_id:%s,region_id:%d,plan_id:%d",
		e.AccountID,
		e.RegionID,
		e.PlanID,
	)
}

var (
	ErrCardDeclined        string = "CardDeclined"
	ErrCardExpired         string = "CardExpired"
	ErrCardTypeNotAccepted string = "CardTypeNotAccepted"
	ErrInvalidCVC          string = "InvalidCVC"
	ErrInvalidCardNumber   string = "InvalidCardNumber"
	ErrGeneralPaymentError string = "GeneralPaymentError"
)

// to represent there was a 402 returned from the payment provider
type ErrPayment struct {
	AccountID string
	RegionID  uint16
	PlanID    uint64
	Reason    string
}

func (e ErrPayment) Error() string {
	return fmt.Sprintf(
		"there was a payment required error %s with new order with acct_id:%s,region_id:%d,plan_id:%d",
		e.Reason,
		e.AccountID,
		e.RegionID,
		e.PlanID,
	)
}

func (e ErrPayment) ClientError() string {
	switch e.Reason {
	case ErrCardDeclined:
		return "Declined"
	case ErrCardExpired:
		return "Expired"
	case ErrInvalidCardNumber:
		return "IncorrectCardNumber"
	case ErrInvalidCVC:
		return "IncorrectCVC"
	case ErrCardTypeNotAccepted:
		return "CardTypeNotAccepted"
	case ErrGeneralPaymentError:
		return "GenericError"
	}
	return e.Reason

}

type ErrBadUpdateOrderRequest struct {
	AccountID string
}

func (e ErrBadUpdateOrderRequest) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept new order with acct_id:%s",
		e.AccountID,
	)
}

// to represent there was an issue with refund order request in account service
type ErrBadRefundOrderRequest struct {
	AccountID string
	OrderID   string
}

func (e ErrBadRefundOrderRequest) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept refund order with acct_id:%s,order_id:%s",
		e.AccountID,
		e.OrderID,
	)
}

type ErrOrderNotFound struct {
	OrderID   string
	AccountID string
}

func (e ErrOrderNotFound) Error() string {
	return fmt.Sprintf(
		"acct_id:%s with order_id:%s not found in accountservice",
		e.AccountID,
		e.OrderID,
	)
}

type ErrAutoRenewBadRequest struct {
	OrderID string
}

func (e ErrAutoRenewBadRequest) Error() string {
	return fmt.Sprintf("accountservice did not accept autorenew order_id:%s", e.OrderID)
}

type ErrBadGetOrders struct {
	AccountID string
	Active    bool
	Recurring bool
}

func (e ErrBadGetOrders) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept get orders with account_id:%s,active:%t,recurring:%t",
		e.AccountID,
		e.Active,
		e.Recurring,
	)
}

type ErrBadGetOrderById struct {
	AccountID string
	OrderID   string
}

func (e ErrBadGetOrderById) Error() string {
	return fmt.Sprintf(
		"accountservice did not accept get order by id with account_id:%s,order_id:%s",
		e.AccountID,
		e.OrderID,
	)
}

type ErrPatchOrdersFailed struct {
	AccountID  string
	OrderID    string
	StatusCode int
}

func (e ErrPatchOrdersFailed) Error() string {
	return fmt.Sprintf(
		"accountservice failed to patch orderID `%s` for accountID `%s` with status %d",
		e.AccountID,
		e.OrderID,
		e.StatusCode,
	)
}

func HandleLoginError(w http.ResponseWriter, r *http.Request, err error, delay time.Duration) {
	if err.Error() == string(AccountLockedOut) {
		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_TOO_MANY_ATTEMPTS,
			http.StatusForbidden,
		) //prompt the user to wait 5 minutes before trying again
		return
	}
	if err.Error() == string(AcctPasswordNotSet) ||
		err.Error() == string(AcctPasswordNotValid) ||
		err.Error() == string(AcctDoesNotExist) ||
		err.Error() == string(AcctNotVerified) {

		// if account wasn't found, add some delay before responding to client
		// https://www.notion.so/pockethealth/Remediate-Process-Timing-Vulnerability-in-Login-6dd1d301c6e84e05801cbe3ba7ddbba6
		if err.Error() == string(AcctDoesNotExist) {
			time.Sleep(delay)
		}

		httperror.ErrorWithLog(
			w,
			r,
			errmsg.ERR_BAD_USER_CREDENTIALS,
			http.StatusUnauthorized,
		)
		return
	}
	if err.Error() == string(AccountSuspended) {
		httperror.ErrorWithLog(w, r, errmsg.ERR_ACCOUNT_LOCKED, http.StatusForbidden)
		return
	}
	httperror.ErrorWithLog(
		w,
		r,
		http.StatusText(http.StatusInternalServerError),
		http.StatusInternalServerError,
	)
	return
}
