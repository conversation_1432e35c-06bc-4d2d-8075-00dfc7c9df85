package pmts

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type KVString struct {
	Key   string
	Value string
}

type PaymentSvcUser struct {
	URL           string `json:"url"`
	ApiKey        string `json:"api_key"`
	ApiKeySecName string `json:"api_key_sec_name"`
	Client        *http.Client
	doRequest     requestwithcontext.RequestWithCtx
}

func (pmts *PaymentSvcUser) SetDoRequest(doer requestwithcontext.RequestWithCtx) {
	if doer == nil {
		pmts.doRequest = requestwithcontext.DoRequestWithCtx
	} else {
		pmts.doRequest = doer
	}
}

// GetPaymentProviders gets the payment providers in a country
func (pmts *PaymentSvcUser) GetPaymentProviders(
	ctx context.Context,
	country string,
) ([]string, error) {
	qsp := []KVString{{Key: "country", Value: country}}
	resp, err := pmts.request(ctx, "GET", "v1/transactions/providers", nil, qsp, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from payment serv", resp.StatusCode)
	}

	var providerList []string
	err = json.NewDecoder(resp.Body).Decode(&providerList)
	if err != nil {
		return nil, err
	}

	return providerList, nil
}

func (pmts *PaymentSvcUser) GetTransactionHistory(
	ctx context.Context,
	acctID string,
) (TransactionsReport, error) {
	lg := logutils.DebugCtxLogger(ctx)
	qsp := []KVString{{Key: "acctID", Value: acctID}}
	qsp = append(qsp, KVString{Key: "limit", Value: "50"})
	resp, err := pmts.request(ctx, "GET", "v1/report/transactions", nil, qsp, nil)
	if err != nil {
		return TransactionsReport{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		lg.WithError(err).WithField("code", resp.StatusCode).Info("error quering payment service")
		return TransactionsReport{}, fmt.Errorf("got %v back from payment serv", resp.StatusCode)
	}

	var transactionsReport TransactionsReport
	err = json.NewDecoder(resp.Body).Decode(&transactionsReport)
	if err != nil {
		return TransactionsReport{}, err
	}

	return transactionsReport, nil
}

func (pmts *PaymentSvcUser) GetDiscountCode(
	ctx context.Context,
	discountCode string,
) ([]coreapi.DiscountDetails, error) {
	lg := logutils.DebugCtxLogger(ctx)
	qsp := []KVString{
		{Key: "disc_name", Value: discountCode},
	}

	resp, err := pmts.request(ctx, "GET", "v1/discounts", nil, qsp, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		lg.WithError(err).WithField("code", resp.StatusCode).Info("error querying payment service")
		return nil, fmt.Errorf("got %v back from payment serv", resp.StatusCode)
	}

	var discountDetails GetDiscountCodeResponse
	err = json.NewDecoder(resp.Body).Decode(&discountDetails)
	if err != nil {
		return nil, err
	}

	return discountDetails.Data, nil
}

func (pmts *PaymentSvcUser) request(
	ctx context.Context,
	verb string,
	path string,
	body io.Reader,
	queryParams []KVString,
	headers []KVString,
) (*http.Response, error) {
	req, err := http.NewRequest(verb, fmt.Sprintf("https://%s/%s", pmts.URL, path), body)
	if err != nil {
		return nil, fmt.Errorf("could not create pmts request: %v", err)
	}

	req.Header.Add("Authorization", pmts.ApiKey)

	for _, kv := range headers {
		req.Header.Add(kv.Key, kv.Value)
	}

	if len(queryParams) > 0 {
		q := req.URL.Query()
		for _, kv := range queryParams {
			q.Add(kv.Key, kv.Value)
		}
		req.URL.RawQuery = q.Encode()
	}

	resp, err := pmts.doRequest(ctx, pmts.Client, req)
	if err != nil {
		return nil, fmt.Errorf("could not do pmts request: %v", err)
	}
	return resp, nil
}
