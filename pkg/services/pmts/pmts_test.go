package pmts

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

var pmts = PaymentSvcUser{
	URL:    "heresaurl",
	ApiKey: "heresakey",
}

func checkAuthAndURL(expectedURL string, req *http.Request, t *testing.T) {
	auth := req.Header.Get("Authorization")
	if auth == "" {
		t.Fatal("request missing authorization")
	}
	if auth != pmts.ApiKey {
		t.Fatalf("expected auth to be %s, got %s", pmts.ApiKey, auth)
	}
	if req.URL.String() != expectedURL {
		t.Fatalf("expected request URL %s, got %s", expectedURL, req.URL.String())
	}
}

func TestCheckPaymentProviders(t *testing.T) {
	zeroproviders := ioutil.NopCloser(strings.NewReader(`
	[]
	`))

	oneProvider := ioutil.NopCloser(strings.NewReader(`
	[
		"moneris"
	]
	`))

	threeproviders := ioutil.NopCloser(strings.NewReader(`
	[
		"moneris",
		"moneris2",
		"moneris3"
	]
	`))

	t.Run("empty provider list", func(t *testing.T) {
		expectedUrl := fmt.Sprintf("https://%s/v1/transactions/providers?country=CA", pmts.URL)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedUrl, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       zeroproviders,
			}, nil
		}
		pmts.SetDoRequest(doReqMock)
		providers, err := pmts.GetPaymentProviders(context.Background(), "CA")
		if err != nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if len(providers) != 0 {
			t.Fatalf("got providers when expected none: %v", providers)
		}
	})

	t.Run("one provider", func(t *testing.T) {
		expectedUrl := fmt.Sprintf("https://%s/v1/transactions/providers?country=CA", pmts.URL)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedUrl, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       oneProvider,
			}, nil
		}
		pmts.SetDoRequest(doReqMock)
		providers, err := pmts.GetPaymentProviders(context.Background(), "CA")
		if err != nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if len(providers) != 1 || providers[0] != "moneris" {
			t.Fatalf("got incorrect providers list: %v", providers)
		}
	})

	t.Run("three providers", func(t *testing.T) {
		expectedUrl := fmt.Sprintf("https://%s/v1/transactions/providers?country=CA", pmts.URL)
		doReqMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			checkAuthAndURL(expectedUrl, req, t)
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       threeproviders,
			}, nil
		}
		pmts.SetDoRequest(doReqMock)
		providers, err := pmts.GetPaymentProviders(context.Background(), "CA")
		if err != nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if len(providers) != 3 || providers[2] != "moneris3" {
			t.Fatalf("got incorrect providers list: %v", providers)
		}
	})
}
