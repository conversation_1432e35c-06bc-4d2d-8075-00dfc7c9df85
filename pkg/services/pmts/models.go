package pmts

import (
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

type PostTransactionRequest struct {
	SourceId       string `json:"source_id"`
	AcctId         string `json:"acct_id"`
	Item           string `json:"item"`
	Amount         uint   `json:"amount"`
	Provider       string `json:"provider"`
	PaymentDetails struct {
		Token   string `json:"token"`
		Country string `json:"country"`
		Zip     string `json:"zip"`
	} `json:"payment_details"`
	Discount *coreapi.PaymentTransactionDiscount `json:"discount,omitempty"`
}

type PostTransactionResponse struct {
	TransactionId  string             `json:"transaction_id"`
	AcctId         string             `json:"acct_id"`
	Item           string             `json:"item"`
	Status         string             `json:"status"`
	Source         string             `json:"source"`
	SourceId       string             `json:"source_id"`
	Country        string             `json:"country"`
	Zip            string             `json:"zip"`
	ChargeInfo     PostTxnChargeInfo  `json:"charge_info"`
	Provider       string             `json:"provider"`
	ProviderTxnId  string             `json:"provider_txn_id"`
	ProviderRefNum string             `json:"provider_ref_num"`
	PaymentInfo    PostTxnPaymentInfo `json:"payment_info"`
}

type PostTxnChargeInfo struct {
	Timestamp       string `json:"timestamp"`
	Amount          uint   `json:"amount"`
	DiscountDollars uint   `json:"discount_dollars"`
	TaxAmount       uint   `json:"tax_amount"`
	TotalCharged    uint   `json:"total_charged"`
	Currency        string `json:"currency"`
	DiscountName    string `json:"discount_name"`
	RefundAmount    uint   `json:"refund_amount"`
}

type PostTxnPaymentInfo struct {
	CardNumMasked string `json:"card_num_masked"`
	CardType      string `json:"card_type"`
	CardExpMonth  uint   `json:"card_exp_month"`
	CardExpYear   uint   `json:"card_exp_year"`
	Country       string `json:"country"`
	Zip           string `json:"zip"`
}

type Item string

var (
	ExpertReview Item = "expertreview"
)

type TransactionsReport struct {
	Data []Transaction `json:"data"`
	// token to get next set of transactions
	NextCursor string `json:"nextCursor"`
}

type Transaction struct {
	TransactionId string `json:"transaction_id"`

	Status TransactionStatus `json:"status"`

	Source TransactionSource `json:"source"`

	SourceId string `json:"source_id"`

	OrderID string `json:"order_id"`

	AcctID string `json:"acct_id"`

	Item string `json:"item"`

	Country string `json:"country"`

	Zip string `json:"zip"`

	Provider string `json:"provider"`

	ChargeInfo ChargeInfo `json:"charge_info,omitempty"`

	ProviderTxnId string `json:"provider_txn_id,omitempty"`

	ProviderRefNum string `json:"provider_ref_num,omitempty"`

	PaymentInfo *PaymentInfo `json:"payment_info,omitempty"`

	RefundReason string `json:"refund_reason,omitempty"`
}

type PaymentInfo struct {
	CardNumMasked string `json:"card_num_masked"`

	CardExpMonth int `json:"card_exp_month"`

	CardExpYear int `json:"card_exp_year"`

	CardType string `json:"card_type,omitempty"`

	Country string `json:"country"`

	Zip string `json:"zip"`
}

type ChargeInfo struct {
	Timestamp time.Time `json:"timestamp"`

	Amount int32 `json:"amount"`

	DiscountAmount int32 `json:"discount_amount,omitempty"`

	Tax int32 `json:"tax"`

	TotalCharged int32 `json:"total_charged"`

	Currency Currency `json:"currency"`

	DiscountName string `json:"discount_name,omitempty"`

	RefundAmount int32 `json:"refund_amount,omitempty"`
}

type GetDiscountCodeResponse struct {
	Data []coreapi.DiscountDetails `json:"data"`
}

type Currency string

// List of Currency
const (
	USD Currency = "USD"
	CAD Currency = "CAD"
)

type TransactionStatus string

// List of TransactionStatus
const (
	PROCESSED    TransactionStatus = "Processed"
	DECLINED     TransactionStatus = "Declined"
	REFUNDED     TransactionStatus = "Refunded"
	NOTPROCESSED TransactionStatus = "NotProcessed"
)

type TransactionSource string

// List of TransactionSource
const (
	SourceSubscription TransactionSource = "subscription"
	SourcePurchase     TransactionSource = "purchase"
	SourceRefund       TransactionSource = "refund"
)
