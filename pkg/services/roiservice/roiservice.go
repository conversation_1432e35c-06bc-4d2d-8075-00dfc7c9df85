package roiservice

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

const (
	REQUEST_SEND_ERROR       = "error making http request - cannot send request to roisvc"
	REQUEST_BODY_PARSE_ERROR = "error parsing request response body"
)

type RequestOptionsFunc func(*RequestOptions)

type RequestOptions struct {
	ctx        context.Context
	retry      bool
	retryCount int
}

type RoiServiceClient struct {
	RoiServiceUrl    string
	RoiServiceUser   string
	RoiServiceAPIKey string
	HttpClient       *httpclient.Client
}

type RoiService interface {
	GetVerificationConfigs(options ...RequestOptionsFunc) []VerificationConfig
	GetVerificationConfig(ctx context.Context, orgId int64) (VerificationConfig, error)
	ProcessDuplicateVerification(
		ctx context.Context,
		originalRequestId string,
		requestEmail string,
	) error
	ProcessVerificationInit(
		ctx context.Context,
		accountId string,
		originalRequestId string,
		requestEmail string,
		orgId string,
		newAccount string,
	) (string, error)
	PostSubmitStep(
		ctx context.Context,
		stepBody orgs.StepValidationBody,
	) (orgs.StepValidationResponse, error)
	PutSubmitStep(
		ctx context.Context,
		stepBody orgs.StepValidationBody,
	) (orgs.StepValidationResponse, error)
	GetRequestStatusHistory(
		requestId string,
		options ...RequestOptionsFunc,
	) []RequestStatusHistory
	PostSyncPatientJacket(
		ctx context.Context,
		accountId string,
	) error
}

func NewRoiServiceClient(
	url, user, apiKey string,
	client *httpclient.Client,
) *RoiServiceClient {
	return &RoiServiceClient{
		RoiServiceUrl:    url,
		RoiServiceUser:   user,
		RoiServiceAPIKey: apiKey,
		HttpClient:       client,
	}
}

func defaultRequestOptions() RequestOptions {
	defaultContext, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	return RequestOptions{
		ctx:   defaultContext,
		retry: false,
	}
}

func WithSetContext(ctx context.Context) RequestOptionsFunc {
	return func(options *RequestOptions) {
		options.ctx = ctx
	}
}

func WithRetryCount(retryCount int) RequestOptionsFunc {
	return func(options *RequestOptions) {
		options.retry = true
		options.retryCount = retryCount
	}
}

func (r *RoiServiceClient) setRequestOptions(options []RequestOptionsFunc) RequestOptions {
	requestOptions := defaultRequestOptions()
	for _, fn := range options {
		fn(&requestOptions)
	}
	if requestOptions.retry {
		r.HttpClient.RetryParams.MaxNumRetries = requestOptions.retryCount
	} else {
		r.HttpClient.RetryParams = nil
	}
	return requestOptions
}

func (r *RoiServiceClient) setupBasicAuth() string {
	cred := fmt.Sprintf("%s:%s", r.RoiServiceUser, r.RoiServiceAPIKey)
	return base64.StdEncoding.EncodeToString([]byte(cred))
}

func (r *RoiServiceClient) GetRequestStatusHistory(
	requestId string,
	options ...RequestOptionsFunc,
) []RequestStatusHistory {
	requestOptions := r.setRequestOptions(options)
	context := requestOptions.ctx
	lg := logutils.DebugCtxLogger(context)

	endpoint := fmt.Sprintf("%s/v1/requests/%s/history", r.RoiServiceUrl, requestId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		ReqBody:       nil,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	results := []RequestStatusHistory{}
	respBody, code, err := r.HttpClient.SendRequest(context, reqParams)
	if err != nil {
		lg.WithError(err).WithField("code", code).Info("error querying orgsvc")
		return results
	}
	defer respBody.Close()

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("error reading resp body")
		return results
	}

	err = json.Unmarshal(body, &results)
	if err != nil {
		lg.WithError(err).Error("error unmarshaling resp body")
		return results
	}

	return results
}

func (r *RoiServiceClient) GetVerificationConfigs(
	options ...RequestOptionsFunc,
) []VerificationConfig {
	requestOptions := r.setRequestOptions(options)
	context := requestOptions.ctx
	lg := logutils.DebugCtxLogger(context)

	endpoint := fmt.Sprintf("%s/v1/verifications/config", r.RoiServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		ReqBody:       nil,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK, http.StatusInternalServerError},
	}

	results := []VerificationConfig{}
	respBody, code, err := r.HttpClient.SendRequest(context, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return results
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()
	if code != http.StatusOK {
		lg.Errorf("got %d back from roi svc", code)
		return results
	}

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return results
	}

	err = json.Unmarshal(body, &results)
	if err != nil {
		lg.WithField("response_body", string(body)).WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return results
	}

	return results
}

func (r *RoiServiceClient) GetVerificationConfig(
	ctx context.Context,
	orgId int64,
) (VerificationConfig, error) {
	lg := logutils.DebugCtxLogger(ctx)

	endpoint := fmt.Sprintf("%s/v1/verifications/%d/config", r.RoiServiceUrl, orgId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "GET",
		ReqBody:       nil,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK, http.StatusNotFound, http.StatusInternalServerError},
	}

	result := VerificationConfig{}
	respBody, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return result, err
	}
	// ensure http.Client connection reuse
	defer func() {
		err := respBody.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()

	if code != http.StatusOK {
		lg.Errorf("got %v back from roi svc", code)
		return result, fmt.Errorf("got %v back from roi svc", code)
	}

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return result, err
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithField("response_body", string(body)).WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return result, err
	}

	return result, nil
}

func (r *RoiServiceClient) ProcessDuplicateVerification(
	ctx context.Context,
	originalRequestId string,
	requestEmail string,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	reqBodyJson, err := json.Marshal(
		DuplicateVerificationRequest{RequestId: originalRequestId, Email: requestEmail},
	)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return err
	}

	endpoint := fmt.Sprintf("%s/v1/verifications/requests/duplicate", r.RoiServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK, http.StatusInternalServerError},
	}

	_, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return err
	}

	if code != http.StatusOK {
		return fmt.Errorf("got %v back from roi svc", code)
	}

	return nil
}

func (r *RoiServiceClient) ProcessVerificationInit(
	ctx context.Context,
	accountId string,
	originalRequestId string,
	requestEmail string,
	orgId string,
	newAccount string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)

	reqBodyJson, err := json.Marshal(InitVerificationRequest{
		AccountId:  accountId,
		RequestId:  originalRequestId,
		Email:      requestEmail,
		OrgId:      orgId,
		NewAccount: newAccount,
	})
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return "", err
	}

	endpoint := fmt.Sprintf("%s/v1/verifications/requests/init", r.RoiServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK, http.StatusInternalServerError},
	}

	respBody, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return "", err
	}

	if code != http.StatusOK {
		return "", fmt.Errorf("got %v back from roi svc", code)
	}

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return "", err
	}

	var link string
	err = json.Unmarshal(body, &link)
	if err != nil {
		lg.WithField("response_body", string(body)).WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return "", err
	}

	return link, nil
}

func (r *RoiServiceClient) PostSubmitStep(
	ctx context.Context,
	stepBody orgs.StepValidationBody,
) (orgs.StepValidationResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	reqBodyJson, err := json.Marshal(stepBody)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return orgs.StepValidationResponse{}, err
	}

	endpoint := fmt.Sprintf("%s/v1/requests/submitStep", r.RoiServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	respBody, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return orgs.StepValidationResponse{}, err
	}

	if code != http.StatusOK {
		return orgs.StepValidationResponse{}, fmt.Errorf("got %v back from roi svc", code)
	}

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return orgs.StepValidationResponse{}, err
	}

	var result orgs.StepValidationResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithField("response_body", string(body)).WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return result, err
	}

	return result, nil
}

func (r *RoiServiceClient) PutSubmitStep(
	ctx context.Context,
	stepBody orgs.StepValidationBody,
) (orgs.StepValidationResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)

	reqBodyJson, err := json.Marshal(stepBody)
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return orgs.StepValidationResponse{}, err
	}

	endpoint := fmt.Sprintf("%s/v1/requests/submitStep", r.RoiServiceUrl)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "PUT",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	respBody, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return orgs.StepValidationResponse{}, err
	}

	if code != http.StatusOK {
		return orgs.StepValidationResponse{}, fmt.Errorf("got %v back from roi svc", code)
	}

	body, err := ioutil.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return orgs.StepValidationResponse{}, err
	}

	var result orgs.StepValidationResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithField("response_body", string(body)).WithError(err).Error(REQUEST_BODY_PARSE_ERROR)
		return result, err
	}

	return result, nil
}

// Triggers patient jacket sync for an account's active enrolments
func (r *RoiServiceClient) PostSyncPatientJacket(
	ctx context.Context,
	accountId string,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	// no request body
	endpoint := fmt.Sprintf("%s/v1/patients/%s/sync-jacket", r.RoiServiceUrl, accountId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       nil,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       r.setupBasicAuth(),
		ExpectedCodes: []int{http.StatusOK},
	}

	// no response body
	_, code, err := r.HttpClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf(REQUEST_SEND_ERROR+" : status %d", code)
		return err
	}

	if code != http.StatusOK {
		return fmt.Errorf("got %v back from roi svc", code)
	}

	return nil
}
