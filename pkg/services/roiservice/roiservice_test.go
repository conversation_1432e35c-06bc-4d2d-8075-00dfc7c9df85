package roiservice

import (
	"context"
	"testing"
)

func TestGetVerificationConfigs(t *testing.T) {
	t.Run("Get verification configuration records successfully", func(t *testing.T) {
		mockService := RoiServiceMock{}
		results := mockService.GetVerificationConfigs()
		if results == nil {
			t.Fatalf("expected empty array got nil")
		}
		if len(results) != 0 {
			t.Fatalf("expected array lenght 0, got %d", len(results))
		}
	})
}

func TestGetVerificationConfig(t *testing.T) {

	ctx := context.Background()

	t.Run("Get verification config record for org successfully", func(t *testing.T) {
		mockService := RoiServiceMock{}
		orgId := 10000
		results, err := mockService.GetVerificationConfig(ctx, int64(orgId))
		if err != nil {
			t.Fatalf("expected no error but got one")
		}
		if results.OrgId != int64(orgId) {
			t.Fatalf("expected result with orgId 0, got %d", results.OrgId)
		}
	})

	t.Run("Get verification config record for org unsuccessfully", func(t *testing.T) {
		mockService := RoiServiceMock{ExpectError: true}
		orgId := 10000
		results, _ := mockService.GetVerificationConfig(ctx, int64(orgId))

		if results.OrgId == int64(orgId) {
			t.Fatalf("expected result with default orgId, got %d", results.OrgId)
		}
	})
}

func TestGetRequestStatusHistory(t *testing.T) {

	ctx := context.Background()

	t.Run("get no records for invalid request identifier", func(t *testing.T) {
		mockService := RoiServiceMock{}

		results := mockService.GetRequestStatusHistory("0", WithSetContext(ctx))
		if len(results) != 0 {
			t.Errorf("expected no results, got %d", len(results))
		}
		results2 := mockService.GetRequestStatusHistory("", WithSetContext(ctx))
		if len(results2) != 0 {
			t.Errorf("expected no results, got %d", len(results2))
		}
	})
	t.Run("get records for valid request identifier", func(t *testing.T) {
		mockService := RoiServiceMock{}

		results := mockService.GetRequestStatusHistory("123456", WithSetContext(ctx))
		if len(results) == 0 {
			t.Errorf("expected results, got %d", len(results))
		}
		if results[0].RequestId != "123456" {
			t.Errorf("expected request identifier %s got %s", "123456", results[0].RequestId)
		}
	})
}
