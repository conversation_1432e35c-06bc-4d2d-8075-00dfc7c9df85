package roiservice

import "time"

type VerificationConfig struct {
	OrgId                int64     `json:"org_id"`
	Active               bool      `json:"active"`
	CreatedAt            time.Time `json:"created_at"`
	VerificationFlowLink string    `json:"idv_link,omitempty"`
}

type DuplicateVerificationRequest struct {
	Email     string `json:"email"`
	RequestId string `json:"request_id"`
}

type InitVerificationRequest struct {
	Email      string `json:"email"`
	AccountId  string `json:"account_id"`
	RequestId  string `json:"request_id"`
	OrgId      string `json:"org_id"`
	NewAccount string `json:"new_account"`
}

type RequestStatusHistory struct {
	RequestId     string    `json:"requestId"`
	Status        string    `json:"status"`
	TrackingState string    `json:"trackingState"`
	CreatedAt     time.Time `json:"createdAt"`
}
