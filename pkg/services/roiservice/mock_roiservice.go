package roiservice

import (
	"context"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
)

type RoiServiceMock struct {
	ExpectError bool
	MockError   error
}

func (r *RoiServiceMock) GetVerificationConfigs(options ...RequestOptionsFunc) []VerificationConfig {
	return []VerificationConfig{}
}

func (r *RoiServiceMock) GetVerificationConfig(ctx context.Context, orgId int64) (VerificationConfig, error) {
	if r.ExpectError {
		return VerificationConfig{}, r.<PERSON>ck<PERSON>rror
	}
	return VerificationConfig{OrgId: orgId}, nil
}

func (r *RoiServiceMock) ProcessDuplicateVerification(ctx context.Context, originalRequestId string, requestEmail string) error {
	return nil
}

func (r *RoiServiceMock) ProcessVerificationInit(
	ctx context.Context,
	accountId string,
	originalRequestId string,
	requestEmail string,
	orgId string,
	newAccount string,
) (string, error) {
	return "", nil
}

func (r *RoiServiceMock) PostSubmitStep(
	ctx context.Context,
	stepBody orgs.StepValidationBody,
) (orgs.StepValidationResponse, error) {
	return orgs.StepValidationResponse{}, nil
}

func (r *RoiServiceMock) PutSubmitStep(
	ctx context.Context,
	stepBody orgs.StepValidationBody,
) (orgs.StepValidationResponse, error) {
	return orgs.StepValidationResponse{}, nil
}

func (r *RoiServiceMock) GetRequestStatusHistory(
	requestId string,
	options ...RequestOptionsFunc,
) []RequestStatusHistory {
	if requestId == "" || requestId == "0" || r.ExpectError {
		return []RequestStatusHistory{}
	}
	return []RequestStatusHistory{
		{
			RequestId:     requestId,
			Status:        "teststatus",
			TrackingState: "testtrackingstate",
			CreatedAt:     time.Now().UTC(),
		},
	}
}

func (r *RoiServiceMock) PostSyncPatientJacket(
	ctx context.Context,
	accountId string,
) error {
	return nil
}
