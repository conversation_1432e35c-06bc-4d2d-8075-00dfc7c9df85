package services

import (
	"context"
	"encoding/base64"
	"io"
	"net/http"

	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// a base client for use with microservices
type BaseClient struct {
	Url        string `json:"url"`
	User       string `json:"name"`
	AuthScheme httpclient.Authorization
	ApiKey     string
	HttpClient *httpclient.Client
	authheader string
}

func NewClient(url string, apiUser string, apiKey string, client *httpclient.Client, authScheme httpclient.Authorization) *BaseClient {
	return &BaseClient{
		Url:        url,
		User:       apiUser,
		ApiKey:     apiKey,
		AuthScheme: authScheme,
		HttpClient: client,
		authheader: base64.StdEncoding.EncodeToString([]byte(apiUser + ":" + apiKey)),
	}
}

// Perform a POST request and get the response
func (bc *BaseClient) Post(ctx context.Context, uri string, reqBody []byte, queryParams map[string]string, expectedCodes []int) ([]byte, int, error) {
	lg := logutils.CtxLogger(ctx)
	endpoint := bc.Url + uri
	res, code, err := bc.HttpClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodPost,
		TargetURL:     endpoint,
		ReqBody:       reqBody,
		QueryParams:   queryParams,
		AuthScheme:    bc.AuthScheme,
		AuthKey:       bc.authheader,
		ExpectedCodes: expectedCodes,
	})
	if err != nil {
		lg.WithError(err).Errorf("request to %s returned an error", endpoint)
		return nil, code, err
	}
	if code != http.StatusOK {
		return nil, code, err
	}

	body, err := bc.readAndClose(ctx, res)

	return body, code, err
}

// Perform a GET request and get the response
func (bc *BaseClient) Get(ctx context.Context, uri string, queryParams map[string]string, expectedCodes []int) ([]byte, int, error) {
	lg := logutils.CtxLogger(ctx)
	endpoint := bc.Url + uri
	res, code, err := bc.HttpClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodGet,
		TargetURL:     endpoint,
		ReqBody:       nil,
		QueryParams:   queryParams,
		AuthScheme:    bc.AuthScheme,
		AuthKey:       bc.authheader,
		ExpectedCodes: expectedCodes,
	})
	if err != nil {
		lg.WithError(err).Errorf("request to %s returned an error", endpoint)
		return nil, code, err
	}
	if code != http.StatusOK {
		return nil, code, err
	}

	body, err := bc.readAndClose(ctx, res)

	return body, code, err
}

func (bc *BaseClient) readAndClose(ctx context.Context, r io.ReadCloser) ([]byte, error) {
	lg := logutils.CtxLogger(ctx)
	body, err := io.ReadAll(r)
	defer func() {
		err := r.Close()
		if err != nil {
			lg.WithError(err).Error("error closing response body")
		}
	}()
	if err != nil {
		lg.WithError(err).Error("error reading body")
		return nil, err
	}
	return body, nil
}
