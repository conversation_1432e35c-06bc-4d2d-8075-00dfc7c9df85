package examinsightsservice

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type EIServiceUser struct {
	URL           string `json:"url"`
	ApiKey        string `json:"api_key"`
	ApiKeySecName string `json:"api_key_sec_name"`
	HttpClient    *http.Client
	doRequest     DoRequestWithCtx
}

type DoRequestWithCtx func(ctx context.Context, client *httpclient.Client, reqParams httpclient.RequestParameters) (io.ReadCloser, int, error)

func doRequestWithCtx(
	ctx context.Context,
	client *httpclient.Client,
	reqParams httpclient.RequestParameters,
) (io.ReadCloser, int, error) {
	return client.SendRequest(ctx, reqParams)
}

func (sos *EIServiceUser) SetDoRequest(doer DoRequestWithCtx) {
	if doer == nil {
		sos.doRequest = doRequestWithCtx
	} else {
		sos.doRequest = doer
	}
}

// PostSecondOpinionRequest attempts to create an expert review request
func (sos *EIServiceUser) PostSecondOpinionRequest(
	ctx context.Context,
	soRequest PostSecondOpinionServiceRequest,
) (string, error) {

	payloadBuf := new(bytes.Buffer)
	if err := json.NewEncoder(payloadBuf).Encode(soRequest); err != nil {
		return "", err
	}

	resp, statusCode, err := sos.request(
		ctx,
		"POST",
		"v1/reviews",
		payloadBuf.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)

	if statusCode == http.StatusNotFound {
		return "", errors.New(errmsg.ERR_NOT_FOUND)
	} else if statusCode == http.StatusBadRequest {
		return "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode == http.StatusUnauthorized {
		return "", errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode != http.StatusOK {
		return "", fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		return "", err
	}

	reviewId, err := io.ReadAll(resp)
	if err != nil {
		return "", err
	}

	rId := string(reviewId)
	return rId[1 : len(rId)-2], nil
}

// GetSecondOpinionEligibilityStatus returns a JSON containing the eligibility and status of an exam given the exam uuid.
func (sos *EIServiceUser) GetSecondOpinionEligibilityStatus(
	ctx context.Context,
	examUuid string,
) (interface{}, error) {
	resp, statusCode, err := sos.request(
		ctx,
		"GET",
		fmt.Sprintf("v1/eligible/%s", examUuid),
		nil,
		nil,
		httpclient.RequestParameters{},
	)
	if err != nil {
		return nil, err
	}

	if statusCode == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from sos", statusCode)
	}

	var jsonResponse GetSecondOpinionEligibilityStatusResponse
	err = json.NewDecoder(resp).Decode(&jsonResponse)
	if err != nil {
		return nil, err
	}

	return jsonResponse, nil
}

func (sos *EIServiceUser) CheckSecondOpinionEligiblePriors(
	ctx context.Context,
	request coreapi.SoEligiblePriorsRequest,
) ([]string, error) {

	payloadBuf := new(bytes.Buffer)
	if err := json.NewEncoder(payloadBuf).Encode(request); err != nil {
		return nil, err
	}

	resp, statusCode, err := sos.request(
		ctx,
		"POST",
		"v1/eligible/priors",
		payloadBuf.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)
	if err != nil {
		return nil, err
	}

	if statusCode == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from sos", statusCode)
	}

	var jsonResponse []string
	err = json.NewDecoder(resp).Decode(&jsonResponse)
	if err != nil {
		return nil, err
	}

	return jsonResponse, nil
}

func (sos *EIServiceUser) SearchSecondOpinionDoctors(
	ctx context.Context,
	query map[string]string,
) ([]coreapi.SOCPSODoctor, error) {
	doctors := make([]coreapi.SOCPSODoctor, 0)

	resp, statusCode, err := sos.request(
		ctx,
		"GET",
		"v1/doctors/search",
		nil,
		query,
		httpclient.RequestParameters{},
	)

	if statusCode == http.StatusUnauthorized {
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		return nil, err
	}

	err = json.NewDecoder(resp).Decode(&doctors)
	if err != nil {
		return nil, err
	}

	return doctors, err
}

func (sos *EIServiceUser) CheckAnalysisEligibility(
	ctx context.Context,
	accountId string,
	provider AnalysisProvider,
	examUuids []string,
) (*AnalysisEligibilityResp, error) {
	body := CheckAnalysisEligibilityRequest{
		AccountId: accountId,
		Provider:  fmt.Sprintf(`%s`, provider),
		ExamUuid:  examUuids,
	}

	payloadBuf := new(bytes.Buffer)
	if err := json.NewEncoder(payloadBuf).Encode(body); err != nil {
		return nil, err
	}

	resp, statusCode, err := sos.request(
		ctx,
		"POST",
		"v2/analysis/eligible",
		payloadBuf.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)

	if statusCode == http.StatusUnauthorized {
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode == http.StatusBadRequest {
		return nil, errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if statusCode == http.StatusUnprocessableEntity {
		return nil, errors.New(errmsg.ERR_DATA_NOT_VALID)
	} else if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		return nil, err
	}

	var eligibilityResp AnalysisEligibilityResp
	err = json.NewDecoder(resp).Decode(&eligibilityResp)
	if err != nil {
		return nil, err
	}

	return &eligibilityResp, err
}

func (sos *EIServiceUser) PostAnalysisRequest(
	ctx context.Context,
	accountId string,
	patientId string,
	provider AnalysisProvider,
	data json.RawMessage,
) (string, error) {
	body := AnalysisRequest{
		Provider:  fmt.Sprintf(`%s`, provider),
		AccountId: accountId,
		PatientId: patientId,
		Data:      data,
	}

	payloadBuf := new(bytes.Buffer)
	if err := json.NewEncoder(payloadBuf).Encode(body); err != nil {
		return "", err
	}

	resp, statusCode, err := sos.request(
		ctx,
		"POST",
		"v2/analysis",
		payloadBuf.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)

	if statusCode == http.StatusUnauthorized {
		return "", errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode == http.StatusBadRequest {
		return "", errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode != http.StatusOK {
		return "", fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		return "", err
	}

	var requestId string
	err = json.NewDecoder(resp).Decode(&requestId)
	if err != nil {
		return "", err
	}

	return requestId, err
}

// POST /v1/patient_eligibility/{account_id}/{program_name}
func (sos *EIServiceUser) CreatePatientEligibilitiesProgramForAccount(
	ctx context.Context,
	accountId string,
	programName coreapi.ProgramName,
) ([]coreapi.PatientEligibilityProgram, error) {
	log := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":   accountId,
		"program_name": programName,
	})

	var peps []coreapi.PatientEligibilityProgram
	path := fmt.Sprintf("v1/patient_eligibility/%s/%s", accountId, programName)
	resp, statusCode, err := sos.request(
		ctx,
		http.MethodPost,
		path,
		[]byte{},
		nil,
		httpclient.RequestParameters{
			ExpectedCodes: []int{
				http.StatusOK,
				http.StatusCreated,
				http.StatusUnauthorized,
				http.StatusBadRequest,
			},
		},
	)
	log = log.WithField("response_status", statusCode)

	if statusCode == http.StatusUnauthorized {
		log.WithError(err).Error("Failed CreatePatientEligibilitiesProgramForAccount request")
		return peps, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode == http.StatusBadRequest {
		log.WithError(err).Error("Failed CreatePatientEligibilitiesProgramForAccount request")
		return peps, errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode > 400 {
		log.WithError(err).Error("Failed CreatePatientEligibilitiesProgramForAccount request")
		return peps, fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		log.WithError(err).Error("Failed CreatePatientEligibilitiesProgramForAccount request")
		return peps, err
	}

	err = json.NewDecoder(resp).Decode(&peps)
	if err != nil {
		log.WithError(err).
			Error("Could not decode CreatePatientEligibilitiesProgramForAccount response")
		return peps, err
	}

	return peps, nil
}

// PUT /v1/patient_eligibility/{patent_id}/{program_name}
func (sos *EIServiceUser) UpdateUncompletedEligibilityProgramsForPatient(
	ctx context.Context,
	patientId string,
	programName coreapi.ProgramName,
	isCompleted bool,
) error {
	log := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"patient_id":   patientId,
		"program_name": programName,
	})

	payload := new(bytes.Buffer)
	err := json.NewEncoder(payload).
		Encode(UpdateUncompletedEligibilityProgramsForPatientRequest{IsCompleted: isCompleted})
	if err != nil {
		log.WithError(err).
			Error("Could not encode UpdateUncompletedEligibilityProgramsForPatient request")
		return err
	}

	path := fmt.Sprintf("v1/patient_eligibility/%s/%s", patientId, programName)
	_, statusCode, err := sos.request(
		ctx,
		http.MethodPut,
		path,
		payload.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)
	log = log.WithField("response_status", statusCode)

	if statusCode == http.StatusUnauthorized {
		log.WithError(err).
			Error("Unauthorized UpdateUncompletedEligibilityProgramsForPatient request")
		return errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode == http.StatusBadRequest {
		log.WithError(err).Error("Invalid UpdateUncompletedEligibilityProgramsForPatient request")
		return errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode == http.StatusNotFound {
		log.WithError(err).Error("Not Found UpdateUncompletedEligibilityProgramsForPatient request")
		return errors.New(errormsgs.ERR_NOT_FOUND)
	} else if statusCode > 400 {
		log.WithError(err).Error("Failed UpdateUncompletedEligibilityProgramsForPatient request")
		return fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		log.WithError(err).Error("Failed UpdateUncompletedEligibilityProgramsForPatient request")
		return err
	}

	return nil
}

// POST /v1/patient_eligibility
func (sos *EIServiceUser) CreatePatientEligibilityProgramForPatient(
	ctx context.Context,
	accountId string,
	patientId string,
	programName coreapi.ProgramName,
	isCompleted bool,
) error {
	log := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"patient_id":   patientId,
		"program_name": programName,
	})

	payload := new(bytes.Buffer)
	err := json.NewEncoder(payload).Encode(CreateNewPatientEligibilityProgramForPatient{
		AccountId:   accountId,
		PatientId:   patientId,
		ProgramName: string(programName),
		IsCompleted: isCompleted,
	})
	if err != nil {
		log.WithError(err).
			Error("Could not encode CreateNewPatientEligibilityProgramForPatient request")
		return err
	}
	path := "v1/patient_eligibility"
	_, statusCode, err := sos.request(
		ctx,
		http.MethodPost,
		path,
		payload.Bytes(),
		nil,
		httpclient.RequestParameters{},
	)

	if statusCode == http.StatusUnauthorized {
		log.WithError(err).
			Error("Unauthorized CreateNewPatientEligibilityProgramForPatient request")
		return errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if statusCode == http.StatusBadRequest {
		log.WithError(err).Error("Invalid CreateNewPatientEligibilityProgramForPatient request")
		return errors.New(errmsg.ERR_INVALID_REQ_BODY)
	} else if statusCode == http.StatusConflict {
		log.WithError(err).Error("Conflict CreateNewPatientEligibilityProgramForPatient request")
		return errors.New(errmsg.ERR_CONFLICT)
	} else if statusCode > 400 {
		log.WithError(err).Error("Failed CreateNewPatientEligibilityProgramForPatient request")
		return fmt.Errorf("got %v back from eisvc", statusCode)
	} else if err != nil {
		log.WithError(err).Error("Failed CreateNewPatientEligibilityProgramForPatient request")
		return err
	}
	return nil
}

func (sos *EIServiceUser) GetInsightEligibility(
	ctx context.Context,
	accountId string,
	patientId string,
	provider InsightProvider,
) ([]InsightsEligibilityResp, error) {
	resp, code, err := sos.request(
		ctx,
		"GET",
		"v2/eligible",
		nil,
		map[string]string{
			"accountId": accountId,
			"patientId": patientId,
			"provider":  fmt.Sprintf("%s", provider),
		},
		httpclient.RequestParameters{},
	)

	if code == http.StatusUnauthorized {
		return nil, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	} else if code == http.StatusBadRequest {
		return nil, errors.New(errmsg.ERR_BAD_QUERY_PARAM)
	} else if code == http.StatusNotFound {
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	} else if code != http.StatusOK {
		return nil, fmt.Errorf("got %v back from eisvc", code)
	} else if err != nil {
		return nil, err
	}

	eligibility := make([]InsightsEligibilityResp, 0)
	err = json.NewDecoder(resp).Decode(&eligibility)
	if err != nil {
		return nil, err
	}

	return eligibility, err
}

func (sos *EIServiceUser) request(
	ctx context.Context,
	verb string,
	path string,
	body []byte,
	queryParams map[string]string,
	reqParams httpclient.RequestParameters,
) (io.ReadCloser, int, error) {
	// Shorter intervals and fewer retries for ers.
	retryParams := &httpclient.RetryParameters{
		MaxNumRetries: 2,
		StartBase:     5,
		BaseDuration:  time.Duration(100) * time.Millisecond,
	}
	httpClient := httpclient.NewHTTPClient(sos.HttpClient, retryParams)

	queryString := url.Values{}
	for key, val := range queryParams {
		queryString.Add(key, val)
	}
	reqParams.HTTPMethod = verb
	reqParams.TargetURL = fmt.Sprintf("%s/%s?", sos.URL, path) + queryString.Encode()
	reqParams.ReqBody = body
	reqParams.AuthScheme = httpclient.APIKey
	reqParams.AuthKey = fmt.Sprintf("Bearer %s", sos.ApiKey)

	resp, statusCode, err := sos.doRequest(ctx, httpClient, reqParams)
	if err != nil {
		return nil, statusCode, fmt.Errorf("could not create sos request: %v", err)
	}

	return resp, statusCode, nil
}
