package examinsightsservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/segmentio/ksuid"
	"io"
	"net/http"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

var eis = EIServiceUser{
	HttpClient: http.DefaultClient,
}

func TestCheckEligibilityStatus(t *testing.T) {
	falseEligibility := io.NopCloser(strings.NewReader(`
	{
		"eligible": false,
		"review_status": ""
	}`))

	trueEligibility := io.NopCloser(strings.NewReader(`
	{
		"eligible": true,
		"review_status": "pending"
	}`))

	t.Run("ineligible response", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return falseEligibility, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		status, err := eis.GetSecondOpinionEligibilityStatus(context.Background(), examUuid)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if status.(GetSecondOpinionEligibilityStatusResponse).Eligible != false {
			t.Fatalf(
				"expected eligibility to be false but got: %v",
				status.(GetSecondOpinionEligibilityStatusResponse).Eligible,
			)
		}
	})

	t.Run("eligible response", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return trueEligibility, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		status, err := eis.GetSecondOpinionEligibilityStatus(context.Background(), examUuid)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if status.(GetSecondOpinionEligibilityStatusResponse).Eligible != true {
			t.Fatalf(
				"expected eligibility to be true but got: %v",
				status.(GetSecondOpinionEligibilityStatusResponse).Eligible,
			)
		}
	})

	t.Run("internal server error", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(
				strings.NewReader(`some err returned`),
			), http.StatusInternalServerError, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.GetSecondOpinionEligibilityStatus(context.Background(), examUuid)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
	})

	t.Run("request error", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return nil, http.StatusInternalServerError, errors.New("ERS Error")
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.GetSecondOpinionEligibilityStatus(context.Background(), examUuid)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
	})
}

func TestSubmitReviewRequest(t *testing.T) {
	t.Run("success submit response", func(t *testing.T) {
		originalId, _ := secure.GenerateRandomReadableCharacters(15)
		requestId := fmt.Sprintf("\"%s\"\n", originalId) // this is the format of how the request id is returned from so svc
		successResponse := io.NopCloser(strings.NewReader(requestId))
		soRequest := PostSecondOpinionServiceRequest{}
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return successResponse, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		id, err := eis.PostSecondOpinionRequest(context.Background(), soRequest)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if id != originalId {
			t.Fatalf("expected id: %s to be equal to: %s", originalId, id)
		}
	})

	t.Run("bad request submit response", func(t *testing.T) {
		soRequest := PostSecondOpinionServiceRequest{}
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(strings.NewReader(`some err returned`)), http.StatusBadRequest, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.PostSecondOpinionRequest(context.Background(), soRequest)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf("expected bad request error but got: %v", err)
		}
	})
	t.Run("unauthorized submit response", func(t *testing.T) {
		soRequest := PostSecondOpinionServiceRequest{}
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(strings.NewReader(`some err returned`)), http.StatusUnauthorized, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.PostSecondOpinionRequest(context.Background(), soRequest)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Fatalf("expected unauthorized error but got: %v", err)
		}
	})
	t.Run("not found submit response", func(t *testing.T) {
		soRequest := PostSecondOpinionServiceRequest{}
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(strings.NewReader(`some err returned`)), http.StatusNotFound, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.PostSecondOpinionRequest(context.Background(), soRequest)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Fatalf("expected not found error but got: %v", err)
		}
	})
}

func TestCheckAnalysisEligibility(t *testing.T) {
	falseEligibility := io.NopCloser(strings.NewReader(`
	{"eligible":false,"status":"blocked"}`))

	trueEligibility := io.NopCloser(strings.NewReader(`
	{
		"eligible": true,
		"status": "pending"
	}`))

	t.Run("ineligible response", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return falseEligibility, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		eligibility, err := eis.CheckAnalysisEligibility(
			context.Background(),
			ksuid.New().String(),
			RHO,
			[]string{examUuid},
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if eligibility.Eligible != false {
			t.Fatalf(
				"expected eligibility to be false but got: %v",
				eligibility.Eligible,
			)
		}
	})

	t.Run("eligible response", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return trueEligibility, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		eligibility, err := eis.CheckAnalysisEligibility(
			context.Background(),
			ksuid.New().String(),
			RHO,
			[]string{examUuid},
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		if eligibility.Eligible != true {
			t.Fatalf(
				"expected eligibility to be true but got: %v",
				eligibility.Eligible,
			)
		}
	})

	t.Run("internal server error", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(
				strings.NewReader(`some err returned`),
			), http.StatusInternalServerError, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.CheckAnalysisEligibility(context.Background(), ksuid.New().String(), RHO, []string{examUuid})
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
	})

	t.Run("bad request", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(
				strings.NewReader(`some err returned`),
			), http.StatusBadRequest, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.CheckAnalysisEligibility(context.Background(), ksuid.New().String(), RHO, []string{examUuid})
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf("expect error Bad request body but got %v", err)
		}
	})

}

func TestPostAnalysisRequest(t *testing.T) {
	id := io.NopCloser(strings.NewReader(`
	"someid"`))

	t.Run("success", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		rhoData := RhoAnalysisRequest{
			ExamUuid: examUuid,
		}
		jsonData, _ := json.Marshal(rhoData)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return id, http.StatusOK, nil
		}
		eis.SetDoRequest(doReqMock)
		id, err := eis.PostAnalysisRequest(context.Background(), "", "", RHO, jsonData)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if id != "someid" {
			t.Fatalf("expect %s got %s", "someid", id)
		}

	})

	t.Run("internal server error", func(t *testing.T) {
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(
				strings.NewReader(`some err returned`),
			), http.StatusInternalServerError, nil
		}
		examUuid, _ := secure.GenerateRandomDigits(10)
		rhoData := RhoAnalysisRequest{
			ExamUuid: examUuid,
		}
		jsonData, _ := json.Marshal(rhoData)
		eis.SetDoRequest(doReqMock)
		_, err := eis.PostAnalysisRequest(context.Background(), "", "", RHO, jsonData)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
	})

	t.Run("bad request", func(t *testing.T) {
		examUuid, _ := secure.GenerateRandomDigits(10)
		rhoData := RhoAnalysisRequest{
			ExamUuid: examUuid,
		}
		jsonData, _ := json.Marshal(rhoData)
		doReqMock := func(_ context.Context, _ *httpclient.Client, req httpclient.RequestParameters) (io.ReadCloser, int, error) {
			return io.NopCloser(
				strings.NewReader(`some err returned`),
			), http.StatusBadRequest, nil
		}
		eis.SetDoRequest(doReqMock)
		_, err := eis.PostAnalysisRequest(context.Background(), "", "", RHO, jsonData)
		if err == nil {
			t.Fatalf("got no error when expected error: %v", err)
		}
		if err.Error() != errormsgs.ERR_INVALID_REQ_BODY {
			t.Fatalf("expect error Bad request body but got %v", err)
		}
	})

}
