package examinsightsservice

import (
	"encoding/json"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// string enum for list of insights provider
type InsightProvider string

const (
	SecondOpinion InsightProvider = "so"
	Rho           InsightProvider = "rho"
)

type PostSecondOpinionServiceRequest struct {
	AccountId       string                           `json:"account_id"`
	ExamUuid        string                           `json:"exam_uuid"`
	PriorExams      []string                         `json:"prior_exams"`
	PatientDetail   coreapi.SORequestPatientDetail   `json:"request_patient_detail"`
	ReferringDetail coreapi.SORequestReferringDetail `json:"request_referring_detail"`
}

type GetSecondOpinionEligibilityStatusResponse struct {
	Eligible             bool   `json:"eligible"`
	ReviewStatus         string `json:"review_status,omitempty"`
	ReferralReceivedDate string `json:"referral_received_date,omitempty"`
}

type AnalysisProvider string

// List of AnalysisProvider
const (
	RHO AnalysisProvider = "rho"
)

type AnalysisEligibilityResp struct {
	Eligible  bool   `json:"eligible"`
	Status    string `json:"status,omitempty"`
	RequestId string `json:"request_id,omitempty"`
}

type CheckAnalysisEligibilityRequest struct {
	AccountId string   `json:"account_id"`
	Provider  string   `json:"provider"`
	ExamUuid  []string `json:"exam_uuid"`
}

type AnalysisRequest struct {
	Provider  string          `json:"provider"`
	RequestId string          `json:"request_id,omitempty"`
	AccountId string          `json:"account_id"`
	PatientId string          `json:"patient_id"`
	Data      json.RawMessage `json:"data"`
}

type RhoAnalysisRequest struct {
	ExamUuid string `json:"exam_uuid"`
}

type UpdateUncompletedEligibilityProgramsForPatientRequest struct {
	IsCompleted bool `json:"is_completed"`
}

type CreateNewPatientEligibilityProgramForPatient struct {
	AccountId   string `json:"account_id"`
	PatientId   string `json:"patient_id"`
	ProgramName string `json:"program_name"`
	IsCompleted bool   `json:"is_completed"`
}

type InsightsEligibilityResp struct {
	ExamUuid string `json:"exam_uuid"`
	Eligible bool   `json:"eligible"`
}
