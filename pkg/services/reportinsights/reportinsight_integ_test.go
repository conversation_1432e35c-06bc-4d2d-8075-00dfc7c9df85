//go:build integration
// +build integration

package reportinsights

import (
	"context"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/testutils"

	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth/service"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
	"golang.org/x/oauth2"
)

func TestReportInsights_GetInsights(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	insightsURL := cfg.ReportInsightsUrl
	path := "./token.txt"
	authUrl := cfg.GatewayAuthUrl
	audience := "reportinsights"
	ctx := context.Background()

	// Creates a text file with a service account token
	// file is deleted after the test
	phtestutils.SetSvcTokenToTextFile(t, ctx, "coreapi", "sa-coreapi", path)
	tokenSource := service.NewTokenSource(
		path,
		authUrl,
		audience,
		ctx,
	)

	testClient := oauth2.NewClient(ctx, oauth2.ReuseTokenSource(nil, tokenSource))
	client := httpclient.NewHTTPClient(testClient, nil)
	reportInsightsClient := NewInsightsClient(insightsURL, client)

	t.Run("successfully call get insights ep", func(t *testing.T) {
		ctx := context.Background()
		_, err := reportInsightsClient.GetInsights(ctx, []string{"random_test_report"})
		require.NoError(t, err)
	})
}
