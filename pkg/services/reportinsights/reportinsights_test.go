package reportinsights

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

func TestReportInsightsClient_NewClient(t *testing.T) {
	baseURL := "https://ri-base-url.com"

	t.Run("with nil client", func(t *testing.T) {
		client := NewInsightsClient(baseURL, nil)
		assert.NotNil(t, client)
		assert.Equal(t, baseURL, client.BaseURL)
		assert.NotNil(t, client.HTTPClient)
	})

	t.Run("with custom client", func(t *testing.T) {
		customHTTPClient := httpclient.NewHTTPClient(&http.Client{}, nil)
		client := NewInsightsClient(baseURL, customHTTPClient)
		assert.NotNil(t, client)
		assert.Equal(t, baseURL, client.BaseURL)
		assert.Equal(t, customHTTPClient, client.HTTPClient)
	})
}
