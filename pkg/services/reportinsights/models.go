package reportinsights

type InsightsResponse struct {
	Reports       map[string]ReportInsight `json:"reports"`
	OrganvizCount TotalOrganViz            `json:"organviz_count"`
}

type TotalOrganViz struct {
	Xray int `json:"xray_chest"`
	Ct   int `json:"ct_abd"`
}

type ReportInsight struct {
	Organviz       OrganViz `json:"organviz"`
	HasExplanation bool     `json:"has_explanation"`
	// TODO: followup
}

type OrganViz struct {
	NumMasksGenerated int    `json:"num_masks_generated"`
	Model             string `json:"model"`
}

type FollowupBatchResponse = []struct {
	ReportId  string        `json:"report_id"`
	Followups FollowUpsResp `json:"followup"` // note singular here
}

type FollowUpsResp struct {
	Exist       bool                 `json:"exists"`
	Occurrences []FollowUpOccurrence `json:"occurrences"`
}

type FollowUpOccurrence struct {
	Context       string `json:"context"`
	MinTimeFrame  int    `json:"min_time_frame"`
	MaxTimeFrame  int    `json:"max_time_frame"`
	TimeFrameUnit string `json:"time_frame_unit"`
	Category      string `json:"category"`
}
