package reportprocessor

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
)

func TestSetAuthHeader(t *testing.T) {
	rp := RPClient{
		URL:    "someurl",
		User:   "core",
		ApiKey: "MJRYSdabkZp22zHL6gCnoSf4R18jmQ0teEdj57RbnkUyzkxmgXWxqV2rXLOOikPj",
	}

	t.Run("set header", func(t *testing.T) {
		rp.setAuthValue()
		if rp.authheader != "Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq" {
			t.Fatalf("expected rp to b64 encode user:APIKey, got %v", rp.authheader)
		}
	})

}

func TestReportInsights(t *testing.T) {
	server := NewTestServer([]byte{})
	defer server.Close()

	client := NewClient(server.URL, "user", "apikey")
	ctx := context.Background()

	t.Run("not found", func(t *testing.T) {
		_, err := client.GetFollowup(ctx, "notfound")
		_, err2 := client.GetQuestions(ctx, "notfound")
		_, err3 := client.GetOrganVisualization(ctx, "notfound", map[string]string{})

		cases := map[string]struct {
			expected error
		}{
			"followup":  {expected: err},
			"questions": {expected: err2},
			"organviz":  {expected: err3},
		}
		for name, testCase := range cases {
			t.Run(name, func(t *testing.T) {
				require.NotNil(t, testCase.expected)
				assert.Equal(t, testCase.expected.Error(), errormsgs.ERR_NOT_FOUND)
			})
		}
	})

	t.Run("followup success", func(t *testing.T) {
		followup, err := client.GetFollowup(ctx, "followup-exists")

		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if followup == nil {
			t.Fatal("expected followup not to be nil")
		}
		expected := `{"exists":true, "occurrences": [{"context":"follow up exists"}]}`
		if !cmp.Equal(string(followup), expected) {
			t.Fatalf("expected %v but got %v", expected, string(followup))
		}
	})

	t.Run("questions success", func(t *testing.T) {
		questions, err := client.GetQuestions(ctx, "questions-exist")

		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if questions == nil {
			t.Fatal("expected questions not to be nil")
		}
		expected := `{"questions":["How is babby formed?", "wat?"]}`
		if !cmp.Equal(string(questions), expected) {
			t.Fatalf("expected %v but got %v", expected, string(questions))
		}
	})

	t.Run("organviz success", func(t *testing.T) {
		oviz, err := client.GetOrganVisualization(ctx, "organviz-exists", map[string]string{})
		require.NoError(t, err)

		require.NotNil(t, oviz)
		assert.JSONEq(t, `
			{
				"organs":[{
					"segmentation":"some segments",
					"body_part": "spleen",
					"status": "COMPLETED"
				}]
			}`,
			string(oviz),
		)
	})
}
