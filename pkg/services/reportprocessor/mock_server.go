package reportprocessor

import (
	"net/http"
	"net/http/httptest"
	"regexp"
)

// sets up a test server to pretend to be report processor service.
// you MUST run `.Close()` on the server after you are finished
func NewTestServer(
	getReportResponse []byte,
) *httptest.Server {

	// #nosec
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		pattern := `^/v1/reports/[^/]+$`
		getReportsRegex := regexp.MustCompile(pattern)
		if getReportsRegex.MatchString(r.URL.Path) ||
			r.URL.Path == "/v1/reports/insights/followups" {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(getReportResponse))
			return
		} else {
			switch r.URL.Path {
			case "/v1/reports/notfound/insights/followup":
				fallthrough
			case "/v1/reports/notfound/insights/questions":
				fallthrough
			case "/v1/reports/notfound/insights/organviz":
				w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusNotFound)
				w.Write([]byte(`{"message":"not found"}`))
				return
			case "/v1/reports/followup-exists/insights/followup":
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"exists":true, "occurrences": [{"context":"follow up exists"}]}`))
				return
			case "/v1/reports/questions-exist/insights/questions":
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"questions":["How is babby formed?", "wat?"]}`))
				return
			case "/v1/reports/explanation-exists/insights/explanation":
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"explanation":"foo"}`))
				return
			case "/v1/reports/organviz-exists/insights/organviz":
				query := r.URL.Query()
				paramStr := query.Get("model")
				if paramStr != "" {
					switch paramStr {
					case "ct_abd":
						w.WriteHeader(http.StatusOK)
						w.Write([]byte(`{"organs":[{"segmentation":"some segments", "body_part": "liver", "status": "COMPLETED"}]}`))
						return
					case "xray_chest":
						w.WriteHeader(http.StatusOK)
						w.Write([]byte(`{"organs":[{"segmentation":"some segments", "body_part": "left lung", "status": "COMPLETED"}]}`))
						return
					case "mri_abd":
						w.WriteHeader(http.StatusOK)
						w.Write([]byte(`{"organs":[{"segmentation":"some segments", "body_part": "right lung", "status": "COMPLETED"}]}`))
						return
					}
				}
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"organs":[{"segmentation":"some segments", "body_part": "spleen", "status": "COMPLETED"}]}`))
				return
			}
		}
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`""`))
	}))
	return server
}
