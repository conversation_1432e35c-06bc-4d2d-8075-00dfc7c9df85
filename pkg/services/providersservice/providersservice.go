package providersservice

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/caches"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type ProvSvcUser struct {
	URL              string `json:"url"`
	User             string `json:"user"`
	PWSecName        string `json:"pw_sec_name"`
	PW               string `json:"pw"`
	Client           *http.Client
	AuthTokenCache   caches.CacheStringToString
	DoRequestWithCtx requestwithcontext.RequestWithCtx
}

type ProvAuthenticateResponse struct {
	Token string `json:"token"`
}

type ProvidersService interface {
	GetConsentAppointmentReminderData(
		ctx context.Context,
		consentId string,
	) (coreapi.ConsentAppointmentData, error)
	GetUploadStatusForStudyProvider(
		ctx context.Context,
		studyUID string,
		providerID int64,
	) (*models.UploadStatusStudy, error)
	GetAppointments(
		ctx context.Context,
		providerID int64,
		consentID string,
	) (coreapi.AppointmentDetails, error)
}

func (p *ProvSvcUser) GetAuthToken(
	ctx context.Context,
	doRequestWithCtx requestwithcontext.RequestWithCtx,
) (string, error) {
	// update cache if no auth token has been loaded or cache needs to be refreshed
	if !p.AuthTokenCache.IsInitialized() || p.AuthTokenCache.IsExpired() {
		p.AuthTokenCache.Initialize(ctx, "provsvc_auth_token")
	}

	// look for auth token in cache
	if cachedAuthToken, found := p.AuthTokenCache.Get(ctx, p.User); found {
		return cachedAuthToken, nil
	}

	//Authenticate with prov svc
	postBody, _ := json.Marshal(map[string]string{
		"username": p.User,
		"password": p.PW,
	})
	b := bytes.NewBuffer(postBody)

	req, err := http.NewRequest("POST", "https://"+p.URL+"/v1/authenticate", b)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("Could not create auth request")
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := doRequestWithCtx(ctx, p.Client, req)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("Prov svc auth failed")
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"prov_resp_status": resp.StatusCode,
		}).Error("Prov svc auth failed")
		return "", err
	}
	defer resp.Body.Close()

	authResp := ProvAuthenticateResponse{}
	err = json.NewDecoder(resp.Body).Decode(&authResp)
	if err != nil {
		return "", err
	}

	p.AuthTokenCache.Set(ctx, p.User, authResp.Token)
	return authResp.Token, nil
}

// GetConsentAppointmentReminderData
//   - Sends update request for patient status of appointment reminder to ProvidersService
func (s *ProvSvcUser) GetConsentAppointmentReminderData(
	ctx context.Context,
	consentId string,
) (coreapi.ConsentAppointmentData, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"consent_id": consentId,
	})
	authToken, err := s.GetAuthToken(ctx, s.DoRequestWithCtx)
	if err != nil || authToken == "" {
		return coreapi.ConsentAppointmentData{}, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	//send http GET request to providers' service v1/reminders/consent/{consent_id}
	req, err := http.NewRequest(
		"GET",
		"https://"+s.URL+"/v1/reminders/consent/"+consentId,
		nil,
	)
	if err != nil {
		return coreapi.ConsentAppointmentData{}, errors.New(errormsgs.ERR_CREATE_REQUEST)
	}

	req.Header.Set("X-Auth-Token", authToken)
	resp, err := s.DoRequestWithCtx(ctx, s.Client, req)
	if err != nil {
		lg.WithError(err).Error("send get consent appointment data to Provsvc failed")
		return coreapi.ConsentAppointmentData{}, err
	}

	if resp.StatusCode != http.StatusOK {
		lg.WithFields(logrus.Fields{
			"provsvc_resp_status": resp.StatusCode,
		}).Error("Provsvc response failed for consent appointment data")
		return coreapi.ConsentAppointmentData{}, err
	}
	defer resp.Body.Close()

	var consentAppointmentData coreapi.ConsentAppointmentData
	err = json.NewDecoder(resp.Body).Decode(&consentAppointmentData)
	if err != nil {
		lg.Error("parse json failed at consent appointment data")
		return coreapi.ConsentAppointmentData{}, err
	}

	return consentAppointmentData, nil
}

func (c *ProvSvcUser) GetUploadStatusForStudyProvider(
	ctx context.Context,
	studyUID string,
	providerID int64,
) (*models.UploadStatusStudy, error) {
	// get auth token for request
	authToken, err := c.GetAuthToken(ctx, c.DoRequestWithCtx)
	if err != nil || authToken == "" {
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	endpoint := fmt.Sprintf("https://%s/v1/uploadstatus/study", c.URL)

	// providersservice v1/uploadstatus/study endpoint requires a list of upload status requests but in this case we only want one study
	uploadStatusStudyReqList := []models.UploadStatusStudyRequest{
		{
			StudyUID:   studyUID,
			ProviderID: providerID,
		},
	}
	postBody, _ := json.Marshal(uploadStatusStudyReqList)
	b := bytes.NewBuffer(postBody)

	req, err := http.NewRequest(
		"POST",
		endpoint,
		b,
	)
	if err != nil {
		return nil, errors.New(errormsgs.ERR_CREATE_REQUEST)
	}

	req.Header.Set("X-Auth-Token", authToken)
	req.Header.Set("Content-Type", "application/json")
	resp, err := c.DoRequestWithCtx(ctx, c.Client, req)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, httperror.MapToError(
			resp.StatusCode,
			"failed to get upload status study list",
		)
	}

	defer resp.Body.Close()

	var uploadStatusList []models.UploadStatusStudy
	respBody, err := io.ReadAll(resp.Body)
	err = json.Unmarshal(respBody, &uploadStatusList)
	if err != nil {
		return nil, err
	}

	// return first upload status
	if len(uploadStatusList) == 1 {
		return &uploadStatusList[0], nil
	}
	return nil, nil
}

func (s *ProvSvcUser) GetAppointments(
	ctx context.Context,
	providerID int64,
	consentID string,
) (coreapi.AppointmentDetails, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"provider_id": providerID,
		"consent_id":  consentID,
	})
	authToken, err := s.GetAuthToken(ctx, s.DoRequestWithCtx)
	if err != nil || authToken == "" {
		return coreapi.AppointmentDetails{}, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	//send http GET request to providers' service v1/appointments
	req, err := http.NewRequest(
		http.MethodGet,
		fmt.Sprintf("https://%s/v1/appointments", s.URL),
		nil,
	)
	if err != nil {
		return coreapi.AppointmentDetails{}, errors.New(errormsgs.ERR_CREATE_REQUEST)
	}

	q := url.Values{}
	q.Add("provider_id", strconv.FormatInt(providerID, 10))
	q.Add("consent_id", consentID)
	req.URL.RawQuery = q.Encode()

	req.Header.Set("X-Auth-Token", authToken)
	resp, err := s.DoRequestWithCtx(ctx, s.Client, req)
	if err != nil {
		lg.WithError(err).Error("send get appointments to Provsvc failed")
		return coreapi.AppointmentDetails{}, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		lg.WithFields(logrus.Fields{
			"provsvc_resp_status": resp.StatusCode,
		}).Error("Provsvc response failed for retreiving appointments")
		return coreapi.AppointmentDetails{}, httperror.MapToError(
			resp.StatusCode,
			"request to get appointments was unsuccessful",
		)
	}

	var appointmentDetails coreapi.AppointmentDetails
	err = json.NewDecoder(resp.Body).Decode(&appointmentDetails)
	if err != nil {
		lg.Error("parse json failed at consent appointment data")
		return coreapi.AppointmentDetails{}, err
	}

	return appointmentDetails, nil
}
