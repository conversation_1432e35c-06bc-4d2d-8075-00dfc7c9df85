package providersservice

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

type ProviderServiceMock struct {
	testutils.BaseMock
}

func NewProviderServiceMock() *ProviderServiceMock {
	return &ProviderServiceMock{
		BaseMock: *testutils.NewBaseMock(),
	}
}

func (mock *ProviderServiceMock) GetConsentAppointmentReminderData(
	ctx context.Context,
	consentId string,
) (coreapi.ConsentAppointmentData, error) {
	mock.IncrementCounter("GetConsentAppointmentReminderData")
	result := testutils.ReturnMockValueOrDefault(
		mock.Returns,
		"GetConsentAppointmentReminderData",
		struct {
			Value coreapi.ConsentAppointmentData
			Err   error
		}{},
	)
	return result.Value, result.Err
}

func (mock *ProviderServiceMock) GetUploadStatusForStudyProvider(
	ctx context.Context,
	studyUID string,
	providerID int64,
) (*models.UploadStatusStudy, error) {
	mock.IncrementCounter("GetUploadStatusForStudyProvider")
	result := testutils.ReturnMockValueOrDefault(
		mock.Returns,
		"GetUploadStatusForStudyProvider",
		struct {
			Value *models.UploadStatusStudy
			Err   error
		}{},
	)
	return result.Value, result.Err
}

func (mock *ProviderServiceMock) GetAppointments(
	ctx context.Context,
	providerID int64,
	consentID string,
) (coreapi.AppointmentDetails, error) {
	mock.IncrementCounter("GetAppointments")
	result := testutils.ReturnMockValueOrDefault(
		mock.Returns,
		"GetAppointments",
		struct {
			Value coreapi.AppointmentDetails
			Err   error
		}{},
	)
	return result.Value, result.Err
}
