//go:build integration
// +build integration

package providersservice

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestGetUploadStatusForStudyProvider(t *testing.T) {
	// initialize service
	provSvcUser := ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	// initialize cache for auth token
	provSvcUser.AuthTokenCache.SetCacheLength(time.Duration(60))

	// prov svc response data
	token, _ := secure.GenerateRandomString(16)
	authBody := ProvAuthenticateResponse{
		Token: token,
	}

	studyUID := testutils.GenerateRandomString(t, 10)
	providerID := testutils.GenerateRandomInt64(t)
	uploadStatusStudyResp := []models.UploadStatusStudy{
		{
			StudyUID:        studyUID,
			ProviderID:      providerID,
			UploadCompleted: true,
			UploadManifest: models.StudyUploadManifest{
				Study:  true,
				Report: false,
				Instances: map[string]bool{
					"1.2.3.4.100.354": true,
					"1.2.3.4.100.355": true,
				},
			},
		},
	}

	// mock prov svc requests
	doRequestWithCtxMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		provSvcRes := http.Response{
			StatusCode: http.StatusOK,
		}

		if req.Method == "POST" &&
			req.URL.String() == "https://"+provSvcUser.URL+"/v1/authenticate" {
			reqBody, _ := io.ReadAll(req.Body)
			var jsonMap map[string]json.RawMessage
			json.Unmarshal(reqBody, &jsonMap)

			// validate request fields
			var username string
			json.Unmarshal(jsonMap["username"], &username)
			require.Equal(t, username, provSvcUser.User)

			var password string
			json.Unmarshal(jsonMap["password"], &password)
			require.Equal(t, password, provSvcUser.PW)

			// setup respopnse
			body, _ := json.Marshal(authBody)
			provSvcRes.Body = io.NopCloser(bytes.NewReader(body))
		} else if req.Method == "POST" && req.URL.String() == "https://"+provSvcUser.URL+"/v1/uploadstatus/study" {
			// validate headers
			authToken := req.Header.Get("X-Auth-Token")
			require.Equal(t, authToken, token)

			contentType := req.Header.Get("Content-Type")
			require.Equal(t, "application/json", contentType)

			// validate request fields
			reqBody, _ := io.ReadAll(req.Body)
			var jsonArr []json.RawMessage
			json.Unmarshal(reqBody, &jsonArr)
			require.Equal(t, len(jsonArr), 1)

			var jsonMap map[string]json.RawMessage
			json.Unmarshal(jsonArr[0], &jsonMap)
			var studyUIDReq string
			json.Unmarshal(jsonMap["studyUID"], &studyUIDReq)
			require.Equal(t, studyUIDReq, studyUID)

			var providerIDReq int64
			json.Unmarshal(jsonMap["providerID"], &providerIDReq)
			require.Equal(t, providerIDReq, providerID)

			body, _ := json.Marshal(uploadStatusStudyResp)
			provSvcRes.Body = io.NopCloser(bytes.NewReader(body))
		} else {
			t.Errorf("unexpected request to prov svc: %s %s", req.Method, req.URL.String())
		}

		return &provSvcRes, nil
	}

	provSvcUser.DoRequestWithCtx = doRequestWithCtxMock
	provSvcUser.Client = &http.Client{}

	// run method
	uploadStatusStudy, err := provSvcUser.GetUploadStatusForStudyProvider(
		context.Background(),
		studyUID,
		providerID,
	)
	require.NoError(t, err)
	actualResp, _ := json.Marshal(uploadStatusStudy)
	expectedResp, _ := json.Marshal(uploadStatusStudyResp[0])
	require.JSONEq(t, string(actualResp), string(expectedResp))
}
