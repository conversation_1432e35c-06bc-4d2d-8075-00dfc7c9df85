package errormsgs

const (
	// Generic
	ERR_NOT_EXISTS     = "does not exist"
	ERR_DATA_NOT_VALID = "data is not valid"

	// Request related errors
	ERR_BAD_USER_CREDENTIALS = "Wrong email and/or password." //#nosec G101
	ERR_BAD_CREDENTIALS      = "Wrong credentials."           //#nosec G101
	ERR_TOO_MANY_ATTEMPTS    = "Too many bad attempts."
	ERR_NOT_AUTHORIZED       = "Not authorized"
	ERR_BAD_QUERY_PARAM      = "Bad query parameter"
	ERR_JSON_UNMARSHAL       = "Error unmarshaling json"
	ERR_INVALID_REQ_BODY     = "Bad request body"
	ERR_NOT_FOUND            = "Not found"
	ERR_MOVED_PERMANENTLY    = "Moved permanently"
	ERR_BAD_TOKEN            = "Bad token"
	ERR_CREATE_REQUEST       = "Could not create request"
	ERR_FORBIDDEN            = "Forbidden"
	ERR_CONFLICT             = "Resource conflict"
	ERR_UNPROCESSABLE_ENTITY = "Unprocessable entity"
	ERR_INVALID_CODE         = "Provided code doesn't match"

	// Filesystem related errors
	ERR_OP_CONFLICT = "OPCONFLICT"
	ERR_COPY_FAIL   = "COPYFAIL"
	ERR_READ_FAIL   = "READFAIL"
	ERR_OPEN_FAIL   = "OPENFAIL"
	ERR_COJOIN_FAIL = "COJOINFAIL"

	// Share errors
	ERR_LINK_EXPIRED  = "Link expired"
	ERR_UNABLE_EXTEND = "Share unable to extend"
	ERR_SHARE_REVOKED = "Share has been revoked"
	ERR_SHARE_ID_NULL = "shareId null"

	// Payment errors
	ERR_MISSING_PAYMENT = "Missing payment token"

	// Password creation error
	ERR_NEW_PASSWORD   = "Password invalid"
	ERR_NEEDS_PURCHASE = "Item requires purchase"
	// Account
	ERR_ACCOUNT_LOCKED = "Account locked"

	ERR_NOT_IMAGE = "not an image"
	ERR_BAD_PATH  = "Bad file path"

	// Health Records error
	ERR_NUMBER_OF_FILES = "Too many files for upload"
	ERR_FILE_SIZE       = "File is too large"
	ERR_FILE_TYPE       = "Filetype not allowed"

	//Image conversion
	ERR_TOO_LARGE = "image too large to convert to png"

	//Follow up tagger
	ERR_FAILED_TO_DOWNLOAD_BLOB = "Failed to download report"

	// CD Upload
	ERR_UPLOAD_NO_IMAGE     = "transfer must contain at least one image"
	ERR_NO_CD_UPLOAD_ACCESS = "user do not have access to CD uploads"

	// Exam Lookup
	ERR_DOB_VERIFICATION_REQUIRED = "provide dob for verification"
	ERR_DOB_FORBIDDEN             = "dob does not match"

	// PDF conversion
	ERR_PDF_FAILED_CONVERSION = "Unable to convert pdf"

	// service bus
	ERR_PUBLISH = "failed to publish event"

	// Account Deactivation
	ERR_FAILED_DEACTIVATION = "failed to deactivate account"

	// Record
	ERR_NO_RECORDS = "no studies available to unlock"

	// Audit
	ERR_AUDIT = "could not complete audit logging"

	// PH Patients
	ERR_FAILED_SET_ACCOUNT_OWNER = "could not set account owner"
	ERR_FAILED_CREATE_PATIENT    = "could not create patient"
)
