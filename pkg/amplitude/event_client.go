package amplitude

import (
	"context"

	amplitudeEvent "github.com/amplitude/analytics-go/amplitude"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type AmplitudeEventWrapper struct {
	client amplitudeEvent.Client
}

func NewAmplitudeEventClient(client amplitudeEvent.Client) interfaces.AmplitudeEventClient {
	return &AmplitudeEventWrapper{client: client}
}

func (a *AmplitudeEventWrapper) Track(event amplitudeEvent.Event) {
	defer a.recoverHelper()
	a.client.Track(event)
	a.client.Flush()
}

func (a *AmplitudeEventWrapper) recoverHelper() {
	lg := logutils.DebugCtxLogger(context.Background())
	if r := recover(); r != nil {
		lg.Errorf("Caught panic: %v", r)
	}
}
