package amplitude

import (
	"context"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	amplitudeExperiment "github.com/amplitude/experiment-go-server/pkg/experiment/remote"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type AmplitudeExperimentWrapper struct {
	client *amplitudeExperiment.Client
}

func NewAmplitudeExperimentClient(client *amplitudeExperiment.Client) interfaces.AmplitudeExperimentClient {
	return &AmplitudeExperimentWrapper{client: client}
}

func (a *AmplitudeExperimentWrapper) Fetch(user *experiment.User) (map[string]experiment.Variant, error) {
	defer a.recoverHelper()
	return a.client.Fetch(user)
}

func (a *AmplitudeExperimentWrapper) recoverHelper() {
	lg := logutils.DebugCtxLogger(context.Background())
	if r := recover(); r != nil {
		lg.Errorf("Caught panic: %v", r)
	}
}
