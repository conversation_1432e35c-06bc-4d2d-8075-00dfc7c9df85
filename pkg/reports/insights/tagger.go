package reports

import (
	"regexp"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func DetectFollow(extractedText [][]string) coreapi.FollowUpReport {
	re := regexp.MustCompile(`\b(?i)follow[- ]?up|follow- up|follow -up\b`)

	negativeFollowUpRegex := []string{
		`\bno\s*follow(?:\sup)?\b`,
		`\b(?i)Any (final|clinical or) pathological (follow[- ]?up|follow- up|follow -up)(?: pertinent to this examinati(?:on)?)? would be sincerely appreciated\b`,
		`\bIf you have not received information on your patient's ((?i)follow[- ]?up|follow- up|follow -up) app\b`,
		`\bAny immediate ((?i)follow[- ]?up|follow- up|follow -up) exam and/or procedure will be booked by the Breast Centre\b`,
		`\bPlease ensure the patient has a signed requisition indicating this is a ((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\bIf your patient is high risk by these criteria, please indicate this on the ((?i)follow[- ]?up|follow- up|follow -up) requisition\b`,
		`\b(?i)clinical indication: ((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\b(?i)clinical information:(?: \w+)? ((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\b(?i)indication\s*:\s*(?:\w+\s*)+(?:[\w\s-]*\w[\w\s-]*)\s+((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\b(?i)indication: ((?i)follow[- ]?up|follow- up|follow -up) .*?\b`,
		`\b(?i)findings\s*:\s*((?i)follow[- ]?up|follow- up|follow -up)\s+\w+\s*\w*\b`,
		`\b(?i)findings\s*:\s*(?:\w+\s*)+(?:[\w\s-]*\w[\w\s-]*)\s+((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\b(?i)frequency\s+of\s+((?i)follow[- ]?up|follow- up|follow -up)\s+determined\s+by\s+predicted\s+rate\s+of\s+bone\s+mineral\s+loss\s+and\s+continued\s+exposure\s+to\s+risk\s+factors\b`,
		`\b(?i)our\s+patient\s+if\s+you\s+previously\s+provided\s+authorization\s+for\s+your\s+patients\s+to\s+be\s+referred\s+on\s+your\s+behalf\s+for\s+additional\s+imaging,\s+to\s+include\s+ultrasound\s+and\/or\s+special\s+views,\s+biopsy\s+or\s+surgical\s+consult\s+at\s+the\s+Breast\s+DAP\s+\(Diagnostic\s+Assessment\s+Program\)\s+for\s+((?i)follow[- ]?up|follow- up|follow -up)\s+breast\s+detected\s+abnormalities\b`,
		`\b(?i)p\(c\)\s+dictated\s+but\s+not\s+read\s+as\s+part\s+of\s+our\s+quality\s+assurance\s+programme,\s+any\s+clinical\s+or\s+pathological\s+((?i)follow[- ]?up|follow- up|follow -up)\s+relevant\s+to\s+this\s+examination\s+will\s+be\s+appreciated\b`,
		`\b(?i)recommend\s+attention\s+on\s+((?i)follow[- ]?up|follow- up|follow -up)\b`,
		`\b(?i)suggest\s+attention\s+on\s+((?i)follow[- ]?up|follow- up|follow -up)\s+examination\b`,
		`\b(?i)recommend\s+((?i)follow[- ]?up|follow- up|follow -up)\s+as\s+clinically\s+indicated\b`,
		`\b((?i)ultrasound[- ]?guided)\s+core biopsy is recommended\b`,
	}

	for _, line := range extractedText {
		text := strings.Join(line, " ")
		if re.MatchString(text) && !matchesAnyRegex(text, negativeFollowUpRegex) {
			return coreapi.FollowUpReport{FollowUpExists: true}
		}
	}

	return coreapi.FollowUpReport{FollowUpExists: false}
}

func matchesAnyRegex(text string, regexList []string) bool {
	for _, regex := range regexList {
		re := regexp.MustCompile(regex)
		if re.MatchString(text) {
			return true
		}
	}
	return false
}
