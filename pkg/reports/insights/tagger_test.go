package reports

import (
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func TestDetectFollow(t *testing.T) {
	testCases := []struct {
		text           string
		expectedReport coreapi.FollowUpReport
	}{
		//matches
		{text: "Please follow up in 6 months.", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "Please follow up with the patient.", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "Follow up required.", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "Follow-up required.", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "follow up required", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "follow- up required", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "follow -up required", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "follow-up required", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		{text: "Short Interval Follow-up Suggested", expectedReport: coreapi.FollowUpReport{FollowUpExists: true}},
		//negatives
		{text: "Some text with no follow-up.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Some text with no follow- up.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Please ensure the patient has a signed requisition indicating this is a follow up.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Any clinical or pathological follow-up pertinent to this examination would be sincerely appreciated", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Any clinical or pathological follow up pertinent to this examination would be sincerely appreciated", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Any clinical or pathological follow up pertinent to this examinati would be sincerely appreciated", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "ANY FINAL PATHOLOGICAL FOLLOW UP WOULD BE SINCERELY APPRECIATED", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Any final pathological follow-up would be sincerely appreciated.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "no follow-up necessary.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "no follow up required.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Indication: Follow-up 1 year", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Indication: follow up 10 years", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical indication: MRI follow up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical indication: MRI follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "clinical indication: MRI Follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "clinical indication: X-Ray Follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "clinical indication: X-Ray follow up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI follow up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI Follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI Follow up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI follow- Up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Clinical information: MRI follow -Up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "INDICATION: Six-month follow up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "CLINICAL INDICATION: Follow-up nodules", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Frequency of follow up determined by predicted rate of bone mineral loss and continued exposure to risk factors", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Followup: Frequency of follow up determined by predicted rate of bone mineral loss and continued exposure to risk factors", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Followup: Frequency of follow-up determined by predicted rate of bone mineral loss and continued exposure to risk factors", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "our patient if you previously provided authorization for your patients to be referred on your behalf for additional imaging, to include ultrasound and/or special views, biopsy or surgical consult at the Breast DAP (Diagnostic Assessment Program) for follow-up breast detected abnormalities", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "P(C) DICTATED BUT NOT READ As part of our quality assurance programme, any clinical or pathological follow up relevant to this examination will be appreciated", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Recommend attention on follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Suggest attention on follow up examination", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "If you have not received information on your patient's follow-up app", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "(Staff) ***Any immediate follow-up exam and/or procedure will be booked by the Breast Centre", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "INDICATION: Fracture clinic follow-up", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "FINDINGS: Intraoperative follow-up at 2131 hours", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "Recommend follow-up as clinically indicated", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
		{text: "ultrasound-guided core biopsy is recommended.", expectedReport: coreapi.FollowUpReport{FollowUpExists: false}},
	}

	for _, testCase := range testCases {
		result := DetectFollow([][]string{{testCase.text}})
		if result != testCase.expectedReport {
			t.Errorf("Follow-up report detection failed. Expected: %v, Got: %v", testCase.expectedReport, result)
		}
	}
}
