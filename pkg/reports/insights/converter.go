package reports

import (
	"context"
	"errors"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/file"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
)

func Split(r rune) bool {
	return r == '\n' || r == '\t' || r == ' ' || r == ',' || r == '.' || r == '|' || r == '_' ||
		r == rune(byte(12))
}

func getTempFilePath(ctx context.Context, reportId string) string {
	corId := ctx.Value(logutils.CorrelationIdContextKey).(string)
	path := "vault/tmp/" + corId + "_" + reportId
	return path
}

// returns the path to txt file on disk
func DownloadPDFAndConvertToText(
	ctx context.Context,
	containerClient *azureUtils.ContainerClient, reportId string) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("reportId", reportId)

	basePath := getTempFilePath(ctx, reportId)

	if filepath.Dir(basePath) != "vault/tmp" {
		lg.WithFields(logrus.Fields{
			"reason": "path traversal detected",
			"path":   basePath,
		}).Error("get report failed ")
		return "", errors.New(errmsg.ERR_BAD_PATH)
	}

	pdfPath := basePath + ".pdf"
	txtPath := basePath + ".txt"

	_, err := file.DownloadFromAzureToDisk(ctx, containerClient.Objects, reportId, pdfPath)
	if err != nil {
		return "", err
	}

	err = pdftotext(pdfPath)
	if err != nil {
		_, errPath := exec.LookPath(txtPath)
		if errPath != nil {
			lg.WithError(errPath).Error(errmsg.ERR_PDF_FAILED_CONVERSION)
			return "", errors.New(errmsg.ERR_PDF_FAILED_CONVERSION)
		} else {
			lg.WithError(errPath).Error("PDF Conversion returned error, but txt got created")
		}
	}

	defer func() {
		if err := os.Remove(pdfPath); err != nil {
			lg.WithError(err).Error("error removing pdf")
		}
	}()

	return txtPath, nil
}

func pdftotext(pdfPath string) error {
	cmd := exec.Command("pdftotext", pdfPath)
	return cmd.Run()
}

// reads a whole file into memory and returns a slice of its lines.
func readTextFileIntoWordsArray(path string) (string, error) {
	fileBytes, err := os.ReadFile(path) // #nosec G304
	if err != nil {
		return "", err
	}
	err = os.Remove(path)
	if err != nil {
		logutils.CtxLogger(context.Background()).WithError(err).Error("error removing file")
	}
	return string(fileBytes), nil
}

func DownloadPdfAndExtractText(
	ctx context.Context,
	containerClient *azureUtils.ContainerClient,
	reportId string,
) ([][]string, error) {
	filePath, _ := DownloadPDFAndConvertToText(ctx, containerClient, reportId)
	lg := logutils.DebugCtxLogger(ctx).WithField("reportId", reportId)

	var words [][]string
	fileContStr, err := readTextFileIntoWordsArray(filePath)
	if err != nil {
		lg.WithError(err).Error("Failed to import text file")
	} else {
		tmpWords := strings.FieldsFunc(fileContStr, Split)
		words = append(words, tmpWords)
	}
	return words, nil
}
