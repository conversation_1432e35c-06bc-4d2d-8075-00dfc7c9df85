package reports

import (
	"context"
	"database/sql"

	"github.com/sirupsen/logrus"
	sqlObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	scans "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func logReportAccess(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	acceptType string,
	reportId string,
	orgSvc orgservice.OrgService,
) {
	//don't fail if the metadata can't be retrieved, log what is there.
	metadata := scans.Metadata{
		InstitutionName: "unknown",
		Modality:        "unknown",
	}
	reportDefsEnabled := true
	scanId, err := sqlObjects.GetScanId(ctx, db, reportId)
	if err == nil {
		metadata, err = scans.GetScan(ctx, db, scanId)
	}

	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":    reportId,
		"accept":       acceptType,
		"institution":  metadata.InstitutionName,
		"modality":     metadata.Modality,
		"acct_id":      acctId,
		"defs_enabled": reportDefsEnabled,
	}).Info("user report access")
}
