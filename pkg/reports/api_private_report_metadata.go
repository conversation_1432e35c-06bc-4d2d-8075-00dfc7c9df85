package reports

import (
	"net/http"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type privateReportMetadataController struct {
	service coreapi.ReportsApiServicer
}

// This controller is specifically for the GET /reports/{id}/metadata endpoint that supports dual
// authentication with either a JWT auth token or X-PH-Signature header. Like the
// NewPrivateUsersExamsApiController() controller, it uses different middleware than the rest of
// the /reports endpoints.
//
// Important: This controller needs to be registered with the router before the other /reports
// controller, NewPrivateReportsApiController(). This constraint exists because this controller
// uses a path prefix that would otherwise be captured by that controller. Registering this
// controller first ensures that the router knows this route is special.
//
// If ever additional /reports routes need dual authentication, this controller's GetPathPrefix()
// should change to /reports (thereby removing the above constraint) and of course the
// GetReportMetadataById() path should be specified (/{reportId}/metadata).
func NewPrivateReportMetadataController(
	s coreapi.ReportsApiServicer,
) coreapi.PrivateReportMetadataApiRouter {
	return &privateReportMetadataController{service: s}
}

// Routes returns all of the api route for the privateReportMetadataController
func (c *privateReportMetadataController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetReportMetadataById",
			Method:      http.MethodGet,
			HandlerFunc: c.GetReportMetadataById,
		},
	}
}

func (c *privateReportMetadataController) GetPathPrefix() string {
	return "/v1/reports/{reportId}/metadata"
}

func (c *privateReportMetadataController) GetMiddleware() []func(next http.Handler) http.Handler {
	// Note different auth than other /reports routes
	return []func(next http.Handler) http.Handler{auth.ValidateDualAuth}
}

func (c *privateReportMetadataController) GetReportMetadataById(
	w http.ResponseWriter,
	r *http.Request,
) {
	ctx := r.Context()
	params := mux.Vars(r)
	reportId := params["reportId"]

	var acctId string
	var shareId string
	if auth.IsValidatedWithBearerToken(ctx) {
		//both users and share viewers can access reports
		token := r.Header.Get("Authorization")
		var errAcc error
		acctId, errAcc = auth.DecodeAccountToken(token)
		var errSV error
		shareId, _, errSV = auth.DecodeShareViewerToken(token)
		if errAcc != nil && errSV != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}
	}

	result, err := c.service.GetReportMetadataById(ctx, reportId, acctId, shareId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
