/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package reports

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/pubsub"
)

// A PrivateReportsApiController binds http requests to an api service and writes the service results to the http response
type PrivateReportsApiController struct {
	service           coreapi.ReportsApiServicer
	reportTopicSender pubsub.TopicSender
}

// NewPRivateReportsApiController creates a default api controller
func NewPrivateReportsApiController(
	s coreapi.ReportsApiServicer,
	reportTopicSender pubsub.TopicSender,
) coreapi.PrivateReportsApiRouter {
	return &PrivateReportsApiController{service: s, reportTopicSender: reportTopicSender}
}

// Routes returns all of the api route for the PrivateReportsApiController
func (c *PrivateReportsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetReportById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}",
			HandlerFunc: c.GetReportById,
		},
		{
			Name:        "GetTaggedReportById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}/taggedhtml",
			HandlerFunc: c.GetTaggedReportById,
		},
		{
			Name:        "GetUnassociatedReports",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetUnassociatedReports,
		},
		{
			Name:        "GetFollowUpReportById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}/followup",
			HandlerFunc: c.GetFollowUpReportById,
		},
		{
			Name:        "GetV2FollowUpReportById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}/insights/followup",
			HandlerFunc: c.GetV2FollowUpReportById,
		},
		{
			Name:        "GetQuestionsByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}/insights/questions",
			HandlerFunc: c.GetQuestionsByReportId,
		},

		{
			Name:        "GetReportExplanationByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{reportId}/explanation",
			HandlerFunc: c.GetReportExplanationByReportId,
		},
		{
			Name:        "PostReportViews",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{reportId}/views",
			HandlerFunc: c.PostReportViews,
		},
	}
}

func (c *PrivateReportsApiController) GetPathPrefix() string {
	return "/v1/reports"
}

func (c *PrivateReportsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all reports paths require authentication
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

// GetReportById - Get report
func (c *PrivateReportsApiController) GetReportById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportID := params["reportId"]
	accept := r.Header.Get("accept")
	ctx := r.Context()
	//TODO: handle Range header

	// optional query parameter provider id for getting shares for record streaming studies
	hasProviderID, providerID, _ := coreapi.ParseQueryParamInt64(r, "providerId")

	//both users and share viewers can access reports
	token := r.Header.Get("Authorization")
	accountID, errAcc := auth.DecodeAccountToken(token)
	shareId, _, errSV := auth.DecodeShareViewerToken(token)
	if errAcc != nil && errSV != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	var result []byte
	var err error

	// First try to fetch the report using record streaming physician account logic
	if hasProviderID {
		result, err = c.service.GetRecordStreamingReportById(
			ctx,
			providerID,
			accountID,
			reportID, // objectID
			accept,
		)
	}

	// If: A - no report was found OR
	//     B - the record streaming logic failed due to authorization
	// Then this is likely a non-record streamed report
	// Try to fetch it as a share instead
	if len(result) == 0 || (err != nil && err.Error() == errmsg.ERR_NOT_AUTHORIZED) {
		result, err = c.service.GetReportById(ctx, reportID, accept, accountID, shareId)
	}

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else if err.Error() == errmsg.ERR_BAD_QUERY_PARAM {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	if accept == "application/pdf" {
		w.Header().Set("Content-Type", "application/pdf")
	} else if accept == "text/html" {
		w.Header().Set("Content-Type", "text/html")
	} else { //default to png
		w.Header().Set("Content-Type", "image/png")
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(result)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}

}

// GetTaggedReportById - Get report in html, tagged with terms and definitions
func (c *PrivateReportsApiController) GetTaggedReportById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	//only users can see report definitions
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	result, err := c.service.GetTaggedReportById(r.Context(), reportId, acctId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	//result is json, but core doesn't need to unmarshal it, so set type and write bytes.
	w.Header().Set("Content-Type", "application/json")

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(result)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

// GetFollowUpReportById - Get follow up report, if it exists
func (c *PrivateReportsApiController) GetFollowUpReportById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	//only users can see report definitions
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	result, err := c.service.GetFollowUpReportById(r.Context(), reportId, acctId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GetV2FollowUpReportById - Get V2 follow-up report
func (c *PrivateReportsApiController) GetV2FollowUpReportById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	//only users can see report definitions
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	followup, err := c.service.GetV2FollowUpReportById(r.Context(), reportId, acctId)

	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), httperror.ErrormsgToStatus(err.Error()))
		return
	}
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(followup)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

// Get questions for your doctor by report id
func (c *PrivateReportsApiController) GetQuestionsByReportId(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	//only users can see report definitions
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	questions, err := c.service.GetQuestionsByReportId(r.Context(), reportId, acctId)

	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), httperror.ErrormsgToStatus(err.Error()))
		return
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(questions)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}



func (c *PrivateReportsApiController) GetReportExplanationByReportId(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	results, err := c.service.GetReportExplanationByReportId(r.Context(), reportId, acctId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), httperror.ErrormsgToStatus(err.Error()))
		return
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(results)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

func (c *PrivateReportsApiController) GetUnassociatedReports(
	w http.ResponseWriter,
	r *http.Request,
) {

	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetUnassociatedReports(r.Context(), acctId)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateReportsApiController) PostReportViews(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	reportId := params["reportId"]

	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	payloadBytes, err := json.Marshal(&ReportMessageEvent{
		ReportId:  reportId,
		AccountId: acctId,
		ViewedAt:  time.Now().Format(time.RFC3339),
	})
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		return
	}
	err = pubsub.SendTopicEvent(r.Context(), c.reportTopicSender, &ReportEvent{
		Payload:      payloadBytes,
		EventType:    string(pubsub.ViewedEventType),
		Timestamp:    time.Now(),
		EventVersion: 1,
	})
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_PUBLISH, http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}
