package organviz

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func CreateExam(t *testing.T, db *sql.DB, examUuid, acctId string) {
	scanId := examUuid + "_scan"

	// register cleanup first in case any of the `require.NoError` hits and
	// stops execution early
	t.Cleanup(func() {
		_, err := db.Exec("DELETE FROM exams WHERE uuid=?", examUuid)
		assert.NoErrorf(t, err, "failed cleaning up exam %v", examUuid)

		_, err = db.Exec("DELETE FROM scans WHERE scan_id=?", scanId)
		assert.NoErrorf(t, err, "failed cleaning up scans %v", scanId)
	})

	_, err := db.Exec(
		`INSERT INTO exams (
			uuid, account_id, activated, description, transfer_id, has_report,
			exam_uid, referring_physician, date, patient_name, patient_mrn, dob, sex, phone, body_part
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?, ?, ?
		)
		ON DUPLICATE KEY UPDATE
			account_id=VALUES(account_id),
			activated=VALUES(activated),
			description=VALUES(description),
			transfer_id=VALUES(transfer_id),
			has_report=VALUES(has_report)
			`,
		examUuid, acctId, true, "CT ABD", scanId, true,
		// all values below are required by the data model but are unused by these tests
		"foo", "foo", "foo", "foo", "foo", "foo", "foo", "foo", "foo",
	)
	require.NoError(t, err)

	_, err = db.Exec(`
	    INSERT INTO scans (scan_id, source) VALUES (?, "CD")
		ON DUPLICATE KEY UPDATE
			scan_id=VALUES(scan_id)
		`,
		scanId,
	)
	require.NoError(t, err)
}

func addImagesToSeries(t *testing.T, db *sql.DB, examUuid, seriesUid string, numImagesToAdd int, imageOrientation string) []string {
	t.Helper()
	if numImagesToAdd == 0 {
		return []string{}
	}

	imageNames := make([]string, numImagesToAdd)
	for i := 0; i < numImagesToAdd; i++ {
		imageName := fmt.Sprintf("%v_img-%v", seriesUid, i)
		imageNames[i] = imageName
	}

	// register cleanup first in case any of the `require.NoError` hits and
	// stops execution early
	t.Cleanup(func() {
		qs := make([]string, len(imageNames))
		interfaceArr := make([]interface{}, len(imageNames))
		for i := range qs {
			qs[i] = "?"
			interfaceArr[i] = imageNames[i]
		}

		_, err := db.Exec(fmt.Sprintf(
			"DELETE FROM object_mappings where object_id IN (%s)",
			strings.Join(qs, ","),
		), interfaceArr...)
		assert.NoError(t, err)

		_, err = db.Exec(fmt.Sprintf(
			"DELETE FROM view_metadata where object_id IN (%s)",
			strings.Join(qs, ","),
		), interfaceArr...)
		assert.NoError(t, err)

		_, err = db.Exec(fmt.Sprintf(
			"DELETE FROM objects where object_id IN (%s)",
			strings.Join(qs, ","),
		), interfaceArr...)
		assert.NoError(t, err)
	})

	// start transaction adding images to both `objects` and `object_mappings`
	tx, err := db.BeginTx(context.Background(), nil)
	require.NoError(t, err)
	for i, imageName := range imageNames {
		_, err = tx.Exec(
			`INSERT INTO objects (object_id, instance_number, is_report) VALUES (?, ?, 0)
			ON DUPLICATE KEY UPDATE instance_number=VALUES(instance_number), is_report=VALUES(is_report)`,
			imageName, i,
		)
		require.NoError(t, err)
		_, err = tx.Exec(
			`INSERT INTO object_mappings (object_id, exam_uuid, series_uid) VALUES (?, ?, ?)
			ON DUPLICATE KEY UPDATE exam_uuid=VALUES(exam_uuid), series_uid=VALUES(series_uid)`,
			imageName, examUuid, seriesUid,
		)
		require.NoError(t, err)
		_, err = tx.Exec(
			`INSERT INTO view_metadata (object_id, image_orientation) VALUES (?, ?)
			ON DUPLICATE KEY UPDATE image_orientation=VALUES(image_orientation)`,
			imageName, imageOrientation,
		)
		require.NoError(t, err)
	}
	err = tx.Commit()
	require.NoError(t, err)

	return imageNames
}

func addViewMetaDataToObject(t *testing.T, db *sql.DB, objectId string, metadata map[string]any) {
	t.Cleanup(func() {
		_, err := db.Exec(`DELETE FROM view_metadata where object_id=?`, objectId)
		assert.NoError(t, err)
	})

	metadata["object_id"] = objectId

	var columns []string
	var valuePlaceholders []string
	var values []any
	var onDuplicate []string
	for column, value := range metadata {
		columns = append(columns, column)
		valuePlaceholders = append(valuePlaceholders, "?")
		values = append(values, value)
		onDuplicate = append(onDuplicate, fmt.Sprintf("%s=VALUES(%s)", column, column))
	}

	_, err := db.Exec(
		fmt.Sprintf(
			`INSERT INTO view_metadata (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s`,
			strings.Join(columns, ","),
			strings.Join(valuePlaceholders, ","),
			strings.Join(onDuplicate, ", "),
		),
		values...,
	)
	require.NoError(t, err)
}

func createSeries(t *testing.T, db *sql.DB, examUuid, seriesUid, seriesDescription string) {
	t.Helper()

	t.Cleanup(func() {
		_, err := db.Exec(
			"DELETE FROM series where series_uid = ? and exam_uuid = ?", seriesUid, examUuid)
		assert.NoError(t, err)
	})

	_, err := db.Exec(
		`INSERT INTO series (series_uid, series_description, exam_uuid) VALUES (?, ?, ?)
		ON DUPLICATE KEY UPDATE series_description=VALUES(series_description)`,
		seriesUid, seriesDescription, examUuid,
	)
	require.NoError(t, err)
}

func asPointer[T any](t T) *T { return &t }
