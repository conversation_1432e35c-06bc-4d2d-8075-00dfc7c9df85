package orientation

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetCleanedOrientationVectors(t *testing.T) {
	testCases := []struct {
		input     string
		expected  Orientation
		expectErr bool
	}{{
		input:    `-1/0/1/0/1/0`,
		expected: Orientation{-1, 0, 1, 0, 1, 0},
	}, {
		input:    `-1\0\1\0\1\0`,
		expected: Orientation{-1, 0, 1, 0, 1, 0},
	}, {
		input:    `0.2/0.02/0.002/1.2/1.02/1.002`,
		expected: Orientation{0, 0, 0, 1, 1, 1},
	}, {
		input:    `-0.2/-0.02/-0.002/-1.2/-1.02/-1.002`,
		expected: Orientation{0, 0, 0, -1, -1, -1},
	}, {
		input:    `0.4/0.5/1.4/1.5/-1.4/-1.5`,
		expected: Orientation{0, 1, 1, 1, -1, -1},
	}, {
		input:     `a/b/c/d/e/f`,
		expectErr: true,
	}}

	for _, testCase := range testCases {
		t.Run(fmt.Sprintf("clean orientation vector %v", testCase.input), func(t *testing.T) {
			actual, err := getCleanedOrientationVectors(testCase.input)

			if testCase.expectErr {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, testCase.expected, actual)
			}
		})
	}
}

func TestParseImageOrientation(t *testing.T) {
	testCases := []struct {
		input    string
		expected Orientation
	}{
		{
			input:    `1/0/0/0/0/-1`,
			expected: CORONAL,
		}, {
			input:    `0/1/0/0/0/-1`,
			expected: SAGITTAL,
		}, {
			input:    `1/0/0/0/1/0`,
			expected: AXIAL,
		},

		{
			input:    `1\0\0\0\0\-1`,
			expected: CORONAL,
		},

		{
			input:    `1/1/1/1/1/1`,
			expected: UNKNOWN_ORIENTATION,
		},
		{
			input:    `random stuff`,
			expected: UNKNOWN_ORIENTATION,
		}}

	for _, testCase := range testCases {
		t.Run(fmt.Sprintf("vectors %v => %v", testCase.input, testCase.expected), func(t *testing.T) {
			actual, _ := ParseImageOrientation(testCase.input)
			assert.Equal(t, testCase.expected, actual)
		})
	}
}
