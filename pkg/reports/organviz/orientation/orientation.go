package orientation

import (
	"fmt"
	"math"
	"strconv"
	"strings"
)

type Orientation [6]int

var (
	UNKNOWN_ORIENTATION Orientation = [6]int{}
	AXIAL               Orientation = [6]int{1, 0, 0, 0, 1, 0}
	CORONAL             Orientation = [6]int{1, 0, 0, 0, 0, -1}
	SAGITTAL            Orientation = [6]int{0, 1, 0, 0, 0, -1}
)

func ParseImageOrientation(rawOrientation string) (Orientation, error) {
	// DICOM orientations are represented as a set of numbers separated by slashes
	// like "1/0/0/0/0/-1" or "0/0.9/0/0.0002/0/-0.2".
	//
	// "1/0/0/0/0/-1" means coronal
	// "0/1/0/0/0/-1" means sagittal
	// "1/0/0/0/1/0" means axial

	nums, err := getCleanedOrientationVectors(rawOrientation)
	if err != nil {
		return UNKNOWN_ORIENTATION, err
	}

	switch nums {
	case AXIAL:
		return AXIAL, nil
	case CORONAL:
		return CORONAL, nil
	case SAGITTAL:
		return SAGITTAL, nil
	default:
		return UNKNOWN_ORIENTATION, nil
	}
}

func getCleanedOrientationVectors(rawOrientation string) (Orientation, error) {
	// take the raw orientation and round the numbers so that
	// 0/0.9/0/0.002/0/-0.2 becomes 0/1/0/0/0/-1
	rawNums, err := getRawVectors(rawOrientation)
	if err != nil {
		return UNKNOWN_ORIENTATION, err
	}

	nums := make([]float64, len(rawNums))
	for i, rawNum := range rawNums {
		num, err := strconv.ParseFloat(rawNum, 64)
		if err != nil {
			return UNKNOWN_ORIENTATION, fmt.Errorf("failed parsing %v as float64: %w", rawNum, err)
		}
		nums[i] = num
	}

	var ints [6]int
	for i, num := range nums {
		// round the number to the nearest int
		num = math.Round(num)

		// we only want numbers in the [-1, 1] range
		if num < -1 {
			num = -1
		} else if num > 1 {
			num = 1
		}

		ints[i] = int(num)
	}

	return ints, nil
}

func getRawVectors(rawOrientation string) ([]string, error) {
	if strings.Contains(rawOrientation, `/`) {
		return strings.Split(rawOrientation, `/`), nil
	} else if strings.Contains(rawOrientation, `\`) {
		return strings.Split(rawOrientation, `\`), nil
	}

	return nil, fmt.Errorf("invalid image orientation format: %s", rawOrientation)
}

func (o Orientation) ToString() string {
	toReturn := make([]string, len(o))
	for i, v := range o {
		toReturn[i] = strconv.Itoa(v)
	}

	return strings.Join(toReturn, "/")
}
