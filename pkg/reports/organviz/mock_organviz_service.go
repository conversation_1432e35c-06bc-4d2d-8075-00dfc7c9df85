package organviz

import (
	"context"

	"github.com/samber/lo"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

type MockOrganvizService struct {
	EligibleExams     []string
	FetchedVizForExams []string
}

func (s *MockOrganvizService) IsExamEligible(ctx context.Context, exam coreapi.ExamRawBasic, interactive bool) (ExamEligibility, error) {
	if !lo.Contains(s.EligibleExams, exam.UUID) {
		return UNKNOWN, nil
	}
	return ELIGIBLE_FOR_MEDSAM, nil
}

func (s *MockOrganvizService) FetchVisualizationsForExam(ctx context.Context, examId, accountId string, eligibility ExamEligibility, interactive bool) ([]byte, error) {
	s.FetchedVizForExams = append(s.FetchedVizForExams, examId)
	return []byte{}, nil
}
