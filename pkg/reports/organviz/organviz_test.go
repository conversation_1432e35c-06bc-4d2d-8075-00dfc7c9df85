package organviz

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPopulateViewPosition(t *testing.T) {
	testCases := map[string]struct {
		input                seriesData
		expectedViewPosition string
	}{
		"PA, keep as it is, ignore description": {
			input: seriesData{
				viewPosition: "PA",
				description:  "CHEST LATERAL",
			},
			expectedViewPosition: "PA",
		},
		"LATERAL, populate as LAT": {
			input: seriesData{
				viewPosition: "LATERAL",
				description:  "CHEST LATERAL",
			},
			expectedViewPosition: "LAT",
		},
		"OBLIQUE, populate as OBL": {
			input: seriesData{
				viewPosition: "OBLIQUE",
			},
			expectedViewPosition: "OBL",
		},
		"empty view position but AP in description, populate as AP": {
			input: seriesData{
				viewPosition: "",
				description:  "CHEST AP view",
			},
			expectedViewPosition: "AP",
		},
		"empty view position and description, nothing get populated": {
			input: seriesData{
				viewPosition: "",
				description:  "",
			},
			expectedViewPosition: "",
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			populateViewPosition(&testCase.input)
			assert.Equal(t, testCase.input.viewPosition, testCase.expectedViewPosition)
		})
	}
}

func TestFindHighestPrioritySeriesBySeriesDesc(t *testing.T) {
	testCases := map[string]struct {
		input          []*seriesData
		expectedSorted []*seriesData
	}{
		"series LOWER should has lower priority ": {
			input: []*seriesData{
				&seriesData{
					description: "AX T1 DIXON LOWER",
				},
				&seriesData{
					description: "AX T1 DIXON UPPER",
				},
				&seriesData{
					description: "AX VIBE PRE",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX T1 DIXON UPPER",
				},
				&seriesData{
					description: "AX VIBE PRE",
				},
			},
		},
		"VIBE has higher priority than T2": {
			input: []*seriesData{
				&seriesData{
					description: "AX T2 HASTE POST CONTRAST",
				},
				&seriesData{
					description: "DIXON VIBE LOWER",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "DIXON VIBE LOWER",
				},
			},
		},
		"exclude DWI/ADC/DIFFUSION": {
			input: []*seriesData{
				&seriesData{
					description: "AX_ssDWI",
				},
				&seriesData{
					description: "eADC AX",
				},
				&seriesData{
					description: "AX DIFFUSION_FB",
				},
			},
			expectedSorted: []*seriesData{},
		},
		"exclude BRAIN series": {
			input: []*seriesData{
				&seriesData{
					description: "AX BRAIN",
				},
				&seriesData{
					description: "AX ABD",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX ABD",
				},
			},
		},
		"exclude _SUB": {
			input: []*seriesData{
				&seriesData{
					description: "AX T1 VIBE FS 2 MIN_REG_SUB",
				},
				&seriesData{
					description: "AX T1 VIBE FS 2 MIN_REG",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX T1 VIBE FS 2 MIN_REG",
				},
			},
		},
		"exclude _F and _fat": {
			input: []*seriesData{
				&seriesData{
					description: "AX T1 VIBE_W",
				},
				&seriesData{
					description: "AX T1 VIBE_F",
				},
				&seriesData{
					description: "AX T1 VIBE_FS",
				},
				&seriesData{
					description: "AX T1 VIBE_fat",
				},
				&seriesData{
					description: "AX T1 VIBE_f_comp",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX T1 VIBE_W",
				},
				&seriesData{
					description: "AX T1 VIBE_FS",
				},
			},
		},
		"_W should has higher priority than T2": {
			input: []*seriesData{
				&seriesData{
					description: "ax dixon_W",
				},
				&seriesData{
					description: "AX t2 haste",
				},
				&seriesData{
					description: "AX haste",
				},
				&seriesData{
					description: "ax_Water",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "ax dixon_W",
				},
				&seriesData{
					description: "ax_Water",
				},
			},
		},
		"_WATS doesnt count as _W": {
			input: []*seriesData{
				&seriesData{
					description: "ax dixon_W",
				},
				&seriesData{
					description: "AX t2 haste",
				},
				&seriesData{
					description: "AX haste",
				},
				&seriesData{
					description: "AX haste_WATS",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "ax dixon_W",
				},
			},
		},
		"_W_COMP should count": {
			input: []*seriesData{
				&seriesData{
					description: "ax dixon_w_comp",
				},
				&seriesData{
					description: "AX t2 haste",
				},
				&seriesData{
					description: "AX haste",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "ax dixon_w_comp",
				},
			},
		},
		"t2 should has higher priority than other random sequence": {
			input: []*seriesData{
				&seriesData{
					description: "DYNAMIC LINK",
				},
				&seriesData{
					description: "AX haste",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX haste",
				},
			},
		},
		"all random sequence": {
			input: []*seriesData{
				&seriesData{
					description: "DYNAMIC LINK",
				},
				&seriesData{
					description: "SURVEY",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "DYNAMIC LINK",
				},
				&seriesData{
					description: "SURVEY",
				},
			},
		},
		"should be case insensitive": {
			input: []*seriesData{
				&seriesData{
					description: "ax dixon_w",
				},
				&seriesData{
					description: "AX t1 post",
				},
				&seriesData{
					description: "AX lava pre",
				},
			},
			expectedSorted: []*seriesData{
				&seriesData{
					description: "AX t1 post",
				},
				&seriesData{
					description: "AX lava pre",
				},
			},
		},
	}
	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			sorted := findHighestPrioritySeriesBySeriesDesc(testCase.input)
			if !seriesDataEqual(testCase.expectedSorted, sorted) {
				t.Error(
					"sorted series does not equal to expected series",
				)
			}
		})
	}
}

func seriesDataEqual(a, b []*seriesData) bool {
	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] == nil && b[i] == nil {
			continue
		}
		if a[i] == nil || b[i] == nil {
			return false
		}
		if !reflect.DeepEqual(*a[i], *b[i]) {
			return false
		}
	}

	return true
}
