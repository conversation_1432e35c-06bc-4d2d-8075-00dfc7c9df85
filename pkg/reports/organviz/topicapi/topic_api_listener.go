package topicapi

import (
	"context"
	"database/sql"
	"fmt"
	"slices"
	"strings"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/pubsub"
)

// Message represents the data being read from the topic
type Message struct {
	NewState string `json:"new_state"`
	ExamUUID string `json:"exam_uuid"`
}

type Listener struct {
	organvizSvc    organviz.Service
	examService    exams.ExamServiceInterface
	pubsubListener pubsub.ListenerRunner[Message]
}

// StartTopicListener starts the organviz topic API
func StartTopicListener(
	ctx context.Context,
	serviceBusName, topicName, subscriptionName string,
	db *sql.DB,
	organvizService organviz.Service,
	examService exams.ExamServiceInterface,
) {
	topicListener, err := pubsub.NewTopicListener[Message](serviceBusName, topicName, subscriptionName)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"service_bus_name":  serviceBusName,
			"topic_name":        topicName,
			"subscription_name": subscriptionName,
		}).WithError(err).Fatal("failed connecting to organviz trigger topic")
	}
	organvizTrigger, err := NewListener(
		organvizService,
		examService,
		topicListener,
	)
	if err != nil {
		logrus.WithError(err).Fatal("failed creating organviz topic trigger")
	}
	organvizTrigger.Start(ctx)
}

// NewListener creates a new object ready to receive messages from a topic
func NewListener(
	organvizSvc organviz.Service,
	examService exams.ExamServiceInterface,
	listener pubsub.ListenerRunner[Message],
) (*Listener, error) {
	return &Listener{
		organvizSvc:    organvizSvc,
		examService:    examService,
		pubsubListener: listener,
	}, nil
}

func (ts *Listener) Start(ctx context.Context) {
	lg := logutils.CtxLogger(ctx)
	go func() {
		err := ts.pubsubListener.Run(ctx, ts.onNewMessage)
		if err != nil {
			lg.WithError(err).Error("Organviz topic listener encountered an error")
		}
	}()
}

func (ts *Listener) onNewMessage(ctx context.Context, message Message) error {
	logutils.CtxLogger(ctx).WithField("message", message).Info("organviz trigger received message")

	switch strings.ToUpper(message.NewState) {
	case "CREATED":
		return ts.triggerInferenceForExam(ctx, message.ExamUUID)
	}
	return nil
}

func (ts *Listener) triggerInferenceForExam(ctx context.Context, examUuid string) error {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid": examUuid,
	})

	exam, err := ts.examService.GetExamSummaryDataForRecordStreamingStudy(ctx, examUuid, -2 /* provider id; unused */)
	if err != nil {
		return fmt.Errorf("failed fetching exam %v: %w", examUuid, err)
	}

	eligibility, err := ts.organvizSvc.IsExamEligible(
		ctx,
		exam.ExamRawBasic,
		false, // interactive; only non-interactive masks in async organviz for now
	)
	if err != nil {
		return fmt.Errorf("failed checking if exam is eligible: %w", err)
	}
	if !slices.Contains([]organviz.ExamEligibility{organviz.ELIGIBLE_FOR_MEDSAM, organviz.ELIGIBLE_FOR_MRI, organviz.ELIGIBLE_FOR_XRAY}, eligibility) {
		lg.WithFields(logrus.Fields{
			"provider":    exam.Provider,
			"provider-id": exam.ProviderId,
			"reason":      eligibility,
		}).Info("Exam is not eligible for organ visualization")
		return nil
	}

	_, err = ts.organvizSvc.FetchVisualizationsForExam(
		ctx,
		examUuid,
		exam.AccountId,
		eligibility,
		false, // interactive; only non-interactive masks in async organviz for now
	)
	if err != nil {
		lg.WithError(err).Warn("Failed triggering inference for exam")
		return fmt.Errorf("failed to trigger inference for exam: %w", err)
	}
	return nil
}
