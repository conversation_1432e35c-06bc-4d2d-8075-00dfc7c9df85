package organviz

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	sqlObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz/orientation"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type ConvertImageFunc func(ctx context.Context, imageId string, accept string, auth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error)

const (
	secondaryCaptureImageStorage string = "1.2.840.10008.*******.1.7"
)

type ExamEligibility string

const (
	UNKNOWN              = "UNKNOWN"
	INELIGIBLE_BODY_PART = "BODY_PART"
	INELIGIBLE_MODALITY  = "MODALITY"
	INELIGIBLE_AGE       = "AGE"
	ELIGIBLE_FOR_MEDSAM  = "ELIGIBLE_FOR_MEDSAM"
	ELIGIBLE_FOR_XRAY    = "ELIGIBLE_FOR_XRAY"
	ELIGIBLE_FOR_MRI     = "ELIGIBLE_FOR_MRI"
)

type seriesData struct {
	seriesUid    string
	orientation  orientation.Orientation
	viewPosition string
	sopClass     string
	numImages    int
	description  string
}

type Service interface {
	IsExamEligible(ctx context.Context, exam coreapi.ExamRawBasic, interactive bool) (ExamEligibility, error)
	FetchVisualizationsForExam(ctx context.Context, examId string, accountId string, eligibility ExamEligibility, interactive bool) ([]byte, error)
}

type OrganVizModel interface {
	FetchVisualizations(ctx context.Context, examId string, interactive bool) ([]byte, error)
}

type Client struct {
	containerClient *azureUtils.ContainerClient
	db              *sql.DB
	convertImage    ConvertImageFunc
	waitGroup       *sync.WaitGroup
	models          map[string]OrganVizModel
}

func NewService(
	containerClient *azureUtils.ContainerClient,
	db *sql.DB,
	convertImageFunc ConvertImageFunc,
	waitGroup *sync.WaitGroup,
	riClient reportinsights.ReportInsights,
) *Client {
	models := setupOrganvizModels(riClient)
	return &Client{
		containerClient: containerClient,
		db:              db,
		convertImage:    convertImageFunc,
		waitGroup:       waitGroup,
		models:          models,
	}
}

func (s *Client) IsExamEligible(ctx context.Context, exam coreapi.ExamRawBasic, interactive bool) (ExamEligibility, error) {
	if isCTScan(exam) && !interactive {
		return isExamEligibleForCT(exam)
	} else if isXRAYScan(exam) {
		return isExamEligibleForXray(exam)
	} else if isMRIScan(exam) && !interactive {
		return isExamEligibleForMRI(exam)
	} else {
		return INELIGIBLE_MODALITY, nil
	}
}

func bodyPartMatches(exam coreapi.ExamRawBasic, bodyParts ...string) bool {
	haystack := exam.BodyPart
	if exam.BodyPart == "" || strings.ToLower(exam.BodyPart) == "unknown" {
		// fall back to looking at the exam description
		haystack = exam.Description
	}

	haystack = strings.ToLower(haystack)
	for _, bodyPart := range bodyParts {
		if strings.Contains(haystack, strings.ToLower(bodyPart)) {
			return true
		}
	}
	return false
}

func (s *Client) isExamPrepared(ctx context.Context, examId string) (bool, error) {
	// `PrepareExam` uploads a number of images named "organviz/input/{examId}_{objectId}".
	// If any file matching this prefix exists in the RI container, we assume
	// preparation completed successfully.
	return blobWithPrefixExists(ctx, s.containerClient.ReportInsight, getBlobPrefix(examId))
}

func getBlobPrefix(examId string) string {
	return fmt.Sprintf("organviz/input/%v", examId)
}

func blobWithPrefixExists(ctx context.Context, containerClient *container.Client, prefix string) (bool, error) {
	pager := containerClient.NewListBlobsFlatPager(&container.ListBlobsFlatOptions{
		Prefix: &prefix,
	})

	if pager.More() {
		res, err := pager.NextPage(ctx)
		if err != nil {
			return false, fmt.Errorf("failed listing blobs in container: %w", err)
		}

		return len(res.Segment.BlobItems) > 0, nil
	}

	return false, nil
}

func (s *Client) prepareExam(ctx context.Context, examUuid, acctId string, eligibility ExamEligibility) error {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid":   examUuid,
		"account_id":  acctId,
		"eligibility": eligibility,
	})

	objectIds, err := s.selectImages(ctx, examUuid, acctId, eligibility)
	if err != nil {
		return fmt.Errorf("failed selecting images: %w", err)
	}

	if len(objectIds) == 0 {
		lg.Info("no object selected from the series")
		return errors.New("no object selected from the series")
	}

	useVoiWindow := eligibility == ELIGIBLE_FOR_MEDSAM

	err = s.convertAndUploadImages(ctx, examUuid, objectIds, "image/png", useVoiWindow)
	if err != nil {
		return fmt.Errorf("failed converting and uploading images: %w", err)
	}

	return nil
}

func (s *Client) selectImages(
	ctx context.Context,
	examUuid, acctId string,
	eligibility ExamEligibility,
) ([]string, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"examUuid":    examUuid,
		"accountId":   acctId,
		"eligibility": eligibility,
	})

	var seriesUid *string
	var err error
	if eligibility == ELIGIBLE_FOR_XRAY {
		seriesUid, err = s.selectValidSeriesForXRAY(ctx, examUuid)
	} else if eligibility == ELIGIBLE_FOR_MRI {
		seriesUid, err = s.selectValidSeriesForMRI(ctx, examUuid)
	} else {
		seriesUid, err = s.selectValidSeriesForMedSAM(ctx, examUuid)
	}
	if err != nil {
		return nil, fmt.Errorf("failed selecting series: %w", err)
	}
	lg = lg.WithField("series_uid", seriesUid)
	if seriesUid == nil {
		lg.Infof("no suitable series found for exam %v", examUuid)
		return nil, nil
	}

	objectsInSeries, err := sqlObjects.GetObjectsInSeries(ctx, s.db, examUuid, *seriesUid)
	if err != nil {
		return nil, fmt.Errorf("failed reading objects in exam %v and series %v: %w", examUuid, seriesUid, err)
	}

	numImagesInSeries := len(objectsInSeries)
	numImagesToChoose := 5
	if numImagesInSeries <= numImagesToChoose {
		// there are fewer images in the series than we want to choose, just
		// select them all
		return objectsInSeries, nil
	}

	// skip the first and last 10% of images in the series
	skipStartEnd := int(float64(numImagesInSeries) * 0.1)
	step := (numImagesInSeries - 2*skipStartEnd) / (numImagesToChoose - 1)

	selectedObjects := make([]string, 0, numImagesToChoose)
	for i := 0; i < numImagesToChoose; i++ {
		objectIndex := skipStartEnd + i*step - 1
		if objectIndex < 0 {
			objectIndex = 0
			skipStartEnd++
		}
		if objectIndex >= len(objectsInSeries) {
			// In certain cases where `numObjectsInSeries % numImagesToChoose == step size + skip`
			// we run into an edge case where we try to select an image not in
			// `objectsInSeries`. This is okay, and we can simply return the
			// images we've successfully selected
			break
		}
		objectId := objectsInSeries[objectIndex]

		// ensure account can access object
		hasAccess, err := s.canAccessObject(ctx, objectId, acctId)
		if err != nil {
			return nil, fmt.Errorf("failed checking if user had access to object %v: %w", objectId, err)
		}

		if hasAccess {
			selectedObjects = append(selectedObjects, objectId)
		} else {
			lg.WithField("objectId", objectId).Warn("user does not have access to image")
		}
	}

	return selectedObjects, nil
}

func (s *Client) getSeriesInExam(ctx context.Context, examUuid string) ([]string, error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		s.db,
		"SELECT distinct series_uid FROM object_mappings WHERE exam_uuid=? AND series_uid IS NOT NULL",
		examUuid,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var series []string
	for rows.Next() {
		var seriesUid sql.NullString
		err := rows.Scan(&seriesUid)
		if err != nil {
			return nil, fmt.Errorf("failed scanning series uid from result set: %w", err)
		}

		if seriesUid.Valid {
			series = append(series, seriesUid.String)
		}
	}
	return series, nil
}

func (s *Client) sortSeriesByImgCountDesc(ctx context.Context, series []string) error {
	args := make([]any, len(series))
	placeholders := make([]string, len(series))
	for i := range placeholders {
		args[i] = series[i]
		placeholders[i] = "?"
	}

	rows, err := mysqlWithLog.Query(
		ctx,
		s.db,
		fmt.Sprintf(`
			SELECT series_uid, count(1) as count
			  FROM pockethealth.object_mappings
             WHERE series_uid in (%s)
          GROUP BY series_uid
			`,
			strings.Join(placeholders, ","),
		),
		args...,
	)
	if err != nil {
		return fmt.Errorf("failed querying object mappings: %w", err)
	}
	defer rows.Close()

	counts := make(map[string]int)
	for rows.Next() {
		var seriesUid string
		var count int
		err := rows.Scan(&seriesUid, &count)
		if err != nil {
			return fmt.Errorf("failed scanning object mappings: %w", err)
		}
		counts[seriesUid] = count
	}

	sort.SliceStable(series, func(a, b int) bool {
		return counts[series[a]] > counts[series[b]]
	})
	return nil
}

func (s *Client) getSeriesData(ctx context.Context, examUuid string, seriesUid string) (*seriesData, error) {
	objectId, err := s.getArbitraryObjectInSeries(ctx, seriesUid, examUuid)
	if err != nil {
		return nil, fmt.Errorf("failed finding object in series %v: %w", seriesUid, err)
	}

	lg := logutils.CtxLogger(ctx)

	var rawOrientation, sopClass sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		s.db,
		`SELECT image_orientation, sop_class_uid
		   FROM view_metadata
		  WHERE object_id=?`,
		[]interface{}{
			&rawOrientation,
			&sopClass,
		},
		objectId,
	)
	if err != nil {
		return nil, fmt.Errorf("failed reading metadata for object %v: %w", objectId, err)
	}

	var o orientation.Orientation
	if rawOrientation.Valid && rawOrientation.String != "" {
		o, err = orientation.ParseImageOrientation(rawOrientation.String)
		if err != nil {
			return nil, fmt.Errorf("failed parsing orientation '%v': %w", rawOrientation.String, err)
		}
	} else {
		lg.WithField("object_id", objectId).Warn("no orientation data stored in the database")
		// successfully fetched data from the db, but it was null so we still
		// don't know the orientation
		o = orientation.UNKNOWN_ORIENTATION
	}

	return &seriesData{
		orientation: o,
		sopClass:    sopClass.String,
	}, nil
}

func (s *Client) getArbitraryObjectInSeries(ctx context.Context, seriesUid string, examUuid string) (string, error) {
	var objectId string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		s.db,
		"SELECT object_id FROM object_mappings WHERE series_uid=? and exam_uuid=? and (has_pixel_data =1 or has_pixel_data is null) LIMIT 1", // we only care about actual image, not report
		[]interface{}{&objectId},
		seriesUid,
		examUuid,
	)
	return objectId, err
}

func (s *Client) getObjectMetadataInEachSeriesByExam(ctx context.Context, examUuid string) ([]seriesData, error) {
	rows, err := mysqlWithLog.Query(ctx, s.db, `select 
	vm.view_position, vm.sop_class_uid, vm.image_orientation, om.series_uid, s.series_description
	from object_mappings om 
	join view_metadata vm on om.object_id=vm.object_id
	join objects o on o.object_id=om.object_id
	join series s on s.series_uid=om.series_uid and s.exam_uuid =om.exam_uuid
	where om.exam_uuid=? and (om.has_pixel_data = 1 or om.has_pixel_data is null) and o.is_report=0 group by om.series_uid`, examUuid)
	if err != nil {
		return []seriesData{}, fmt.Errorf("failed to query object_mapping: %w", err)
	}

	seriesList := make([]seriesData, 0)
	defer rows.Close()

	for rows.Next() {
		var viewPosition, sopClass, seriesDescription, imageOrientation sql.NullString
		var seriesUid string
		err := rows.Scan(&viewPosition, &sopClass, &imageOrientation, &seriesUid, &seriesDescription)
		if err != nil {
			return []seriesData{}, fmt.Errorf("failed scanning object mappings: %w", err)
		}

		var o orientation.Orientation
		if imageOrientation.Valid && imageOrientation.String != "" {
			o, err = orientation.ParseImageOrientation(imageOrientation.String)
			if err != nil {
				return nil, fmt.Errorf("failed parsing orientation '%v': %w", imageOrientation.String, err)
			}
		} else {
			// successfully fetched data from the db, but it was null so we still
			// don't know the orientation
			o = orientation.UNKNOWN_ORIENTATION
		}
		seriesList = append(seriesList, seriesData{
			seriesUid:    seriesUid,
			sopClass:     sopClass.String,
			viewPosition: viewPosition.String,
			description:  seriesDescription.String,
			orientation:  o,
		})
	}
	return seriesList, nil
}

func (s *Client) canAccessObject(ctx context.Context, objectId, acctId string) (bool, error) {
	hasAccess, err := sqlObjects.CanAccessObject(ctx, s.db, objectId, acctId, "")
	if err != nil {
		if err == sql.ErrNoRows {
			return false, errors.New(errormsgs.ERR_NOT_FOUND)
		}
		return false, err
	}

	if hasAccess {
		if !sqlObjects.IsActivated(ctx, s.db, objectId) {
			return false, fmt.Errorf("%s: %s", errormsgs.ERR_NEEDS_PURCHASE, objectId)
		}
	}

	return hasAccess, nil
}

func (s *Client) convertAndUploadImages(
	ctx context.Context,
	examId string,
	objectIds []string,
	format string,
	useVoiWindow bool,
) error {
	for _, objectId := range objectIds {
		imageReader, _, err := s.convertImage(
			ctx,
			objectId,
			format,
			true, /* auth status, only used for logging */
			*s.containerClient,
			s.db,
			s.waitGroup,
			useVoiWindow, // useVoiWindow
		)
		if err != nil {
			return fmt.Errorf("failed converting object %v to image: %w", objectId, err)
		}

		blobName := fmt.Sprintf("%s_%s", getBlobPrefix(examId), objectId)
		err = azureUtils.UploadBlobStream(ctx, s.containerClient.ReportInsight, blobName, imageReader)
		if err != nil {
			return fmt.Errorf("unable to upload image %v to reportinsight storage: %w", objectId, err)
		}
	}

	return nil
}

func (s *Client) fetchVisualizations(ctx context.Context, examId string, eligibility ExamEligibility, interactive bool) ([]byte, error) {
	service, _ := s.getOrganvizModel(eligibility)
	if service == nil {
		logutils.CtxLogger(ctx).Error("no implementation to use")
		return nil, fmt.Errorf("no implementation to use")
	}
	return service.FetchVisualizations(ctx, examId, interactive)
}

func (s *Client) FetchVisualizationsForExam(ctx context.Context, examId string, accountId string, eligibility ExamEligibility, interactive bool) ([]byte, error) {
	isPreparedAlready, err := s.isExamPrepared(ctx, examId)
	if err != nil {
		return nil, fmt.Errorf("unable to tell if exam is prepared for organviz or not: %w", err)
	}

	if !isPreparedAlready {
		err = s.prepareExam(ctx, examId, accountId, eligibility)
		if err != nil {
			if err.Error() == "no object selected from the series" {
				// its not actually an error, but also no need to proceed if no object is selected for segmentation
				return nil, nil
			}
			return nil, fmt.Errorf("failed preparing exam for organ visualization: %w", err)
		}
	}

	// trigger inference by requesting visualizations, but not using the results
	b, err := s.fetchVisualizations(ctx, examId, eligibility, interactive)
	if err != nil {
		return nil, fmt.Errorf("failed triggering organviz inference for exam: %w", err)
	}

	return b, nil
}

func setupOrganvizModels(riClient reportinsights.ReportInsights) map[string]OrganVizModel {
	ctmedsam := NewOrganVizCTMedsam(riClient)
	xray := NewOrganVizXRAY(riClient)
	mri := NewOrganVizMRIMedsam(riClient)
	models := map[string]OrganVizModel{
		"ct_abd":     ctmedsam,
		"xray_chest": xray,
		"mri_abd":    mri,
	}
	return models
}

func (s *Client) getOrganvizModel(eligibility ExamEligibility) (OrganVizModel, error) {
	if eligibility == ELIGIBLE_FOR_MEDSAM {
		model, ok := (s.models)["ct_abd"]
		if !ok {
			return nil, errors.New("ct medsam implementation not found")
		}
		return model, nil
	} else if eligibility == ELIGIBLE_FOR_XRAY {
		model, ok := (s.models)["xray_chest"]
		if !ok {
			return nil, errors.New("xray implementation not found")
		}
		return model, nil
	} else if eligibility == ELIGIBLE_FOR_MRI {
		model, ok := (s.models)["mri_abd"]
		if !ok {
			return nil, errors.New("mri implementation not found")
		}
		return model, nil
	}
	return nil, nil
}

func patientAgeAtTimeOfExam(exam coreapi.ExamRawBasic) (int, error) {
	birthDate, err := time.Parse(coreapi.DICOMDateFormat, exam.DICOMBirthDate)
	if err != nil {
		return 0, fmt.Errorf("failed parsing birth date '%s': %v", exam.DICOMBirthDate, err)
	}

	examDate, err := time.Parse(coreapi.DICOMDateFormat, exam.DICOMExamDate)
	if err != nil {
		return 0, fmt.Errorf("failed parsing exam date '%s': %v", exam.DICOMExamDate, err)
	}

	return datetime.CalculateAgeAt(birthDate, examDate), nil
}
