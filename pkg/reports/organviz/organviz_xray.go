package organviz

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type OrganVizXRAY struct {
	riClient reportinsights.ReportInsights
}

func NewOrganVizXRAY(riClient reportinsights.ReportInsights) *OrganVizXRAY {
	return &OrganVizXRAY{
		riClient: riClient,
	}
}

func (s *OrganVizXRAY) FetchVisualizations(ctx context.Context, examId string, interactive bool) ([]byte, error) {
	query := map[string]string{"model": "xray_chest"}
	return s.riClient.GetOrganVisualization(ctx, examId, query, interactive)
}

func getViewPositionPriority() map[string]int {
	return map[string]int{
		"PA":  1,
		"AP":  2,
		"LAT": 3,
		"OBL": 4,
	}
}

func isExamEligibleForXray(exam coreapi.ExamRawBasic) (ExamEligibility, error) {
	if !bodyPartMatches(exam, "chest", "thorax") {
		return INELIGIBLE_BODY_PART, nil
	}
	return ELIGIBLE_FOR_XRAY, nil
}

func isXRAYScan(exam coreapi.ExamRawBasic) bool {
	haystack := strings.ToLower(exam.Modality)
	// even though XA is a modality of XA, for now we exclude it
	// if modality is XA, explicitly return early
	if haystack == "xa" {
		return false
	}

	if exam.Modality == "" {
		haystack = strings.ToLower(exam.Description)
	}
	return strings.Contains(haystack, "cr") || strings.Contains(haystack, "dx") || strings.Contains(haystack, "xr")
}

func compareSeriesByViewPosition(seriesMetadata []seriesData) string {
	priorities := getViewPositionPriority()
	highestPriority := int(^uint(0) >> 1)
	var bestSeries seriesData

	for _, sm := range seriesMetadata {
		currPriority, exists := priorities[sm.viewPosition]
		if !exists {
			currPriority = 5
		}
		// if the current series has PA view position (priorty = 1), return right away
		if currPriority == 1 {
			return sm.seriesUid
		}

		if currPriority < highestPriority {
			highestPriority = currPriority
			bestSeries = sm
		}
	}
	return bestSeries.seriesUid
}

func populateViewPosition(metadata *seriesData) {
	var haystack string
	if metadata.viewPosition != "" {
		haystack = metadata.viewPosition
	} else if metadata.description != "" {
		haystack = metadata.description
	} else { // nothing to check, just return
		return
	}
	// get the list of view position
	viewPositions := make([]string, 0, len(getViewPositionPriority()))
	for k := range getViewPositionPriority() {
		viewPositions = append(viewPositions, k)
	}

	for _, vp := range viewPositions {
		if strings.Contains(strings.ToUpper(haystack), vp) {
			metadata.viewPosition = vp
			break
		}
	}
}

func (s *Client) selectValidSeriesForXRAY(ctx context.Context, examUuid string) (*string, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid": examUuid,
	})

	seriesMetadata, err := s.getObjectMetadataInEachSeriesByExam(ctx, examUuid)
	if err != nil {
		return nil, fmt.Errorf("failed getting series metadata in exam %v: %w", examUuid, err)
	}

	if seriesMetadata == nil {
		lg.Info("no valid series in exam")
		return nil, nil
	}

	processedMetadata := make([]seriesData, 0)
	for i, _ := range seriesMetadata {
		if seriesMetadata[i].sopClass == secondaryCaptureImageStorage {
			// Objects from "Secondary Capture Image Storage" are unlikely to be
			// scanned images, and more likely to be reports and other types of
			// data. Organviz only works on scanned images, so ignore any series
			// with this SOP class. See AIT-84
			continue
		}
		// in case view position is empty, trying to get its view position from series description
		populateViewPosition(&seriesMetadata[i])
		processedMetadata = append(processedMetadata, seriesMetadata[i])
	}
	if len(processedMetadata) == 0 {
		lg.Info("no valid series")
		return nil, nil
	}
	bestSeries := compareSeriesByViewPosition(processedMetadata)
	if bestSeries == "" {
		return nil, nil
	}
	return &bestSeries, nil
}
