//go:build integration
// +build integration

package reports

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"testing"
	"time"

	auditmocks "gitlab.com/pockethealth/coreapi/generated/mocks/audit"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/recordstreaming"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	_ "github.com/go-sql-driver/mysql"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestUnassociatedReports(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	db := testutils.SetupTestDB(t)
	defer db.Close()

	orgsvc := orgs.OrgServiceClient{
		OrgServiceUrl:    cfg.OrgSvcUrl,
		OrgServiceUser:   cfg.OrgSvcUser,
		OrgServiceAPIKey: cfg.OrgSvcApiKey,
		HttpClient: httpclient.NewHTTPClient(
			&http.Client{},
			nil,
		),
	}
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	s := ReportsApiService{
		sqldb:       db,
		orgSvc:      &orgsvc,
		examService: examServiceMock,
	}

	t.Run("user with reports", func(t *testing.T) {
		ctx := context.Background()
		// TODO move away from testing on DB data that is not produced by test to avoid data change breaks
		result, err := s.GetUnassociatedReports(ctx, "1wIQTGOqeNB6SwWdfeg9tMn8im8")
		if err != nil {
			t.Fatalf("unexpected error getting user reports: %v", err)
		}
		reports, ok := result.([]coreapi.Report)
		if !ok {
			t.Fatalf("expected reports list to be returned from GetUnassociatedReports")
		}

		if len(reports) == 0 {
			t.Fatalf("expected at least one report for user 250788, got zero.")
		}

		rep := reports[0]
		_, err = time.Parse("2006-01-02 15:04:05 -0700 MST", rep.UploadTime)
		if err != nil {
			t.Errorf("upload time not in expected format: %v", err)
		}

		if rep.ReportId == "" {
			t.Error("expected report id to be populated")
		}

		if rep.ClinicName == "" {
			t.Error("expected clinic name to be populated")
		}

		if rep.PatientName.FirstAndMiddleName == "" {
			t.Error("expected pt name to be populated")
		}
	})

	t.Run("user without reports", func(t *testing.T) {
		ctx := context.Background()

		result, err := s.GetUnassociatedReports(ctx, "1wIQT8jrCCOgKaDAEo7wTk4deSz")
		if err != nil {
			t.Fatalf("unexpected error getting user reports: %v", err)
		}
		reports, ok := result.([]coreapi.Report)
		if !ok {
			t.Fatalf("expected reports list to be returned from GetUnassociatedReports")
		}

		if len(reports) != 0 {
			t.Fatalf(
				"expected at no reports for account 1wIQT8jrCCOgKaDAEo7wTk4deSz, got %d.",
				len(reports),
			)
		}
	})
}

func TestGetInsights(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	// todo: create an account and a report object in the test setup
	// todo: then expand the test server to respond when using that report id
	rpServer := reportprocessor.NewTestServer([]byte{})
	db := testutils.SetupTestDB(t)
	t.Cleanup(func() {
		db.Close()
		rpServer.Close()
	})

	rp := reportprocessor.NewClient(rpServer.URL, "user", "apikey")
	riContainerUrl, _ := azstorageauth.GetContainerClient(
		context.Background(),
		cfg.AzureStorageAccount,
		cfg.ReportInsightsStorageContainer,
		&policy.RetryOptions{
			TryTimeout: 5 * time.Second,
		},
	)
	containerClient := &azureUtils.ContainerClient{
		ReportInsight: riContainerUrl,
	}

	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)
	auditServiceMock := auditmocks.NewMockService(t)
	recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)

	service := NewReportsApiService(
		db,
		containerClient,
		rp,
		nil,
		nil,
		auditServiceMock,
		recordService,
		true,
		examServiceMock,
	)
	t.Run("get followup not found", func(t *testing.T) {
		_, err := service.GetV2FollowUpReportById(
			context.Background(),
			"nope",
			"1wIQTGOqeNB6SwWdfeg9tMn8im8",
		)

		if err == nil {
			t.Fatal("expected to get an error but got nothing")
		}
		if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Fatalf("expected %s but got %s", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})

	t.Run("get followup for report does not belongs to the account", func(t *testing.T) {
		examUuid := "followup-integ-test-exam-uuid"
		objectId := "followup-integ-test-object-id"
		acctId := "followup_random_account"
		reportId := "followup-integ-test-report-id"
		setupTestDB(t, db, reportId, examUuid, acctId, objectId)
		_, err := service.GetV2FollowUpReportById(
			context.Background(),
			reportId,
			"1wIQTGOqeNB6SwWdfeg9tMn8im8",
		)

		if err == nil {
			t.Fatal("expected to get an error but got nothing")
		}
		if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Fatalf("expected %s but got %s", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})

	t.Run("get questions not found", func(t *testing.T) {
		_, err := service.GetQuestionsByReportId(
			context.Background(),
			"nope",
			"1wIQTGOqeNB6SwWdfeg9tMn8im8",
		)

		if err == nil {
			t.Fatal("expected to get an error but got nothing")
		}
		if err.Error() != errormsgs.ERR_NOT_FOUND {
			t.Fatalf("expected %s but got %s", errormsgs.ERR_NOT_FOUND, err.Error())
		}
	})

	t.Run("get report explanation found", func(t *testing.T) {
		acctId := "explanation_random_account"
		reportId := "explanation-exists" // this must match the mock server rules
		examUuid := "explanation_integ_test_exam_uuid"
		objectId := "explanation_integ_test_object_id"

		setupTestDB(t, db, reportId, examUuid, acctId, objectId)
		resp, err := service.GetReportExplanationByReportId(context.Background(), reportId, acctId)
		require.NoError(t, err)

		assert.Greater(t, len(resp), 0, "expected non-empty response")
	})

	t.Run("get report explanation - no access", func(t *testing.T) {
		reportId := "explanation_integ_test_report_id"
		examUuid := "explanation_integ_test_exam_uuid"
		objectId := "explanation_integ_test_object_id"
		setupTestDB(t, db, reportId, examUuid, "some_account", objectId)

		_, err := service.GetReportExplanationByReportId(
			context.Background(),
			reportId,
			"some_other_account",
		)
		assert.Error(t, err)
	})
}



func TestGetRecordStreamingReportByID(t *testing.T) {
	db := testutils.SetupTestDB(t)
	reportResponse := []byte("dummy report")
	reportProcessorServer := reportprocessor.NewTestServer(reportResponse)
	t.Cleanup(func() {
		db.Close()
		reportProcessorServer.Close()
	})

	t.Run("get report by ID success", func(t *testing.T) {
		reportsController, auditServiceMock, recordService := setupController(
			t,
			db,
			reportProcessorServer.URL,
		)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			studyUID, // studyUID
			objectID,
			true, // is report uploaded
		)
		study := generatedrecordservice.PhysicianPatientStudy{
			UUID:      generatedrecordservice.NewOptString(uuid),
			HasReport: generatedrecordservice.NewOptBool(true),
			DicomStudyTags: generatedrecordservice.NewOptDicomStudyTags(
				generatedrecordservice.DicomStudyTags{
					StudyInstanceUID: generatedrecordservice.NewOptString(studyUID),
				},
			),
			DicomPatientTags: generatedrecordservice.NewOptDicomPatientTags(
				generatedrecordservice.DicomPatientTags{
					PatientName: generatedrecordservice.NewOptString(
						"Dummy",
					), // will be parsed as just last name by dcmtools
					PatientBirthDate: generatedrecordservice.NewOptString(
						phtestutils.GenerateRandomString(t, 10),
					),
				},
			),
		}
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{study}, nil)
		auditServiceMock.EXPECT().
			CreatePhysicianReportViewEvent(mock.Anything, models.EventDataPhysicianReportView{
				EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
					EventDataPhysicianBase: models.EventDataPhysicianBase{
						ProviderID: providerID,
						UserID:     physicianAccountID,
						UserType:   models.UserTypePhysician,
					},
					EventDataPatientBase: models.EventDataPatientBase{
						PatientFirstName: "",
						PatientLastName:  study.DicomPatientTags.Value.PatientName.Value,
						PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
					},
					StudyUID: studyUID,
				},
			}).Return(nil)

		// make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, body := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify status OK and report body
		if status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

		assert.Equal(t, reportResponse, body)
	})

	t.Run("get report with failed audit log should fail", func(t *testing.T) {
		reportsController, auditServiceMock, recordService := setupController(
			t,
			db,
			reportProcessorServer.URL,
		)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			studyUID, // studyUID
			objectID,
			true, // is report uploaded
		)
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{
				generatedrecordservice.PhysicianPatientStudy{
					UUID:      generatedrecordservice.NewOptString(uuid),
					HasReport: generatedrecordservice.NewOptBool(true),
					DicomStudyTags: generatedrecordservice.NewOptDicomStudyTags(
						generatedrecordservice.DicomStudyTags{
							StudyInstanceUID: generatedrecordservice.NewOptString(studyUID),
						},
					),
				},
			}, nil)
		auditServiceMock.EXPECT().
			CreatePhysicianReportViewEvent(mock.Anything, models.EventDataPhysicianReportView{
				EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
					EventDataPhysicianBase: models.EventDataPhysicianBase{
						ProviderID: providerID,
						UserID:     physicianAccountID,
						UserType:   models.UserTypePhysician,
					},
					StudyUID: studyUID,
				},
			}).Return(errors.New("dummy"))

		// make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusNotFound {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusNotFound)
		}
	})

	t.Run("get report without providerID should be unauthorized", func(t *testing.T) {
		reportsController, _, _ := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)

		// make authorized request to GetReportById without providerId
		request := testutils.CreateRequest(
			t,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s", objectID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusUnauthorized {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusUnauthorized)
		}
	})

	t.Run("get report no auth token should be unauthorized", func(t *testing.T) {
		reportsController, _, _ := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)

		// make unauthorized request to GetReportById with providerId
		request := testutils.CreateRequest(
			t,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusUnauthorized {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusUnauthorized)
		}
	})

	t.Run("get report failed physician permission should be unauthorized", func(t *testing.T) {
		reportsController, _, recordService := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{}, errors.New("dummy"))
		// make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusNotFound {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusUnauthorized)
		}
	})

	t.Run("get report missing physician permission should be unauthorized", func(t *testing.T) {
		reportsController, _, recordService := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{}, nil)
		// make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusNotFound {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusNotFound)
		}
	})

	t.Run("get report invalid physician permission should return error", func(t *testing.T) {
		reportsController, _, recordService := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{
				generatedrecordservice.PhysicianPatientStudy{
					UUID:      generatedrecordservice.NewOptString("wrong uuid"),
					HasReport: generatedrecordservice.NewOptBool(true),
				},
			}, nil) // 2. Make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusNotFound {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusNotFound)
		}
	})

	t.Run("get report invalid physician permission should return error", func(t *testing.T) {
		reportsController, _, recordService := setupController(t, db, reportProcessorServer.URL)
		// setup test data
		providerID := phtestutils.GenerateRandomInt64(t)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		uuid, _ := recordstreaming.SetupRecordStreamingData(
			t,
			db,
			providerID,
			physicianAccountID,
			"", // studyUID
			objectID,
			true, // is report uploaded
		)
		recordService.EXPECT().
			GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
				UUID:      []string{uuid},
			}).
			Return(&generatedrecordservice.PhysicianPatientStudies{
				generatedrecordservice.PhysicianPatientStudy{
					UUID:      generatedrecordservice.NewOptString("wrong uuid"),
					HasReport: generatedrecordservice.NewOptBool(true),
				},
			}, nil)
		// 2. Make authorized request to GetReportById with providerId
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/reports/%s?providerId=%d", objectID, providerID),
			"", // payload
		)
		request = mux.SetURLVars(request, map[string]string{
			"reportId": objectID,
		})
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			reportsController.GetReportById,
		)

		// verify error status code
		if status != http.StatusNotFound {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusNotFound)
		}
	})
}

func setupController(
	t *testing.T,
	db *sql.DB,
	url string,
) (coreapi.PrivateReportsApiRouter, *auditmocks.MockService, *mockrecordservice.MockRecordServiceClientInterface) {
	reportProcessorClient := reportprocessor.NewClient(url, "user", "apikey")
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)
	auditServiceMock := auditmocks.NewMockService(t)
	recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)

	service := NewReportsApiService(
		db,
		nil,
		reportProcessorClient,
		nil,
		nil,
		auditServiceMock,
		recordService,
		false,
		examServiceMock,
	)
	reportsController := NewPrivateReportsApiController(service, nil)
	return reportsController, auditServiceMock, recordService
}

func setupTestDB(t *testing.T, db *sql.DB, reportId, examUuid, acctId, objectId string) {
	t.Cleanup(func() {
		db.Exec("DELETE FROM object_mappings WHERE object_id=?", reportId)

		db.Exec("DELETE FROM view_metadata WHERE object_id=?", objectId)
		db.Exec("DELETE FROM object_mappings WHERE object_id=?", objectId)
		db.Exec("DELETE FROM objects WHERE object_id=?", objectId)

		db.Exec("DELETE FROM exams WHERE uuid=?", examUuid)
	})

	// create report if it does not exist
	_, err := db.Exec(
		"INSERT INTO object_mappings (object_id, exam_uuid) VALUES (?, ?)",
		reportId,
		examUuid,
	)
	require.NoError(t, err)

	_, err = db.Exec(
		"INSERT INTO object_mappings (object_id, exam_uuid, series_uid) VALUES (?, ?, 'some-series')",
		objectId,
		examUuid,
	)
	require.NoError(t, err)

	_, err = db.Exec("INSERT INTO objects (object_id, is_report) VALUES (?, 0)", objectId)
	require.NoError(t, err)

	_, err = db.Exec(
		"INSERT INTO view_metadata (object_id, image_orientation) VALUES (?, '1/0/0/0/1/0')",
		objectId,
	)
	require.NoError(t, err)

	_, err = db.Exec(
		`INSERT INTO exams (uuid, account_id, activated, exam_uid, description, body_part, modality, dob, date, referring_physician,
		  transfer_id, patient_name, patient_mrn, sex, phone
	)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		examUuid,
		acctId,
		true,
		"exam_uid",
		"CT ABD",
		"ABD",
		"CT",
		"********",
		"********",
		"referring_physician",
		"transfer_id",
		"patient_name",
		"patient_mrn",
		"sex",
		"phone",
	)
	if err != nil {
		t.Fatalf("failed to setup test data: %q", err.Error())
	}
}
