//go:build integration
// +build integration

package reports_test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	auditmocks "gitlab.com/pockethealth/coreapi/generated/mocks/audit"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/reports"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	testreports "gitlab.com/pockethealth/coreapi/pkg/testutils/reports"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestGetReportMetadata(t *testing.T) {
	db := testutils.SetupTestDB(t)

	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	svc := reports.NewReportsApiService(
		db,
		&azureUtils.ContainerClient{},
		reportprocessor.RPClient{},
		&orgs.OrgServiceMock{},
		nil,
		auditmocks.NewMockService(t),
		mockrecordservice.NewMockRecordServiceClientInterface(t),
		true,
		examServiceMock,
	)
	controller := reports.NewPrivateReportMetadataController(svc)
	router, err := coreapi.NewRouter(controller)
	require.NoError(t, err)

	acctID := phtestutils.GenerateRandomString(t, 10)
	otherAcctID := phtestutils.GenerateRandomString(t, 10)

	report := testreports.CreateAndInsertTestReport(t, db, &acctID)
	otherAcctReport := testreports.CreateAndInsertTestReport(t, db, &otherAcctID)

	for name, tc := range map[string]struct {
		reportID                      string
		signRequest                   bool
		useInvalidSignedRequestHeader bool
		expectNotOk                   bool
	}{
		"get own report": {
			reportID: report.ReportId,
		},
		"get own report using signed request": {
			reportID:    report.ReportId,
			signRequest: true,
		},
		"get report for other account": {
			reportID:    otherAcctReport.ReportId,
			expectNotOk: true,
		},
		"get report for other account using signed request": {
			reportID:    otherAcctReport.ReportId,
			signRequest: true,
		},
		"get report for other account using bad signed request": {
			// This test tries to access a report on another account, by using an invalid
			// X-PH-Signature header. This exploits a bug that was introduced in the dual-auth flow
			// for GET /users/exams. Testing here due to its own dual-auth flow
			reportID:                      otherAcctReport.ReportId,
			useInvalidSignedRequestHeader: true,
			expectNotOk:                   true,
		},
	} {
		t.Run(name, func(t *testing.T) {
			req := testutils.CreateAuthorizedRequest(
				t,
				acctID,
				http.MethodGet,
				fmt.Sprintf("/v1/reports/%s/metadata", tc.reportID),
				nil,
			)

			if tc.signRequest {
				req.Header.Del("Authorization")
				privateKey := testutils.SetupXPhSignaturePrivKey(t)
				testutils.SignRequest(t, req, privateKey)
			}

			if tc.useInvalidSignedRequestHeader {
				req.Header.Add("X-PH-Signature", "made-up header")
			}

			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			if tc.expectNotOk {
				require.NotEqual(t, http.StatusOK, rr.Code)
				return
			}

			require.Equal(t, http.StatusOK, rr.Code)

			var resp coreapi.Report
			err := json.Unmarshal(rr.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.Equal(t, tc.reportID, resp.ReportId)
		})
	}
}
