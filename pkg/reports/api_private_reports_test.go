package reports

import (
	"net/http"
	"net/http/httptest"
	"testing"

	auth "gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// when you test locally this ends up being what clientAddr[0] looks like
const (
	localClaimIp = ""
)

func TestGetReportById(t *testing.T) {

	//mock get user exams function
	mockFnEncapTrue := func(reportId string, accept string, acctId string, shareId string) ([]byte, error) {
		var report []byte
		return report, nil
	}

	// happy cases
	t.Run("report pdf request", func(t *testing.T) {

		service := NewMockReportsApiService(mockFnEncapTrue)

		controller := NewPrivateReportsApiController(service, nil)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			"GET",
			"/v1/reports/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "application/pdf"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("report png request", func(t *testing.T) {

		service := NewMockReportsApiService(mockFnEncapTrue)

		controller := NewPrivateReportsApiController(service, nil)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			"GET",
			"/v1/reports/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "image/png"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != requestType {
			t.Errorf(
				"handler set wrong Content-Type header: got %q want %q",
				contentType,
				requestType,
			)
		}
	})

	// happy cases
	t.Run("report bad type request", func(t *testing.T) {

		service := NewMockReportsApiService(mockFnEncapTrue)

		controller := NewPrivateReportsApiController(service, nil)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			"GET",
			"/v1/reports/-2BFAHy4_biqS637viBGvGO5fiYmJqXnw9pBtEXwono=",
			nil,
		)
		if err != nil {
			t.Fatal(err)
		}
		requestType := "notarealtype"

		token := auth.MakeAccountAuthToken("27nakqZdHKADEuxK8kgfROJimwo", localClaimIp)
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Accept", requestType)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		wantType := "image/png" //anything that's not application/pdf should default to png
		if contentType := rr.HeaderMap["Content-Type"][0]; contentType != wantType {
			t.Errorf("handler set wrong Content-Type header: got %q want %q", contentType, wantType)
		}
	})
}


