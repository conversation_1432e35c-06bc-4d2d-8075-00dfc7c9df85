package reports

import (
	"github.com/samber/lo"
	"gitlab.com/pockethealth/phutils/v10/pkg/pubsub"
)

type ReportMessageEvent struct {
	AccountId string `json:"account_id"`
	ReportId  string `json:"report_id"`
	ViewedAt  string `json:"viewed_at"`
}

type ReportEvent pubsub.Envelope

func (r *ReportEvent) IsValid() bool {
	return !lo.IsEmpty(r.EventType) &&
		!lo.IsEmpty(r.EventVersion) &&
		!lo.IsEmpty(r.Timestamp) &&
		!lo.IsNil(r.Payload)
}

func (r *ReportEvent) ApplicationProperties() map[string]any {
	return map[string]any{
		"event_type": r.EventType,
	}
}
