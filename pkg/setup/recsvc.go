package setup

import (
	"encoding/json"
	"log"

	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/keyvault"
)

// creates a record service client
func SetupRecordServiceClient(
	kv *keyvault.Keyvault,
	confMap map[string]json.RawMessage,
) *recordservice.RecordServiceClient {
	var recSvcConf map[string]string
	svcName := "rec_svc"
	err := json.Unmarshal(confMap[svcName], &recSvcConf)
	if err != nil {
		log.Fatalf("can't read rec_svc json %+v.  error: %+v\n", confMap[svcName], err)
	}
	apiKey := GetApiKey(kv, confMap, svcName)
	return recordservice.NewClient(recSvcConf["url"], recSvcConf["name"], api<PERSON><PERSON>)
}
