package setup

import (
	"encoding/json"
	"log"

	"gitlab.com/pockethealth/phutils/v10/pkg/config"
	"gitlab.com/pockethealth/phutils/v10/pkg/keyvault"
)

// grabs an api key from secrets or the config using the standard names `api_key_sec_name` or `api_key`
func GetApi<PERSON>ey(kv *keyvault.Keyvault, confMap map[string]json.RawMessage, svcName string) string {
	var svcConfig map[string]json.RawMessage
	err := json.Unmarshal(confMap[svcName], &svcConfig)
	if err != nil {
		log.Fatalf("can't read %s json %+v.  error: %+v\n", svcName, confMap[svcName], err)
	}
	return config.ValFromConfOrKeyvault(kv, svcConfig, "api_key", "api_key_sec_name")
}
