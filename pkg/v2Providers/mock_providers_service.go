package v2providers

import (
	"context"

	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

type MockV2ProviderService struct {
	mockGetPlans func(context.Context, uint, string, string) ([]planservice.PlanV1, error)
}

func (ps *MockV2ProviderService) GetPlans(
	ctx context.Context,
	providerId uint,
	accountId string,
	deviceId string,
) ([]planservice.PlanV1, error) {
	return ps.mockGetPlans(ctx, providerId, accountId, deviceId)
}

func GetPlansResponse(ctx context.Context,
	providerId uint,
	accountId string,
	deviceId string,
) ([]planservice.PlanV1, error) {
	return []planservice.PlanV1{
		{
			Id:           2,
			Name:         "unlimited",
			DisplayName:  "Unlimited",
			IsActive:     true,
			IsRecurring:  false,
			Amount:       49,
			PeriodUnit:   "y",
			PeriodLength: 1,
			RegionId:     1,
			ChargeTax:    false,
		},
	}, nil
}
