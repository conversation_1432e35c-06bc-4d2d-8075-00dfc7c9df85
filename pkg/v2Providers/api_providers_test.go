package v2providers

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/amplitude/analytics-go/amplitude"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

func verifyPlansResult(
	t *testing.T,
	resultPlanIds map[int]any,
	expectedPlanIds map[int]any,
	amplitudeEvents []amplitude.Event,
	accountId string,
	deviceId string,
	experimentName string,
	variant string,
	hasExposureEvent bool,
) {

	// 1. Verify plans are returned as expected
	if !reflect.DeepEqual(resultPlanIds, expectedPlanIds) {
		resultJson, _ := json.Marshal(resultPlanIds)
		expectedJson, _ := json.Marshal(expectedPlanIds)
		t.Fatalf("got plans: %s, expected plans: %s", string(resultJson), expectedJson)
	}

	// 2. Verify amplitude exposures sent
	if hasExposureEvent {
		if len(amplitudeEvents) == 0 {
			t.Fatalf("expected exposure event")
		}

		event := amplitudeEvents[0]
		if event.EventType != "$exposure" {
			t.Fatalf("eventType is not $exposure: %s", event.EventType)
		}
		if event.UserID != accountId {
			t.Fatalf("got accountId: %s, expected accountId: %s", event.UserID, accountId)
		}
		if event.DeviceID != deviceId {
			t.Fatalf("got deviceId: %s, expected deviceId: %s", event.DeviceID, deviceId)
		}
		payload := event.EventProperties
		if payload["flag_key"] != experimentName {
			t.Fatalf(
				"got flag_key: %s, expected flag_key: %s",
				event.EventProperties["flag_key"],
				experimentName,
			)
		}
		if payload["variant"] != variant {
			t.Fatalf(
				"got variant: %s, expected variant: %s",
				payload["variant"],
				variant,
			)
		}
	} else if len(amplitudeEvents) != 0 {
		eventsJson, _ := json.Marshal(amplitudeEvents)
		t.Fatalf("unexpected exposure event: %s", eventsJson)
	}
}

func TestGetPlans(t *testing.T) { // TODO: Bhavik add relevant test cases after small refactor
	testcases := []struct {
		name           string
		email          string
		getPlansFn     func(context.Context, uint, string, string) ([]planservice.PlanV1, error)
		expectedStatus int
	}{
		{
			name:           "valid request",
			getPlansFn:     GetPlansResponse,
			expectedStatus: http.StatusOK,
		},
	}

	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			//setup router
			ctrl := NewV2PublicProvidersApiController(
				&MockV2ProviderService{mockGetPlans: c.getPlansFn}, "ampcookie")

			r, err := coreapi.NewRouter(ctrl)
			if err != nil {
				t.Fatal(err)
			}

			path := "/v2/providers/1/plans"
			req, err := http.NewRequest("GET", path, nil)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			r.ServeHTTP(rr, req)

			if status := rr.Code; status != c.expectedStatus {
				t.Fatalf(
					"handler returned wrong status code: got %v want %v",
					status,
					c.expectedStatus,
				)
			}
			var plans []planservice.PlanV1
			err = json.Unmarshal(rr.Body.Bytes(), &plans)
			if err != nil {
				t.Fatalf("failed to unmarshal body or wrong return type")
			}
			if plans[0].Id != 2 {
				t.Fatalf("expected unlimited")
			}
		})
	}
}
