/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package v2providers

import (
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/amplitude_util"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A ProvidersApiController binds http requests to an api service and writes the service results to the http response
type V2PublicProvidersApiController struct {
	service         coreapi.V2ProvidersApiServicer
	ampCookieHeader string
}

// NewProvidersApiController creates a default api controller
func NewV2PublicProvidersApiController(
	s coreapi.V2ProvidersApiServicer,
	ampCookieHeader string,
) coreapi.PublicV2ProvidersApiRouter {
	return &V2PublicProvidersApiController{
		service:         s,
		ampCookieHeader: ampCookieHeader,
	}
}

// Routes returns all of the api route for the ProvidersApiController
func (c *V2PublicProvidersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetPlans",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{provider_id}/plans",
			HandlerFunc: c.GetPlans,
		},
	}
}

func (c *V2PublicProvidersApiController) GetPathPrefix() string {
	return "/v2/providers"
}

func (c *V2PublicProvidersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{} //no middleware for providers, return empty list
}

func (c *V2PublicProvidersApiController) GetPlans(w http.ResponseWriter, r *http.Request) {
	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)
	accountId := r.URL.Query().Get("accountId")

	params := mux.Vars(r)
	providerId, err := coreapi.ParseUIntParameter(params["provider_id"])
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			WithField("path_param", params["provider_id"]).
			Error("invalid providerId")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	providerPlans, err := c.service.GetPlans(r.Context(), providerId, accountId, deviceID)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), providerPlans, nil, w)
}
