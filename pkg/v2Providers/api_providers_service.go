/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package v2providers

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

// ProvidersApiService is a service that implents the logic for the ProvidersApiServicer
// This service should implement the business logic for every endpoint for the ProvidersApi API.
// Include any external packages or services that will be required by this service.
type V2ProvidersApiService struct {
	orgSvc            orgs.OrgService
	planSvc           planservice.PlanService
	experimentsClient interfaces.AmplitudeExperimentClient
	eventsClient      interfaces.AmplitudeEventClient
	acctSvcClient     accountservice.AccountService
}

// NewProvidersApiService creates a default api service
func NewV2ProvidersApiService(
	os orgs.OrgService,
	ps planservice.PlanService,
	experimentsClient interfaces.AmplitudeExperimentClient,
	eventsClient interfaces.AmplitudeEventClient,
	acctSvcClient accountservice.AccountService,
) coreapi.V2ProvidersApiServicer {
	return &V2ProvidersApiService{
		orgSvc:            os,
		planSvc:           ps,
		experimentsClient: experimentsClient,
		eventsClient:      eventsClient,
		acctSvcClient:     acctSvcClient,
	}
}

// GetProviderById - Get a provider
func (s *V2ProvidersApiService) GetPlans(
	ctx context.Context,
	providerId uint,
	accountId string,
	deviceId string,
) ([]planservice.PlanV1, error) {
	plans, err := s.orgSvc.GetPlans(ctx, providerId)
	returnArray := make([]planservice.PlanV1, len(plans))
	for i, plan := range plans {
		returnArray[i] = planservice.PlanV1{
			Id:          plan.Id,
			Name:        plan.Name,
			DisplayName: plan.DisplayName,
			CreatedAt:   plan.CreatedAt,
			IsActive:    plan.IsActive,
			IsRecurring: plan.IsRecurring,
			Amount:      plan.Amount,
			PeriodUnit:  plan.PeriodUnit,
			RegionId:    plan.RegionId,
			ChargeTax:   plan.ChargeTax,
			IsCollapsed: plan.IsCollapsed,
		}
	}
	return returnArray, err
}
