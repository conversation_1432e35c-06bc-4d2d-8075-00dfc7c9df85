package recorduploadstatus

import (
	"context"
	"errors"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RecordUploadStatusApiService struct {
	providersServiceClient providersservice.ProvidersService
	accountServiceClient   accountservice.AccountService
	recordServiceClient    recordservice.RecordServiceClientInterface
	migrationHelper        exams.MigrationHelperInterface
}

func NewRecordUploadStatusApiService(
	providersServiceClient providersservice.ProvidersService,
	accountServiceClient accountservice.AccountService,
	recordServiceClient recordservice.RecordServiceClientInterface,
	migrationHelper exams.MigrationHelperInterface,
) coreapi.RecordUploadStatusApiServicer {
	return &RecordUploadStatusApiService{
		providersServiceClient: providersServiceClient,
		accountServiceClient:   accountServiceClient,
		recordServiceClient:    recordServiceClient,
		migrationHelper:        migrationHelper,
	}
}

// GetRecordUploadStatusListForAccountID returns a list of study upload statuses
// for studies the patient has access to.
// Using rollout flag to switch from calling ProvidersService to calling RecordService
// TODO [DELTA-716]: include data for computing upload status for transfer-based studies
func (s *RecordUploadStatusApiService) GetRecordUploadStatusListForAccountID(
	ctx context.Context,
	accountID string,
	accountType models.AccountType,
) ([]models.RecordUploadStatus, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountID,
	})

	if accountType != models.ACCOUNT_TYPE_PATIENT {
		lg.Error("record upload status list is not available yet for non-patient accounts")
		return []models.RecordUploadStatus{}, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	var recordUploadStatusList []models.RecordUploadStatus
	var err error

	// get list of studies with upload status from RecordService
	// - contains record streaming studies AND transfer-based studies
	recordUploadStatusList, err = s.recordServiceClient.GetPatientStudiesWithUploadStatus(
		ctx,
		accountID,
	)
	if err != nil {
		lg.Error("failed to query record upload status list from record service")
		return []models.RecordUploadStatus{}, err
	}

	patients, err := s.accountServiceClient.GetPatients(ctx, accountID)
	if err != nil {
		lg.Error("failed to fetch patients for account")
		return []models.RecordUploadStatus{}, err
	}

	patientMap := map[string]accountservice.Patient{}
	for _, patient := range patients {
		patientMap[patient.PatientId] = patient
	}

	// Post-processing to add patient name
	for i, recordUploadStatus := range recordUploadStatusList {
		if patient, ok := patientMap[recordUploadStatus.PatientID]; ok {
			recordUploadStatusList[i].PatientName = models.PatientName{
				FirstAndMiddleName: patient.FirstName,
				LastName:           patient.LastName,
			}
		}
	}
	return recordUploadStatusList, nil
}
