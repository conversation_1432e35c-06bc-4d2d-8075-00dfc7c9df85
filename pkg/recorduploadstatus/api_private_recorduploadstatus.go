package recorduploadstatus

import (
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivateRecordUploadStatusApiController struct {
	service coreapi.RecordUploadStatusApiServicer
}

func NewPrivateRecordUploadStatusApiController(
	s coreapi.RecordUploadStatusApiServicer,
) coreapi.PrivateRecordUploadStatusApiRouter {
	return &PrivateRecordUploadStatusApiController{service: s}
}

func (c *PrivateRecordUploadStatusApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetRecordUploadStatusList",
			Method:      http.MethodGet,
			Pattern:     "/upload-status",
			HandlerFunc: c.GetRecordUploadStatusList,
		},
	}
}

func (c *PrivateRecordUploadStatusApiController) GetPathPrefix() string {
	return "/v1/records"
}

func (c *PrivateRecordUploadStatusApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateRecordUploadStatusApiController) GetRecordUploadStatusList(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())

	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if hasError(&w, r, err) {
		lg.WithError(err).Error("error decoding auth token when getting record upload status list for account")
		return
	}
	if claims.AccountID == "" {
		lg.WithError(err).Error("missing account id in auth token when getting record upload status list for account")
		httperror.ErrorWithLog(w, r, "account id cannot be empty", http.StatusUnauthorized)
		return
	}
	if err = claims.AccountType.Validate(); err != nil {
		lg.WithError(err).Error("missing claims in auth token when getting record upload status list for account")
		httperror.ErrorWithLog(w, r, "account type cannot be empty", http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetRecordUploadStatusListForAccountID(r.Context(), claims.AccountID, claims.AccountType)
	if hasError(&w, r, err) {
		lg.WithError(err).Error("error getting record upload status list for account")
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func hasError(w *http.ResponseWriter, r *http.Request, err error) bool {
	if err == nil {
		return false
	}
	status := http.StatusInternalServerError
	if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
		status = http.StatusUnauthorized
	} else if err.Error() == errmsg.ERR_NOT_FOUND {
		status = http.StatusNotFound
	}
	httperror.ErrorWithLog(*w, r, err.Error(), status)
	return true
}
