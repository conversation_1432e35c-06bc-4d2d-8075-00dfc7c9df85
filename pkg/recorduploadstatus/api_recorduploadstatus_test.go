package recorduploadstatus_test

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/recorduploadstatus"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

const (
	localClaimIp = "127.0.0.1"
)

func TestGetRecordUploadStatusList(t *testing.T) {
	accountID := testutils.GenerateRandomString(t, 10)
	physicianToken := "Bearer " + auth.MakePhysicianAccountAuthToken(accountID, localClaimIp)
	patientID := testutils.GenerateRandomString(t, 10)
	patientFirstName := "James"
	patientLastName := "Shen"
	patient := accountservice.Patient{
		PatientId: patientID,
		FirstName: patientFirstName,
		LastName:  patientLastName,
	}

	testCases := []struct {
		name               string
		authToken          string
		expectedStatusCode int
		expectedResult     []models.RecordUploadStatus
		setupMock          func(providersServiceClientMock *providersservice.ProviderServiceMock)
		verifyMock         func(t *testing.T, providersServiceClientMock *providersservice.ProviderServiceMock)
	}{
		{
			name:               "should return error if caller is unauthorized",
			authToken:          "",
			expectedStatusCode: http.StatusUnauthorized,
		},
		{
			name:               "should return error if caller is not patient",
			authToken:          physicianToken,
			expectedStatusCode: http.StatusUnauthorized,
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			router, providersServiceClientMock := setupRouter(t, patient)
			if testCase.setupMock != nil {
				testCase.setupMock(providersServiceClientMock)
			}
			request, _ := http.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/v1/records/upload-status?account_id=%s", accountID),
				nil,
			)
			request.Header.Set("Authorization", testCase.authToken)

			responseRecorder := httptest.NewRecorder()
			router.ServeHTTP(responseRecorder, request)

			assert.Equal(t, testCase.expectedStatusCode, responseRecorder.Code)
			responseBody, err := io.ReadAll(responseRecorder.Body)
			assert.NoError(t, err)

			if testCase.expectedResult != nil {
				var result []models.RecordUploadStatus
				err = json.Unmarshal(responseBody, &result)
				assert.NoError(t, err)
				assert.Equal(t, len(testCase.expectedResult), len(result))
				for i := range result {
					assert.True(t, reflect.DeepEqual(testCase.expectedResult[i], result[i]))
				}
			}

			if testCase.verifyMock != nil {
				testCase.verifyMock(t, providersServiceClientMock)
			}
		})
	}
}

func setupRouter(
	t *testing.T,
	patient accountservice.Patient,
) (*mux.Router, *providersservice.ProviderServiceMock) {
	providersServiceClientMock := providersservice.NewProviderServiceMock()
	accountServiceMock := &accountservice.AcctSvcMock{
		GetPatientsReturn: []accountservice.Patient{patient},
	}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)

	migrationHelperMock := exams.MockMigrationHelper{}
	service := recorduploadstatus.NewRecordUploadStatusApiService(
		providersServiceClientMock,
		accountServiceMock,
		recordServiceMock,
		&migrationHelperMock,
	)
	controller := recorduploadstatus.NewPrivateRecordUploadStatusApiController(service)
	router, err := coreapi.NewRouter(controller)
	assert.NoError(t, err)
	return router, providersServiceClientMock
}
