package pubsub

import "context"

type MockTopic struct {
	CallLogs []Event
}

func (t *MockTopic) Publish(ctx context.Context, evt Event) {
	t.CallLogs = append(t.CallLogs, evt)
}

func (t *MockTopic) Reset() {
	t.CallLogs = []Event{}
}

type MockQueue struct {
	CallLogs []Message
}

func (t *MockQueue) Publish(ctx context.Context, data Message) {
	t.CallLogs = append(t.CallLogs, data)
}

func (t *MockQueue) Reset() {
	t.CallLogs = []Message{}
}
