package pubsub

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
)

type Message interface {
	IsDefault() bool
}

type Queue interface {
	GetGlobalClient() (*azservicebus.Client, error)
	GetRegionClient() (*azservicebus.Client, error)
	Publish(ctx context.Context, data Message, queue string, client *azservicebus.Client)
	PublishSession(data Message, queue string, sessionId string, client *azservicebus.Client)
}
