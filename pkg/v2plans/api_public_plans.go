package v2plans

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PublicPlansApiController binds http requests to an api service and writes the service results to the http response
type PublicPlansApiController struct {
	service coreapi.V2PlanApiServicer
}

// NewPublicPlansApiController creates a default api controller
func NewPublicPlansApiController(
	s coreapi.V2PlanApiServicer,
) coreapi.PublicV2PlanApiRouter {
	return &PublicPlansApiController{
		service: s,
	}
}

// Routes returns all of the api route for the V2UsersApiController
func (c *PublicPlansApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetPlans",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetPlans,
		},
		{
			Name:        "GetPlanById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{planId}",
			HandlerFunc: c.GetPlanById,
		},
	}
}

func (c *PublicPlansApiController) GetPathPrefix() string {
	return "/v2/plans"
}

func (c *PublicPlansApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func decodeBearerToken(r *http.Request) (auth.Claims, error) {
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	return claims, err
}

// GetPlans
func (c *PublicPlansApiController) GetPlans(w http.ResponseWriter, r *http.Request) {
	lg := logutils.DebugCtxLogger(r.Context())

	var err error
	params := r.URL.Query()

	claims, err := decodeBearerToken(r)
	if err != nil {
		lg.WithError(err).Infof("unable to decode bearer token")
	}
	acctId := claims.AccountID
	planId := claims.PlanID

	recurring := true // default param to true
	recurringStr := params.Get("recurring")
	if recurringStr != "" {
		val, err := strconv.ParseBool(recurringStr)
		if err != nil {
			lg.WithField("recurring_param", recurring).Error("invalid recurring")
			w.WriteHeader(http.StatusBadRequest)
			return
		} else {
			recurring = val
		}
	}

	providerIdsParam := params.Get("providerIds")

	// Split the comma-separated string into individual numbers
	providerIdsString := strings.Split(providerIdsParam, ",")

	var providerIds []uint
	for _, numStr := range providerIdsString {
		num, err := strconv.Atoi(numStr)
		if err == nil {
			providerIds = append(providerIds, uint(num)) // #nosec G115 provider ID > 0
		}

	}

	plans, err := c.service.GetPlans(r.Context(), recurring, acctId, providerIds, planId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), plans, nil, w)
}

// GetPlans
func (c *PublicPlansApiController) GetPlanById(w http.ResponseWriter, r *http.Request) {
	var err error
	params := mux.Vars(r)
	planId, err := strconv.ParseUint(params["planId"], 10, 32)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}

	plan, err := c.service.GetPlanById(
		r.Context(),
		int32(planId), // #nosec G115 plans.id is bigint so > 0, and not worried yet about max_int32
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), plan, nil, w)
}
