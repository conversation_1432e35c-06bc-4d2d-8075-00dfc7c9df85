package v2plans

import (
	"context"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// PlansApiService is a service that implents the logic for the PlanApiServicer
// This service should implement the business logic for every endpoint for the Plans API.
// Include any external packages or services that will be required by this service.
type PlansApiService struct {
	plansService      planservice.PlanService
	experimentsClient interfaces.AmplitudeExperimentClient
	eventsClient      interfaces.AmplitudeEventClient
}

// NewPlansApiService creates a default api service
func NewPlansApiService(
	planSvc planservice.PlanService,
	experimentsClient interfaces.AmplitudeExperimentClient,
	eventsClient interfaces.AmplitudeEventClient,
) coreapi.V2PlanApiServicer {
	return &PlansApiService{
		plansService:      planSvc,
		experimentsClient: experimentsClient,
		eventsClient:      eventsClient,
	}
}

// GetPlans
func (s *PlansApiService) GetPlans(ctx context.Context, recurring bool, acctId string, providerIds []uint, planId uint64) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"recurring":   recurring,
		"acctId":      acctId,
		"providerIds": providerIds,
		"planId":      planId,
	})

	plans, err := s.plansService.GetPlans(ctx, recurring)
	if err != nil {
		lg.WithError(err).Error("unable to get plans from plansvc")
		return nil, err
	}

	return s.filterPlansByRegion(plans), nil
}

// GetPlans
func (s *PlansApiService) GetPlanById(
	ctx context.Context,
	planId int32,
) (*planservice.PlanV2, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("planId", planId)

	plan, err := s.plansService.GetPlanById(ctx, planId)
	if err != nil {
		lg.WithError(err).Error("unable to get plans from plansvc")
		return nil, err
	}
	return &plan, nil
}

func (s *PlansApiService) filterPlansByRegion(plans []planservice.PlanV1) []planservice.PlanV1 {
	// filter plans by current core region
	filtered := make([]planservice.PlanV1, 0)
	for _, p := range plans {
		if p.RegionId == regions.GetRegionID() {
			filtered = append(filtered, p)
		}
	}

	return filtered
}

func isPaidPlanId(planId uint64) bool {
	return IsUnlimitedPlan(planId) || IsFlexPlan(planId) || IsCorePlan(planId)
}

func IsUnlimitedPlan(planID uint64) bool {
	return planID == 2 || planID == 4
}

func IsFlexPlan(planId uint64) bool {
	return planId == 7 || planId == 8
}

func IsCorePlan(planID uint64) bool {
	return planID == 19 || planID == 20
}
