package v2plans

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

type MockV2PlansService struct {
	MockGetPlans    func(ctx context.Context, recurring bool) (interface{}, error)
	MockGetPlanById func(ctx context.Context, planId int32) (*planservice.PlanV2, error)
}

func NewMockV2PlansService() coreapi.V2PlanApiServicer {
	return &MockV2PlansService{}
}

func (s *MockV2PlansService) GetPlans(
	ctx context.Context,
	recurring bool,
	acctId string,
	providerIds []uint,
	planId uint64,
) (interface{}, error) {
	return s.MockGetPlans(ctx, recurring)
}

func (s *MockV2PlansService) GetPlanById(
	ctx context.Context,
	planId int32,
) (*planservice.PlanV2, error) {
	return s.MockGetPlanById(ctx, planId)
}
