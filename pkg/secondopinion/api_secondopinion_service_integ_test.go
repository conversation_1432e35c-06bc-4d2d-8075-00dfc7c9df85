//go:build integration
// +build integration

package secondopinion_test

import (
	"context"
	"database/sql"
	"net/http"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/secondopinion"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

func TestCreateNewPatientEligibilityProgramsForAccount(t *testing.T) {
	t.Skip(
		"We can't easily delete peps at the moment, so we are skipping until a DELETE endpoint is in place and we can cleanup",
	)
	db := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	ctx := context.Background()
	acctSrvc := newAcctService(cfg)
	soSrvcUser := newEISVCUser(cfg)
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	soSrvcUser.SetDoRequest(nil)
	srvc := secondopinion.NewSecondOpinionApiService(db, soSrvcUser, *acctSrvc, examService)

	t.Run("an account has no eligible patients for a program", func(t *testing.T) {
		accountId := ksuid.New().String()

		actual, err := srvc.CreateNewPatientEligibilityProgramsForAccount(
			ctx,
			accountId,
			coreapi.ProgramObsp,
		)
		require.NoError(t, err)

		assert.Zero(t, actual)
	})

	t.Run("an account has eligible patient for a program", func(t *testing.T) {
		accountId := "2oRdiEOa1JgUqpUrkZ7Phlc1z9J"
		patientId := "2oRdi7w24yRtEWX7wVqAkR1rN31"

		exam := TestExamScanSetup{
			examId:    ksuid.New().String(),
			accountId: accountId,
			patientId: patientId,
			sex:       "F",
			dob:       time.Now().AddDate(-55, 0, 0).Format("********"),
			date:      time.Now().AddDate(-3, 0, 0).Format("********"),
			modality:  "MG",
			scanId:    ksuid.New().String(),
			orgId:     **********,
			source:    "PS",
		}

		createExamAndScan(t, db, exam)

		count, err := srvc.CreateNewPatientEligibilityProgramsForAccount(
			ctx,
			accountId,
			coreapi.ProgramObsp,
		)
		require.NoError(t, err)

		assert.Equal(t, 1, count)

		t.Run("a new notification is created for the eligible patient", func(t *testing.T) {
			row := db.QueryRow(
				"SELECT patient_id, name FROM pockethealth.user_notifications WHERE account_id = ?",
				accountId,
			)

			var notifPatientId string
			var notifName string
			err := row.Scan(&notifPatientId, &notifName)
			require.NoError(t, err)

			assert.Equal(t, exam.patientId, notifPatientId)
			assert.Equal(t, coreapi.NotificationObsp, notifName)
		})

		t.Run("when a second request is made", func(t *testing.T) {
			count, err := srvc.CreateNewPatientEligibilityProgramsForAccount(
				ctx,
				exam.accountId,
				coreapi.ProgramObsp,
			)
			require.NoError(t, err)
			assert.Zero(t, count)

			var rowCount int
			row := db.QueryRow(
				"SELECT COUNT(*) FROM pockethealth.user_notifications WHERE account_id = ?",
				exam.accountId,
			)
			err = row.Scan(&rowCount)
			require.NoError(t, err)
			assert.Equal(t, 1, rowCount)
		})

		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM pockethealth.user_notifications WHERE account_id = ?",
				exam.accountId,
			)
		})
	})

	t.Run("for an unknown program", func(t *testing.T) {
		accountId := ksuid.New().String()

		_, err := srvc.CreateNewPatientEligibilityProgramsForAccount(
			ctx,
			accountId,
			coreapi.ProgramName("unknown_program"),
		)
		assert.Error(t, err)
		assert.Equal(t, "Bad request body", err.Error())
	})
}

func TestUpdateUncompletedEligibilityProgramsForPatient(t *testing.T) {
	db := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	ctx := context.Background()
	acctSrvc := newAcctService(cfg)
	soSrvcUser := newEISVCUser(cfg)
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	soSrvcUser.SetDoRequest(nil)
	srvc := secondopinion.NewSecondOpinionApiService(db, soSrvcUser, *acctSrvc, examService)

	t.Run("when an account has access to a patient", func(t *testing.T) {
		t.Run("and the patient does not have a pep", func(t *testing.T) {
			t.Run("returns a NOT_FOUND error", func(t *testing.T) {
				err := srvc.UpdateUncompletedEligibilityProgramsForPatient(
					ctx,
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					"2cKYg6SWoRPRdRWpcv9kx79YCj7",
					coreapi.ProgramBreastRiskScoring,
					false,
				)
				assert.Error(t, err)
				assert.Equal(t, errormsgs.ERR_NOT_FOUND, err.Error())
			})
		})

		t.Run("and the patient has a pep", func(t *testing.T) {
			t.Run("the request is successful when peps should be updated", func(t *testing.T) {
				err := srvc.UpdateUncompletedEligibilityProgramsForPatient(
					ctx,
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					"2cKYg6SWoRPRdRWpcv9kx79YCj7",
					coreapi.ProgramObsp,
					false,
				)
				assert.NoError(t, err)
			})

			t.Run(
				"the request should be successful the pep should be completed",
				func(t *testing.T) {
					err := srvc.UpdateUncompletedEligibilityProgramsForPatient(
						ctx,
						"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
						"2cKYg6SWoRPRdRWpcv9kx79YCj7",
						coreapi.ProgramObsp,
						true,
					)
					assert.NoError(t, err)
				},
			)
		})
	})

	t.Run("when the account does not have access to a patient", func(t *testing.T) {
		err := srvc.UpdateUncompletedEligibilityProgramsForPatient(
			ctx,
			ksuid.New().String(),
			ksuid.New().String(),
			coreapi.ProgramObsp,
			false,
		)
		assert.Error(t, err)
		assert.Equal(t, errormsgs.ERR_FORBIDDEN, err.Error())
	})
}

func TestCreateNewPatientEligibilityProgramsForPatient(t *testing.T) {
	db := testutils.SetupTestDB(t)
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	ctx := context.Background()
	acctSrvc := newAcctService(cfg)
	soSrvcUser := newEISVCUser(cfg)
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	soSrvcUser.SetDoRequest(nil)
	srvc := secondopinion.NewSecondOpinionApiService(db, soSrvcUser, *acctSrvc, examService)

	t.Run("when an account has access to a patient", func(t *testing.T) {
		t.Run("and the patient has completed peps", func(t *testing.T) {
			t.Run(
				"the request should be successful when creating a completed pep",
				func(t *testing.T) {
					err := srvc.CreateNewPatientEligibilityProgramForPatient(
						ctx,
						"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
						"2cKYg6SWoRPRdRWpcv9kx79YCj7",
						coreapi.ProgramObsp,
						true,
						false,
					)
					assert.NoError(t, err)
				},
			)
		})
		t.Run("and the patient has an incomplete pep", func(t *testing.T) {
			srvc.CreateNewPatientEligibilityProgramForPatient(
				ctx,
				"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
				"2cKYg6SWoRPRdRWpcv9kx79YCj7",
				coreapi.ProgramObsp,
				false,
				false,
			)
			t.Run(
				"the request should be successful when creating a completed pep",
				func(t *testing.T) {
					err := srvc.CreateNewPatientEligibilityProgramForPatient(
						ctx,
						"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
						"2cKYg6SWoRPRdRWpcv9kx79YCj7",
						coreapi.ProgramObsp,
						true,
						false,
					)
					assert.NoError(t, err)
				},
			)

			t.Run(
				"the request should not be successful when creating an incomplete pep",
				func(t *testing.T) {
					err := srvc.CreateNewPatientEligibilityProgramForPatient(
						ctx,
						"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
						"2cKYg6SWoRPRdRWpcv9kx79YCj7",
						coreapi.ProgramObsp,
						false,
						false,
					)
					assert.Error(t, err)
					assert.Equal(t, errormsgs.ERR_CONFLICT, err.Error())
				},
			)
		})

		t.Run("and a notification is requested", func(t *testing.T) {
			t.Cleanup(func() {
				db.Exec(
					"DELETE FROM pockethealth.user_notifications WHERE account_id = ?",
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
				)
			})

			t.Run("creates a notification for an uncompleted PEP", func(t *testing.T) {
				srvc.UpdateUncompletedEligibilityProgramsForPatient(
					ctx,
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					"2cKYg6SWoRPRdRWpcv9kx79YCj7",
					coreapi.ProgramObsp,
					true,
				)
				err := srvc.CreateNewPatientEligibilityProgramForPatient(
					ctx,
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					"2cKYg6SWoRPRdRWpcv9kx79YCj7",
					coreapi.ProgramObsp,
					false,
					true,
				)
				assert.NoError(t, err)

				var rowCount int
				row := db.QueryRow(
					"SELECT COUNT(*) FROM pockethealth.user_notifications WHERE account_id = ? and name = ?",
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					coreapi.NotificationObsp,
				)
				err = row.Scan(&rowCount)
				require.NoError(t, err)
				assert.Equal(t, 1, rowCount)
			})

			t.Run("does not creates a notification for a completed PEP", func(t *testing.T) {
				var beforeCount int
				row := db.QueryRow(
					"SELECT COUNT(*) FROM pockethealth.user_notifications WHERE account_id = ?",
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
				)
				err := row.Scan(&beforeCount)
				require.NoError(t, err)

				err = srvc.CreateNewPatientEligibilityProgramForPatient(
					ctx,
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
					"2cKYg6SWoRPRdRWpcv9kx79YCj7",
					coreapi.ProgramObsp,
					true,
					true,
				)
				assert.NoError(t, err)

				var afterCount int
				row = db.QueryRow(
					"SELECT COUNT(*) FROM pockethealth.user_notifications WHERE account_id = ?",
					"2cKYgC0ZmaIa49z4DAUZhx9wcaw",
				)
				err = row.Scan(&afterCount)
				require.NoError(t, err)
				assert.Equal(t, beforeCount, afterCount)
			})
		})

		// need to create an endpoint on SO to delete PEPs so we can cleanup
	})

	t.Run("when the account does not have access to a patient", func(t *testing.T) {
		err := srvc.CreateNewPatientEligibilityProgramForPatient(
			ctx,
			ksuid.New().String(),
			ksuid.New().String(),
			coreapi.ProgramObsp,
			false,
			false,
		)
		assert.Error(t, err)
		assert.Equal(t, errormsgs.ERR_FORBIDDEN, err.Error())
	})
}

type TestExamScanSetup struct {
	examId    string
	accountId string
	patientId string
	dob       string
	sex       string
	modality  string
	date      string
	scanId    string
	orgId     int
	source    string
}

func createExamAndScan(t *testing.T, db *sql.DB, data TestExamScanSetup) {
	t.Helper()

	_, err := db.Exec(
		"INSERT INTO pockethealth.exams (uuid, exam_uid, account_id, patient_id, dob, sex, modality, date, transfer_id, activated) VALUES (?,?,?,?,?,?,?,?,?,?)",
		data.examId,
		data.examId,
		data.accountId,
		data.patientId,
		data.dob,
		data.sex,
		data.modality,
		data.date,
		data.scanId,
		1,
	)
	require.NoError(t, err)

	_, err = db.Exec(
		"INSERT INTO pockethealth.scans (scan_id, origin_id, source, ph_patient_id) VALUES(?,?,?,?)",
		data.scanId,
		data.orgId,
		data.source,
		data.patientId,
	)
	require.NoError(t, err)

	t.Cleanup(func() {
		db.Exec("DELETE FROM pockethealth.exams WHERE uuid = ?", data.examId)
		db.Exec("DELETE FROM pockethealth.scans WHERE scan_id = ?", data.scanId)
	})
}

func newAcctService(cfg testutils.IntegTestConfig) *accountservice.AccountServiceClient {
	acctSrvcUser := "apptest_rw"
	acctSrvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"
	httpClient := httpclient.NewHTTPClient(
		http.DefaultClient,
		nil,
	)

	return accountservice.NewHTTPAccountServiceClient(
		cfg.AcctSvcUrl,
		acctSrvcUser,
		acctSrvcApiKey,
		httpClient,
	)
}

func newEISVCUser(cfg testutils.IntegTestConfig) examinsights.EIServiceUser {
	return examinsights.EIServiceUser{
		URL:           cfg.ExamInsightsSrvcUrl,
		ApiKey:        cfg.ExamInsightsSrvcApiKey,
		ApiKeySecName: cfg.ExamInsightsSrvcApiKeySecName,
		HttpClient:    http.DefaultClient,
	}
}
