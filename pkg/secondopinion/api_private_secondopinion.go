package secondopinion

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivateSecondOpinionApiController struct {
	service coreapi.SecondOpinionApiServicer
}

func NewPrivateSecondOpinionApiController(
	s coreapi.SecondOpinionApiServicer,
) coreapi.PrivateSecondOpinionApiRouter {
	return &PrivateSecondOpinionApiController{service: s}
}

func (c *PrivateSecondOpinionApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostSecondOpinionRequest",
			Method:      http.MethodPost,
			Pattern:     "/review/{examUuid}",
			HandlerFunc: c.PostSecondOpinionRequest,
		},
		{
			Name:        "CheckSecondOpinionEligiblePriors",
			Method:      http.MethodPost,
			Pattern:     "/eligible/priors",
			HandlerFunc: c.CheckSecondOpinionEligiblePriors,
		},
		{
			Name:        "GetSecondOpinionEligibilityStatus",
			Method:      http.MethodGet,
			Pattern:     "/eligible/{examUuid}",
			HandlerFunc: c.GetSecondOpinionEligibilityStatus,
		},
		{
			Name:        "SearchSecondOpinionDoctors",
			Method:      http.MethodGet,
			Pattern:     "/doctors/search",
			HandlerFunc: c.SearchSecondOpinionDoctors,
		},
		{
			Name:        "CreateNewPatientEligibilityProgramsForAccount",
			Method:      http.MethodPost,
			Pattern:     "/patient_eligibility/{programName}",
			HandlerFunc: c.CreateNewPatientEligibilityProgramsForAccount,
		},
		{
			Name:        "UpdateUncompletedEligibilityProgramsForPatient",
			Method:      http.MethodPut,
			Pattern:     "/patient_eligibility/{patientId}/{programName}",
			HandlerFunc: c.UpdateUncompletedEligibilityProgramsForPatient,
		},
		{
			Name:        "CreateNewPatientEligibilityProgramForPatient",
			Method:      http.MethodPost,
			Pattern:     "/patient_eligibility",
			HandlerFunc: c.CreateNewPatientEligibilityProgramsForPatient,
		},
	}
}

func (c *PrivateSecondOpinionApiController) GetPathPrefix() string {
	return "/v2/secondopinion"
}

func (c *PrivateSecondOpinionApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateSecondOpinionApiController) PostSecondOpinionRequest(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	params := mux.Vars(r)
	examUuid := params["examUuid"]

	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)

	decoder := json.NewDecoder(r.Body)
	var t coreapi.SORequest
	err := decoder.Decode(&t)
	if err != nil {
		lg.WithError(err).Error("error decoding request body")
	}
	reviewId, err := c.service.PostSecondOpinionRequest(r.Context(), examUuid, accountId, t)
	if hasError(&w, r, err) {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("error posting second opinion review request")
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), reviewId, nil, w)
}

func (c *PrivateSecondOpinionApiController) GetSecondOpinionEligibilityStatus(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	examUuid := params["examUuid"]
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)

	result, err := c.service.GetSecondOpinionEligibilityStatus(r.Context(), examUuid, accountId)
	if hasError(&w, r, err) {
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateSecondOpinionApiController) CheckSecondOpinionEligiblePriors(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, _ := auth.DecodeAccountToken(token)

	decoder := json.NewDecoder(r.Body)
	var examUuids coreapi.SoEligiblePriorsRequest
	err := decoder.Decode(&examUuids)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("error decoding request body")
	}

	result, err := c.service.CheckSecondOpinionEligiblePriors(r.Context(), examUuids, accountId)
	if hasError(&w, r, err) {
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateSecondOpinionApiController) SearchSecondOpinionDoctors(
	w http.ResponseWriter,
	r *http.Request,
) {
	values := r.URL.Query()

	query := make(map[string]string, 0)
	for k := range values {
		query[k] = values.Get(k)
	}

	result, err := c.service.SearchSecondOpinionDoctors(r.Context(), query)
	if hasError(&w, r, err) {
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateSecondOpinionApiController) CreateNewPatientEligibilityProgramsForAccount(
	w http.ResponseWriter,
	r *http.Request,
) {
	log := logutils.DebugCtxLogger(r.Context())

	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		log.WithError(err).Info("cannot decode request account token from token header")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
	}
	log = log.WithField("account_id", accountId)

	params := mux.Vars(r)
	programName := coreapi.ProgramName(params["programName"])
	log = log.WithField("program_name", programName)

	newPepsCount, err := c.service.CreateNewPatientEligibilityProgramsForAccount(r.Context(), accountId, programName)
	if hasError(&w, r, err) {
		log.WithError(err).Error("Could not create new patient eligibility programs for account")
		return
	}

	status := http.StatusOK
	if newPepsCount > 0 {
		status = http.StatusCreated
	}

	// We don't really care about the actual peps that are/are not created so just return the status so that the FE knows
	// what happened so it can fetch new notifications or not.
	w.WriteHeader(status)
}

func (c *PrivateSecondOpinionApiController) UpdateUncompletedEligibilityProgramsForPatient(
	w http.ResponseWriter,
	r *http.Request,
) {
	log := logutils.DebugCtxLogger(r.Context())

	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		log.WithError(err).Info("cannot decode request account token from token header")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
	}
	log = log.WithField("account_id", accountId)

	params := mux.Vars(r)
	patientId := params["patientId"]
	programName := params["programName"]

	log = log.WithFields(logrus.Fields{
		"patient_id":   patientId,
		"program_name": programName,
	})

	var reqBody coreapi.SORequestUpdateUncompletedEligibilityPrograms
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&reqBody)
	if hasError(&w, r, err) {
		log.WithError(err).Error("Could not decode request body")
		return
	}

	log = log.WithField("is_completed", reqBody.IsCompleted)

	err = c.service.UpdateUncompletedEligibilityProgramsForPatient(r.Context(), accountId, patientId, coreapi.ProgramName(programName), reqBody.IsCompleted)
	if hasError(&w, r, err) {
		log.WithError(err).Error("Could not update uncompleted eligibility programs for patient")
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (c *PrivateSecondOpinionApiController) CreateNewPatientEligibilityProgramsForPatient(w http.ResponseWriter, r *http.Request) {
	log := logutils.DebugCtxLogger(r.Context())

	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		log.WithError(err).Info("cannot decode request account token from token header")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
	}
	log = log.WithField("account_id", accountId)

	var reqBody coreapi.SORequestCreatePatientEligibilityProgram
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&reqBody)
	if hasError(&w, r, err) {
		log.WithError(err).Error("Could not decode request body")
		return
	}
	log = log.WithFields(logrus.Fields{"patient_id": reqBody.PatientId, "program_name": reqBody.ProgramName, "is_completed": reqBody.IsCompleted})

	err = c.service.CreateNewPatientEligibilityProgramForPatient(
		r.Context(),
		accountId,
		reqBody.PatientId,
		reqBody.ProgramName,
		reqBody.IsCompleted,
		reqBody.WithNotification,
	)
	if hasError(&w, r, err) {
		log.WithError(err).Error("Could not create eligibility program for patient")
		return
	}

	w.WriteHeader(http.StatusCreated)
}

func hasError(w *http.ResponseWriter, r *http.Request, err error) bool {
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			status = http.StatusUnauthorized
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			status = http.StatusNotFound
		} else if err.Error() == errmsg.ERR_CONFLICT {
			status = http.StatusConflict
		} else if err.Error() == errmsg.ERR_INVALID_REQ_BODY {
			status = http.StatusBadRequest
		} else if err.Error() == errmsg.ERR_FORBIDDEN {
			// If the user is not authorized to view a resource then present them with a 404 instead.
			status = http.StatusNotFound
			err = errors.New(errmsg.ERR_NOT_FOUND)
		}
		httperror.ErrorWithLog(*w, r, err.Error(), status)
		return true
	}
	return false
}
