package secondopinion

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	notifs "gitlab.com/pockethealth/coreapi/pkg/mysql/notifications"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	examinsights "gitlab.com/pockethealth/coreapi/pkg/services/examinsightsservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type SecondOpinionApiService struct {
	sqldb              *sql.DB
	examinsightsClient examinsights.EIServiceUser
	accountSrvcClient  accountservice.AccountServiceClient
	examService        exams.ExamServiceInterface
}

func NewSecondOpinionApiService(
	db *sql.DB,
	examinsightsClient examinsights.EIServiceUser,
	accountSrvcClient accountservice.AccountServiceClient,
	examService exams.ExamServiceInterface,
) coreapi.SecondOpinionApiServicer {
	return &SecondOpinionApiService{
		sqldb:              db,
		examinsightsClient: examinsightsClient,
		accountSrvcClient:  accountSrvcClient,
		examService:        examService,
	}
}

func (s *SecondOpinionApiService) PostSecondOpinionRequest(
	ctx context.Context,
	examUuid string,
	accountId string,
	e coreapi.SORequest,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"exam_uuid":  examUuid,
	})

	canAccess, err := s.canAccessExam(ctx, examUuid, accountId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to exam")
		return "", err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access exam")
		return "", errors.New(errmsg.ERR_FORBIDDEN)
	}

	var soRequest examinsights.PostSecondOpinionServiceRequest
	soRequest.ExamUuid = examUuid
	soRequest.AccountId = accountId
	soRequest.PriorExams = e.PriorExams
	soRequest.PatientDetail = e.PatientDetail
	soRequest.ReferringDetail = e.ReferringDetail

	reviewId, err := s.examinsightsClient.PostSecondOpinionRequest(ctx, soRequest)
	if err != nil {
		lg.WithError(err).Error("failed to post second op req")
		return "", err
	}

	return reviewId, nil
}

func (s *SecondOpinionApiService) GetSecondOpinionEligibilityStatus(
	ctx context.Context,
	examUuid string,
	accountId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid": examUuid,
	})

	canAccess, err := s.canAccessExam(ctx, examUuid, accountId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to exam")
		return nil, err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access exam")
		return nil, errors.New(errmsg.ERR_FORBIDDEN)
	}

	resp, err := s.examinsightsClient.GetSecondOpinionEligibilityStatus(ctx, examUuid)
	if err != nil {
		lg.WithError(err).Error("failed to get second op eligibility")
		return nil, err
	}

	return resp, nil
}

func (s *SecondOpinionApiService) CheckSecondOpinionEligiblePriors(
	ctx context.Context,
	request coreapi.SoEligiblePriorsRequest,
	accountId string,
) ([]string, error) {

	found, err := s.examService.ExamsBelongToAccount(ctx, accountId, request.ExamUuids)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("error checking for access to exams")

		return nil, err
	}
	if !found {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("no exams found")
		return nil, errors.New(errmsg.ERR_NOT_FOUND)
	}

	resp, err := s.examinsightsClient.CheckSecondOpinionEligiblePriors(ctx, request)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("failed to get second op eligibility")
		return nil, err
	}

	return resp, nil
}

func (s *SecondOpinionApiService) SearchSecondOpinionDoctors(
	ctx context.Context,
	query map[string]string,
) ([]coreapi.SOCPSODoctor, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query": query,
	})

	resp, err := s.examinsightsClient.SearchSecondOpinionDoctors(ctx, query)
	if err != nil {
		lg.WithError(err).Error("failed to get second op eligibility")
		return nil, err
	}

	return resp, nil
}

// Create new Patient Eligibility Programs for an account, and then create new notifications for them.
// Just returns the number of newly created notifications as currently we don't do anything with the PEPs themselves
// and just need to indicate to the FE wether or not new records were made.
func (s *SecondOpinionApiService) CreateNewPatientEligibilityProgramsForAccount(ctx context.Context, accountId string, programName coreapi.ProgramName) (int, error) {
	peps, err := s.examinsightsClient.CreatePatientEligibilitiesProgramForAccount(ctx, accountId, programName)
	if err != nil {
		return 0, err
	}

	for _, pep := range peps {
		err := s.createPepNotification(ctx, pep.AccountId, pep.PatientId, programName)
		if err != nil {
			continue
		}
	}

	return len(peps), nil
}

func (s *SecondOpinionApiService) UpdateUncompletedEligibilityProgramsForPatient(ctx context.Context, accountId string, patientId string, programName coreapi.ProgramName, isCompleted bool) error {
	log := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":   accountId,
		"patient_id":   patientId,
		"program_name": programName,
		"is_completed": isCompleted,
	})

	if !s.accountSrvcClient.AccountHasAccessToPatient(ctx, accountId, patientId) {
		return fmt.Errorf(errormsgs.ERR_FORBIDDEN)
	}

	err := s.examinsightsClient.UpdateUncompletedEligibilityProgramsForPatient(ctx, patientId, programName, isCompleted)
	if err != nil {
		log.WithError(err).Error("Could not update uncompleted patient eligibility programs")
		return err
	}

	return nil
}

func (s *SecondOpinionApiService) CreateNewPatientEligibilityProgramForPatient(ctx context.Context, accountId string, patientId string, programName coreapi.ProgramName, isCompleted bool, withNotification bool) error {
	log := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":        accountId,
		"patient_id":        patientId,
		"program_name":      programName,
		"is_completed":      isCompleted,
		"with_notification": withNotification,
	})

	if !s.accountSrvcClient.AccountHasAccessToPatient(ctx, accountId, patientId) {
		return fmt.Errorf(errormsgs.ERR_FORBIDDEN)
	}
	err := s.examinsightsClient.CreatePatientEligibilityProgramForPatient(ctx, accountId, patientId, programName, isCompleted)
	if err != nil {
		log.WithError(err).Errorf("Could not create patient eligibility program for %s", patientId)
		return err
	}

	// Don't create a new notification for a newly created and completed PEP.
	if withNotification && !isCompleted {
		err = s.createPepNotification(ctx, accountId, patientId, programName)
		if err != nil {
			log.WithError(err).Errorf("Could not create pep notification for %s", patientId)
		}
	}

	return nil
}

func (s *SecondOpinionApiService) canAccessExam(
	ctx context.Context,
	examUuid string,
	accountId string,
) (bool, error) {

	var exam = []string{examUuid}
	found, err := s.examService.ExamsBelongToAccount(ctx, accountId, exam)
	if err != nil {
		return false, err
	}
	if !found {
		return false, errors.New(errmsg.ERR_NOT_FOUND)
	}

	return found, nil
}

func (s *SecondOpinionApiService) createPepNotification(
	ctx context.Context,
	accountId string,
	patientId string,
	programName coreapi.ProgramName,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{
			"acct_id":      accountId,
			"patient_id":   patientId,
			"program_name": programName,
		})

	notifName, err := programNameToNotificationName(programName)
	if err != nil {
		lg.WithError(err).Error("Could not get notification name")
		return err
	}

	_, err = notifs.CreateAccountNotification(
		ctx,
		s.sqldb,
		ksuid.New().String(),
		accountId,
		notifName,
		patientId,
		nil,
	)
	if err != nil {
		lg.WithError(err).Error("failed to create patient eligibility program notification")
		return err
	}

	return nil
}

func programNameToNotificationName(programName coreapi.ProgramName) (coreapi.NotificationType, error) {
	switch programName {
	case coreapi.ProgramObsp:
		return coreapi.NotificationObsp, nil
	case coreapi.ProgramBreastRiskScoring:
		return coreapi.NotificationBreastRiskScoring, nil
	default:
		return "", fmt.Errorf("unknown programName: %v", programName)
	}
}
