//go:build integration
// +build integration

package v2transfers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"reflect"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

func TestPostTransfer(t *testing.T) {

	db := testutils.SetupTestDB(t)

	// initialize service
	provSvcUser := providersservice.ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	// initialize cache for auth token
	provSvcUser.AuthTokenCache.SetCacheLength(time.Duration(60))

	mockIsAuthForFeature := func(token string, featureId uint64) (bool, error) {
		if featureId != uint64(planservice.CD_UPLOADING) {
			return false, errors.New("should only check CD UPLOADING feature here")
		}
		if token == "Unlimited" {
			return true, nil
		} else {
			return false, nil
		}
	}
	testemail := "<EMAIL>"
	s := V2TransfersApiService{
		sqldb:    db,
		provUser: provSvcUser,
		// unused
		frontEndHost: "test",
		acctclient: &accountservice.AcctSvcMock{
			GetAccountInfoReturn: accountservice.Account{
				MainRegion: 1,
				Email:      testemail,
			},
		},
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run(
		"when valid input, should send correct manifest to prov svc and create upload_sessions entry",
		func(t *testing.T) {
			userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"
			userIp := "***********"
			uploadImages := []coreapi.UploadImage{
				{
					Partname:   "asdf",
					StudyId:    "1234",
					SeriesId:   "1234",
					InstanceId: "1234",
					Sha1:       "1ivndsoadv",
				},
			}

			// prov svc response data
			token, _ := secure.GenerateRandomString(16)
			authBody := providersservice.ProvAuthenticateResponse{
				Token: token,
			}
			randStr, _ := secure.GenerateRandomString(16)
			transferId := "test_" + randStr

			// mock prov svc requests
			doRequestWithCtxMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
				body, _ := ioutil.ReadAll(req.Body)
				var jsonMap map[string]json.RawMessage
				json.Unmarshal(body, &jsonMap)

				provSvcRes := http.Response{
					StatusCode: http.StatusOK,
				}

				if req.Method == "POST" &&
					req.URL.String() == "https://"+provSvcUser.URL+"/v1/authenticate" {
					// authenticate request

					// validate request fields
					var username string
					json.Unmarshal(jsonMap["username"], &username)
					if username != provSvcUser.User {
						t.Errorf("expected %s, got %s", provSvcUser.User, username)
					}
					var password string
					json.Unmarshal(jsonMap["password"], &password)
					if password != provSvcUser.PW {
						t.Errorf("expected %s, got %s", provSvcUser.User, password)
					}

					// setup respopnse
					body, _ := json.Marshal(authBody)
					provSvcRes.Body = ioutil.NopCloser(bytes.NewReader(body))
				} else if req.Method == "POST" && req.URL.String() == "https://"+provSvcUser.URL+"/v2/transfers/init" {
					// transfer init request

					// validate authorization
					authToken := req.Header.Get("X-Auth-Token")
					if authToken != token {
						t.Errorf("bad authorization: expected %s, got %s", token, authToken)
					}

					// validate request fields
					var email string
					json.Unmarshal(jsonMap["email"], &email)
					if email != testemail {
						t.Errorf("expected %s, got %s", testemail, email)
					}

					var imageList []coreapi.UploadImage
					json.Unmarshal(jsonMap["images"], &imageList)
					if !reflect.DeepEqual(imageList, uploadImages) {
						t.Error("manifest image list not equal")
					}

					// setup response
					body, _ := json.Marshal(transferId)
					provSvcRes.Body = ioutil.NopCloser(bytes.NewReader(body))
				} else {
					t.Errorf("unexpected request to prov svc: %s %s", req.Method, req.URL.String())
				}

				return &provSvcRes, nil
			}
			s.doRequestWithCtx = doRequestWithCtxMock

			// run method
			resp, err := s.PostTransfer(
				context.Background(),
				"anaccountid",
				userIp,
				userAgent,
				"Unlimited",
				uploadImages,
			)
			if err != nil {
				t.Errorf("got error expected none: %q", err.Error())
			}
			if resp.(coreapi.UploadInitResponse).TransferId != transferId {
				t.Errorf(
					"expected %s, got %s",
					transferId,
					resp.(coreapi.UploadInitResponse).TransferId,
				)
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM upload_sessions WHERE transfer_id=?", transferId)
			})

			// check that correct transferId in upload sessions
			var gotTransferId string
			err = db.QueryRow("SELECT transfer_id FROM upload_sessions WHERE session_id=?", resp.(coreapi.UploadInitResponse).UploadSessionId).
				Scan(&gotTransferId)
			if err != nil {
				t.Errorf("unexpected error checking upload_sessions tbl: %q", err.Error())
			}
			if transferId != gotTransferId {
				t.Errorf("expected %s, got %s", transferId, gotTransferId)
			}
		},
	)

	t.Run("when user is not subscribed to Connect, should return error", func(t *testing.T) {
		userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"

		// run method
		_, err := s.PostTransfer(
			context.Background(),
			"anaccountid",
			"***********",
			userAgent,
			"Free",
			[]coreapi.UploadImage{},
		)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err.Error() != errormsgs.ERR_NO_CD_UPLOAD_ACCESS {
			t.Errorf("expected %s, got %q", errormsgs.ERR_NO_CD_UPLOAD_ACCESS, err.Error())
		}
	})

	t.Run("when no images in list, should return error", func(t *testing.T) {
		userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"

		// run method
		_, err := s.PostTransfer(
			context.Background(),
			"anaccountid",
			"***********",
			userAgent,
			"Unlimited",
			[]coreapi.UploadImage{},
		)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err.Error() != errormsgs.ERR_UPLOAD_NO_IMAGE {
			t.Errorf("expected %s, got %q", errormsgs.ERR_UPLOAD_NO_IMAGE, err.Error())
		}
	})
}

func TestPostTransferImages(t *testing.T) {

	db := testutils.SetupTestDB(t)

	// initialize service
	provSvcUser := providersservice.ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	s := V2TransfersApiService{
		sqldb:    db,
		provUser: provSvcUser,
		// unused
		frontEndHost: "test",
		acctclient:   &accountservice.AcctSvcMock{},
	}

	t.Run("when not valid upload session, should return error", func(t *testing.T) {
		rand, _ := secure.GenerateRandomString(16)
		sessionId := "test_session_id_" + rand
		transferId := "test_transfer_id_" + rand
		userIp := "***********"
		userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"

		// run method
		err := s.PostTransferImages(
			context.Background(),
			transferId,
			sessionId,
			userIp,
			userAgent,
			nil,
		)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected %s, got %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})
}

func TestPostTransferReportDCM(t *testing.T) {

	db := testutils.SetupTestDB(t)

	// initialize service
	provSvcUser := providersservice.ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	s := V2TransfersApiService{
		sqldb:    db,
		provUser: provSvcUser,
		// unused
		frontEndHost: "test",
		acctclient:   &accountservice.AcctSvcMock{},
	}

	t.Run("when not valid upload session, should return error", func(t *testing.T) {
		rand, _ := secure.GenerateRandomString(16)
		sessionId := "test_session_id_" + rand
		transferId := "test_transfer_id_" + rand
		userIp := "***********"
		userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"

		// run method
		err := s.PostTransferReportDCM(
			context.Background(),
			transferId,
			sessionId,
			userIp,
			userAgent,
			coreapi.UploadFileMetadata{},
			nil,
		)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected %s, got %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})
}

func TestDeleteTransfer(t *testing.T) {

	db := testutils.SetupTestDB(t)

	// initialize service
	provSvcUser := providersservice.ProvSvcUser{
		URL:  "test.provider.pocket.health",
		User: "<EMAIL>",
		PW:   "g0odP@s5w0rD",
	}
	s := V2TransfersApiService{
		sqldb:    db,
		provUser: provSvcUser,
		// unused
		frontEndHost: "test",
		acctclient:   &accountservice.AcctSvcMock{},
	}

	t.Run("when not valid upload session, should return error", func(t *testing.T) {
		rand, _ := secure.GenerateRandomString(16)
		sessionId := "test_session_id_" + rand
		transferId := "test_transfer_id_" + rand
		userIp := "***********"
		userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"

		// run method
		err := s.DeleteTransfer(
			context.Background(),
			transferId,
			sessionId,
			userIp,
			userAgent,
		)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err.Error() != errormsgs.ERR_NOT_AUTHORIZED {
			t.Errorf("expected %s, got %q", errormsgs.ERR_NOT_AUTHORIZED, err.Error())
		}
	})

	t.Run(
		"when valid input, should send delete request to prov svc and remove upload session in db",
		func(t *testing.T) {
			rand, _ := secure.GenerateRandomString(16)
			sessionId := "test_session_id_" + rand
			transferId := "test_transfer_id_" + rand
			userIp := "***********"
			userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6)"
			clientId := userIp + userAgent
			authToken, _ := secure.GenerateRandomString(32)

			_, err := db.Exec(
				"INSERT upload_sessions SET session_id = ?, transfer_id = ?, client_id = ?, auth = ?",
				sessionId,
				transferId,
				clientId,
				authToken,
			)
			if err != nil {
				t.Fatalf("error setting up upload_session: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM upload_sessions WHERE session_id=?", sessionId)
			})

			// mock prov svc request
			doRequestWithCtxMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
				provSvcRes := http.Response{
					StatusCode: http.StatusOK,
				}

				if req.Method == "DELETE" &&
					req.URL.String() == "https://"+provSvcUser.URL+"/v2/transfers/"+transferId {
					// validate authorization
					token := req.Header.Get("X-Auth-Token")
					if token != authToken {
						t.Errorf("bad authorization: expected %s, got %s", authToken, token)
					}
				} else {
					t.Errorf("unexpected request to prov svc: %s %s", req.Method, req.URL.String())
				}

				return &provSvcRes, nil
			}
			s.doRequestWithCtx = doRequestWithCtxMock

			// run method
			err = s.DeleteTransfer(
				context.Background(),
				transferId,
				sessionId,
				userIp,
				userAgent,
			)
			if err != nil {
				t.Errorf("got error expected none")
			}

			var got string
			err = db.QueryRow("SELECT transfer_id FROM upload_sessions WHERE session_id=?", sessionId).
				Scan(&got)
			if err == nil {
				t.Errorf("expected error got none")
			} else if err != sql.ErrNoRows {
				t.Errorf("expected no rows, got error %q", err.Error())
			}
		},
	)
}
