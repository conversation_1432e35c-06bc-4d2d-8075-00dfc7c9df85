package v2transfers

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateV2TransfersApiController binds http requests to an api service and writes the service results to the http response
type PrivateV2TransfersApiController struct {
	service coreapi.V2TransfersApiServicer
}

// NewPrivateTransfersApiController creates a default api controller
func NewPrivateV2TransfersApiController(
	s coreapi.V2TransfersApiServicer,
) coreapi.PrivateV2TransfersApiRouter {
	return &PrivateV2TransfersApiController{service: s}
}

// Routes returns all of the api route for the V2TransfersApiController
func (c *PrivateV2TransfersApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostTransferImages",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{transferId}/images",
			HandlerFunc: c.PostTransferImages,
		},
		{
			Name:        "PostTransferReportDCM",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{transferId}/reportdcm",
			HandlerFunc: c.PostTransferReportDCM,
		},
		{
			Name:        "DeleteTransfer",
			Method:      strings.ToUpper("Delete"),
			Pattern:     "/{transferId}",
			HandlerFunc: c.DeleteTransfer,
		},
		{
			Name:        "PostTransfer",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostTransfer,
		},
	}
}

func (c *PrivateV2TransfersApiController) GetPathPrefix() string {
	return "/v2/transfers"
}

func (c *PrivateV2TransfersApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

// Post transfer - initialize a self-uploaded transfer
func (c *PrivateV2TransfersApiController) PostTransfer(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("Authorization")
	acctId, _ := auth.DecodeAccountToken(token)

	imageList := []coreapi.UploadImage{}
	err := json.NewDecoder(r.Body).Decode(&imageList)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	result, err := c.service.PostTransfer(
		r.Context(),
		acctId,
		strings.Split(r.RemoteAddr, ":")[0],
		r.UserAgent(),
		token,
		imageList,
	)
	if err != nil {
		if err.Error() == errormsgs.ERR_NO_CD_UPLOAD_ACCESS {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusForbidden)
			return
		}
		if err.Error() == errormsgs.ERR_UPLOAD_NO_IMAGE {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostTransferImages - upload dcm images for a transfer
func (c *PrivateV2TransfersApiController) PostTransferImages(
	w http.ResponseWriter,
	r *http.Request,
) {
	ctx := r.Context()
	params := mux.Vars(r)
	transferId := params["transferId"]

	uploadSessionId := r.Header.Get("upload-session-id")
	if uploadSessionId == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	reader, err := r.MultipartReader()
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't get multipart reader for post images")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	err = c.service.PostTransferImages(
		ctx,
		transferId,
		uploadSessionId,
		strings.Split(r.RemoteAddr, ":")[0],
		r.UserAgent(),
		reader,
	)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(200)
}

// PostTransferReportDCM - upload dcm report for a transfer
func (c *PrivateV2TransfersApiController) PostTransferReportDCM(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	transferId := params["transferId"]

	uploadSessionId := r.Header.Get("upload-session-id")
	if uploadSessionId == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	err := r.ParseMultipartForm(2 << 30) //2GB, max we expect for an image
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).WithError(err).Error("couldn't parse multipart form")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	m := r.MultipartForm
	fileHeader := m.File["file"]

	var fileMetadata coreapi.UploadFileMetadata
	metadataBodyStr := r.FormValue("metadata")
	err = json.Unmarshal([]byte(metadataBodyStr), &fileMetadata)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PostTransferReportDCM(
		r.Context(),
		transferId,
		uploadSessionId,
		strings.Split(r.RemoteAddr, ":")[0],
		r.UserAgent(),
		fileMetadata,
		fileHeader,
	)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(200)
}

// DeleteTransfer - roll back any incomplete self-uploaded transfers
func (c *PrivateV2TransfersApiController) DeleteTransfer(w http.ResponseWriter, r *http.Request) {

	params := mux.Vars(r)
	transferId := params["transferId"]

	uploadSessionId := r.Header.Get("upload-session-id")
	if uploadSessionId == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	err := c.service.DeleteTransfer(
		r.Context(),
		transferId,
		uploadSessionId,
		strings.Split(r.RemoteAddr, ":")[0],
		r.UserAgent(),
	)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}

	w.WriteHeader(200)
}
