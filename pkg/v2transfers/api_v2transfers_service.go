package v2transfers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"
	"net/http"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	uploadsessions "gitlab.com/pockethealth/coreapi/pkg/mysql/uploadsessions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	requestwithcontext "gitlab.com/pockethealth/coreapi/pkg/util/requestWithContext"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// TransfersApiService is a service that implents the logic for the TransfersApiServicer
// This service should implement the business logic for every endpoint for the TransfersApi API.
// Include any external packages or services that will be required by this service.
type V2TransfersApiService struct {
	sqldb        *sql.DB
	provUser     providersservice.ProvSvcUser
	frontEndHost string
	acctclient   accountservice.AccountService

	doRequestWithCtx requestwithcontext.RequestWithCtx

	// injected functions (so we can mock them when testing)
	isAuthForFeature IsAuthForFeatureFunc
}

type IsAuthForFeatureFunc func(token string, featureId uint64) (bool, error)

type UploadEmail struct {
	URL string
}

// transferStatus and ImageUploadStatus are from providers service
type TransferStatus struct {
	Complete bool `json:"complete"`

	Images []ImageUploadStatus `json:"images"`

	TransferId string `json:"transfer_id"`
}

type ImageUploadStatus struct {
	Partname string `json:"partname"`

	ObjectId string `json:"object_id"`

	Status string `json:"status"`

	LastUpdate string `json:"last_update"`
}

// NewTransfersApiService creates a default api service
func NewV2TransfersApiService(
	db *sql.DB,
	provSvcUser providersservice.ProvSvcUser,
	feHost string,
	as accountservice.AccountService,
) coreapi.V2TransfersApiServicer {
	return &V2TransfersApiService{
		sqldb:            db,
		provUser:         provSvcUser,
		frontEndHost:     feHost,
		doRequestWithCtx: requestwithcontext.DoRequestWithCtx,
		acctclient:       as,
		isAuthForFeature: auth.IsAuthForFeature,
	}
}

// PostTransfer - Initialize self-upload transfer
func (s *V2TransfersApiService) PostTransfer(
	ctx context.Context,
	acctId string,
	ip string,
	userAgent string,
	token string,
	imageList []coreapi.UploadImage,
) (interface{}, error) {
	//check if user is allowed uploads
	isAuth, err := s.isAuthForFeature(token, uint64(planservice.CD_UPLOADING))
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Error("unable to check if user has auth to access cd upload feature")
		return nil, err
	}
	if !isAuth {
		logutils.DebugCtxLogger(ctx).Error("user has no access to cd uploads feature")
		return nil, errors.New(errormsgs.ERR_NO_CD_UPLOAD_ACCESS)
	}

	if len(imageList) == 0 {
		logutils.DebugCtxLogger(ctx).Error("no images included in transfer")
		return nil, errors.New(errormsgs.ERR_UPLOAD_NO_IMAGE)
	}

	acct, err := s.acctclient.GetAccountInfo(ctx, acctId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't get account info")
		return nil, err
	}
	userEmail := acct.Email

	//Authenticate with prov svc.
	authResp, err := s.provUser.GetAuthToken(ctx, s.doRequestWithCtx)
	if err != nil || authResp == "" {
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	//create manifest object to send to providers' service
	manifest := coreapi.Manifest{}
	manifest.Images = imageList
	manifest.Ver = "1"
	manifest.Email = userEmail

	postBody, _ := json.Marshal(manifest)
	b := bytes.NewBuffer(postBody)

	//send http POST request to providers' service
	req, err := http.NewRequest("POST", "https://"+s.provUser.URL+"/v2/transfers/init", b)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't init transfer")
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Auth-Token", authResp)
	initResp, err := s.doRequestWithCtx(ctx, s.provUser.Client, req)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("Prov svc init failed")
		return nil, err
	}
	if initResp.StatusCode != http.StatusOK {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"prov_resp_status": initResp.StatusCode,
		}).Error("Prov svc init failed")
		return nil, errors.New("init failed")
	}

	var transferId string
	err = json.NewDecoder(initResp.Body).Decode(&transferId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't get transferId from response")
		return nil, err
	}

	sessionId, err := uploadsessions.CreateSession(
		ctx,
		s.sqldb,
		uploadsessions.UploadSession{
			TransferId: transferId,
			ClientId:   ip + userAgent,
			AuthToken:  authResp,
		},
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't create upload session")
		return nil, err
	}

	return coreapi.UploadInitResponse{TransferId: transferId, UploadSessionId: sessionId}, nil
}

// PostTransferImages - upload all images for a transfer
func (s *V2TransfersApiService) PostTransferImages(
	ctx context.Context,
	transferId string,
	sessionId string,
	ip string,
	userAgent string,
	reader *multipart.Reader,
) error {
	authToken := uploadsessions.GetV2SessionToken(
		ctx,
		s.sqldb,
		uploadsessions.UploadSession{
			SessionId:  sessionId,
			TransferId: transferId,
			ClientId:   ip + userAgent,
		},
	)
	if authToken == "" {
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}
	lg := logutils.DebugCtxLogger(ctx).WithField("transfer_id", transferId)

	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	for pn := 1; ; pn += 1 {
		var fw io.Writer
		part, err := reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		lgPn := lg.WithFields(logrus.Fields{
			"pn":        pn,
			"part_name": part.FileName(),
		})
		if part.FileName() == "" {
			continue
		}

		if fw, err = w.CreateFormFile("file", part.FileName()); err != nil {
			lgPn.WithError(err).Error("couldn't create form file")
			return err
		}
		if _, err = io.Copy(fw, part); err != nil {
			lgPn.WithError(err).Error("couldn't copy form file")
			return err
		}
	}
	// If not closed, request will be missing the terminating boundary.
	err := w.Close()
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("Prov svc writer close failed")
		return err
	}

	//forward the images along to providers service to begin uploading
	req, err := http.NewRequest(
		"POST",
		"https://"+s.provUser.URL+"/v2/transfers/images/"+transferId,
		&b,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't post images")
		return err
	}

	req.Header.Set("X-Auth-Token", authToken)
	req.Header.Set(
		"Content-Type",
		w.FormDataContentType(),
	) //this sets the boundary as well as the content type
	resp, err1 := s.doRequestWithCtx(ctx, s.provUser.Client, req)
	if err1 != nil {
		lg.WithError(err1).Error("Prov svc images upload failed")
		return err1
	}
	if resp.StatusCode != http.StatusOK {
		lg.WithFields(logrus.Fields{
			"prov_resp_status": resp.StatusCode,
		}).Error("Prov svc images upload  failed")
		return errors.New("Unable to upload images")
	}
	return nil
}

// PostTransferReportDCM - upload a DCM report
func (s *V2TransfersApiService) PostTransferReportDCM(
	ctx context.Context,
	transferId string,
	sessionId string,
	ip string,
	userAgent string,
	metadata coreapi.UploadFileMetadata,
	fileHeader []*multipart.FileHeader,
) error {
	authToken := uploadsessions.GetV2SessionToken(
		ctx,
		s.sqldb,
		uploadsessions.UploadSession{
			SessionId:  sessionId,
			TransferId: transferId,
			ClientId:   ip + userAgent,
		},
	)
	if authToken == "" {
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	file, err := fileHeader[0].Open()
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't open report file")
		return err
	}

	//send http POST request to providers' service  /v1/transfers/{transfer_id}/{study_id}/reportdcm
	req, err := http.NewRequest(
		"POST",
		"https://"+s.provUser.URL+"/v1/transfers/"+transferId+"/"+metadata.InstanceNumber+"/reportdcm",
		file,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("couldn't post report")
		return err
	}

	req.Header.Set("X-Auth-Token", authToken)
	resp, err1 := s.doRequestWithCtx(ctx, s.provUser.Client, req)
	if err1 != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"transfer_id": transferId,
		}).WithError(err1).Error("Prov svc reportdcm upload failed")
		return err1
	}
	if resp.StatusCode != http.StatusOK {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"transfer_id":      transferId,
			"prov_resp_status": resp.StatusCode,
		}).Error("Prov svc reportdcm upload  failed")
		return errors.New("Unable to upload dcm report.")
	}

	return nil
}

// DeleteTransfer - roll back a self-upload transfer
func (s *V2TransfersApiService) DeleteTransfer(
	ctx context.Context,
	transferId string,
	sessionId string,
	ip string,
	userAgent string,
) error {
	return s.rollbackSelfUpload(
		ctx,
		uploadsessions.UploadSession{
			SessionId:  sessionId,
			TransferId: transferId,
			ClientId:   ip + userAgent,
		},
	)
}

func (s *V2TransfersApiService) rollbackSelfUpload(
	ctx context.Context,
	session uploadsessions.UploadSession,
) error {
	//this ensures this action can only be performed on un-finalized transfers, since finalize deletes the upload session
	authToken := uploadsessions.GetV2SessionToken(ctx, s.sqldb, session)
	if authToken == "" {
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	//send http DELETE request to providers' service
	req, err := http.NewRequest(
		"DELETE",
		"https://"+s.provUser.URL+"/v2/transfers/"+session.TransferId,
		nil,
	)
	if err != nil {
		logutils.CtxLogger(ctx).WithError(err).Error("couldn't delete transfer")
		return err
	}
	req.Header.Set("X-Auth-Token", authToken)
	resp, err1 := s.doRequestWithCtx(ctx, s.provUser.Client, req)
	if err1 != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"transfer_id": session.TransferId,
		}).WithError(err1).Error("Prov svc delete failed")
		return err1
	}
	if resp.StatusCode != http.StatusOK {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"transfer_id":      session.TransferId,
			"prov_resp_status": resp.StatusCode,
		}).Error("Prov svc delete failed")
		return errors.New("Rollback failed")
	}

	err = uploadsessions.DeleteSession(ctx, s.sqldb, session.SessionId)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"session_id": session.SessionId,
		}).Error("Prov svc delete session failed")
		return err
	}
	return nil
}
