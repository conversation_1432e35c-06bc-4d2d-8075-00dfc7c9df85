package download

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
)

const MOCK_DOWNLOAD_BYTES string = "I believe I am DIC<PERSON>, therefore I am DICOM"

// This mock method emulates the original download.Prepare function for downloading and processing share files
// but instead of performing an actual Azure download and file conversion
// we simply write a dummy file to the expected output path
//
// application/zip:           vault/tmp/prep/IMAGES_userName.zip
// application/octect-stream: vault/tmp/prep/ISO_userName.iso
func MockDownloadPrepare(
	ctx context.Context,
	path string,
	objectIds []string,
	hasExam bool,
	contentType string,
	dlname string,
	containerClient azureUtils.ContainerClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
	ampliClient interfaces.AmplitudeEventClient,
	userName string,
	includeViwer bool,
	isBasicPlanAccount bool,
	acctId string,
) error {
	fileExtension := "iso"
	filePrefix := "ISO_"
	if contentType == "application/zip" {
		fileExtension = "zip"
		filePrefix = "IMAGES_"
	}

	fileDirectory := filepath.Join("vault/tmp/prep", filepath.Clean(dlname))
	_ = os.MkdirAll(fileDirectory, 0o750)

	fileName := filepath.Clean(filepath.Join(
		fileDirectory,
		filepath.Clean(fmt.Sprintf("%s%s.%s", filePrefix, userName, fileExtension)),
	))
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}

	_, err = file.Write([]byte(MOCK_DOWNLOAD_BYTES))
	if err != nil {
		return err
	}

	err = file.Close()
	if err != nil {
		return err
	}
	return nil
}
