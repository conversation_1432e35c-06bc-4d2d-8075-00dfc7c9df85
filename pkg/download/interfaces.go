package download

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
)

type DownloadPrepare func(
	ctx context.Context,
	path string,
	objectIds []string,
	hasExam bool,
	contentType string,
	dlname string,
	containerClient azureUtils.ContainerClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
	ampliClient interfaces.AmplitudeEventClient,
	userName string,
	includeViwer bool,
	isBasicPlanAccount bool,
	acctId string,
) error
