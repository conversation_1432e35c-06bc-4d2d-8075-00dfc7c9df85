package download

import (
	"bytes"
	"context"
	"errors"
	"os"
	"os/exec"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/amplitude/experiment-go-server/pkg/experiment"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/util/amplitude_util"
	"gitlab.com/pockethealth/coreapi/pkg/util/file"
	"gitlab.com/pockethealth/coreapi/pkg/util/iso"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func Prepare(
	ctx context.Context,
	path string,
	objectIds []string,
	hasExam bool,
	contentType string,
	dlname string,
	containerClient azureUtils.ContainerClient,
	experimentsClient interfaces.AmplitudeExperimentClient,
	ampliClient interfaces.AmplitudeEventClient,
	userName string,
	includeViwer bool,
	isBasicPlanAccount bool,
	acctId string,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	err := os.RemoveAll(path)
	if err != nil {
		lg.WithError(err).Error("could not clear download path")
		return errors.New("concurrentDownloads")
	}
	if _, err := os.Stat(path + "/DICOMS"); err != nil {
		mkdirErr := os.MkdirAll(path+"/DICOMS", 0o700)
		if mkdirErr != nil {
			return mkdirErr
		}
	} else {
		lg.WithError(err).Error("could not stat download path")
		return errors.New("concurrentDownloads")
	}
	var count uint64 = 0
	var errorFlag uint32 = 0
	connectionCount := 10
	connPool := make(chan bool, connectionCount)
	var swg sync.WaitGroup
	start := time.Now()
	var totalBytes int64
	var concurrentCounter int = 0
	var maxConcurrent int = 0
	for _, objectId := range objectIds {
		if atomic.LoadUint32(&errorFlag) > 0 {
			break
		}
		swg.Add(1)
		connPool <- true
		concurrentCounter++
		if concurrentCounter > maxConcurrent {
			maxConcurrent = concurrentCounter
		}
		go func(objectId string) {
			lgr := lg.WithField("object_id", objectId)
			pCount := atomic.AddUint64(&count, 1)

			downloadPath := path + "/DICOMS/IM" + strconv.FormatUint(pCount, 10)
			// copy the file
			// do not read into memory - shares can be extremely large
			numBytes, err := file.DownloadFromAzureToDisk(
				ctx,
				containerClient.Objects,
				objectId,
				downloadPath,
			)
			if err != nil {
				_ = atomic.AddUint32(&errorFlag, 1)
				elapsed := time.Since(start)
				lgr.WithFields(logrus.Fields{
					"sec_to_error": elapsed.Seconds(),
				}).WithError(err).Error("failed to download from azure")
				swg.Done()
				<-connPool
				return
			}

			atomic.AddInt64(&totalBytes, numBytes)
			swg.Done()
			concurrentCounter--
			<-connPool
		}(objectId)
	}
	swg.Wait()
	if errorFlag > 0 {
		removeErr := os.RemoveAll(path)
		if removeErr != nil {
			lg.WithError(removeErr).Error("remove all: ", path)
		}
		return errors.New("Download failed")
	}
	elapsed := time.Since(start)

	lg.WithFields(logrus.Fields{
		"num_objs":        len(objectIds),
		"kb":              totalBytes / 1000,
		"sec_to_download": elapsed.Seconds(),
		"has_exam":        hasExam,
		"content_type":    contentType,
		"max_concurrent":  maxConcurrent,
	}).Info("download stats")

	dirName := "IMAGES"
	cmd := exec.Command("mkdir", "-p", dirName)
	cmd.Dir = path
	if err := cmd.Run(); err != nil {
		lg.WithError(err).Error("download zip failed")
		removeErr := os.RemoveAll(path)
		if removeErr != nil {
			lg.WithError(removeErr).Error("remove all: ", path)
		}
		return err
	}

	cmd = exec.Command("mv", "DICOMS", "IMAGES/")
	cmd.Dir = path

	if err := cmd.Run(); err != nil {
		lg.WithError(err).Error("download mv DICOMS failed")
		removeErr := os.RemoveAll(path)
		if removeErr != nil {
			lg.WithError(removeErr).Error("remove all: ", path)
		}
		return err
	}

	if hasExam {
		cmd := exec.Command(
			"dcmmkdir",
			"-Nxc",
			"-Pfl",
			"+I",
			"+r",
			"+m",
			"+id",
			path+"/",
			"+D",
			path+"/DICOMDIR",
			"+F",
			"POCKETHEALTH",
		) // #nosec G204 hope this is ok :/
		var stderr bytes.Buffer
		cmd.Stderr = &stderr

		err := cmd.Run()
		if err != nil {
			lg.WithError(err).
				Error("download dcmmkdir failed: " + stderr.String())
			removeErr := os.RemoveAll(path)
			if removeErr != nil {
				lg.WithError(removeErr).Error("remove all: ", path)
			}
			return err
		}
	}

	if contentType == "application/octet-stream" {
		name := path + "/" + dlname + ".iso"
		if userName != "" {
			name = path + "/ISO_" + userName + ".iso"
		} else {
			lg.Info("no username for share download")
		}

		var err error
		// Patient Download, include the viewer to the package
		if includeViwer && !isBasicPlanAccount {
			err = iso.MakeIso(name, path+"/", "assets/viewer/general")
		} else if includeViwer && isBasicPlanAccount {

			hideInstructions := shouldRemoveViewerInstructions(ctx, experimentsClient, ampliClient, acctId)

			if hideInstructions {
				err = iso.MakeIso(name, path+"/", "assets/viewer/basic_no_instructions")
			} else {
				err = iso.MakeIso(name, path+"/", "assets/viewer/basic")
			}

		} else {
			err = iso.MakeIso(name, path+"/", "")
		}
		if err != nil {
			lg.WithError(err).Error("download iso error")
			// os.RemoveAll(path)
			return err
		}

	} else if contentType == "application/zip" {
		cmd = exec.Command("zip", "-r", "-0", "IMAGES_"+userName+".zip", "./") // #nosec G204 hope this is ok :/
		cmd.Dir = path
		output, err := cmd.CombinedOutput()
		if err != nil {
			lg.WithError(err).Error("download zip error: " + string(output))
			removeErr := os.RemoveAll(path)
			if removeErr != nil {
				logutils.DebugCtxLogger(ctx).WithError(removeErr).Error("remove all: ", path)
			}
			return err
		}

		// Patient Download, include the viewer to the package
		if includeViwer && !isBasicPlanAccount {
			lg.Info("shares download for non-basic account initiated")
			cmd = exec.Command("zip", "-ur", "-j", path+"/"+"IMAGES_"+userName+".zip", "assets/viewer/general") // #nosec G204 hope this is ok :/
			output, err = cmd.CombinedOutput()
			if err != nil {
				if err.Error() == "exit status 12" {
					/* Empty directory*/
					return nil
				}
				lg.WithError(err).Error("download viewer zip error: " + string(output))
				removeErr := os.RemoveAll(path)
				if removeErr != nil {
					lg.WithError(removeErr).Error("remove all: ", path)
				}
				return err
			}
		} else if includeViwer && isBasicPlanAccount {
			lg.Info("shares download for basic account initiated")

			hideInstructions := shouldRemoveViewerInstructions(ctx, experimentsClient, ampliClient, acctId)

			if hideInstructions {
				cmd = exec.Command("zip", "-ur", "-j", path+"/"+"IMAGES_"+userName+".zip", "assets/viewer/basic_no_instructions") // #nosec G204 hope this is ok :/
			} else {
				cmd = exec.Command("zip", "-ur", "-j", path+"/"+"IMAGES_"+userName+".zip", "assets/viewer/basic") // #nosec G204 hope this is ok :/
			}

			output, err = cmd.CombinedOutput()
			if err != nil {
				if err.Error() == "exit status 12" {
					/* Empty directory*/
					return nil
				}
				lg.WithError(err).Error("download viewer zip for basic account error: " + string(output))
				removeErr := os.RemoveAll(path)
				if removeErr != nil {
					lg.WithError(removeErr).Error("remove all: ", path)
				}
				return err
			}
		}
	}
	return nil
}

func shouldRemoveViewerInstructions(
	ctx context.Context,
	experimentsClient interfaces.AmplitudeExperimentClient,
	ampliClient interfaces.AmplitudeEventClient,
	acctId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx)
	experiments, err := experimentsClient.Fetch(
		&experiment.User{UserId: acctId},
	)
	if err != nil {
		lg.WithError(err).
			Infof("unable to fetch experiment for accountId: %s", acctId)
		return false
	}

	if variant, ok := experiments[amplitude_util.REMOVE_DOWNLOAD_ENHANCEMENTS]; ok {
		amplitude_util.ExposeExperimentVariant(
			ctx,
			ampliClient,
			acctId,
			"",
			amplitude_util.REMOVE_DOWNLOAD_ENHANCEMENTS,
			variant.Value,
		)
		if variant.Value == "treatment" {
			return true
		} else {
			return false
		}
	}
	return false
}
