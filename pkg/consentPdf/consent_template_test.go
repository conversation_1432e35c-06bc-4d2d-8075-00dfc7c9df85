package consentPdf

import (
	"context"
	"fmt"
	"io"
	"os"
	"regexp"
	"strings"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	"golang.org/x/text/language"
	languageTag "golang.org/x/text/language"
	"gopkg.in/osteele/liquid.v1"
)

/*================================================================================
README
==================================================================================
If you are updating the consent PDF templates (tmpl/locale/<lang>/consentform.html), you will by default break all tests
as the expected output will be different.

Here's what you should do:
1. Run the test
2. Check and visually validate the HTML files in assets/pdfTemplates/testOutput
3. If all is correct (formatting, template parameters populated, translations),
   copy the HTML files in testOutput to assets/pdfTemplates/example
=================================================================================*/

var examplePdfTemplatePath string = fmt.Sprintf("%s/example", PdfTemplatePath)
var testOutputPdfTemplatePath string = fmt.Sprintf("%s/testOutput", PdfTemplatePath)
var customConsentTextPath string = "customConsentText"
var defaultConsentTextPath string = "defaultConsentText"

var testOutputLineSeparator string = fmt.Sprintf("\n%s\n", strings.Repeat("=", 100))

// Base metadata
var providerName string = "Uncle Hubey's Healthcare"
var requestId string = "90210"
var date string = "31/12/2023"
var consentText string = "You are hereby consenting to PocketHealth."
var base64 string = "data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="

// Patient info
var fullName string = "James Shen"
var healthId string = "*********"
var ohipVc string = "AB"
var email string = "<EMAIL>"
var phoneNumber string = "**********"
var dateOfBirth string = "1900/01/01"

// Delegate info
var delegateFullName string = "Hames Then"
var delegateRelation string = "Husband's girlfriend's mom's friend"
var delegatePhoneNumber string = "**********"
var delegateAddress string = "1 PocketHealth Way"

var testcases []struct {
	language string
} = []struct {
	language string
}{
	{
		language: "en",
	},
	{
		language: "es-US",
	},
	{
		language: "fr-CA",
	},
}

func sanitizeHtml(html string) string {
	sanitizedHtml := ""
	lines := strings.Split(html, "\n")
	for _, line := range lines {
		sanitizedHtml += strings.TrimSpace(line)
	}
	return sanitizedHtml
}

func getTemplateForLanguage(fileName string, tag language.Tag) string {

	defaultTemplate := "../../tmpl/locale/en/" + fileName
	path := "../../tmpl/locale/" + tag.String() + "/" + fileName

	if _, err := os.Stat(path); err != nil {
		return defaultTemplate
	}
	return path
}

func getBase64PocketHealthLogo() string {
	file, _ := os.Open("../../assets/pocketHealthLogo.txt")
	// We only read from this file, so we can ignore close errors.
	/* #nosec G307 */
	defer file.Close()

	bytes, _ := io.ReadAll(file)
	return string(bytes)
}

func generateAndValidateTemplate(
	t *testing.T,
	templateFile string,
	parameters liquid.Bindings,
	language string,
) {
	// Populate consent pdf template
	tag, err := languageTag.Parse(language)
	if err != nil {
		t.Fatalf("failed to parse language tag: %s", err.Error())
	}
	templateFilePath := getTemplateForLanguage("consentform.html", tag)
	populatedTemplate, err := PopulateLiquidTemplate(templateFilePath, parameters)
	if err != nil {
		t.Fatalf("failed to populate html template: %s", err.Error())
	}

	var outputTemplateFileDirectory string
	var expectedTemplateFileDirectory string

	// Determine the expected output directory/ test output directory based on whether or not we are using default consent text
	if customConsentText, exists := parameters["ConsentText"]; exists && customConsentText != "" {
		outputTemplateFileDirectory = fmt.Sprintf("%s/%s", testOutputPdfTemplatePath, customConsentTextPath)
		expectedTemplateFileDirectory = fmt.Sprintf("%s/%s", examplePdfTemplatePath, customConsentTextPath)
	} else {
		outputTemplateFileDirectory = fmt.Sprintf("%s/%s", testOutputPdfTemplatePath, defaultConsentTextPath)
		expectedTemplateFileDirectory = fmt.Sprintf("%s/%s", examplePdfTemplatePath, defaultConsentTextPath)

	}
	outputTemplateFileDirectory = fmt.Sprintf("../../%s/%s", outputTemplateFileDirectory, language)
	outputTemplateFilePath := fmt.Sprintf("%s/%s", outputTemplateFileDirectory, templateFile)

	expectedTemplateFilePath := fmt.Sprintf("../../%s/%s/%s", expectedTemplateFileDirectory, language, templateFile)
	expectedTemplate, err := os.ReadFile(expectedTemplateFilePath)
	if err != nil {
		t.Fatalf("failed to find expected template: %s", err.Error())
	}

	// Write the output to assets/pdfTemplates/testOutput for manual inspection
	if err := os.MkdirAll(outputTemplateFileDirectory, os.ModePerm); err != nil {
		fmt.Println("Error creating directory:", err)
		return
	}
	if _, err := os.Stat(outputTemplateFilePath); err == nil {
		err := os.Remove(outputTemplateFilePath)
		if err != nil {
			t.Fatalf("failed to delete test file: %s", err.Error())
		}
	}
	err = os.WriteFile(outputTemplateFilePath, populatedTemplate, 0644)
	if err != nil {
		t.Fatalf("failed to write test output file: %s", err.Error())
	}

	// Check if the HTML string contains any Liquid template tags
	liquidRegex := regexp.MustCompile(`{{.*?}}`)
	if liquidRegex.MatchString(string(populatedTemplate)) {
		t.Errorf("populated template has lingering Liquid tags: %s", string(populatedTemplate))
	}

	// Ensure the populated HTML matches the expected HTML
	if sanitizeHtml(string(populatedTemplate)) != sanitizeHtml(string(expectedTemplate)) {
		t.Errorf(
			"expected html: %s%sgot html: %s%sTemplate file: %s%sCheck the expected template file at: %s",
			expectedTemplate,
			testOutputLineSeparator,
			populatedTemplate,
			testOutputLineSeparator,
			templateFilePath,
			testOutputLineSeparator,
			expectedTemplateFilePath,
		)
	}
}

func TestGetConsentPdfParametersEscape(t *testing.T) {
	// put unescaped html characters in every field that needs escaping, then verify
	// the final result does not have any of those characters

	got := GetConsentPdfParameters(
		context.TODO(),
		"date",
		"consentText",
		"minorConsentText",
		"requestId",
		models.Request{
			Ohip:  "<ohip>",
			Mrn:   "<mrn>",
			Bcphn: "<bcphn>",
			Ipn:   "<ipn>",
			Ssn:   "<ssn>",
			AltId: "<altid>",
			Email: "<email>",
			Tel:   "<tel>",
			Contents: models.StudyRequest{
				Delegate: &models.StudyRequestDelegate{
					Relation: "<delegateRelation>",
					Phone:    "<delegatePhone>",
					Address:  "<delegateAddress>",
				},
			},
		},
		"<fullName>",
		"<delegateFullName>",
		"providerName",
		"providerLogoBase64",
		"<signatureBase64>",
		"<minorSignatureBase64>",
		"pocketHealthLogoBase64",
	)

	// the return value is just a map, print out all its values and ensure no angle brackets exist
	gotStr := fmt.Sprintf("%+v", got)
	if strings.Contains(gotStr, "<") || strings.Contains(gotStr, ">") {
		t.Errorf("unescaped html detected in map %+v", gotStr)
	}

}

func TestConsentTemplateCustomConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("consent PDF template with custom consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				consentText,
				"",
				requestId,
				models.Request{
					Dob:    dateOfBirth,
					Ohip:   healthId,
					Ohipvc: ohipVc,
					Email:  email,
					Tel:    phoneNumber,
				},
				fullName,
				"",
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, ConsentPDF, parameters, c.language)
		})
	}
}

func TestConsentTemplateDefaultConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("consent PDF template with default consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				"",
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					Ssn:   healthId,
					Email: email,
					Tel:   phoneNumber,
				},
				fullName,
				"",
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, ConsentPDF, parameters, c.language)
		})
	}
}

func TestDelegateConsentTemplateCustomConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("Delegate consent PDF template with custom consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				consentText,
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					Ipn:   healthId,
					Email: email,
					Tel:   phoneNumber,
					Contents: models.StudyRequest{
						Delegate: &models.StudyRequestDelegate{
							Relation: delegateRelation,
							Phone:    delegatePhoneNumber,
							Address:  delegateAddress,
						},
					},
				},
				fullName,
				delegateFullName,
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, DelegateConsentPDF, parameters, c.language)
		})
	}
}

func TestDelegateConsentTemplateDefaultConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("Delegate consent PDF template with default consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				"",
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					AltId: healthId,
					Email: email,
					Tel:   phoneNumber,
					Contents: models.StudyRequest{
						Delegate: &models.StudyRequestDelegate{
							Relation: delegateRelation,
							Phone:    delegatePhoneNumber,
							Address:  delegateAddress,
						},
					},
				},
				fullName,
				delegateFullName,
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, DelegateConsentPDF, parameters, c.language)
		})
	}
}

func TestDelegateConsentTemplateMinorSignature(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("Delegate consent PDF template with minor signature:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				"",
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					AltId: healthId,
					Email: email,
					Tel:   phoneNumber,
					Contents: models.StudyRequest{
						Delegate: &models.StudyRequestDelegate{
							Relation: delegateRelation,
							Phone:    delegatePhoneNumber,
							Address:  delegateAddress,
						},
					},
				},
				fullName,
				delegateFullName,
				providerName,
				base64,
				base64,
				base64,
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, MinorConsentPDF, parameters, c.language)
		})
	}
}

func TestUphConsentTemplateCustomConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("UPH consent PDF template with custom consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				consentText,
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					Email: email,
					Tel:   phoneNumber,
					Contents: models.StudyRequest{
						RecentExamDetails: &models.StudyRequestRecentExamDetails{
							ExamMetadatas: []models.ExamMetadata{
								{Type: "X-Ray", Year: "1990", Month: "01"},
								{Type: "CT", Year: "2010", Month: "05"},
								{Type: "Ultrasound", Year: "2020", Month: "12"},
							},
						},
					},
				},
				fullName,
				"",
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, UPHConsentPDF, parameters, c.language)
		})
	}
}

func TestUphConsentTemplateDefaultConsentText(t *testing.T) {
	for _, c := range testcases {
		t.Run(fmt.Sprintf("UPH consent PDF template with default consent text:%s", c.language), func(t *testing.T) {
			parameters := GetConsentPdfParameters(
				context.TODO(),
				date,
				"",
				"",
				requestId,
				models.Request{
					Dob:   dateOfBirth,
					Email: email,
					Tel:   phoneNumber,
					Contents: models.StudyRequest{
						RecentExamDetails: &models.StudyRequestRecentExamDetails{
							ExamMetadatas: []models.ExamMetadata{
								{Type: "X-Ray", Year: "1990", Month: "01"},
								{Type: "CT", Year: "2010", Month: "05"},
								{Type: "Ultrasound", Year: "2020", Month: "12"},
							},
						},
					},
				},
				fullName,
				"",
				providerName,
				base64,
				base64,
				"",
				getBase64PocketHealthLogo(),
			)

			generateAndValidateTemplate(t, UPHConsentPDF, parameters, c.language)
		})
	}
}
