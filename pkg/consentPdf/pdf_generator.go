package consentPdf

import (
	"bytes"
	"os"

	wkhtmltopdf "github.com/Sebastiaan<PERSON><PERSON><PERSON>/go-wkhtmltopdf"
	liquid "gopkg.in/osteele/liquid.v1"
)

const PdfTemplatePath string = "assets/pdfTemplates"

const (
	ConsentPDF         string = "consent_form.html"
	DelegateConsentPDF string = "consent_form_delegate.html"
	MinorConsentPDF    string = "consent_form_delegate_minor.html"
	UPHConsentPDF      string = "consent_form_uph.html"
)

// PopulateTemplate loads an HTML template from file, applies parameters, and returns the populated template in bytes.
func PopulateLiquidTemplate(filename string, data liquid.Bindings) ([]byte, error) {
	// Read the HTML template file
	templateFile, err := os.ReadFile(filename) // #nosec G304
	if err != nil {
		return nil, err
	}

	// Create a new template and parse the file content
	engine := liquid.NewEngine()

	populatedTemplate, sourceError := engine.ParseAndRender(templateFile, data)
	if sourceError != nil {
		return populatedTemplate, sourceError.Cause()

	}
	return populatedTemplate, nil
}

// HtmlToPdf property customZoom sets a custom zoom with the value is anything other than 0
func HtmlToPdf(html []byte, customZoom float64) ([]byte, error) {
	// Create new PDF generator
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return []byte{}, err
	}

	// set page size to Letter
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeLetter)

	// Reduce page margins
	pdfg.MarginTop.Set(10)    // 10mm
	pdfg.MarginBottom.Set(10) // 10mm
	pdfg.MarginLeft.Set(10)   // 10mm
	pdfg.MarginRight.Set(10)  // 10mm

	page := wkhtmltopdf.NewPageReader(bytes.NewReader(html))
	page.DisableJavascript.Set(true)
	page.EnableLocalFileAccess.Set(true)

	if customZoom != 0 {
		page.Zoom.Set(customZoom)
	}

	// Add to document
	pdfg.AddPage(page)

	// Create PDF document in internal buffer
	err = pdfg.Create()
	if err != nil {
		return []byte{}, err
	}

	return pdfg.Buffer().Bytes(), nil
}
