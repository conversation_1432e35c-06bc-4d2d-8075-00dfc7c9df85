package consentPdf

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/net/html"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	liquid "gopkg.in/osteele/liquid.v1"
)

func GetConsentPdfParameters(
	ctx context.Context,
	date string,
	consentText string,
	minorConsentText string,
	requestId string,
	request models.Request,
	fullName string,
	delegateFullName string,
	providerName string,
	providerLogoBase64 string,
	signatureBase64 string,
	minorSignatureBase64 string,
	pocketHealthLogoBase64 string,
) liquid.Bindings {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"request_id": requestId,
	})

	examInformation := []liquid.Bindings{}
	recentExamDetails := request.Contents.RecentExamDetails
	if recentExamDetails != nil && len(recentExamDetails.ExamMetadatas) > 0 {
		for _, examMetadata := range recentExamDetails.ExamMetadatas {
			// Parse the month into the 3 character abbreviated format (ex. 'Feb')
			parsedTime, err := time.Parse("01", examMetadata.Month)
			monthStr := "Jan"
			successParsing := false
			if err != nil {
				lg.WithError(err).Info("failed to parse exam month string")
			} else {
				monthStr = parsedTime.Format("Jan")
				successParsing = true
			}
			examInformation = append(examInformation, liquid.Bindings{
				"ExamType":  html.EscapeString(examMetadata.Type),
				"ExamYear":  html.EscapeString(examMetadata.Year),
				"ExamMonth": html.EscapeString(monthStr),
				"HasMonth":  successParsing,
			})
		}
	}
	ohip := request.Ohip
	if request.Ohipvc != "" {
		ohip = fmt.Sprintf("%s - %s", request.Ohip, request.Ohipvc)
	}
	hasHealthId := (ohip != "" ||
		request.Mrn != "" ||
		request.Bcphn != "" ||
		request.Ipn != "" ||
		request.Ssn != "" ||
		request.AltId != "")

	formattedDob := formatPdfDob(lg, request.Dob)
	parameters := liquid.Bindings{
		"ProviderName": html.EscapeString(providerName),
		"ProviderLogo": providerLogoBase64, // not from the form
		"RequestId":    html.EscapeString(requestId),
		"FullName":     html.EscapeString(fullName),
		"DateOfBirth":  html.EscapeString(formattedDob),
		"HealthId": liquid.Bindings{
			"OHIP":                html.EscapeString(ohip),
			"MRN":                 html.EscapeString(request.Mrn),
			"BCPHN":               html.EscapeString(request.Bcphn),
			"IPN":                 html.EscapeString(request.Ipn),
			"SSN":                 html.EscapeString(request.Ssn),
			"AlternativeHealthId": html.EscapeString(request.AltId),
		},
		"HasHealthId":      hasHealthId, // not from the form
		"Email":            html.EscapeString(request.Email),
		"PhoneNumber":      html.EscapeString(request.Tel),
		"ExamInformation":  examInformation,
		"DelegateFullName": html.EscapeString(delegateFullName),
		"ConsentText":      consentText,      // not from the form
		"MinorConsentText": minorConsentText, // not from the form
		"Signature":        html.EscapeString(signatureBase64),
		"MinorSignature":   html.EscapeString(minorSignatureBase64),
		"Date":             date,                   //not from the form
		"PocketHealthLogo": pocketHealthLogoBase64, // not from the form
	}
	if request.Contents.Delegate != nil {
		parameters["DelegateRelationToPatient"] = html.EscapeString(request.Contents.Delegate.Relation)
		parameters["DelegatePhoneNumber"] = html.EscapeString(request.Contents.Delegate.Phone)
		parameters["DelegateAddress"] = html.EscapeString(request.Contents.Delegate.Address)
	}

	return parameters
}

func formatPdfDob(lg *logrus.Entry, dob string) string {
	mmddyyyyFormat := "01/02/2006"
	yyyymmddFormat := "2006/01/02"

	parsedDob, err := time.Parse(mmddyyyyFormat, dob)
	if err != nil {
		lg.WithError(err).Error("formatPdfDob ran into an error parsing the date of birth, using original dob format")
		return dob
	}

	formattedDob := parsedDob.Format(yyyymmddFormat)

	return formattedDob
}
