package short_urls

import (
	"database/sql"
	"net/http"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type PublicShortUrlsController struct {
	service coreapi.ShortUrlsServicer
}

func NewPublicShortUrlsApiController(
	s coreapi.ShortUrlsServicer,
) coreapi.PublicShortUrlsApiRouter {
	return &PublicShortUrlsController{service: s}
}

func (c *PublicShortUrlsController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetOriginalUrl",
			Method:      http.MethodGet,
			Pattern:     "/{slug}",
			HandlerFunc: c.GetOriginalUrl,
		},
	}
}

func (c *PublicShortUrlsController) GetPathPrefix() string {
	return "/v1/short-urls"
}

func (c *PublicShortUrlsController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *PublicShortUrlsController) GetOriginalUrl(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	params := mux.Vars(r)
	slug := params["slug"]

	if slug == "" {
		httperror.ErrorWithLog(w, r, "slug cannot be empty", http.StatusBadRequest)
		return
	}

	originalURL, err := c.service.GetOriginalUrl(ctx, slug)
	if err != nil {
		if err == sql.ErrNoRows {
			httperror.ErrorWithLog(w, r, "no url exist for the given slug", http.StatusNotFound)
			return
		}
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(
		r.Context(),
		coreapi.ShortUrlResponse{Slug: slug, OriginalURL: originalURL},
		&status,
		w,
	)
}
