package short_urls

import (
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func TestGetOriginalUrls(t *testing.T) {

	t.Run("should return StatusNotFound when no slug is present", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		controller := NewPublicShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		req, err := http.NewRequest(
			http.MethodGet,
			"/v1/short-urls/",
			nil,
		)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusNotFound, rr.Code)
	})

	t.Run("should return StatusNotFound for non-existent slug", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		service.EXPECT().GetOriginalUrl(mock.Anything, mock.Anything).
			Return("", sql.ErrNoRows)
		controller := NewPublicShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		req, err := http.NewRequest(
			http.MethodGet,
			"/v1/short-urls/abcd",
			nil,
		)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusNotFound, rr.Code)
	})

	t.Run("should return StatusInternalServerError for all other errors", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		service.EXPECT().GetOriginalUrl(mock.Anything, mock.Anything).
			Return("", errors.New("some random error"))
		controller := NewPublicShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		req, err := http.NewRequest(
			http.MethodGet,
			"/v1/short-urls/abcd",
			nil,
		)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusInternalServerError, rr.Code)
	})

	t.Run("should return StatusOk for valid slug", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		service.EXPECT().GetOriginalUrl(mock.Anything, mock.Anything).
			Return("url", nil)
		controller := NewPublicShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		req, err := http.NewRequest(
			http.MethodGet,
			"/v1/short-urls/abcd",
			nil,
		)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})
}
