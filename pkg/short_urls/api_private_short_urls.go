package short_urls

import (
	"encoding/json"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

type PrivateShortUrlsController struct {
	service coreapi.ShortUrlsServicer
}

// This controller uses X-PH-Signature header authentication only.
// This route is expected to be called from our backend services only and not frontend
func NewPrivateShortUrlsApiController(
	s coreapi.ShortUrlsServicer,
) coreapi.PrivateShortUrlsApiRouter {
	return &PrivateShortUrlsController{service: s}
}

func (c *PrivateShortUrlsController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostShortUrl",
			Method:      http.MethodPost,
			HandlerFunc: c.PostShortUrl,
		},
	}
}

func (c *PrivateShortUrlsController) GetPathPrefix() string {
	return "/v1/short-urls"
}

func (c *PrivateShortUrlsController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{auth.ValidatePHSignature}
}

func (c *PrivateShortUrlsController) PostShortUrl(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var body coreapi.ShortUrlGenerationRequest
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	url, err := coreapi.ValidateSecureURL(body.Url)

	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnprocessableEntity)
		return
	}

	slug, err := c.service.PostShortUrl(ctx, url)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	status := http.StatusOK
	coreapi.EncodeJSONResponse(
		r.Context(),
		coreapi.ShortUrlResponse{Slug: slug, OriginalURL: url},
		&status,
		w,
	)
}
