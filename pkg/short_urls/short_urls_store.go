package short_urls

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/sqids/sqids-go"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/phutils/v8/pkg/logutils"
)

func Insert(
	ctx context.Context,
	db *sql.DB,
	sq *sqids.Sqids,
	url string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		lg.WithError(err).Error("couldn't begin transaction")
		return "", err
	}
	row, err := tx.ExecContext(
		ctx,
		`INSERT INTO short_urls (original_url) VALUES (?)`,
		url,
	)
	if err != nil {
		lg.WithError(err).Error("couldn't insert the row in short_urls table")
		err2 := tx.Rollback()
		if err2 != nil {
			lg.WithError(err2).Error("couldn't rollback")
			return "", err2
		}
		return "", err
	}

	lastInsertID, err := row.LastInsertId()
	if err != nil {
		lg.WithError(err).Error("failed to get last insert ID")
		return "", nil
	}

	slug, err := sq.Encode(
		[]uint64{
			//nolint:gosec // G115: lastInsertID should always be in range for billions of rows.
			uint64(lastInsertID),
		},
	)
	if err != nil {
		lg.WithError(err).Error("failed to encode lastInsertID: ", lastInsertID)
		return "", nil
	}

	// append regionID to the slug so that FE can call correct region
	region := regions.GetRegionID()
	slug = fmt.Sprint(slug, region)

	_, err = tx.ExecContext(
		ctx,
		`UPDATE short_urls SET slug=? WHERE id=?`,
		slug,
		lastInsertID,
	)
	if err != nil {
		lg.WithError(err).Error("couldn't update slug in short_urls")
		err2 := tx.Rollback()
		if err2 != nil {
			lg.WithError(err2).Error("couldn't rollback")
			return "", err2
		}
		return "", err
	}

	err = tx.Commit()
	if err != nil {
		lg.WithError(err).Error("failed to commit transaction")
		return "", err
	}

	return slug, nil
}

func Get(
	ctx context.Context,
	db *sql.DB,
	slug string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)
	var originalURL string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT original_url from short_urls where slug=?",
		[]interface{}{&originalURL},
		slug,
	)
	if err != nil {
		lg.WithError(err).Error("failed to fetch original url from slug")
		return "", err
	}

	return originalURL, nil
}
