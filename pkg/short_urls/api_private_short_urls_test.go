package short_urls

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestPostShortUrl(t *testing.T) {
	privateKey := testutils.SetupXPhSignaturePrivKey(t)

	t.Run("should return StatusUnauthorized when missing PHSignature", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		controller := NewPrivateShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/short-urls",
			strings.NewReader(fmt.Sprintf(`{"url": %q}`, "https://www.example.com")),
		)
		require.NoError(t, err)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusUnauthorized, rr.Code)
	})

	t.Run("should return StatusBadRequest for request with empty url", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		controller := NewPrivateShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/short-urls",
			nil,
		)
		require.NoError(t, err)
		testutils.SignRequest(t, req, privateKey)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusBadRequest, rr.Code)
	})

	t.Run("should return StatusUnprocessableEntity for request with empty url", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		controller := NewPrivateShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/short-urls",
			strings.NewReader(fmt.Sprintf(`{"url": %q}`, "")),
		)
		require.NoError(t, err)
		testutils.SignRequest(t, req, privateKey)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusUnprocessableEntity, rr.Code)
	})

	t.Run("should return StatusOk for request with good url", func(t *testing.T) {
		service := mockcoreapi.NewMockShortUrlsServicer(t)
		service.EXPECT().PostShortUrl(mock.Anything, mock.Anything).
			Return("abcdef", nil)
		controller := NewPrivateShortUrlsApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest(
			http.MethodPost,
			"/v1/short-urls",
			strings.NewReader(fmt.Sprintf(`{"url": %q}`, "https://www.example.com")),
		)
		require.NoError(t, err)
		testutils.SignRequest(t, req, privateKey)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		require.Equal(t, http.StatusOK, rr.Code)
	})
}
