//go:build integration
// +build integration

package short_urls

import (
	"context"
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	phutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestPostShortUrls(t *testing.T) {
	db := testutils.SetupTestDB(t)

	service := NewShortUrlsApiService(db)

	t.Run(
		"should insert slug correctly for the provided url",
		func(t *testing.T) {
			ctx := context.Background()
			slug, err := service.PostShortUrl(ctx, "https://www.example.com")

			assert.NoError(t, err)
			assert.NotEqual(t, "", slug)

			t.Cleanup(func() {
				db.Exec("delete from short_urls where slug = ?", slug)
			})
		},
	)
}

func TestGetOriginalUrl(t *testing.T) {
	db := testutils.SetupTestDB(t)

	service := NewShortUrlsApiService(db)

	t.Run(
		"should return sql no row error for non-existent slug",
		func(t *testing.T) {
			ctx := context.Background()
			originalUrl, err := service.GetOriginalUrl(ctx, phutils.GenerateRandomString(t, 6))
			assert.Equal(t, "", originalUrl)
			assert.Equal(t, sql.ErrNoRows, err)

		},
	)

	t.Run(
		"should return correct original url for the given slug",
		func(t *testing.T) {
			ctx := context.Background()
			requestUrl := "https://www.example.com"
			slug, err := service.PostShortUrl(ctx, requestUrl)

			assert.NoError(t, err)
			assert.NotEqual(t, "", slug)

			responseUrl, err := service.GetOriginalUrl(ctx, slug)
			assert.Equal(t, requestUrl, responseUrl)
			assert.NoError(t, err)

			t.Cleanup(func() {
				db.Exec("delete from short_urls where slug = ?", slug)
			})

		},
	)
}
