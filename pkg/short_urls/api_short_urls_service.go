package short_urls

import (
	"context"
	"database/sql"

	"github.com/sqids/sqids-go"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/phutils/v8/pkg/logutils"
)

type ShortUrlsApiService struct {
	sqldb *sql.DB
}

// NewUsersApiService creates a default api service
func NewShortUrlsApiService(mysqlDB *sql.DB) coreapi.ShortUrlsServicer {
	return &ShortUrlsApiService{sqldb: mysqlDB}
}

// PostShortUrl - generates a slug for the given url
func (s *ShortUrlsApiService) PostShortUrl(ctx context.Context, url string) (string, error) {
	sq, err := sqids.New(sqids.Options{
		MinLength: 6,
	})
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("failed to initialize Sqids")
		return "", err
	}
	return Insert(ctx, s.sqldb, sq, url)
}

// GetOriginalUrl - gets the original url for the given slug
func (s *ShortUrlsApiService) GetOriginalUrl(ctx context.Context, slug string) (string, error) {
	return Get(ctx, s.sqldb, slug)
}
