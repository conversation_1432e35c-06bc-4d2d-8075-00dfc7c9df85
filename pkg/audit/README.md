# Audit Log Service

This package represent the first step in a transition to a general audit log solution. 

In the future, audit logging should be happening in a dedicated Audit Service. 
Other services would then forward messages to this service via service bus messages or via an auto-generated client.

**Please do NOT implement anything but tests that `SELECT`/read data from the audit log events in Core API.
Only Providers Service should be reading from the audit log events in production.**

For more information on long-term plans, see [the corresponding design document](https://www.notion.so/pockethealth/Audit-logging-1ae0d4c6794380b383dad3fb009581e2?pvs=4)


The current implementation is based on a short-term plan that satisfies legal requirements for Physician Hub audit logging within a tight deadline. For more information, see [short-term design doc](https://www.notion.so/pockethealth/Physician-Hub-Audit-Logging-1b20d4c67943800090aeee2bfdd85896?pvs=4)
