package audit

import (
	"context"
	"sync"
	"time"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/audit/internal"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/repository"
)

// Store is the interface to the audit data store.
type Store interface {
	CreatePhysicianPACSSearchEvent(
		context.Context,
		models.Event[*models.EventDataPhysicianPACSSearch],
	) error
	CreatePhysicianStudyRetrieveEvent(
		context.Context,
		models.Event[*models.EventDataPhysicianStudyRetrieve],
	) error
	CreatePhysicianStudyViewEvent(
		context.Context,
		models.Event[*models.EventDataPhysicianStudyView],
	) error
	CreatePhysicianReportViewEvent(
		context.Context,
		models.Event[*models.EventDataPhysicianReportView],
	) error
}

// Service is the interface to the audit data service.
type Service interface {
	CreatePhysicianPACSSearchEvent(
		ctx context.Context,
		data models.EventDataPhysicianPACSSearch,
	) error
	CreatePhysicianStudyRetrieveEvent(
		ctx context.Context,
		data models.EventDataPhysicianStudyRetrieve,
	) error
	CreatePhysicianStudyViewEvent(
		ctx context.Context,
		data models.EventDataPhysicianStudyView,
	) error
	CreatePhysicianReportViewEvent(
		ctx context.Context,
		data models.EventDataPhysicianReportView,
	) error
}

type service struct {
	store          Store
	clientID       ksuid.KSUID
	messageCounter uint32
	counterMutex   sync.Mutex
}

func NewService(store Store) *service {
	return &service{
		store:          store,
		clientID:       ksuid.New(),
		messageCounter: 0,
	}
}

func (s *service) CreatePhysicianPACSSearchEvent(
	ctx context.Context,
	data models.EventDataPhysicianPACSSearch,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"event_type":  data.Type(),
		"provider_id": data.ProviderID,
		"user_id":     data.UserID,
	})
	// create event
	event, err := newEvent(
		ctx,
		lg,
		s.clientID.String(),
		s.newMessageID(),
		&data,
	)
	if err != nil {
		lg.WithError(err).Error("audit event for physician PACS search is not valid")
		return err
	}
	// log event
	err = s.store.CreatePhysicianPACSSearchEvent(ctx, event)
	if err != nil {
		lg.WithError(err).Error("failed to write audit event for physician PACS search")
		return err
	}
	return nil
}

func (s *service) CreatePhysicianStudyRetrieveEvent(
	ctx context.Context,
	data models.EventDataPhysicianStudyRetrieve,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"event_type":  data.Type(),
		"provider_id": data.ProviderID,
		"user_id":     data.UserID,
	})
	// create event
	event, err := newEvent(
		ctx,
		lg,
		s.clientID.String(),
		s.newMessageID(),
		&data,
	)
	if err != nil {
		lg.WithError(err).Error("audit event for physician study retrieval is not valid")
		return err
	}
	// log event
	err = s.store.CreatePhysicianStudyRetrieveEvent(ctx, event)
	if err != nil {
		lg.WithError(err).Error("failed to write audit event for physician study retrieval")
		return err
	}
	return nil
}

func (s *service) CreatePhysicianStudyViewEvent(
	ctx context.Context,
	data models.EventDataPhysicianStudyView,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"event_type":  data.Type(),
		"provider_id": data.ProviderID,
		"user_id":     data.UserID,
	})
	// create event
	event, err := newEvent(
		ctx,
		lg,
		s.clientID.String(),
		s.newMessageID(),
		&data,
	)
	if err != nil {
		lg.WithError(err).Error("audit event for physician study view is not valid")
		return err
	}
	// log event
	err = s.store.CreatePhysicianStudyViewEvent(ctx, event)
	if err != nil {
		lg.WithError(err).Error("failed to write audit event for physician study view")
		return err
	}
	return nil
}

func (s *service) CreatePhysicianReportViewEvent(
	ctx context.Context,
	data models.EventDataPhysicianReportView,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"event_type":  data.Type(),
		"provider_id": data.ProviderID,
		"user_id":     data.UserID,
	})
	// create event
	event, err := newEvent(
		ctx,
		lg,
		s.clientID.String(),
		s.newMessageID(),
		&data,
	)
	if err != nil {
		lg.WithError(err).Error("audit event for physician report view is not valid")
		return err
	}
	// log event
	err = s.store.CreatePhysicianReportViewEvent(ctx, event)
	if err != nil {
		lg.WithError(err).Error("failed to write audit event for physician report view")
		return err
	}
	return nil
}

func (s *service) newMessageID() uint32 {
	s.counterMutex.Lock()
	defer s.counterMutex.Unlock()
	messageID := s.messageCounter
	s.messageCounter++
	return messageID
}

func newEvent[T models.EventData](
	ctx context.Context,
	lg *logrus.Entry,
	clientID string,
	messageID uint32,
	data T,
) (models.Event[T], error) {
	// format event data
	data.Format()

	// get correlation id from context
	correlationID, err := logutils.CorrelationID(ctx)
	if err != nil {
		lg.Warn("no correlation id available - logging without correlation id")
	}
	// get subject from context
	subject, err := auth.SubjectOrEmpty(ctx)
	if err != nil {
		lg.Warn("subject not available - logging without subject")
	}
	// get IP from context
	ip, err := auth.IPOrEmpty(ctx)
	if err != nil {
		lg.Warn("ip not available - logging without ip")
	}

	event := models.Event[T]{
		LogTimestamp:  time.Now().UTC(),
		Service:       models.ServiceProvider,
		ClientID:      clientID,
		MessageID:     messageID,
		Subject:       subject,
		CorrelationID: correlationID,
		SourceIP:      ip,
		EventType:     data.Type(),
		EventData:     data,
	}

	// validate event
	if err := models.ValidateEvent(event); err != nil {
		return models.Event[T]{}, err
	}
	return event, nil
}

func NewStore(repo repository.Repository) Store {
	return internal.NewStore(repo)
}
