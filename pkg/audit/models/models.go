package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
)

// EventData is the interface that any event data for audit events needs to fulfill
type EventData interface {
	Scan(
		value any,
	) error // Scan converts json data to a raw byte array for arbitrary JSON tag data.
	Type() EventType              // Type returns the event type for the given event data.
	Validate() error              // Validate returns an error if the event data is not valid.
	Value() (driver.Value, error) // Value converts event data into a format that can be stored as JSON in the database.
	Format()                      // Format changes values of event data to a consistent format.
}

// Event is a generic event that can be logged to event_log.
// Different event types requrie different data.
type Event[T EventData] struct {
	LogTimestamp  time.Time `db:"produced_at"`    // timestamp of event creation in microseconds
	Service       Service   `db:"service"`        // service that triggered log
	ClientID      string    `db:"client_id"`      // id of client triggering log
	MessageID     uint32    `db:"message_id"`     // auto-increment id of message comming from client
	Subject       string    `db:"subject"`        // authentication subject linked to request that triggered log
	CorrelationID string    `db:"correlation_id"` // correlation id of workflow that triggered event
	SourceIP      string    `db:"source_ip"`      // ip address of incoming request that triggered this workflow
	EventType     EventType `db:"event_type"`     // type of event
	EventData     T         `db:"event_data"`     // additional data of event
}

// Validate returns an error if the given event is not valid.
func ValidateEvent[T EventData](o Event[T]) error {
	if o.LogTimestamp.IsZero() {
		return errors.New("timestamp cannot be empty")
	} else if o.Service == "" {
		return errors.New("service cannot be empty")
	} else if o.ClientID == "" {
		return errors.New("client id cannot be empty")
	} else if o.EventType == "" {
		return errors.New("event type cannot be empty")
	}
	return o.EventData.Validate()
}

// EventDataPhysicianBase is the base data that should be included in event data of a physician event
type EventDataPhysicianBase struct {
	ProviderID int64    `json:"provider_id"` // ID of provider that owns the autorouted record
	UserID     string   `json:"user_id"`     // ID of user that record is routed to
	UserType   UserType `json:"user_type"`   // type of user that the event relates to
}

// Validate returns an error if the given event data is not valid.
func (o *EventDataPhysicianBase) Validate() error {
	if o.ProviderID == 0 {
		return errors.New("provider id cannot be empty")
	} else if o.UserID == "" {
		return errors.New("user id cannot be empty")
	}
	return nil
}

// EventDataPatientBase is the base data on a patient that should be included in event data of a physician event
// that relates to a specific patient or a specific record that is tied to a patient
type EventDataPatientBase struct {
	PatientFirstName string `json:"phi_patient_firstname"`  // first name of patient of autorouted record
	PatientLastName  string `json:"phi_patient_lastname"`   // last name of patient of autorouted record
	PatientBirthDate string `json:"phi_patient_birth_date"` // dob of patient of autorouted record
}

// Validate returns an error if the given event data is not valid.
func (o *EventDataPatientBase) Validate() error {
	// DICOM data in a PACS might be incomplete - patients could e.g. be missing their first name.
	// In those cases, we don't want to block access to their data.
	// Instead, we accept any patient data that isn't completely empty.
	if o.PatientFirstName == "" && o.PatientLastName == "" && o.PatientBirthDate == "" {
		return errors.New("need at least one of patient first name, last name or date of birth")
	}
	return nil
}

// Format changes values of event data to a consistent format.
func (o *EventDataPatientBase) Format() {
	o.PatientFirstName = strings.ToUpper(o.PatientFirstName)
	o.PatientLastName = strings.ToUpper(o.PatientLastName)
	formattedDate := dcmtools.ParseDob(o.PatientBirthDate, "-")
	if formattedDate != "Unknown" {
		o.PatientBirthDate = formattedDate
	}
}

// EventDataPhysicianPACSSearch is the additional event data that should be logged
// with a PACS search event.
type EventDataPhysicianPACSSearch struct {
	EventDataPhysicianBase
	EventDataPatientBase
	StudyRangeStartDate string `json:"study_start_date"` // earliest study date that should be included in search
	StudyRangeEndDate   string `json:"study_end_date"`   // latest study date that should be included in search
}

// Scan converts json data for PACS search events to a raw byte array for arbitrary JSON tag data.
func (o *EventDataPhysicianPACSSearch) Scan(value any) error {
	if bytes, ok := value.([]byte); ok {
		return json.Unmarshal(bytes, o)
	}
	return errors.New("type assertion to []byte failed")
}

// Type returns the event type for the given event data.
func (o *EventDataPhysicianPACSSearch) Type() EventType {
	return EventTypePhysicianPACSSearch
}

// Value converts EventDataPhysicianPACSSearch into a format that can be stored as JSON in the database.
func (o *EventDataPhysicianPACSSearch) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// Validate returns an error if the given event data is not valid.
func (o *EventDataPhysicianPACSSearch) Validate() error {
	if err := o.EventDataPhysicianBase.Validate(); err != nil {
		return err
	}
	if err := o.EventDataPatientBase.Validate(); err != nil {
		return err
	}
	return nil
}

// Format changes values of event data to a consistent format.
func (o *EventDataPhysicianPACSSearch) Format() {
	o.EventDataPatientBase.Format()
	formattedStartDate := dcmtools.ParseDob(o.StudyRangeStartDate, "-")
	if formattedStartDate != "Unknown" {
		o.StudyRangeStartDate = formattedStartDate
	}
	formattedEndDate := dcmtools.ParseDob(o.StudyRangeEndDate, "-")
	if formattedEndDate != "Unknown" {
		o.StudyRangeEndDate = formattedEndDate
	}
}

// EventDataPhysicianStudyBase is the additional event data that should be logged
// with a event that refers to a single study.
type EventDataPhysicianStudyBase struct {
	EventDataPhysicianBase
	EventDataPatientBase
	StudyUID string `json:"study_uid"` // DICOM UID of study
}

// Scan converts json data to a raw byte array for arbitrary JSON tag data.
func (o *EventDataPhysicianStudyBase) Scan(value any) error {
	if bytes, ok := value.([]byte); ok {
		return json.Unmarshal(bytes, o)
	}
	return errors.New("type assertion to []byte failed")
}

// Validate returns an error if the given event data is not valid.
func (o *EventDataPhysicianStudyBase) Validate() error {
	if err := o.EventDataPhysicianBase.Validate(); err != nil {
		return err
	}
	if err := o.EventDataPatientBase.Validate(); err != nil {
		return err
	}
	if o.StudyUID == "" {
		return errors.New("study uid cannot be empty")
	}
	return nil
}

// Value converts event data into a format that can be stored as JSON in the database.
func (o *EventDataPhysicianStudyBase) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// EventDataPhysicianStudyView is the additional event data that should be logged
// with a study view request.
// Reuses Scan, Validate and Value through embedding.
type EventDataPhysicianStudyView struct {
	EventDataPhysicianStudyBase
}

// Type returns the event type for the given event data.
func (o *EventDataPhysicianStudyView) Type() EventType {
	return EventTypePhysicianStudyView
}

// EventDataPhysicianReportView is the additional event data that should be logged
// with a report view request.
// Reuses Scan, Validate and Value through embedding.
type EventDataPhysicianReportView struct {
	EventDataPhysicianStudyBase
}

// Type returns the event type for the given event data.
func (o *EventDataPhysicianReportView) Type() EventType {
	return EventTypePhysicianReportView
}

// EventDataPhysicianStudyRetrieve is the additional event data that should be logged
// with a retrieval request.
// Reuses Scan, Validate and Value through embedding.
type EventDataPhysicianStudyRetrieve struct {
	EventDataPhysicianStudyBase
}

// Type returns the event type for the given event data.
func (o *EventDataPhysicianStudyRetrieve) Type() EventType {
	return EventTypePhysicianStudyRetrieve
}

// EventType defines the types of events logged to event_logs
// that can be triggered by a user.
type EventType string

const (
	EventTypePhysicianPACSSearch    EventType = "PHYSICIANHUB_PACS_SEARCH"
	EventTypePhysicianStudyRetrieve EventType = "PHYSICIANHUB_STUDY_RETRIEVE"
	EventTypePhysicianStudyView     EventType = "PHYSICIANHUB_STUDY_VIEW"
	EventTypePhysicianReportView    EventType = "PHYSICIANHUB_REPORT_VIEW"
)

// UserType defines the types of user that an event relates to.
type UserType string

const (
	UserTypePhysician UserType = "PHYSICIAN"
)

// Service defines the service that triggered logging of an event.
type Service string

const (
	ServiceProvider Service = "coreapi"
)
