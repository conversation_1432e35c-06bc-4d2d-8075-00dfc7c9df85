package audit_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mocks "gitlab.com/pockethealth/coreapi/generated/mocks/audit"
	"gitlab.com/pockethealth/coreapi/pkg/audit"
	"gitlab.com/pockethealth/coreapi/pkg/audit/internal/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func TestCreatePhysicianPACSSearchEvent(t *testing.T) {
	testCases := map[string]struct {
		initMocks     func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianPACSSearch])
		eventData     []models.Event[*models.EventDataPhysicianPACSSearch]
		expectedError error
	}{
		"successfully creates a log entries with incremental message id": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianPACSSearch]) {
				for i := range data {
					mockStore.EXPECT().CreatePhysicianPACSSearchEvent(
						mock.Anything,
						mock.MatchedBy(func(value models.Event[*models.EventDataPhysicianPACSSearch]) bool {
							return value.LogTimestamp.Truncate(200*time.Millisecond).
								Equal(data[i].LogTimestamp.Truncate(200*time.Millisecond)) &&
								value.Service == data[i].Service &&
								value.Subject == data[i].Subject &&
								value.CorrelationID == data[i].CorrelationID &&
								value.SourceIP == data[i].SourceIP &&
								value.MessageID == uint32(i)
						}),
					).
						Return(nil)
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianPACSSearch]{
				testutils.BuildPhysicianPACSSearchEvent(t),
				testutils.BuildPhysicianPACSSearchEvent(t),
			},
		},
		"returns error if DB has an error": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianPACSSearch]) {
				mockStore.EXPECT().
					CreatePhysicianPACSSearchEvent(mock.Anything, mock.Anything).
					Return(fmt.Errorf("DB Error"))
			},
			eventData: []models.Event[*models.EventDataPhysicianPACSSearch]{
				testutils.BuildPhysicianPACSSearchEvent(t),
			},
			expectedError: fmt.Errorf("DB Error"),
		},
		"returns error if event data validation fails": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianPACSSearch]) {
				// invalidate event data
				for i := range data {
					data[i].EventData.PatientFirstName = ""
					data[i].EventData.PatientLastName = ""
					data[i].EventData.PatientBirthDate = ""
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianPACSSearch]{
				testutils.BuildPhysicianPACSSearchEvent(t),
			},
			expectedError: errors.New(
				"need at least one of patient first name, last name or date of birth",
			),
		},
	}
	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				mockStore := mocks.NewMockStore(t)
				service := audit.NewService(mockStore)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, mockStore, tc.eventData)
				}

				for i := range tc.eventData {
					// create enriched context
					ctx := context.Background()
					ctx = auth.WithSubject(ctx, tc.eventData[i].Subject)
					ctx = auth.WithIP(ctx, tc.eventData[i].SourceIP)
					ctx = context.WithValue(
						ctx,
						logutils.CorrelationIdContextKey,
						tc.eventData[i].CorrelationID,
					)
					err := service.CreatePhysicianPACSSearchEvent(
						ctx,
						*tc.eventData[i].EventData,
					)
					// check errors
					if tc.expectedError == nil {
						require.NoError(t, err)
					} else {
						require.Error(t, err) // if there is no error here, calling err.Error() below will cause the test to crash
						require.EqualError(t, tc.expectedError, err.Error())
					}
				}
			},
		)
	}
}

func TestCreatePhysicianStudyRetrieveEvent(t *testing.T) {
	testCases := map[string]struct {
		initMocks     func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyRetrieve])
		eventData     []models.Event[*models.EventDataPhysicianStudyRetrieve]
		expectedError error
	}{
		"successfully creates a log entries with incremental message id": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyRetrieve]) {
				for i := range data {
					mockStore.EXPECT().CreatePhysicianStudyRetrieveEvent(
						mock.Anything,
						mock.MatchedBy(func(value models.Event[*models.EventDataPhysicianStudyRetrieve]) bool {
							return value.LogTimestamp.Truncate(200*time.Millisecond).
								Equal(data[i].LogTimestamp.Truncate(200*time.Millisecond)) &&
								value.Service == data[i].Service &&
								value.Subject == data[i].Subject &&
								value.CorrelationID == data[i].CorrelationID &&
								value.SourceIP == data[i].SourceIP &&
								value.MessageID == uint32(i)
						}),
					).
						Return(nil)
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyRetrieve]{
				testutils.BuildPhysicianStudyRetrieveEvent(t),
				testutils.BuildPhysicianStudyRetrieveEvent(t),
			},
		},
		"returns error if DB has an error": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyRetrieve]) {
				mockStore.EXPECT().
					CreatePhysicianStudyRetrieveEvent(mock.Anything, mock.Anything).
					Return(fmt.Errorf("DB Error"))
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyRetrieve]{
				testutils.BuildPhysicianStudyRetrieveEvent(t),
			},
			expectedError: fmt.Errorf("DB Error"),
		},
		"returns error if event data validation fails": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyRetrieve]) {
				// invalidate event data
				for i := range data {
					data[i].EventData.ProviderID = 0
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyRetrieve]{
				testutils.BuildPhysicianStudyRetrieveEvent(t),
			},
			expectedError: errors.New("provider id cannot be empty"),
		},
	}

	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				mockStore := mocks.NewMockStore(t)
				service := audit.NewService(mockStore)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, mockStore, tc.eventData)
				}
				for i := range tc.eventData {
					// create enriched context
					ctx := context.Background()
					ctx = auth.WithSubject(ctx, tc.eventData[i].Subject)
					ctx = auth.WithIP(ctx, tc.eventData[i].SourceIP)
					ctx = context.WithValue(
						ctx,
						logutils.CorrelationIdContextKey,
						tc.eventData[i].CorrelationID,
					)
					err := service.CreatePhysicianStudyRetrieveEvent(
						ctx,
						*tc.eventData[i].EventData,
					)
					// check errors
					if tc.expectedError == nil {
						require.NoError(t, err)
					} else {
						require.Error(t, err) // if there is no error here, calling err.Error() below will cause the test to crash
						require.EqualError(t, tc.expectedError, err.Error())
					}
				}
			},
		)
	}
}

func TestCreatePhysicianStudyViewEvent(t *testing.T) {
	testCases := map[string]struct {
		initMocks     func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyView])
		eventData     []models.Event[*models.EventDataPhysicianStudyView]
		expectedError error
	}{
		"successfully creates a log entries with incremental message id": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyView]) {
				for i := range data {
					mockStore.EXPECT().CreatePhysicianStudyViewEvent(
						mock.Anything,
						mock.MatchedBy(func(value models.Event[*models.EventDataPhysicianStudyView]) bool {
							return value.LogTimestamp.Truncate(200*time.Millisecond).
								Equal(data[i].LogTimestamp.Truncate(200*time.Millisecond)) &&
								value.Service == data[i].Service &&
								value.Subject == data[i].Subject &&
								value.CorrelationID == data[i].CorrelationID &&
								value.SourceIP == data[i].SourceIP &&
								value.MessageID == uint32(i)
						}),
					).
						Return(nil)
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyView]{
				testutils.BuildPhysicianStudyViewEvent(t),
				testutils.BuildPhysicianStudyViewEvent(t),
			},
		},
		"returns error if DB has an error": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyView]) {
				mockStore.EXPECT().
					CreatePhysicianStudyViewEvent(mock.Anything, mock.Anything).
					Return(fmt.Errorf("DB Error"))
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyView]{
				testutils.BuildPhysicianStudyViewEvent(t),
			},
			expectedError: fmt.Errorf("DB Error"),
		},
		"returns error if event data validation fails": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianStudyView]) {
				// invalidate event data
				for i := range data {
					data[i].EventData.UserID = ""
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianStudyView]{
				testutils.BuildPhysicianStudyViewEvent(t),
			},
			expectedError: errors.New("user id cannot be empty"),
		},
	}

	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				mockStore := mocks.NewMockStore(t)
				service := audit.NewService(mockStore)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, mockStore, tc.eventData)
				}
				for i := range tc.eventData {
					// create enriched context
					ctx := context.Background()
					ctx = auth.WithSubject(ctx, tc.eventData[i].Subject)
					ctx = auth.WithIP(ctx, tc.eventData[i].SourceIP)
					ctx = context.WithValue(
						ctx,
						logutils.CorrelationIdContextKey,
						tc.eventData[i].CorrelationID,
					)
					err := service.CreatePhysicianStudyViewEvent(
						ctx,
						*tc.eventData[i].EventData,
					)
					// check errors
					if tc.expectedError == nil {
						require.NoError(t, err)
					} else {
						require.Error(t, err) // if there is no error here, calling err.Error() below will cause the test to crash
						require.EqualError(t, tc.expectedError, err.Error())
					}
				}
			},
		)
	}
}

func TestCreatePhysicianReportViewEvent(t *testing.T) {
	testCases := map[string]struct {
		desc          string
		initMocks     func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianReportView])
		eventData     []models.Event[*models.EventDataPhysicianReportView]
		expectedError error
	}{
		"successfully creates a log entries with incremental message id": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianReportView]) {
				for i := range data {
					mockStore.EXPECT().CreatePhysicianReportViewEvent(
						mock.Anything,
						mock.MatchedBy(func(value models.Event[*models.EventDataPhysicianReportView]) bool {
							return value.LogTimestamp.Truncate(200*time.Millisecond).
								Equal(data[i].LogTimestamp.Truncate(200*time.Millisecond)) &&
								value.Service == data[i].Service &&
								value.Subject == data[i].Subject &&
								value.CorrelationID == data[i].CorrelationID &&
								value.SourceIP == data[i].SourceIP &&
								value.MessageID == uint32(i)
						}),
					).
						Return(nil)
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianReportView]{
				testutils.BuildPhysicianReportViewEvent(t),
				testutils.BuildPhysicianReportViewEvent(t),
			},
		},
		"returns error if DB has an error": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianReportView]) {
				mockStore.EXPECT().
					CreatePhysicianReportViewEvent(mock.Anything, mock.Anything).
					Return(fmt.Errorf("DB Error"))
			},
			eventData: []models.Event[*models.EventDataPhysicianReportView]{
				testutils.BuildPhysicianReportViewEvent(t),
			},
			expectedError: fmt.Errorf("DB Error"),
		},
		"returns error if event data validation fails": {
			initMocks: func(t *testing.T, mockStore *mocks.MockStore, data []models.Event[*models.EventDataPhysicianReportView]) {
				// invalidate event data
				for i := range data {
					data[i].EventData.StudyUID = ""
				}
			},
			eventData: []models.Event[*models.EventDataPhysicianReportView]{
				testutils.BuildPhysicianReportViewEvent(t),
			},
			expectedError: errors.New("study uid cannot be empty"),
		},
	}

	for _, tc := range testCases {
		t.Run(
			tc.desc,
			func(t *testing.T) {
				mockStore := mocks.NewMockStore(t)
				service := audit.NewService(mockStore)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, mockStore, tc.eventData)
				}
				for i := range tc.eventData {
					// create enriched context
					ctx := context.Background()
					ctx = auth.WithSubject(ctx, tc.eventData[i].Subject)
					ctx = auth.WithIP(ctx, tc.eventData[i].SourceIP)
					ctx = context.WithValue(
						ctx,
						logutils.CorrelationIdContextKey,
						tc.eventData[i].CorrelationID,
					)
					err := service.CreatePhysicianReportViewEvent(
						ctx,
						*tc.eventData[i].EventData,
					)
					// check errors
					if tc.expectedError == nil {
						require.NoError(t, err)
					} else {
						require.Error(t, err) // if there is no error here, calling err.Error() below will cause the test to crash
						require.EqualError(t, tc.expectedError, err.Error())
					}
				}
			},
		)
	}
}
