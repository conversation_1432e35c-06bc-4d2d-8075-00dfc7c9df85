package testutils

import (
	"testing"
	"time"

	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func BuildPhysicianPACSSearchEvent(
	t *testing.T,
) models.Event[*models.EventDataPhysicianPACSSearch] {
	t.Helper()
	eventData := &models.EventDataPhysicianPACSSearch{
		EventDataPhysicianBase: buildPhysicianBaseEventData(t),
		EventDataPatientBase:   buildPatientBaseEventData(t),
		StudyRangeStartDate:    testutils.GenerateRandomDigits(t, 8),
		StudyRangeEndDate:      testutils.GenerateRandomDigits(t, 8),
	}
	return buildEvent(t, eventData)
}

func BuildPhysicianStudyRetrieveEvent(
	t *testing.T,
) models.Event[*models.EventDataPhysicianStudyRetrieve] {
	t.Helper()
	eventData := &models.EventDataPhysicianStudyRetrieve{
		EventDataPhysicianStudyBase: buildBaseStudyEventData(t),
	}
	return buildEvent(t, eventData)
}

func BuildPhysicianStudyViewEvent(
	t *testing.T,
) models.Event[*models.EventDataPhysicianStudyView] {
	t.Helper()
	eventData := &models.EventDataPhysicianStudyView{
		EventDataPhysicianStudyBase: buildBaseStudyEventData(t),
	}
	return buildEvent(t, eventData)
}

func BuildPhysicianReportViewEvent(
	t *testing.T,
) models.Event[*models.EventDataPhysicianReportView] {
	t.Helper()
	eventData := &models.EventDataPhysicianReportView{
		EventDataPhysicianStudyBase: buildBaseStudyEventData(t),
	}
	return buildEvent(t, eventData)
}

func buildBaseStudyEventData(t *testing.T) models.EventDataPhysicianStudyBase {
	t.Helper()
	return models.EventDataPhysicianStudyBase{
		EventDataPhysicianBase: buildPhysicianBaseEventData(t),
		EventDataPatientBase:   buildPatientBaseEventData(t),
		StudyUID:               testutils.GenerateRandomString(t, 10),
	}
}

func buildPhysicianBaseEventData(t *testing.T) models.EventDataPhysicianBase {
	t.Helper()
	return models.EventDataPhysicianBase{
		ProviderID: testutils.GenerateRandomInt64(t),
		UserID:     testutils.GenerateRandomString(t, 10),
		UserType:   models.UserTypePhysician,
	}
}

func buildPatientBaseEventData(t *testing.T) models.EventDataPatientBase {
	t.Helper()
	return models.EventDataPatientBase{
		PatientFirstName: testutils.GenerateRandomString(t, 10),
		PatientLastName:  testutils.GenerateRandomString(t, 10),
		PatientBirthDate: testutils.GenerateRandomDigits(t, 8),
	}
}

func buildEvent[T models.EventData](t *testing.T, eventData T) models.Event[T] {
	t.Helper()
	return models.Event[T]{
		LogTimestamp: time.Now().UTC().Truncate(time.Microsecond),
		Service:      models.ServiceProvider,
		ClientID:     ksuid.New().String(),
		MessageID: uint32( //nolint:gosec // G115: We assume that the testutils func respects the given range, which is a valid range for uint32
			testutils.GenerateRandomIntInRange(
				t,
				1,
				10000,
			),
		),
		Subject:       testutils.GenerateRandomString(t, 10),
		CorrelationID: testutils.GenerateRandomString(t, 10),
		SourceIP:      testutils.GenerateRandomString(t, 10),
		EventType:     models.EventTypePhysicianPACSSearch,
		EventData:     eventData,
	}
}
