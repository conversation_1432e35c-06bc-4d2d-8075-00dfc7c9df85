//go:build integration
// +build integration

package internal_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/audit/internal"
	internal_testutils "gitlab.com/pockethealth/coreapi/pkg/audit/internal/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/repository"
)

const (
	GetEventLogDataQuery = `
		SELECT 
			produced_at,
			service,
			client_id,
			message_id,
			subject,
			correlation_id,
			source_ip,
			event_type,
			event_data

		FROM audit.event_logs 
		WHERE 
			client_id = :client_id
			AND message_id = :message_id`
)

func TestCreatePhysicianPACSSearchEvent(t *testing.T) {
	ctx := context.Background()
	repo := setupRepository(t)
	t.Run("successfully creates new event", func(t *testing.T) {
		repo.WithTx(ctx, func(ctx context.Context, tx *repository.Tx) error {
			// create new store
			store := internal.NewStore(tx)
			// set up test data
			testEvent := internal_testutils.BuildPhysicianPACSSearchEvent(t)

			// create new log entry
			err := store.CreatePhysicianPACSSearchEvent(ctx, testEvent)
			require.NoError(t, err)

			// query created entry
			result := getTestData[*models.EventDataPhysicianPACSSearch](
				t,
				tx,
				testEvent.ClientID,
				testEvent.MessageID,
			)
			require.NotNil(t, result)
			// verify new entry matches expected
			compareEventData(t, testEvent, *result)
			return errors.New("cleanup - trigger tx rollback")
		})
	})
}

func TestCreatePhysicianStudyRetrieveEvent(t *testing.T) {
	ctx := context.Background()
	repo := setupRepository(t)
	t.Run("successfully creates new event", func(t *testing.T) {
		repo.WithTx(ctx, func(ctx context.Context, tx *repository.Tx) error {
			// create new store
			store := internal.NewStore(tx)
			// set up test data
			testEvent := internal_testutils.BuildPhysicianStudyRetrieveEvent(t)

			// create new log entry
			err := store.CreatePhysicianStudyRetrieveEvent(ctx, testEvent)
			require.NoError(t, err)

			// query created entry
			result := getTestData[*models.EventDataPhysicianStudyRetrieve](
				t,
				tx,
				testEvent.ClientID,
				testEvent.MessageID,
			)
			require.NotNil(t, result)
			// verify new entry matches expected
			compareEventData(t, testEvent, *result)
			return errors.New("cleanup - trigger tx rollback")
		})
	})
}

func TestCreatePhysicianStudyViewEvent(t *testing.T) {
	ctx := context.Background()
	repo := setupRepository(t)
	t.Run("successfully creates new event", func(t *testing.T) {
		repo.WithTx(ctx, func(ctx context.Context, tx *repository.Tx) error {
			// create new store
			store := internal.NewStore(tx)
			// set up test data
			testEvent := internal_testutils.BuildPhysicianStudyViewEvent(t)

			// create new log entry
			err := store.CreatePhysicianStudyViewEvent(ctx, testEvent)
			require.NoError(t, err)

			// query created entry
			result := getTestData[*models.EventDataPhysicianStudyView](
				t,
				tx,
				testEvent.ClientID,
				testEvent.MessageID,
			)
			require.NotNil(t, result)
			// verify new entry matches expected
			compareEventData(t, testEvent, *result)
			return errors.New("cleanup - trigger tx rollback")
		})
	})
}

func TestCreatePhysicianReportViewEvent(t *testing.T) {
	ctx := context.Background()
	repo := setupRepository(t)
	t.Run("successfully creates new event", func(t *testing.T) {
		repo.WithTx(ctx, func(ctx context.Context, tx *repository.Tx) error {
			// create new store
			store := internal.NewStore(tx)
			// set up test data
			testEvent := internal_testutils.BuildPhysicianReportViewEvent(t)

			// create new log entry
			err := store.CreatePhysicianReportViewEvent(ctx, testEvent)
			require.NoError(t, err)

			// query created entry
			result := getTestData[*models.EventDataPhysicianReportView](
				t,
				tx,
				testEvent.ClientID,
				testEvent.MessageID,
			)
			require.NotNil(t, result)
			// verify new entry matches expected
			compareEventData(t, testEvent, *result)
			return errors.New("cleanup - trigger tx rollback")
		})
	})
}

func setupRepository(t *testing.T) repository.Repository {
	t.Helper()
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	repo, err := repository.New(repository.WithConnectionString(cfg.SqlConnString))
	require.NoError(t, err)
	return repo
}

func getTestData[T models.EventData](
	t *testing.T,
	tx *repository.Tx,
	clientID string,
	messageID uint32,
) *models.Event[T] {
	t.Helper()

	result := &models.Event[T]{}
	args := map[string]any{
		"client_id":  clientID,
		"message_id": messageID,
	}
	err := tx.Get(
		context.Background(),
		result,
		GetEventLogDataQuery,
		args,
	)
	if errors.Is(err, sql.ErrNoRows) {
		return nil
	}
	assert.NoError(t, err)
	return result
}

func compareEventData[T models.EventData](
	t *testing.T,
	expected models.Event[T],
	actual models.Event[T],
) {
	assert.Equal(t, expected.LogTimestamp, actual.LogTimestamp)
	assert.Equal(t, expected.Service, actual.Service)
	assert.Equal(t, expected.ClientID, actual.ClientID)
	assert.Equal(t, expected.MessageID, actual.MessageID)
	assert.Equal(t, expected.Subject, actual.Subject)
	assert.Equal(t, expected.CorrelationID, actual.CorrelationID)
	assert.Equal(t, expected.SourceIP, actual.SourceIP)
	assert.Equal(t, expected.EventType, actual.EventType)
	expectedData := expected.EventData
	actualData := actual.EventData
	assert.Equal(t, expectedData, actualData)
}
