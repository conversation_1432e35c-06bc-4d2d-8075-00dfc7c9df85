package internal

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/phutils/v10/pkg/repository"
	"gitlab.com/pockethealth/phutils/v10/pkg/store"
)

type auditStore struct {
	store.Store
	repo repository.Repository
}

func NewStore(repo repository.Repository) *auditStore {
	return &auditStore{
		Store: store.NewStore(repo),
		repo:  repo,
	}
}

// CreatePhysicianPACSSearchEvent adds a new entry to table `event_log`
// for a physician that send a PACS search request to a provider.
func (s *auditStore) CreatePhysicianPACSSearchEvent(
	ctx context.Context,
	event models.Event[*models.EventDataPhysicianPACSSearch],
) error {
	return createEvent[*models.EventDataPhysicianPACSSearch](ctx, s.repo, event)
}

// CreatePhysicianStudyRetrieveEvent adds a new entry to table `event_log`
// for a physician that send a record retrieval request to a provider.
func (s *auditStore) CreatePhysicianStudyRetrieveEvent(
	ctx context.Context,
	event models.Event[*models.EventDataPhysicianStudyRetrieve],
) error {
	return createEvent[*models.EventDataPhysicianStudyRetrieve](ctx, s.repo, event)
}

// CreatePhysicianStudyViewEvent adds a new entry to table `event_log`
// for a physician that views a study they have permission to access.
func (s *auditStore) CreatePhysicianStudyViewEvent(
	ctx context.Context,
	event models.Event[*models.EventDataPhysicianStudyView],
) error {
	return createEvent[*models.EventDataPhysicianStudyView](ctx, s.repo, event)
}

// CreatePhysicianReportViewEvent adds a new entry to table `event_log`
// for a physician that views a report for a study they have permission to access.
func (s *auditStore) CreatePhysicianReportViewEvent(
	ctx context.Context,
	event models.Event[*models.EventDataPhysicianReportView],
) error {
	return createEvent[*models.EventDataPhysicianReportView](ctx, s.repo, event)
}

// createEvent adds a new entry to table `event_log` with the given event data.
func createEvent[T models.EventData](
	ctx context.Context,
	repo repository.Repository,
	event models.Event[T],
) error {
	query := `
		INSERT INTO audit.event_logs
			(
				produced_at,
				service,
				client_id,
				message_id,
				subject,
				correlation_id,
				source_ip,
				event_type,
				event_data
			)
		VALUES
			(
				:produced_at, 
				:service, 
				:client_id, 
				:message_id, 
				:subject,
				:correlation_id,
				:source_ip,
				:event_type, 
				:event_data
			);
	`
	_, err := repo.Insert(
		ctx,
		query,
		event,
	)
	if err != nil {
		return err
	}
	return nil
}
