package iso

import (
	"errors"
	"os/exec"
)

func MakeIso(fileName string, path ...string) error {
	if len(path) > 2 {
		return errors.New("Greater than 2 arguments, this function is not designed to handle.")
	}
	var cmd *exec.Cmd
	if path[1] != "" {
		cmd = exec.Command("genisoimage", "-l", "-o", fileName, path[0], path[1]) // #nosec G204 hope this is ok :/
	} else {
		cmd = exec.Command("genisoimage", "-l", "-o", fileName, path[0]) // #nosec G204 hope this is ok :/
	}
	err := cmd.Run()
	return err
}
