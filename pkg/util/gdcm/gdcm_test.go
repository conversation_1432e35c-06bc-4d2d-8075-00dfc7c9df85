package gdcmutil

import (
	"testing"
)

func TestGetGDCMInfo(t *testing.T) {
	t.Run("test get gdcm info", func(t *testing.T) {
		gdcmstr := `MediaStorage is 1.2.840.10008.5.1.4.1.1.1 [Computed Radiography Image Storage]
		TransferSyntax is 1.2.840.10008.1.2.4.90 [JPEG 2000 Image Compression (Lossless Only)]
		NumberOfDimensions: 2
		Dimensions: (1811,1609,1)
		SamplesPerPixel    :1
		BitsAllocated      :16
		BitsStored         :16
		HighBit            :15
		PixelRepresentation:0
		ScalarType found   :UINT16
		PhotometricInterpretation: MONOCHROME1
		PlanarConfiguration: 0
		TransferSyntax: 1.2.840.10008.1.2.4.90
		Origin: (0,0,0)
		Spacing: (0.143,0.143,1)
		DirectionCosines: (1,0,0,0,1,0)
		Rescale Intercept/Slope: (0,1)
		Orientation Label: AXIAL
		Encapsulated Stream was found to be: lossless`

		gdcminfo, err := parseGdcmStr(gdcmstr)
		if err != nil {
			t.Fatalf("parseGdcmStr should not have returned an error. Got %v", err)
		}
		if gdcminfo.MediaStorageCode != "1.2.840.10008.5.1.4.1.1.1" {
			t.Errorf(
				"Got wrong MediaStorageCode. Expected 1.2.840.10008.5.1.4.1.1.1, got '%s'",
				gdcminfo.MediaStorageCode,
			)
		}
		if gdcminfo.MediaStorageName != "Computed Radiography Image Storage" {
			t.Errorf(
				"Got wrong MediaStorageName. Expected Computed Radiography Image Storage, got '%s'",
				gdcminfo.MediaStorageName,
			)
		}
		if gdcminfo.TransferSyntaxCode != "1.2.840.10008.1.2.4.90" {
			t.Errorf(
				"Got wrong TransferSyntaxCode. Expected 1.2.840.10008.1.2.4.90, got '%s'",
				gdcminfo.TransferSyntaxCode,
			)
		}
		if gdcminfo.TransferSyntaxName != "JPEG 2000 Image Compression (Lossless Only)" {
			t.Errorf(
				"Got wrong TransferSyntaxName. Expected JPEG 2000 Image Compression (Lossless Only), got '%s'",
				gdcminfo.TransferSyntaxName,
			)
		}
		if gdcminfo.PhotometricInterpretation != "MONOCHROME1" {
			t.Errorf(
				"Got wrong PhotometricInterpretation. Expected MONOCHROME1, got '%s'",
				gdcminfo.PhotometricInterpretation,
			)
		}
		if gdcminfo.PixelRepresentation != "0" {
			t.Errorf(
				"Got wrong PixelRepresentation. Expected 0, got '%s'",
				gdcminfo.PixelRepresentation,
			)
		}
		if gdcminfo.PlanarConfiguration != "0" {
			t.Errorf(
				"Got wrong PlanarConfiguration. Expected 0, got '%s'",
				gdcminfo.PlanarConfiguration,
			)
		}
		if gdcminfo.ScalarType != "UINT16" {
			t.Errorf("Got wrong ScalarType. Expected UINT16, got '%s'", gdcminfo.ScalarType)
		}
	})
	t.Run("test empty gdcm info", func(t *testing.T) {
		gdcmstr := ``

		_, err := parseGdcmStr(gdcmstr)
		if err == nil {
			t.Fatalf("parseGdcmStr should have returned an error")
		}
		if err.Error() != "gdcminfo empty" {
			t.Fatalf("parseGdcmStr returned an unexpected error: %v", err)
		}

	})

}
