package gdcmutil

import (
	"errors"
	"os/exec"
	"strings"
)

type GdcmInfo struct {
	MediaStorageCode          string
	MediaStorageName          string
	TransferSyntaxCode        string
	TransferSyntaxName        string
	PixelRepresentation       string
	PhotometricInterpretation string
	ScalarType                string
	PlanarConfiguration       string
}

func GetGdcmInfo(filepath string) (GdcmInfo, error) {
	cmd := exec.Command("gdcminfo", filepath)
	stdouterr, err := cmd.CombinedOutput()
	if err != nil {
		return GdcmInfo{}, err
	}
	return parseGdcmStr(string(stdouterr[:]))
}

//gdcminfo output is formatted like this:
/*MediaStorage is 1.2.840.10008.5.1.4.1.1.1 [Computed Radiography Image Storage]
TransferSyntax is 1.2.840.10008.1.2.4.90 [JPEG 2000 Image Compression (Lossless Only)]
NumberOfDimensions: 2
Dimensions: (1811,1609,1)
SamplesPerPixel    :1
BitsAllocated      :16
BitsStored         :16
HighBit            :15
PixelRepresentation:0
ScalarType found   :UINT16
PhotometricInterpretation: MONOCHROME1
PlanarConfiguration: 0
TransferSyntax: 1.2.840.10008.1.2.4.90
Origin: (0,0,0)
Spacing: (0.143,0.143,1)
DirectionCosines: (1,0,0,0,1,0)
Rescale Intercept/Slope: (0,1)
Orientation Label: AXIAL
Encapsulated Stream was found to be: lossless*/

func parseGdcmStr(gdcmstring string) (GdcmInfo, error) {
	if gdcmstring == "" {
		return GdcmInfo{}, errors.New("gdcminfo empty")
	}
	lines := strings.Split(gdcmstring, "\n")
	gdcminfo := GdcmInfo{}
	for _, line := range lines {
		line = strings.Trim(line, "\t ")
		if strings.HasPrefix(line, "MediaStorage is") {
			bracketIdx := strings.Index(line, "[")
			gdcminfo.MediaStorageName = strings.Trim(line[bracketIdx+1:len(line)-1], " ")
			gdcminfo.MediaStorageCode = strings.Trim(
				line[strings.Index(line, "is ")+3:bracketIdx],
				" ",
			)
		} else if strings.HasPrefix(line, "TransferSyntax is") {
			bracketIdx := strings.Index(line, "[")
			gdcminfo.TransferSyntaxName = strings.Trim(line[bracketIdx+1:len(line)-1], " ")
			gdcminfo.TransferSyntaxCode = strings.Trim(line[strings.Index(line, "is ")+3:bracketIdx], " ")
		} else if strings.HasPrefix(line, "PixelRepresentation") {
			gdcminfo.PixelRepresentation = getStrAfterColon(line)
		} else if strings.HasPrefix(line, "PhotometricInterpretation") {
			gdcminfo.PhotometricInterpretation = getStrAfterColon(line)
		} else if strings.HasPrefix(line, "ScalarType") {
			gdcminfo.ScalarType = getStrAfterColon(line)
		} else if strings.HasPrefix(line, "PlanarConfiguration") {
			gdcminfo.PlanarConfiguration = getStrAfterColon(line)
		} else {
			continue
		}
	}
	return gdcminfo, nil
}

func getStrAfterColon(line string) string {
	return strings.Trim(line[strings.Index(line, ":")+1:], " ")
}
