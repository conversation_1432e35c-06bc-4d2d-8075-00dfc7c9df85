package script

import (
	"bytes"
	"os/exec"
)

func RunNodeScript(scriptPath string, args []string) (error, string) {
	cmd := createNodeCmd(scriptPath, args)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		return err, stderr.String()
	}
	return err, ""
}

func createNodeCmd(scriptPath string, args []string) *exec.Cmd {
	// need to append some things to the beginning of the args list because Command only accepts 1 list
	// max-old-space-size is for node to run in kubernetes
	argsList := []string{"--max-old-space-size=512", scriptPath}
	argsList = append(argsList, args...)
	return exec.Command("node", argsList...) // #nosec G204 hope this is ok :/
}
