package amplitude_util

import (
	"context"

	"github.com/amplitude/analytics-go/amplitude"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
)

const (
	DEFAULT_TO_BASIC             = "default_to_basic"
	REMOVE_DOWNLOAD_ENHANCEMENTS = "remove_download_enhancements"
	SECURITY_QUESTION            = "security_question"
	REGISTER_PAGE_CODE           = "register_page_code"
	MISMATCH_REGION_PROVIDERS    = "mismatch_region_providers"
	AUTO_LOGIN                   = "auto_login"
)

func ExposeExperimentVariant(
	ctx context.Context,
	eventClient interfaces.AmplitudeEventClient,
	acctId string,
	deviceID string,
	flagKey string,
	variant string,
) {
	if acctId != "" || deviceID != "" {
		eventClient.Track(amplitude.Event{
			EventType: "$exposure",
			UserID:    acctId,
			DeviceID:  deviceID,
			// date in format YYYY-MM-DDThh:mm:ss e.g. 2022-08-17T15:36:42
			EventProperties: map[string]interface{}{
				"flag_key": flagKey,
				"variant":  variant,
			},
		})
	}
}

func IsEligibleProvider(providerId uint64, payload map[string]any) bool {
	disabledProviderIds := getProviderIdsFromVariantPayload(payload, false)
	if len(disabledProviderIds) > 0 {
		for _, disabledProviderId := range disabledProviderIds {
			if providerId == disabledProviderId {
				return false
			}
		}
		return true
	}

	enabledProviderIds := getProviderIdsFromVariantPayload(payload, true)
	for _, enabledProviderId := range enabledProviderIds {
		if providerId == enabledProviderId {
			return true
		}
	}
	return false
}

func getProviderIdsFromVariantPayload(payload map[string]any, enabled bool) []uint64 {
	var enabledProviderIds []uint64
	payloadName := "disabledProviders"
	if enabled {
		payloadName = "enabledProviders"
	}

	if enabledProviderIdsPayload, ok := payload[payloadName]; ok {
		if enabledProviders, ok := enabledProviderIdsPayload.([]any); ok {
			for _, enabledProviderIdPayload := range enabledProviders {
				if enabledProviderId, ok := enabledProviderIdPayload.(float64); ok {
					enabledProviderIds = append(
						enabledProviderIds,
						uint64(enabledProviderId),
					)
				}
			}
		}
	}
	return enabledProviderIds
}

func IsBlocklistedAccount(accountId string, payload map[string]any) bool {
	if blocklistedAccountIdsPayload, ok := payload["blocklistedAccountIds"]; ok {
		if blocklistedAccountIds, ok := blocklistedAccountIdsPayload.([]any); ok {
			for _, blocklistedAccountIdPayload := range blocklistedAccountIds {
				if blocklistedAccountId, ok := blocklistedAccountIdPayload.(string); ok &&
					accountId == blocklistedAccountId {
					return true
				}
			}
		}
	}

	return false
}
