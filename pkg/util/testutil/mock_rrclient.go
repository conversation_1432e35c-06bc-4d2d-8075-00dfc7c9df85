package testutil

import (
	"context"
	"errors"
)

// mock regionrouter client that returns mock values based on expected values
type MockRRClient struct {
	ExpectedIDType int16  // used by AddNewId
	ExpectedID     string // used by AddNewId
	ExpectError    bool   // should an error be returned

	MockError error // mock error to return
}

func (m MockRRClient) AddNewId(ctx context.Context, idType int16, id string) error {
	if m.ExpectError {
		return m.MockError
	} else if idType == m.ExpectedIDType && (m.ExpectedID == "" || id == m.ExpectedID) {
		return nil
	} else {
		return errors.New("AddNewId: unexpected arguments")
	}
}
