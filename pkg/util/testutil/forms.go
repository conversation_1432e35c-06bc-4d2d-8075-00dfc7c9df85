package testutil

import (
	"fmt"
	"io"
	"mime/multipart"
	"net/textproto"
	"strings"
)

// "forked" from official go func multipart.Writer.CreateFormFile, but modified
// so we can specify Content-Type header
func CreateFormFileWithType(w *multipart.Writer, fieldname, filename string, mime string) (io.Writer, error) {
	var quoteEscaper = strings.NewReplacer("\\", "\\\\", `"`, "\\\"")

	h := make(textproto.MIMEHeader)
	h.Set("Content-Disposition",
		fmt.Sprintf(`form-data; name="%s"; filename="%s"`,
			quoteEscaper.Replace(fieldname), quoteEscaper.Replace(filename)))
	h.Set("Content-Type", mime)
	return w.CreatePart(h)
}
