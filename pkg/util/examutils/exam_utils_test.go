package examutils

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

func TestApplyExamUnlockStatus(t *testing.T) {
	plans := []planservice.PlanV2{
		{Id: 2, GeneralProviderAccess: true, Amount: 50, FullUnlockAccess: true},
		{Id: 9, FullUnlockAccess: true},
		{Id: 16, GeneralProviderAccess: true, Amount: 0, FullUnlockAccess: false},
	}
	planServiceMock := planservice.PlanSvcMock{
		GetPlansByIdsReturn: plans,
	}
	isActive := true
	isInactive := false
	orderID := "order123"
	dateFormat := "2006-01-02"

	accountID := "james123"

	t.Run("no exams no error", func(t *testing.T) {
		hasActivePaidOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isActive, PlanId: 2, OrderId: orderID},
			},
		}
		err := ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&hasActivePaidOrdersAccountServiceMock,
			accountID,
			[]coreapi.ExamRaw{},
		)
		assert.NoError(t, err)
	})

	t.Run("inactivated exams always locked", func(t *testing.T) {
		hasActivePaidOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isActive, PlanId: 2, OrderId: orderID},
			},
		}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1", AccountId: accountID, Activated: false}},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2", AccountId: accountID, Activated: false}},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&hasActivePaidOrdersAccountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_LOCKED {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_LOCKED,
					exam.UnlockStatus,
				)
			}
		}
	})

	t.Run("no order attribution provides full availability on activated exams", func(t *testing.T) {
		noOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{},
		}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1", AccountId: accountID, Activated: true}},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2", AccountId: accountID, Activated: true}},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&noOrdersAccountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_LOCKED,
					exam.UnlockStatus,
				)
			}
		}
	})

	t.Run("paid active order provides full availability on activated exams", func(t *testing.T) {
		hasActivePaidOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isActive, PlanId: 2, OrderId: orderID},
			},
		}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1", AccountId: accountID, Activated: true}},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2", AccountId: accountID, Activated: true}},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&hasActivePaidOrdersAccountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					exam.UnlockStatus,
				)
			}
		}
	})

	t.Run("paid inactive order provides full availability on activated exams", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-01")
		expiresAt, _ := time.Parse(dateFormat, "2000-01-02")
		hasInactiveOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isInactive, PlanId: 2, OrderId: orderID, ExpiresAt: expiresAt},
			},
		}
		exams := []coreapi.ExamRaw{
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "1",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
				AttributedAt:     attributedAt,
			},
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "2",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
				AttributedAt:     attributedAt,
			},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&hasInactiveOrdersAccountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					exam.UnlockStatus,
				)
			}
		}
	})

	t.Run("basic order provides limited availability on activated exams", func(t *testing.T) {
		hasActiveBasicOrdersAccountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isActive, PlanId: 16, OrderId: orderID},
			},
		}
		exams := []coreapi.ExamRaw{
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "1",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
			},
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "2",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
			},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&hasActiveBasicOrdersAccountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_LIMITED_AVAILABILITY {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_LIMITED_AVAILABILITY,
					exam.UnlockStatus,
				)
			}
		}
	})

	t.Run(
		"stale paid order does not provide full availability on activated exams",
		func(t *testing.T) {
			attributedAt, _ := time.Parse(dateFormat, "2000-01-02")
			expiresAt, _ := time.Parse(dateFormat, "2000-01-01")
			hasActiveBasicOrdersAccountServiceMock := accountservice.AcctSvcMock{
				GetOrdersReturn: []accountservice.Order{
					{IsActive: &isActive, PlanId: 16, OrderId: orderID},
					{IsActive: &isInactive, PlanId: 2, OrderId: "12345", ExpiresAt: expiresAt},
				},
			}
			exams := []coreapi.ExamRaw{
				{
					ExamRawBasic: coreapi.ExamRawBasic{
						UUID:      "1",
						AccountId: accountID,
						Activated: true,
					},
					AttributeOrderId: orderID,
					AttributedAt:     attributedAt,
				},
				{
					ExamRawBasic: coreapi.ExamRawBasic{
						UUID:      "2",
						AccountId: accountID,
						Activated: true,
					},
					AttributeOrderId: orderID,
					AttributedAt:     attributedAt,
				},
			}
			ApplyExamUnlockStatus(
				context.Background(),
				&planServiceMock,
				&hasActiveBasicOrdersAccountServiceMock,
				accountID,
				exams,
			)
			for _, exam := range exams {
				if exam.UnlockStatus != models.EXAM_LIMITED_AVAILABILITY {
					t.Errorf(
						"expected exam to have status : %s, got: %s",
						models.EXAM_LIMITED_AVAILABILITY,
						exam.UnlockStatus,
					)
				}
			}
		},
	)

	t.Run("financial assistance provides full availability on activated exam", func(t *testing.T) {
		accountServiceMock := accountservice.AcctSvcMock{
			GetOrdersReturn: []accountservice.Order{
				{IsActive: &isActive, PlanId: 9, OrderId: orderID},
			},
		}
		exams := []coreapi.ExamRaw{
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "1",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
			},
			{
				ExamRawBasic: coreapi.ExamRawBasic{
					UUID:      "2",
					AccountId: accountID,
					Activated: true,
				},
				AttributeOrderId: orderID,
			},
		}
		ApplyExamUnlockStatus(
			context.Background(),
			&planServiceMock,
			&accountServiceMock,
			accountID,
			exams,
		)
		for _, exam := range exams {
			if exam.UnlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_LIMITED_AVAILABILITY,
					exam.UnlockStatus,
				)
			}
		}
	})
}

func TestIsExamCoveredByOrder(t *testing.T) {
	dateFormat := "2006-01-02"
	orderId := "123"
	t.Run("exam covered by order: expired order", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-01")
		orderExpiresAt, _ := time.Parse(dateFormat, "2000-01-02")
		isCovered := isExamCoveredByOrder(
			coreapi.ExamRaw{AttributedAt: attributedAt},
			accountservice.Order{
				OrderId:   orderId,
				ExpiresAt: orderExpiresAt,
			},
		)
		if !isCovered {
			t.Errorf("expected exam to be covered by order")
		}
	})

	t.Run("exam covered by order: active order", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-01")
		isActive := true
		isCovered := isExamCoveredByOrder(
			coreapi.ExamRaw{AttributedAt: attributedAt},
			accountservice.Order{
				OrderId:  orderId,
				IsActive: &isActive,
			},
		)
		if !isCovered {
			t.Errorf("expected exam to be covered by order")
		}
	})

	t.Run("exam not covered by order: attributed after order time", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-02")
		orderExpiresAt, _ := time.Parse(dateFormat, "2000-01-01")
		isCovered := isExamCoveredByOrder(
			coreapi.ExamRaw{AttributedAt: attributedAt},
			accountservice.Order{
				OrderId:   orderId,
				ExpiresAt: orderExpiresAt,
			},
		)
		if isCovered {
			t.Errorf("expected exam to not be covered by order")
		}
	})

	t.Run("exam not covered by order: no orderID", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-01")
		orderExpiresAt, _ := time.Parse(dateFormat, "2000-01-02")
		isCovered := isExamCoveredByOrder(
			coreapi.ExamRaw{AttributedAt: attributedAt},
			accountservice.Order{
				ExpiresAt: orderExpiresAt,
			},
		)
		if isCovered {
			t.Errorf("expected exam to not be covered by order")
		}
	})
}

func TestApplyExamUnlockStatusHelper(t *testing.T) {
	dateFormat := "2006-01-02"
	plans := map[uint64]planservice.PlanV2{
		2:  {Id: 2, GeneralProviderAccess: true, Amount: 50, FullUnlockAccess: true},
		9:  {Id: 9, FullUnlockAccess: true},
		16: {Id: 16, GeneralProviderAccess: true, Amount: 0, FullUnlockAccess: false},
	}
	accountId := "james123"
	lastPaidGeneralAccessOrderId := "123"
	lastUnpaidGeneralAccessOrderId := "456"
	financialAssistanceOrderId := "789"
	orderIsActive := true

	t.Run("paid active order covers all exams", func(t *testing.T) {
		var lastPaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, IsActive: &orderIsActive}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1"}},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2"}},
		}
		orders := map[string]accountservice.Order{
			lastPaidGeneralAccessOrderId: lastPaidGeneralAccessOrder,
		}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		for _, unlockStatus := range unlockStatuses {
			if unlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					unlockStatus,
				)
			}
		}
	})

	t.Run("paid inactive order covers all exams", func(t *testing.T) {
		attributedAt, _ := time.Parse(dateFormat, "2000-01-01")
		expiresAt, _ := time.Parse(dateFormat, "2000-01-02")
		var lastPaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, ExpiresAt: expiresAt}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1"}, AttributedAt: attributedAt},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2"}, AttributedAt: attributedAt},
		}
		orders := map[string]accountservice.Order{
			lastPaidGeneralAccessOrderId: lastPaidGeneralAccessOrder,
		}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		for _, unlockStatus := range unlockStatuses {
			if unlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					unlockStatus,
				)
			}
		}
	})

	t.Run("basic -> paid: all exams covered", func(t *testing.T) {
		attributedAt1, _ := time.Parse(dateFormat, "2000-01-01")
		attributedAt2, _ := time.Parse(dateFormat, "2000-01-04")
		expiresAt1, _ := time.Parse(dateFormat, "2000-01-02")
		expiresAt2, _ := time.Parse(dateFormat, "2000-01-05")
		var lastUnpaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastUnpaidGeneralAccessOrderId, PlanId: 16, ExpiresAt: expiresAt1}
		var lastPaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, ExpiresAt: expiresAt2}
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1"}, AttributedAt: attributedAt1},
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "2"}, AttributedAt: attributedAt2},
		}
		orders := map[string]accountservice.Order{
			lastPaidGeneralAccessOrderId:   lastPaidGeneralAccessOrder,
			lastUnpaidGeneralAccessOrderId: lastUnpaidGeneralAccessOrder,
		}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		for _, unlockStatus := range unlockStatuses {
			if unlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					unlockStatus,
				)
			}
		}
	})

	t.Run("paid -> basic: exam 1 full access, exam 2 limited availability", func(t *testing.T) {
		attributedAt1, _ := time.Parse(dateFormat, "2000-01-01")
		attributedAt2, _ := time.Parse(dateFormat, "2000-01-04")
		expiresAt1, _ := time.Parse(dateFormat, "2000-01-02")
		expiresAt2, _ := time.Parse(dateFormat, "2000-01-05")
		var lastPaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastPaidGeneralAccessOrderId, PlanId: 2, ExpiresAt: expiresAt1}
		var lastUnpaidGeneralAccessOrder accountservice.Order = accountservice.Order{OrderId: lastUnpaidGeneralAccessOrderId, PlanId: 16, ExpiresAt: expiresAt2}
		exams := []coreapi.ExamRaw{
			{
				ExamRawBasic:     coreapi.ExamRawBasic{UUID: "1"},
				AttributedAt:     attributedAt1,
				AttributeOrderId: lastPaidGeneralAccessOrderId,
			},
			{
				ExamRawBasic:     coreapi.ExamRawBasic{UUID: "2"},
				AttributedAt:     attributedAt2,
				AttributeOrderId: lastUnpaidGeneralAccessOrderId,
			},
		}
		orders := map[string]accountservice.Order{
			lastPaidGeneralAccessOrderId:   lastPaidGeneralAccessOrder,
			lastUnpaidGeneralAccessOrderId: lastUnpaidGeneralAccessOrder,
		}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		if unlockStatuses[exams[0].UUID] != models.EXAM_FULL_ACCESS {
			t.Errorf(
				"expected exam to have status : %s, got: %s",
				models.EXAM_FULL_ACCESS,
				unlockStatuses[exams[0].UUID],
			)
		}
		if unlockStatuses[exams[1].UUID] != models.EXAM_LIMITED_AVAILABILITY {
			t.Errorf(
				"expected exam to have status : %s, got: %s",
				models.EXAM_LIMITED_AVAILABILITY,
				unlockStatuses[exams[1].UUID],
			)
		}
	})

	t.Run("financial assistance code: FULL ACCESS", func(t *testing.T) {
		var lastPaidGeneralAccessOrder accountservice.Order
		exams := []coreapi.ExamRaw{
			{
				ExamRawBasic:     coreapi.ExamRawBasic{UUID: "1"},
				AttributeOrderId: financialAssistanceOrderId,
			},
		}
		orders := map[string]accountservice.Order{
			financialAssistanceOrderId: {OrderId: financialAssistanceOrderId, PlanId: 9},
			lastUnpaidGeneralAccessOrderId: {
				OrderId:  lastUnpaidGeneralAccessOrderId,
				PlanId:   16,
				IsActive: &orderIsActive,
			},
		}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		for _, unlockStatus := range unlockStatuses {
			if unlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam1 to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					unlockStatus,
				)
			}
		}
	})

	t.Run("unattributed exams: FULL ACCESS", func(t *testing.T) {
		var lastPaidGeneralAccessOrder accountservice.Order
		exams := []coreapi.ExamRaw{
			{ExamRawBasic: coreapi.ExamRawBasic{UUID: "1"}},
		}
		orders := map[string]accountservice.Order{}
		unlockStatuses := getExamUnlockStatuses(
			context.Background(),
			accountId,
			exams,
			plans,
			orders,
			lastPaidGeneralAccessOrder,
		)
		for _, unlockStatus := range unlockStatuses {
			if unlockStatus != models.EXAM_FULL_ACCESS {
				t.Errorf(
					"expected exam1 to have status : %s, got: %s",
					models.EXAM_FULL_ACCESS,
					unlockStatus,
				)
			}
		}
	})
}

func TestGetRecordsWithUnlockStatus(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name           string
		records        []models.RecordUploadStatus
		expectedResult []models.RecordUploadStatus
	}{
		{
			name: "should filter out any record with empty unlock status",
			records: []models.RecordUploadStatus{
				{
					UnlockStatus: models.EXAM_FULL_ACCESS,
				},
				{
					UnlockStatus: models.EXAM_LIMITED_AVAILABILITY,
				},
				{
					UnlockStatus: "",
				},
				{
					UnlockStatus: models.EXAM_LOCKED,
				},
			},
			expectedResult: []models.RecordUploadStatus{
				{
					UnlockStatus: models.EXAM_FULL_ACCESS,
				},
				{
					UnlockStatus: models.EXAM_LIMITED_AVAILABILITY,
				},
				{
					UnlockStatus: models.EXAM_LOCKED,
				},
			},
		},
		{
			name:           "should make no changes for empty list",
			records:        []models.RecordUploadStatus{},
			expectedResult: []models.RecordUploadStatus{},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			result := GetRecordsWithUnlockStatus(ctx, testCase.records)
			assert.ElementsMatch(t, testCase.expectedResult, result)
		})
	}
}
