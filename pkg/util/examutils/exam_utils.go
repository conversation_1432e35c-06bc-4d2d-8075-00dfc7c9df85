package examutils

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	orderutils "gitlab.com/pockethealth/coreapi/pkg/orders"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func GetImageToken(imageId string) (string, error) {
	tok, err := auth.MakeImageAuthToken(imageId)
	if err != nil {
		tok = ""
	}
	return tok, err
}

func ApplyExamUnlockStatus(
	ctx context.Context,
	planServiceClient planservice.PlanService,
	accountServiceClient accountservice.AccountService,
	accountID string,
	exams []coreapi.ExamRaw,
) error {
	if len(exams) == 0 {
		return nil
	}

	ordersList, err := accountServiceClient.GetOrders(ctx, accountID, map[string]bool{})
	if err != nil {
		return err
	}

	var orders map[string]accountservice.Order = make(map[string]accountservice.Order)
	planIds := []uint64{}
	for _, order := range ordersList {
		orders[order.OrderId] = order
		planIds = append(planIds, order.PlanId)
	}

	plans, err := planServiceClient.GetPlansByIds(ctx, planIds)
	if err != nil {
		return err
	}
	plansMap := map[uint64]planservice.PlanV2{}
	for _, plan := range plans {
		plansMap[uint64(plan.Id)] = plan // #nosec G115 plans.id is bigint so > 0
	}

	lastEligibleGeneralAccessOrder := orderutils.GetLatestGeneralAccessOrder(
		plansMap,
		ordersList,
	)

	unlockStatuses := getExamUnlockStatuses(
		ctx,
		accountID,
		exams,
		plansMap,
		orders,
		lastEligibleGeneralAccessOrder,
	)

	for i := range exams {
		if exams[i].UnlockStatus == "" {
			if exams[i].Activated {
				exams[i].UnlockStatus = unlockStatuses[exams[i].UUID]
			} else {
				exams[i].UnlockStatus = models.EXAM_LOCKED
			}
		}
	}
	return nil
}

func getExamUnlockStatuses(
	ctx context.Context,
	accountId string,
	exams []coreapi.ExamRaw,
	plans map[uint64]planservice.PlanV2,
	orders map[string]accountservice.Order,
	lastEligibleGeneralAccessOrder accountservice.Order,
) map[string]models.UnlockStatus {
	_ = logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":         accountId,
		"last_paid_order": lastEligibleGeneralAccessOrder.OrderId,
	})
	examUUIDToUnlockStatus := map[string]models.UnlockStatus{}

	for i, exam := range exams {
		var plan planservice.PlanV2
		order, exists := orders[exam.AttributeOrderId]
		if exists {
			plan = plans[order.PlanId]
		}

		// Check if exam is attributed to no plan (shouldn't be the case) or unlocked with a full unlock access plan
		// or is not unlocked with a full unlock access plan but covered by eligible plan later
		if plan.Id == 0 || plan.FullUnlockAccess || exam.FacilityFunded ||
			(isExamCoveredByOrder(exam, lastEligibleGeneralAccessOrder) && !plan.FullUnlockAccess) {
			examUUIDToUnlockStatus[exams[i].UUID] = models.EXAM_FULL_ACCESS
		} else {
			examUUIDToUnlockStatus[exams[i].UUID] = models.EXAM_LIMITED_AVAILABILITY
		}
	}
	return examUUIDToUnlockStatus
}

func isExamCoveredByOrder(
	exam coreapi.ExamRaw,
	order accountservice.Order,
) bool {
	var expiryTime time.Time
	if order.CancelledAt.IsZero() {
		expiryTime = order.ExpiresAt
	} else {
		expiryTime = order.CancelledAt
	}
	return order.OrderId != "" &&
		((order.IsActive != nil && *order.IsActive) || exam.AttributedAt.Before(expiryTime))
}

// GetRecordsWithUnlockStatus takes a list of record upload statuses
// and filters out any records that don't have a unlock status.
// This status is needed for the FE to determine what access permissions a user has.
// Transfer-based studies that have been activated currently do not have an unlock status.
// For more information on how to compute the unlock status for these studies instead, see [DELTA-716]
func GetRecordsWithUnlockStatus(
	ctx context.Context,
	records []models.RecordUploadStatus,
) []models.RecordUploadStatus {
	if len(records) == 0 {
		return []models.RecordUploadStatus{}
	}

	filteredRecords := make([]models.RecordUploadStatus, 0)
	for i := range records {
		if records[i].UnlockStatus != "" {
			filteredRecords = append(filteredRecords, records[i])
		}
	}
	return filteredRecords
}
