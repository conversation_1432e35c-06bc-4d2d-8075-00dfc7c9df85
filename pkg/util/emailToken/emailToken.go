package emailToken

import (
	"encoding/base64"
	"encoding/json"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

type EmailToken struct {
	Key string
}

// parse email token from an encoded and encrpted token string.
func (e *EmailToken) ParseEmailToken(token string) (coreapi.PublicReferEmailToken, error) {
	var emailToken coreapi.PublicReferEmailToken
	// decode token
	tokendecode, err := base64.URLEncoding.DecodeString(token)
	if err != nil {
		return emailToken, err
	}

	// decrypt token
	plaintext, err := secure.Decrypt([]byte(e.Key), tokendecode)
	if err != nil {
		return emailToken, err
	}

	// unmarshal token to PublicReferEmailToken
	if err := json.Unmarshal(plaintext, &emailToken); err != nil {
		return emailToken, err
	}
	return emailToken, nil
}
