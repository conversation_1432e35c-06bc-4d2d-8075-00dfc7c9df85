package emailToken

import (
	"testing"
)

func TestParseEmailToken(t *testing.T) {
	t.Run(
		"when tokenSec<PERSON>ey is this-is-a-secret and accountId is 'acctId', should return emailToken",
		func(t *testing.T) {
			service := EmailToken{
				Key: "this-is-a-secret",
			}
			accountId := "acctId"

			token := "wxkPcxQMkDgmwj5r4tD-jwPDY8dzmF_TYt92utGcBAInsRwYBvAGd3Yp13Na2uwHqTTRlnMIcHao0qa4RBWAZ0IRtqFOXFVlLkvawtU="

			emailToken, err := service.ParseEmailToken(token)
			if err != nil {
				t.<PERSON>rrorf("err when test ParseEmailToken")
			}

			if emailToken.AccountId != accountId {
				t.Errorf("email token parse failed, expected %s got %s", accountId, emailToken.AccountId)
			}
		},
	)
}
