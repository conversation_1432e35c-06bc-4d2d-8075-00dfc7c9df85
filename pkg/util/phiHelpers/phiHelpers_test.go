package phiHelpers

import (
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

func TestGetPhiTypeFromPatient(t *testing.T) {
	patientId := "123"
	pt := &models.PhiPatient{
		Ohip:   "123",
		Ohipvc: "456",
		Bcphn:  "789",
		Ssn:    "012",
		Ipn:    "345",
		AltId:  "678",
	}

	phiProfiles := GetPhiInfoFromPatient(patientId, pt)

	// Check if the length of the slice is 6
	if len(phiProfiles) != 6 {
		t.<PERSON><PERSON><PERSON>("Expected length of phiProfiles to be 6, but got %d", len(phiProfiles))
	}
}
