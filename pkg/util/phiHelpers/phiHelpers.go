package phiHelpers

import (
	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

// TODO: POST-MIGRATION(PatientProfiles) - used for shadow write, BCPHN doesn't exist at the moment
// this fn can be deleted when UI is changed
func GetPhiInfoFromUserProfile(
	patientId string,
	userProfile *coreapi.UserProfile,
) []coreapi.PhiProfile {
	req := make([]coreapi.PhiProfile, 0)
	if userProfile.Ohip != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.OHIP,
			PhiValue:  userProfile.Ohip,
		}
		req = append(req, phiProfile)
	}
	if userProfile.Ohipvc != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.OHIP_VC,
			PhiValue:  userProfile.Ohipvc,
		}
		req = append(req, phiProfile)
	}

	if userProfile.Ssn != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.SSN,
			PhiValue:  userProfile.Ssn,
		}
		req = append(req, phiProfile)
	}
	if userProfile.Ipn != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.IPN,
			PhiValue:  userProfile.Ipn,
		}
		req = append(req, phiProfile)
	}
	if userProfile.AltId != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.ALT_H_ID,
			PhiValue:  userProfile.AltId,
		}
		req = append(req, phiProfile)
	}

	return req
}

func GetPhiInfoFromPatient(patientId string, pt *models.PhiPatient) []coreapi.PhiProfile {
	req := make([]coreapi.PhiProfile, 0)
	if pt.Ohip != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.OHIP,
			PhiValue:  pt.Ohip,
		}
		req = append(req, phiProfile)
	}
	if pt.Ohipvc != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.OHIP_VC,
			PhiValue:  pt.Ohipvc,
		}
		req = append(req, phiProfile)
	}
	if pt.Bcphn != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.BCPHN,
			PhiValue:  pt.Bcphn,
		}
		req = append(req, phiProfile)
	}
	if pt.Ssn != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.SSN,
			PhiValue:  pt.Ssn,
		}
		req = append(req, phiProfile)
	}
	if pt.Ipn != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.IPN,
			PhiValue:  pt.Ipn,
		}
		req = append(req, phiProfile)
	}
	if pt.AltId != "" {
		phiProfile := coreapi.PhiProfile{
			Id:        ksuid.New().String(),
			PatientId: patientId,
			PhiType:   coreapi.ALT_H_ID,
			PhiValue:  pt.AltId,
		}
		req = append(req, phiProfile)
	}

	return req
}
