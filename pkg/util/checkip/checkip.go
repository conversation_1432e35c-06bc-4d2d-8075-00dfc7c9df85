package checkip

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
)

type Result struct {
	Country string `json:"country_name"`
}

func IpIsCanada(r *http.Request) bool {
	clientAddr := strings.Split(r.RemoteAddr, ":")
	ip := clientAddr[0]
	url := "https://freegeoip.app/json/" + ip
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false
	}
	client := &http.Client{}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false
	}
	var result Result
	err = json.Unmarshal(body, &result)
	if err != nil || result.Country != "Canada" {
		return false
	}

	return true
}
