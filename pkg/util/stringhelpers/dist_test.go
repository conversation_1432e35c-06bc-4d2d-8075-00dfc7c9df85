package stringhelpers

import "testing"

func Test_isVariation(t *testing.T) {
	type args struct {
		word string
		pw   string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "numbers at end",
			args: args{
				word: "hello",
				pw:   "hello123",
			},
			want: true,
		},
		{
			name: "numbers then char at end",
			args: args{
				word: "hello",
				pw:   "hello123x",
			},
			want: false,
		},
		{
			name: "char then numbers at end",
			args: args{
				word: "hello",
				pw:   "hello123x",
			},
			want: false,
		},
		{
			name: "numbers at start",
			args: args{
				word: "hello",
				pw:   "1hello",
			},
			want: true,
		},
		{
			name: "numbers then char at start",
			args: args{
				word: "hello",
				pw:   "1xhello",
			},
			want: false,
		},
		{
			name: "char then numbers at start",
			args: args{
				word: "hello",
				pw:   "x1hello",
			},
			want: false,
		},
		{
			name: "char then numbers at start",
			args: args{
				word: "hello",
				pw:   "hello",
			},
			want: true,
		},
		{
			name: "numbers on both sides",
			args: args{
				word: "hello",
				pw:   "1hello2",
			},
			want: false,
		},
		{
			name: "different capitalization",
			args: args{
				word: "hello",
				pw:   "HELlo",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSimpleVariation(tt.args.word, tt.args.pw); got != tt.want {
				t.Errorf("IsCommonWordSimpleVariation() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isCommonWordSimpleVariation(t *testing.T) {
	wordList := map[string]struct{}{
		"apple":  {},
		"summer": {},
	}

	type args struct {
		wordList map[string]struct{}
		pw       string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "word not in word list",
			args: args{
				wordList: wordList,
				pw:       "hello123",
			},
			want: false,
		},
		{
			name: "word in list followed by number",
			args: args{
				wordList: wordList,
				pw:       "apple123",
			},
			want: true,
		},
		{
			name: "word in list preceded by number",
			args: args{
				wordList: wordList,
				pw:       "2021Summer",
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsCommonWordSimpleVariation(wordList, tt.args.pw); got != tt.want {
				t.Errorf("IsCommonWordSimpleVariation() = %v, want %v", got, tt.want)
			}
		})
	}
}
