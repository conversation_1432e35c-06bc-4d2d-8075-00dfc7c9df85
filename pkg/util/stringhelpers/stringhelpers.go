package stringhelpers

import (
	"regexp"
	"strings"
)

// Helper function to remove any characters in toReplace and replace them with replacement
func CleanUp(s string, toReplace string, replacement string) string {
	re := regexp.MustCompile(toReplace)
	s1 := re.ReplaceAllString(s, replacement)
	return s1
}

func CompareAlphabeticOnly(s1 string, s2 string) bool {
	//strip all non alphabetic chars
	s1 = CleanUp(s1, `[^A-Za-z]`, "")
	s2 = CleanUp(s2, `[^A-Za-z]`, "")
	return strings.Compare(strings.ToLower(s1), strings.ToLower(s2)) == 0
}

func RemoveDuplicates(arr []string) []string {
	deduped := make([]string, 0)
	check := make(map[string]int)
	for _, str := range arr {
		check[str] = 1
	}

	for str := range check {
		deduped = append(deduped, str)
	}
	return deduped
}

// NonEmptyAndEqual checks to see if 2 strings are equal taking into account
// that these strings may be "" and aren't actually equal in this case as
// this is their default / unpopulated value
func NonEmptyAndEqual(s1, s2 string) bool {
	return s1 != "" && s2 != "" && s1 == s2
}

// borrowed from multipart package
var quoteEscaper = strings.NewReplacer("\\", "\\\\", `"`, "\\\"")

func EscapeQuotes(s string) string {
	return quoteEscaper.Replace(s)
}

// helper function to help properly add escape characters for building JSON
func EscapeTextFieldForJSON(toEscape string) (escapedString string) {

	for _, char := range toEscape {
		if char == '"' || char == '\\' {
			escapedString += "\\"
		}
		escapedString += string(char)
	}
	return escapedString
}

func BidirectionalSubstringMatch(a string, b string) bool {
	a = strings.ToLower(a)
	b = strings.ToLower(b)
	return a != "" && (strings.Contains(a, b) || strings.Contains(b, a))
}
