package stringhelpers

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCleanUp(t *testing.T) {
	var string1 string
	var actual string
	var expected string
	t.Run("strip non-alpabetic characters", func(t *testing.T) {
		string1 = "abc123DEF!@#"
		actual = CleanUp(string1, `[^A-Za-z]`, "")
		expected = "abcDEF"
		if strings.Compare(actual, expected) != 0 {
			t.Fatalf("Expected %s to be %s", actual, expected)
		}
	})
	t.Run("strip non-numeric characters", func(t *testing.T) {
		string1 = ".01*mi234(56)/789'"
		actual = CleanUp(string1, `[^0-9]`, "")
		expected = "0123456789"
		if strings.Compare(actual, expected) != 0 {
			t.Fatalf("Expected %s to be %s", actual, expected)
		}
	})
}

func TestAlphabeticCompare(t *testing.T) {
	var string1 string
	var string2 string
	//happy cases
	t.Run("exact match", func(t *testing.T) {
		string1 = "String"
		string2 = "String"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	t.Run("case difference", func(t *testing.T) {
		string1 = "STRING"
		string2 = "string"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	t.Run("dash difference", func(t *testing.T) {
		string1 = "String-One"
		string2 = "StringOne"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	t.Run("space difference", func(t *testing.T) {
		string1 = "Stringone"
		string2 = "String One"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	t.Run("dash and space difference", func(t *testing.T) {
		string1 = "String-One"
		string2 = "String One"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	t.Run("accent difference", func(t *testing.T) {
		string1 = "S̆t̗r̈́ìn̂g̃"
		string2 = "String"
		if !CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s to match", string1, string2)
		}
	})

	//unhappy cases
	t.Run("not a match", func(t *testing.T) {
		string1 = "String-OneTwo"
		string2 = "StringOne"
		if CompareAlphabeticOnly(string1, string2) {
			t.Fatalf("Expected %s and %s not to match", string1, string2)
		}
	})
}

func TestRemoveDuplicates(t *testing.T) {
	//happy cases
	t.Run("no dupes", func(t *testing.T) {
		arr := []string{"a", "bc", "def"}
		deduped := RemoveDuplicates(arr)
		if !assert.ElementsMatch(t, arr, deduped) {
			t.Fatalf("Expected deduped result to be the same, got %v", deduped)
		}
	})

	t.Run("empty", func(t *testing.T) {
		arr := []string{}
		deduped := RemoveDuplicates(arr)
		if !assert.ElementsMatch(t, arr, deduped) {
			t.Fatalf("Expected deduped result to be the same, got %v", deduped)
		}
	})

	t.Run("one dupe", func(t *testing.T) {
		arr := []string{"a", "bc", "def", "bc"}
		expect := []string{"a", "bc", "def"}
		deduped := RemoveDuplicates(arr)
		if !assert.ElementsMatch(t, expect, deduped) {
			t.Fatalf("Expected %v, got %v", expect, deduped)
		}
	})

	t.Run("two dupe", func(t *testing.T) {
		arr := []string{"a", "bc", "def", "bc", "a", "a"}
		expect := []string{"a", "bc", "def"}
		deduped := RemoveDuplicates(arr)
		if !assert.ElementsMatch(t, expect, deduped) {
			t.Fatalf("Expected %v, got %v", expect, deduped)
		}
	})
}

func TestNonEmptyAndEqual(t *testing.T) {
	testCases := []struct {
		name           string
		string1        string
		string2        string
		expectedResult bool
	}{
		{
			name:           "returns false if both fields are empty",
			string1:        "",
			string2:        "",
			expectedResult: false,
		},
		{
			name:           "returns true if both fields are equal",
			string1:        "test",
			string2:        "test",
			expectedResult: true,
		},
		{
			name:           "returns false if both fields aren't equal",
			string1:        "nilay",
			string2:        "shah",
			expectedResult: false,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			equality := NonEmptyAndEqual(tt.string1, tt.string2)

			if equality != tt.expectedResult {
				t.Errorf(
					"NonEmptyAndEqual() actually got an error: %v, expected error: %v",
					equality,
					tt.expectedResult,
				)
			}
		})
	}
}

func TestBidirectionalSubstringMatch(t *testing.T) {
	testCases := []struct {
		name           string
		string1        string
		string2        string
		expectedResult bool
	}{
		{
			name:           "returns false if both fields are empty",
			expectedResult: false,
		},
		{
			name:           "return true if both fields are equal",
			string1:        "cookie",
			string2:        "cookie",
			expectedResult: true,
		},
		{
			name:           "return true if string1 contains string2",
			string1:        "cookie",
			string2:        "cookie monster",
			expectedResult: true,
		},
		{
			name:           "return true if string2 contains string1",
			string1:        "cookie monster",
			string2:        "cookie",
			expectedResult: true,
		},
		{
			name:           "return true if string2 contains string1, letter case shouldn't matter",
			string1:        "Cookie",
			string2:        "cookie monster",
			expectedResult: true,
		},
		{
			name:           "return false if same first name but different middle name",
			string1:        "cookie monster",
			string2:        "cookie trashcan",
			expectedResult: false,
		},
		{
			name:           "return false if strings don't match in any way",
			string1:        "cookie monster",
			string2:        "big bird",
			expectedResult: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := BidirectionalSubstringMatch(tc.string1, tc.string2)
			assert.Equal(t, tc.expectedResult, res)
		})
	}
}
