package stringhelpers

import (
	"regexp"
	"strings"
)

var re = regexp.MustCompile("^[0-9]+$")

func IsCommonWordSimpleVariation(commonWords map[string]struct{}, pw string) bool {
	for cw := range commonWords {
		isVar := isSimpleVariation(cw, strings.ToLower(pw))
		if isVar {
			return true
		}
	}

	return false
}

func isSimpleVariation(word string, pw string) bool {
	pwLower := strings.ToLower(pw)
	tl := strings.TrimSuffix(pwLower, word)
	tr := strings.TrimPrefix(pwLower, word)

	return re.MatchString(tl) != re.MatchString(tr) /*xor*/ ||
		(len(tl) == 0 && len(tr) == 0)
}
