package json

import (
	"encoding/json"
)

func IsInJsonMap(value string, jsonString string) (exist bool, key string, err error) {
	var jsonMap map[string]string
	err = json.Unmarshal([]byte(jsonString), &jsonMap)
	if err != nil {
		return
	}
	for k, v := range jsonMap {
		if v == value {
			exist = true
			key = k
			break
		}
	}
	return
}

func GetValueFromJsonMap(key string, jsonString string) (exist bool, value string, err error) {
	if jsonString == "" {
		return
	}
	var jsonMap map[string]string
	err = json.Unmarshal([]byte(jsonString), &jsonMap)
	if err != nil {
		return
	}
	value, exist = jsonMap[key]
	return
}
