package json

import (
	"testing"
)

func TestIsInJsonMap(t *testing.T) {
	examTypeJson := `{"DX/CR": "X-ray","US": "Ultrasound","MRI": "MRI","DOC": "Document","OT": "Other"}`
	examSiteJson := `{"TGH": "Toronto General Hospital","PMH": "Princess Margaret Hospital"}`
	randomJson := `{"a":"apple", "b":"bird", "c":"cookie"}`
	t.<PERSON>("Ultrasound is in examTypeJson", func(t *testing.T) {
		exist, key, err := IsInJsonMap("Ultrasound", examTypeJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.<PERSON><PERSON><PERSON>())
		}
		if !exist {
			t.<PERSON><PERSON>rf("got %t when expected true", exist)
		}
		if key == "" {
			t.<PERSON>rrorf("got %s when expected %s", key, "US")
		}
	})
	t.<PERSON>("X-Ray is not in examTypeJson but X-ray is", func(t *testing.T) {
		exist, _, err := IsInJsonMap("X-Ray", examTypeJson)
		if err != nil {
			t.<PERSON><PERSON>("got error when expected none: %q", err.Error())
		}
		if exist {
			t.Errorf("got %t when expected false, because it is case sensitive", exist)
		}

		exist, key, err := IsInJsonMap("X-ray", examTypeJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if !exist {
			t.Errorf("got %t when expected true", exist)
		}
		if key == "" {
			t.Errorf("got %s when expected %s", key, "DX/CR")
		}
	})
	t.Run("Tester 1 Location is not in examSiteJson", func(t *testing.T) {
		exist, key, err := IsInJsonMap("Tester 1 Location", examSiteJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if exist {
			t.Errorf("got %t when expected false", exist)
		}
		if key != "" {
			t.Errorf("got %s when expected empty", key)
		}
	})

	t.Run("Apple is not in randomJson because of uppercase", func(t *testing.T) {
		exist, key, err := IsInJsonMap("Apple", randomJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if exist {
			t.Errorf("got %t when expected false", exist)
		}
		if key != "" {
			t.Errorf("got %s when expected empty", key)
		}
	})
}

func TestGetValueFromJsonMap(t *testing.T) {
	randomJson := `{"a":"apple", "b":"bird", "c":"cookie"}`
	t.Run("a is in randomJson", func(t *testing.T) {
		exist, value, err := GetValueFromJsonMap("a", randomJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if !exist {
			t.Errorf("got %t when expected true", exist)
		}
		if value == "" {
			t.Errorf("got empty when expected %s", "apple")
		}
	})

	t.Run("a is not in randomJson", func(t *testing.T) {
		exist, _, err := GetValueFromJsonMap("a", randomJson)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if !exist {
			t.Errorf("got %t when expected true", exist)
		}
	})
}
