package httperror

import (
	"errors"
	"net/http"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

const (
	UNAUTHORIZED_TOKEN = "UNAUTHORIZED_TOKEN"
	BAD_QUERY_STRING   = "BAD_QUERY_STRING"
	EMPTY_PARAM        = "EMPTY_PARAM"
	PARSE_FAILED       = "PARSE_FAILED"
	RETRIEVAL_FAILED   = "RETRIEVAL_FAILED"
)

// wrapper for http.Error() that logs the error message.
func ErrorWithLog(w http.ResponseWriter, r *http.Request, errStr string, code int) {

	logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
		"status": code,
		"err":    errStr,
	}).Error("Http request error")

	http.Error(w, errStr, code)
}

func ErrorWithLogFields(
	w http.ResponseWriter,
	r *http.Request,
	errStr string,
	code int,
	fields map[string]interface{},
) {

	logutils.DebugCtxLogger(r.Context()).WithFields(logrus.Fields{
		"status": code,
		"err":    errStr,
	}).WithFields(fields).Error("Http request error")

	http.Error(w, errStr, code)
}

func MapToError(statusCode int, defaultMsg string) error {
	switch statusCode {
	case http.StatusNotFound:
		return errors.New(errormsgs.ERR_NOT_FOUND)
	case http.StatusBadRequest:
		return errors.New(errormsgs.ERR_INVALID_REQ_BODY)
	case http.StatusUnauthorized:
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	case http.StatusOK:
		return nil
	default:
		return errors.New(defaultMsg)
	}
}

func ErrormsgToStatus(msg string) int {
	switch msg {
	case errormsgs.ERR_NOT_FOUND:
		return http.StatusNotFound
	case errormsgs.ERR_INVALID_REQ_BODY:
		return http.StatusBadRequest
	case errormsgs.ERR_NOT_AUTHORIZED:
		return http.StatusUnauthorized
	case errormsgs.ERR_MISSING_PAYMENT:
		return http.StatusPaymentRequired
	default:
		return http.StatusInternalServerError
	}
}
