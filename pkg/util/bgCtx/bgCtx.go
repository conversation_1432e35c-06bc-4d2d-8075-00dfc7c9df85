package bgctx

import (
	"context"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// returns a bg ctx with correlation id included if its present in ctx
func GetBGCtxWithCorrelation(ctx context.Context) context.Context {
	bgCtx := context.Background()
	if corID := ctx.Value(logutils.CorrelationIdContextKey); corID != nil {
		bgCtx = context.WithValue(bgCtx, logutils.CorrelationIdContextKey, corID)
	}
	return bgCtx
}
