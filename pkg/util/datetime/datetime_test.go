package datetime

import (
	"testing"
	"time"
)

func TestCalculateAge(t *testing.T) {
	t.Run("one year after current date", func(t *testing.T) {
		dob := time.Now().AddDate(1, 0, 0)
		age := CalculateAge(dob)

		if age != -1 {
			t.<PERSON><PERSON><PERSON>("got %d when expected -1", age)
		}
	})
	t.Run("one year before current date", func(t *testing.T) {
		dob := time.Now().AddDate(-1, 0, 0)
		age := CalculateAge(dob)

		if age != 1 {
			t.<PERSON><PERSON><PERSON>("got %d when expected 1", age)
		}
	})
	t.Run("already 18", func(t *testing.T) {
		dob := time.Now().AddDate(-18, 0, -1)
		age := CalculateAge(dob)

		if age != 18 {
			t.<PERSON>rrorf("got %d when expected 18", age)
		}
	})
	t.Run("one more day to 18", func(t *testing.T) {
		dob := time.Now().AddDate(-18, 0, 1)
		age := CalculateAge(dob)

		if age != 17 {
			t.Errorf("got %d when expected 17", age)
		}
	})
	t.Run("born yesterday", func(t *testing.T) {
		dob := time.Now().AddDate(0, 0, -1)
		age := CalculateAge(dob)

		if age != 0 {
			t.Errorf("got %d when expected 17", 0)
		}
	})
}

func TestConvertDate(t *testing.T) {

	t.Run("takes date in MM/DD/YYYY returns YYYY-MM-DD", func(t *testing.T) {
		inputDate := "10/29/1992"
		expectedOutput := "1992-10-29"
		output := ConvertMMDDYYYYToISO8061(inputDate)
		if output != expectedOutput {
			t.Errorf("convertDate(%s) = %s; want %s", inputDate, output, expectedOutput)
		}
	})

	t.Run("takes date in YYYY-MM-DD returns YYYY-MM-DD", func(t *testing.T) {
		inputDate := "1992-10-29"
		expectedOutput := "1992-10-29"
		output := ConvertMMDDYYYYToISO8061(inputDate)
		if output != expectedOutput {
			t.Errorf("convertDate(%s) = %s; want %s", inputDate, output, expectedOutput)
		}
	})
}
