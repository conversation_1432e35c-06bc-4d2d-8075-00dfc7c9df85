package datetime

import (
	"time"

	"github.com/goodsign/monday"
	"github.com/sirupsen/logrus"
	"golang.org/x/text/language"
)

const (
	//TODO: make all date formats consistent
	ProfDOBFormat      = "01/02/2006"
	ReqDOBFormat       = "01/02/2006"
	ViewShareDOBFormat = "20060102"
)

func mondayLocaleFromLangTag(tag language.Tag) monday.Locale {
	switch tag.String() {
	case "es-US":
		return monday.LocaleEsES //esUS not supported by this library
	case "fr-CA":
		return monday.LocaleFrCA
	default:
		return monday.LocaleEnUS
	}
}

func MediumLocaleDateStr(dt time.Time, lang language.Tag) string {
	locale := mondayLocaleFromLangTag(lang)
	format := monday.MediumFormatsByLocale[locale]
	return monday.Format(dt, format, locale)
}

func IsExpired(expiryTime string) bool {
	expired, _ := time.Parse(time.RFC3339, expiryTime)
	loc, _ := time.LoadLocation("America/Toronto")
	return time.Now().In(loc).After(expired)
}

// generates an expiry timestamp that is valid for ValidityPeriod days
// returns the full expiry timestamp and the expiry date as strings
// time zone of timestamp is "America/Toronto"
// format of expiry timestamp is "2006-01-02 15:04"
// format of expiry date is "Jan 02, 2006"
func GenerateExpiry(ValidityPeriod int) (ExpiryTime string, ExpiryDate string) {
	loc, _ := time.LoadLocation("America/Toronto")
	ExpiryNow := time.Now().AddDate(0, 0, ValidityPeriod).In(loc)
	ExpiryTime = ExpiryNow.Format("2006-01-02 15:04")
	ExpiryDate = ExpiryNow.Format("Jan 02, 2006")
	return ExpiryTime, ExpiryDate
}

func GetNowPlusXHours(hrs int) time.Time {
	return time.Now().Add(time.Hour * time.Duration(hrs))
}

func GetNowPlusXMinutes(mins int) time.Time {
	return time.Now().Add(time.Minute * time.Duration(mins))
}

func CalculateAge(birthdate time.Time) (year int) {
	return CalculateAgeAt(birthdate, time.Now())
}

func CalculateAgeAt(birthdate, date time.Time) (year int) {
	if birthdate.After(date) {
		return -1
	}
	y1, M1, d1 := birthdate.Date()
	y2, M2, d2 := date.Date()

	year = int(y2 - y1)
	month := int(M2 - M1)
	day := int(d2 - d1)

	// Normalize negative values
	if day < 0 {
		month--
	}
	if month < 0 {
		year--
	}

	return
}

func ConvertMMDDYYYYToISO8061(i string) string {
	layout := "01/02/2006"
	desiredLayout := "2006-01-02"
	t, err := time.Parse(layout, i)
	if err != nil {
		logrus.Error(err)
	}
	if t.IsZero() {
		//date was not in expected format
		//check if its already in the desired format, and if not, return blank
		if _, err = time.Parse(desiredLayout, i); err != nil {
			return ""
		}
		return i
	}
	return t.Format(desiredLayout)
}
