package file

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func DownloadFromAzureToDisk(
	ctx context.Context,
	containerUrl *container.Client,
	objectId string,
	filepath string,
) (int64, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("object_id", objectId)
	objReader, _ := azureUtils.DownloadBlobReader(ctx, containerUrl, objectId)
	if objReader == nil {
		lg.Error("failed to get blob")
		return 0, errors.New("DownloadBlobReader failure")
	}

	file, err := os.OpenFile(
		filepath,
		os.O_WRONLY|os.O_CREATE,
		0600,
	) // #nosec G304

	if err != nil {
		lg.WithError(err).Error("download file open failed")
		return 0, err
	}

	defer func() {
		errClose := file.Close()
		if errClose != nil {
			lg.WithError(errClose).Error("download file close failed: ", filepath)
		}
	}()

	numBytes, err := io.Copy(file, objReader)
	if err != nil {
		lg.WithError(err).Error("blob file copy failed")
		return numBytes, err
	}
	return numBytes, nil
}

func DownloadFromAzureInMemory(
	ctx context.Context,
	containerUrl *container.Client,
	objectId string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("object_id", objectId)
	objReader, _ := azureUtils.DownloadBlobReader(ctx, containerUrl, objectId)

	decBytesFile, err := io.ReadAll(objReader)
	if err != nil {
		lg.WithError(err).Error("blob file read all failed")
		return nil, err
	}
	return decBytesFile, nil
}

func DetectContentType(
	ctx context.Context,
	contents multipart.File,
) (string, error) {
	lg := logutils.CtxLogger(ctx)

	// the detecting func, http.DetectContentType, reads at most 512 B, so load only
	// at most 512 B from the input file
	lr := io.LimitReader(contents, 512)

	fileBytes, err := io.ReadAll(lr)
	defer func() {
		// reset the original file stream so caller can still use it
		_, err := contents.Seek(0, 0)
		if err != nil {
			lg.WithError(err).Warn("file type check failed to rewind stream")
		}
	}()
	if err != nil {
		return "", fmt.Errorf("file type check can't load file contents: %w", err)
	}

	return http.DetectContentType(fileBytes), nil
}

func IsAllowedImageFileType(mimeContentType string) bool {
	return mimeContentType == "image/jpeg" ||
		mimeContentType == "image/png" ||
		mimeContentType == "application/pdf"
}
