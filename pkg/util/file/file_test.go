package file

import (
	"context"
	"os"
	"testing"
)

func TestDetectContentType(t *testing.T) {
	f, err := os.OpenFile("../../../apptest/gotests/assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
	if err != nil {
		t.Fatalf("test setup error: %v", err)
	}

	f2, err := os.OpenFile("../../../apptest/gotests/assets/samplenotjpeg.jpeg", os.O_RDONLY, 0600)
	if err != nil {
		t.Fatalf("test setup error: %v", err)
	}

	expectedMimeType := "image/jpeg"

	// valid case
	detectedMimeType, err := DetectContentType(context.TODO(), f)
	if expectedMimeType != detectedMimeType {
		t.Errorf("unexpected result, expected %s, got %s", expectedMimeType, detectedMimeType)
	}
	if err != nil {
		t.<PERSON>("unexpected error: %v", err)
	}

	expectedMimeType = "text/plain; charset=utf-8"

	// invalid jpeg case
	detectedMimeType, err = DetectContentType(context.TODO(), f2)
	if expectedMimeType != detectedMimeType {
		t.Errorf(
			"unexpected result, expected %s, got %s",
			expectedMimeType,
			detectedMimeType,
		)
	}
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
}
