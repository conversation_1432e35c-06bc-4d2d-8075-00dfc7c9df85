package providerHelpers

import (
	"regexp"
	"strconv"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

type ProviderField string

const (
	NAME    ProviderField = "name"
	ADDRESS ProviderField = "address"
)

var nonAlphanumericRegex = regexp.MustCompile("[^a-zA-Z0-9 ]+")

func sanitizeString(str string) string {
	return strings.ToLower(nonAlphanumericRegex.ReplaceAllString(str, ""))
}

func SortProvidersByBestMatch(searchTerm string, providers []coreapi.Provider) []coreapi.Provider {
	// matches anywhere in Provider.Name
	providerNameAnyMatches := filterProviders(searchTerm, providers, NAME, matchProviderAny)
	// matches anywhere in Provider.Address
	providerAddressAnyMatches := filterProviders(searchTerm, providers, ADDRESS, matchProviderAny)
	// matches anywhere in Provider.Name (whitespace stripped)
	providerNameAnyStrippedMatches := filterProviders(
		searchTerm,
		providers,
		NAME,
		matchProviderAnyStripped,
	)
	// matches anywhere in Provider.Address (whitespace stripped)
	providerAddressAnyStrippedMatches := filterProviders(
		searchTerm,
		providers,
		ADDRESS,
		matchProviderAnyStripped,
	)
	// matches the beginning of a word in Provider.Name
	providerNameWordMatches := filterProviders(
		searchTerm,
		providerNameAnyMatches,
		NAME,
		matchProviderWord,
	)
	// matches the beginning of a word in Provider.Address
	providerAddressWordMatches := filterProviders(
		searchTerm,
		providerAddressAnyMatches,
		ADDRESS,
		matchProviderWord,
	)
	// matches the beginning of Provider.Name
	providerNameTitleMatches := filterProviders(
		searchTerm,
		providerNameWordMatches,
		NAME,
		matchProviderTitle,
	)
	// matches the beginning of Provider.Address
	providerAddressTitleMatches := filterProviders(
		searchTerm,
		providerAddressWordMatches,
		ADDRESS,
		matchProviderTitle,
	)

	providerMatches := [][]coreapi.Provider{
		providerNameTitleMatches,
		providerAddressTitleMatches,
		providerNameWordMatches,
		providerAddressWordMatches,
		providerNameAnyMatches,
		providerAddressAnyMatches,
		providerNameAnyStrippedMatches,
		providerAddressAnyStrippedMatches,
		providers,
	}
	// combines filtered matches in prioritized order and removes duplicates
	sortedProviders := combineAndDeduplicateProviders(providerMatches)
	return sortedProviders
}

func combineAndDeduplicateProviders(providerMatches [][]coreapi.Provider) []coreapi.Provider {
	unique := make(map[string]any)
	deduped := []coreapi.Provider{}
	for _, providerMatch := range providerMatches {
		for _, provider := range providerMatch {
			providerKey := strconv.FormatInt(
				provider.OrgId,
				10,
			) + ":" + strconv.FormatInt(
				provider.ProviderId,
				10,
			)
			if _, exists := unique[providerKey]; !exists {
				deduped = append(deduped, provider)
				unique[providerKey] = true
			}
		}
	}
	return deduped
}

// A generic provider fitlering function. Filters providers with searchTerm using providerFilterFunc on providerField.
func filterProviders(
	searchTerm string,
	providers []coreapi.Provider,
	providerField ProviderField,
	providerFilterFunc func(string, string) bool,
) []coreapi.Provider {
	result := []coreapi.Provider{}
	for _, provider := range providers {
		if providerFilterFunc(searchTerm, getProviderFieldValue(providerField, provider)) {
			result = append(result, provider)
		}
	}
	return result
}

func getProviderFieldValue(providerField ProviderField, provider coreapi.Provider) string {
	if providerField == NAME {
		return provider.Name
	}
	return provider.Address
}

// Returns true if providerFieldValue contains searchTerm.
func matchProviderAny(searchTerm string, providerFieldValue string) bool {
	return strings.Contains(sanitizeString(providerFieldValue), sanitizeString(searchTerm))
}

// Returns true if providerFieldValue (whitespace stripped) contains searchTerm (whitespace stripped).
func matchProviderAnyStripped(searchTerm string, providerFieldValue string) bool {
	sanitizedProvider := sanitizeString(providerFieldValue)
	sanitizedSearchTerm := sanitizeString(searchTerm)
	return strings.Contains(
		strings.ReplaceAll(sanitizedProvider, " ", ""),
		strings.ReplaceAll(sanitizedSearchTerm, " ", ""),
	)
}

// Returns true if a word in providerFIeldValue starts with searchTerm.
func matchProviderWord(searchTerm string, providerFieldValue string) bool {
	sanitizedProvider := sanitizeString(providerFieldValue)
	sanitizedSearchTerm := sanitizeString(searchTerm)
	if sanitizedSearchTerm != "" {
		sanitizedSearchTermFirstWord := strings.Fields(sanitizedSearchTerm)[0]
		for _, word := range strings.Fields(sanitizedProvider) {
			if strings.HasPrefix(word, sanitizedSearchTermFirstWord) {
				return true
			}
		}
	}
	return false
}

// Returns true if providerFieldValue starts with searchTerm.
func matchProviderTitle(searchTerm string, providerFieldValue string) bool {
	return strings.HasPrefix(sanitizeString(providerFieldValue), sanitizeString(searchTerm))
}
