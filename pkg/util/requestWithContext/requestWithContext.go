package requestwithcontext

import (
	"context"
	"net/http"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RequestWithCtx func(ctx context.Context, client *http.Client, req *http.Request) (*http.Response, error)

// append correlation ID header to all prov svc requests
func DoRequestWithCtx(
	ctx context.Context,
	client *http.Client,
	req *http.Request,
) (*http.Response, error) {
	correlationId, ok := ctx.Value(logutils.CorrelationIdContextKey).(string)
	if ok && correlationId != "" {
		req.Header.Set("X-Correlation-ID", correlationId)
	} else {
		logutils.DebugCtxLogger(ctx).Error("Missing correlation ID")
	}
	return client.Do(req)
}
