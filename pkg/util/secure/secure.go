package secure

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
)

// GenerateRandomBytes returns securely generated random bytes.
// It will return an error if the system's secure random
// number generator fails to function correctly, in which
// case the caller should not continue.
func GenerateRandomBytes(n int) ([]byte, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	// Note that err == nil only if we read len(b) bytes.
	if err != nil {
		return nil, err
	}
	return b, nil
}

func GenerateRandomDigits(n int) (string, error) {
	b, err := GenerateRandomBytes(n)
	if err != nil {
		return "", err
	}
	var digits string
	digits = ""
	for i := 0; i < n; i++ {
		digits = digits + strconv.Itoa(int(b[i])%10)
	}
	return digits, nil
}

// GenerateRandomReadableCharacters returns a securely
// generated string of length n, of letters and numbers.
// It excludes potentially ambiguous characters for improved
// legibility.
func GenerateRandomReadableCharacters(n int) (string, error) {
	const readableChars = "234679ACDEFGHJKLMNPQRTUVWXYZ"
	bytesrandom, err := GenerateRandomBytes(n)
	if err != nil {
		return "", err
	}
	for i, b := range bytesrandom {
		bytesrandom[i] = readableChars[b%byte(len(readableChars))]
	}
	chars := string(bytesrandom)
	return chars, nil
}

// GenerateRandomString returns a URL-safe, base64 encoded
// securely generated random string.
// It will return an error if the system's secure random
// number generator fails to function correctly, in which
// case the caller should not continue.
func GenerateRandomString(s int) (string, error) {
	b, err := GenerateRandomBytes(s)
	return base64.URLEncoding.EncodeToString(b), err
}

func Decrypt(key []byte, ciphertext []byte) (plaintext []byte, err error) {
	var block cipher.Block
	if block, err = aes.NewCipher(key); err != nil {
		return
	}
	if len(ciphertext) < aes.BlockSize {
		err = fmt.Errorf("ciphertext too short: %q", len(ciphertext))
		return
	}
	iv := ciphertext[:aes.BlockSize]
	plaintext = ciphertext[aes.BlockSize:]
	cfb := cipher.NewCFBDecrypter(block, iv)
	cfb.XORKeyStream(plaintext, plaintext)
	return
}

// PKCS7Pad right-pads the given byte slice using the PKCS5 padding method,
// where we pad with bytes all of the same value as the number of padding bytes
func PKCS7Pad(b []byte, keylen int) ([]byte, error) {
	if len(b) > keylen {
		return nil, errors.New("key to pad is longer than required keylen")
	}
	padding := keylen - len(b)
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(b, padtext...), nil
}
