package rollout

import (
	"context"
	"database/sql"
	"math/rand"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// Assign whether something should be part of a percentage based rollout or not
// and log the results.
// rolloutPct should be a number from 1-100
func AssignRollout(ctx context.Context, rolloutPct int, rolloutName string) bool {
	rand.Seed(time.Now().UnixNano())
	//Intn generates a number from [0, max), so add 1
	rolloutAssignment := rand.Intn(100) + 1 //#nosec G404
	rollout := rolloutAssignment <= rolloutPct
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"rand_assignment": rolloutAssignment,
		"roll_pct":        rolloutPct,
		"rollout":         rollout,
	}).Info(rolloutName)
	return rollout
}

func Rollout(ctx context.Context, db *sql.DB, name string) bool {
	lg := logutils.DebugCtxLogger(ctx).WithField("rollout_name", name)

	var threshold int
	err := db.QueryRowContext(ctx, "SELECT pct FROM rollout WHERE	name=?", name).Scan(&threshold)
	if err != nil {
		lg.WithError(err).Error("error getting rollout")
		return false
	}
	lg.WithField("threshold", threshold).Info("threshold for rollout")

	// probabilistically select true/false by picking random number
	// to compare against configured threshold
	n := rand.Intn(100) //#nosec G404

	return n < threshold
}
