package typeconverter

import "gitlab.com/pockethealth/coreapi/generated/api"

// MapToList converts a map to a list
func MapToList[M ~map[K]V, K comparable, V any](m M) []V {
	list := make([]V, 0, len(m))
	for _, value := range m {
		list = append(list, value)
	}
	return list
}

func ToOptString(s *string) api.OptString {
	if s == nil {
		return api.OptString{}
	}

	return api.NewOptString(*s)
}

func ToOptInt64(i *int64) api.OptInt64 {
	if i == nil {
		return api.OptInt64{}
	}

	return api.NewOptInt64(*i)
}
