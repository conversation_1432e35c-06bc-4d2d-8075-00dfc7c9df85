package physicianaccount

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PublicPhysicianAccountApiController struct {
	service PhysicianAccountApiServicer

	// sets refresh_token cookie domain to the current env's frontend host
	refreshCookieDomain string

	// delay(in ms) added to login requests
	badLoginDelay time.Duration
}

func NewPublicPhysicianAccountApiController(
	s PhysicianAccountApiServicer,
	refreshCookieDomain string,
	badLoginDelayMs int,
) coreapi.Router {
	return &PublicPhysicianAccountApiController{
		service:             s,
		refreshCookieDomain: refreshCookieDomain,
		badLoginDelay:       time.Millisecond * time.Duration(badLoginDelayMs),
	}
}

func (c *PublicPhysicianAccountApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostLogin",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/login",
			HandlerFunc: c.PostLogin,
		},
		{
			Name:        "PostCreateAccount",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostCreateAccount,
		},
		{
			Name:        "PostResetPasswordInit",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/resetpassword/init",
			HandlerFunc: c.PostResetPasswordInit,
		},
		{
			Name:        "PostResetPassword",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/resetpassword",
			HandlerFunc: c.PostResetPassword,
		},
		{
			Name:        "PostVerify",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/verify",
			HandlerFunc: c.PostVerifyAccount,
		},
	}
}

func (c *PublicPhysicianAccountApiController) GetPathPrefix() string {
	return "/v1/physician_accounts"
}

func (c *PublicPhysicianAccountApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *PublicPhysicianAccountApiController) PostLogin(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())

	var loginCredentials coreapi.LoginCredentials
	if err := json.NewDecoder(r.Body).Decode(&loginCredentials); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	email := loginCredentials.Email
	password := loginCredentials.Password

	if email == "" || password == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_USER_CREDENTIALS, http.StatusBadRequest)
		return
	}

	ip := strings.Split(r.RemoteAddr, ":")[0]

	result, refreshToken, err := c.service.PostLogin(r.Context(), email, password, ip)
	if err != nil {
		lg.WithError(err).Error(err.Error())
		accountservice.HandleLoginError(w, r, err, c.badLoginDelay)
		return
	}

	if refreshToken != "" {
		http.SetCookie(
			w,
			&http.Cookie{
				Name:     "refresh_token",
				Value:    refreshToken,
				Path:     "/",
				Domain:   c.refreshCookieDomain,
				Secure:   true,
				HttpOnly: true,
				SameSite: http.SameSiteStrictMode,
			},
		)
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PublicPhysicianAccountApiController) PostCreateAccount(
	w http.ResponseWriter,
	r *http.Request,
) {
	rd := coreapi.RegisterData{}
	if err := json.NewDecoder(r.Body).Decode(&rd); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	// check if email / password are missing
	if err := rd.Valid(); err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			fmt.Sprintf("%s: %s", errmsg.ERR_INVALID_REQ_BODY, err.Error()),
			http.StatusBadRequest,
		)
		return
	}

	// for auto-linking a share on account creation
	if rd.ShareId != "" {
		// they should have a share token after viewcode + dob validation
		token := r.Header.Get("Authorization")
		tokenShareId, _, err := auth.DecodeShareViewerToken(token)
		if err != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}
		// Token and shareID should be matched
		if tokenShareId != rd.ShareId {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}
	}

	acctId, err := c.service.PostCreateAccount(r.Context(), rd)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), acctId, nil, w)
}

// PostResetPasswordInit - Send password reset email
func (c *PublicPhysicianAccountApiController) PostResetPasswordInit(
	w http.ResponseWriter,
	r *http.Request,
) {
	lg := logutils.DebugCtxLogger(r.Context())
	var email string

	if err := json.NewDecoder(r.Body).Decode(&email); err != nil || email == "" {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err := c.service.PostResetPasswordInit(r.Context(), email)
	if err != nil {
		lg.WithError(err).Error("failed to initialize reset password using acctsvc")
	}

	w.WriteHeader(http.StatusOK)
}

// PostResetPassword - Reset password
func (c *PublicPhysicianAccountApiController) PostResetPassword(
	w http.ResponseWriter,
	r *http.Request,
) {
	resetInfo := coreapi.PasswordResetInfo{}
	if err := json.NewDecoder(r.Body).Decode(&resetInfo); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	err := c.service.PostResetPassword(r.Context(), resetInfo)
	if err != nil {
		// if it's an error with the client given password we can let the client know
		var badRequest accountservice.ErrBadRequestAccountEdit
		var badPw accountservice.ErrBadNewPassword
		if errors.As(err, &badRequest) || errors.As(err, &badPw) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know too many details
		// and return a generic 500 instead
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// PostVerifyAccount - verifies physician account
func (c *PublicPhysicianAccountApiController) PostVerifyAccount(
	w http.ResponseWriter,
	r *http.Request,
) {
	verification := accountservice.Verification{}
	if err := json.NewDecoder(r.Body).Decode(&verification); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if verification.Token == "" {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	verification.IP = strings.Split(r.RemoteAddr, ":")[0]

	err := c.service.PostVerifyAccount(r.Context(), verification)
	if err != nil {
		// if it's an error with the client given password we can let the client know
		var epr accountservice.ErrPasswordRequirement
		var eve accountservice.ErrVerifyEmail
		if errors.As(err, &epr) || errors.As(err, &eve) {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
			return
		}

		// if it's another err with acctsvc we do NOT want the client to know too many details
		// and return a generic 500 instead
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
	}

	w.WriteHeader(http.StatusOK)
}
