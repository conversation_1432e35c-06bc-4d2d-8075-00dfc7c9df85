//go:build integration
// +build integration

package physicianaccount_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/generated/api"
	mockaudit "gitlab.com/pockethealth/coreapi/generated/mocks/audit"
	mocktopic "gitlab.com/pockethealth/coreapi/generated/mocks/pubsub"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	mockrecordstreaming "gitlab.com/pockethealth/coreapi/generated/mocks/recordstreaming"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	auditmodels "gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/models/modelphysicianaccount"
	examStore "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	objectStore "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	studyPermissionsStore "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	recordHandlerStore "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	scansStore "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	shareObjectsStore "gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	sharesStore "gitlab.com/pockethealth/coreapi/pkg/mysql/shares"
	"gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/exams"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/phutils/v10/pkg/audit"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

const (
	REQUEST_RECORD_PATH       = "/v1/physician_accounts/shares/request"
	GET_UPLOADED_STUDIES_PATH = "/v1/physician_accounts/studies"
)

func TestRequestRecordsAPI(t *testing.T) {
	db := testutils.SetupTestDB(t)

	patientID := phtestutils.GenerateRandomString(t, 10)
	studyUID := phtestutils.GenerateRandomString(t, 10)
	var providerID int64 = phtestutils.GenerateRandomInt64(t)

	studyRequest := physicianaccount.StudyRequest{
		StudyUID:         studyUID,
		PatientId:        patientID,
		Issuer:           phtestutils.GenerateRandomString(t, 10),
		PatientFirstName: phtestutils.GenerateRandomString(t, 10),
		PatientLastName:  phtestutils.GenerateRandomString(t, 10),
		PatientBirthDate: "********",
	}

	recordRequest := physicianaccount.PhysicianRecordRequest{
		ProviderId: providerID,
		Requests:   []physicianaccount.StudyRequest{studyRequest},
	}

	t.Run("should succeed to enqueue to azure and create study permission", func(t *testing.T) {
		// 1. Setup test data and cleanup func
		physicianAccountController, mockTopicSender, mockAudit, _ := setupTestController(
			t,
			db,
			providerID,
			true,
		)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		for i := range recordRequest.Requests {
			mockTopicSender.EXPECT().
				SendMessage(mock.Anything, mock.Anything, mock.Anything).
				Return(nil)
			mockAudit.EXPECT().CreatePhysicianStudyRetrieveEvent(
				mock.Anything,
				auditmodels.EventDataPhysicianStudyRetrieve{
					EventDataPhysicianStudyBase: auditmodels.EventDataPhysicianStudyBase{
						EventDataPhysicianBase: auditmodels.EventDataPhysicianBase{
							ProviderID: providerID,
							UserID:     physicianAccountID,
							UserType:   auditmodels.UserTypePhysician,
						},
						EventDataPatientBase: auditmodels.EventDataPatientBase{
							PatientFirstName: recordRequest.Requests[i].PatientFirstName,
							PatientLastName:  recordRequest.Requests[i].PatientLastName,
							PatientBirthDate: recordRequest.Requests[i].PatientBirthDate,
						},
						StudyUID: studyUID,
					},
				},
			).Return(nil)
		}
		defer t.Cleanup(func() {
			_, err := db.Exec(
				"DELETE FROM physician_study_permissions WHERE physician_account_id=?",
				physicianAccountID,
			)
			assert.NoError(t, err, fmt.Sprintf("failed to cleanup test data: %v", err))
		})
		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodPost,
			REQUEST_RECORD_PATH,
			recordRequest,
		)
		status, responseBody := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.PostShareRequest,
		)

		// 3. Verify status OK
		if status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}

		// 4. Verify study permission created
		hasAccess := verifyPhysicianPermission(
			t,
			db,
			physicianAccountID,
			studyUID,
			providerID,
		)
		assert.Equal(t, true, hasAccess)

		var response []modelphysicianaccount.StudyUploadResponse
		err := json.Unmarshal(responseBody, &response)
		assert.NoError(t, err, fmt.Sprintf("error during response body unmarshal: %v", err))
		assert.Len(t, response, 1)
		assert.Equal(t, studyUID, response[0].StudyUID)
		assert.Equal(t, true, response[0].Success)
		assert.Equal(t, false, response[0].AlreadyUploaded)
	})

	t.Run(
		"should associate study to physician and not reupload study if already exists",
		func(t *testing.T) {
			// setup test data and cleanup
			physicianAccountController, _, mockAudit, _ := setupTestController(
				t,
				db,
				providerID,
				true,
			)
			physicianAccountID := phtestutils.GenerateRandomString(t, 10)
			examUUID := phtestutils.GenerateRandomString(t, 10)
			studyPermissionsStore.InsertStudyIndex(
				t,
				db,
				studyUID,
				providerID,
				examUUID,
			)
			studyPermissionsStore.InsertReportIndex(
				t,
				db,
				studyUID,
				providerID,
				phtestutils.GenerateRandomString(t, 10), // objectId
			)
			for i := range recordRequest.Requests {
				mockAudit.EXPECT().CreatePhysicianStudyRetrieveEvent(
					mock.Anything,
					auditmodels.EventDataPhysicianStudyRetrieve{
						EventDataPhysicianStudyBase: auditmodels.EventDataPhysicianStudyBase{
							EventDataPhysicianBase: auditmodels.EventDataPhysicianBase{
								ProviderID: providerID,
								UserID:     physicianAccountID,
								UserType:   auditmodels.UserTypePhysician,
							},
							EventDataPatientBase: auditmodels.EventDataPatientBase{
								PatientFirstName: recordRequest.Requests[i].PatientFirstName,
								PatientLastName:  recordRequest.Requests[i].PatientLastName,
								PatientBirthDate: recordRequest.Requests[i].PatientBirthDate,
							},
							StudyUID: recordRequest.Requests[i].StudyUID,
						},
					},
				).Return(nil)
			}
			defer t.Cleanup(func() {
				_, err := db.Exec(
					"DELETE FROM physician_study_permissions WHERE physician_account_id=?",
					physicianAccountID,
				)
				studyPermissionsStore.DeleteStudyIndex(
					t,
					db,
					studyUID,
					providerID,
					examUUID,
				)
				studyPermissionsStore.DeleteReportIndex(
					t,
					db,
					studyUID,
					providerID,
				)
				assert.NoError(t, err, fmt.Sprintf("failed to cleanup test data: %v", err))
			})

			// send request
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodPost,
				REQUEST_RECORD_PATH,
				recordRequest,
			)
			status, responseBody := testutils.MakeRequestToHandler(
				t,
				request,
				physicianAccountController.PostShareRequest,
			)

			// verify response status
			if status != http.StatusOK {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusOK)
			}

			// verify study permission created
			hasAccess := verifyPhysicianPermission(
				t,
				db,
				physicianAccountID,
				studyUID,
				providerID,
			)
			assert.Equal(t, true, hasAccess)

			var response []modelphysicianaccount.StudyUploadResponse
			err := json.Unmarshal(responseBody, &response)
			assert.NoError(t, err, fmt.Sprintf("error during response body unmarshal: %v", err))
			assert.Len(t, response, 1)
			assert.Equal(t, studyUID, response[0].StudyUID)
			assert.Equal(t, true, response[0].Success)
			assert.Equal(t, true, response[0].AlreadyUploaded)
			assert.Equal(t, true, response[0].HasReport)
		},
	)

	t.Run("should fail due to missing permissions", func(t *testing.T) {
		// 1. Setup test data
		physicianAccountController, _, _, _ := setupTestController(t, db, providerID, false)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)

		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			"POST",
			REQUEST_RECORD_PATH,
			recordRequest,
		)
		status, _ := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.PostShareRequest,
		)

		// 3. Verify status Unauthorized due to missing permission
		if status != http.StatusUnauthorized {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, http.StatusUnauthorized)
		}
	})
}

func TestGetRecordStreamingStudiesWithUploadStatus(t *testing.T) {
	db := testutils.SetupTestDB(t)
	providerID := phtestutils.GenerateRandomInt64(t)
	physicianAccountID := "id"

	t.Run(
		"should forward response returned from record service",
		func(t *testing.T) {
			// setup test data and cleanup
			physicianAccountController, _, _, recordService := setupTestController(
				t,
				db,
				providerID,
				true,
			)

			// send request
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodGet,
				GET_UPLOADED_STUDIES_PATH,
				"",
			)
			// set up record service mock
			recordService.EXPECT().GetV1StudiesUploadStatus(
				request.Context(),
				generatedrecordservice.GetV1StudiesUploadStatusParams{
					AccountID: physicianAccountID,
				}).Return(&generatedrecordservice.GetV1StudiesUploadStatusOKApplicationJSON{}, nil)
			status, responseBody := testutils.MakeRequestToHandler(
				t,
				request,
				physicianAccountController.GetRecordStreamingStudiesWithUploadStatus,
			)

			// verify response status
			if status != http.StatusOK {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, http.StatusOK)
			}

			// verify response body
			var result []api.PhysicianRecordUploadStatus
			err := json.Unmarshal(responseBody, &result)
			assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))
			assert.Len(t, result, 0)
		},
	)
}

func TestGetPatientSharedExams(t *testing.T) {
	db := testutils.SetupTestDB(t)
	var providerID int64 = phtestutils.GenerateRandomInt64(t)

	t.Run("returns error if physician account does not have handler id", func(t *testing.T) {
		// 1. Setup test data and cleanup func
		physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		patientID := phtestutils.GenerateRandomString(t, 10)

		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
			"",
		)
		request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
		status, responseBody := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.GetPatientSharedExams,
		)
		// 3. Verify result
		assert.Equal(t, status, http.StatusInternalServerError)
		assert.Equal(t, "unexpected error\n", string(responseBody))
	})
	t.Run("returns exams and health record data for physician", func(t *testing.T) {
		// 1. Setup test data and cleanup func - physician has one non-expired exam
		physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
		physicianAccountID, shareID, examUUID, patientID := setupPatientSharedExams(t, db)

		// expiry timestamp one day in the future - share is valid for one more day
		expiryTime, _ := datetime.GenerateExpiry(1)
		// add view_shares with expiry in past
		setupViewShare(t, db, shareID, expiryTime)

		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
			"",
		)
		request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
		status, responseBody := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.GetPatientSharedExams,
		)
		// 3. Verify result
		assert.Equal(t, status, http.StatusOK)
		var result coreapi.PatientShareInfo
		err := json.Unmarshal(responseBody, &result)
		assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))

		if assert.Len(t, result.Exams, 1) &&
			assert.Len(t, result.ExamShareInfoMap, 1) {
			// result should have right exam uuid and share id
			assert.Equal(t, examUUID, result.Exams[0].UUID)
			assert.Equal(t, shareID, result.ExamShareInfoMap[examUUID].ShareId)
			// exam share info should contain an eUnity token for an expired exam
			assert.NotEqual(t, "", result.ExamShareInfoMap[examUUID].EUnityToken)
			// exam share info should contain expiry
			compareExpiryTimestamps(t, expiryTime, result.ExamShareInfoMap[examUUID].Expiry)
		}
	})
	t.Run("does not add eUnity tokens for expired exams", func(t *testing.T) {
		// 1. Setup test data and cleanup func
		physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
		physicianAccountID, shareID, examUUID, patientID := setupPatientSharedExams(t, db)

		// expiry timestamp one day in the past
		expiryTime, _ := datetime.GenerateExpiry(-1)
		// add view_shares with expiry in past
		setupViewShare(t, db, shareID, expiryTime)

		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
			"",
		)
		request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
		status, responseBody := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.GetPatientSharedExams,
		)
		// 3. Verify result
		assert.Equal(t, status, http.StatusOK)
		var result coreapi.PatientShareInfo
		err := json.Unmarshal(responseBody, &result)
		assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))

		if assert.Len(t, result.Exams, 1) &&
			assert.Len(t, result.ExamShareInfoMap, 1) {
			// exam share info should contain an eUnity token for an expired exam
			compareExpiryTimestamps(t, expiryTime, result.ExamShareInfoMap[examUUID].Expiry)
			// exam share info should not contain an eUnity token for an expired exam
			assert.Equal(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].EUnityToken)
		}
	})
	t.Run(
		"includes eUnity tokens for expired exams with non-expired extension",
		func(t *testing.T) {
			// 1. Setup test data and cleanup func
			physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
			physicianAccountID, shareID, examUUID, patientID := setupPatientSharedExams(t, db)

			// expiry timestamp one day in the past
			expiryTime, _ := datetime.GenerateExpiry(-1)
			// add view_shares with expiry in past
			setupViewShare(t, db, shareID, expiryTime)
			// extend exam
			sharesStore.ExtendViewShare(context.Background(), db, shareID)

			// 2. Make the request
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodGet,
				fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
				"",
			)
			request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
			status, responseBody := testutils.MakeRequestToHandler(
				t,
				request,
				physicianAccountController.GetPatientSharedExams,
			)
			// 3. Verify result
			assert.Equal(t, status, http.StatusOK)
			var result coreapi.PatientShareInfo
			err := json.Unmarshal(responseBody, &result)
			assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))

			if assert.Len(t, result.Exams, 1) &&
				assert.Len(t, result.ExamShareInfoMap, 1) {
				// exam share info should contain an eUnity token for an expired exam
				compareExpiryTimestamps(t, expiryTime, result.ExamShareInfoMap[examUUID].Expiry)
				// exam share info should not contain an eUnity token for an expired exam
				assert.NotEqual(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].EUnityToken)
			}
		},
	)
	t.Run(
		"does not include eUnity tokens for expired exams with expired extension",
		func(t *testing.T) {
			// 1. Setup test data and cleanup func
			physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
			physicianAccountID, shareID, examUUID, patientID := setupPatientSharedExams(t, db)

			// extension will be for 9 months - this will result in an expired extension
			expiryTime, _ := datetime.GenerateExpiry(-280)
			// add view_shares with expiry in past
			setupViewShare(t, db, shareID, expiryTime)
			sharesStore.ExtendViewShare(context.Background(), db, shareID) // extend exam

			// 2. Make the request
			request := testutils.CreateAuthorizedRequest(
				t,
				physicianAccountID,
				http.MethodGet,
				fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
				"",
			)
			request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
			status, responseBody := testutils.MakeRequestToHandler(
				t,
				request,
				physicianAccountController.GetPatientSharedExams,
			)
			// 3. Verify result
			assert.Equal(t, status, http.StatusOK)
			var result coreapi.PatientShareInfo
			err := json.Unmarshal(responseBody, &result)
			assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))

			if assert.Len(t, result.Exams, 1) &&
				assert.Len(t, result.ExamShareInfoMap, 1) {
				// exam share info should contain an eUnity token for an expired exam
				compareExpiryTimestamps(t, expiryTime, result.ExamShareInfoMap[examUUID].Expiry)
				// exam share info should not contain an eUnity token for an expired exam
				assert.Equal(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].EUnityToken)
			}
		},
	)
	t.Run("includes eUnity tokens for exams without expiry (unlimited access)", func(t *testing.T) {
		// 1. Setup test data and cleanup func
		physicianAccountController, _, _, _ := setupTestController(t, db, providerID, true)
		physicianAccountID, shareID, _, patientID := setupPatientSharedExams(t, db)

		// 2. Make the request
		request := testutils.CreateAuthorizedRequest(
			t,
			physicianAccountID,
			http.MethodGet,
			fmt.Sprintf("/v1/physician_accounts/patients/%s", patientID),
			"",
		)
		request = mux.SetURLVars(request, map[string]string{"patientId": patientID})
		status, responseBody := testutils.MakeRequestToHandler(
			t,
			request,
			physicianAccountController.GetPatientSharedExams,
		)
		// 3. Verify result
		assert.Equal(t, status, http.StatusOK)
		var result coreapi.PatientShareInfo
		err := json.Unmarshal(responseBody, &result)
		assert.NoError(t, err, fmt.Sprintf("failed to unmarshal response body: %v", err))

		if assert.Len(t, result.Exams, 1) &&
			assert.Len(t, result.ExamShareInfoMap, 1) {
			assert.Equal(t, shareID, result.ExamShareInfoMap[result.Exams[0].UUID].ShareId)
			assert.NotEqual(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].EUnityToken)
			assert.Equal(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].Expiry)
			assert.Equal(t, "", result.ExamShareInfoMap[result.Exams[0].UUID].ExtendedExpiry)
		}
	})
}

func TestPostStudyAccessVerification(t *testing.T) {
	db := testutils.SetupTestDB(t)
	physicianAccountID, shareID, uuid, _ := setupPatientSharedExams(t, db)
	request := api.PostPhysicianAccountStudyAccessReq{
		StudyUID:   phtestutils.GenerateRandomString(t, 10),
		ProviderID: phtestutils.GenerateRandomInt64(t),
		ShareID:    api.NewOptString(shareID),
	}
	study := generatedrecordservice.PhysicianPatientStudy{
		UUID: generatedrecordservice.NewOptString(uuid),
		DicomStudyTags: generatedrecordservice.NewOptDicomStudyTags(
			generatedrecordservice.DicomStudyTags{
				StudyInstanceUID: generatedrecordservice.NewOptString(request.StudyUID),
			},
		),
		DicomPatientTags: generatedrecordservice.NewOptDicomPatientTags(
			generatedrecordservice.DicomPatientTags{
				PatientName: generatedrecordservice.NewOptString(
					"Dummy",
				), // will be parsed as just last name by dcmtools
				PatientBirthDate: generatedrecordservice.NewOptString(
					phtestutils.GenerateRandomString(t, 10),
				),
			},
		),
	}

	testCases := map[string]struct {
		setup              func(t *testing.T, db *sql.DB, auditService *mockaudit.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface, request api.PostPhysicianAccountStudyAccessReq)
		requestBody        api.PostPhysicianAccountStudyAccessReq
		authorized         bool
		expectedStatusCode int
	}{
		"returns StatusOK for valid record streaming access": {
			setup: func(t *testing.T, db *sql.DB, auditService *mockaudit.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface, request api.PostPhysicianAccountStudyAccessReq) {
				studyPermissionsStore.InsertStudyIndex(
					t,
					db,
					request.StudyUID,
					request.ProviderID,
					uuid,
				)
				t.Cleanup(func() {
					studyPermissionsStore.DeleteStudyIndex(
						t,
						db,
						request.StudyUID,
						request.ProviderID,
						uuid,
					)
				})
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: physicianAccountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{study}, nil)
				auditService.EXPECT().
					CreatePhysicianStudyViewEvent(mock.Anything, auditmodels.EventDataPhysicianStudyView{
						EventDataPhysicianStudyBase: auditmodels.EventDataPhysicianStudyBase{
							EventDataPhysicianBase: auditmodels.EventDataPhysicianBase{
								ProviderID: request.ProviderID,
								UserID:     physicianAccountID,
								UserType:   auditmodels.UserTypePhysician,
							},
							EventDataPatientBase: auditmodels.EventDataPatientBase{
								PatientFirstName: "",
								PatientLastName:  study.DicomPatientTags.Value.PatientName.Value,
								PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
							},
							StudyUID: request.StudyUID,
						},
					}).
					Return(nil)
			},
			requestBody:        request,
			authorized:         true,
			expectedStatusCode: http.StatusOK,
		},
		"returns StatusOK for valid share access": {
			// request body matches created share, but not created record streaming permission
			requestBody: api.PostPhysicianAccountStudyAccessReq{
				StudyUID:   phtestutils.GenerateRandomString(t, 10),
				ProviderID: phtestutils.GenerateRandomInt64(t),
				ShareID:    api.NewOptString(shareID),
			},
			authorized:         true,
			expectedStatusCode: http.StatusOK,
		},
		"returns StatusUnauthorized for missing account data in context": {
			requestBody:        request,
			authorized:         false,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"returns StatusUnauthorized for no record streaming or share access": {
			requestBody: api.PostPhysicianAccountStudyAccessReq{
				StudyUID:   phtestutils.GenerateRandomString(t, 10),
				ProviderID: phtestutils.GenerateRandomInt64(t),
				ShareID:    api.NewOptString(phtestutils.GenerateRandomString(t, 10)),
			},
			authorized:         true,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"returns StatusInternalServerError for failed audit logging": {
			setup: func(t *testing.T, db *sql.DB, auditService *mockaudit.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface, request api.PostPhysicianAccountStudyAccessReq) {
				studyPermissionsStore.InsertStudyIndex(
					t,
					db,
					request.StudyUID,
					request.ProviderID,
					uuid,
				)
				t.Cleanup(func() {
					studyPermissionsStore.DeleteStudyIndex(
						t,
						db,
						request.StudyUID,
						request.ProviderID,
						uuid,
					)
				})
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: physicianAccountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{study}, nil)
				auditService.EXPECT().
					CreatePhysicianStudyViewEvent(mock.Anything, auditmodels.EventDataPhysicianStudyView{
						EventDataPhysicianStudyBase: auditmodels.EventDataPhysicianStudyBase{
							EventDataPhysicianBase: auditmodels.EventDataPhysicianBase{
								ProviderID: request.ProviderID,
								UserID:     physicianAccountID,
								UserType:   auditmodels.UserTypePhysician,
							},
							EventDataPatientBase: auditmodels.EventDataPatientBase{
								PatientFirstName: "",
								PatientLastName:  study.DicomPatientTags.Value.PatientName.Value,
								PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
							},
							StudyUID: request.StudyUID,
						},
					}).
					Return(errors.New("dummy"))
			},
			requestBody:        request,
			authorized:         true,
			expectedStatusCode: http.StatusInternalServerError,
		},
	}
	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				// 1. Setup test data and cleanup
				physicianAccountController, _, mockAudit, recordService := setupTestController(
					t,
					db,
					request.ProviderID,
					true,
				)
				if tc.setup != nil {
					tc.setup(t, db, mockAudit, recordService, tc.requestBody)
				}
				// 2. Make the request
				request := testutils.CreateAuthorizedRequest(
					t,
					physicianAccountID,
					http.MethodPost,
					"/v1/physician_accounts/studies/verify-access",
					tc.requestBody,
				)
				if tc.authorized {
					// add auth to context
					ctx := auth.WithAccountID(request.Context(), physicianAccountID)
					ctx = auth.WithAccountType(ctx, string(models.ACCOUNT_TYPE_PHYSICIAN))
					request = request.WithContext(ctx)
				}
				status, _ := testutils.MakeRequestToHandler(
					t,
					request,
					physicianAccountController.PostStudyAccessVerification,
				)
				// 3. Verify result
				assert.Equal(t, tc.expectedStatusCode, status)
			},
		)
	}
}

func setupTestController(
	t *testing.T,
	db *sql.DB,
	providerID int64,
	hasPermissions bool,
) (*physicianaccount.PrivatePhysicianAccountApiController, *mocktopic.MockTopicSender, *mockaudit.MockService, *mockrecordservice.MockRecordServiceClientInterface) {
	var permissions map[int64][]accountservice.PhysicianAccountPermission = map[int64][]accountservice.PhysicianAccountPermission{}
	if hasPermissions {
		permissions[providerID] = []accountservice.PhysicianAccountPermission{}
	}

	accountServiceMock := &accountservice.AcctSvcMock{
		GetPhysicianPermissionsByProviderIdAndPermissionNameReturn: permissions,
	}

	topicSender := mocktopic.NewMockTopicSender(t)
	auditService := mockaudit.NewMockService(t)
	recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
	service := physicianaccount.NewPhysicianAccountApiService(
		db,
		accountServiceMock,
		fax.FaxSvcUser{},
		hrs.HlthRecSvcUser{},
		&orgs.OrgServiceMock{}, // using mock because we don't want to query other service
		recordService,
		auditService,
		&roiservice.RoiServiceMock{},
		topicSender,
		topicSender,
		"CA",
		false,
	)
	physicianAccountController := physicianaccount.NewPrivatePhysicianAccountApiController(
		service,
		accountServiceMock,
		mockrecordstreaming.NewMockRecordStreamingServicer(t),
		&audit.AuditLogServiceMock{},
	)
	return physicianAccountController, topicSender, auditService, recordService
}

func setupViewShare(t *testing.T, db *sql.DB, shareID string, expiry string) {
	err := sharesStore.InsertViewShare(context.Background(), db, "", shareID, "", expiry, "")
	defer t.Cleanup(func() {
		_, err := db.Exec("DELETE FROM view_shares WHERE share_id=?", shareID)
		assert.NoError(t, err)
	})
	assert.NoError(t, err)
}

func compareExpiryTimestamps(t *testing.T, expected string, actual string) {
	actualExpiry, _ := time.Parse(time.RFC3339, actual)
	expectedExpiry, _ := time.Parse("2006-01-02 15:04", expected)
	assert.Equal(t, expectedExpiry, actualExpiry)
}

func setupPatientSharedExams(t *testing.T, db *sql.DB) (string, string, string, string) {
	exam := examStore.GenerateTestExam(t, phtestutils.GenerateRandomString(t, 10), "2000/01/01")
	physicianAccountID := phtestutils.GenerateRandomString(t, 10)
	handlerID := phtestutils.GenerateRandomString(t, 10)
	shareID := phtestutils.GenerateRandomString(t, 10)
	objectID := phtestutils.GenerateRandomString(t, 10)

	defer t.Cleanup(func() {
		objectStore.DeleteObjectMapping(t, db, exam.UUID, objectID)
		shareObjectsStore.DeleteShareObject(t, db, shareID, objectID)
		recordHandlerStore.DeleteRecordHandlersShareMap(t, db, handlerID, shareID)
		scansStore.DeleteScan(t, db, exam.TransferId)
		exams.DeleteExam(db, exam.UUID)
		sharesStore.DeleteShare(t, db, exam.PatientId, exam.PatientId, shareID)
		recordHandlerStore.DeleteRecordHandler(
			t,
			db,
			handlerID,
			coreapi.PhysicianAccount,
			physicianAccountID,
		)
	})

	recordHandlerStore.InsertRecordHandler(
		t,
		db,
		handlerID,
		coreapi.PhysicianAccount,
		physicianAccountID,
	)
	sharesStore.InsertTestShare(t, db, exam.PatientId, exam.PatientId, shareID)
	exams.InsertTestExam(t, db, exam, false)
	scansStore.InsertScan(t, db, exam.TransferId, "PS")
	recordHandlerStore.InsertRecordHandlersShareMap(t, db, handlerID, shareID)
	shareObjectsStore.InsertShareObject(t, db, shareID, objectID, false)
	objectStore.InsertObjectMapping(t, db, exam.UUID, objectID, 0)
	return physicianAccountID, shareID, exam.UUID, exam.PatientId
}

func getPhysicianStudies(t *testing.T) []api.PhysicianRecordUploadStatus {
	return []api.PhysicianRecordUploadStatus{
		{
			StudyUID:        phtestutils.GenerateRandomString(t, 10),
			ProviderID:      phtestutils.GenerateRandomInt64(t),
			UUID:            phtestutils.GenerateRandomString(t, 10),
			HasReport:       true,
			ProgressPercent: phtestutils.GenerateRandomIntInRange(t, 0, 100),
		},
		{
			StudyUID:        phtestutils.GenerateRandomString(t, 10),
			ProviderID:      phtestutils.GenerateRandomInt64(t),
			UUID:            phtestutils.GenerateRandomString(t, 10),
			HasReport:       false,
			ProgressPercent: phtestutils.GenerateRandomIntInRange(t, 0, 100),
		},
	}
}

func verifyPhysicianPermission(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) bool {
	var count int
	err := db.QueryRow(
		`SELECT COUNT(*)
		FROM physician_study_permissions
		WHERE physician_account_id=? AND study_uid=? AND provider_id=?`,
		physicianAccountID,
		studyUID,
		providerID,
	).Scan(&count)
	require.NoError(t, err)
	return count == 1
}
