package physicianaccount

import (
	"net/http"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

func handleError(w *http.ResponseWriter, r *http.Request, err error) {

	_, isBadPasswordErr := err.(accountservice.ErrBadNewPassword)
	if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
		httperror.ErrorWithLog(*w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
	} else if err.Error() == errormsgs.ERR_NOT_FOUND {
		httperror.ErrorWithLog(*w, r, errormsgs.ERR_NOT_FOUND, http.StatusNotFound)
	} else if strings.HasPrefix(err.<PERSON>rror(), errormsgs.ERR_BAD_QUERY_PARAM) {
		httperror.ErrorWithLog(*w, r, errormsgs.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
	} else if isBadPasswordErr ||
		strings.HasPrefix(err.Error(), errormsgs.ERR_INVALID_REQ_BODY) {
		httperror.ErrorWithLog(*w, r, err.Error(), http.StatusNotAcceptable)
	} else {
		httperror.ErrorWithLog(*w, r, "unexpected error", http.StatusInternalServerError)
	}
}
