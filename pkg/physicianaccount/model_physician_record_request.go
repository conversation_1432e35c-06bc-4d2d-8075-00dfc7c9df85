package physicianaccount

import "errors"

type PhysicianRecordRequest struct {
	ProviderId int64          `json:"providerId"`
	Requests   []StudyRequest `json:"studyRequests"`
}

func (r *PhysicianRecordRequest) Validate() error {
	if r.ProviderId == 0 {
		return errors.New("missing required provider id")
	}
	if len(r.Requests) <= 0 {
		return errors.New("need at least one study request")
	}
	for _, v := range r.Requests {
		if err := v.Validate(); err != nil {
			return err
		}
	}
	return nil
}

type StudyRequest struct {
	StudyUID         string `json:"studyUID"`         // DICOM UID of study that is being requestd
	PatientId        string `json:"patientId"`        // MRN of patient associated with study
	Issuer           string `json:"issuer"`           // issuer of patient MRN
	PatientFirstName string `json:"patientFirstName"` // first name of patient
	PatientLastName  string `json:"patientLastName"`  // last name of patient
	PatientBirthDate string `json:"patientBirthDate"` // date of birth of patient
}

func (r *StudyRequest) Validate() error {
	if r.StudyUID == "" {
		return errors.New("missing required study uid")
	}
	if r.PatientId == "" {
		return errors.New("missing required patient id")
	}
	return nil
}
