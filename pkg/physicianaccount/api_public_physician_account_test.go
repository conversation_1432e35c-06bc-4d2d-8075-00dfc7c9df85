package physicianaccount_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockphysicianaccount "gitlab.com/pockethealth/coreapi/generated/mocks/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
)

func TestPostCreateAccount(t *testing.T) {
	t.Run("valid request", func(t *testing.T) {
		service := mockphysicianaccount.NewMockPhysicianAccountApiServicer(t)
		service.EXPECT().PostCreateAccount(mock.Anything, mock.Anything).Return("", nil)

		controller := physicianaccount.NewPublicPhysicianAccountApiController(
			service,
			"testhost",
			0,
		)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		createAccountRequest := &coreapi.RegisterData{
			Email:     "test",
			Password:  "testpw",
			FirstName: "t1",
			LastName:  "t2",
		}
		body, _ := json.Marshal(createAccountRequest)

		req, err := http.NewRequest(
			"POST",
			"/v1/physician_accounts",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("missing email password request", func(t *testing.T) {
		service := mockphysicianaccount.NewMockPhysicianAccountApiServicer(t)
		controller := physicianaccount.NewPublicPhysicianAccountApiController(
			service,
			"testhost",
			0,
		)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		noPwReq := &coreapi.RegisterData{
			Email:     "test",
			FirstName: "t1",
			LastName:  "t2",
		}
		body, _ := json.Marshal(noPwReq)

		req, err := http.NewRequest(
			"POST",
			"/v1/physician_accounts",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

		noEmailReq := &coreapi.RegisterData{
			Password:  "testpw",
			FirstName: "t1",
			LastName:  "t2",
		}
		body, _ = json.Marshal(noEmailReq)

		req, err = http.NewRequest(
			"POST",
			"/v1/physician_accounts",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr = httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want = http.StatusBadRequest
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("bad password", func(t *testing.T) {
		service := mockphysicianaccount.NewMockPhysicianAccountApiServicer(t)
		service.EXPECT().
			PostCreateAccount(mock.Anything, mock.Anything).
			Return("", accountservice.ErrBadNewPassword{MissingRequirement: "breached"})
		controller := physicianaccount.NewPublicPhysicianAccountApiController(
			service,
			"testhost",
			0,
		)
		router, err := coreapi.NewRouter(controller)
		require.NoError(t, err)

		badPasswordRequest := &coreapi.RegisterData{
			Email:     "test",
			FirstName: "t1",
			LastName:  "t2",
			Password:  "password",
		}
		body, _ := json.Marshal(badPasswordRequest)

		req, err := http.NewRequest(
			"POST",
			"/v1/physician_accounts",
			bytes.NewReader(body),
		)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusNotAcceptable
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}

	})
}
