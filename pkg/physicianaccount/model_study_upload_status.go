package physicianaccount

import (
	"gitlab.com/pockethealth/coreapi/generated/api"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
)

type StudyUploadStatus generatedrecordservice.StudyUploadStatus

func (s StudyUploadStatus) ToAPIModel() api.PhysicianRecordUploadStatus {
	return api.PhysicianRecordUploadStatus{
		StudyUID:        s.StudyIdentifier.StudyUID,
		ProviderID:      s.StudyIdentifier.ProviderID,
		UUID:            s.UUID,
		HasReport:       s.HasReport,
		ProgressPercent: s.ProgressPercent,
	}
}

type StudyUploadStatuses []generatedrecordservice.StudyUploadStatus

func (s StudyUploadStatuses) ToAPIModel() []api.PhysicianRecordUploadStatus {
	result := []api.PhysicianRecordUploadStatus{}
	for _, studyUploadStatus := range s {
		result = append(result, StudyUploadStatus(studyUploadStatus).ToAPIModel())
	}
	return result
}
