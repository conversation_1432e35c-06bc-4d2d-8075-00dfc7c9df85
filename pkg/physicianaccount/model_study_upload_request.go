package physicianaccount

import "strconv"

type StudyUploadRequest struct {
	ProviderId int64  `json:"providerId"`
	StudyUID   string `json:"studyUID"`
	PatientId  string `json:"patientID"`
}

func (s *StudyUploadRequest) IsDefault() bool {
	return *s == StudyUploadRequest{}
}

func (s *StudyUploadRequest) ApplicationProperties() map[string]any {
	return map[string]any{
		"providerId": strconv.Itoa(int(s.ProviderId)),
	}
}

func (s *StudyUploadRequest) IsValid() bool {
	if s.ProviderId == 0 ||
		s.StudyUID == "" {
		return false
	}
	return true
}

func NewStudyUploadRequest(
	providerId int64,
	studyUID string,
	patientId string,
) *StudyUploadRequest {
	return &StudyUploadRequest{
		ProviderId: providerId,
		StudyUID:   studyUID,
		PatientId:  patientId,
	}
}
