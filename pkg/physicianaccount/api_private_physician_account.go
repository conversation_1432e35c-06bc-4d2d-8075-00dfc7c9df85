package physicianaccount

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/generated/api"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"

	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/audit"
	phauth "gitlab.com/pockethealth/phutils/v10/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivatePhysicianAccountApiController struct {
	service                PhysicianAccountApiServicer
	accountService         accountservice.AccountService
	recordStreamingService recordstreaming.RecordStreamingServicer
	auditLogService        audit.AuditLoggerService
}

func NewPrivatePhysicianAccountApiController(
	s PhysicianAccountApiServicer,
	accountService accountservice.AccountService,
	recordStreamingService recordstreaming.RecordStreamingServicer,
	auditLogService audit.AuditLoggerService,
) *PrivatePhysicianAccountApiController {
	return &PrivatePhysicianAccountApiController{
		service:                s,
		accountService:         accountService,
		recordStreamingService: recordStreamingService,
		auditLogService:        auditLogService,
	}
}

func (c *PrivatePhysicianAccountApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetPhysicianAccount",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetPhysicianAccount,
		},
		{
			Name:        "PostLogout",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/logout",
			HandlerFunc: c.PostLogout,
		},
		{
			Name:        "GetPatients",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/patients",
			HandlerFunc: c.GetPatients,
		},
		{
			Name:        "GetPatientSharedExams",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/patients/{patientId}",
			HandlerFunc: c.GetPatientSharedExams,
		},
		{
			Name:        "PatchPhysician",
			Method:      strings.ToUpper("Patch"),
			Pattern:     "/physicians/{physician_id}",
			HandlerFunc: c.PatchPhysician,
		},
		{
			Name:        "PostPhysicianLicense",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/physicians/{physician_id}/licenses",
			HandlerFunc: c.PostPhysicianLicense,
		},
		{
			Name:        "PostVerifyPhysicianNotificationMethod",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/physicians/{physician_id}/verify",
			HandlerFunc: c.PostVerifyPhysicianNotificationMethod,
		},
		{
			Name:        "PostSearch",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/search",
			HandlerFunc: c.PostSearch,
		},
		{
			Name:        "PostShareRequest",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/shares/request",
			HandlerFunc: c.PostShareRequest,
		},
		{
			Name:        "PutExtendShare",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/shares/{shareId}/extend",
			HandlerFunc: c.PutExtendShare,
		},
		{
			Name:        "GetEUnityTokenForShare",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/shares/{shareId}/token",
			HandlerFunc: c.GetEUnityTokenForShare,
		},
		{
			Name:        "GetRecordStreamingStudiesWithUploadStatus",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/studies",
			HandlerFunc: c.GetRecordStreamingStudiesWithUploadStatus,
		},
		{
			Name:        "PostAccessVerification",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/studies/verify-access",
			HandlerFunc: c.PostStudyAccessVerification,
		},
	}
}

func (c *PrivatePhysicianAccountApiController) GetPathPrefix() string {
	return "/v1/physician_accounts"
}

func (c *PrivatePhysicianAccountApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{
		auth.ValidateAuth,
	}
}

// GET /v1/physician_accounts
func (c *PrivatePhysicianAccountApiController) GetPhysicianAccount(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	result, err := c.service.GetPhysicianAccount(r.Context(), accountId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// POST /v1/physician_accounts/logout
func (c *PrivatePhysicianAccountApiController) PostLogout(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	err := c.service.PostLogout(r.Context(), token)
	if err != nil {
		handleError(&w, r, err)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// GET /v1/physician_accounts/patients - Get patients for a physician
func (c *PrivatePhysicianAccountApiController) GetPatients(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	tokenHeader := r.Header.Get("Authorization")
	physicianAccountID, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	shareMetadata, err := c.service.GetShareMetadata(ctx, physicianAccountID)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	// add record streaming studies to result
	recordStreamingShareMetadata, err := c.recordStreamingService.GetShareMetadataForRecordStreamingStudies(
		ctx,
		physicianAccountID,
	)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	// group share metadata into patient summary cards
	shareMetadata = append(shareMetadata, recordStreamingShareMetadata...)
	patientSummaryCards, err := coreapi.GroupShareMetadata(ctx, shareMetadata)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), patientSummaryCards, nil, w)
}

// GET /v1/physician_accounts/patients/{patient_id} - Get a patient's exams
func (c *PrivatePhysicianAccountApiController) GetPatientSharedExams(
	w http.ResponseWriter,
	r *http.Request,
) {
	tokenHeader := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	params := mux.Vars(r)
	patientId := params["patientId"]
	if patientId == "" {
		httperror.ErrorWithLog(w, r, "missing patientId", http.StatusBadRequest)
		return
	}

	result, err := c.service.GetPatientSharedExams(r.Context(), acctId, patientId)
	if err != nil {
		handleError(&w, r, err)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PATCH /v1/physician_accounts/physicians/{physician_id}
// Updates a physician and initiates fax number verification if needed
func (c *PrivatePhysicianAccountApiController) PatchPhysician(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	physicianId := params["physician_id"]

	request := accountservice.PhysicianRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PatchPhysician(r.Context(), accountId, physicianId, request)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// POST /v1/physician_accounts/physicians/{physician_id}/licenses
func (c *PrivatePhysicianAccountApiController) PostPhysicianLicense(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	physicianId := params["physician_id"]

	request := accountservice.PhysicianLicenceRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PostPhysicianLicense(r.Context(), accountId, physicianId, request)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// POST /v1/physician_accounts/physicians/{physician_id}/verify
func (c *PrivatePhysicianAccountApiController) PostVerifyPhysicianNotificationMethod(
	w http.ResponseWriter,
	r *http.Request,
) {
	token := r.Header.Get("Authorization")
	accountId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusUnauthorized),
			http.StatusUnauthorized,
		)
		return
	}

	params := mux.Vars(r)
	physicianId := params["physician_id"]

	request := accountservice.PhysicianNotificationRequest{}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.PostVerifyPhysicianNotificationMethod(
		r.Context(),
		accountId,
		physicianId,
		request,
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// POST /v1/physician_accounts/search - initiates search for studies based on search params
func (c *PrivatePhysicianAccountApiController) PostSearch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	lg := logutils.DebugCtxLogger(ctx)

	tokenHeader := r.Header.Get("Authorization")
	accountID, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	eventBusClient := c.auditLogService.GetClient(ctx)
	event := audit.SetAuditEvent(accountID, audit.PhysicianEvent, audit.REQUEST)

	var searchQueryParams PhysicianRecordSearchParameters
	if err := json.NewDecoder(r.Body).Decode(&searchQueryParams); err != nil ||
		!searchQueryParams.IsValid() {
		event.SetEventParams("could not decode PACS search request", audit.FAIL, nil)
		go c.auditLogService.CreateAuditEvent(
			bgctx.GetBGCtxWithCorrelation(ctx),
			eventBusClient,
			event,
		)

		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	providerID := strconv.FormatInt(searchQueryParams.ProviderId, 10)
	queryId, err := c.service.SearchRecords(
		r.Context(),
		accountID,
		searchQueryParams,
	)
	if err != nil {
		event.SetEventParams(
			fmt.Sprintf("failed to initiate PACS search with error %s", err.Error()),
			audit.FAIL,
			&providerID,
		)
		go c.auditLogService.CreateAuditEvent(
			bgctx.GetBGCtxWithCorrelation(ctx),
			eventBusClient,
			event,
		)

		lg.WithError(err).Error("error searching for studies")
		handleError(&w, r, err)
		return
	}

	event.SetEventParams("successfully initiated PACS search", audit.SUCCESS, &providerID)
	go c.auditLogService.CreateAuditEvent(bgctx.GetBGCtxWithCorrelation(ctx), eventBusClient, event)

	coreapi.EncodeJSONResponse(r.Context(), queryId, nil, w)
}

// POST /v1/physician_accounts/shares/request - forwards a request for a record share to the provider
func (c *PrivatePhysicianAccountApiController) PostShareRequest(
	w http.ResponseWriter,
	r *http.Request,
) {
	ctx := r.Context()
	lg := logutils.DebugCtxLogger(ctx)

	// get account id from auth token
	tokenHeader := r.Header.Get("Authorization")
	accountID, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	eventBusClient := c.auditLogService.GetClient(ctx)
	event := audit.SetAuditEvent(accountID, audit.PhysicianEvent, audit.REQUEST)

	// get request from request body
	var request PhysicianRecordRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		event.SetEventParams("could not decode PACS study request", audit.FAIL, nil)
		go c.auditLogService.CreateAuditEvent(
			bgctx.GetBGCtxWithCorrelation(ctx),
			eventBusClient,
			event,
		)
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}
	if err := request.Validate(); err != nil {
		event.SetEventParams("invalid PACS study request", audit.FAIL, nil)
		go c.auditLogService.CreateAuditEvent(
			bgctx.GetBGCtxWithCorrelation(ctx),
			eventBusClient,
			event,
		)
		httperror.ErrorWithLog(w, r, errormsgs.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		return
	}
	providerID := strconv.FormatInt(request.ProviderId, 10)
	response, err := c.service.RequestRecords(
		r.Context(),
		accountID,
		request,
	)
	if err != nil {
		event.SetEventParams(
			fmt.Sprintf("failed to initiate PACS study request with error %s", err.Error()),
			audit.FAIL,
			&providerID,
		)
		go c.auditLogService.CreateAuditEvent(
			bgctx.GetBGCtxWithCorrelation(ctx),
			eventBusClient,
			event,
		)

		lg.WithError(err).Error("error requesting study")
		handleError(&w, r, err)
		return
	}
	event.SetEventParams("successfully initiated PACS study request", audit.SUCCESS, &providerID)
	go c.auditLogService.CreateAuditEvent(bgctx.GetBGCtxWithCorrelation(ctx), eventBusClient, event)

	coreapi.EncodeJSONResponse(r.Context(), response, nil, w)
}

// PUT /v1/physician_accounts/shares/{shareId}/extend - extend a physican's share access
func (c *PrivatePhysicianAccountApiController) PutExtendShare(
	w http.ResponseWriter,
	r *http.Request,
) {
	tokenHeader := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	params := mux.Vars(r)
	shareId := params["shareId"]
	if shareId == "" {
		httperror.ErrorWithLog(w, r, "missing shareId", http.StatusBadRequest)
		return
	}

	err = c.service.PutExtendShare(r.Context(), acctId, shareId)
	if err != nil {
		handleError(&w, r, err)
		return
	}
	w.WriteHeader(http.StatusOK)
}

// GET /v1/physician_accounts/shares/{shareId}/token - create an eunity token for the share
func (c *PrivatePhysicianAccountApiController) GetEUnityTokenForShare(
	w http.ResponseWriter,
	r *http.Request,
) {
	tokenHeader := r.Header.Get("Authorization")
	physicianAccountID, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	params := mux.Vars(r)
	shareID := params["shareId"]
	if shareID == "" {
		httperror.ErrorWithLog(w, r, "missing shareId", http.StatusBadRequest)
		return
	}
	// optional query parameter provider id for creating eunity tokens for record streaming studies
	hasProviderID, providerID, _ := coreapi.ParseQueryParamInt64(r, "providerId")

	// check if physician has access to study via standard share workflow and create token
	result, err := c.service.GetEUnityTokenForShare(r.Context(), physicianAccountID, shareID)
	if err != nil {
		// check if valid providerID was given
		if hasProviderID && providerID != 0 {
			// try to create eunity token for record streaming study
			result, err = c.recordStreamingService.CreateEUnityAccessTokenForStudy(
				r.Context(),
				physicianAccountID,
				shareID,
				providerID,
			)
		}
	}

	if err != nil {
		handleError(&w, r, err)
		return
	}
	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// GET /v1/physician_accounts/studies - get a list of record streaming study ids and their upload status that the physician has access to
func (c *PrivatePhysicianAccountApiController) GetRecordStreamingStudiesWithUploadStatus(
	w http.ResponseWriter,
	r *http.Request,
) {
	tokenHeader := r.Header.Get("Authorization")
	physicianAccountID, err := auth.DecodeAccountToken(tokenHeader)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	// get list of record streaming studies with upload state
	result, err := c.service.GetRecordStreamingStudiesWithUploadStatus(
		r.Context(),
		physicianAccountID,
	)
	if err != nil {
		handleError(&w, r, err)
		return
	}
	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// POST /v1/physician_accounts/studies/verify-access
//   - sends a study access request, resolves to success status code if physician can access study with given id and access attempt could be logged
func (c *PrivatePhysicianAccountApiController) PostStudyAccessVerification(
	w http.ResponseWriter,
	r *http.Request,
) {
	ctx := r.Context()
	physicianAccountID, err := phauth.AccountIDOrEmpty(ctx)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	// verify that caller is physician
	accountType, err := phauth.AccountTypeOrEmpty(ctx)
	if err != nil || (models.AccountType(accountType) != models.ACCOUNT_TYPE_PHYSICIAN) {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	// get request from request body
	var request api.PostPhysicianAccountStudyAccessReq
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.SubmitStudyAccessVerification(
		r.Context(),
		physicianAccountID,
		request.StudyUID,
		request.ProviderID,
		request.ShareID.Value,
	)
	if err != nil {
		handleError(&w, r, err)
		return
	}
	w.WriteHeader(http.StatusOK)
}
