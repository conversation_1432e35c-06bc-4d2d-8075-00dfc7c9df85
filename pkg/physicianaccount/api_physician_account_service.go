package physicianaccount

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"text/template"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/generated/api"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/audit"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models/modelphysicianaccount"
	sqlHandlers "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/coreapi/pkg/util/script"
	"gitlab.com/pockethealth/coreapi/pkg/v2users"

	eunitytokens "gitlab.com/pockethealth/coreapi/pkg/mysql/eunityTokens"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	sqlPhysicians "gitlab.com/pockethealth/coreapi/pkg/mysql/physicians"
	sqlStudyPermissions "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	sqlShares "gitlab.com/pockethealth/coreapi/pkg/mysql/shares"
	"gitlab.com/pockethealth/coreapi/pkg/pubsub"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	orgsvc "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

type PhysicianAccountApiService struct {
	db                  *sql.DB
	acctSvcUser         accountservice.AccountService
	faxServiceUser      fax.FaxSvcUser
	hlthRecSvcUser      hrs.HlthRecSvcUser
	orgSvcClient        orgsvc.OrgService
	recordServiceClient recordservice.RecordServiceClientInterface
	auditService        audit.Service
	roiSvcClient        roiservice.RoiService
	// service bus
	recordsSearchTopic   pubsub.TopicSender
	studyUploadTopic     pubsub.TopicSender
	region               string
	excludeDeletedShares bool
}

// NewPhysicianAccountApiService creates a default api service
func NewPhysicianAccountApiService(
	db *sql.DB,
	as accountservice.AccountService,
	fs fax.FaxSvcUser,
	hlthRecSvcUser hrs.HlthRecSvcUser,
	orgSvcClient orgsvc.OrgService,
	recordServiceClient recordservice.RecordServiceClientInterface,
	auditService audit.Service,
	roiSvcClient roiservice.RoiService,
	recordsSearchTopic pubsub.TopicSender,
	studyUploadTopic pubsub.TopicSender,
	region string,
	excludeDeletedShares bool,
) PhysicianAccountApiServicer {
	return &PhysicianAccountApiService{
		db:                   db,
		acctSvcUser:          as,
		faxServiceUser:       fs,
		hlthRecSvcUser:       hlthRecSvcUser,
		orgSvcClient:         orgSvcClient,
		recordServiceClient:  recordServiceClient,
		auditService:         auditService,
		roiSvcClient:         roiSvcClient,
		recordsSearchTopic:   recordsSearchTopic,
		studyUploadTopic:     studyUploadTopic,
		region:               region,
		excludeDeletedShares: excludeDeletedShares,
	}
}

func (s *PhysicianAccountApiService) GetPhysicianAccount(
	ctx context.Context,
	accountId string,
) (accountservice.PhysicianAccount, error) {
	// get physician account
	physicianAccount, err := s.acctSvcUser.GetPhysicianAccount(
		ctx,
		accountId,
	)
	// check if physician account is valid
	if err != nil {
		return accountservice.PhysicianAccount{}, err
	}

	// get permisisons for physician account
	physicianPermissions, err := s.acctSvcUser.GetAllPhysicianPermissions(
		ctx,
		physicianAccount.AccountId,
	)
	if err != nil {
		return accountservice.PhysicianAccount{}, err
	}

	// return full physician account object
	physicianAccount.PermissionsMap = physicianPermissions

	return physicianAccount, nil
}

func (s *PhysicianAccountApiService) PatchPhysician(
	ctx context.Context,
	accountId string,
	physicianId string,
	request accountservice.PhysicianRequest,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":   accountId,
		"physician_id": physicianId,
	})

	verificationData, err := s.acctSvcUser.PatchPhysician(
		ctx,
		accountId,
		physicianId,
		request,
	)
	if err != nil {
		lg.Error("failed to patch physician")
		return err
	}

	if verificationData.NeedsFaxVerification {
		// send verification fax to physician
		err = s.sendFaxVerification(ctx, physicianId, verificationData)
		if err != nil {
			lg.Error("failed to send verification fax after physician update")
			return err
		}
	}

	return nil
}

func (s *PhysicianAccountApiService) PostLogin(
	ctx context.Context,
	email string,
	password string,
	ip string,
) (interface{}, string, error) {
	return v2users.HandleLoginInAcctSvc(
		ctx,
		s.acctSvcUser,
		s.roiSvcClient,
		s.db,
		accountservice.PhysicianAccountEndpoint,
		email,
		password,
		ip,
	)
}

func (s *PhysicianAccountApiService) PostLogout(
	ctx context.Context,
	token string,
) error {
	// Blacklist token. Expiry will already have been checked in auth middleware
	err := auth.AddToBlacklist(ctx, token)
	if err != nil {
		return err
	}
	return nil
}

func (s *PhysicianAccountApiService) PostPhysicianLicense(
	ctx context.Context,
	accountId string,
	physicianId string,
	request accountservice.PhysicianLicenceRequest,
) error {
	return s.acctSvcUser.PostPhysicianLicense(
		ctx,
		accountId,
		physicianId,
		request,
	)
}

func (s *PhysicianAccountApiService) PostVerifyPhysicianNotificationMethod(
	ctx context.Context,
	accountId string,
	physicianId string,
	request accountservice.PhysicianNotificationRequest,
) error {
	return s.acctSvcUser.PostVerifyPhysicianNotificationMethod(
		ctx,
		accountId,
		physicianId,
		request,
	)
}

// PostCreateAccount - forward request to account service - returns acctId from account service
func (s *PhysicianAccountApiService) PostCreateAccount(
	ctx context.Context,
	rd coreapi.RegisterData,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx)

	prd := accountservice.PhysicianRegisterData{
		Email:     rd.Email,
		Password:  rd.Password,
		FirstName: rd.FirstName,
		LastName:  rd.LastName,
	}

	// send them through acctsvc verification flow
	acctId, err := s.acctSvcUser.CreatePhysicianAccount(
		ctx,
		prd,
	)
	if err != nil {

		if _, ok := err.(accountservice.ErrBadNewPassword); ok {
			lg.WithError(err).Error("unable to register acct and send verification email")
			return "", err
		}

		if _, ok := err.(accountservice.ErrNewAcctConflict); !ok {
			// do not return with error if account exists
			lg.WithError(err).Error("unable to register acct and send verification email")
			return "", err
		}
	}

	// create a handler entry for the new physician account
	handlerId, err := sqlHandlers.CreateHandler(ctx, s.db, coreapi.PhysicianAccount, acctId)
	if err != nil {
		// if this fails, delete the account
		lg.WithError(err).Error("unable to assign physician account a handler id")
		err2 := s.acctSvcUser.DeleteAccount(ctx, acctId)
		if err2 != nil {
			lg.WithError(err2).Error("unable to delete physician account")
			return "", err
		}
		return "", err
	}

	// auto-link shareId if already verified and submitted with acct creation request
	if rd.ShareId != "" {
		err = sqlHandlers.CreateHandlerShareMapping(ctx, s.db, handlerId, rd.ShareId)
		if err != nil {
			lg.WithError(err).Error("unable to assign physician account a handler id")
			return acctId, nil
		}
	}

	return acctId, nil
}

// PostResetPassword - Reset password
func (s *PhysicianAccountApiService) PostResetPassword(
	ctx context.Context,
	resetInfo coreapi.PasswordResetInfo,
) error {
	lg := logutils.DebugCtxLogger(ctx)
	acctEdit := accountservice.AccountEdit{
		Token:    resetInfo.Token,
		Password: resetInfo.NewPassword,
		Code:     resetInfo.SecurityCode,
	}

	err := s.acctSvcUser.ResetPassword(ctx, accountservice.PhysicianAccountEndpoint, acctEdit)
	if err != nil {
		lg.WithError(err).Error("error resetting password")
		return err
	}

	return nil
}

// PostResetPasswordInit - Send password email
func (s *PhysicianAccountApiService) PostResetPasswordInit(
	ctx context.Context,
	email string,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	err := s.acctSvcUser.ResetPasswordInit(ctx, accountservice.PhysicianAccountEndpoint, email)
	if err != nil {
		lg.WithError(err).Error("error resetting password")
		return err
	}

	return nil
}

// PostResetPasswordInit - Send password email
func (s *PhysicianAccountApiService) PostVerifyAccount(
	ctx context.Context,
	verification accountservice.Verification,
) error {
	lg := logutils.DebugCtxLogger(ctx)

	_, err := s.acctSvcUser.VerifyEmail(ctx, accountservice.PhysicianAccountEndpoint, verification)
	if err != nil {
		lg.WithError(err).Error("error verifying physician account email")
		return err
	}
	return nil
}

func (s *PhysicianAccountApiService) GetShareMetadata(
	ctx context.Context,
	acctId string,
) ([]coreapi.ShareMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("physician_acct_id", acctId)

	handlerId, err := sqlHandlers.LookupHandlerId(ctx, s.db, coreapi.PhysicianAccount, acctId)
	if err != nil {
		lg.WithError(err).Error("failed to lookup handler id")
		return []coreapi.ShareMetadata{}, err
	}

	canView, err := sqlHandlers.HandlerCanView(ctx, s.db, handlerId)
	if err != nil || !canView {
		lg.WithError(err).Error("handler does not have view permissions")
		return []coreapi.ShareMetadata{}, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	summary, err := sqlPhysicians.GetShareMetadata(
		ctx,
		s.db,
		s.acctSvcUser,
		s.orgSvcClient,
		handlerId,
	)
	if err != nil {
		lg.WithError(err).Error("failed to retrieve patients summary")
		return []coreapi.ShareMetadata{}, err
	}

	return summary, nil
}

func (s *PhysicianAccountApiService) GetPatientSharedExams(
	ctx context.Context,
	acctId string,
	patientId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"patient_id":        patientId,
		"physician_acct_id": acctId,
	})
	handlerId, err := sqlHandlers.LookupHandlerId(ctx, s.db, coreapi.PhysicianAccount, acctId)
	if err != nil {
		lg.WithError(err).Error("failed to lookup handler id")
		return []coreapi.Exam{}, err
	}

	// get exam id list and exam - share info map
	examIdList, examShareInfoMap, err := sqlPhysicians.GetPatientExams(
		ctx,
		s.db,
		s.acctSvcUser,
		handlerId,
		patientId,
	)
	if err != nil {
		lg.WithError(err).Error("could not retrieve patient exam ids")
		return nil, err
	}

	// add eunity tokens to for all non-expired exams
	for key := range examShareInfoMap {
		// check if exam access is expired
		expiry := examShareInfoMap[key].Expiry
		if examShareInfoMap[key].ExtendedExpiry != "" {
			// use extended expiry timestamp if access has been extended
			expiry = examShareInfoMap[key].ExtendedExpiry
		}
		if expiry == "" || !datetime.IsExpired(expiry) {
			eunityToken, err := eunitytokens.Create(ctx, s.db, examShareInfoMap[key].ShareId)
			if err != nil {
				lg.WithError(err).Error("could not create eunity token")
				return nil, err
			}
			examShareInfo := examShareInfoMap[key]
			examShareInfo.EUnityToken = eunityToken
			examShareInfoMap[key] = examShareInfo
		}
	}

	// retrieve health record ids
	hrIdList, hrShareInfoMap, err := sqlPhysicians.GetPatientHealthRecordIdList(
		ctx,
		s.db,
		handlerId,
		patientId,
	)
	if err != nil {
		lg.WithError(err).Error("could not retrieve patient health record ids")
		return nil, err
	}

	// retrieve exam summary data
	exams := make([]coreapi.Exam, 0)
	if len(examIdList) > 0 {
		q, err := queries.NewWhere(map[string][]any{"e.activated": {true}})
		if err != nil {
			return nil, err
		}
		examsRaw, err := sqlExams.GetExamSummaryData(
			ctx,
			s.db,
			"",
			examIdList,
			q,
			false,
			s.excludeDeletedShares,
			s.orgSvcClient,
		)
		if err != nil || len(examsRaw) <= 0 {
			lg.WithError(err).Error("could not retrieve exam summary data")
			return nil, err
		}
		exams = coreapi.RawToExams(ctx, examsRaw)
	}

	// retrieve health records
	records := make([]coreapi.Record, 0)
	if len(hrIdList) > 0 {
		records, err = s.hlthRecSvcUser.GetMetadataByIds(ctx, hrIdList)
		if err != nil {
			lg.WithError(err).Error("could not retrieve health records")
			return nil, err
		}
	}

	return coreapi.PatientShareInfo{
		Exams:                    exams,
		ExamShareInfoMap:         examShareInfoMap,
		HealthRecords:            records,
		HealthRecordShareInfoMap: hrShareInfoMap,
	}, nil
}

func (s *PhysicianAccountApiService) GetEUnityTokenForShare(
	ctx context.Context,
	acctId string,
	shareId string,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id":          shareId,
		"physician_acct_id": acctId,
	})

	canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.db, acctId, shareId)
	if err != nil || !canView {
		lg.WithError(err).Error("handler does not have permission to view share")
		return "", errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	} else {
		return eunitytokens.Create(ctx, s.db, shareId)
	}
}

func (s *PhysicianAccountApiService) PutExtendShare(
	ctx context.Context,
	acctId string,
	shareId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id":          shareId,
		"physician_acct_id": acctId,
	})

	canView, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.db, acctId, shareId)
	if err != nil || !canView {
		lg.WithError(err).Error("handler does not have permission to view share")
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	validExtend, err := sqlShares.ExtendViewShare(ctx, s.db, shareId)
	if err != nil {
		return err
	}
	if !validExtend {
		// Already extended before or It's a email share
		return errors.New(errormsgs.ERR_UNABLE_EXTEND)
	}

	return nil
}

// SearchRecords checks physician's permission to search records for the given provider
// and forwards the search request to a search topic
func (s *PhysicianAccountApiService) SearchRecords(
	ctx context.Context,
	acctId string,
	searchParams PhysicianRecordSearchParameters,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":  acctId,
		"provider_id": searchParams.ProviderId,
	})

	// get search permissions for physician and provider from account service
	permissionMap, err := s.acctSvcUser.GetPhysicianPermissionsByProviderIdAndPermissionName(
		ctx,
		acctId,
		searchParams.ProviderId,
		// name of permission needed to search provider pacs and request records
		accountservice.PermissionBreakTheGlass,
	)
	if err != nil || len(permissionMap) == 0 {
		// physician does not have permission to search provider
		lg.WithError(err).Error("could not verify physician's permission to search records")
		return "", errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	// create ksuid for valid query
	queryId := ksuid.New().String()
	// send querId + accountId + search query to topic
	searchEvent := NewPhysicianRecordsSearchEvent(
		acctId,
		queryId,
		searchParams,
		s.region,
	)
	if !searchEvent.IsValid() {
		// event is not valid
		lg.Error("could not create valid event for physician study search topic")
		return "", errors.New(errormsgs.ERR_INVALID_REQ_BODY)
	}

	// forward event to topic
	err = azureUtils.SendEvent(
		ctx,
		s.recordsSearchTopic,
		searchEvent,
	)
	if err != nil {
		lg.WithError(err).Error("failed to send event to azure topic")
		return "", errors.New(errormsgs.ERR_PUBLISH)
	}

	err = s.auditService.CreatePhysicianPACSSearchEvent(
		ctx,
		models.EventDataPhysicianPACSSearch{
			EventDataPhysicianBase: models.EventDataPhysicianBase{
				ProviderID: searchParams.ProviderId,
				UserID:     acctId,
				UserType:   models.UserTypePhysician,
			},
			EventDataPatientBase: models.EventDataPatientBase{
				PatientFirstName: searchParams.Query.FirstName,
				PatientLastName:  searchParams.Query.LastName,
				PatientBirthDate: searchParams.Query.DateOfBirth,
			},
			StudyRangeStartDate: searchParams.Query.StudyStartDate,
			StudyRangeEndDate:   searchParams.Query.StudyStartDate,
		},
	)
	if err != nil {
		return "", errors.New(errormsgs.ERR_AUDIT)
	}

	// return async queryId to caller
	return queryId, nil
}

// RequestRecords checks physician's permission to request records for the given provider
// and forwards the record request to a request topic
func (s *PhysicianAccountApiService) RequestRecords(
	ctx context.Context,
	physicianAccountID string,
	physicianRecordRequest PhysicianRecordRequest,
) ([]modelphysicianaccount.StudyUploadResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":  physicianAccountID,
		"provider_id": physicianRecordRequest.ProviderId,
	})

	// get search permissions for physician and provider from account service
	permissionMap, err := s.acctSvcUser.GetPhysicianPermissionsByProviderIdAndPermissionName(
		ctx,
		physicianAccountID,
		physicianRecordRequest.ProviderId,
		// name of permission needed to search provider pacs and request records
		accountservice.PermissionBreakTheGlass,
	)
	if err != nil || len(permissionMap) == 0 {
		// physician does not have permission to search provider
		lg.WithError(err).Error("could not verify physician's permission to search records")
		return []modelphysicianaccount.StudyUploadResponse{}, errors.New(
			errormsgs.ERR_NOT_AUTHORIZED,
		)
	}

	responses := []modelphysicianaccount.StudyUploadResponse{}
	for _, studyUploadRequest := range physicianRecordRequest.Requests {
		responses = append(
			responses,
			s.initiateStudyUpload(
				ctx,
				physicianAccountID,
				physicianRecordRequest.ProviderId,
				studyUploadRequest,
			),
		)
	}

	return responses, nil
}

func (s *PhysicianAccountApiService) GetRecordStreamingStudiesWithUploadStatus(
	ctx context.Context,
	physicianAccountID string,
) ([]api.PhysicianRecordUploadStatus, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": physicianAccountID,
	})
	result := []api.PhysicianRecordUploadStatus{}
	response, err := s.recordServiceClient.GetV1StudiesUploadStatus(
		ctx,
		generatedrecordservice.GetV1StudiesUploadStatusParams{
			AccountID: physicianAccountID,
		},
	)
	if err != nil {
		lg.WithError(err).Error("failed to get study data for physician")
		return result, err
	}
	uploadStatuses, ok := response.(*generatedrecordservice.GetV1StudiesUploadStatusOKApplicationJSON)
	if !ok {
		return result, errors.New("failed to convert recordservice response")
	}
	result = StudyUploadStatuses(*uploadStatuses).ToAPIModel()
	return result, nil
}

// SubmitStudyAccessVerification
// verifies that the physician has access to the study via record streaming permission or shares.
// Record streaming access is verified via studyUID and provider ID.
// Share access is verified via share ID.
// For studies with record streaming access, an audit event is logged.
// If access verification or audit logging fails, an error is returned
func (s *PhysicianAccountApiService) SubmitStudyAccessVerification(
	ctx context.Context,
	accountID string,
	studyUID string,
	providerID int64,
	shareID string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountID,
	})

	// get UUID for study
	uuid, err := sqlStudyPermissions.GetExamUUIDForRecordStreamingStudy(s.db, studyUID, providerID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		lg.WithError(err).
			Error("failed to get uuid for record streaming study")
		return errors.New("failed to verify study access")
	} else if errors.Is(err, sql.ErrNoRows) {
		// if study does not exist as record streaming study,
		// check if physician has access via share instead
		hasAccess, err := sqlPhysicians.PhysicianCanViewShare(ctx, s.db, accountID, shareID)
		if err != nil {
			lg.WithError(err).
				Error("failed to verify study access via share")
			return errors.New("failed to verify study access")
		}
		if !hasAccess {
			lg.Error("physician does not have permission to view study")
			return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		}
		return nil
	} else {
		// check study
		response, err := s.recordServiceClient.GetV1PhysicianStudies(
			ctx,
			generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: accountID,
				UUID:      []string{uuid},
			})
		if err != nil {
			lg.WithError(err).Error("could not verify access")
			return err
		}
		studies, ok := response.(*generatedrecordservice.PhysicianPatientStudies)
		if !ok {
			lg.WithError(err).Error("could not parse recordservice response")
			return errors.New("failed to convert recordservice response")
		}
		// find study with matching uuid from list of studies
		var study generatedrecordservice.PhysicianPatientStudy
		for _, responseStudy := range *studies {
			if responseStudy.UUID.Value == uuid {
				study = responseStudy
			}
		}
		if study.UUID.Value == "" {
			lg.WithError(err).Error("physician is not verified to view study")
			return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		}

		firstName, lastName := dcmtools.ParseFirstLastName(
			ctx,
			study.DicomPatientTags.Value.PatientName.Value,
		)
		// log physician access attempt
		err = s.auditService.CreatePhysicianStudyViewEvent(ctx, models.EventDataPhysicianStudyView{
			EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
				EventDataPhysicianBase: models.EventDataPhysicianBase{
					ProviderID: providerID,
					UserID:     accountID,
					UserType:   models.UserTypePhysician,
				},
				EventDataPatientBase: models.EventDataPatientBase{
					PatientFirstName: firstName,
					PatientLastName:  lastName,
					PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
				},
				StudyUID: study.DicomStudyTags.Value.StudyInstanceUID.Value,
			},
		})
		if err != nil {
			lg.WithError(err).
				Error("failed to log audit event for study access request")
			return errors.New(errormsgs.ERR_AUDIT)
		}
		return nil
	}
}

// sends out a verification fax to a physician after the physician's fax number has been updated
func (s *PhysicianAccountApiService) sendFaxVerification(
	ctx context.Context,
	physicianId string,
	verificationData accountservice.PhysicianVerificationData,
) error {
	// generate directory for fax file
	tempFilesDirectory := fmt.Sprintf("vault/tmp/prep/%s/VERIFICATION", physicianId) // #nosec G304
	if _, err := os.Stat(tempFilesDirectory); err != nil {
		err = os.MkdirAll(tempFilesDirectory, 0700)
		if err != nil {
			return errors.New(errormsgs.ERR_OP_CONFLICT)
		}
	}

	// remove temp files after sending out the fax
	defer os.RemoveAll(tempFilesDirectory)

	// load fax number verification template
	templateFile := "tmpl/physicianFaxVerificationTemplate.json"
	tmpl, err := template.ParseFiles(templateFile)
	if err != nil {
		return errors.New("failed to parse template for physician verification fax")
	}
	var result bytes.Buffer
	// customize fax number verification template based on physician data
	err = tmpl.Execute(&result, verificationData)
	if err != nil {
		return errors.New("failed to update template for physician verification fax")
	}

	// save customized verification document
	bytebuf := result.Bytes()
	err = os.WriteFile(tempFilesDirectory+"/DESCRIPTOR", bytebuf, 0600)
	if err != nil {
		return errors.New(errormsgs.ERR_COPY_FAIL)
	}

	// convert verification document to pdf
	err, _ = script.RunNodeScript(
		"jsScripts/pdfHeader.js",
		[]string{tempFilesDirectory + "/DESCRIPTOR"},
	)
	if err != nil {
		return errors.New("failed to create pdf file for physician verification fax")
	}

	// load fax number verification pdf
	finalFilePath := tempFilesDirectory + "/MAIN.pdf"
	/* #nosec G304 */
	file, err := os.ReadFile(finalFilePath)
	if err != nil {
		return errors.New(errormsgs.ERR_READ_FAIL)
	}

	// send fax message
	_, err = s.faxServiceUser.PostFax(ctx, verificationData.ContactData, bytes.NewReader(file))
	return err
}

func (s *PhysicianAccountApiService) initiateStudyUpload(
	ctx context.Context,
	physicianAccountID string,
	providerID int64,
	studyUploadRequest StudyRequest,
) modelphysicianaccount.StudyUploadResponse {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id":  physicianAccountID,
		"provider_id": providerID,
		"study_uid":   studyUploadRequest.StudyUID,
	})
	studyUID := studyUploadRequest.StudyUID
	studyUploadResponse := modelphysicianaccount.StudyUploadResponse{
		StudyUID: studyUID,
	}

	// 1. Check if study exists (true if study manifest upload step has succeeded and study index has been created)
	studyExists, err := sqlStudyPermissions.HasStudyIndex(s.db, studyUID, providerID)
	if err != nil {
		lg.WithError(err).Error("could not check existence of study")
		return studyUploadResponse
	}

	if studyExists {
		// 2. Upload for requested study has already been triggered
		lg.Info("requested study already exists")
		studyUploadResponse.AlreadyUploaded = true

		// 2.5 Check if report has been uploaded for study (true if unique index exists for study report)
		hasReport, err := sqlStudyPermissions.HasReportIndex(
			s.db, studyUID, providerID,
		)
		if err != nil {
			// don't block rest of request - assume that there is no report available at the moment
			lg.WithError(err).Error("could not check existence of report")
		}
		studyUploadResponse.HasReport = hasReport
	} else {
		// 2. Initiate study retrieval if study manifest upload hasn't completed yet
		// NOTE: this could lead to re-triggering the same study upload repeatedly
		//	until the study manifest upload succeeds.
		//	But it also enables physicians to re-trigger a study upload
		//	if the study manifest upload step fails.

		// create event
		event := NewStudyUploadRequest(
			providerID,
			studyUID,
			studyUploadRequest.PatientId,
		)

		// forward event to topic
		err = azureUtils.SendEvent(
			ctx,
			s.studyUploadTopic,
			event,
		)
		if err != nil {
			lg.WithError(err).Error("failed to send event to azure topic")
			return studyUploadResponse
		}
	}

	// 3. Create access permission for physician.
	//	If a physician already requested this study and the first upload step failed,
	//	they already have permissions. In this case, nothing will change here.
	err = sqlStudyPermissions.CreatePhysicianStudyAccess(
		s.db,
		physicianAccountID,
		studyUID,
		providerID,
	)
	if err != nil {
		lg.WithError(err).Error("failed to create physician study access")
		return studyUploadResponse
	}

	// 4. Create audit event for retrieval request
	err = s.auditService.CreatePhysicianStudyRetrieveEvent(
		ctx,
		models.EventDataPhysicianStudyRetrieve{
			EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
				EventDataPhysicianBase: models.EventDataPhysicianBase{
					ProviderID: providerID,
					UserID:     physicianAccountID,
					UserType:   models.UserTypePhysician,
				},
				EventDataPatientBase: models.EventDataPatientBase{
					PatientFirstName: studyUploadRequest.PatientFirstName,
					PatientLastName:  studyUploadRequest.PatientLastName,
					PatientBirthDate: studyUploadRequest.PatientBirthDate,
				},
				StudyUID: studyUID,
			},
		},
	)
	if err != nil {
		lg.WithError(err).Error(errormsgs.ERR_AUDIT)
		return studyUploadResponse
	}

	studyUploadResponse.Success = true
	return studyUploadResponse
}
