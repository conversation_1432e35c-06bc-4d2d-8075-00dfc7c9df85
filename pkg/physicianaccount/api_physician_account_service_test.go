package physicianaccount_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	auditmocks "gitlab.com/pockethealth/coreapi/generated/mocks/audit"
	topicmocks "gitlab.com/pockethealth/coreapi/generated/mocks/pubsub"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models/modelphysicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/fax"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

var (
	accountId             = "accountId"
	emptyPhysicianAccount = accountservice.PhysicianAccount{}
	physicianAccount      = accountservice.PhysicianAccount{
		AccountId: accountId,
	}
	emptyPermissions = map[int64][]accountservice.PhysicianAccountPermission{}
	permissions      = map[int64][]accountservice.PhysicianAccountPermission{
		200035: {},
	}
	errDummy = errors.New("unexpected error")
	region   = "region"
)

func TestCreatePhysicianAccount(t *testing.T) {
	ctx := context.Background()
	t.Run("acct conflict", func(t *testing.T) {
		// setup service
		acctConflictErr := accountservice.ErrNewAcctConflict{}
		service, _, dbmock, _, _ := setup(
			t,
			ctx,
			physicianAccount,
			emptyPermissions,
			acctConflictErr,
			false,
		)
		dbmock.ExpectExec(`INSERT INTO record_handlers`).
			WithArgs(sqlmock.AnyArg(), coreapi.PhysicianAccount, sqlmock.AnyArg(), false, false, false, true).
			WillReturnResult(sqlmock.NewResult(1, 1))
		// call function
		result, err := service.PostCreateAccount(
			ctx,
			coreapi.RegisterData{
				Email:    "<EMAIL>",
				Password: "1234test1234",
				ShareId:  "123456",
			},
		)

		require.NoError(t, dbmock.ExpectationsWereMet())
		// validate results
		require.NoError(t, err)
		assert.NotEmpty(t, result) // with acct conflict should still return id
	})

	t.Run("invalid password", func(t *testing.T) {
		// setup service
		pwerr := accountservice.ErrBadNewPassword{MissingRequirement: "Password invalid:2"}
		service, _, _, _, _ := setup(t, ctx, physicianAccount, emptyPermissions, pwerr, false)
		// call function
		_, err := service.PostCreateAccount(
			ctx,
			coreapi.RegisterData{Email: "<EMAIL>", Password: "password", ShareId: "123456"},
		)
		// validate results
		require.Error(t, err)

		require.Equal(t, "Password invalid:2", err.Error())
	})
}

func TestGetPhysicianAccount(t *testing.T) {
	ctx := context.Background()
	t.Run("Returns error if physician account can't be verified", func(t *testing.T) {
		// setup service
		service, _, _, _, _ := setup(t, ctx, physicianAccount, emptyPermissions, errDummy, false)
		// call function
		result, err := service.GetPhysicianAccount(ctx, accountId)
		// validate results
		assert.Error(t, err)
		assert.Equal(t, emptyPhysicianAccount, result) // no account returned on error
	})
	t.Run(
		"Returns physician with empty permissions if physician does not have permissions",
		func(t *testing.T) {
			// setup service
			service, _, _, _, _ := setup(t, ctx, physicianAccount, emptyPermissions, nil, false)
			// call function
			result, err := service.GetPhysicianAccount(ctx, accountId)
			// validate results
			if assert.NoError(t, err) {
				physicianAccountWithoutPermissions := physicianAccount
				physicianAccountWithoutPermissions.PermissionsMap = map[int64][]accountservice.PhysicianAccountPermission{}
				assert.Equal(
					t,
					physicianAccountWithoutPermissions,
					result,
				) // should be physician account with empty map
			}
		},
	)
	t.Run(
		"Returns queryId and publishes search event if physician has search permission",
		func(t *testing.T) {
			// setup service
			service, _, _, _, _ := setup(t, ctx, physicianAccount, permissions, nil, false)
			// call function
			result, err := service.GetPhysicianAccount(ctx, accountId)
			// validate results
			if assert.NoError(t, err) {
				physicianAccountWithPermissions := physicianAccount
				physicianAccountWithPermissions.PermissionsMap = permissions
				assert.Equal(t, physicianAccountWithPermissions, result)
			}
		},
	)
	t.Run(
		"Returns group ID and name if physician has group permissions",
		func(t *testing.T) {
			// setup group permission
			groupPermissions := map[int64][]accountservice.PhysicianAccountPermission{
				phtestutils.GenerateRandomInt64(t): {
					{
						PermissionId:          phtestutils.GenerateRandomInt64(t),
						PermissionName:        "break the glass",
						PermissionDescription: "allows a physician to break the glass",
						GroupId:               phtestutils.GenerateRandomInt64(t),
						GroupName:             "Clinic A",
					},
				},
			}
			// setup service
			service, _, _, _, _ := setup(t, ctx, physicianAccount, groupPermissions, nil, false)
			// call function
			result, err := service.GetPhysicianAccount(ctx, accountId)
			// validate results
			if assert.NoError(t, err) {
				physicianAccountWithGroupPermissions := physicianAccount
				physicianAccountWithGroupPermissions.PermissionsMap = groupPermissions
				assert.Equal(t, physicianAccountWithGroupPermissions, result)
			}
		},
	)
}

func TestSearchRecords(t *testing.T) {
	ctx := context.Background()
	searchParams := physicianaccount.PhysicianRecordSearchParameters{
		ProviderId: 200035,
		Query: physicianaccount.RecordSearchQueryParameters{
			FirstName:      "Ada",
			LastName:       "Lovelace",
			DateOfBirth:    "1990-01-01",
			StudyStartDate: "2000-12-31",
			StudyEndDate:   "2024-02-29",
		},
	}

	testCases := map[string]struct {
		initMocks     func(t *testing.T, request physicianaccount.PhysicianRecordSearchParameters, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService)
		request       physicianaccount.PhysicianRecordSearchParameters
		permissions   map[int64][]accountservice.PhysicianAccountPermission
		expectedError string
	}{
		"returns error if physician does not have permissions": {
			request:       searchParams,
			expectedError: errormsgs.ERR_NOT_AUTHORIZED,
			permissions:   emptyPermissions,
		},
		"returns error if event sending failed": {
			initMocks: func(t *testing.T, request physicianaccount.PhysicianRecordSearchParameters, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService) {
				topic.EXPECT().
					SendMessage(mock.Anything, mock.Anything, mock.Anything).
					Return(errors.New("dummy"))
			},
			request:       searchParams,
			expectedError: errormsgs.ERR_PUBLISH,
			permissions:   permissions,
		},
		"returns error if audit logging failed": {
			initMocks: func(t *testing.T, request physicianaccount.PhysicianRecordSearchParameters, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService) {
				topic.EXPECT().SendMessage(mock.Anything, mock.Anything, mock.Anything).Return(nil)
				audit.EXPECT().
					CreatePhysicianPACSSearchEvent(ctx, models.EventDataPhysicianPACSSearch{
						EventDataPhysicianBase: models.EventDataPhysicianBase{
							ProviderID: searchParams.ProviderId,
							UserID:     accountId,
							UserType:   models.UserTypePhysician,
						},
						EventDataPatientBase: models.EventDataPatientBase{
							PatientFirstName: searchParams.Query.FirstName,
							PatientLastName:  searchParams.Query.LastName,
							PatientBirthDate: searchParams.Query.DateOfBirth,
						},
						StudyRangeStartDate: searchParams.Query.StudyStartDate,
						StudyRangeEndDate:   searchParams.Query.StudyStartDate,
					}).Return(errors.New("dummy"))
			},
			request:       searchParams,
			expectedError: errormsgs.ERR_AUDIT,
			permissions:   permissions,
		},
		"returns upload responses on success": {
			initMocks: func(t *testing.T, request physicianaccount.PhysicianRecordSearchParameters, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService) {
				topic.EXPECT().SendMessage(mock.Anything, mock.Anything, mock.Anything).Return(nil)
				audit.EXPECT().
					CreatePhysicianPACSSearchEvent(ctx, models.EventDataPhysicianPACSSearch{
						EventDataPhysicianBase: models.EventDataPhysicianBase{
							ProviderID: searchParams.ProviderId,
							UserID:     accountId,
							UserType:   models.UserTypePhysician,
						},
						EventDataPatientBase: models.EventDataPatientBase{
							PatientFirstName: searchParams.Query.FirstName,
							PatientLastName:  searchParams.Query.LastName,
							PatientBirthDate: searchParams.Query.DateOfBirth,
						},
						StudyRangeStartDate: searchParams.Query.StudyStartDate,
						StudyRangeEndDate:   searchParams.Query.StudyStartDate,
					}).Return(nil)
			},
			request:     searchParams,
			permissions: permissions,
		},
	}
	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				// setup service
				service, receiver, _, audit, _ := setup(
					t,
					ctx,
					emptyPhysicianAccount,
					tc.permissions,
					nil,
					true,
				)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, tc.request, receiver, audit)
				}
				// call function
				queryId, err := service.SearchRecords(ctx, accountId, searchParams)
				// validate results
				if tc.expectedError != "" {
					assert.EqualError(t, err, tc.expectedError)
					assert.Empty(t, queryId)
				} else {
					assert.NoError(t, err)
					assert.NotEmpty(t, queryId)
				}
			},
		)
	}
}

func TestRequestRecords(t *testing.T) {
	ctx := context.Background()
	request := physicianaccount.PhysicianRecordRequest{
		ProviderId: 200035,
		Requests: []physicianaccount.StudyRequest{
			{
				StudyUID:         "study uid 1",
				PatientId:        "patient id 1",
				Issuer:           "issuer 1",
				PatientFirstName: "first name 1",
				PatientLastName:  "last name 1",
				PatientBirthDate: "********",
			},
			{
				StudyUID:  "study uid 2",
				PatientId: "patient id 2",
			},
		},
	}

	testCases := map[string]struct {
		initMocks      func(t *testing.T, request physicianaccount.PhysicianRecordRequest, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService)
		request        physicianaccount.PhysicianRecordRequest
		permissions    map[int64][]accountservice.PhysicianAccountPermission
		expectedError  string
		expectedResult []modelphysicianaccount.StudyUploadResponse
	}{
		"returns error if physician does not have permissions": {
			request:        request,
			permissions:    emptyPermissions,
			expectedError:  errormsgs.ERR_NOT_AUTHORIZED,
			expectedResult: []modelphysicianaccount.StudyUploadResponse{},
		},
	}
	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				// setup service
				service, receiver, _, audit, _ := setup(
					t,
					ctx,
					emptyPhysicianAccount,
					tc.permissions,
					nil,
					true,
				)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, tc.request, receiver, audit)
				}
				// call function
				result, err := service.RequestRecords(ctx, accountId, request)
				// validate results
				if tc.expectedError != "" {
					assert.EqualError(t, err, tc.expectedError)
				}
				assert.Equal(t, tc.expectedResult, result)
			},
		)
	}
}

func TestSubmitStudyAccessVerification(t *testing.T) {
	ctx := context.Background()
	accountID := ksuid.New().String()
	studyUID := "studyUID"
	uuid := "uuid"
	providerID := int64(42)
	shareID := ksuid.New().String()
	testErr := errors.New("dummy")
	study := generatedrecordservice.PhysicianPatientStudy{
		UUID: generatedrecordservice.NewOptString(uuid),
		DicomStudyTags: generatedrecordservice.NewOptDicomStudyTags(
			generatedrecordservice.DicomStudyTags{
				StudyInstanceUID: generatedrecordservice.NewOptString(studyUID),
			},
		),
		DicomPatientTags: generatedrecordservice.NewOptDicomPatientTags(
			generatedrecordservice.DicomPatientTags{
				PatientName: generatedrecordservice.NewOptString(
					"Dummy",
				), // will be parsed as just last name by dcmtools
				PatientBirthDate: generatedrecordservice.NewOptString(
					phtestutils.GenerateRandomString(t, 10),
				),
			},
		),
	}

	queries := []string{
		// query for getting uuid for study
		`SELECT exam_id
		FROM unique_study_index 
		WHERE study_uid=\? AND provider_id=\?`,
		// first query for checking share access
		`SELECT id FROM record_handlers WHERE handler_type=\? AND handler_type_id=\?`,
		// second query for checking share access
		`SELECT view FROM record_handlers rh 
			JOIN record_handlers_share_map rhsm ON rh.id = rhsm.handler_id 
			WHERE rh.id = \?
			AND rhsm.share_id = \?`,
	}
	testCases := map[string]struct {
		initMocks     func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface)
		expectedError string
	}{
		"should return error if uuid cannot be fetched for study": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnError(testErr)
			},
			expectedError: "failed to verify study access",
		},
		"should return error if verification cannot be done for record streaming": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnRows(sqlmock.NewRows([]string{"exam_id"}).AddRow(uuid))
				// requested study is not returned from record service
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: accountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{}, nil)
			},
			expectedError: errormsgs.ERR_NOT_AUTHORIZED,
		},
		"should return error if verification fails for record streaming": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnRows(sqlmock.NewRows([]string{"exam_id"}).AddRow(uuid))
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: accountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{}, testErr)
			},
			expectedError: testErr.Error(),
		},
		"should return error if audit fails": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnRows(sqlmock.NewRows([]string{"exam_id"}).AddRow(uuid))
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: accountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{study}, nil)
				audit.EXPECT().
					CreatePhysicianStudyViewEvent(mock.Anything, models.EventDataPhysicianStudyView{
						EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
							EventDataPhysicianBase: models.EventDataPhysicianBase{
								ProviderID: providerID,
								UserID:     accountID,
								UserType:   models.UserTypePhysician,
							},
							EventDataPatientBase: models.EventDataPatientBase{
								PatientFirstName: "",
								PatientLastName:  study.DicomPatientTags.Value.PatientName.Value,
								PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
							},
							StudyUID: studyUID,
						},
					}).
					Return(testErr)
			},
			expectedError: errormsgs.ERR_AUDIT,
		},
		"should return no error for record streaming access": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnRows(sqlmock.NewRows([]string{"exam_id"}).AddRow(uuid))
				recordService.EXPECT().
					GetV1PhysicianStudies(mock.Anything, generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID: accountID,
						UUID:      []string{uuid},
					}).
					Return(&generatedrecordservice.PhysicianPatientStudies{study}, nil)
				audit.EXPECT().
					CreatePhysicianStudyViewEvent(mock.Anything, models.EventDataPhysicianStudyView{
						EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
							EventDataPhysicianBase: models.EventDataPhysicianBase{
								ProviderID: providerID,
								UserID:     accountID,
								UserType:   models.UserTypePhysician,
							},
							EventDataPatientBase: models.EventDataPatientBase{
								PatientFirstName: "",
								PatientLastName:  study.DicomPatientTags.Value.PatientName.Value,
								PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
							},
							StudyUID: studyUID,
						},
					}).
					Return(nil)
			},
		},
		"should return error if verification fails for share": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnError(sql.ErrNoRows)
				db.ExpectQuery(queries[1]).
					WithArgs("phys_acct", accountID).
					WillReturnError(testErr)
			},
			expectedError: "failed to verify study access",
		},
		"should return error if physician does not have access to study": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnError(sql.ErrNoRows)
				db.ExpectQuery(queries[1]).
					WithArgs("phys_acct", accountID).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("id"))
				db.ExpectQuery(queries[2]).
					WithArgs("id", shareID).
					WillReturnRows(sqlmock.NewRows([]string{"view"}).AddRow(false))
			},
			expectedError: errormsgs.ERR_NOT_AUTHORIZED,
		},
		"should return no error for share access": {
			initMocks: func(t *testing.T, db sqlmock.Sqlmock, topic *topicmocks.MockTopicSender, audit *auditmocks.MockService, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				db.ExpectQuery(queries[0]).
					WithArgs(studyUID, providerID).
					WillReturnError(sql.ErrNoRows)
				db.ExpectQuery(queries[1]).
					WithArgs("phys_acct", accountID).
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("id"))
				db.ExpectQuery(queries[2]).
					WithArgs("id", shareID).
					WillReturnRows(sqlmock.NewRows([]string{"view"}).AddRow(true))
			},
		},
	}
	for description, tc := range testCases {
		t.Run(
			description,
			func(t *testing.T) {
				// setup service
				service, receiver, db, audit, recordService := setup(
					t,
					ctx,
					emptyPhysicianAccount,
					emptyPermissions,
					nil,
					true,
				)

				// run initMocks
				if tc.initMocks != nil {
					tc.initMocks(t, db, receiver, audit, recordService)
				}
				// call function
				err := service.SubmitStudyAccessVerification(
					ctx,
					accountID,
					studyUID,
					providerID,
					shareID,
				)
				// validate results
				if tc.expectedError != "" {
					assert.EqualError(t, err, tc.expectedError)
				} else {
					assert.NoError(t, err)
				}
			},
		)
	}
}

func getRetrievalResponse(
	request physicianaccount.PhysicianRecordRequest,
	isSuccess bool,
) []modelphysicianaccount.StudyUploadResponse {
	response := []modelphysicianaccount.StudyUploadResponse{}
	for i := range request.Requests {
		response = append(response,
			modelphysicianaccount.StudyUploadResponse{
				StudyUID:        request.Requests[i].StudyUID,
				Success:         isSuccess,
				AlreadyUploaded: false,
				HasReport:       false,
			})
	}
	return response
}

func setup(
	t *testing.T,
	ctx context.Context,
	physicianAccount accountservice.PhysicianAccount,
	physicianPermissions map[int64][]accountservice.PhysicianAccountPermission,
	accountServiceError error,
	setupTopic bool,
) (physicianaccount.PhysicianAccountApiServicer, *topicmocks.MockTopicSender, sqlmock.Sqlmock, *auditmocks.MockService, *mockrecordservice.MockRecordServiceClientInterface) {
	t.Helper()
	// setup account service mock
	accountServiceMock := &accountservice.AcctSvcMock{
		GetPhysicianAccountReturn:                                  physicianAccount,
		GetPhysicianPermissionsByProviderIdAndPermissionNameReturn: physicianPermissions,
		MockError: accountServiceError,
	}
	recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
	auditService := auditmocks.NewMockService(t)
	topicSender := topicmocks.NewMockTopicSender(t)
	db, dbmock, _ := sqlmock.New()
	service := physicianaccount.NewPhysicianAccountApiService(
		db,
		accountServiceMock,
		fax.FaxSvcUser{},
		hrs.HlthRecSvcUser{},
		nil, // org svc
		recordService,
		auditService,
		&roiservice.RoiServiceMock{},
		topicSender,
		topicSender,
		region,
		false,
	)
	return service, topicSender, dbmock, auditService, recordService
}
