package physicianaccount

import (
	"context"

	"gitlab.com/pockethealth/coreapi/generated/api"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models/modelphysicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
)

type PhysicianAccountApiServicer interface {
	GetEUnityTokenForShare(ctx context.Context, acctId string, shareId string) (string, error)
	GetShareMetadata(
		ctx context.Context,
		acctId string,
	) ([]coreapi.ShareMetadata, error)
	GetPatientSharedExams(
		ctx context.Context,
		acctId string,
		patientId string,
	) (interface{}, error)
	GetPhysicianAccount(context.Context, string) (accountservice.PhysicianAccount, error)

	PatchPhysician(
		context.Context,
		string,
		string,
		accountservice.PhysicianRequest,
	) error

	PostCreateAccount(context.Context, coreapi.RegisterData) (string, error)
	PostLogin(
		ctx context.Context,
		email string,
		password string,
		ip string,
	) (interface{}, string, error)
	PostLogout(context.Context, string) error
	PostPhysicianLicense(
		context.Context,
		string,
		string,
		accountservice.PhysicianLicenceRequest,
	) error
	PostResetPassword(context.Context, coreapi.PasswordResetInfo) error
	PostResetPasswordInit(context.Context, string) error
	PostVerifyAccount(context.Context, accountservice.Verification) error
	PostVerifyPhysicianNotificationMethod(
		context.Context,
		string,
		string,
		accountservice.PhysicianNotificationRequest,
	) error

	PutExtendShare(ctx context.Context, acctId string, shareId string) error
	SearchRecords(
		ctx context.Context,
		acctId string,
		searchParams PhysicianRecordSearchParameters,
	) (string, error)
	RequestRecords(
		ctx context.Context,
		acctId string,
		request PhysicianRecordRequest,
	) ([]modelphysicianaccount.StudyUploadResponse, error)
	GetRecordStreamingStudiesWithUploadStatus(
		ctx context.Context,
		physicianAccountID string,
	) ([]api.PhysicianRecordUploadStatus, error)
	SubmitStudyAccessVerification(
		ctx context.Context,
		physicianAccountID string,
		studyUID string,
		providerID int64,
		shareID string,
	) error
}
