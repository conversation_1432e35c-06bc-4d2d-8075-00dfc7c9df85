package physicianaccount

import "strconv"

type PhysicianRecordSearchEvent struct {
	QueryId            string                      `json:"queryId"`
	PhysicianAccountId string                      `json:"physicianAccountId"`
	ProviderId         string                      `json:"providerId"`
	Query              RecordSearchQueryParameters `json:"query"`
	Region             string                      `json:"region"`
}

func (r *PhysicianRecordSearchEvent) IsDefault() bool {
	return *r == PhysicianRecordSearchEvent{}
}

func (r *PhysicianRecordSearchEvent) ApplicationProperties() map[string]any {
	return map[string]any{
		"region":     r.Region,
		"providerId": r.ProviderId,
	}
}

func (p *PhysicianRecordSearchEvent) IsValid() bool {
	return p.QueryId != "" &&
		p.PhysicianAccountId != "" &&
		p.ProviderId != "" &&
		p.ProviderId != "0" &&
		p.Query.IsValid()
}

func NewPhysicianRecordsSearchEvent(
	accountId string,
	queryId string,
	searchParams PhysicianRecordSearchParameters,
	region string,
) *PhysicianRecordSearchEvent {
	return &PhysicianRecordSearchEvent{
		PhysicianAccountId: accountId,
		QueryId:            queryId,
		ProviderId:         strconv.Itoa(int(searchParams.ProviderId)),
		Query:              searchParams.Query,
		Region:             region,
	}
}
