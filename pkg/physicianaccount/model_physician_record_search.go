package physicianaccount

import (
	"time"
)

type PhysicianRecordSearchParameters struct {
	ProviderId int64                       `json:"providerId"`
	Query      RecordSearchQueryParameters `json:"query"`
}

func (p *PhysicianRecordSearchParameters) IsValid() bool {
	return p.ProviderId != 0 && p.Query.IsValid()
}

type RecordSearchQueryParameters struct {
	FirstName      string `json:"firstName"`
	LastName       string `json:"lastName"`
	DateOfBirth    string `json:"dateOfBirth"`              // date in format RFC3339 (yyyy-mm-dd)
	StudyStartDate string `json:"studyStartDate,omitempty"` // date in format RFC3339 (yyyy-mm-dd)
	StudyEndDate   string `json:"studyEndDate,omitempty"`   // date in format RFC3339 (yyyy-mm-dd)
}

func (p *RecordSearchQueryParameters) IsValid() bool {
	// Validate search parameters for at least 2 out of 3 identifying
	// pieces of information (first name, last name, and date of birth).
	if !hasRequiredNonEmptyParams([]string{p.FirstName, p.LastName, p.DateOfBirth}, 2) {
		return false
	}
	// if date of birth is set, it needs to be date in format RFC3339
	if p.DateOfBirth != "" {
		if _, err := time.Parse("2006-01-02", p.DateOfBirth); err != nil {
			return false
		}
	}
	// if study start date is set, it needs to be date in format RFC3339
	if p.StudyStartDate != "" {
		if _, err := time.Parse("2006-01-02", p.StudyStartDate); err != nil {
			return false
		}
	}
	// if study start date is set, it needs to be date in format RFC3339
	if p.StudyEndDate != "" {
		if _, err := time.Parse("2006-01-02", p.StudyEndDate); err != nil {
			return false
		}
	}
	return true
}

// hasRequiredNonEmptyParams takes a slice of strings and returns true if there
// are at least N non-empty search params.
func hasRequiredNonEmptyParams(params []string, n int) bool {
	count := 0
	for _, p := range params {
		if p != "" {
			count++
			if count >= n {
				return true
			}
		}
	}
	return false
}
