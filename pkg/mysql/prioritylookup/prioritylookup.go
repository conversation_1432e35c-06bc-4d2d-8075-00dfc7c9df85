package mysql

import (
	"context"
	"database/sql"
)

func InsertIntoPriorityLookupAccession(ctx context.Context, db *sql.DB, enrollmentID int64, accountID string, accession string, token string) error {
	const query = `
        INSERT IGNORE INTO priority_lookup_accession (enrollment_id, account_id, accession, token) 
        VALUES (?, ?, ?, ?)
    `
	_, err := db.ExecContext(ctx, query, enrollmentID, accountID, accession, token)
	if err != nil {
		return err
	}
	return nil
}

func MarkPriorityLookupAccessionAsCompleted(ctx context.Context, db *sql.DB, acctId string, accession string) error {
	const query = `
        UPDATE priority_lookup_accession 
        SET completed_timestamp = CURRENT_TIMESTAMP 
        WHERE accession = ? 
		  AND account_id = ?
		  AND completed_timestamp is NULL
    `
	_, err := db.ExecContext(ctx, query, accession, acctId)
	if err != nil {
		return err
	}
	return nil
}

func CheckIncompleteLookup(ctx context.Context, db *sql.DB, acctId string, accession string) (bool, error) {
	const query = `
        SELECT 1
        FROM priority_lookup_accession 
        WHERE accession = ? 
		  AND account_id = ?
          AND completed_timestamp IS NULL
		LIMIT 1;
    `

	var exists int
	err := db.QueryRowContext(ctx, query, accession, acctId).Scan(&exists)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
