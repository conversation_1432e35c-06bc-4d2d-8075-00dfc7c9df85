package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func CreateAccountNotification(
	ctx context.Context,
	db *sql.DB,
	id string,
	acctId string,
	name coreapi.NotificationType,
	patientId string,
	data *coreapi.NotificationData,
) (notification coreapi.Notification, err error) {
	var dataJson []byte
	if data != nil {
		dataJson, err = json.Marshal(data)
		if err != nil {
			return coreapi.Notification{}, err
		}
	} else {
		dataJson = []byte{}
	}
	nullablePatientId := sql.NullString{}
	if patientId != "" {
		nullablePatientId = sql.NullString{
			Valid:  true,
			String: patientId,
		}
	}

	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO user_notifications (id, account_id, name, is_read, patient_id, data) VALUES (?,?,?,?,?,?)",
		id,
		acctId,
		name,
		0,
		nullablePatientId,
		dataJson,
	)
	if err != nil {
		return coreapi.Notification{}, err
	}
	notification = coreapi.Notification{
		Id:   id,
		Type: name,
		Read: false,
	}
	return notification, nil
}

// GetUserNotifications gets the user notifications based on the account id.
func GetAllAccountNotifications(
	ctx context.Context,
	db *sql.DB,
	acctId string,
) (userNotification coreapi.UserNotifications, err error) {
	userNotification = coreapi.UserNotifications{}
	notifications := []coreapi.Notification{}
	var args = []interface{}{acctId}
	var notifPatientId, dataStr sql.NullString
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		`SELECT un.id, un.name, un.is_read, un.patient_id , un.created_timestamp, un.data
			FROM user_notifications un
			WHERE un.account_id = ?
			ORDER BY un.created_timestamp DESC;`,
		args...)
	if err != nil {
		return userNotification, err
	}
	defer rows.Close()
	if rows.Next() {
		for ok := true; ok; ok = rows.Next() {
			notification := coreapi.Notification{PatientId: ""}
			err := rows.Scan(&notification.Id, &notification.Type, &notification.Read, &notifPatientId, &notification.CreatedDate, &dataStr)
			if err != nil {
				return userNotification, err
			}
			if notifPatientId.Valid {
				notification.PatientId = notifPatientId.String
			}
			if dataStr.Valid && dataStr.String != "" {
				data := coreapi.NotificationData{}
				err = json.Unmarshal([]byte(dataStr.String), &data)
				if err != nil {
					return coreapi.UserNotifications{}, err
				}
				notification.Data = &data
			}
			notifications = append(notifications, notification)
		}
	}
	userNotification.Notifications = notifications
	return userNotification, nil
}

func CountNotificationsForAccountByType(ctx context.Context, db *sql.DB, accountId string, notification coreapi.NotificationType) (int, error) {
	notifCount := 0
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT count(*) from user_notifications WHERE account_id = ? AND name = ?",
		[]interface{}{&notifCount},
		accountId,
		notification,
	)

	if err != nil {
		return 0, err
	}

	return notifCount, nil
}

func GetNotificationsForAccountByType(ctx context.Context, db *sql.DB, accountId string, notificationType coreapi.NotificationType) (coreapi.UserNotifications, error) {
	userNotifications := coreapi.UserNotifications{}
	notifications := []coreapi.Notification{}
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT un.id, un.is_read, un.patient_id, un.created_timestamp, un.data from user_notifications un WHERE account_id = ? AND name = ? ORDER BY un.created_timestamp DESC",
		accountId,
		notificationType,
	)

	if err != nil {
		return coreapi.UserNotifications{}, err
	}
	var notifPatientId, dataNullStr sql.NullString

	defer rows.Close()
	for rows.Next() {
		notification := coreapi.Notification{PatientId: ""}
		err := rows.Scan(&notification.Id, &notification.Read, &notifPatientId, &notification.CreatedDate, &dataNullStr)
		if err != nil {
			return coreapi.UserNotifications{}, err
		}
		if notifPatientId.Valid {
			notification.PatientId = notifPatientId.String
		}
		if dataNullStr.Valid && dataNullStr.String != "" {
			data := coreapi.NotificationData{}
			err = json.Unmarshal([]byte(dataNullStr.String), &data)
			if err != nil {
				return coreapi.UserNotifications{}, fmt.Errorf("unable to unmarshal notification data string %w", err)
			}
			notification.Data = &data
		}
		notifications = append(notifications, notification)
	}
	userNotifications.Notifications = notifications
	return userNotifications, nil
}

func SetNotificationReadById(ctx context.Context, db *sql.DB, accountId, notificationId string) (err error) {
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE user_notifications un SET un.is_read=? WHERE id=? AND account_id = ?",
		1,
		notificationId,
		accountId,
	)
	if err != nil {
		return err
	}
	return nil
}

func FilterNotificationsByUnread(
	userNotification coreapi.UserNotifications,
) []coreapi.Notification {
	unreadNotifications := []coreapi.Notification{}
	allNotifications := userNotification.Notifications
	for i := range allNotifications {
		if !allNotifications[i].Read {
			unreadNotifications = append(unreadNotifications, allNotifications[i])
		}
	}
	return unreadNotifications

}

func DeleteNotificationById(ctx context.Context, db *sql.DB, accountId, notificationId string) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"DELETE FROM user_notifications WHERE id = ? AND account_id = ?",
		notificationId,
		accountId,
	)
	return err
}
