package mysql

import (
	"context"
	"database/sql"
	"fmt"
)

func QueryOrgIDAndMRNBySSOToken(ctx context.Context, db *sql.DB, token string) (int64, string, error) {
	const query = `
		SELECT org_id, mrn 
		FROM sso_tokens 
		WHERE token = ?
		  AND expiry > NOW()
	`
	var orgID sql.NullInt64
	var mrn sql.NullString
	err := db.QueryRowContext(ctx, query, token).Scan(&orgID, &mrn)
	if err != nil {
		return 0, "", err
	}
	if !orgID.Valid || !mrn.Valid || orgID.Int64 == 0 || mrn.String == "" {
		return 0, "", fmt.Errorf("missing one of orgID: %d, mrn: %s", orgID.Int64, mrn.String)
	}
	return orgID.Int64, mrn.String, nil
}
