//go:build integration
// +build integration

package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

// inserts a new entry into table physician_study_permissions
func InsertStudyPermission(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) {
	_, err := db.Exec(
		`INSERT INTO 
		physician_study_permissions (physician_account_id, study_uid, provider_id) 
		VALUES (?, ?, ?)`,
		physicianAccountID, studyUID, providerID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table physician_study_permissions
func DeleteStudyPermission(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) {
	_, err := db.Exec(
		`DELETE FROM physician_study_permissions
		WHERE physician_account_id=? AND study_uid=? AND provider_id=?`,
		physicianAccountID, studyUID, providerID,
	)
	assert.NoError(t, err)
}

// inserts a new entry into table unique_study_index
func InsertStudyIndex(
	t *testing.T,
	db *sql.DB,
	studyUID string,
	providerID int64,
	examUUID string,
) {
	_, err := db.Exec(
		`INSERT INTO unique_study_index (study_uid, provider_id, exam_id) VALUES (?, ?, ?)`,
		studyUID, providerID, examUUID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table unique_study_index
func DeleteStudyIndex(
	t *testing.T,
	db *sql.DB,
	studyUID string,
	providerID int64,
	examUUID string,
) {
	_, err := db.Exec(
		`DELETE FROM unique_study_index 
		WHERE study_uid=? AND provider_id=? AND exam_id=?`,
		studyUID, providerID, examUUID,
	)
	assert.NoError(t, err)
}

// inserts a new entry into table unique_instance_index
func InsertInstanceIndex(
	t *testing.T,
	db *sql.DB,
	providerID int64,
	studyUID string,
	instanceUID string,
	objectID string,
) {
	_, err := db.Exec(
		`INSERT INTO unique_instance_index (provider_id, study_uid, instance_uid, object_id) VALUES (?, ?, ?, ?)`,
		providerID,
		studyUID,
		instanceUID,
		objectID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table unique_instance_index
func DeleteInstanceIndex(
	t *testing.T,
	db *sql.DB,
	providerID int64,
	studyUID string,
	instanceUID string,
	objectID string,
) {
	_, err := db.Exec(
		`DELETE FROM unique_instance_index
		WHERE provider_id=? AND study_UID=? AND instance_uid=? AND object_id=?`,
		providerID, studyUID, instanceUID, objectID,
	)
	assert.NoError(t, err)
}

// inserts a new entry into table unique_report_index
func InsertReportIndex(
	t *testing.T,
	db *sql.DB,
	studyUID string,
	providerID int64,
	objectID string,
) {
	_, err := db.Exec(
		`INSERT INTO unique_report_index (study_uid, provider_id, object_id) VALUES (?, ?, ?)`,
		studyUID, providerID, objectID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table unique_report_index
func DeleteReportIndex(
	t *testing.T,
	db *sql.DB,
	studyUID string,
	providerID int64,
) {
	_, err := db.Exec(
		`DELETE FROM unique_report_index 
		WHERE study_uid=? AND provider_id=?`,
		studyUID, providerID,
	)
	assert.NoError(t, err)
}
