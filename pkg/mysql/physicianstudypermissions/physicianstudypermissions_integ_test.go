//go:build integration
// +build integration

package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestGetExamUUIDForRecordStreamingStudy(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("returns examUUID for record streaming study if study index exists", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		examUUID := phtestutils.GenerateRandomString(t, 10)

		InsertStudyIndex(t, db, studyUID, providerID, examUUID)
		t.Cleanup(func() {
			DeleteStudyIndex(t, db, studyUID, providerID, examUUID)
		})

		result, err := GetExamUUIDForRecordStreamingStudy(db, studyUID, providerID)
		assert.NoError(t, err)
		assert.Equal(t, examUUID, result)
	})
	t.Run("fails if no study index exists", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)

		result, err := GetExamUUIDForRecordStreamingStudy(db, studyUID, providerID)
		assert.Error(t, err)
		assert.ElementsMatch(t, "", result)
	})
}

func TestGetStudyUIDAndProviderIDByExamUUID(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run(
		"return study UID and provider ID for record streaming study if study index exists",
		func(t *testing.T) {
			providerID := phtestutils.GenerateRandomInt64(t)
			studyUID := phtestutils.GenerateRandomString(t, 10)
			examUUID := phtestutils.GenerateRandomString(t, 10)

			InsertStudyIndex(t, db, studyUID, providerID, examUUID)
			t.Cleanup(func() {
				DeleteStudyIndex(t, db, studyUID, providerID, examUUID)
			})

			studyUIDResult, providerIDResult, err := GetStudyUIDAndProviderIDByExamUUID(
				db,
				examUUID,
			)
			assert.NoError(t, err)
			assert.Equal(t, studyUID, studyUIDResult)
			assert.Equal(t, providerID, providerIDResult)
		},
	)
	t.Run("fails if no study index exists", func(t *testing.T) {
		examUUID := phtestutils.GenerateRandomString(t, 10)

		studyUIDResult, providerIDResult, err := GetStudyUIDAndProviderIDByExamUUID(db, examUUID)
		assert.Error(t, err)
		assert.Equal(t, "", studyUIDResult)
		assert.Equal(t, int64(0), providerIDResult)
	})
}

func TestGetObjectIDByInstanceProviderStudy(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("return object ID for instance UID and Provider ID", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		instanceUID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)

		InsertInstanceIndex(t, db, providerID, studyUID, instanceUID, objectID)
		t.Cleanup(func() {
			DeleteInstanceIndex(t, db, providerID, studyUID, instanceUID, objectID)
		})

		objectIDResult, err := GetObjectIDByInstanceProviderStudy(
			db,
			instanceUID,
			providerID,
			studyUID,
		)
		assert.NoError(t, err)
		assert.Equal(t, objectID, objectIDResult)
	})
	t.Run("fails if no instance index exists", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		instanceUID := phtestutils.GenerateRandomString(t, 10)

		objectIDResult, err := GetObjectIDByInstanceProviderStudy(
			db,
			instanceUID,
			providerID,
			studyUID,
		)
		assert.Error(t, err)
		assert.Equal(t, "", objectIDResult)
	})
}

func TestGetStudyIndexByPhysicianAccountId(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("should return list of unique study indices", func(t *testing.T) {
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)
		providerID1 := phtestutils.GenerateRandomInt64(t)
		studyUID1 := phtestutils.GenerateRandomString(t, 10)
		examID1 := phtestutils.GenerateRandomString(t, 10)
		studyUID2 := phtestutils.GenerateRandomString(t, 10)
		examID2 := phtestutils.GenerateRandomString(t, 10)
		providerID2 := phtestutils.GenerateRandomInt64(t)
		studyUID3 := phtestutils.GenerateRandomString(t, 10)
		examID3 := phtestutils.GenerateRandomString(t, 10)

		SetupDatabaseEntry(t, db, physicianAccountID, studyUID1, providerID1, examID1)
		SetupDatabaseEntry(t, db, physicianAccountID, studyUID2, providerID1, examID2)
		SetupDatabaseEntry(t, db, physicianAccountID, studyUID3, providerID2, examID3)

		t.Cleanup(func() {
			CleanupDatabaseEntry(t, db, physicianAccountID, studyUID1, providerID1, examID1)
			CleanupDatabaseEntry(t, db, physicianAccountID, studyUID2, providerID1, examID2)
			CleanupDatabaseEntry(t, db, physicianAccountID, studyUID3, providerID2, examID3)
		})

		expectedResult := []coreapi.StudyIndexEntry{
			{
				ProviderID: providerID1,
				StudyUID:   studyUID1,
				ExamID:     examID1,
			},
			{
				ProviderID: providerID1,
				StudyUID:   studyUID2,
				ExamID:     examID2,
			},
			{
				ProviderID: providerID2,
				StudyUID:   studyUID3,
				ExamID:     examID3,
			},
		}

		result, err := GetStudyIndexByPhysicianAccountId(db, physicianAccountID)
		assert.NoError(t, err)
		assert.ElementsMatch(t, expectedResult, result)
	})
	t.Run("should return empty list if no matches are found", func(t *testing.T) {
		physicianAccountID := phtestutils.GenerateRandomString(t, 10)

		expectedResult := []coreapi.StudyIndexEntry{}

		result, err := GetStudyIndexByPhysicianAccountId(db, physicianAccountID)

		assert.NoError(t, err)
		assert.ElementsMatch(t, expectedResult, result)
	})
}

func TestHasReportIndex(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("should return true if report index exists", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)
		objectID := phtestutils.GenerateRandomString(t, 10)
		InsertReportIndex(t, db, studyUID, providerID, objectID)

		t.Cleanup(func() {
			DeleteReportIndex(t, db, studyUID, providerID)
		})

		result, err := HasReportIndex(db, studyUID, providerID)
		assert.NoError(t, err)
		assert.Equal(t, true, result)
	})
	t.Run("should return false if report index does not exist", func(t *testing.T) {
		providerID := phtestutils.GenerateRandomInt64(t)
		studyUID := phtestutils.GenerateRandomString(t, 10)

		result, err := HasReportIndex(db, studyUID, providerID)
		assert.NoError(t, err)
		assert.Equal(t, false, result)
	})
}

func SetupDatabaseEntry(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
	examID string,
) {
	InsertStudyPermission(t, db, physicianAccountID, studyUID, providerID)
	InsertStudyIndex(t, db, studyUID, providerID, examID)
}

func CleanupDatabaseEntry(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
	examID string,
) {
	DeleteStudyPermission(t, db, physicianAccountID, studyUID, providerID)
	DeleteStudyIndex(t, db, studyUID, providerID, examID)
}
