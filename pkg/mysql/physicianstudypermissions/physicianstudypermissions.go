package mysql

import (
	"database/sql"
	"errors"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// CreatePhysicianStudyAccess creates an association between a physician -> study
// This grants a physician permission to view this study in their physician account
func CreatePhysicianStudyAccess(
	db *sql.DB,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) error {
	_, err := db.Exec(
		`INSERT IGNORE INTO physician_study_permissions 
		(physician_account_id, study_uid, provider_id) VALUES (?, ?, ?)`,
		physicianAccountID,
		studyUID,
		providerID,
	)
	return err
}

// GetRecordStreamingStudyIDForObject returns the examUUID
// of a study with a given studyUID and providerID.
//
// It only considers studies shared via record streaming.
func GetExamUUIDForRecordStreamingStudy(
	db *sql.DB,
	studyUID string,
	providerID int64,
) (string, error) {
	var examUUID string
	err := db.QueryRow(
		`SELECT exam_id
		FROM unique_study_index 
		WHERE study_uid=? AND provider_id=?`,
		studyUID, providerID,
	).Scan(&examUUID)
	if err != nil {
		return "", err
	}
	return examUUID, nil
}

// GetStudyUIDAndProviderIDByExamUUID returns a studyUID and providerID given
// an examUUID
func GetStudyUIDAndProviderIDByExamUUID(
	db *sql.DB,
	examUUID string,
) (string, int64, error) {
	var studyUID string
	var providerID int64
	err := db.QueryRow(
		`SELECT study_uid, provider_id
		FROM unique_study_index
		WHERE exam_id=?`,
		examUUID,
	).Scan(&studyUID, &providerID)
	if err != nil {
		return "", int64(0), err
	}
	return studyUID, providerID, nil
}

// GetObjectIDByInstanceProviderStudy returns the objectID for a given instanceUID,
// providerID, and studyUID
func GetObjectIDByInstanceProviderStudy(
	db *sql.DB,
	instanceUID string,
	providerID int64,
	studyUID string,
) (string, error) {
	var objectID string
	err := db.QueryRow(
		`SELECT object_id
		FROM unique_instance_index
		WHERE instance_uid=? AND provider_id=? AND study_uid=?`,
		instanceUID, providerID, studyUID,
	).Scan(&objectID)
	if err != nil {
		return "", err
	}
	return objectID, nil
}

// GetStudyIndexByPhysicianAccountId returns a list of study indexes
// that a physician has access to.
// It only considers studies shared via record streaming.
func GetStudyIndexByPhysicianAccountId(
	db *sql.DB,
	physicianAccountID string,
) ([]coreapi.StudyIndexEntry, error) {
	// get all uploaded
	rows, err := db.Query(
		`SELECT usi.study_uid, usi.provider_id, usi.exam_id
		FROM physician_study_permissions psp
		INNER JOIN unique_study_index usi
		ON psp.study_uid=usi.study_uid AND psp.provider_id=usi.provider_id
		WHERE physician_account_id=?`,
		physicianAccountID,
	)
	if err != nil {
		return []coreapi.StudyIndexEntry{}, err
	}
	defer rows.Close()
	result := []coreapi.StudyIndexEntry{}
	for rows.Next() {
		var entry coreapi.StudyIndexEntry
		err = rows.Scan(&entry.StudyUID, &entry.ProviderID, &entry.ExamID)
		if err != nil {
			return []coreapi.StudyIndexEntry{}, err
		}
		result = append(result, entry)
	}
	return result, nil
}

// HasStudyIndex returns true if there is an entry in unique_study_index
// for the given studyUID and providerID.
// It only considers studies shared via record streaming.
func HasStudyIndex(
	db *sql.DB,
	studyUID string,
	providerID int64,
) (bool, error) {
	var count int
	err := db.QueryRow(
		`SELECT COUNT(*)
		FROM unique_study_index 
		WHERE study_uid=? AND provider_id=?`,
		studyUID,
		providerID,
	).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// HasReportIndex returns true if there is an entry in unique_report_index
// for the given studyUID and providerID.
// It only considers studies shared via record streaming.
func HasReportIndex(
	db *sql.DB,
	studyUID string,
	providerID int64,
) (bool, error) {
	var hasReport bool
	err := db.QueryRow(
		`SELECT 
			true
		FROM 
			unique_report_index 
		WHERE 
			study_uid=? AND provider_id=?
		LIMIT 1`,
		studyUID,
		providerID,
	).Scan(&hasReport)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil
		}
		return false, err
	}
	return hasReport, nil
}

// GetRecordStreamingStudyIDForObject returns the studyUID and providerId
// of an object with the given objectID.
// It only considers studies shared via record streaming.
func GetRecordStreamingStudyIDForObject(
	db *sql.DB,
	objectID string,
) (coreapi.RecordStreamingStudyID, error) {
	var studyID coreapi.RecordStreamingStudyID
	err := db.QueryRow(
		`SELECT usi.study_uid, usi.provider_id
			FROM object_mappings om
			INNER JOIN unique_study_index usi
			ON om.exam_uuid = usi.exam_id
			WHERE om.object_id=?`,
		objectID,
	).Scan(&studyID.StudyUID, &studyID.ProviderID)
	if err != nil {
		return coreapi.RecordStreamingStudyID{}, err
	}
	return studyID, nil
}
