package mysql

// represents a row in the upload_status_log table
type UploadStep string

const (
	StepInitialized  UploadStep = "Initialized"
	StepEnqueued     UploadStep = "Enqueued"
	StepUploaded     UploadStep = "Uploaded"
	StepNotAvailable UploadStep = "Not Available" // used e.g. for studies that don't have reports
)

// type DICOMLevelType describes the dicom level of an entry in upload_status_log table
type DICOMLevelType string

const (
	DICOMLevelTypeInstance DICOMLevelType = "Instance"
	DICOMLevelTypeSeries   DICOMLevelType = "Series"
	DICOMLevelTypeReport   DICOMLevelType = "Report"
	DICOMLevelTypeStudy    DICOMLevelType = "Study"
)
