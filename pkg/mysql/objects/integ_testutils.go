//go:build integration
// +build integration

package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

// inserts a new object entry into table objects
func InsertObject(
	t *testing.T,
	db *sql.DB,
	objectID string,
	isReport bool,
) {
	_, err := db.Exec(
		`INSERT INTO objects (object_id, is_report) VALUES (?, ?)`,
		objectID, isReport,
	)
	assert.NoError(t, err)
}

// deletes an entry from table objects
func DeleteObject(
	t *testing.T,
	db *sql.DB,
	objectID string,
) {
	_, err := db.Exec(
		`DELETE FROM objects 
		WHERE object_id=?`,
		objectID,
	)
	assert.NoError(t, err)
}

// inserts a new entry into table object_mappings
func InsertObjectMapping(
	t *testing.T,
	db *sql.DB,
	examUUID string,
	objectID string,
	size int64,
) {
	_, err := db.Exec(
		`INSERT INTO object_mappings (exam_uuid, object_id, size) VALUES (?, ?, ?)`,
		examUUID, objectID, size,
	)
	assert.NoError(t, err)
}

// deletes an entry from table object_mappings
func DeleteObjectMapping(
	t *testing.T,
	db *sql.DB,
	examUUID string,
	objectID string,
) {
	_, err := db.Exec(
		`DELETE FROM object_mappings 
		WHERE exam_uuid=? AND object_id=?`,
		examUUID, objectID,
	)
	assert.NoError(t, err)
}
