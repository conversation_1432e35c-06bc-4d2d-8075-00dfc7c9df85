package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	providers "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	sqlHandlers "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	sqlShareObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type UserObjects struct {
	Images  []string
	Reports []string
}

func GetScanId(ctx context.Context, db *sql.DB, objectId string) (scanId string, err error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT scan_id FROM objects WHERE object_id=?",
		objectId,
	)
	if err != nil {
		return "", err
	}
	defer rows.Close()
	rows.Next()
	err = rows.Scan(&scanId)
	if err != nil {
		return "", err
	}
	return scanId, nil
}

func objectBelongsToAccount(
	ctx context.Context,
	db *sql.DB,
	objectID string,
	accountID string,
) (bool, error) {
	var legacyAccountID sql.NullString

	// legacy transfers account query by object ID
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT account_id FROM object_mappings o LEFT JOIN exams e on o.exam_uuid = e.uuid WHERE o.object_id=?",
		[]interface{}{&legacyAccountID},
		objectID,
	)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}

	if legacyAccountID.Valid && legacyAccountID.String == accountID {
		return true, nil
	}

	// record streaming check - existence of report or instance w/ patient permission
	var patientPermissionsAccountID sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`select
			pasp.patient_account_id
		from
			patient_account_study_permissions pasp
		left join unique_instance_index uii on
			uii.provider_id = pasp.provider_id
			and uii.study_uid = pasp.study_uid
		left join unique_report_index uri on
			uri.provider_id = pasp.provider_id
			and uri.study_uid = pasp.study_uid
		where
			pasp.patient_account_id = ?
			and (
				uii.object_id = ? or
				uri.object_id = ?
			)`,
		[]interface{}{&patientPermissionsAccountID}, accountID, objectID, objectID)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}

	if patientPermissionsAccountID.Valid && patientPermissionsAccountID.String == accountID {
		return true, nil
	}

	return false, nil
}

func GetImageMetadata(
	ctx context.Context,
	db *sql.DB,
	imageId string,
	orgsvcClient orgservice.OrgService,
) (coreapi.ImageMetadata, error) {
	// currently the image metadata object actual only contains data at the exam level (probably due to limitations from mongo).
	md := coreapi.ImageMetadata{}
	var sqlOriginId sql.NullInt64
	var source string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		fmt.Sprintf(
			`SELECT e.activated, e.description, e.date, s.modality, e.exam_uid, e.body_part, s.origin_id, s.source, e.referring_physician
	FROM object_mappings om
	JOIN exams e ON om.exam_uuid = e.uuid
	JOIN scans s ON s.scan_id = e.transfer_id
	WHERE om.object_id = ? AND %s`,
			exams.ExamNotRevokedWhere,
		),
		[]interface{}{
			&md.Activated,
			&md.Description,
			&md.Date,
			&md.Type,
			&md.ExamId,
			&md.BodyPart,
			&sqlOriginId,
			&source,
			&md.ReferringPhysician,
		},
		imageId,
	)
	if err != nil {
		return md, err
	}

	if source != exams.CDUpload {
		// lookup clinic name from origin id.
		clinic, err := providers.GetClinicDisplayInfoFromOriginId(
			ctx,
			db,
			sqlOriginId.Int64,
			orgsvcClient,
		)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Info("failed to retrieve clinic info")
			return md, err
		}

		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"report_id": imageId,
				"origin_id": sqlOriginId.Int64,
			}).WithError(err).Error("failed to retrieve clinic name")
			md.ClinicName = "Unknown"
			md.OrgName = "Unknown"
		} else {
			md.ClinicName = clinic.Name
			md.OrgName = clinic.OrgName
		}
	} else {
		md.ClinicName = "Unknown"
	}

	md.Type = dcmtools.ParseType(md.Type)
	md.Date = dcmtools.ParseDate(md.Date)
	if md.ReferringPhysician != "" {
		md.ReferringPhysician = "Dr. " + dcmtools.ParseName(ctx, md.ReferringPhysician)
	}
	return md, nil
}

func GetReportMetadata(
	ctx context.Context,
	db *sql.DB,
	reportId string,
	orgsvcClient orgservice.OrgService,
) (coreapi.Report, error) {
	// reports have more metadata than images because Drag n Drop reports don't really have an exam, so we're pulling that detail.
	rep := coreapi.Report{}
	var sqlOriginId sql.NullInt64
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		fmt.Sprintf(
			`SELECT e.activated, e.sex, e.patient_name, e.dob, s.uploaded, om.size, s.origin_id, e.referring_physician, o.protocol
	FROM object_mappings om
	JOIN exams e ON om.exam_uuid = e.uuid
	JOIN scans s ON s.scan_id = e.transfer_id
	JOIN objects o ON o.object_id = om.object_id
	WHERE om.object_id = ? AND %s`,
			exams.ExamNotRevokedWhere,
		),
		[]interface{}{
			&rep.Activated,
			&rep.Sex,
			&rep.PatientName.DicomName,
			&rep.Dob,
			&rep.UploadTime,
			&rep.Size,
			&sqlOriginId,
			&rep.ReferringPhysician,
			&rep.Protocol,
		},
		reportId,
	)
	if err != nil {
		return rep, err
	}

	// lookup clinic id from origin id.
	if sqlOriginId.Valid {
		clinic, err := providers.GetClinicDisplayInfoFromOriginId(
			ctx,
			db,
			sqlOriginId.Int64,
			orgsvcClient,
		)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"report_id": reportId,
				"origin_id": sqlOriginId.Int64,
			}).WithError(err).Info("failed to retrieve clinic info")
			rep.ClinicName = "Unknown"
			rep.OrgName = "Unknown"
		} else {
			rep.ClinicName = clinic.Name
			rep.Definitions = true
			rep.OrgName = clinic.OrgName
		}
	} else {
		rep.ClinicName = "Unknown"
	}
	if rep.ReferringPhysician != "" {
		rep.ReferringPhysician = "Dr. " + dcmtools.ParseName(ctx, rep.ReferringPhysician)
	}
	rep.ReportId = reportId

	f, l := dcmtools.ParseFirstLastName(ctx, rep.PatientName.DicomName)
	rep.PatientName.FirstAndMiddleName = f
	rep.PatientName.LastName = l
	rep.Dob = dcmtools.ParseDob(rep.Dob, "/")
	return rep, nil
}

func IsActivated(ctx context.Context, db *sql.DB, objectId string) bool {
	var activated sql.NullBool // nullbool, just in case the object id is bad
	err := mysqlWithLog.QueryRowAndScan(ctx, db, fmt.Sprintf(`
	SELECT activated
	FROM object_mappings o
	JOIN exams e on o.exam_uuid = e.uuid
	WHERE o.object_id = ? AND %s`, exams.ExamNotRevokedWhere), []interface{}{&activated}, objectId)
	if err != nil || !activated.Valid {
		return false
	}
	return activated.Bool
}

func CanAccessObject(
	ctx context.Context,
	db *sql.DB,
	objId string,
	acctId string,
	shareId string,
) (bool, error) {
	if acctId != "" {
		if physicianAccess, err := physicianCanAccessObject(ctx, db, objId, acctId); physicianAccess &&
			err == nil {
			return true, nil
		}

		belongs, err := objectBelongsToAccount(ctx, db, objId, acctId)
		if err != nil {
			return false, err // not found or db problem
		}
		return belongs, nil
	} else if shareId != "" {
		lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"shareId": shareId,
			"objId":   objId,
		})
		// check if image is part of share
		objectIds, err := sqlShareObjects.GetShareObjectIds(ctx, db, shareId)
		if err != nil {
			lg.WithError(err).Error("share image not found in sql")
			return false, err
		}
		found := false
		for _, id := range objectIds {
			if id == objId {
				found = true
				break
			}
		}

		return found, nil
	} else if auth.IsValidatedWithSignedRequest(ctx) {
		return true, nil
	} else {
		return false, nil
	}
}

func physicianCanAccessObject(
	ctx context.Context,
	db *sql.DB,
	objId string,
	acctId string,
) (bool, error) {
	// check if this account ID belongs to a physician
	physicianHandlerId, err := sqlHandlers.LookupHandlerId(
		ctx,
		db,
		coreapi.PhysicianAccount,
		acctId,
	)
	if err != nil {
		return false, nil
	}

	// accountId belongs to a physician
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"objId":        objId,
		"acctId":       acctId,
		"account_type": coreapi.PhysicianAccount,
	})

	// check if any shares exist that this object is in that the physician has access to
	objShareIds, err := sqlShareObjects.GetObjectShareIds(ctx, db, objId)
	if err != nil {
		lg.WithError(err).Error("unauthorized physician access: object does not exist in a share")
		return false, err
	}
	canView, err := sqlHandlers.HandlerCanViewAtLeastOneShare(
		ctx,
		db,
		physicianHandlerId,
		objShareIds,
	)
	// physicians can access reports inside of shares they can view
	if err != nil {
		lg.WithError(err).
			Error("unauthorized physician access: unable to check if handler can view share")
		return false, err
	}

	return canView, nil
}

func getObjectBaseQuery(seriesUid string) string {
	var seriesUidQuery string
	if seriesUid != "" {
		seriesUidQuery = "AND om.series_uid = ?"
	}
	return `FROM object_mappings om
	JOIN objects o on o.object_id = om.object_id
	WHERE om.exam_uuid = ? AND o.is_report = 0
	AND (om.has_pixel_data = 1 OR om.has_pixel_data IS NULL)` + seriesUidQuery
}

// get object id in a series in an exam by row number(offset) order by instance_number (the correct sequence of the images)
// making assumption the seriesUid belongs to an unrevoked exam
func getObjectByOffset(
	ctx context.Context,
	db *sql.DB,
	examUuid string,
	seriesUid string,
	offset int64,
) (string, error) {
	var args []interface{}

	if seriesUid != "" {
		args = []interface{}{examUuid, seriesUid, offset}
	} else {
		args = []interface{}{examUuid, offset}
	}
	var objectId string
	err := mysqlWithLog.QueryRowAndScan(ctx, db, `
	SELECT om.object_id`+getObjectBaseQuery(seriesUid)+
		`ORDER BY o.instance_number ASC LIMIT 1 OFFSET ?`, []interface{}{&objectId}, args...)
	if err != nil {
		return "", err
	}
	return objectId, nil
}

func GetExamUuid(ctx context.Context, db *sql.DB, objectId string) (string, error) {
	var examUuid sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT exam_uuid FROM object_mappings WHERE object_id=?",
		[]interface{}{&examUuid},
		objectId,
	)
	return examUuid.String, err
}

// making assumption the seriesUid belongs to an unrevoked exam
func getObjectsCountInSeries(
	ctx context.Context,
	db *sql.DB,
	examUuid string,
	seriesUid string,
) (int64, error) {
	var args []interface{}

	if seriesUid != "" {
		args = []interface{}{examUuid, seriesUid}
	} else {
		args = []interface{}{examUuid}
	}
	var count int64
	err := mysqlWithLog.QueryRowAndScan(ctx, db,
		`SELECT COUNT(*)`+getObjectBaseQuery(seriesUid), []interface{}{&count}, args...)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func GetMiddleSliceInSeries(
	ctx context.Context,
	db *sql.DB,
	examUuid string,
	seriesUid string,
) (string, error) {
	count, err := getObjectsCountInSeries(ctx, db, examUuid, seriesUid)
	if err != nil {
		return "", err
	}

	midRow := math.Floor(float64(count) / 2)

	objectId, err := getObjectByOffset(ctx, db, examUuid, seriesUid, int64(midRow))
	if err != nil {
		return "", err
	}
	return objectId, nil
}

func GetObjectsInSeries(
	ctx context.Context,
	db *sql.DB,
	examUuid string,
	seriesUid string,
) ([]string, error) {
	var args []interface{}
	if seriesUid != "" {
		args = []interface{}{examUuid, seriesUid}
	} else {
		args = []interface{}{examUuid}
	}

	// NOTE: limit 2000 to avoid loading exceedingly large series into memory.
	// The limit is mostly arbitrary, but most series have < 500 objects at the
	// time of writing (2024-07-11)
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		`SELECT o.object_id `+getObjectBaseQuery(
			seriesUid,
		)+` ORDER BY o.instance_number ASC LIMIT 2000`,
		args...,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var objectIds []string
	for rows.Next() {
		var objectId string
		err := rows.Scan(&objectId)
		if err != nil {
			return nil, fmt.Errorf("failed scanning object id: %w", err)
		}

		objectIds = append(objectIds, objectId)
	}
	return objectIds, nil
}
