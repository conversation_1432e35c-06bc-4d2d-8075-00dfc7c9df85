//go:build integration
// +build integration

package mysql_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/testutils/exams"
	phtestutils "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestCanAccessObject(t *testing.T) {
	db := testutils.SetupTestDB(t)
	ctx := context.Background()

	testCases := []struct {
		desc          string
		setupTestData func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool
	}{
		{
			desc: "returns false if objectID and accountID don't exist",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				return false
			},
		},
		{
			desc: "returns true if accountID has access to objectID for legacy transfer study",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				exam := exams.CreateAndInsertTestExam(t, db, &accountID)
				mysql.InsertObjectMapping(t, db, exam.UUID, objectID, 1)
				t.Cleanup(func() {
					mysql.DeleteObject(t, db, objectID)
				})
				return true
			},
		},
		{
			desc: "returns false if accountID does not have access to objectID for legacy transfer study",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				_ = exams.CreateAndInsertTestExam(t, db, &accountID)
				return false
			},
		},
		{
			desc: "returns true if accountID has access to instance objectID for record streaming study",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				// create exam with a different accountID to
				// simulate one exam with multiple account ID
				// permissions on it
				otherAccountID := phtestutils.GenerateRandomString(t, 10)
				exam := exams.CreateAndInsertTestExam(t, db, &otherAccountID)

				mysql.InsertObjectMapping(t, db, exam.UUID, objectID, 1)
				studyUID := phtestutils.GenerateRandomString(t, 10)
				providerID := phtestutils.GenerateRandomInt64(t)
				_, err := db.ExecContext(
					ctx,
					`INSERT INTO patient_account_study_permissions (patient_account_id, study_uid, provider_id) VALUES (?, ?, ?)`,
					accountID,
					studyUID,
					providerID,
				)
				require.NoError(t, err)
				_, err = db.ExecContext(
					ctx,
					`INSERT INTO unique_study_index (study_uid, provider_id, exam_id) VALUES (?, ?, ?)`,
					studyUID,
					providerID,
					exam.UUID,
				)
				require.NoError(t, err)
				instanceUID := phtestutils.GenerateRandomString(t, 10)
				_, err = db.ExecContext(
					ctx,
					`INSERT INTO unique_instance_index (study_uid, provider_id, instance_uid, object_id) VALUES (?, ?, ?, ?)`,
					studyUID,
					providerID,
					instanceUID,
					objectID,
				)
				require.NoError(t, err)
				t.Cleanup(func() {
					mysql.DeleteObjectMapping(t, db, exam.UUID, objectID)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM patient_account_study_permissions WHERE patient_account_id=? and study_uid=? and provider_id=?`,
						accountID,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM unique_study_index WHERE study_uid=? and provider_id=?`,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM unique_instance_index WHERE study_uid=? and provider_id=? and instance_uid=?`,
						studyUID,
						providerID,
						instanceUID,
					)
					assert.NoError(t, err)
				})
				return true
			},
		},
		{
			desc: "returns true if accountID has access to report objectID for record streaming study",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				// create exam with a different accountID to
				// simulate one exam with multiple account ID
				// permissions on it
				otherAccountID := phtestutils.GenerateRandomString(t, 10)
				exam := exams.CreateAndInsertTestExam(t, db, &otherAccountID)

				mysql.InsertObjectMapping(t, db, exam.UUID, objectID, 1)
				studyUID := phtestutils.GenerateRandomString(t, 10)
				providerID := phtestutils.GenerateRandomInt64(t)
				_, err := db.ExecContext(
					ctx,
					`INSERT INTO patient_account_study_permissions (patient_account_id, study_uid, provider_id) VALUES (?, ?, ?)`,
					accountID,
					studyUID,
					providerID,
				)
				require.NoError(t, err)
				_, err = db.ExecContext(
					ctx,
					`INSERT INTO unique_study_index (study_uid, provider_id, exam_id) VALUES (?, ?, ?)`,
					studyUID,
					providerID,
					exam.UUID,
				)
				require.NoError(t, err)
				_, err = db.ExecContext(
					ctx,
					`INSERT INTO unique_report_index (study_uid, provider_id, object_id) VALUES (?, ?, ?)`,
					studyUID,
					providerID,
					objectID,
				)
				require.NoError(t, err)
				t.Cleanup(func() {
					mysql.DeleteObjectMapping(t, db, exam.UUID, objectID)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM patient_account_study_permissions WHERE patient_account_id=? and study_uid=? and provider_id=?`,
						accountID,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM unique_study_index WHERE study_uid=? and provider_id=?`,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM unique_report_index WHERE study_uid=? and provider_id=?`,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
				})
				return true
			},
		},
		{
			desc: "returns true if accountID has access to instance objectID for record streaming study",
			setupTestData: func(t *testing.T, db *sql.DB, objectID string, accountID string, shareID string) bool {
				// create exam with a different accountID to
				// simulate one exam with multiple account ID
				// permissions on it
				otherAccountID := phtestutils.GenerateRandomString(t, 10)
				exam := exams.CreateAndInsertTestExam(t, db, &otherAccountID)

				mysql.InsertObjectMapping(t, db, exam.UUID, objectID, 1)
				studyUID := phtestutils.GenerateRandomString(t, 10)
				providerID := phtestutils.GenerateRandomInt64(t)
				_, err := db.ExecContext(
					ctx,
					`INSERT INTO patient_account_study_permissions (patient_account_id, study_uid, provider_id) VALUES (?, ?, ?)`,
					accountID,
					studyUID,
					providerID,
				)
				require.NoError(t, err)
				_, err = db.ExecContext(
					ctx,
					`INSERT INTO unique_study_index (study_uid, provider_id, exam_id) VALUES (?, ?, ?)`,
					studyUID,
					providerID,
					exam.UUID,
				)
				require.NoError(t, err)
				t.Cleanup(func() {
					mysql.DeleteObjectMapping(t, db, exam.UUID, objectID)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM patient_account_study_permissions WHERE patient_account_id=? and study_uid=? and provider_id=?`,
						accountID,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
					_, err = db.ExecContext(
						ctx,
						`DELETE FROM unique_study_index WHERE study_uid=? and provider_id=?`,
						studyUID,
						providerID,
					)
					assert.NoError(t, err)
				})
				return false
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			objectID := phtestutils.GenerateRandomString(t, 10)
			accountID := phtestutils.GenerateRandomString(t, 10)
			shareID := phtestutils.GenerateRandomString(t, 10)

			var expectedAccess bool
			if tc.setupTestData != nil {
				expectedAccess = tc.setupTestData(t, db, objectID, accountID, shareID)
			}
			canAccess, err := mysql.CanAccessObject(
				ctx,
				db,
				objectID,
				accountID,
				shareID,
			)
			require.NoError(t, err)
			require.Equal(t, expectedAccess, canAccess)
		})
	}
}
