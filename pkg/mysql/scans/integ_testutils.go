package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

// inserts a new object entry into table scans
func InsertScan(
	t *testing.T,
	db *sql.DB,
	scanID string,
	source string, // share type, "CD", "P2P" or "PS"
) {
	_, err := db.Exec(
		`INSERT INTO scans (scan_id, source) VALUES (?, ?)`,
		scanID,
		source,
	)

	t.Cleanup(func() {
		DeleteScan(t, db, scanID)
	})
	assert.NoError(t, err)
}

// deletes an entry from table scans
func DeleteScan(
	t *testing.T,
	db *sql.DB,
	scanID string,
) {
	_, err := db.Exec(
		`DELETE FROM scans 
		WHERE scan_id=?`,
		scanID,
	)
	assert.NoError(t, err)
}
