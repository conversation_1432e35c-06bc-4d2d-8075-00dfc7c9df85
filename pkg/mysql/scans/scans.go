package mysql

import (
	"context"
	"database/sql"
	"strings"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type Metadata struct {
	UploadDate       string
	StudyDate        string
	Modality         string
	StudyDescription string
	InstitutionName  string
	PatientName      string
	PatientId        string
	Email            string
	OriginId         int64
}

type Transfer struct {
	TransferId string
	Reports    []string
	Images     []string
}

func GetScan(
	ctx context.Context,
	db *sql.DB,
	transferId string,
) (metadata Metadata, err error) {
	var sqlOriginId sql.NullInt64
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT DATE_FORMAT(date,'%Y-%m-%d'), DATE_FORMAT(uploaded,'%b %d, %Y'),modality,description,institution,patient_name,patient_id,origin_id FROM scans WHERE scan_id=? ORDER by uploaded",
		transferId,
	)
	if err != nil {
		return metadata, err
	}
	defer rows.Close()
	rows.Next()
	err = rows.Scan(
		&metadata.StudyDate,
		&metadata.UploadDate,
		&metadata.Modality,
		&metadata.StudyDescription,
		&metadata.InstitutionName,
		&metadata.PatientName,
		&metadata.PatientId,
		&sqlOriginId,
	)
	if err != nil {
		return metadata, err
	}
	if sqlOriginId.Valid {
		metadata.OriginId = sqlOriginId.Int64
	}

	names := strings.Split(metadata.PatientName, "^")
	metadata.PatientName = ""
	for i, name := range names {
		if i == 0 {
			continue
		}
		metadata.PatientName = metadata.PatientName + name + " "
	}
	metadata.PatientName = metadata.PatientName + names[0]
	metadata.PatientName = strings.Title(strings.ToLower(metadata.PatientName))
	err = rows.Close()
	return
}

func GetScanAccountId(ctx context.Context, db *sql.DB, scanId string) (string, error) {
	var acctId sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT e.account_id FROM scans s join exams e on s.scan_id = e.transfer_id
		WHERE scan_id=? ORDER by uploaded limit 1`,
		[]interface{}{&acctId},
		scanId,
	)
	return acctId.String, err
}

// attribute patient id for both exams and patients by transfer id
func AttributePatient(ctx context.Context, db *sql.DB, transferId string, patientId string) error {
	lg := logutils.DebugCtxLogger(ctx)
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		lg.WithError(err).Error("couldn't begin transaction")
		return err
	}
	_, err = tx.ExecContext(
		ctx,
		`UPDATE scans SET ph_patient_id=? WHERE scan_id=?`,
		patientId,
		transferId,
	)
	if err != nil {
		lg.WithError(err).Error("couldn't update ph_patient_id in scans")
		err2 := tx.Rollback()
		if err2 != nil {
			lg.WithError(err2).Error("couldn't rollback")
			return err2
		}
		return err
	}
	_, err = tx.ExecContext(
		ctx,
		`UPDATE exams SET patient_id=? WHERE transfer_id=?`,
		patientId,
		transferId,
	)
	if err != nil {
		lg.WithError(err).Error("couldn't update patient_id in exams")
		err2 := tx.Rollback()
		if err2 != nil {
			lg.WithError(err2).Error("couldn't rollback")
			return err2
		}
		return err
	}
	err = tx.Commit()
	if err != nil {
		lg.WithError(err).Error("failed to commit transaction")
		return err
	}
	return nil
}
