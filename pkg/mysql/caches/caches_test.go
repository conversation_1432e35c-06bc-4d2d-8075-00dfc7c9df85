package caches

import (
	"context"
	"testing"
	"time"
)

func TestCacheStringToInt32(t *testing.T) {

	t.Run(
		"when initialized with expiry length > 0, should not expiry immediately",
		func(t *testing.T) {
			var c CacheStringToInt32
			cacheLength := time.Duration(500) * time.Millisecond
			c.SetCacheLength(cacheLength)

			t0 := time.Now()
			timeout := 1 * time.Second
			c.Initialize(context.Background(), "unit_test_cache")

			for {
				if time.Since(t0) >= timeout {
					break
				}

				if c.IsExpired() {
					break
				}

				time.Sleep(100 * time.Millisecond)
			}

			t1 := time.Now()
			if t1.Sub(t0) < cacheLength {
				t.Errorf(
					"expected cache to expire after at least %s, but expired in %s",
					cacheLength,
					t1.Sub(t0),
				)
			}
		},
	)

	t.Run("when cache initialized, IsInitialized should return true", func(t *testing.T) {
		var c CacheStringToInt32

		if c.<PERSON>Initialized() {
			t.<PERSON><PERSON><PERSON>("expected false, got true")
		}

		c.Initialize(context.Background(), "unit_test_cache")

		if !c.IsInitialized() {
			t.<PERSON>rrorf("expected true, got false")
		}
	})

	t.Run("when value is Set, should be able to Get value", func(t *testing.T) {
		ctx := context.Background()
		var c CacheStringToInt32
		c.Initialize(ctx, "unit_test_cache")

		key := "unit_test_key"
		val := int32(4265)
		c.Set(ctx, key, val)

		got, found := c.Get(ctx, key)
		if !found {
			t.Errorf("expected key to be found, got not found")
		}
		if got != val {
			t.Errorf("expected %d, got %d", val, got)
		}
	})

	t.Run(
		"when value is Set and cache reinitialized, Get should return not found",
		func(t *testing.T) {
			ctx := context.Background()
			var c CacheStringToInt32
			c.Initialize(ctx, "unit_test_cache")

			key := "unit_test_key"
			val := int32(4265)
			c.Set(ctx, key, val)

			c.Initialize(ctx, "unit_test_cache")

			_, found := c.Get(ctx, key)
			if found {
				t.Errorf("expected false, got true")
			}
		},
	)

	t.Run("when no value is Set, Get should return not found", func(t *testing.T) {
		ctx := context.Background()
		var c CacheStringToInt32
		c.Initialize(ctx, "unit_test_cache")

		_, found := c.Get(ctx, "some_random_key")
		if found {
			t.Errorf("expected false, got true")
		}
	})
}
