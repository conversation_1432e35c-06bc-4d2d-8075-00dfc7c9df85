package caches

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// mysqlCache is the generic cache struct
// that should be extended by more specific typed structs
type mysqlCache struct {
	name        string
	cacheExpiry time.Time
	cacheLength time.Duration
}

func (c *mysqlCache) IsExpired() bool {
	return time.Now().After(c.cacheExpiry)
}

func (c *mysqlCache) SetCacheLength(length time.Duration) {
	c.cacheLength = length
}

func (c *mysqlCache) initializeMysqlCache(ctx context.Context, name string) {
	c.name = name
	if c.cacheLength.Nanoseconds() == 0 {
		// cacheLength not set, fallback to a default value
		c.cacheLength = 480 * time.Minute
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"cache_name":            name,
			"default_expiry_length": c.cacheLength,
		}).Warn("cache expiry length not set, falling back to default value")
	}
	c.cacheExpiry = time.Now().Add(c.cacheLength)
}

// Specific Caches

type CacheInt64ToString struct {
	mysqlCache
	cache map[int64]string
}

func (c *CacheInt64ToString) IsInitialized() bool {
	return c.cache != nil
}

func (c *CacheInt64ToString) Initialize(ctx context.Context, name string) {
	c.initializeMysqlCache(ctx, name)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name":    name,
		"expiry_length": fmt.Sprintf("%s", c.cacheLength),
	})
	if c.IsInitialized() {
		lg.WithField("num_entries", len(c.cache)).
			Info("cache expired, invalidating and reinitializing")
	} else {
		lg.Info("initializing cache for the first time")
	}
	c.cache = make(map[int64]string)
}

func (c *CacheInt64ToString) Get(ctx context.Context, key int64) (value string, found bool) {
	value, found = c.cache[key]
	return value, found
}

func (c *CacheInt64ToString) Set(ctx context.Context, key int64, value string) {
	c.cache[key] = value
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name": c.name,
		"set_key":    key,
		"set_value":  value,
	}).Info("set value in cache")
}

type CacheInt64ToProviderDisplayInfo struct {
	mysqlCache
	cache map[int64]coreapi.ProviderDisplayInfo
}

func (c *CacheInt64ToProviderDisplayInfo) IsInitialized() bool {
	return c.cache != nil
}

func (c *CacheInt64ToProviderDisplayInfo) Initialize(ctx context.Context, name string) {
	c.initializeMysqlCache(ctx, name)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name":    name,
		"expiry_length": fmt.Sprintf("%s", c.cacheLength),
	})
	if c.IsInitialized() {
		lg.WithField("num_entries", len(c.cache)).
			Info("cache expired, invalidating and reinitializing")
	} else {
		lg.Info("initializing cache for the first time")
	}
	c.cache = make(map[int64]coreapi.ProviderDisplayInfo)
}

func (c *CacheInt64ToProviderDisplayInfo) Get(
	ctx context.Context,
	key int64,
) (value coreapi.ProviderDisplayInfo, found bool) {
	value, found = c.cache[key]
	return value, found
}

func (c *CacheInt64ToProviderDisplayInfo) Set(
	ctx context.Context,
	key int64,
	value coreapi.ProviderDisplayInfo,
) {
	c.cache[key] = value
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name": c.name,
		"set_key":    key,
		"set_value":  value,
	}).Info("set value in cache")
}

type CacheInt64ToInt64 struct {
	mysqlCache
	cache map[int64]int64
}

func (c *CacheInt64ToInt64) IsInitialized() bool {
	return c.cache != nil
}

func (c *CacheInt64ToInt64) Initialize(ctx context.Context, name string) {
	c.initializeMysqlCache(ctx, name)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name":    name,
		"expiry_length": fmt.Sprintf("%s", c.cacheLength),
	})
	if c.IsInitialized() {
		lg.WithField("num_entries", len(c.cache)).
			Info("cache expired, invalidating and reinitializing")
	} else {
		lg.Info("initializing cache for the first time")
	}
	c.cache = make(map[int64]int64)
}

func (c *CacheInt64ToInt64) Get(ctx context.Context, key int64) (value int64, found bool) {
	value, found = c.cache[key]
	return value, found
}

func (c *CacheInt64ToInt64) Set(ctx context.Context, key int64, value int64) {
	c.cache[key] = value
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name": c.name,
		"set_key":    key,
		"set_value":  value,
	}).Info("set value in cache")
}

type CacheStringToInt32 struct {
	mysqlCache
	cache map[string]int32
}

func (c *CacheStringToInt32) IsInitialized() bool {
	return c.cache != nil
}

func (c *CacheStringToInt32) Initialize(ctx context.Context, name string) {
	c.initializeMysqlCache(ctx, name)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name":    name,
		"expiry_length": fmt.Sprintf("%s", c.cacheLength),
	})
	if c.IsInitialized() {
		lg.WithField("num_entries", len(c.cache)).
			Info("cache expired, invalidating and reinitializing")
	} else {
		lg.Info("initializing cache for the first time")
	}
	c.cache = make(map[string]int32)
}

func (c *CacheStringToInt32) Get(ctx context.Context, key string) (value int32, found bool) {
	value, found = c.cache[key]
	return value, found
}

func (c *CacheStringToInt32) Set(ctx context.Context, key string, value int32) {
	c.cache[key] = value
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name": c.name,
		"set_key":    key,
		"set_value":  value,
	}).Info("set value in cache")
}

type CacheStringToString struct {
	mysqlCache
	cache map[string]string
}

func (c *CacheStringToString) IsInitialized() bool {
	return c.cache != nil
}

func (c *CacheStringToString) Initialize(ctx context.Context, name string) {
	c.initializeMysqlCache(ctx, name)
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name":    name,
		"expiry_length": fmt.Sprintf("%s", c.cacheLength),
	})
	if c.IsInitialized() {
		lg.WithField("num_entries", len(c.cache)).
			Info("cache expired, invalidating and reinitializing")
	} else {
		lg.Info("initializing cache for the first time")
	}
	c.cache = make(map[string]string)
}

func (c *CacheStringToString) Get(
	ctx context.Context,
	key string,
) (value string, found bool) {
	value, found = c.cache[key]
	return value, found
}

func (c *CacheStringToString) Set(
	ctx context.Context,
	key string,
	value string,
) {
	c.cache[key] = value
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"cache_name": c.name,
		"set_key":    key,
		"set_value":  value,
	}).Info("set value in cache")
}
