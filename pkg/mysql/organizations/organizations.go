package mysql

import (
	"context"
	"database/sql"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/text/language"

	"gitlab.com/pockethealth/coreapi/pkg/mysql/caches"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

var delayCache caches.CacheStringToInt32

func SetupCacheLength(cacheLengthMins int) {
	delayCache.SetCacheLength(time.Duration(cacheLengthMins) * time.Minute)
}

// Default delay is 7 days. If the organization does not release reports, delay is -1
// Get provider transfer configurations from orgsvc
func GetReportSendDelayV2(
	ctx context.Context,
	db *sql.DB,
	clinicName string,
	modality string,
	providerId string,
	providerLegacyId int64,
	orgsvc orgservice.OrgService,
) int32 {
	if !delayCache.IsInitialized() || delayCache.IsExpired() {
		delayCache.Initialize(ctx, "report_delay")
	}

	if delay, found := delayCache.Get(ctx, clinicName+modality); !found {
		// get provider transfer config from orgsvc
		providerTransferConfig, err := orgsvc.GetProviderTransferConfig(ctx, providerId)
		if err != nil {
			return 7
		}

		if !providerTransferConfig.SendReports {
			delayCache.Set(ctx, clinicName+modality, -1)
			return -1
		}
		var modSendTime sql.NullInt32
		err = mysqlWithLog.QueryRowAndScan(
			ctx,
			db,
			"SELECT report_delay FROM report_modality WHERE org_id=? and modality=?",
			[]interface{}{&modSendTime},
			providerLegacyId,
			modality,
		)
		if err != nil {
			// If provider send report send time is not zero, sends general report turn around time for that org
			if providerTransferConfig.ReportSendTime != 0 {
				sendTime := int32(
					providerTransferConfig.ReportSendTime,
				) // #nosec G115 chances are the delay is not silly-long, right?
				delayCache.Set(ctx, clinicName+modality, sendTime)
				return sendTime
			}
		}
		if modSendTime.Valid && modSendTime.Int32 != 0 {
			delayCache.Set(ctx, clinicName+modality, modSendTime.Int32)
			return modSendTime.Int32
		}
		delayCache.Set(ctx, clinicName+modality, 7)
		return 7
	} else {
		return delay
	}
}

// TODO Toks deprecate
func GetOrgFromClinicName(
	ctx context.Context,
	db *sql.DB,
	clinicName string,
) (organization_id int) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT organization_id FROM clinics WHERE name=?",
		clinicName,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("get org failed")
		return 0
	}
	defer rows.Close()
	if rows.Next() {
		err = rows.Scan(&organization_id)
	}
	return organization_id
}

// GetOrgIdLanguageTagProvider returns a LanguageTagProvider suitable for phlanguage.languageSelector.WithOrganization
func GetOrgIdLanguageTagProvider(
	orgsvc orgservice.OrgService,
) phlanguage.LanguageTagProvider {
	p := func(ctx context.Context, orgId interface{}) language.Tag {
		intOrgId, ok := orgId.(int64)
		if !ok {
			logutils.CtxLogger(ctx).WithFields(logrus.Fields{
				"provider_id": orgId,
			}).Error("could not get provider language")
			return language.Tag{}
		}
		provider, err := orgsvc.GetProviderByLegacyId(ctx, intOrgId)
		if err != nil {
			logutils.CtxLogger(ctx).WithFields(logrus.Fields{
				"provider_id": intOrgId,
			}).WithError(err).Error("could not get provider language settings")
			return language.Tag{}
		}
		langString := provider.Language
		if langString == "" {
			langString = "en"
		}

		langTag, err := language.Parse(langString)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"language": langString,
			}).WithError(err).Error("error parsing language from string")
		}
		return langTag
	}
	return phlanguage.LanguageTagProvider{
		GetLanguageTag: p,
	}
}
