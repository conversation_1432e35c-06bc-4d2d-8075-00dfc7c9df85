package mysql

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

type UploadSession struct {
	SessionId  string
	TransferId string
	ClientId   string //ip+userAgent
	AuthToken  string
}

func CreateSession(
	ctx context.Context,
	db *sql.DB,
	session UploadSession,
) (sessionId string, err error) {
	sessionId, err = secure.GenerateRandomString(32)

	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT upload_sessions SET session_id = ?, transfer_id = ?, client_id = ?, auth = ?",
		sessionId,
		session.TransferId,
		session.ClientId,
		session.AuthToken,
	)
	if err != nil {
		return "", err
	}
	return sessionId, nil
}

func GetV2SessionToken(ctx context.Context, db *sql.DB, session UploadSession) string {
	var authToken sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT auth from upload_sessions WHERE session_id = ? AND transfer_id = ? AND client_id = ?",
		[]interface{}{&authToken},
		session.SessionId,
		session.TransferId,
		session.ClientId,
	)
	if err != nil {
		return ""
	}
	if authToken.Valid {
		return authToken.String
	}
	return ""
}

func GetV2SessionByTransferId(
	ctx context.Context,
	db *sql.DB,
	transferId string,
) (UploadSession, error) {
	var authToken, sessionId, client sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT auth, session_id, client_id from upload_sessions WHERE transfer_id = ?",
		[]interface{}{&authToken, &sessionId, &client},
		transferId,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return UploadSession{}, nil
		}
		return UploadSession{}, err
	}
	if authToken.Valid {
		return UploadSession{
			AuthToken:  authToken.String,
			SessionId:  sessionId.String,
			ClientId:   client.String,
			TransferId: transferId,
		}, nil
	}
	return UploadSession{}, nil
}

func DeleteSession(ctx context.Context, db *sql.DB, sessionId string) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"DELETE FROM upload_sessions WHERE session_id = ?",
		sessionId,
	)
	return err
}
