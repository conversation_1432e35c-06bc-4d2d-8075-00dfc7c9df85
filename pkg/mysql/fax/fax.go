package mysql

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func InsertFaxRequest(
	ctx context.Context,
	db *sql.DB,
	shareId string,
	faxNum string,
	faxSid string,
	faxId string,
	faxJsonStr string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO fax_requests SET share_id=?, faxnumber=?, fax_sid=?, fax_id=?, fax_json_string=?",
		shareId,
		faxNum,
		faxSid,
		faxId,
		faxJsonStr,
	)
	return err
}

func UpdateFaxRequest(ctx context.Context, db *sql.DB, faxId string, shareId string) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE fax_requests SET fax_id=? WHERE share_id=?",
		faxId,
		shareId,
	)
	return err
}
