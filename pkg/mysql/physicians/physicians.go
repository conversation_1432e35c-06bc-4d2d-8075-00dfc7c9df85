package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	sqlHandlers "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	sqlShareMetadata "gitlab.com/pockethealth/coreapi/pkg/mysql/sharemetadata"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// rhsm.is_deleted = 0 filters soft deleted mappings
// s.is_deleted = 0 filters soft deleted shares
// s.active = 1 filters revoked shares
// we do not filter expired shares from view_shares since we want to display those on the frontend
const ShareAvailableWhere = `
	rhsm.is_deleted = 0
	AND s.is_deleted = 0
	AND s.active = 1
`

func PhysicianCanViewShare(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	shareId string,
) (canView bool, err error) {
	handlerId, err := sqlHandlers.LookupHandlerId(ctx, db, coreapi.PhysicianAccount, acctId)
	if err != nil {
		return false, err
	}
	return sqlHandlers.HandlerCanViewShare(ctx, db, handlerId, shareId)
}

// builds the base query for fetching exams
func buildExamsBaseQuery(joinString string) string {
	return fmt.Sprintf(`
		FROM record_handlers_share_map rhsm
		JOIN shares s ON rhsm.share_id = s.share_id
		JOIN share_objects2 so ON so.share_id = s.share_id
		JOIN object_mappings om ON so.object_id = om.object_id
		JOIN exams e ON e.uuid = om.exam_uuid
		%s
		WHERE rhsm.handler_id = ?
		AND %s
		AND %s
	`, joinString, exams.ExamNotRevokedWhere, ShareAvailableWhere)
}

func buildHealthRecordsBaseQuery(joinString string) string {
	return fmt.Sprintf(`FROM record_handlers_share_map rhsm 
		JOIN shares s ON s.share_id = rhsm.share_id
		JOIN share_healthrecords hr ON rhsm.share_id = hr.share_id
		%s
		WHERE rhsm.handler_id = ?
		AND %s
	`, joinString, ShareAvailableWhere)
}

// GetShareMetadata gets base info for records that a physician has access to
func GetShareMetadata(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	orgsvcClient orgservice.OrgService,
	handlerID string,
) ([]coreapi.ShareMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("handlerID", handlerID)
	start := time.Now()
	combinedPatients, err := getShareMetadataForHandler(
		ctx,
		db,
		acctSvcClient,
		orgsvcClient,
		handlerID,
	)
	if err != nil {
		return nil, err
	}

	lg.WithField("time_taken_ms", time.Since(start).Milliseconds()).
		Info("get patients for physician account")
	return combinedPatients, nil
}

// getShareMetadataForHandler retrieves metadata on records a handler has access to
func getShareMetadataForHandler(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	orgsvcClient orgservice.OrgService,
	handlerID string,
) ([]coreapi.ShareMetadata, error) {
	// get a list of shareIds
	shareIds, err := sqlHandlers.GetHandlerShareIdList(ctx, db, handlerID)
	if err == sql.ErrNoRows {
		return []coreapi.ShareMetadata{}, nil
	} else if err != nil {
		return []coreapi.ShareMetadata{}, err
	}

	shareMetadatas, err := sqlShareMetadata.GetAndInsertShareMetadataFromShareIds(
		ctx,
		db,
		acctSvcClient,
		orgsvcClient,
		shareIds,
		handlerID,
	)
	if err != nil {
		return []coreapi.ShareMetadata{}, err
	}
	return shareMetadatas, nil
}

// GetPatientExams retrieves a list of exam_uuids for a single patient
// if patientAcctId is not empty, include all shares without a patientId
func GetPatientExams(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	handlerID string,
	patientID string,
) ([]string, map[string]coreapi.PhysicianShareInfo, error) {
	var err error
	examUUIDs := make([]string, 0)
	examShareInfoMap := make(map[string]coreapi.PhysicianShareInfo)
	baseQuery := buildExamsBaseQuery(`
		LEFT JOIN view_shares vs ON vs.share_id = s.share_id
	`)
	finalQuery := fmt.Sprintf(
		"SELECT e.uuid, s.share_id, vs.expiry_timestamp, vs.extended_expiry %s",
		baseQuery,
	)

	// if this patient is the owner, also return shares of this account with null patient_id
	var ptAcctId string
	var isOwner bool
	if ptAcctId, err = acctSvcClient.LookupAccountIdByPatientId(ctx, patientID); err == nil {
		pt := getAcctOwner(ctx, acctSvcClient, ptAcctId)
		isOwner = patientID == pt.PatientId
	}

	var rows *sql.Rows
	if isOwner {
		finalQuery = fmt.Sprintf(
			"%s AND (s.patient_id = ? OR (s.patient_id is null AND s.account_id = ?))",
			finalQuery,
		)
		rows, err = mysqlWithLog.Query(ctx, db, finalQuery, handlerID, patientID, ptAcctId)
	} else {
		finalQuery = fmt.Sprintf("%s AND s.patient_id = ?", finalQuery)
		rows, err = mysqlWithLog.Query(ctx, db, finalQuery, handlerID, patientID)
	}
	if err != nil {
		return nil, nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var examUUID, shareId, expiry, extendedExpiry string
		var sqlExpiry, sqlExtendedExpiry sql.NullString
		if err := rows.Scan(&examUUID, &shareId, &sqlExpiry, &sqlExtendedExpiry); err != nil {
			return nil, nil, err
		}
		if sqlExpiry.Valid {
			expiry = sqlExpiry.String
		}
		if sqlExtendedExpiry.Valid {
			extendedExpiry = sqlExtendedExpiry.String
		}
		examShareInfoMap[examUUID] = coreapi.PhysicianShareInfo{
			ShareId:        shareId,
			Expiry:         expiry,
			ExtendedExpiry: extendedExpiry,
		}

		examUUIDs = append(examUUIDs, examUUID)
	}

	return examUUIDs, examShareInfoMap, rows.Err()
}

// GetPatientHealthRecords retrieves health record IDs for a specific patient based on handlerID and patientID.
func GetPatientHealthRecordIdList(
	ctx context.Context,
	db *sql.DB,
	handlerID,
	patientID string,
) ([]string, map[string]coreapi.PhysicianShareInfo, error) {
	recordIdList := make([]string, 0)
	hrShareInfoMap := make(map[string]coreapi.PhysicianShareInfo)
	baseQuery := buildHealthRecordsBaseQuery(`LEFT JOIN view_shares vs ON vs.share_id = s.share_id`)
	finalQuery := fmt.Sprintf(
		`SELECT DISTINCT hr.record_id, s.share_id, vs.expiry_timestamp, vs.extended_expiry 
		%s 
		AND s.patient_id = ?`,
		baseQuery,
	)
	rows, err := mysqlWithLog.Query(ctx, db, finalQuery, handlerID, patientID)
	if err != nil {
		return nil, nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var recordId, shareId, expiry, extendedExpiry string
		var sqlExpiry, sqlExtendedExpiry sql.NullTime
		if err := rows.Scan(&recordId, &shareId, &sqlExpiry, &sqlExtendedExpiry); err != nil {
			return nil, nil, err
		}

		if sqlExpiry.Valid {
			expiry = sqlExpiry.Time.Format("2006-01-02 15:04:05")
		}
		if sqlExtendedExpiry.Valid {
			extendedExpiry = sqlExtendedExpiry.Time.Format("2006-01-02 15:04:05")
		}
		recordIdList = append(recordIdList, recordId)
		hrShareInfoMap[recordId] = coreapi.PhysicianShareInfo{
			ShareId:        shareId,
			Expiry:         expiry,
			ExtendedExpiry: extendedExpiry,
		}
	}

	return recordIdList, hrShareInfoMap, rows.Err()
}

func getAcctOwner(
	ctx context.Context,
	acctSvcClient accountservice.AccountService,
	accountId string,
) accountservice.Patient {
	pts, err := acctSvcClient.GetPatients(ctx, accountId)
	if err != nil || len(pts) == 0 {
		return accountservice.Patient{}
	}
	return accountservice.GetAcctOwner(pts)
}
