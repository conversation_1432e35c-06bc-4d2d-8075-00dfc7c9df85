package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func CreateHandler(
	ctx context.Context,
	db *sql.DB,
	handlerType coreapi.HandlerType,
	handlerTypeId string,
) (string, error) {
	// permissions
	var upload, own, share, view bool

	// update permission depending on handlerType
	if handlerType == coreapi.PhysicianAccount {
		view = true
	}

	// check that the handler has at least one permission allocated
	if !upload && !own && !share && !view {
		return "", fmt.Errorf("handler must have at least one permission")
	}

	if handlerTypeId == "" {
		return "", fmt.Errorf("handler type id must not be empty")
	}

	// generate new handler ID
	handlerId := ksuid.New().String()
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO record_handlers (id, handler_type, handler_type_id, upload, own, share, view) VALUES (?, ?, ?, ?, ?, ?, ?)",
		handlerId,
		handlerType,
		handlerTypeId,
		upload,
		own,
		share,
		view,
	)
	if err != nil {
		return "", err
	}
	return handlerId, nil
}

func LookupHandlerId(
	ctx context.Context,
	db *sql.DB,
	handlerType coreapi.HandlerType,
	handlerTypeId string,
) (string, error) {
	var handlerId string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT id FROM record_handlers WHERE handler_type=? AND handler_type_id=?",
		[]interface{}{&handlerId},
		handlerType,
		handlerTypeId,
	)
	if err != nil {
		return "", err
	}
	return handlerId, nil
}

func CreateHandlerShareMapping(
	ctx context.Context,
	db *sql.DB,
	handlerId string,
	shareId string,
) error {
	if _, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT IGNORE INTO record_handlers_share_map (handler_id, share_id) VALUES (?,?)",
		handlerId,
		shareId,
	); err != nil {
		return err
	}

	return nil
}

func HandlerCanView(ctx context.Context, db *sql.DB, handlerId string) (bool, error) {
	var canView bool
	if err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT view FROM record_handlers rh WHERE rh.id = ?`,
		[]interface{}{&canView},
		handlerId,
	); err != nil {
		return false, err
	}

	return canView, nil
}

func HandlerCanViewShare(
	ctx context.Context,
	db *sql.DB,
	handlerId string,
	shareId string,
) (bool, error) {
	var canView bool
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT view FROM record_handlers rh 
		JOIN record_handlers_share_map rhsm ON rh.id = rhsm.handler_id 
		WHERE rh.id = ?
		AND rhsm.share_id = ?`,
		[]interface{}{&canView},
		handlerId,
		shareId,
	)
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		return false, nil
	} else if err != nil {
		return false, err
	}
	return canView, nil
}

func HandlerCanViewAtLeastOneShare(
	ctx context.Context,
	db *sql.DB,
	handlerId string,
	shareIds []string,
) (bool, error) {
	// Check for empty array
	if len(shareIds) == 0 {
		return false, errors.New("no share IDs provided")
	}

	// create placeholder string of query
	placeholders := make([]string, len(shareIds))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	placeholderStr := strings.Join(placeholders, ",")

	// check if there is at least one share where view access is possible
	query := fmt.Sprintf(`SELECT COALESCE(SUM(view), 0) > 0 FROM record_handlers rh
        JOIN record_handlers_share_map rhsm ON rh.id = rhsm.handler_id
        WHERE rh.id = ?
        AND rhsm.share_id IN (%s)`, placeholderStr)

	// create query params of share ids
	args := make([]interface{}, len(shareIds)+1)
	args[0] = handlerId
	for i, id := range shareIds {
		args[i+1] = id
	}

	var canView bool
	if err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{&canView},
		args...,
	); err != nil {
		return false, err
	}

	return canView, nil
}

func GetHandlerShareIdList(ctx context.Context, db *sql.DB, handlerId string) ([]string, error) {
	query := `SELECT share_id FROM record_handlers rh 
	JOIN record_handlers_share_map rhsm ON rh.id = rhsm.handler_id 
	WHERE rh.id = ?
	AND rhsm.is_deleted = 0
	AND view = 1`
	rows, err := mysqlWithLog.Query(ctx, db, query, handlerId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	shareIds := make([]string, 0)
	for rows.Next() {
		var shareId string
		if err := rows.Scan(&shareId); err != nil {
			return nil, err
		}
		shareIds = append(shareIds, shareId)
	}
	if rows.Err() != nil {
		return nil, rows.Err()
	}

	return shareIds, nil
}
