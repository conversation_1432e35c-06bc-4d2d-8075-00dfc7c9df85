package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// inserts a new entry into table record_handlers
func InsertRecordHandler(
	t *testing.T,
	db *sql.DB,
	handlerID string,
	handlerType coreapi.HandlerType,
	handlerTypeID string,
) {
	_, err := db.Exec(
		`INSERT INTO record_handlers (id, handler_type, handler_type_id, view) VALUES (?, ?, ?, true)`,
		handlerID,
		handlerType,
		handlerTypeID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table record_handlers
func DeleteRecordHandler(
	t *testing.T,
	db *sql.DB,
	handlerID string,
	handlerType coreapi.HandlerType,
	handlerTypeID string,
) {
	_, err := db.Exec(
		`DELETE FROM record_handlers WHERE id=? AND handler_type=? AND handler_type_id=?`,
		handlerID,
		handlerType,
		handlerTypeID,
	)
	assert.NoError(t, err)
}

// inserts a new entry into table record_handlers_share_map
func InsertRecordHandlersShareMap(
	t *testing.T,
	db *sql.DB,
	handlerID string,
	shareID string,
) {
	_, err := db.Exec(
		"INSERT INTO record_handlers_share_map (handler_id, share_id) VALUES (?,?)",
		handlerID,
		shareID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table record_handlers_share_map
func DeleteRecordHandlersShareMap(
	t *testing.T,
	db *sql.DB,
	handlerID string,
	shareID string,
) {
	_, err := db.Exec(
		"DELETE FROM record_handlers_share_map WHERE handler_id=? AND share_id=?",
		handlerID,
		shareID,
	)
	assert.NoError(t, err)
}
