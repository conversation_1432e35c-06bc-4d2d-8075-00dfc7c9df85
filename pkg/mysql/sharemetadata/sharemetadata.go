package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func GetAndInsertShareMetadataFromShareIds(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	orgsvcClient orgservice.OrgService,
	shareIds []string,
	handlerID string,
) ([]coreapi.ShareMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx)
	metadatas := make([]coreapi.ShareMetadata, 0)
	newMetadatas := make([]coreapi.ShareMetadata, 0)

	for _, shareId := range shareIds {
		start := time.Now()
		shareMetadata, err := GetShareMetadataByShareID(ctx, db, shareId)
		if err == sql.ErrNoRows {
			// new metadata found, gather info and insert
			shareMetadata, err = generateShareMetadata(
				ctx,
				db,
				acctSvcClient,
				orgsvcClient,
				shareId,
			)
			if err != nil {
				lg.WithField("shareId", shareId).
					WithError(err).
					Error("error generating share metadata")
				continue
			}
			lg.WithField("time_taken_ms", time.Since(start).Milliseconds()).
				Info("create new share metadata")
			newMetadatas = append(newMetadatas, shareMetadata)
		} else if err != nil {
			lg.WithField("shareId", shareId).WithError(err).Error("error looking up share metadata")
			continue
		} else {
			lg.WithField("time_taken_ms", time.Since(start).Milliseconds()).Info("get share metadata from cache")
		}

		start = time.Now()
		// set referring physicians for share
		referringPhysicians, err := exams.GetReferringPhysiciansByShareID(db, shareMetadata.ShareID)
		if err != nil {
			lg.WithField("shareId", shareId).
				WithError(err).
				Error("error looking up referring physician for existing share metadata")
			continue
		}
		lg.WithFields(
			logrus.Fields{
				"pod":           shareId,
				"time_taken_ms": time.Since(start).Milliseconds(),
			}).Info("get referring physicians for share metadata")

		shareMetadata.ReferringPhysicians = referringPhysicians

		metadatas = append(metadatas, shareMetadata)
	}

	// insert newly generated share metadata
	if len(newMetadatas) > 0 {
		err := BatchInsertShareMetadata(ctx, db, newMetadatas)
		if err != nil {
			// if insert fails, do not return error, just pass the metadata back
			lg.WithError(err).Error("error inserting new share metadata")
		}
	}
	return metadatas, nil
}

func generateShareMetadata(
	ctx context.Context,
	db *sql.DB,
	acctSvcClient accountservice.AccountService,
	orgsvcClient orgservice.OrgService,
	shareId string,
) (coreapi.ShareMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("shareId", shareId)
	// query for share ownership + patient info from exam metadata
	var nullAccountId, nullPatientId sql.NullString
	var patientName, patientDOB string

	query := `
	SELECT s.account_id, s.patient_id, e.patient_name, e.dob
	FROM shares s
	JOIN share_objects2 so ON so.share_id = s.share_id
	JOIN object_mappings om ON so.object_id = om.object_id
	JOIN exams e ON e.uuid = om.exam_uuid
	WHERE s.share_id = ?
	`
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{
			&nullAccountId,
			&nullPatientId,
			&patientName,
			&patientDOB,
		},
		shareId,
	)
	if err != nil {
		return coreapi.ShareMetadata{}, err
	}

	var legacyProviderID int64
	var patientID, orgName string
	// check if its a PS or P2P Share
	if nullAccountId.Valid {
		// TODO: remove when all shares have patient ID
		// PS Share -  use main account owner's patient id
		patient, err := getAcctOwner(ctx, acctSvcClient, nullAccountId.String)
		if err != nil {
			return coreapi.ShareMetadata{}, err
		}
		patientID = patient.PatientId
		patientName = patient.LastName + "^" + patient.FirstName
		patientDOB = strings.Replace(patient.DOB, "-", "", -1)

	} else {
		// P2P Share - add on orgid and org name
		legacyProviderID, orgName, err = getProviderNameAndIDForShare(ctx, db, orgsvcClient, shareId)
		if err != nil {
			lg.WithError(err).Error("failed retrieving P2P share org info")
			return coreapi.ShareMetadata{}, err
		}
	}

	var examCount, healthRecordCount int
	// query to count number of exams in the share
	examNumQuery := fmt.Sprintf(`
	SELECT COUNT(DISTINCT e.uuid)
	FROM shares s
	JOIN share_objects2 so ON so.share_id = s.share_id
	JOIN object_mappings om ON so.object_id = om.object_id
	JOIN exams e ON e.uuid = om.exam_uuid
	WHERE s.share_id = ?
	AND %s
	`, exams.ExamNotRevokedWhere)
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		examNumQuery,
		[]interface{}{&examCount},
		shareId,
	)
	if err != nil {
		return coreapi.ShareMetadata{}, err
	}

	// query to count number of health records in share
	hrsNumQuery := `
	SELECT COUNT(DISTINCT record_id)
	FROM share_healthrecords
	WHERE share_id = ?
	`
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		hrsNumQuery,
		[]interface{}{&healthRecordCount},
		shareId,
	)
	if err != nil {
		return coreapi.ShareMetadata{}, err
	}

	return coreapi.NewShareMetadata(
		shareId,
		patientID,
		legacyProviderID,
		orgName,
		examCount,
		healthRecordCount,
		patientName,
		patientDOB,
		[]string{},
	), nil
}

// for P2P shares, query for legacy provider id and org name
func getProviderNameAndIDForShare(
	ctx context.Context,
	db *sql.DB,
	orgsvcClient orgservice.OrgService,
	shareId string,
) (int64, string, error) {
	lg := logutils.CtxLogger(ctx).WithField("share_id", shareId)

	query := `
	SELECT g.org_id
	FROM shares s
	JOIN share_objects2 so ON so.share_id = s.share_id
	JOIN objects o ON o.object_id = so.object_id
	JOIN scans sc ON sc.scan_id = o.scan_id
	JOIN gateway_users g ON g.user_id = sc.origin_id
	WHERE s.share_id = ?
	LIMIT 1
	`
	var legacyProviderID int64
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{&legacyProviderID},
		shareId,
	)
	if err != nil {
		lg.WithError(err).Error("error retrieving org id")
		return 0, "", err
	}

	// lookup provider's  name
	prov, err := orgsvcClient.GetProviderByLegacyId(ctx, legacyProviderID)
	if err != nil {
		lg.WithError(err).Error("error looking up org name via org svc client")
		return 0, "", err
	}

	return legacyProviderID, prov.Name, nil
}

func BatchInsertShareMetadata(
	ctx context.Context,
	db *sql.DB,
	records []coreapi.ShareMetadata,
) error {
	if len(records) == 0 {
		// nothing to insert
		return nil
	}

	valueStrings := make([]string, 0, len(records))
	valueArgs := make([]interface{}, 0, len(records)*8)

	for _, r := range records {
		// create query placeholders and arg list
		valueStrings = append(
			valueStrings,
			`(?, ?, ?, ?, ?, NULLIF(?, 0), NULLIF(?, ""), NULLIF(?, ""), ?)`,
		)
		valueArgs = append(
			valueArgs,
			r.ShareID,
			r.ExamCount,
			r.HealthRecordCount,
			r.PatientName,
			r.PatientDOB,
			r.LegacyProviderID,
			r.OrgName,
			r.PhPatientID,
			r.ShareType,
		)
	}

	// combine query with placeholders
	query := fmt.Sprintf(`INSERT IGNORE INTO share_metadata
        (share_id, exam_count, hrs_count, patient_name, patient_dob, legacy_provider_id, org_name, ph_patient_id, share_type) 
        VALUES %s`, strings.Join(valueStrings, ","))

	_, err := mysqlWithLog.Exec(ctx, db, query, valueArgs...)
	return err
}

func GetShareMetadataByShareID(
	ctx context.Context,
	db *sql.DB,
	shareID string,
) (coreapi.ShareMetadata, error) {
	var metadata coreapi.ShareMetadata
	query := `SELECT share_id, exam_count, hrs_count, patient_name, patient_dob, IFNULL(legacy_provider_id, 0), IFNULL(org_name, ''), IFNULL(ph_patient_id, ''), share_type 
	FROM share_metadata 
	WHERE share_id = ?`

	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{
			&metadata.ShareID,
			&metadata.ExamCount,
			&metadata.HealthRecordCount,
			&metadata.PatientName,
			&metadata.PatientDOB,
			&metadata.LegacyProviderID,
			&metadata.OrgName,
			&metadata.PhPatientID,
			&metadata.ShareType,
		},
		shareID,
	)
	if err != nil {
		return metadata, err
	}

	return metadata, nil
}

func getAcctOwner(
	ctx context.Context,
	acctSvcClient accountservice.AccountService,
	accountId string,
) (accountservice.Patient, error) {
	pts, err := acctSvcClient.GetPatients(ctx, accountId)
	if err != nil || len(pts) == 0 {
		logutils.CtxLogger(ctx).WithError(err).Error("failed to get account owner")
		return accountservice.Patient{}, err
	}
	return accountservice.GetAcctOwner(pts), nil
}
