package mysql

import (
	"context"
	"database/sql"
	"fmt"
)

func PullSurveyLinks(ctx context.Context, db *sql.DB, orgID int64) (int64, string, error) {
	var sqlLink sql.NullString
	var sqlId sql.NullInt64
	err := db.QueryRowContext(ctx, "SELECT id, survey_link FROM surveys WHERE org_id=? AND active=1", orgID).
		Scan(&sqlId, &sqlLink)
	if err != nil {
		return 0, "", err
	}
	if sqlLink.Valid {
		return sqlId.Int64, sqlLink.String, nil
	}

	return 0, "", fmt.Errorf("no available survey")
}
