package mysql

import (
	"context"
	"database/sql"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func QueryRowAndScan(
	ctx context.Context,
	db *sql.DB,
	query string,
	resultArr []interface{},
	args ...interface{},
) error {

	start := time.Now()
	err := db.QueryRow(query, args...).Scan(resultArr...)
	elapsed := time.Since(start)
	errmsg := ""
	if err != nil {
		errmsg = err.Error()
	}
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query":     query,
		"time_ms":   elapsed.Milliseconds(),
		"query_err": errmsg,
	}).Debug("MySQL QueryRow")

	return err
}

func Query(ctx context.Context, db *sql.DB, query string, args ...interface{}) (*sql.Rows, error) {

	start := time.Now()
	result, err := db.Query(query, args...)
	elapsed := time.Since(start)
	errmsg := ""
	if err != nil {
		errmsg = err.Error()
	}
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query":     query,
		"time_ms":   elapsed.Milliseconds(),
		"query_err": errmsg,
	}).Debug("MySQL Query")

	return result, err
}

func Exec(ctx context.Context, db *sql.DB, query string, args ...interface{}) (sql.Result, error) {

	start := time.Now()
	result, err := db.Exec(query, args...)
	elapsed := time.Since(start)
	errmsg := ""
	if err != nil {
		errmsg = err.Error()
	}

	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query":     query,
		"time_ms":   elapsed.Milliseconds(),
		"query_err": errmsg,
	}).Debug("MySQL Exec")

	return result, err
}

func ExecTx(
	ctx context.Context,
	tx *sql.Tx,
	query string,
	args ...interface{},
) (sql.Result, error) {

	start := time.Now()
	result, err := tx.Exec(query, args...)
	elapsed := time.Since(start)
	errmsg := ""
	if err != nil {
		errmsg = err.Error()
	}

	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query":     query,
		"time_ms":   elapsed.Milliseconds(),
		"query_err": errmsg,
	}).Debug("MySQL Exec")

	return result, err
}

func QueryTxRowAndScan(
	ctx context.Context,
	tx *sql.Tx,
	query string,
	resultArr []interface{},
	args ...interface{},
) error {

	start := time.Now()
	err := tx.QueryRow(query, args...).Scan(resultArr...)
	elapsed := time.Since(start)
	errmsg := ""
	if err != nil {
		errmsg = err.Error()
	}
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query":     query,
		"time_ms":   elapsed.Milliseconds(),
		"query_err": errmsg,
	}).Debug("MySQL QueryRow")

	return err
}
