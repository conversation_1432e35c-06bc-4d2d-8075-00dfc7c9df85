package shareobjects

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

// inserts a new entry into table share_objects2
func InsertShareObject(
	t *testing.T,
	db *sql.DB,
	shareID string,
	objectID string,
	isDeleted bool,
) {
	_, err := db.Exec(
		`INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES (?, ?, ?)`,
		shareID,
		objectID,
		isDeleted,
	)
	assert.NoError(t, err)
}

// deletes an entry from table share_objects2
func DeleteShareObject(
	t *testing.T,
	db *sql.DB,
	shareID string,
	objectID string,
) {
	_, err := db.Exec(
		`DELETE FROM share_objects2 WHERE share_id=? AND object_id=?`,
		shareID,
		objectID)
	assert.NoError(t, err)
}
