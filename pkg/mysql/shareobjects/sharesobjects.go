package shareobjects

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

type Share struct {
	ShareId string
	Images  []string
	Reports []string
}

func GetShareObjectIds(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (objectIds []string, err error) {
	rows, err := getShareObjectIds(ctx, db, shareId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var id string
	for rows.Next() {
		if err = rows.Scan(&id); err != nil {
			return nil, err
		}

		objectIds = append(objectIds, id)
	}

	return objectIds, nil
}

func GetShareObjectIdsAsMap(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (objectIds map[string]int, err error) {
	rows, err := getShareObjectIds(ctx, db, shareId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	objectIds = make(map[string]int)
	var id string
	for rows.Next() {
		if err = rows.Scan(&id); err != nil {
			return nil, err
		}

		objectIds[id] = 0
	}

	return objectIds, nil
}

func getShareObjectIds(ctx context.Context, db *sql.DB, shareId string) (*sql.Rows, error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT object_id FROM share_objects2 WHERE share_id=?",
		shareId,
	)
	if err != nil {
		return nil, err
	}
	return rows, nil
}

// returns the shareId of a share that an object belongs to, if it exists
func GetObjectShareIds(ctx context.Context, db *sql.DB, objectID string) (shareIds []string, err error) {
	rows, err := getObjectShareIds(ctx, db, objectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	shareIds = make([]string, 0)
	var id string
	for rows.Next() {
		if err = rows.Scan(&id); err != nil {
			return nil, err
		}

		shareIds = append(shareIds, id)
	}

	return shareIds, nil
}
func getObjectShareIds(ctx context.Context, db *sql.DB, objectID string) (*sql.Rows, error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT share_id FROM share_objects2 WHERE object_id=?",
		objectID,
	)
	if err != nil {
		return nil, err
	}

	return rows, nil
}

func GetShare(ctx context.Context, db *sql.DB, shareId string) (share Share, err error) {
	rows, err := mysqlWithLog.Query(
		ctx, db,
		`SELECT so.object_id, o.is_report
		FROM share_objects2 so
		INNER JOIN objects o ON so.object_id = o.object_id
		WHERE share_id=?`,
		shareId,
	)
	if err != nil {
		return Share{}, err
	}
	defer rows.Close()
	var objId string
	var isReport bool
	for rows.Next() {
		if err = rows.Scan(&objId, &isReport); err != nil {
			return Share{}, err
		}

		if isReport {
			share.Reports = append(share.Reports, objId)
		} else {
			share.Images = append(share.Images, objId)
		}
	}

	return share, nil
}

func HealthRecordShareExists(
	ctx context.Context,
	db *sql.DB,
	shareID string,
	recordID string,
) (bool, error) {
	exists := false

	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT EXISTS (SELECT * FROM share_healthrecords WHERE share_id=? AND record_id=? LIMIT 1) AS e`,
		[]interface{}{&exists},
		shareID,
		recordID,
	)
	if err != nil {
		return false, err
	}

	return exists, nil
}

// returns the shareIds of a share that a health record belongs to, if it exists
func GetHealthRecordShareIds(ctx context.Context, db *sql.DB, recordId string) (shareIds []string, err error) {
	rows, err := getHealthRecordSharesIds(ctx, db, recordId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	shareIds = make([]string, 0)
	var id string
	for rows.Next() {
		if err = rows.Scan(&id); err != nil {
			return nil, err
		}

		shareIds = append(shareIds, id)
	}

	return shareIds, nil
}

func getHealthRecordSharesIds(
	ctx context.Context,
	db *sql.DB,
	recordID string,
) (*sql.Rows, error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		`SELECT share_id FROM share_healthrecords WHERE record_id=?`,
		recordID,
	)
	if err != nil {
		return nil, err
	}

	return rows, nil
}
