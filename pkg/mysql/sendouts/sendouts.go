package mysql

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func LogSendout(
	ctx context.Context,
	db *sql.DB,
	emailAddress string,
	emailType string,
	requestId int64,
	customerId string,
	subId string,
	transferId string,
	token string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO sendouts SET recipient=?, type=?, token=?, request_id=?, customer_id = ?, sub_id = ?, scan_id = ?",
		emailAddress,
		emailType,
		token,
		requestId,
		customerId,
		subId,
		transferId,
	)
	return err
}

func IsEmailSent(ctx context.Context, db *sql.DB, emailAddress string, emailType string) bool {
	isEmailSent := true
	var recipient string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT recipient FROM sendouts WHERE recipient=? and type=?",
		[]interface{}{&recipient},
		emailAddress,
		emailType,
	)
	if err != nil {
		return false
	}
	return isEmailSent
}
