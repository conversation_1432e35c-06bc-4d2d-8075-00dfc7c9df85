package mysql

import (
	"context"
	"database/sql"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func Create(
	ctx context.Context,
	db *sql.DB,
	id string,
	email string,
	clinicId int64,
	dataToken string,
	lastCompletedStep models.RequestFormStep,
) (err error) {
	_, err = mysqlWithLog.Exec(
		ctx, db,
		`INSERT incomplete_requests 
		SET id=?, email=?, clinic_id=?, data_token=?, last_completed_step=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 7 DAY)`,
		id, email, clinicId, dataToken, lastCompletedStep,
	)
	return err
}

// ValidIncompleteRequestExistsByEmail returns whether an un-expired, and un-completed incomplete request exists for the specified email and clinic
func ValidIncompleteRequestExistsByEmailAndClinic(
	ctx context.Context,
	db *sql.DB,
	email string,
	clinicId int64,
) (bool, error) {
	var counter int
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT COUNT(*) FROM incomplete_requests WHERE email=? AND clinic_id=? and expiry_timestamp > NOW() AND last_completed_step!=?",
		[]interface{}{&counter},
		email,
		clinicId,
		models.RequestStepComplete,
	)
	if err != nil {
		return false, err
	}
	if counter >= 1 {
		return true, nil
	}
	return false, nil
}

// RequestExists - returns whether an unexpired request with the id exists in the incomplete_requests table or not,
func RequestExists(ctx context.Context, db *sql.DB, id string) (bool, error) {
	var counter int
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT count(*) FROM incomplete_requests WHERE id=? AND expiry_timestamp > NOW()",
		[]interface{}{&counter},
		id,
	)
	if err != nil {
		return false, err
	}
	if counter >= 1 {
		return true, nil
	}
	return false, nil
}

func UpdateRequestById(
	ctx context.Context,
	db *sql.DB,
	id string,
	dataToken string,
	lastCompletedStep models.RequestFormStep,
) (err error) {
	_, err = mysqlWithLog.Exec(
		ctx, db,
		`UPDATE incomplete_requests 
		SET data_token=?, last_completed_step=?
		WHERE id=?`,
		dataToken, lastCompletedStep, id,
	)
	return err
}

func GetMetadata(
	ctx context.Context,
	db *sql.DB,
	id string,
) (clinicId int64, completed bool, expired bool, err error) {
	var lastCompletedStep sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx, db,
		`SELECT 
			clinic_id,
			last_completed_step,
			( expiry_timestamp <= NOW() ) as expired
		FROM incomplete_requests WHERE id=?`,
		[]interface{}{&clinicId, &lastCompletedStep, &expired}, id,
	)

	if err != nil {
		return 0, false, false, err
	}

	completed = lastCompletedStep.Valid && lastCompletedStep.String == string(
		models.RequestStepComplete,
	)

	return clinicId, completed, expired, nil
}

func GetEmailDataById(
	ctx context.Context,
	db *sql.DB,
	id string,
) (clinicId int64, completed bool, emailsSent int, email string, dataToken string, err error) {
	var requestId sql.NullInt64

	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT clinic_id, request_id, emails_sent, email, data_token FROM incomplete_requests WHERE id=?",
		[]interface{}{&clinicId, &requestId, &emailsSent, &email, &dataToken},
		id,
	)
	if err != nil {
		return 0, false, 0, "", "", err
	}

	// incomplete_request is "completed" if a completed request is associated with the entry
	completed = requestId.Valid && requestId.Int64 > 0
	return clinicId, completed, emailsSent, email, dataToken, nil
}

func GetDataTokenAndLastCompletedStepById(
	ctx context.Context,
	db *sql.DB,
	id string,
) (dataToken string, lastCompletedStep models.RequestFormStep, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT data_token, last_completed_step FROM incomplete_requests WHERE id=?",
		[]interface{}{&dataToken, &lastCompletedStep},
		id,
	)
	if err != nil {
		return "", models.RequestStepIntro, err
	}

	return dataToken, lastCompletedStep, nil
}

func GetIncompleteRequestCreationTimestamp(
	ctx context.Context,
	db *sql.DB,
	email string,
	clinicID int64,
) (createdTimestamp time.Time, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT created_timestamp FROM incomplete_requests WHERE clinic_id=? and email=?",
		[]interface{}{&createdTimestamp},
		clinicID,
		email,
	)
	if err != nil {
		return time.Time{}, err
	}
	return createdTimestamp, nil
}

// MarkRequestsAsCompleted updates all un-completed and un-expired requests as complete
func MarkRequestsAsCompleted(
	ctx context.Context,
	db *sql.DB,
	completedRequestId int64,
	userEmail string,
) (err error) {
	_, err = mysqlWithLog.Exec(
		ctx, db,
		`UPDATE incomplete_requests
		SET request_id=?, last_completed_step=?
		WHERE email=? AND request_id IS NULL AND expiry_timestamp > NOW()`,
		completedRequestId, models.RequestStepComplete, userEmail,
	)
	return err
}

func ClearExpiredRequests(ctx context.Context, db *sql.DB) (numRemoved int64, err error) {
	res, err := mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE incomplete_requests SET email=NULL, data_token=NULL WHERE expiry_timestamp <= NOW()",
	)
	if err != nil {
		return 0, err
	}
	return res.RowsAffected()
}
