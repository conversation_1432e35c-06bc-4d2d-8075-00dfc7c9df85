//go:build integration
// +build integration

package mysql

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"

	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
)

func TestMarkRequestsAsCompleted(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run(
		"when multiple matching requests from different clinics, should retain last completed step info",
		func(t *testing.T) {
			rand := strconv.FormatInt(time.Now().UnixNano(), 10)
			incompleteReqIds := []string{
				ksuid.New().String(),
				ksuid.New().String(),
				ksuid.New().String(),
			}
			clinicIds := []int64{13, 29, 5}
			lastCompletedStep := models.RequestStepHealthId
			request := models.Request{
				FirstName: "Ada",
				LastName:  "Lovelace",
				Dob:       "19900601",
				Email:     fmt.Sprintf("<EMAIL>", rand),
			}
			incompleteReq := models.IncompleteRequest{
				RequestData:       request,
				LastCompletedStep: lastCompletedStep,
			}

			// setup request db entry
			res, err := db.Exec(
				`INSERT INTO requests(clinic_id, email, dob) VALUES (?, ?, ?)`,
				int64(clinicIds[0]), request.Email, request.Dob,
			)
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			requestId, err := res.LastInsertId()
			if err != nil {
				t.Fatalf("unable to setup test data: %q", err.Error())
			}
			t.Cleanup(func() {
				// this needs to go before other cleanup calls since
				// the incomplete_requests table has a foreign key with this table
				db.Exec("DELETE FROM requests WHERE id=?", requestId)
			})

			// setup multiple incomplete request db entries
			for i, id := range incompleteReqIds {
				// add some variation
				incompleteReq.RequestData.LastName += id

				// setup db entry
				jwtTok, err := auth.MakeIncomplReqDataToken(incompleteReq)
				if err != nil {
					t.Fatalf("unable to create test data token: %q", err.Error())
				}
				dataTok, err := auth.EncryptJWETwoPartKey(
					[]byte(jwtTok),
					incompleteReq.RequestData.Dob,
				)
				if err != nil {
					t.Fatalf("unable to encrypt test data token")
				}
				_, err = db.Exec(
					`INSERT incomplete_requests
				SET id=?, last_completed_step=?, data_token=?, email=?, clinic_id=?, expiry_timestamp=DATE_ADD(NOW(), INTERVAL 10 HOUR)`,
					id, lastCompletedStep, dataTok, request.Email, int64(clinicIds[i]),
				)
				if err != nil {
					t.Fatalf("unable to setup test incomplete request: %q", err.Error())
				}
				t.Cleanup(func() {
					db.Exec("DELETE FROM incomplete_requests WHERE id=?", id)
				})
			}

			err = MarkRequestsAsCompleted(context.Background(), db, requestId, request.Email)
			if err != nil {
				t.Errorf("got error, expected none: %q", err.Error())
			}

			// check that all incomplete requests are marked as completed
			var gotLastCompletedStep models.RequestFormStep
			var gotRequestId int64
			for _, id := range incompleteReqIds {
				err = db.QueryRow("SELECT last_completed_step, request_id FROM incomplete_requests WHERE id=?", id).
					Scan(&gotLastCompletedStep, &gotRequestId)
				if err != nil {
					t.Errorf("error getting incomplete request from db: %q", err.Error())
				}

				if gotLastCompletedStep != models.RequestStepComplete {
					t.Errorf(
						"expected %s, got %s",
						models.RequestStepComplete,
						gotLastCompletedStep,
					)
				}
				if gotRequestId != requestId {
					t.Errorf("expected %d, got %d", requestId, gotRequestId)
				}
			}

		},
	)
}
