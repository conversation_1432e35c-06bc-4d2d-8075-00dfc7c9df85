package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func AddPhiProfile(ctx context.Context, db *sql.DB, phiProfile coreapi.PhiProfile) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO phi_profiles (id, patient_id, phi_type, phi_value) VALUES (?,?,?,?)",
		phiProfile.Id,
		phiProfile.PatientId,
		phiProfile.PhiType,
		phiProfile.PhiValue,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			With<PERSON>ield("patient_id", phiProfile.PatientId).
			Error("unable to insert phi_profile")
		return errors.New("unable to add phi_profile")
	}
	return err
}

func AddPhiProfiles(ctx context.Context, db *sql.DB, phiProfiles []coreapi.PhiProfile) error {
	if len(phiProfiles) == 0 {
		return errors.New("no phi to add")
	}

	valueStrings := make([]string, 0, len(phiProfiles))
	valueArgs := make([]interface{}, 0, len(phiProfiles))
	i := 0
	for _, profile := range phiProfiles {
		valueStrings = append(valueStrings, ("(?,?,?,?)"))
		valueArgs = append(valueArgs, profile.Id)
		valueArgs = append(valueArgs, profile.PatientId)
		valueArgs = append(valueArgs, profile.PhiType)
		valueArgs = append(valueArgs, profile.PhiValue)
		i++
	}
	stmt := fmt.Sprintf(
		"INSERT INTO phi_profiles (id, patient_id, phi_type, phi_value) VALUES %s",
		strings.Join(valueStrings, ","),
	)
	_, err := mysqlWithLog.Exec(ctx, db, stmt, valueArgs...)
	return err
}

func PhiProfileExists(ctx context.Context, db *sql.DB, patientId string) (bool, error) {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"SELECT true FROM phi_profiles WHERE patient_id = ?",
		patientId,
	)
	if err == sql.ErrNoRows {
		return false, nil
	}
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("patient_id", patientId).
			Error("unable to check if phi_profile exists")
		return false, errors.New("unable to check if phi_profile exists")
	}

	return true, nil
}

func GetPhiProfiles(
	ctx context.Context,
	db *sql.DB,
	patientIds []interface{},
) ([]coreapi.PhiProfile, error) {
	phiProfiles := []coreapi.PhiProfile{}
	if len(patientIds) == 0 {
		logutils.DebugCtxLogger(ctx).Info("no pt ids given for GetPhiProfiles")
		return phiProfiles, nil
	}
	var args string
	for range patientIds {
		args += `?,`
	}
	args = strings.TrimSuffix(args, ",")
	query := fmt.Sprintf(
		`SELECT id, patient_id, phi_type, phi_value FROM phi_profiles WHERE patient_id in (%s)`,
		args,
	)
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		query,
		patientIds...)
	if err != nil {
		return phiProfiles, err
	}
	defer rows.Close()
	if rows.Next() {
		for ok := true; ok; ok = rows.Next() {
			phiProfile := coreapi.PhiProfile{}
			err := rows.Scan(
				&phiProfile.Id,
				&phiProfile.PatientId,
				&phiProfile.PhiType,
				&phiProfile.PhiValue,
			)
			if err != nil {
				return phiProfiles, err
			}
			phiProfiles = append(phiProfiles, phiProfile)
		}
	}
	return phiProfiles, nil
}

// Deleting PHI profiles is a hard delete
func DeletePHIProfile(ctx context.Context, db *sql.DB, patientId string) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"DELETE FROM phi_profiles WHERE patient_id=?",
		patientId,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("patient_id", patientId).
			Error("unable to delete phi_profile")
		return errors.New("unable to delete phi_profile")
	}
	return err
}
