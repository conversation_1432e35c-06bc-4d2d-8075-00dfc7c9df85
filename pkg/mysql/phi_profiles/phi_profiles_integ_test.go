//go:build integration
// +build integration

package mysql

import (
	"context"
	"database/sql"
	"strconv"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/phiHelpers"
)

func TestDeletePHIProfile(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("delete phi_profiles", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		id := "t_id" + rand
		patient_id := "pt_id" + rand
		phi_type := "OHIP"
		phi_value := "**********"
		_, err := db.Exec(
			`INSERT INTO phi_profiles(id, patient_id, phi_type, phi_value) VALUES (?, ?, ?, ?)`,
			id, patient_id, phi_type, phi_value,
		)
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}

		err = DeletePHIProfile(context.Background(), db, patient_id)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}
		var got string
		err = db.QueryRow("SELECT id from phi_profiles where patient_id = ?", patient_id).
			Scan(&got)
		if err == nil {
			t.Errorf("expected error got none")
		} else if err != sql.ErrNoRows {
			t.Errorf("expected no rows, got error %q", err.Error())
		}

	})
}

func TestGetPhiProfiles(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("get all phi_profiles", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		id := "t_1" + rand
		patient_id := "pt_1" + rand
		phi_type := "OHIP"
		phi_value := "**********"
		id2 := "t_2" + rand
		patient_id2 := "pt_2" + rand
		phi_type2 := "BCPHN"
		phi_value2 := "88999"

		_, err := db.Exec(
			`INSERT INTO phi_profiles(id, patient_id, phi_type, phi_value) VALUES (?,?,?,?), (?,?,?,?)`,
			id,
			patient_id,
			phi_type,
			phi_value,
			id2,
			patient_id2,
			phi_type2,
			phi_value2,
		)
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}

		ids := []interface{}{patient_id, patient_id2}
		res, err := GetPhiProfiles(context.Background(), db, ids)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}

		if len(res) != 2 {
			t.Errorf("expected 2 rows, got error %d", len(res))
		}

		if res[0].Id != id {
			t.Errorf("expected %s rows, got error %s", id, res[0].Id)
		}

		if res[1].Id != id2 {
			t.Errorf("expected %s rows, got error %s", id, res[1].Id)
		}

		t.Cleanup(func() {
			db.Exec("DELETE FROM phi_profiles WHERE id in (?,?)", id, id2)
		})

	})
}

func TestInsertPhiProfiles(t *testing.T) {
	db := testutils.SetupTestDB(t)

	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	patientId := "pt_" + rand

	t.Run("Insert all types of phi_profiles", func(t *testing.T) {
		pt := &models.PhiPatient{
			Ohip:   "123",
			Ohipvc: "456",
			Bcphn:  "789",
			Ssn:    "012",
			Ipn:    "345",
			AltId:  "678",
		}
		phiProfiles := phiHelpers.GetPhiInfoFromPatient(patientId, pt)

		err := AddPhiProfiles(context.Background(), db, phiProfiles)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}

		rows, err := db.Query(
			"SELECT id, patient_id, phi_type, phi_value FROM phi_profiles WHERE patient_id=?",
			patientId,
		)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}
		defer rows.Close()
		phPS := make([]*coreapi.PhiProfile, 0)
		for rows.Next() {
			phP := new(coreapi.PhiProfile)
			if err := rows.Scan(&phP.Id, &phP.PatientId, &phP.PhiType, &phP.PhiValue); err != nil {
				panic(err)
			}
			phPS = append(phPS, phP)
		}

		if len(phPS) != 6 {
			t.Errorf("expected len to be 6, got:%d", len(phPS))
		}
		for _, phP := range phPS {
			if phP.PatientId != patientId {
				t.Errorf("expected patient_id to be %q, got:%q", patientId, phP.PatientId)
			}
			if phP.PhiType == "" {
				t.Errorf("expected phi_type to be non-empty, got:%q", phP.PhiType)
			}
			if phP.PhiValue == "" {
				t.Errorf("expected phi_value to be non-empty, got:%q", phP.PhiValue)
			}
		}
	})
}

func TestUpdatePhiProfiles(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("get all phi_profiles", func(t *testing.T) {
		pt := &models.PhiPatient{
			Ohip:   "123",
			Ohipvc: "456",
			Bcphn:  "789",
			Ssn:    "012",
			Ipn:    "345",
			AltId:  "678",
		}
		phiProfiles := phiHelpers.GetPhiInfoFromPatient("patientId", pt)

		err := AddPhiProfiles(context.Background(), db, phiProfiles)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}
	})
}
