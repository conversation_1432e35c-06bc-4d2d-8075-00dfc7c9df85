package mysql

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func CreateBadInputEntry(
	ctx context.Context,
	db *sql.DB,
	providerId string,
	requestId string,
	stepName string,
	fieldName string,
	value string,
	errorCode string,
	deviceId string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		`INSERT INTO request_form_bad_inputs (
			provider_id, 
			request_id, 
			step_name, 
			field_name, 
			value, 
			error_code, 
			device_id
		) values
		(?, ?, ?, ?, ?, ?, ?)`,
		providerId,
		requestId,
		stepName,
		fieldName,
		value,
		errorCode,
		deviceId,
	)
	return err
}

func DeleteExpiredEntries(
	ctx context.Context,
	db *sql.DB,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		`DELETE FROM request_form_bad_inputs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 2 WEEK)`,
	)
	return err
}
