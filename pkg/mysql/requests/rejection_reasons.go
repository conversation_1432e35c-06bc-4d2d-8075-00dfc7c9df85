package mysql

var rejectionReasons = map[int64]string{
	0:  "NONE",
	1:  "UNKNOWN",
	2:  "INTERNAL_ERROR",
	3:  "HID_NOT_MATCH",
	4:  "MRN_NOT_MATCH",
	5:  "DOB_NOT_MATCH",
	6:  "NAME_NOT_MATCH",
	7:  "NOT_FOUND",
	8:  "ENROLLED_SAME_EMAIL",
	9:  "ENROLLED_DIFF_EMAIL",
	10: "ENROLLED_UNKNOWN_EMAIL",
	11: "EMAIL_VALIDATION_FAILED",
	12: "ALT_ID_MRN_NOT_MATCH",
	13: "ADMIN",
	14: "NO_HID_IN_PACS",
	15: "BLOCKED_PATIENT",
	16: "FAILED_FUZZY_WUZZY",
	17: "PX_REJECTED",
	18: "NO_DELEGATE_IN_REQUEST",
	19: "NO_STUDY_IN_PACS",
}

// GetRejectionReasonById returns a machine readable string representation of why a
// request was rejected.
func GetRejectionReasonById(rejectionReasonNum int64) string {
	if reason, ok := rejectionReasons[rejectionReasonNum]; ok {
		return reason
	}
	return "UNKNOWN"
}
