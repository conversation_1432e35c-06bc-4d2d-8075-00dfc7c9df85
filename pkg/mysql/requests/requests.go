package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// List of Request statuses
const (
	// UNFINALIZED: the initial status of a new request,
	// set it to null only if all the other conditions are satisfied,
	// so prov svc could pick it up and continue the next step.
	UNFINALIZED                  = "UNFINALIZED"
	REJECTED                     = "REJECTED"
	UNDER_REVIEW                 = "UNDER_REVIEW"
	SUPPRESS                     = "SUPPRESS"
	DELEGATE_PENDING_AUTH        = "DELEGATE_PENDING_AUTH"
	VOID                         = "VOID"
	VERIFICATION_PENDING         = "VERIFICATION_PENDING"
	APPROVAL_REQUIRED            = "APPROVAL_REQUIRED"
	SUBMISSION_COMPLETE          = "SUBMISSION_COMPLETE" // this new status will replace the NULL/"" status, gateway/providers service needs to be updated before this change
	UPH_RR_PENDING               = "UPH_RR_PENDING"
	UPH_RR_IN_PROGRESS           = "UPH_RR_IN_PROGRESS"
	UPH_RR_PENDING_DELIVERY_INFO = "UPH_RR_PENDING_DELIVERY_INFO"
	UPH_RR_MISMATCH              = "UPH_RR_MISMATCH"
	UPH_RR_TRANSFERRING          = "UPH_RR_TRANSFERRING"
)

// IsPendingStatus validates pending request statuses
func IsPendingStatus(requestStatus string) bool {
	return requestStatus == "" || requestStatus == UNDER_REVIEW || requestStatus == SUPPRESS ||
		requestStatus == DELEGATE_PENDING_AUTH || requestStatus == VERIFICATION_PENDING || requestStatus == APPROVAL_REQUIRED
}

func IsRejectedStatus(status string) bool {
	return status == REJECTED
}

func IsEnrolled(status string) bool {
	// status IDs longer than 26 characters are transfer IDs. i.e request exists and has been completed + patient has been enrolled
	return len(status) > 26
}

func GetRequestById(ctx context.Context, db *sql.DB, reqId int64) (models.Request, error) {
	query := `SELECT id,clinic_id,first_name,last_name,email,alt_last_name,ohip,ohip_vc,mrn,alt_h_id,dob,ssn,ipn,patient_id,rejection_reason
	FROM requests
	WHERE id=?`
	var request models.Request
	var email, altLastName, ohip, ohipVc, mrn, altId, dob, ssn, ipn, patientId sql.NullString
	var rejectionReason sql.NullInt64
	resArray := []interface{}{
		&request.RequestId,
		&request.ProviderId,
		&request.FirstName,
		&request.LastName,
		&email,
		&altLastName,
		&ohip,
		&ohipVc,
		&mrn,
		&altId,
		&dob,
		&ssn,
		&ipn,
		&patientId,
		&rejectionReason,
	}
	err := mysqlWithLog.QueryRowAndScan(ctx, db, query, resArray, reqId)
	if err != nil {
		return models.Request{}, err
	}
	fillIfValidString := func(input sql.NullString, field *string) {
		if input.Valid {
			*field = input.String
		}
	}
	fillIfValidInt64 := func(input sql.NullInt64, field *int64) {
		if input.Valid {
			*field = input.Int64
		}
	}
	fillIfValidString(email, &request.Email)
	fillIfValidString(altLastName, &request.AltLastName)
	fillIfValidString(ohip, &request.Ohip)
	fillIfValidString(ohipVc, &request.Ohipvc)
	fillIfValidString(mrn, &request.Mrn)
	fillIfValidString(altId, &request.AltId)
	fillIfValidString(dob, &request.Dob)
	fillIfValidString(ssn, &request.Ssn)
	fillIfValidString(ipn, &request.Ipn)

	fillIfValidInt64(rejectionReason, &request.RejectionReason)

	return request, nil
}

func AttributePatient(ctx context.Context, db *sql.DB, reqId int64, patientId string) error {
	lg := logutils.DebugCtxLogger(ctx)
	_, err := db.ExecContext(ctx, `UPDATE requests SET patient_id=? WHERE id=?`, patientId, reqId)
	if err != nil {
		lg.WithError(err).Error("failed to update patient id on requests")
		return err
	}
	return nil
}

// TODO refactor function
func GetPendingRequests(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	orgsvcClient orgservice.OrgService,
	excludeDeleted bool,
) (pendingRequests models.PendingRequests, err error) {
	pendingRequests = make([]models.PendingRequest, 0)

	baseQ := `SELECT first_name, last_name, alt_last_name, patient_id, ohip, ohip_vc, mrn, alt_h_id, dob, ssn, ipn, contents, clinic_id, timestamp, id, legacy_provider_id, scan_id,
	(scan_id IS NOT NULL AND (scan_id in (?,?))) AND !enrolled AS editable
	FROM requests
	WHERE account_id=? AND
	(scan_id IS NULL OR scan_id in (?,?,?,?,?,?,?,?,?,?)) %s ORDER BY timestamp DESC`
	isDeletedQuery := ""
	if excludeDeleted {
		isDeletedQuery = "AND is_deleted != true"
	}
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		fmt.Sprintf(baseQ, isDeletedQuery),
		REJECTED,
		UNDER_REVIEW,
		acctId,
		SUPPRESS,
		REJECTED,
		UNDER_REVIEW,
		DELEGATE_PENDING_AUTH,
		VERIFICATION_PENDING,
		UPH_RR_PENDING,
		UPH_RR_IN_PROGRESS,
		UPH_RR_PENDING_DELIVERY_INFO,
		UPH_RR_MISMATCH,
		UPH_RR_TRANSFERRING,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		var pr models.PendingRequest
		var contentsStr sql.NullString
		contents := models.StudyRequest{}
		var scan_id, ohip, ohipvc, mrn, altId, ssn, ipn, altLastName, patientId sql.NullString
		var legacyProviderID sql.NullInt64
		var timestamp time.Time
		if err := rows.Scan(&pr.FirstName, &pr.LastName, &altLastName, &patientId, &ohip, &ohipvc, &mrn, &altId, &pr.DOB, &ssn, &ipn, &contentsStr, &pr.ClinicId, &timestamp, &pr.RequestId, &legacyProviderID, &scan_id, &pr.Editable); err != nil {
			return pendingRequests, err
		}
		if scan_id.Valid {
			pr.Status = scan_id.String
		} else {
			/*scan_id is null, pending request hasn't been processed yet*/
			pr.Status = "SUPPRESS"
		}

		if legacyProviderID.Valid {
			pr.OrgId = legacyProviderID.Int64
		}

		clinic, err := orgsvcClient.GetClinicByLegacyId(ctx, pr.ClinicId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Info("error retrieving request clinic info")
		}
		pr.ProviderName = clinic.ProviderName
		pr.ClinicName = clinic.Name
		pr.ProviderLogo = clinic.Provider.Logo
		pr.IsUph = clinic.Provider.IsUph

		if ohip.Valid {
			pr.Ohip = ohip.String
			orgId := clinic.Provider.LegacyId
			pr.OrgId = orgId

			err = mysqlWithLog.QueryRowAndScan(
				ctx,
				db,
				"SELECT bc_phn FROM forms WHERE organization_id=?",
				[]interface{}{&pr.IsBcphn},
				orgId,
			)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithError(err).Error("scan rows failed")
			}
		}
		if ohipvc.Valid {
			pr.OhipVC = ohipvc.String
		}
		if mrn.Valid {
			pr.Mrn = mrn.String
		}
		if altId.Valid {
			pr.AltId = altId.String
		}
		if ssn.Valid {
			pr.Ssn = ssn.String
		}
		if ipn.Valid {
			pr.Ipn = ipn.String
		}
		if contentsStr.Valid {
			err = json.Unmarshal([]byte(contentsStr.String), &contents)
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithError(err).Error("invalid contentsStr")
			}
		}
		if altLastName.Valid {
			pr.AltLastName = altLastName.String
		}
		if patientId.Valid {
			pr.PatientId = patientId.String
		}

		pr.Contents = contents
		pr.Timestamp = timestamp.Format("2006-01-02T15:04:05")
		pr.Date = timestamp.Format("Jan 02, 2006")
		pendingRequests = append(pendingRequests, pr)
	}
	return pendingRequests, nil
}

func Create(
	ctx context.Context,
	db *sql.DB,
	clinicId int64,
	providerId int64,
	r models.Request,
	acctId string,
	ptId string,
	status string,
) (id int64, err error) {
	contentsJson, _ := json.Marshal(r.Contents)
	var res sql.Result

	// TODO: At some point remove payment_deferred from this query
	res, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT requests SET clinic_id=?,legacy_provider_id=?,first_name=?,last_name=?,alt_last_name=?,email=?,tel=?,ohip=?,ohip_vc=?,mrn=?,alt_h_id=?,dob=?,contents=?,ssn=?,ipn=?, scan_id =?,account_id=?, patient_id=?, payment_deferred=?",
		clinicId,
		providerId,
		r.FirstName,
		r.LastName,
		r.AltLastName,
		strings.ToLower(r.Email),
		r.Tel,
		r.Ohip,
		r.Ohipvc,
		r.Mrn,
		r.AltId,
		r.Dob,
		contentsJson,
		r.Ssn,
		r.Ipn,
		status,
		acctId,
		ptId,
		true,
	)
	if err != nil {
		return -1, err
	}
	id, err = res.LastInsertId()
	if err != nil {
		return -1, err
	}
	return id, nil
}

func Remove(ctx context.Context, db *sql.DB, requestId int64) error {
	_, err := mysqlWithLog.Exec(ctx, db, "DELETE FROM requests WHERE id=?", requestId)
	return err
}

func SetDelegateAuth(ctx context.Context, db *sql.DB, requestId int64) (status string, err error) {
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE requests SET scan_id='"+DELEGATE_PENDING_AUTH+"', timestamp=CURRENT_TIMESTAMP WHERE id=?",
		requestId,
	)
	if err != nil {
		return "", err
	}
	return DELEGATE_PENDING_AUTH, nil
}

func BelongsToAcct(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	requestId int64,
) (bool, error) {
	reqAcct, err := getAccountId(ctx, db, requestId)
	if err != nil {
		return false, err
	}
	return reqAcct == acctId, nil
}

func getAccountId(ctx context.Context, db *sql.DB, requestId int64) (string, error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT account_id FROM requests WHERE id=?",
		requestId,
	)
	if err != nil {
		return "", err
	}
	defer rows.Close()
	var acctId string
	for rows.Next() {
		if err := rows.Scan(&acctId); err != nil {
			return acctId, err
		}
	}
	return acctId, nil
}

// UpdateRequestById updates a request in a transaction given request
// expects caller to start & commit the transaction
func UpdateRequestById(
	ctx context.Context,
	tx *sql.Tx,
	requestId int64,
	request models.Request,
	clinicId int64,
	providerId int64,
) (status string, err error) {
	contentsJson, _ := json.Marshal(request.Contents)
	requestStatus := ""
	_, err = mysqlWithLog.ExecTx(
		ctx,
		tx,
		"UPDATE requests SET clinic_id=?,legacy_provider_id=?,first_name=?,last_name=?,alt_last_name=?,email=?,tel=?,ohip=?,ohip_vc=?,mrn=?,alt_h_id=?,dob=?,contents=?,ssn=?,ipn=? WHERE id=?",
		clinicId,
		providerId,
		request.FirstName,
		request.LastName,
		request.AltLastName,
		strings.ToLower(request.Email),
		request.Tel,
		request.Ohip,
		request.Ohipvc,
		request.Mrn,
		request.AltId,
		request.Dob,
		contentsJson,
		request.Ssn,
		request.Ipn,
		requestId,
	)
	if err != nil {
		return requestStatus, err
	}
	if request.NeedsIdVerification {
		err = SetRequestStatusTx(ctx, tx, VERIFICATION_PENDING, requestId)
		if err != nil {
			return requestStatus, err
		}
		requestStatus = VERIFICATION_PENDING
	} else {
		_, err = mysqlWithLog.ExecTx(
			ctx,
			tx,
			"UPDATE requests SET timestamp=CURRENT_TIMESTAMP, scan_id = NULL WHERE id=?",
			requestId,
		)
		if err != nil {
			return requestStatus, err
		}
		requestStatus = SUBMISSION_COMPLETE
	}
	return requestStatus, nil
}

func GetRequestStatusV2(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	r models.Request,
	providerId int64,
) (int64, string) {
	var status sql.NullString
	var reqId sql.NullInt64
	whereClause := "account_id = ? AND dob = ?"

	argsList := make([]interface{}, 2)
	argsList[0] = acctId
	argsList[1] = r.Dob

	if r.Ohip != "" {
		whereClause += " AND ohip = ?"
		argsList = append(argsList, r.Ohip)
	}
	if r.Mrn != "" {
		whereClause += " AND mrn = ?"
		argsList = append(argsList, r.Mrn)
	}
	if r.AltId != "" {
		whereClause += " AND alt_h_id = ?"
		argsList = append(argsList, r.AltId)
	}
	if r.Ssn != "" {
		whereClause += " AND ssn = ?"
		argsList = append(argsList, r.Ssn)
	}
	if r.Ipn != "" {
		whereClause += " AND ipn = ?"
		argsList = append(argsList, r.Ipn)
	}
	whereClause += " AND legacy_provider_id = ?"
	argsList = append(argsList, providerId)

	query := "SELECT id, scan_id FROM requests WHERE (scan_id IS NULL OR scan_id NOT IN ('VOID', 'UNFINALIZED')) AND " + whereClause

	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{&reqId, &status},
		argsList...)
	if err != nil || !reqId.Valid {
		return -1, ""
	}

	if status.Valid {
		return reqId.Int64, status.String
	}

	return reqId.Int64, ""
}

func SetRequestStatus(ctx context.Context, db *sql.DB, status string, requestId int64) error {
	// convert status to sql nullable string
	var sqlStatus sql.NullString
	if len(status) == 0 {
		sqlStatus = sql.NullString{}
	} else {
		sqlStatus = sql.NullString{
			String: status,
			Valid:  true,
		}
	}
	/*update request status*/
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE requests SET scan_id = ? WHERE id=?",
		sqlStatus,
		requestId,
	)
	return err
}

func SetRequestStatusTx(ctx context.Context, tx *sql.Tx, status string, requestId int64) error {
	var sqlStatus sql.NullString
	if status != "" {
		sqlStatus = sql.NullString{
			String: status,
			Valid:  true,
		}
	}

	_, err := mysqlWithLog.ExecTx(
		ctx,
		tx,
		"UPDATE requests SET scan_id = ? WHERE id=?",
		sqlStatus,
		requestId,
	)
	return err
}

func GetClinicId(
	ctx context.Context,
	db *sql.DB,
	requestId int64,
) (int64, error) {
	var clinicId int64
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT clinic_id FROM requests WHERE id=?",
		[]interface{}{&clinicId},
		requestId,
	)
	if err != nil {
		return 0, err
	}
	return clinicId, nil
}

// ExistsForAccountAndOrg checks if a request exists for the provided account and provider
func ExistsForAccountAndOrg(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	providerLegacyId int64,
) (bool, error) {
	var counter int
	err := mysqlWithLog.QueryRowAndScan(
		ctx, db,
		`SELECT COUNT(*)
		FROM requests WHERE account_id=? AND legacy_provider_id=?`,
		[]interface{}{&counter}, acctId, providerLegacyId)
	if err != nil {
		return false, err
	}
	if counter >= 1 {
		return true, nil
	}
	return false, nil
}

func SetOrderId(ctx context.Context, db *sql.DB, requestId int64, orderId string) error {
	_, err := mysqlWithLog.Exec(
		ctx, db,
		"UPDATE requests SET order_id=? WHERE id=?",
		orderId, requestId,
	)
	return err
}

// get the dob of the first request associated with the given email
func GetRequestDateOfBirth(ctx context.Context, db *sql.DB, accountId string) (time.Time, error) {
	var dob string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT dob FROM requests WHERE account_id=?",
		[]interface{}{&dob},
		accountId,
	)
	if err == sql.ErrNoRows {
		return time.Time{}, nil
	} else if err != nil {
		return time.Time{}, err
	}
	result, err := time.Parse("01/02/2006", dob)
	if err != nil {
		return time.Time{}, err
	}
	return result, nil
}

func GetRequestPatientId(ctx context.Context, db *sql.DB, requestId int64, accountId string) (string, error) {
	var patientId sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT patient_id FROM requests WHERE id = ? AND account_id = ?",
		[]interface{}{&patientId},
		requestId, accountId,
	)
	if err != nil {
		return "", err
	}
	return patientId.String, nil
}

func GetNumRequestsByPatientId(ctx context.Context, db *sql.DB, accountId string, patientId string) (int, error) {
	var count int
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT COUNT(*) FROM requests WHERE account_id=? AND patient_id=?",
		[]interface{}{&count},
		accountId,
		patientId,
	)
	return count, err
}

func SetPatientIdTx(ctx context.Context, tx *sql.Tx, requestId int64, patientId string) error {
	_, err := mysqlWithLog.ExecTx(ctx, tx, "UPDATE requests SET patient_id = ? WHERE id = ?", patientId, requestId)
	return err
}
