//go:build integration
// +build integration

package eunitytokens

import (
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
)

// deletes an entry from table eunity_tokens
func DeleteToken(t *testing.T, db *sql.DB, token string) {
	_, err := db.Exec(
		`DELETE FROM eunity_tokens 
		WHERE token=?`,
		token,
	)
	assert.NoError(t, err)
}

// deletes an entry from table eunity_tokens by share_id
func DeleteTokenByShareID(t *testing.T, db *sql.DB, shareID string) {
	_, err := db.Exec(
		`DELETE FROM eunity_tokens 
		WHERE share_id=?`,
		shareID,
	)
	assert.NoError(t, err)
}

// checks if there is an entry in tableeunity_tokens for share_id
func HasTokenForShareID(t *testing.T, db *sql.DB, shareID string) bool {
	var count int
	err := db.QueryRow(
		`SELECT COUNT(*) FROM eunity_tokens 
		WHERE share_id=?`,
		shareID,
	).Scan(&count)
	assert.NoError(t, err)

	return count > 0

}
