package eunitytokens

import (
	"context"
	"database/sql"

	"gitlab.com/pockethealth/coreapi/pkg/util/secure"

	_ "github.com/go-sql-driver/mysql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func Create(ctx context.Context, db *sql.DB, shareId string) (token string, err error) {
	token, err = secure.GenerateRandomString(32)
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT into eunity_tokens (token, share_id, expiry) VALUES(?, ?, NOW()+INTERVAL 12 HOUR);",
		token,
		shareId,
	)
	return token, err
}

func GetShareID(ctx context.Context, db *sql.DB, token string) (shareId string, err error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT share_id from eunity_tokens WHERE token=? and expiry > NOW()",
		token,
	)
	if err != nil {
		return "", nil
	}
	defer rows.Close()
	if rows.Next() {
		err = rows.Scan(&shareId)
		return shareId, err
	}
	return "", err
}
