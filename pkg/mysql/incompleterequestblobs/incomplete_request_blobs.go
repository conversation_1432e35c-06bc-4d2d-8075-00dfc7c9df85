package mysql

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

type FileType int64

const (
	SIGNATURE FileType = iota
	DELEGATE_FORM
	DELEGATE_PHOTO
)

func (s FileType) String() string {
	switch s {
	case SIGNATURE:
		return "signature"
	case DELEGATE_FORM:
		return "delegate_form"
	case DELEGATE_PHOTO:
		return "delegate_photo"
	}
	return "unknown"
}

func Create(
	ctx context.Context,
	db *sql.DB,
	blobId string,
	incompleteRequestId string,
	fileType FileType,
	contentType string,
) error {
	// overwrite is okay here because blob uploads overwrite in Azure
	// Multiple uploads on the same blob_id will result in only the latest blob being stored
	_, err := mysqlWithLog.Exec(
		ctx, db,
		`INSERT INTO incomplete_request_blobs 
		SET blob_id=?, incomplete_request_id=?, file_type=?, content_type=?
		ON DUPLICATE KEY UPDATE blob_id=?, incomplete_request_id=?, file_type=?, content_type=?`,
		blobId, incompleteRequestId, fileType, contentType,
		blobId, incompleteRequestId, fileType, contentType,
	)
	return err
}

func GetBlobsByIncompleteRequestId(
	ctx context.Context,
	db *sql.DB,
	incompleteRequestId string,
) (result []IncompleteRequestBlob, err error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT blob_id, file_type, content_type, created_timestamp, updated_timestamp FROM incomplete_request_blobs WHERE incomplete_request_id=?",
		incompleteRequestId,
	)
	if err != nil {
		return result, err
	}
	defer rows.Close()
	for rows.Next() {
		var blob IncompleteRequestBlob
		err = rows.Scan(
			&blob.BlobId,
			&blob.FileType,
			&blob.ContentType,
			&blob.CreatedAt,
			&blob.UpdatedAt,
		)
		blob.IncompleteRequestId = incompleteRequestId
		if err == nil {
			result = append(result, blob)
		}
	}
	return result, nil
}

func GetExpiredBlobIds(ctx context.Context, db *sql.DB) (result []string, err error) {
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT blob_id FROM incomplete_request_blobs WHERE created_timestamp <= DATE_SUB(NOW(), INTERVAL 1 WEEK)",
	)
	if err != nil {
		return []string{}, err
	}
	defer rows.Close()
	if rows.Next() {
		var id string
		err = rows.Scan(&id)
		result = append(result, id)
	}
	return result, nil
}

func DeleteBlob(
	ctx context.Context,
	db *sql.DB,
	blobId string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"DELETE FROM incomplete_request_blobs WHERE blob_id=?",
		blobId,
	)
	return err
}
