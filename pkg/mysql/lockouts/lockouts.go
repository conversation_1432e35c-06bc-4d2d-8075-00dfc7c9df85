package mysql

import (
	"context"
	"database/sql"
	"errors"
	"time"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func GetResourceAttempts(
	ctx context.Context,
	db *sql.DB,
	resource string,
) (attempts int, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"Select attempts from lockout_tracker where resource=?",
		[]interface{}{&attempts},
		resource,
	)
	return attempts, err
}

func InsertAttempt(ctx context.Context, db *sql.DB, resource string, attempts int) error {
	//race condition between querying attempts and this insert, so update if duplicate key error
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"Insert into lockout_tracker (resource, attempts, lock_time) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE attempts = attempts + 1",
		resource,
		attempts,
		nil,
	)
	return err
}

func LockResource(ctx context.Context, db *sql.DB, resource string) error {
	//set relative to existing record to avoid race conditions
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"Update lockout_tracker set attempts = attempts + 1, lock_time = CASE WHEN lock_time IS NULL THEN ? ELSE lock_time END where resource = ?",
		time.Now(),
		resource,
	)
	return err
}

func IncrementAttempts(ctx context.Context, db *sql.DB, resource string) error {

	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"Update lockout_tracker set attempts = attempts + 1 where resource = ?",
		resource,
	)
	return err
}

func GetLockedTime(ctx context.Context, db *sql.DB, resource string) (time.Time, error) {
	var lock sql.NullTime
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"Select lock_time from lockout_tracker where resource=?",
		[]interface{}{&lock},
		resource,
	)
	if err != nil || !lock.Valid {
		return time.Time{}, errors.New("not locked")
	}

	return lock.Time, nil
}

func ResetResource(ctx context.Context, db *sql.DB, resource string) error {
	_, err := mysqlWithLog.Exec(ctx, db, "DELETE from lockout_tracker where resource=?", resource)
	return err
}
