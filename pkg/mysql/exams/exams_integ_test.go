//go:build integration
// +build integration

package exams

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	phtestutil "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

func TestGetPatientNameAndDOBByExamUUID(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("gets name and dob from exams", func(t *testing.T) {
		testExam := CreateAndInsertTestExam(t, db)
		t.Cleanup(func() {
			DeleteExam(t, db, testExam.UUID)
		})

		patientName, patientDOB, referringPhysician, err := GetPatientDataAndReferringPhysicianByExamUUID(
			db,
			testExam.UUID,
		)
		assert.NoError(t, err)
		assert.Equal(t, testExam.DICOMPatientName, patientName)
		assert.Equal(t, testExam.DICOMBirthDate, patientDOB)
		assert.Equal(t, testExam.DICOMReferringPhysician, referringPhysician)
	})

	t.Run("returns error if no match is found", func(t *testing.T) {
		examUUID := phtestutil.GenerateRandomString(t, 10)
		patientName, patientDOB, referringPhysician, err := GetPatientDataAndReferringPhysicianByExamUUID(
			db,
			examUUID,
		)
		assert.Error(t, err)
		assert.Equal(t, "", patientName)
		assert.Equal(t, "", patientDOB)
		assert.Equal(t, "", referringPhysician)
	})
}
