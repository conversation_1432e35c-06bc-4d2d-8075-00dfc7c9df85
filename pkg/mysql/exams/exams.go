package exams

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	orgs "gitlab.com/pockethealth/coreapi/pkg/mysql/organizations"
	providers "gitlab.com/pockethealth/coreapi/pkg/mysql/providers"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// source types
const (
	PatientSharing = "PS"
	P2P            = "P2P"
	CDUpload       = "CD"
)

const ExamNotRevokedWhere = `NOT
(e.access_revoked_at IS NOT NULL AND
	 (e.access_restored_at IS NULL OR
		e.access_restored_at > NOW() OR
		e.access_restored_at < e.access_revoked_at))`

func isImageModality(modality string) bool {
	blockedModalities := []string{"KO", "PR", "SR"}
	for i := range blockedModalities {
		if modality == blockedModalities[i] {
			return false
		}
	}
	return true
}

func GetLastNameFromExam(ctx context.Context, db *sql.DB, uuid string) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"exam_id": uuid,
	})
	var patientName string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT patient_name FROM exams WHERE uuid=?",
		[]interface{}{&patientName},
		uuid,
	)
	if err != nil {
		lg.WithError(err).Error("failed to retrieve patient name")
		return "", err
	}

	_, lastName := dcmtools.ParseFirstLastName(ctx, patientName)
	return lastName, nil
}

// This is a silly little helper that checks if the caller is querying by study UID, which is
// exposed as the ?uid query parameter (see queries.WithQueries() usage in
// privateUsersExamsApiController.GetUserExams()). The queries package will soon expose a way to
// get this info, but for now we have to get around it circuitously by looking at the context,
// where the queries object has stored the query parameters
func IsUidSearch(ctx context.Context) bool {
	fields := logutils.Fields(ctx)
	_, ok := fields["uid"] // "uid" is the registered query parameter name on `queries`
	return ok
}

// Similar helper to IsUidSearch() that checks if the caller is querying by account ID
func IsAccountIDSearch(ctx context.Context) bool {
	fields := logutils.Fields(ctx)
	_, ok := fields["account_id"] // "account_id" is the registered query parameter name on `queries`
	return ok
}

// Attn: this fn populates a subset of Exam attributes intended to provide a more high performing function to retrieve a summary view of a list of exams.
// The behaviour of populating the reports list is controlled by needReports.
// Series list & size will never be populated.
func GetExamSummaryData(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	uuidList []string,
	q queries.Queries,
	needReports bool,
	excludeDeleted bool,
	orgsvcClient orgservice.OrgService,
) (exams []coreapi.ExamRaw, err error) {
	ctx = q.WithFields(logutils.WithFields(ctx,
		"exam_id", uuidList,
		"GetExams", time.Now().UTC(),
	))

	// If the acctID is not empty, the request is authorised by JWT. Otherwise, a service
	// is looking for something by UUID, UID or account ID. Unbounded queries are not supported
	if acctId == "" && len(uuidList) == 0 && !IsUidSearch(ctx) && !IsAccountIDSearch(ctx) {
		return nil, errors.New("no exam filter criteria")
	}
	examMap := map[string]coreapi.ExamRaw{}

	if acctId != "" {
		q.AddExtraWhere("e.account_id", acctId)
	}
	if excludeDeleted {
		q.AddExtraWhere("e.is_deleted != true")
	}
	if len(uuidList) != 0 { // get specific ids
		examArgs := make([]any, len(uuidList))
		for i := range uuidList {
			examArgs[i] = uuidList[i]
		}
		q.AddExtraWhere("e.uuid", examArgs...)
	}

	where, args := q.Where()
	queryStr := getExamsBaseQuery() + " AND " + where

	start := time.Now()
	rows, err := mysqlWithLog.Query(ctx, db, queryStr, args...)
	elapsed := time.Since(start)

	logutils.Infox(ctx, "get exams query",
		"acct_id", acctId,
		"query_duration_ms", elapsed.Milliseconds(),
	)

	logutils.Infox(ctx, "examLatency",
		"examQuery", time.Now().UTC(),
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return []coreapi.ExamRaw{}, nil // user just doesn't have exams
		}
		return nil, err
	}

	defer rows.Close()
	start = time.Now()
	for rows.Next() {
		var sqlOriginId sql.NullInt64
		exam := coreapi.ExamRaw{}
		var modality, scanModality, patientId, accountId sql.NullString
		var hasReport sql.NullBool
		var transferDate sql.NullTime
		var attributeOrderId sql.NullString
		var attributedAt sql.NullTime
		var source string
		err := rows.Scan(
			&exam.UUID,
			&exam.ExamId,
			&exam.TransferId,
			&exam.TransferStatus,
			&exam.Activated,
			&exam.DICOMReferringPhysician,
			&exam.Description,
			&exam.DICOMExamDate,
			&exam.DICOMPatientName,
			&exam.PatientMrn,
			&exam.DICOMBirthDate,
			&exam.Sex,
			&exam.Phone,
			&exam.BodyPart,
			&accountId,
			&patientId,
			&sqlOriginId,
			&source,
			&modality,
			&hasReport,
			&scanModality,
			&transferDate,
			&attributeOrderId,
			&attributedAt,
		)
		if err != nil {
			return nil, err
		}

		exam.ExamType = dcmtools.ParseType(modality.String)
		exam.Modality = modality.String
		if !isImageModality(exam.Modality) && scanModality.Valid {
			exam.Modality = scanModality.String
		}

		if hasReport.Bool {
			// this will get replaced if we require actual reports
			exam.Reports = []coreapi.Report{{ReportId: ""}}
		}

		var originId uint
		if sqlOriginId.Valid {
			originId = uint(sqlOriginId.Int64) // #nosec G115 not worrying about max_uint
		}
		err = populateExamFields(
			ctx,
			db,
			&exam,
			transferDate,
			exam.Activated,
			needReports,
			originId,
			source,
			orgsvcClient,
		)
		if err != nil {
			return nil, err
		}
		if accountId.Valid {
			exam.AccountId = accountId.String
		}
		if patientId.Valid {
			exam.PatientId = patientId.String
		}

		if transferDate.Valid {
			exam.TransferDate = transferDate.Time.Format("2006-01-02T15:04:05Z")
		}
		if attributeOrderId.Valid {
			exam.AttributeOrderId = attributeOrderId.String
		}
		if attributedAt.Valid {
			exam.AttributedAt = attributedAt.Time
		}

		examMap[exam.UUID] = exam
	}
	elapsed = time.Since(start)

	logutils.Infox(ctx, "get reports loop",
		"acct_id", acctId,
		"loop_duration_ms", elapsed.Milliseconds(),
	)

	if len(examMap) > 0 {
		var examList []coreapi.ExamRaw
		for _, v := range examMap {
			if v.Activated && v.Provider != "" {
				v.ReportDelay = int(
					orgs.GetReportSendDelayV2(
						ctx,
						db,
						v.Provider,
						strings.Split(v.ExamType, ",")[0],
						v.ProviderId,
						v.OrgId,
						orgsvcClient,
					),
				) // use modality of 1st image
			}
			examList = append(examList, v)
		}

		// sort list by date desc
		sort.Slice(examList, func(i, j int) bool {
			date1, _ := time.Parse(coreapi.DICOMDateFormat, examList[i].DICOMExamDate)
			date2, _ := time.Parse(coreapi.DICOMDateFormat, examList[j].DICOMExamDate)
			return date1.After(date2)
		})

		return examList, nil
	} else {
		return []coreapi.ExamRaw{}, nil
	}
}

// GetExamSummaryDataForRecordStreamingStudy gets a base exam object
// that can be used to create a share object for displaying the given study.
// NOTE: ignores activation, deletion, revoke logic on exam object
// NOTE: copies workflow from existing share logic.
// Consider refactoring in the future to remove unnecessary data and data processing
func GetExamSummaryDataForRecordStreamingStudy(
	ctx context.Context,
	db *sql.DB,
	examUUID string,
	providerID int64,
) (coreapi.ExamRaw, error) {
	query := `SELECT
			e.uuid, e.exam_uid, e.transfer_id, e.transfer_status, e.activated,
			e.referring_physician, e.description, e.date, e.patient_name, e.patient_mrn,
			e.dob, e.sex, e.phone, e.body_part, e.patient_id, e.account_id, e.modality, e.has_report
		FROM exams e
		WHERE e.uuid = ?`

	var exam coreapi.ExamRaw
	var modality, patientId, accountId sql.NullString
	var hasReport sql.NullBool

	err := mysqlWithLog.QueryRowAndScan(ctx, db, query, []interface{}{
		&exam.UUID,
		&exam.ExamId,
		&exam.TransferId,
		&exam.TransferStatus,
		&exam.Activated,
		&exam.DICOMReferringPhysician,
		&exam.Description,
		&exam.DICOMExamDate,
		&exam.DICOMPatientName,
		&exam.PatientMrn,
		&exam.DICOMBirthDate,
		&exam.Sex,
		&exam.Phone,
		&exam.BodyPart,
		&patientId,
		&accountId,
		&modality,
		&hasReport,
	}, examUUID)
	if err != nil {
		return coreapi.ExamRaw{}, err
	}

	// set account and patient id
	exam.AccountId = accountId.String
	exam.PatientId = patientId.String
	// set modality and exam type
	exam.ExamType = dcmtools.ParseType(modality.String)
	exam.Modality = modality.String
	// set up report
	if hasReport.Bool {
		// get reports for exam
		exam.Reports, err = getReports(ctx, db, exam.UUID, exam.Activated, providerID)
		if err != nil {
			return coreapi.ExamRaw{}, nil
		}
	}

	return exam, nil
}

func SetRawExamDisplayData(
	ctx context.Context,
	db *sql.DB,
	rawExam coreapi.ExamRaw,
	provider orgservice.Provider,
) coreapi.ExamRaw {
	// check if provider data is empty
	if provider.IsValid() {
		rawExam.Provider = provider.Name
		rawExam.OrgName = provider.Name
		rawExam.AllowPtPngDownloads = provider.AllowDownload
		rawExam.ProviderId = provider.Id
		rawExam.OrgId = provider.LegacyId
	} else {
		// this can happen when a clinic gets deactivated improperly - use fallback values
		rawExam.Provider = "Unknown"
		rawExam.OrgName = "Unknown"
		rawExam.AllowPtPngDownloads = true
	}
	return rawExam
}

func populateExamFields(
	ctx context.Context,
	db *sql.DB,
	exam *coreapi.ExamRaw,
	transferDate sql.NullTime,
	activated bool,
	needReports bool,
	originId uint,
	source string,
	orgsvcClient orgservice.OrgService,
) error {
	var clinicId int64
	var orgId int64 = 0 // default to 0

	exam.AllowPtPngDownloads = true
	if source != CDUpload { // CDs don't have an org we can look up
		// lookup clinic name from origin id.
		prov, err := providers.GetClinicDisplayInfoFromOriginId(
			ctx,
			db,
			int64(originId), // #nosec G115 origin ID > 0
			orgsvcClient,
		)
		if err != nil {
			// this shouldn't happen, but it does. Sometimes clinics are deactivated improperly.
			logutils.CtxLogger(ctx).WithField("origin_id", originId).
				WithError(err).Error("origin id is missing")
			exam.Provider = "Unknown"
			exam.OrgName = "Unknown"
		} else {
			exam.Provider = prov.Name
			exam.OrgName = prov.OrgName
			exam.AllowPtPngDownloads = prov.PtPngDownload
			clinicId = prov.ClinicId
			orgId = prov.OrgId
			exam.ProviderId = prov.ProviderId
			exam.FacilityFunded = slices.ContainsFunc(
				prov.ProviderPlans,
				func(plan orgservice.ProviderPlan) bool {
					return plan.PlanId == orgservice.FacilityFundedPlanId
				},
			)
		}
	}
	exam.OrgId = orgId // will be 0 if its self-upload or error as early return won't be ideal if there's an error with orgId.

	if activated && needReports {
		reports, err := getReports(ctx, db, exam.UUID, exam.Activated, clinicId)
		if err != nil {
			return err
		}
		exam.Reports = reports
	}

	if transferDate.Valid {
		exam.TransferDate = transferDate.Time.Format("2006-01-02T15:04:05Z")
	}

	return nil
}

func getExamsBaseQuery() string {
	return fmt.Sprintf(
		`SELECT e.uuid, e.exam_uid, e.transfer_id, e.transfer_status, e.activated, e.referring_physician, e.description, e.date, e.patient_name, e.patient_mrn, e.dob,
		   e.sex, e.phone, e.body_part, e.account_id, e.patient_id, s.origin_id, s.source, e.modality, e.has_report, s.modality, s.uploaded, eo.order_id, eo.attributed_at
	FROM exams e
	JOIN scans s ON e.transfer_id = s.scan_id
    LEFT JOIN gateway_users gw ON s.origin_id = gw.user_id
	LEFT JOIN exam_orders eo ON e.uuid = eo.exam_uuid
	WHERE NOT (COALESCE(gw.user_type,'') = 'dnd' && e.accession_number = '') AND %s`,
		ExamNotRevokedWhere,
	)
}

func getSeriesObjectQueryTables() string {
	return `FROM objects o
	JOIN object_mappings om ON o.object_id = om.object_id
	JOIN view_metadata v ON v.object_id = o.object_id
	WHERE om.exam_uuid = ? AND o.is_report = 0  AND v.sop_class_uid != "1.2.840.10008.*******.1.66"` + // this sop class is RawDataStorage, not image
		`AND (om.has_pixel_data = 1 OR om.has_pixel_data IS NULL) ` + // om.has_pixel_data is NULL for historical images.
		`ORDER BY om.series_uid DESC, o.instance_number ASC`
}

// ATTN: this function DOES NOT filter out exams that have had access revoked
func GetFirstImageID(ctx context.Context, db *sql.DB, uuid string) (string, error) {
	var imageId string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT o.object_id `+getSeriesObjectQueryTables()+` LIMIT 1`,
		[]interface{}{&imageId},
		uuid,
	)
	if err != nil {
		return "", err
	}

	return imageId, nil
}

func getReports(
	ctx context.Context,
	db *sql.DB,
	examUuid string,
	activated bool,
	clinicId int64,
) ([]coreapi.Report, error) {
	var reports []coreapi.Report
	rows, err := mysqlWithLog.Query(ctx, db, `SELECT o.object_id, om.size, o.protocol
	FROM objects o
	JOIN object_mappings om ON o.object_id = om.object_id
	WHERE om.exam_uuid = ? AND o.is_report = 1`, examUuid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		var objId sql.NullString
		var size int64
		var protocol string
		err := rows.Scan(&objId, &size, &protocol)
		if err != nil {
			return nil, err
		}
		reports = append(
			reports,
			coreapi.Report{
				ReportId:    objId.String,
				ClinicId:    clinicId,
				Activated:   activated,
				Size:        size,
				Protocol:    protocol,
				Definitions: true, // always true now
			},
		)
	}
	return reports, nil
}

func LookupExamUUID(
	ctx context.Context,
	db *sql.DB,
	accountId string,
	accession string,
	mrn string,
) (uuid string, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountId,
		"accession":  accession,
		"mrn":        mrn,
	})

	err = mysqlWithLog.QueryRowAndScan(ctx, db, fmt.Sprintf(`SELECT e.uuid
	FROM exams e
	WHERE e.account_id = ? AND e.accession_number = ? AND e.patient_mrn = ? AND %s
	LIMIT 1`,
		ExamNotRevokedWhere), []interface{}{&uuid}, accountId, accession, mrn)
	if err != nil {
		lg.Error("failed to retrieve exam uuid and dob")
		return "", err
	}

	return uuid, nil
}

// v3 transfer: exams can be shared only after they have been fully transferred
func CheckExamsNotFullyTransferred(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	examList []coreapi.Exam,
) (bool, error) {
	qs := make([]string, len(examList))
	interfaceArr := make([]interface{}, len(examList))
	for i := range qs {
		qs[i] = "?"
		interfaceArr[i] = examList[i].UUID
	}

	var notTransferred bool
	err := mysqlWithLog.QueryRowAndScan(ctx, db, `SELECT EXISTS(
		SELECT 1 FROM exams
		WHERE uuid IN (`+strings.Join(qs, ",")+`) AND transfer_status IN ('Initialized', 'Prefetched')
	) AS not_transferred`, []interface{}{&notTransferred}, interfaceArr...)

	return notTransferred, err
}

func GetObjectIdsByExamUUIDs(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	examUUIDs []string,
	activated bool,
) ([]string, error) {
	qs := make([]string, len(examUUIDs))
	interfaceArr := make([]interface{}, len(examUUIDs))
	for i := range qs {
		qs[i] = "?"
		interfaceArr[i] = examUUIDs[i]
	}
	interfaceArr = append(interfaceArr, activated)
	rows, err := mysqlWithLog.Query(ctx, db, fmt.Sprintf(`SELECT e.account_id, om.object_id
		FROM exams e
		JOIN object_mappings om ON om.exam_uuid = e.uuid
		WHERE e.uuid IN (`+strings.Join(qs, ",")+`) AND activated = ? AND %s`, ExamNotRevokedWhere), interfaceArr...)
	if err != nil {
		return []string{}, err
	}

	defer rows.Close()
	var objIds []string
	for rows.Next() {
		var objId string
		var sqlAcctId sql.NullString
		err := rows.Scan(&sqlAcctId, &objId)
		if err != nil {
			if err == sql.ErrNoRows {
				return []string{}, nil
			}
			return []string{}, err
		}
		if acctId != "" &&
			sqlAcctId.Valid { // when acct ID is passed in, we expect the only matches for a given exam uuid to match that acct
			if acctId != sqlAcctId.String {
				logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
					"acct_id":    acctId,
					"sql_acct":   sqlAcctId.String,
					"exam_uuids": examUUIDs,
				}).Error("exam uuid pulled up different acct than expected")
				continue
			}
		}
		objIds = append(objIds, objId)
	}
	return objIds, nil
}

func GetExamUUIDListForShare(ctx context.Context, db *sql.DB, shareId string) ([]string, error) {
	rows, err := mysqlWithLog.Query(ctx, db, fmt.Sprintf(`SELECT DISTINCT exam_uuid
	FROM share_objects2 so
	JOIN object_mappings om ON so.object_id = om.object_id
	JOIN exams e on e.uuid = exam_uuid
	WHERE so.share_id = ? AND %s`, ExamNotRevokedWhere), shareId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var examIdList []string
	for rows.Next() {
		var id string
		err := rows.Scan(&id)
		if err != nil {
			return nil, err
		}
		examIdList = append(examIdList, id)

	}
	return examIdList, nil
}

func GetUnassociatedReports(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	activated bool,
	excludeDeleted bool,
	orgsvcClient orgservice.OrgService,
) ([]coreapi.Report, error) {
	isDeletedQuery := ""
	if excludeDeleted {
		isDeletedQuery = "AND e.is_deleted != true"
	}

	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		fmt.Sprintf(
			`SELECT om.object_id, s.patient_name, e.sex, om.size, e.activated, s.origin_id, s.uploaded, e.dob, e.referring_physician
	FROM objects o
	JOIN object_mappings om ON o.object_id = om.object_id
	JOIN exams e ON om.exam_uuid = e.uuid
	JOIN scans s ON o.scan_id = s.scan_id
	JOIN gateway_users gu ON gu.user_id = s.origin_id
	WHERE gu.user_type = 'dnd' AND e.account_id = ? AND e.activated = ? AND is_report = 1 AND %s %s`,
			ExamNotRevokedWhere,
			isDeletedQuery,
		),
		acctId,
		activated,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	reportList := make([]coreapi.Report, 0)
	for rows.Next() {
		var rep coreapi.Report
		var sqlOriginId sql.NullInt64
		err := rows.Scan(
			&rep.ReportId,
			&rep.PatientName.DicomName,
			&rep.Sex,
			&rep.Size,
			&rep.Activated,
			&sqlOriginId,
			&rep.UploadTime,
			&rep.Dob,
			&rep.ReferringPhysician,
		)
		if err != nil {
			return nil, err
		}
		var origin int64
		if sqlOriginId.Valid {
			origin = sqlOriginId.Int64
		}
		if rep.PatientName.DicomName != "" {
			f, l := dcmtools.ParseFirstLastName(ctx, rep.PatientName.DicomName)
			rep.PatientName.FirstAndMiddleName = f
			rep.PatientName.LastName = l
		}
		// put upload time in format: 2021-02-09 17:55:11.795 +0000 UTC
		// SQL stores as 2021-02-09T17:55:10Z

		timeObj, err := time.Parse("2006-01-02T15:04:05Z", rep.UploadTime)
		if err == nil { // leave as blank if it can't be parsed
			rep.UploadTime = timeObj.Format("2006-01-02 15:04:05 -0700 MST")
		}

		prov, err := providers.GetClinicDisplayInfoFromOriginId(ctx, db, origin, orgsvcClient)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithError(err).
				WithField("origin_id", origin).
				Error("error getting clinic name for origin")
		} else {
			rep.ClinicName = prov.Name
			rep.OrgName = prov.OrgName
		}
		reportList = append(reportList, rep)
	}
	return reportList, nil
}

func GetPatientNameAndDOB(
	ctx context.Context,
	db *sql.DB,
	acctId string,
) (patientName string, dob string, err error) {
	var name string
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT patient_name, dob FROM exams WHERE account_id = ? AND patient_name != '' AND dob != '' LIMIT 1",
		[]interface{}{&name, &dob},
		acctId,
	)
	if err != nil {
		return "", "", err
	}

	// parse pt name, it's in DICOM format
	parsedName := dcmtools.ParseName(ctx, name)
	return parsedName, dob, nil
}

// GetPatientDataAndReferringPhysicianByExamUUID
// returns patient name and dob and referring physician
// for a study with a given exam uuid.
func GetPatientDataAndReferringPhysicianByExamUUID(
	db *sql.DB,
	examUUID string,
) (string, string, string, error) {
	var patientName, patientDob string
	var referringPhysician sql.NullString
	// get all uploaded
	err := db.QueryRow(
		`SELECT patient_name, dob, referring_physician FROM exams WHERE uuid=?`,
		examUUID,
	).Scan(&patientName, &patientDob, &referringPhysician)
	if err != nil {
		return "", "", "", err
	}
	return patientName, patientDob, referringPhysician.String, nil
}

// GetPatientDataAndReferringPhysicianByExamUUID
// returns returns a list of unique referring physicians
// for for exams within the share with the given shareID.
func GetReferringPhysiciansByShareID(
	db *sql.DB,
	shareID string,
) ([]string, error) {
	rows, err := db.Query(`
	SELECT DISTINCT
		e.referring_physician
	FROM
		exams e
	INNER JOIN
		object_mappings om
		ON e.uuid = om.exam_uuid
	INNER JOIN
		share_objects2 so
		ON so.object_id = om.object_id
	WHERE
		so.share_id=?`,
		shareID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	referringPhysicians := make([]string, 0)
	for rows.Next() {
		var referringPhysician sql.NullString
		err := rows.Scan(&referringPhysician)
		if err != nil {
			return nil, err
		}

		if referringPhysician.Valid {
			referringPhysicians = append(referringPhysicians, referringPhysician.String)
		}
	}
	return referringPhysicians, nil
}

// IsBreastSubdictionary returns if subdictonary of an exam can be breast
// based on the modality, body part, and study description of the exam
// breast rules: https://linear.app/pockethealth/issue/PE-200/corefe-pass-exam-type-to-taggedhtml#comment-66f17b37
func IsBreastSubdictionary(
	modality string,
	bodyPart string,
	studyDescription string,
) string {
	loweredModality := strings.ToLower(modality)
	bodyPart = strings.ToLower(bodyPart)
	studyDescription = strings.ToLower(studyDescription)
	if modality == "MG" || modality == "Mammograph" || modality == "Mammo" {
		return "breast"
	} else if strings.Contains(modality, "MG") {
		return "breast"
	} else if strings.Contains(modality, "US") || strings.Contains(loweredModality, "ultrasound") {
		if strings.Contains(bodyPart, "breast") {
			return "breast"
		} else if strings.Contains(studyDescription, "breast") {
			return "breast"
		}
	} else if strings.Contains(modality, "MR") || strings.Contains(loweredModality, "mri") {
		if strings.Contains(bodyPart, "breast") {
			return "breast"
		} else if strings.Contains(studyDescription, "breast") {
			return "breast"
		}
	}
	return ""
}
