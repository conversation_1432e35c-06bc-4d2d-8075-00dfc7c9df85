//go:build integration
// +build integration

package mysql

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestIsAcctEnrolled(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("when enrollment exists, should return true", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		// these clinic and org IDs correspond to test entries in our dbs
		providerLegacyId := int64(9)
		email := fmt.Sprintf("<EMAIL>", rand)

		// setup enrollments db entry
		res, err := db.Exec(
			`INSERT INTO enrollments(org_id, email, account_id) VALUES (?, ?, ?)`,
			providerLegacyId, email, rand,
		)
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}
		enrollmentId, err := res.LastInsertId()
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM enrollments WHERE id=?", enrollmentId)
		})

		enrolled, err := IsAcctEnrolled(context.Background(), db, rand, providerLegacyId)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}

		if !enrolled {
			t.Errorf("expected true, got %t", enrolled)
		}
	})

	t.Run("when no enrollment exists, should return false", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		// this clinic ID corresponds to a test entry in our dbs
		providerLegacyId := int64(9)

		enrolled, err := IsAcctEnrolled(context.Background(), db, rand, providerLegacyId)
		if err != nil {
			t.Errorf("got error expected none: %q", err.Error())
		}

		if enrolled {
			t.Errorf("expected false, got %t", enrolled)
		}
	})
}

func TestIsSSOEnrolled(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("when SSO enrollment exists for account and provider, return true", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		providerLegacyId := int64(9)
		email := fmt.Sprintf("<EMAIL>", rand)
		testMrn := fmt.Sprintf("testmrn+%s", rand)

		// setup SSO enrollment db entry
		res, err := db.Exec(
			`INSERT INTO enrollments(org_id, mrn, email, audit_user, account_id) VALUES (?, ?, ?, ?, ?)`,
			providerLegacyId, testMrn, email, "SSO_AUTHORIZATION", rand,
		)
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}
		enrollmentId, err := res.LastInsertId()
		if err != nil {
			t.Fatalf("unable to setup test data: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM enrollments WHERE id=?", enrollmentId)
		})

		result := IsSSOEnrolled(context.Background(), db, rand, providerLegacyId)

		if !result {
			t.Errorf("expected true, got %t", result)
		}
	})

	t.Run("when no SSO enrollment exists for account and provider, return false", func(t *testing.T) {
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		providerLegacyId := int64(9)

		result := IsSSOEnrolled(context.Background(), db, rand, providerLegacyId)
		if result {
			t.Errorf("expected false, got %t", result)
		}
	})
}
