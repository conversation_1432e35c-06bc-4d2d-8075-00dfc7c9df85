package mysql

import (
	"context"
	"database/sql"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func AttributePatient(ctx context.Context, db *sql.DB, enrollId int64, patientId string) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("enrollment_id", enrollId)
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		`UPDATE enrollments SET patient_id=? WHERE id=?`,
		patientId,
		enrollId,
	)
	if err != nil {
		lg.WithError(err).Error("failed to attribute patient")
		return err
	}
	return nil
}

func GetEnrollment(
	ctx context.Context,
	db *sql.DB,
	accountId string,
	orgId int64,
	mrn string,
) (enrollment coreapi.Enrollment, err error) {
	query := `SELECT e.id,e.org_id,e.mrn,e.email,e.audit_user,DATE_FORMAT(e.timestamp,'%b %d, %Y'),e.request_id,e.account_id,e.patient_id
		FROM enrollments e
		WHERE e.org_id = ? AND e.account_id = ? AND e.mrn= ? AND e.active = true`
	var requestId sql.NullInt64
	var patientId sql.NullString
	resArr := []interface{}{
		&enrollment.Id,
		&enrollment.OrgId,
		&enrollment.Mrn,
		&enrollment.Email,
		&enrollment.AuditUser,
		&enrollment.Timestamp,
		&requestId,
		&enrollment.AccountId,
		&patientId,
	}

	args := []interface{}{orgId, accountId, mrn}

	err = mysqlWithLog.QueryRowAndScan(
		ctx, db, query,
		resArr, args...)
	if err != nil {
		return coreapi.Enrollment{}, err
	}

	if requestId.Valid {
		enrollment.RequestId = requestId.Int64
	}
	if patientId.Valid {
		enrollment.PatientId = patientId.String
	}
	return enrollment, nil
}

func IsPatientEnrolled(
	ctx context.Context,
	db *sql.DB,
	accountId string,
	patientId string,
	providerLegacyId int64,
) (enrolled bool, requestId int64) {
	err := mysqlWithLog.QueryRowAndScan(
		ctx, db,
		`SELECT active, request_id
		FROM enrollments WHERE org_id=? AND account_id=? AND patient_id=?`,
		[]interface{}{&enrolled, &requestId}, providerLegacyId, accountId, patientId,
	)

	if err != nil {
		return false, -1
	}

	return enrolled, requestId
}

func IsAcctEnrolled(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	providerLegacyId int64,
) (enrolled bool, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx, db,
		`SELECT active
		FROM enrollments WHERE org_id=? AND account_id=?`,
		[]interface{}{&enrolled}, providerLegacyId, acctId,
	)

	if err != nil {
		if err != sql.ErrNoRows {
			return false, err
		}
		// no rows found means no enrollment
		enrolled = false
	}

	return enrolled, nil
}

func IsSSOEnrolled(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	providerLegacyId int64,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"accountId:":  acctId,
		"providerId:": providerLegacyId,
	})

	result := false
	query := `SELECT EXISTS (SELECT 1 FROM enrollments WHERE org_id=? AND account_id=? AND audit_user=?)`

	err := db.QueryRow(query, providerLegacyId, acctId, "SSO_AUTHORIZATION").Scan(&result)
	if err != nil {
		if err == sql.ErrNoRows {
			lg.WithError(err).Info("cannot find any sso enrollment")
			return false
		}
		lg.WithError(err).Info("cannot query for sso enrollment")
		return false
	}

	return result
}

func GetAccountEnrolmentProviders(ctx context.Context, db *sql.DB, accountId string) ([]int64, error) {
	rows, err := db.Query("SELECT DISTINCT org_id FROM enrollments WHERE account_id = ?", accountId)
	if err != nil {
		return []int64{}, err
	}
	defer rows.Close()
	orgIds := []int64{}
	for rows.Next() {
		var orgId int64
		if err := rows.Scan(&orgId); err != nil {
			continue
		}
		orgIds = append(orgIds, orgId)
	}
	return orgIds, nil
}

func GetEnrolmentByAccountId(ctx context.Context, db *sql.DB, accountId string) ([]coreapi.Enrollment, error) {
	lg := logutils.DebugCtxLogger(ctx)
	rows, err := mysqlWithLog.Query(ctx, db, `SELECT org_id, patient_id, request_id, timestamp FROM enrollments WHERE active = 1 AND account_id = ?`, accountId)
	if err != nil {
		lg.WithError(err).Info("failed to query enrollments")
		return []coreapi.Enrollment{}, err
	}
	defer rows.Close()
	enrolments := []coreapi.Enrollment{}
	for rows.Next() {
		var timestamp time.Time
		var patientId sql.NullString
		var requestId sql.NullInt64
		var enrolment coreapi.Enrollment
		err := rows.Scan(&enrolment.OrgId, &patientId, &requestId, &timestamp)
		if err != nil {
			lg.WithError(err).Info("failed to scan rows")
			continue
		}
		formattedTimestamp := timestamp.UTC().Format("2006-01-02 15:04:05")
		enrolment.Timestamp = formattedTimestamp
		if patientId.Valid {
			enrolment.PatientId = patientId.String
		}
		if requestId.Valid {
			enrolment.RequestId = requestId.Int64
		}
		enrolments = append(enrolments, enrolment)
	}
	return enrolments, nil

}
