package mysql

import (
	"context"
	"database/sql"
	"strconv"
	"time"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func RemoveExpiredTokens(db *sql.DB) error {
	_, err := db.Exec(
		"DELETE from token_blacklist where expiry < ?", strconv.Itoa(int(time.Now().Unix())),
	)
	return err
}

func LoadBlacklist(db *sql.DB) (blacklist map[string]int64, err error) {
	blacklist = make(map[string]int64)
	rows, err := db.Query("SELECT token, expiry FROM token_blacklist")
	if err != nil {
		return blacklist, err
	}
	defer rows.Close()
	for rows.Next() {
		var token string
		var expiry int64
		err = rows.Scan(&token, &expiry)
		if err != nil {
			return blacklist, err
		}
		blacklist[token] = expiry
	}
	return blacklist, nil
}

func AddToken(ctx context.Context, db *sql.DB, token string, expiry int64) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO token_blacklist SET token=?, expiry=?",
		token,
		expiry,
	)
	return err
}

func IsInBlacklist(ctx context.Context, db *sql.DB, token string) (bool, int64) {
	var expiry int64
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"select expiry from token_blacklist where token=?",
		[]interface{}{&expiry},
		token,
	)
	if err != nil {
		return false, 0
	}
	return true, expiry
}
