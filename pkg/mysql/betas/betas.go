package betas

import (
	"context"
	"database/sql"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func GetBetas(ctx context.Context, db *sql.DB, acctId string) []string {

	names := []string{}
	rows, err := mysqlWithLog.Query(ctx, db, "SELECT name FROM betas WHERE account_id=?", acctId)
	if err != nil {
		return names
	}

	defer rows.Close()
	for rows.Next() {
		var name string
		err = rows.Scan(&name)
		if err == nil {
			names = append(names, name)
		}
	}

	return names
}
