package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

type batchInsert struct {
	size   int
	sqlStr string
	vals   []interface{}
}

// creates a string of len question marks, comma separated
func CreateSqlArgString(len int) string {
	qs := make([]string, len)
	for i := range qs {
		qs[i] = "?"
	}
	return strings.Join(qs, ",")
}

// Creates an INSERT INTO tablex (col1,col2,...) VALUES (?,?,...), (?,?,...), ... statement
func CreateSqlInsertString(table string, cols []string, numInserts int) string {
	if table == "" || cols == nil || len(cols) == 0 || numInserts == 0 {
		return ""
	}
	numCols := len(cols)
	colString := strings.Join(cols, ",")
	var sqlStrList []string
	for i := 0; i < numInserts; i++ {
		sqlStrList = append(sqlStrList, "("+CreateSqlArgString(numCols)+")")
	}
	sqlArgStr := strings.Join(sqlStrList, ",")

	return fmt.Sprintf("INSERT INTO %s(%s) VALUES %s", table, colString, sqlArgStr)
}

func createBatches(
	args [][]interface{},
	tableName string,
	cols []string,
	maxBatchSize int,
) []batchInsert {

	batches := make([]batchInsert, 0)
	argLen := len(args[0])
	for j := 0; j < argLen; j += maxBatchSize {
		start := j
		var end int
		if argLen < start+maxBatchSize {
			end = argLen
		} else {
			end = start + maxBatchSize
		}
		sqlStr := CreateSqlInsertString(tableName, cols, end-start)
		vals := []interface{}{}
		for i := start; i < end; i++ {
			for _, argList := range args {
				vals = append(vals, argList[i])
			}
		}
		batches = append(batches, batchInsert{size: end - start, sqlStr: sqlStr, vals: vals})
	}
	return batches
}

// Will attempt inserts in batches of 100, with 5 retries 10 sec apart.
// Meant for large async ops where latency is unimportant.
// args should be a list of lists of values to enter (in the same order as cols)
// Ie, if I'm inserting (oId, shareID) VALUES (1,2), (3,4), (5,6) then
// args should be [[1,3,5], [2,4,6]]
func BatchInsertWithRetry(
	ctx context.Context,
	db *sql.DB,
	lg *logrus.Entry,
	cols []string,
	tableName string,
	args [][]interface{},
) error {
	//the total number of arg lists should match the total columns
	if len(args) != len(cols) {
		return fmt.Errorf("Incorrect number of args %d for %d columns", len(args), len(cols))
	}

	//each arg list should be the same length
	argLen := len(args[0])
	for i := 1; i < len(args); i++ {
		if len(args[i]) != argLen {
			return fmt.Errorf("Mismatched number of arguments for inserts")
		}
	}

	var total int
	batches := createBatches(args, tableName, cols, 100)
	for _, batch := range batches {
		retries := 5
		var err error
		for i := 0; i <= retries; i++ {
			_, err = mysqlWithLog.Exec(ctx, db, batch.sqlStr, batch.vals...)
			if err == nil {
				lg.Infof(
					"Insert %d rows into %s succeeded after %d tries",
					batch.size,
					tableName,
					i+1,
				)
				total += batch.size
				break
			} else {
				lg.WithError(err).Errorf("Insert %s failed, try #%d", tableName, i+1)
			}
			time.Sleep(time.Second * 10)
		}
		if err != nil {
			return err
		}
	}
	lg.Infof("Inserted a total of %d rows into %s", total, tableName)
	return nil
}
