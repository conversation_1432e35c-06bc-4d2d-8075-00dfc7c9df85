package mysql

import (
	"reflect"
	"testing"
)

func TestSqlArgString(t *testing.T) {
	testCases := []struct {
		name           string
		lengthArgs     int
		expectedResult string
	}{
		{
			name:           "length zero is empty string",
			lengthArgs:     0,
			expectedResult: "",
		},
		{
			name:           "length one is single question mark",
			lengthArgs:     1,
			expectedResult: "?",
		},
		{
			name:           "length many is comma delimited string of q marks",
			lengthArgs:     5,
			expectedResult: "?,?,?,?,?",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := CreateSqlArgString(tc.lengthArgs)
			if res != tc.expectedResult {
				t.Errorf(
					"CreateSqlArgString created string %v, expected string: %v",
					res,
					tc.expectedResult,
				)
			}
		})
	}

}

func TestSqlInsertString(t *testing.T) {
	testCases := []struct {
		name           string
		tableName      string
		cols           []string
		numInserts     int
		expectedResult string
	}{
		{
			name:           "empty table/cols/inserts",
			expectedResult: "",
		},
		{
			name:           "1 insert, 1 col",
			tableName:      "table1",
			cols:           []string{"col1"},
			numInserts:     1,
			expectedResult: "INSERT INTO table1(col1) VALUES (?)",
		},
		{
			name:           "1 insert, many col",
			tableName:      "table1",
			cols:           []string{"col1", "col2", "col3"},
			numInserts:     1,
			expectedResult: "INSERT INTO table1(col1,col2,col3) VALUES (?,?,?)",
		},
		{
			name:           "many insert, many col",
			tableName:      "table1",
			cols:           []string{"col1", "col2", "col3"},
			numInserts:     3,
			expectedResult: "INSERT INTO table1(col1,col2,col3) VALUES (?,?,?),(?,?,?),(?,?,?)",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := CreateSqlInsertString(tc.tableName, tc.cols, tc.numInserts)
			if res != tc.expectedResult {
				t.Errorf(
					"CreateSqlInsertString created string %v, expected string: %v",
					res,
					tc.expectedResult,
				)
			}
		})
	}

}

func TestCreateBatches(t *testing.T) {
	testCases := []struct {
		name           string
		tableName      string
		cols           []string
		maxBatchSize   int
		args           [][]interface{}
		expectedResult []batchInsert
	}{
		{
			name:         "batches of 1 for 1 column, 1 insert",
			tableName:    "table1",
			cols:         []string{"col1"},
			maxBatchSize: 1,
			args: [][]interface{}{
				[]interface{}{
					"val1",
				},
			},
			expectedResult: []batchInsert{
				batchInsert{
					size:   1,
					sqlStr: "INSERT INTO table1(col1) VALUES (?)",
					vals:   []interface{}{"val1"},
				},
			},
		},
		{
			name:         "batches of 2 for 1 column, 10 inserts total",
			tableName:    "table1",
			cols:         []string{"col1"},
			maxBatchSize: 2,
			args: [][]interface{}{
				[]interface{}{
					"val1",
					"val2",
					"val3",
					"val4",
					"val5",
					"val6",
					"val7",
					"val8",
					"val9",
					"val10",
				},
			},
			expectedResult: []batchInsert{
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val1", "val2"},
				},
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val3", "val4"},
				},
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val5", "val6"},
				},
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val7", "val8"},
				},
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val9", "val10"},
				},
			},
		}, {
			name:         "odd batch size",
			tableName:    "table1",
			cols:         []string{"col1"},
			maxBatchSize: 2,
			args: [][]interface{}{
				[]interface{}{
					"val1",
					"val2",
					"val3",
				},
			},
			expectedResult: []batchInsert{
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1) VALUES (?),(?)",
					vals:   []interface{}{"val1", "val2"},
				},
				batchInsert{
					size:   1,
					sqlStr: "INSERT INTO table1(col1) VALUES (?)",
					vals:   []interface{}{"val3"},
				},
			},
		},
		{
			name:         "odd batch size, multiple columns",
			tableName:    "table1",
			cols:         []string{"col1", "col2"},
			maxBatchSize: 2,
			args: [][]interface{}{
				[]interface{}{
					"val1",
					"val2",
					"val3",
				},
				[]interface{}{
					"b1",
					"b2",
					"b3",
				},
			},
			expectedResult: []batchInsert{
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1,col2) VALUES (?,?),(?,?)",
					vals:   []interface{}{"val1", "b1", "val2", "b2"},
				},
				batchInsert{
					size:   1,
					sqlStr: "INSERT INTO table1(col1,col2) VALUES (?,?)",
					vals:   []interface{}{"val3", "b3"},
				},
			},
		},
		{
			name:         "even batch size, multiple columns",
			tableName:    "table1",
			cols:         []string{"col1", "col2"},
			maxBatchSize: 2,
			args: [][]interface{}{
				[]interface{}{
					"val1",
					"val2",
					"val3",
					"val4",
				},
				[]interface{}{
					"b1",
					"b2",
					"b3",
					"b4",
				},
			},
			expectedResult: []batchInsert{
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1,col2) VALUES (?,?),(?,?)",
					vals:   []interface{}{"val1", "b1", "val2", "b2"},
				},
				batchInsert{
					size:   2,
					sqlStr: "INSERT INTO table1(col1,col2) VALUES (?,?),(?,?)",
					vals:   []interface{}{"val3", "b3", "val4", "b4"},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := createBatches(tc.args, tc.tableName, tc.cols, tc.maxBatchSize)
			if !reflect.DeepEqual(res, tc.expectedResult) {
				t.Errorf(
					"createBatches created string %v, expected string: %v",
					res,
					tc.expectedResult,
				)
			}
		})
	}

}
