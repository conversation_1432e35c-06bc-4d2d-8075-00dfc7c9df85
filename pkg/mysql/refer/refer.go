package mysql

import (
	"context"
	"database/sql"
	"time"

	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

// get email token count by account id
func GetEmailTokenCountByAccountId(
	ctx context.Context,
	db *sql.DB,
	accountId string,
	token string,
	timestamp time.Time,
) (count int, err error) {
	var rows *sql.Rows
	rows, err = mysqlWithLog.Query(
		ctx,
		db,
		"SELECT count(*) FROM pockethealth.email_token_log where account_id = ? and email_token = ? and timestamp > ?",
		accountId,
		token,
		timestamp,
	)
	if err != nil {
		return 0, err
	}
	defer rows.Close()
	if rows.Next() {
		err = rows.Scan(&count)
		return count, err
	}

	return 0, err

}

// insert email token log
func InsertEmailTokenLog(
	ctx context.Context,
	db *sql.DB,
	token string,
	acctId string,
	email string,
	emailType string,
	timestamp time.Time,
) (err error) {

	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT email_token_log SET email_token=?,email=?,email_type=?,timestamp=?,account_id=?",
		token,
		email,
		emailType,
		timestamp,
		acctId,
	)
	if err != nil {
		return err
	}
	return nil
}
