//go:build integration
// +build integration

package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

type consentTestRecord struct {
	id          string
	providerId  int64
	email       string
	mrn         string
	phoneNumber string
}

type enrollmentTestRecord struct {
	accountId  string
	email      string
	mrn        string
	patientId  string
	providerId int64
}

func TestGetConsentData(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("get no consent data for consent ID that does not exist", func(t *testing.T) {
		consentId := "testId"
		//get consent data
		_, err := GetConsentData(context.Background(), db, consentId)
		if err == nil {
			t.Error("expected error but got none")
		}
	})

	t.Run("get consent data for consent ID that exists", func(t *testing.T) {
		consent := consentTestRecord{
			id: "testId",
			email: fmt.Sprintf(
				"<EMAIL>",
				strconv.FormatInt(time.Now().UnixNano(), 10),
			), providerId: int64(9),
		}
		insertConsent(t, db, consent)

		//get consent data
		data, err := GetConsentData(context.Background(), db, consent.id)
		if err != nil {
			t.Errorf("expected no error but got: %s", err.Error())
		}
		if data.ProviderId != consent.providerId {
			t.Errorf("expected provider ID: %d, got %d", consent.providerId, data.ProviderId)
		}

	})
}

func TestIsEnrolled(t *testing.T) {
	db := testutils.SetupTestDB(t)

	consent := consentTestRecord{
		id: "testId",
		email: fmt.Sprintf(
			"<EMAIL>",
			strconv.FormatInt(time.Now().UnixNano(), 10),
		),
		providerId: int64(9),
		mrn:        "TEST-MRN",
	}
	insertConsent(t, db, consent)

	t.Run("returns false if enrollment for consent ID does not exist", func(t *testing.T) {
		enrolled, err := IsEnrolled(context.Background(), db, consent.id)
		assert.NoError(t, err)
		assert.False(t, enrolled)
	})

	t.Run("returns false if consent is not opted-in", func(t *testing.T) {
		enrollment := enrollmentTestRecord{
			mrn:        consent.mrn,
			email:      consent.email,
			providerId: consent.providerId,
		}
		insertEnrollment(t, db, enrollment)

		enrolled, err := IsEnrolled(context.Background(), db, consent.id)
		assert.NoError(t, err)
		assert.False(t, enrolled)
	})

	t.Run("returns false if consent is opted-out", func(t *testing.T) {
		enrollment := enrollmentTestRecord{
			mrn:        consent.mrn,
			email:      consent.email,
			providerId: consent.providerId,
		}
		optIn(t, db, consent.id)
		insertEnrollment(t, db, enrollment)
		optOut(t, db, consent.id)

		enrolled, err := IsEnrolled(context.Background(), db, consent.id)
		assert.NoError(t, err)
		assert.False(t, enrolled)
	})

	t.Run("returns false if no matching mrn in enrollments", func(t *testing.T) {
		enrollment := enrollmentTestRecord{
			mrn:        "TEST-DIFF-MRN",
			email:      consent.email,
			providerId: consent.providerId,
		}
		optIn(t, db, consent.id)
		insertEnrollment(t, db, enrollment)

		enrolled, err := IsEnrolled(context.Background(), db, consent.id)
		assert.NoError(t, err)
		assert.False(t, enrolled)
	})

	t.Run("returns false if no matching provider id in enrollments", func(t *testing.T) {
		enrollment := enrollmentTestRecord{
			mrn:        consent.mrn,
			email:      consent.email,
			providerId: int64(445),
		}
		optIn(t, db, consent.id)
		insertEnrollment(t, db, enrollment)

		enrolled, err := IsEnrolled(context.Background(), db, consent.id)
		assert.NoError(t, err)
		assert.False(t, enrolled)
	})

	t.Run(
		"returns true if there exists an opted-in enrollment for consent ID",
		func(t *testing.T) {
			enrollment := enrollmentTestRecord{
				mrn:        consent.mrn,
				email:      consent.email,
				providerId: consent.providerId,
			}
			optIn(t, db, consent.id)
			insertEnrollment(t, db, enrollment)

			enrolled, err := IsEnrolled(context.Background(), db, consent.id)
			assert.NoError(t, err)
			assert.True(t, enrolled)
		},
	)

	t.Run(
		"returns true if there exists at least 1 opted-in enrollment for consent ID",
		func(t *testing.T) {
			enrollment := enrollmentTestRecord{
				mrn:        consent.mrn,
				email:      consent.email,
				providerId: consent.providerId,
			}
			optIn(t, db, consent.id)
			insertEnrollment(t, db, enrollment)
			insertEnrollment(t, db, enrollment)
			insertEnrollment(t, db, enrollment)

			enrolled, err := IsEnrolled(context.Background(), db, consent.id)
			assert.NoError(t, err)
			assert.True(t, enrolled)
		},
	)
}

func insertEnrollment(t *testing.T, db *sql.DB, enrollment enrollmentTestRecord) {
	result, err := db.Exec(
		"INSERT INTO enrollments SET audit_user='POCKETHEALTH-CONSENT', mrn=?, email=?, org_id=?, account_id=?, patient_id=?",
		enrollment.mrn,
		enrollment.email,
		enrollment.providerId,
		enrollment.accountId,
		enrollment.patientId,
	)
	if err != nil {
		t.Errorf("unable to setup test data: %q", err.Error())
	}

	enrollmentId, _ := result.LastInsertId()
	t.Cleanup(func() {
		db.Exec("DELETE FROM enrollments WHERE id=?", enrollmentId)
	})
}

func insertConsent(t *testing.T, db *sql.DB, consent consentTestRecord) {
	// setup consent db record
	_, err := db.Exec(
		`INSERT into consents (id, email, phone_number, mrn, org_id, pre_appointment, post_appointment, is_appointment_reminder) VALUES (?,?,?,?,?,?,?,?)`,
		consent.id,
		consent.email,
		consent.phoneNumber,
		consent.mrn,
		consent.providerId,
		false,
		false,
		false,
	)
	if err != nil {
		t.Errorf("unable to setup test data: %q", err.Error())
	}

	t.Cleanup(func() {
		db.Exec("DELETE FROM consents WHERE id=?", consent.id)
	})
}

func optIn(t *testing.T, db *sql.DB, consentId string) {
	_, err := db.Exec("UPDATE consents SET opt_in=true WHERE id=?", consentId)
	if err != nil {
		t.Errorf("unable to opt-in consent: %q", err.Error())
	}

	t.Cleanup(func() {
		db.Exec("UPDATE consents SET opt_in=NULL WHERE id=?", consentId)
	})
}

func optOut(t *testing.T, db *sql.DB, consentId string) {
	_, err := db.Exec("UPDATE consents SET opt_out=true WHERE id=?", consentId)
	if err != nil {
		t.Errorf("unable to opt-out consent: %q", err.Error())
	}

	t.Cleanup(func() {
		db.Exec("UPDATE consents SET opt_out=NULL WHERE id=?", consentId)
	})
}
