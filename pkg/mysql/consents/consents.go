package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

func GetConsentData(
	ctx context.Context,
	db *sql.DB,
	consentId string,
) (coreapi.ConsentData, error) {
	var result coreapi.ConsentData
	var providerId sql.NullInt64
	var optIn sql.NullBool
	var optOut sql.NullBool
	var enrolled sql.NullBool

	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT org_id, opt_in, opt_out, enrolled, is_appointment_reminder FROM consents where id=?",
		[]interface{}{
			&providerId,
			&optIn,
			&optOut,
			&enrolled,
			&result.IsAppointmentReminder,
		},
		consentId,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return result, fmt.Errorf("consent %s", errormsgs.ERR_NOT_EXISTS)
		}
		return result, err
	}

	result.ProviderId = providerId.Int64
	result.OptIn = optIn.Bool
	result.OptOut = optOut.Bool

	return result, nil
}

func GetConsentFormTextByProvider(ctx context.Context, db *sql.DB, providerId int64) string {
	result := ""
	_ = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT consent_text from consent_forms where org_id = ?",
		[]interface{}{&result},
		providerId,
	)
	return result
}

func OptIn(
	ctx context.Context,
	db *sql.DB,
	consentId string,
	acctId string,
	ptId string,
	patientEmail string,
) (enrollmentSuccess bool, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("consent_id", consentId)

	createEnrollment := false
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE consents SET opt_in=true, account_id = ?, patient_id = ?, email = ?, response_timestamp = NOW() WHERE id=?",
		acctId,
		ptId,
		patientEmail,
		consentId,
	)
	if err != nil {
		lg.WithError(err).Error("error updating consent to opt_in=true")
		return createEnrollment, err
	}

	//TODO split enrollment record insert to its own function
	var mrn string
	var orgId int64
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT mrn, org_id FROM consents where id=?",
		[]interface{}{&mrn, &orgId},
		consentId,
	)
	if err != nil {
		lg.WithError(err).Error("consent not found")
		return createEnrollment, err
	}
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO enrollments SET audit_user='POCKETHEALTH-CONSENT', mrn=?, email=?, org_id=?, account_id=?, patient_id=?",
		mrn,
		patientEmail,
		orgId,
		acctId,
		ptId,
	)

	if err != nil {
		lg.WithError(err).Error("failed to insert enrollment")
	} else {
		createEnrollment = true
	}

	return createEnrollment, err
}

func OptOut(ctx context.Context, db *sql.DB, consent_id string) (err error) {
	_, err = mysqlWithLog.Exec(ctx, db, "UPDATE consents SET opt_out=true, response_timestamp = NOW() WHERE id=?", consent_id)
	return err
}

func IsEnrolled(ctx context.Context, db *sql.DB, consentId string) (isEnrolled bool, err error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("consent_id", consentId)

	var mrn string
	var orgId int64
	var enrolled bool
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT mrn, org_id FROM consents where id=? AND opt_in AND opt_out IS NULL",
		[]interface{}{&mrn, &orgId},
		consentId,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			lg.WithError(err).Error("consent not found")
			return false, nil
		}
		return false, err
	}

	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		`SELECT EXISTS (
			SELECT 1 FROM enrollments WHERE active AND mrn=? AND org_id=?
		) AS enrolled`,
		[]interface{}{&enrolled},
		mrn,
		orgId,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			lg.WithError(err).Error("no active enrollment found")
			return false, nil
		}
		return false, err
	}

	return enrolled, nil
}

func GetConsentUserInfo(
	ctx context.Context,
	db *sql.DB,
	consentId string,
) (userinfo coreapi.ConsentUserData, err error) {
	var phoneNumber, firstName, lastName sql.NullString
	var dob sql.NullTime
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT first_name, last_name, dob, email, phone_number FROM consents WHERE id = ?",
		[]interface{}{&firstName, &lastName, &dob, &userinfo.Email, &phoneNumber},
		consentId,
	)
	if phoneNumber.Valid {
		userinfo.Phone = phoneNumber.String
	}
	if firstName.Valid {
		userinfo.FirstName = firstName.String
	}
	if lastName.Valid {
		userinfo.LastName = lastName.String
	}
	if dob.Valid {
		userinfo.Dob = dob.Time
	}
	return userinfo, err
}

// get the dob of the first consent associated with the given email
func GetConsentDateOfBirth(ctx context.Context, db *sql.DB, accountId string) (time.Time, error) {
	var dob sql.NullTime
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT dob FROM consents WHERE account_id=?",
		[]interface{}{&dob},
		accountId,
	)
	if err == sql.ErrNoRows {
		return time.Time{}, nil
	} else if err != nil {
		return time.Time{}, err
	}
	return dob.Time, nil
}
