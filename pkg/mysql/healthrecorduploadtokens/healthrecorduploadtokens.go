package mysql

import (
	"context"
	"database/sql"
	"errors"

	"github.com/sirupsen/logrus"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type UploadSession struct {
	AccountId       string
	PatientId       string
	UploadSessionId string
	Used            bool
}

func GetV2UploadSession(ctx context.Context, db *sql.DB, patientId string) (*UploadSession, error) {
	var sessionId string
	var used bool
	var acctId string

	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT account_id, session_id, used FROM hr_v2_upload_sessions WHERE patient_id=?",
		[]interface{}{&acctId, &sessionId, &used},
		patientId,
	)
	if err != nil {
		return nil, err
	}

	upload := UploadSession{
		PatientId:       patientId,
		AccountId:       acctId,
		Used:            used,
		UploadSessionId: sessionId,
	}
	return &upload, nil
}

func IsValidV2Session(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	patientId string,
	uploadSessionId string,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"patient_id": patientId,
		"account_id": acctId,
	})

	upload, err := GetV2UploadSession(ctx, db, patientId)
	if err != nil {
		return false, err
	}

	if acctId != upload.AccountId {
		lg.Error("upload session account id does not match")
		return false, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	if uploadSessionId != upload.UploadSessionId {
		lg.Error("upload session id does not match")
		return false, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	if upload.Used {
		lg.Error("token already used")
		return false, errors.New(errmsg.ERR_NOT_AUTHORIZED)
	}

	return true, nil
}

func InsertV2UploadSession(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	patientId string,
	uploadSessionId string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"REPLACE INTO hr_v2_upload_sessions(account_id, patient_id, session_id, used) VALUES (?, ?, ?, 0)",
		acctId,
		patientId,
		uploadSessionId,
	)
	return err
}

func UseV2Session(ctx context.Context, db *sql.DB, acctId string, patientId string) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"UPDATE hr_v2_upload_sessions SET used=1 WHERE patient_id=? AND account_id=?",
		patientId,
		acctId,
	)
	return err
}
