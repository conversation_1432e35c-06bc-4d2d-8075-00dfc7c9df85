package shareanalytics

import (
	"context"
	"database/sql"
	"errors"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
	mysqlHelpers "gitlab.com/pockethealth/coreapi/pkg/mysql/helpers"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
)

func LogShare(
	ctx context.Context,
	db *sql.DB,
	method coreapi.ShareMethod,
	objectIds []string,
	hrIds []string,
	shareId string,
	acctId string,
	allRecords bool,
) error {
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
	}).Info("log share")

	switch method {
	case coreapi.ACCESS_PAGE_FAX:
		return logFaxShare(
			ctx,
			db,
			objectIds,
			hrIds,
			shareId,
			acctId,
			allRecords,
		)
	case coreapi.ACCESS_PAGE_PRINT:
		return logPDFShare(
			ctx,
			db,
			objectIds,
			hrIds,
			shareId,
			acctId,
			allRecords,
		)
	case coreapi.EMAIL:
		return logEmailShare(
			ctx,
			db,
			objectIds,
			hrIds,
			shareId,
			acctId,
			allRecords,
		)
	case coreapi.ISO, coreapi.ZIP:
		return logOfflineShare(
			ctx,
			db,
			objectIds,
			hrIds,
			shareId,
			acctId,
			allRecords,
			method,
		)
	}
	return errors.New("invalid share method")

}

func insertShareObjectsToTable(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	lg *logrus.Entry,
) error {
	if len(objectIds) > 0 {
		//remove any duplicates
		//TODO: find out why there are duplicates.
		objectIds = stringhelpers.RemoveDuplicates(objectIds)
		objIntList := make([]interface{}, len(objectIds))
		shareObjIntList := make([]interface{}, len(objectIds))
		for i := range objectIds {
			objIntList[i] = objectIds[i]
			shareObjIntList[i] = shareId
		}

		err := mysqlHelpers.BatchInsertWithRetry(
			ctx,
			db,
			lg,
			[]string{"object_id", "share_id"},
			"share_objects2",
			[][]interface{}{objIntList, shareObjIntList},
		)
		if err != nil {
			return err
		}
	}
	if len(hrIds) > 0 {
		hrIntList := make([]interface{}, len(hrIds))
		shareHRsIntList := make([]interface{}, len(hrIds))
		for i := range hrIds {
			hrIntList[i] = hrIds[i]
			shareHRsIntList[i] = shareId
		}
		err := mysqlHelpers.BatchInsertWithRetry(
			ctx,
			db,
			lg,
			[]string{"record_id", "share_id"},
			"share_healthrecords",
			[][]interface{}{hrIntList, shareHRsIntList},
		)
		if err != nil {
			return err
		}
	}
	return nil
}

func logShareObjects(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	shareMethod coreapi.ShareMethod,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"shareId":     shareId,
		"shareMethod": shareMethod,
		"numExamObjs": len(objectIds),
		"numHRRec":    len(hrIds),
	})

	if len(objectIds) == 0 && len(hrIds) == 0 {
		err := errors.New("nothing included in share")
		lg.WithError(err).Error("Insert share objects failed")
		return err
	}

	err := insertShareObjectsToTable(ctx, db, objectIds, hrIds, shareId, lg)
	if err != nil {
		return err
	}

	return nil
}

func logEmailShare(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	acctId string,
	allRecords bool,
) error {

	err := logShareObjects(ctx, db, objectIds, hrIds, shareId, coreapi.EMAIL)
	if err != nil {
		return err
	}
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT share_analytics SET account_id=?,share_id=?,share_mode=?,all_records=?",
		acctId,
		shareId,
		"EMAIL",
		allRecords,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"shareId":     shareId,
			"shareMethod": coreapi.EMAIL,
			"error_msg":   err.Error(),
		}).Error("Insert share analytics failed")
	}
	return nil

}

func logFaxShare(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	acctId string,
	allRecords bool,
) error {
	err := logShareObjects(ctx, db, objectIds, hrIds, shareId, coreapi.ACCESS_PAGE_FAX)
	if err != nil {
		return err
	}

	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT share_analytics SET account_id=?,share_id=?,share_mode=?,all_records=?",
		acctId,
		shareId,
		"FAX",
		allRecords,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"shareId":     shareId,
			"shareMethod": coreapi.ACCESS_PAGE_FAX,
			"error_msg":   err.Error(),
		}).Error("Insert share analytics failed")
	}
	return nil

}

func logPDFShare(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	acctId string,
	allRecords bool,
) error {
	err := logShareObjects(ctx, db, objectIds, hrIds, shareId, coreapi.ACCESS_PAGE_PRINT)
	if err != nil {
		return err
	}
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT share_analytics SET account_id=?,share_id=?,share_mode=?,all_records=?",
		acctId,
		shareId,
		"PDF",
		allRecords,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"shareId":     shareId,
			"shareMethod": coreapi.ACCESS_PAGE_PRINT,
			"error_msg":   err.Error(),
		}).Error("Insert share analytics failed")
	}
	return nil

}

func logOfflineShare(
	ctx context.Context,
	db *sql.DB,
	objectIds []string,
	hrIds []string,
	shareId string,
	acctId string,
	allRecords bool,
	shareMethod coreapi.ShareMethod,
) error {

	err := logShareObjects(ctx, db, objectIds, hrIds, shareId, shareMethod)
	if err != nil {
		return err
	}
	_, err = mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT share_analytics SET account_id=?,share_id=?,share_mode=?,all_records=?",
		acctId,
		shareId,
		"OFFLINE",
		allRecords,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
			"shareId":     shareId,
			"shareMethod": shareMethod,
			"error_msg":   err.Error(),
		}).Error("Insert share analytics failed")
	}
	return nil

}

func LogShareView(
	ctx context.Context,
	db *sql.DB,
	ip string,
	share_id string,
	user_agent string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO share_access_log (ip, share_id, user_agent) VALUES (?, ?, ?)",
		ip,
		share_id,
		user_agent,
	)
	if err != nil {
		return err
	}
	return nil
}
