package mysql

import (
	"context"
	"database/sql"
	"errors"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/text/language"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/caches"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/providerHelpers"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type ClinicEmail struct {
	Email     string
	IsPrimary bool
}

var origins caches.CacheInt64ToProviderDisplayInfo
var clinicOrgIds caches.CacheInt64ToInt64

func SetupCacheLengths(
	originIdNameLengthMins int,
	clinicIdOrgIdLengthMins int,
) {
	origins.SetCacheLength(time.Duration(originIdNameLengthMins) * time.Minute)
	clinicOrgIds.SetCacheLength(time.Duration(clinicIdOrgIdLengthMins) * time.Minute)
}

func GetClinicDisplayInfoFromOriginId(
	ctx context.Context,
	db *sql.DB,
	originId int64,
	orgsvcClient orgservice.OrgService,
) (coreapi.ProviderDisplayInfo, error) {
	//if no clinics have been loaded or it's time to refresh the cache
	if !origins.IsInitialized() || origins.IsExpired() {
		origins.Initialize(ctx, "origin_id_name")
	}

	if cachedProvider, found := origins.Get(ctx, originId); !found {
		var clinicId sql.NullInt64
		result := coreapi.ProviderDisplayInfo{}

		err := mysqlWithLog.QueryRowAndScan(
			ctx,
			db,
			"SELECT clinic_id FROM users WHERE user_id = ?",
			[]interface{}{&clinicId},
			originId,
		)
		if err != nil {
			return result, err
		}

		if clinicId.Valid {
			//keep this logic here to take advantage of the established caching
			clinic, err := orgsvcClient.GetClinicByLegacyId(ctx, clinicId.Int64)
			if err != nil {
				return result, err
			}

			result.Name = clinic.Name
			result.OrgName = clinic.ProviderName
			result.PtPngDownload = clinic.Provider.AllowDownload
			result.ClinicId = clinic.LegacyId
			result.OrgId = clinic.Provider.LegacyId
			result.ProviderClinicId = clinic.Id
			result.ProviderId = clinic.ProviderId
			result.ProviderTaxName = clinic.Provider.TaxName
			result.ProviderPlans = clinic.Provider.Plans
			origins.Set(ctx, originId, result)
		}
		return result, nil
	} else {
		return cachedProvider, nil
	}
}

func GetClinicIdFromOriginId(ctx context.Context, db *sql.DB, originId int64) (int64, error) {

	var id sql.NullInt64
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT clinic_id FROM users WHERE user_id = ?",
		[]interface{}{&id},
		originId,
	)
	if err != nil {
		return 0, err
	}
	if id.Valid {
		return id.Int64, nil
	}
	return 0, nil
}

// TODO Toks: deprecate after GetProvider is deprecated
func GetOrganizationId(ctx context.Context, db *sql.DB, clinicId int64) (id int64, err error) {
	//if no clinics have been loaded or it's time to refresh the cache
	if !clinicOrgIds.IsInitialized() || clinicOrgIds.IsExpired() {
		clinicOrgIds.Initialize(ctx, "clinic_id_org_id")
	}
	if cachedId, found := clinicOrgIds.Get(ctx, clinicId); !found {
		err := mysqlWithLog.QueryRowAndScan(
			ctx,
			db,
			"SELECT organization_id from clinics where id=?",
			[]interface{}{&id},
			clinicId,
		)
		if err != nil {
			return 0, err
		}
		clinicOrgIds.Set(ctx, clinicId, id)
		return id, nil
	} else {
		return cachedId, nil
	}
}

// TODO Toks: deprecate after GetFormConfig is deprecated, do not use in new code
func GetProvider(
	ctx context.Context,
	db *sql.DB,
	clinicId int64,
) (provider coreapi.Provider, err error) {
	provider = coreapi.Provider{}
	provider.ProviderId = clinicId
	provider.OrgId, err = GetOrganizationId(ctx, db, clinicId)
	if err != nil || provider.OrgId == -1 {
		return coreapi.Provider{}, errors.New("organization not found")
	}

	pullOne, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT COALESCE(c.name,''), COALESCE(o.name,''), COALESCE(c.address,''), o.patient_fee, o.tax_percentage, COALESCE(o.tax_name,''), o.region, o.url_name, o.alt_url_names FROM clinics c left join organizations o on c.organization_id = o.id WHERE c.id=?",
		clinicId,
	)

	if err != nil {
		return coreapi.Provider{}, err
	}
	defer pullOne.Close()

	var altUrlList sql.NullString

	for pullOne.Next() {
		err = pullOne.Scan(
			&provider.Name,
			&provider.OrgName,
			&provider.Address,
			&provider.FeeAmount,
			&provider.TaxPercent,
			&provider.TaxName,
			&provider.Region,
			&provider.Url,
			&altUrlList,
		)
		provider.ClinicName = provider.Name

		if altUrlList.Valid {
			var trimmedString = strings.ReplaceAll(altUrlList.String, " ", "")
			provider.AltUrls = strings.Split(trimmedString, ",")
		} else {
			provider.AltUrls = []string{}
		}
		if err != nil {
			return provider, err
		}
	}

	return provider, nil
}

// TODO James: deprecate
func GetFormConfig(ctx context.Context, db *sql.DB, clinicId int64) (interface{}, error) {
	var err error
	reqForm := coreapi.RequestFormConfig{}
	var modalTemp sql.NullString
	var mrnTemp sql.NullString

	reqForm.Provider, err = GetProvider(ctx, db, clinicId)
	if err != nil {
		return coreapi.RequestFormConfig{}, err
	}

	if reqForm.Provider.Url != "" {
		var delegConsentLang sql.NullString
		var patientConsentLang sql.NullString
		var affiliatedText sql.NullString

		pullTwo, err := mysqlWithLog.Query(
			ctx,
			db,
			"SELECT base64, alt_h_id, mrn, single_date, multi_locations, delegate, payment, reports, modalities, record_txt, pay_txt, enroll_consent, legal,legal_surcharge, detail_request, ohip, ssn, ipn, bc_phn, delegate_consent_lang, patient_consent_lang, affiliated_provider, min_consent_age, exam_type, exam_site, exam_site_mrn_prefix, exam_date, patient_address, enrollment_optin, delegate_auth_instructions, survey_id, show_login_link, show_zoho_live_chat, require_delegate_review, is_uph FROM forms WHERE organization_id=?",
			reqForm.Provider.OrgId,
		)

		if err != nil {
			return coreapi.RequestFormConfig{}, err
		}
		defer pullTwo.Close()

		var examType sql.NullString
		var examSite sql.NullString
		var examSiteMrnPrefix sql.NullString
		var surveyId sql.NullInt32
		var requireDelegateReview sql.NullBool

		for pullTwo.Next() {
			err = pullTwo.Scan(
				&reqForm.Base64Logo,
				&reqForm.AltID,
				&mrnTemp,
				&reqForm.SingleDate,
				&reqForm.Multilocations,
				&reqForm.Delegate,
				&reqForm.Payment,
				&reqForm.Reports,
				&modalTemp,
				&reqForm.RecordText,
				&reqForm.PaymentText,
				&reqForm.EnrollConsent,
				&reqForm.Legal,
				&reqForm.LegalSurcharge,
				&reqForm.RecentAndComment,
				&reqForm.Ohip,
				&reqForm.Ssn,
				&reqForm.Ipn,
				&reqForm.Bcphn,
				&delegConsentLang,
				&patientConsentLang,
				&affiliatedText,
				&reqForm.MinConsentAge,
				&examType,
				&examSite,
				&examSiteMrnPrefix,
				&reqForm.ExamDate,
				&reqForm.PatientAddress,
				&reqForm.EnrollOpt,
				&reqForm.DelegateAuthInstruction,
				&surveyId,
				&reqForm.ShowLoginLink,
				&reqForm.ShowZohoLiveChat,
				&requireDelegateReview,
				&reqForm.IsUPH,
			)
			if err != nil {
				return "", err
			}
		}

		if reqForm.SingleDate && reqForm.RecentAndComment {
			reqForm.RecentAndComment = false
		}

		if mrnTemp.Valid {
			reqForm.Mrn = mrnTemp.String
		}

		if delegConsentLang.Valid {
			reqForm.DelegateConsent = delegConsentLang.String
		} else {
			reqForm.DelegateConsent = ""
		}

		if patientConsentLang.Valid {
			reqForm.PatientConsent = patientConsentLang.String
		} else {
			reqForm.PatientConsent = ""
		}

		if examType.Valid {
			reqForm.ExamType = examType.String
		} else {
			reqForm.ExamType = ""
		}

		if requireDelegateReview.Valid {
			reqForm.RequireDelegateReview = requireDelegateReview.Bool
		} else {
			// not a custom delegate flow by default
			reqForm.RequireDelegateReview = false
		}

		if examSite.Valid {
			reqForm.ExamSite = examSite.String
		} else {
			reqForm.ExamSite = ""
		}

		if examSiteMrnPrefix.Valid {
			reqForm.ExamSiteMrnPrefix = examSiteMrnPrefix.String
		} else {
			reqForm.ExamSiteMrnPrefix = ""
		}

		if modalTemp.Valid {
			reqForm.ModalTypes = strings.Split(modalTemp.String, ",")
		}

		if affiliatedText.Valid {
			reqForm.AffiliatedText = affiliatedText.String
		} else {
			reqForm.AffiliatedText = ""
		}

		if reqForm.DelegateAuthInstruction == "" {
			reqForm.DelegateAuthInstruction = "Please provide supporting documentation affirming your status as an authorized representative of the patient. If you are a parent/guardian, you may submit government-issued identification for yourself and your child. You may attach any image file or a PDF document."
		}

		if surveyId.Valid {
			reqForm.SurveyId = surveyId.Int32
		}

		return reqForm, err
	} else {
		var offlineModalTitle sql.NullString
		var offlineModalHtml sql.NullString
		rows, err := mysqlWithLog.Query(ctx, db, "SELECT offline_modal_title, offline_modal_html FROM organizations WHERE id=?", reqForm.Provider.OrgId)
		if err != nil {
			return "", err
		}
		defer rows.Close()
		var offlineRequest coreapi.OfflineRequestConfig
		if rows.Next() {
			err = rows.Scan(&offlineModalTitle, &offlineModalHtml)
		}

		if offlineModalTitle.Valid {
			offlineRequest.NoFormTitle = offlineModalTitle.String
		}

		if offlineModalHtml.Valid {
			offlineRequest.NoFormHtml = offlineModalHtml.String
		}

		return offlineRequest, err
	}
}

// TODO Toks deprecate
func FuzzySearch(
	ctx context.Context,
	db *sql.DB,
	searchTerms []string,
	searchTerm string,
) (results []coreapi.Provider, err error) {
	baseQuery := "SELECT COALESCE(c.name,''), COALESCE(o.name,''), COALESCE(c.address,''), COALESCE(c.id,''), COALESCE(o.id,''), COALESCE(o.url_name,''), o.alt_url_names FROM clinics c JOIN organizations o ON c.organization_id = o.id LEFT JOIN forms f ON o.id = f.organization_id "

	err = nil

	whereClause := ""
	queryClauseList := make([]string, 0)
	var queryInputTerms []interface{}
	baseWhere := "(c.name LIKE ? OR o.name LIKE ? OR c.nickname LIKE ? OR c.address LIKE ? OR o.synonyms LIKE ? OR o.url_name LIKE ? OR o.alt_url_names LIKE ?) "
	//generate query where Clause and query parameters
	for index, term := range searchTerms {
		if index == 0 {
			//valid (ie, active) clinics will either
			//a) have a entry in forms (f.organization_id not null) and valid o.url_name
			//b) have valid offline modal info
			//c) not the UPH general org (in QA or prod)
			whereClause = whereClause + "WHERE o.id != 43583 AND o.id != 203 AND ((o.url_name != '' AND o.url_name IS NOT NULL AND f.organization_id IS NOT NULL) OR  (o.offline_modal_title IS NOT NULL AND o.offline_modal_html IS NOT NULL)) AND " + baseWhere
			queryClauseList = append(queryClauseList, whereClause)
		} else {
			whereClause = whereClause + "AND " + baseWhere
			queryClauseList = append(queryClauseList, whereClause)
		}
		termString := "%" + term + "%"
		queryInputTerms = append(
			queryInputTerms,
			termString,
			termString,
			termString,
			termString,
			termString,
			termString,
			termString,
		)
	}

	//send and process query
	for i := len(queryClauseList) - 1; i >= 0; i-- {
		rows, err_query := mysqlWithLog.Query(
			ctx,
			db,
			baseQuery+queryClauseList[i],
			queryInputTerms[:(i+1)*7]...)
		if err_query != nil {
			err = err_query
			continue
		}
		defer rows.Close()
		results = make([]coreapi.Provider, 0)
		var altUrlList sql.NullString
		for rows.Next() {
			var clinic coreapi.Provider
			err = rows.Scan(
				&clinic.Name,
				&clinic.OrgName,
				&clinic.Address,
				&clinic.ProviderId,
				&clinic.OrgId,
				&clinic.Url,
				&altUrlList,
			)
			clinic.ClinicName = clinic.Name
			if altUrlList.Valid {
				var trimmedString = strings.ReplaceAll(altUrlList.String, " ", "")
				clinic.AltUrls = strings.Split(trimmedString, ",")
			} else {
				clinic.AltUrls = []string{}
			}
			results = append(results, clinic)
		}
		if len(results) > 0 {
			err = rows.Close()
			if err != nil {
				logutils.DebugCtxLogger(ctx).WithError(err).Error("issue closing db rows")
			}
			break
		}
		err = rows.Close()
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("issue closing db rows")
		}
	}

	sort.Slice(results, func(i, j int) bool {
		return results[i].Name < results[j].Name
	})

	sortedResults := providerHelpers.SortProvidersByBestMatch(searchTerm, results)

	return sortedResults, err
}

func GetProviderConfig(
	ctx context.Context,
	db *sql.DB,
) (providerConfig coreapi.ProviderConfig, err error) {
	providerConfig = coreapi.ProviderConfig{}
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"select org_id, show_upsell, disabled_features, record_streaming, show_core_upsell from provider_config",
	)
	if err != nil {
		return providerConfig, err
	}
	defer rows.Close()

	configItems := make(map[int]coreapi.ProviderConfigItem)

	for rows.Next() {
		var orgId int
		var showUpsell bool
		var disabledFeature sql.NullInt32 // Using sql.NullInt32 to handle NULL values
		var recordStreaming bool
		var showCoreUpsell bool
		err = rows.Scan(&orgId, &showUpsell, &disabledFeature, &recordStreaming, &showCoreUpsell)
		if err != nil {
			return providerConfig, err
		}

		// Check if orgId already exists in the map
		if setting, exists := configItems[orgId]; exists {
			// Append the disabled feature if it's not NULL
			if disabledFeature.Valid {
				setting.DisabledFeatures = append(
					setting.DisabledFeatures,
					int(disabledFeature.Int32),
				)
			}
			configItems[orgId] = setting
		} else {
			// Initialize the ProviderConfigs and DisabledFeatures array
			providerConfigItem := coreapi.ProviderConfigItem{
				ShowUpsell:      showUpsell,
				ShowCoreUpsell:  showCoreUpsell,
				RecordStreaming: recordStreaming,
			}

			// Initialize the array with disabled feature if it's not NULL
			if disabledFeature.Valid {
				providerConfigItem.DisabledFeatures = []int{int(disabledFeature.Int32)}
			} else {
				providerConfigItem.DisabledFeatures = []int{} // Initialize to an empty slice
			}

			configItems[orgId] = providerConfigItem
		}
	}

	providerConfig.Settings = configItems

	return providerConfig, nil
}

func GetClinicLanguageTagProvider(orgsvc orgservice.OrgService) phlanguage.LanguageTagProvider {
	p := func(ctx context.Context, clinicId interface{}) language.Tag {
		intClinicId := reflect.ValueOf(clinicId).Int()
		clinic, err := orgsvc.GetClinicByLegacyId(ctx, intClinicId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"clinic_id": intClinicId,
			}).WithError(err).Error("could not get provider id for clinic")
			return language.Tag{}
		}

		langString := clinic.Provider.Language
		if langString == "" {
			langString = "en"
		}

		langTag, err := language.Parse(langString)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
				"language": langString,
			}).WithError(err).Error("error parsing language from string")
		}
		return langTag
	}
	return phlanguage.LanguageTagProvider{
		GetLanguageTag: p,
	}
}
