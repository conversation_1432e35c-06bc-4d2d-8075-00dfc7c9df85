//go:build integration
// +build integration

package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

// Sets up shares in the shares and view_shares table, with incrementing id's (0 - n-1).
// Each subsq share has a share date that is 1 hr newer than the previous, so id 0 is the oldest share.
func setupTestShares(db *sql.DB, n int, acctId string, baseShareId string) (err error) {
	// insert shares (1 hr apart, oldest to newest)
	sqlValsFmt := ""
	vals := []interface{}{}
	for i := 0; i < n; i++ {
		sqlValsFmt += "(?, ?, ?, ?, ?),"
		shareDate := time.Now().Add(time.Hour * -1 * time.Duration(n-i))
		vals = append(
			vals,
			fmt.Sprintf(baseShareId+"_%d", i),
			acctId,
			fmt.Sprintf("test_scan_%d", i),
			"pdf",
			shareDate,
		)
	}
	// trim the last ,
	sqlValsFmt = strings.TrimSuffix(sqlValsFmt, ",")
	res, err := db.Exec(
		"INSERT INTO shares (share_id, account_id, scan_id, recipient, date) VALUES "+sqlValsFmt,
		vals...)
	if err != nil {
		return err
	}
	num, _ := res.RowsAffected()
	fmt.Println("inserted: ", num)

	// insert view_shares
	sqlValsFmt = ""
	vals = []interface{}{}
	for i := 0; i < n; i++ {
		sqlValsFmt += "(?),"
		vals = append(vals, fmt.Sprintf(baseShareId+"_%d", i))
	}
	// trim the last ,
	sqlValsFmt = strings.TrimSuffix(sqlValsFmt, ",")
	_, err = db.Exec("INSERT INTO view_shares (share_id) VALUES "+sqlValsFmt, vals...)
	return err
}

func TestGetSharesBaseDataByAccountPaginated(t *testing.T) {
	db := testutils.SetupTestDB(t)

	t.Run("when limit 20 and offset 0, should return newest 20 shares", func(t *testing.T) {
		total := 36
		limit := 20
		offset := 0
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		baseShareId := "test_share_" + rand
		acctId := ksuid.New().String()

		// setup test data
		err := setupTestShares(db, total, acctId, baseShareId)
		if err != nil {
			t.Fatalf("unable to setup test shares: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM shares WHERE share_id LIKE ?", baseShareId+"%")
			db.Exec("DELETE FROM view_shares WHERE share_id LIKE ?", baseShareId+"%")
		})

		shares, err := GetSharesBaseDataByAccountPaginated(
			context.Background(),
			db,
			acctId,
			limit,
			offset,
			false,
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		if len(shares) != 20 {
			t.Fatalf("expected %d shares, got %d", 20, len(shares))
		}
		// expect the most recent shares to be returned (id's 16 - 35)
		wantShareId := fmt.Sprintf(baseShareId+"_%d", 35)
		if shares[0].ShareId != wantShareId {
			t.Errorf("expected %s, got %s", wantShareId, shares[0].ShareId)
		}
		wantShareId = fmt.Sprintf(baseShareId+"_%d", 16)
		if shares[len(shares)-1].ShareId != wantShareId {
			t.Errorf("expected %s, got %s", wantShareId, shares[len(shares)-1].ShareId)
		}
	})

	t.Run(
		"when limit 20, offset 20 and total 36 shares, should return oldest 16 shares",
		func(t *testing.T) {
			total := 36
			limit := 20
			offset := 20
			rand := strconv.FormatInt(time.Now().UnixNano(), 10)
			baseShareId := "test_share_" + rand
			acctId := "test_" + rand

			// setup test data
			err := setupTestShares(db, total, acctId, baseShareId)
			if err != nil {
				t.Fatalf("unable to setup test shares: %q", err.Error())
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM shares WHERE share_id LIKE ?", baseShareId+"%")
				db.Exec("DELETE FROM view_shares WHERE share_id LIKE ?", baseShareId+"%")
			})

			shares, err := GetSharesBaseDataByAccountPaginated(
				context.Background(),
				db,
				acctId,
				limit,
				offset,
				false,
			)
			if err != nil {
				t.Fatalf("got error when expected none: %q", err.Error())
			}

			if len(shares) != 16 {
				t.Errorf("expected %d shares, got %d", 16, len(shares))
			}
			// expect older shares to be returned (id's 0 - 15)
			wantShareId := fmt.Sprintf(baseShareId+"_%d", 15)
			if shares[0].ShareId != wantShareId {
				t.Errorf("expected %s, got %s", wantShareId, shares[0].ShareId)
			}
			wantShareId = fmt.Sprintf(baseShareId+"_%d", 0)
			if shares[len(shares)-1].ShareId != wantShareId {
				t.Errorf("expected %s, got %s", wantShareId, shares[len(shares)-1].ShareId)
			}
		},
	)

	t.Run("share list should be ordered newest first", func(t *testing.T) {
		total := 36
		limit := -1
		offset := 0
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		baseShareId := "test_share_" + rand
		acctId := "test_" + rand

		// setup test data
		err := setupTestShares(db, total, acctId, baseShareId)
		if err != nil {
			t.Fatalf("unable to setup test shares: %q", err.Error())
		}
		t.Cleanup(func() {
			db.Exec("DELETE FROM shares WHERE share_id LIKE ?", baseShareId+"%")
			db.Exec("DELETE FROM view_shares WHERE share_id LIKE ?", baseShareId+"%")
		})

		shares, err := GetSharesBaseDataByAccountPaginated(
			context.Background(),
			db,
			acctId,
			limit,
			offset,
			false,
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}

		firstDate, _ := time.Parse("2006-01-02T15:04:05Z0700", shares[0].Date)
		lastDate, _ := time.Parse("2006-01-02T15:04:05Z0700", shares[len(shares)-1].Date)
		if lastDate.After(firstDate) {
			t.Errorf("expected %s to be before %s", shares[0].Date, shares[len(shares)-1].Date)
		}
	})
}

func TestContainsDeletedExams(t *testing.T) {
	db := testutils.SetupTestDB(t)

	cases := []struct {
		name        string
		setupFunc   func()
		cleanupFunc func()
		expectedRes bool
		wantErr     bool
	}{
		{
			name:        "successfully returns false if there's no share at all",
			expectedRes: false,
			wantErr:     false,
		},
		{
			name: "successfully returns false if the object isn't deleted",
			setupFunc: func() {
				db.Exec(
					"INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES ('TestContainsDeletedExams(): some-share-id', 'some-object-id', 0)",
				)
			},
			cleanupFunc: func() {
				db.Exec(
					"DELETE FROM share_objects2 where share_id='TestContainsDeletedExams(): some-share-id'",
				)
			},
			expectedRes: false,
			wantErr:     false,
		},
		{
			name: "successfully returns false if the objects is_deleted flag is null",
			setupFunc: func() {
				db.Exec(
					"INSERT INTO share_objects2 (share_id, object_id) VALUES ('TestContainsDeletedExams(): some-share-id', 'some-object-id')",
				)
			},
			cleanupFunc: func() {
				db.Exec(
					"DELETE FROM share_objects2 where share_id='TestContainsDeletedExams(): some-share-id'",
				)
			},
			expectedRes: false,
			wantErr:     false,
		},
		{
			name: "successfully returns true if the object is deleted",
			setupFunc: func() {
				db.Exec(
					"INSERT INTO share_objects2 (share_id, object_id, is_deleted) VALUES ('TestContainsDeletedExams(): some-share-id', 'some-object-id', 1)",
				)
			},
			cleanupFunc: func() {
				db.Exec(
					"DELETE FROM share_objects2 where share_id='TestContainsDeletedExams(): some-share-id'",
				)
			},
			expectedRes: true,
			wantErr:     false,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			if c.setupFunc != nil {
				c.setupFunc()
			}

			cde, err := ContainsDeletedExams(
				context.TODO(),
				db,
				"TestContainsDeletedExams(): some-share-id",
			)
			if cde != c.expectedRes {
				t.Fatalf(
					"error getting ContainsDeletedExams(): got %v but wanted %v",
					cde,
					c.expectedRes,
				)
			}
			if (err != nil) != c.wantErr {
				t.Fatalf(
					"ContainsDeletedExams() error mismatch: %s, expected error: %v",
					err,
					c.wantErr,
				)
				return
			}

			if c.cleanupFunc != nil {
				c.cleanupFunc()
			}
		})
	}
}
