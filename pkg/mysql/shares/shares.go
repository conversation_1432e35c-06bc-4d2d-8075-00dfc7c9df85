package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	mysqlWithLog "gitlab.com/pockethealth/coreapi/pkg/mysql/logger"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type ShareBase struct {
	ShareId        string
	ScanId         string
	Recipient      string
	Date           string
	Active         bool
	Expiry         string
	FaxNumber      string
	FaxStatus      coreapi.FaxStatus
	ExtendedExpiry string
	HRIdList       []string
}

type resendPDFData struct {
	Code          string
	Expiry        string
	PdfJsonString string
}

func InsertShare(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	shareId string,
	scanId string,
	recipient string,
	pin string,
	patientId string,
) error {
	logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
	}).Info("insert share")

	var ptId sql.NullString
	if patientId != "" {
		ptId.Valid = true
		ptId.String = patientId
	}

	var err error
	if pin == "" {
		_, err = mysqlWithLog.Exec(
			ctx,
			db,
			"INSERT shares SET account_id=?,share_id=?,scan_id=?,recipient=?,pin=NULL,patient_id=?",
			acctId,
			shareId,
			scanId,
			recipient,
			ptId,
		)
	} else {
		_, err = mysqlWithLog.Exec(ctx, db, "INSERT shares SET account_id=?,share_id=?,scan_id=?,recipient=?,pin=?,patient_id=?", acctId, shareId, scanId, recipient, pin, ptId)
	}
	return err
}

func InsertViewShare(
	ctx context.Context,
	db *sql.DB,
	viewCode string,
	shareId string,
	dob string,
	expiry string,
	pdfJson string,
) error {
	_, err := mysqlWithLog.Exec(
		ctx,
		db,
		"INSERT INTO view_shares SET view_code=?, share_id=?, dob=?, expiry_timestamp=?, pdf_json_string=?",
		viewCode,
		shareId,
		dob,
		expiry,
		pdfJson,
	)
	return err
}

func IsValidPin(ctx context.Context, db *sql.DB, pin string, shareId string) bool {
	rows, err := mysqlWithLog.Query(ctx, db, "SELECT pin FROM shares WHERE share_id=?", shareId)
	if err != nil {
		return false
	}
	defer rows.Close()
	rows.Next()
	var sharePin string
	err = rows.Scan(&sharePin)
	if err != nil {
		return false
	}
	return pin == sharePin
}

// Several cases:
// viewcode does not exist (return bad credentials)
// viewcode and dob match, but share ID is null (pending transfer)
// viewcode and dob match, share ID is not null (valid, complete transfer)
// viewcode and dob do not match (bad credentials)
func IsValidViewCodeDOB(
	ctx context.Context,
	db *sql.DB,
	securityCode string,
	DOB string,
) (shareId string, expiryTime string, err error) {
	var shareNull sql.NullString
	var dbDOB sql.NullString
	var extendedExpiryDate sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT share_id, dob, expiry_timestamp, extended_expiry FROM view_shares WHERE view_code = ?",
		[]interface{}{&shareNull, &dbDOB, &expiryTime, &extendedExpiryDate},
		securityCode,
	)

	//viewcode does not exist
	//bad creds
	if !dbDOB.Valid || dbDOB.String != DOB {
		return "", "", errors.New(errormsgs.ERR_BAD_CREDENTIALS)
	}

	//complete, shareId valid, share extended need more validation:
	if shareNull.Valid && extendedExpiryDate.Valid {
		return shareNull.String, extendedExpiryDate.String, errors.New("Extended")
	}

	//complate, shareId valid, Not extended share
	if shareNull.Valid && !extendedExpiryDate.Valid {
		return shareNull.String, expiryTime, nil
	}

	//pending
	return "", "", nil
}

func IsRevokedProviderShare(
	ctx context.Context,
	db *sql.DB,
	viewCode string,
) bool {
	var status sql.NullString
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT status FROM provider_transfers WHERE view_code = ?",
		[]interface{}{&status},
		viewCode,
	)
	if err != nil || !status.Valid {
		return false
	}
	return status.String == "revoked"
}

func getSharesBaseDataFromRows(rows *sql.Rows) (string, []ShareBase, error) {
	shares := []ShareBase{}
	var acctId string
	for rows.Next() {
		var share ShareBase

		//we expect expiry to be null for email shares, and fax details to be null for email and print shares
		//acct id will be null for p2ps
		var nullExpiry, nullFaxNum, nullFaxStatus, nullExtendedExpiry, nullAcctId sql.NullString
		if err := rows.Scan(&share.ShareId, &nullAcctId, &share.ScanId, &share.Recipient, &share.Date, &share.Active, &nullExpiry, &nullFaxNum, &nullFaxStatus, &nullExtendedExpiry); err != nil {
			return "", nil, err
		}
		if nullExtendedExpiry.Valid {
			share.ExtendedExpiry = nullExtendedExpiry.String
		}
		if nullExpiry.Valid {
			share.Expiry = nullExpiry.String
		}
		if nullFaxNum.Valid {
			share.FaxNumber = nullFaxNum.String
		}
		if nullFaxStatus.Valid {
			share.FaxStatus = coreapi.FaxStatus(nullFaxStatus.String)
		}
		if nullAcctId.Valid {
			acctId = nullAcctId.String
		}
		shares = append(shares, share)
	}
	return acctId, shares, nil
}

func getSharesQueryTablesAndCols() (tables string, cols string) {
	tables = "pockethealth.shares s " +
		"LEFT JOIN pockethealth.view_shares vs on s.share_id = vs.share_id " +
		"LEFT JOIN pockethealth.fax_requests fr on s.share_id = fr.share_id " +
		"LEFT JOIN pockethealth.faxes f on fr.fax_id = f.fax_id"

	cols = "s.share_id, s.account_id, s.scan_id, s.recipient, s.date, s.Active, vs.expiry_timestamp, fr.faxnumber, f.status, vs.extended_expiry"

	return tables, cols
}

func GetSharesBaseDataByAccountPaginated(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	limit int,
	offset int,
	excludeDeleted bool,
) ([]ShareBase, error) {
	lg := logutils.CtxLogger(ctx)
	tables, cols := getSharesQueryTablesAndCols()
	// prevent integer out of range errors
	if limit < 0 {
		limit = 100
	}

	baseQ := "SELECT " + cols + " FROM " + tables + " WHERE s.account_id=? %s ORDER BY s.date DESC LIMIT ? OFFSET ?;"
	isDeletedQuery := ""
	if excludeDeleted {
		isDeletedQuery = "AND s.is_deleted != true"
	}
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		fmt.Sprintf(baseQ, isDeletedQuery),
		acctId,
		fmt.Sprint(limit),
		fmt.Sprint(offset),
	)
	if err != nil {
		lg.WithError(err).
			Errorf("failed to get shares; account_id %s, limit %d, offset %d", acctId, limit, offset)
		return nil, err
	}
	defer rows.Close()

	_, shares, err := getSharesBaseDataFromRows(rows)
	if err != nil {
		return []ShareBase{}, err
	}

	for _, s := range shares {
		idList, err := GetHRIDList(ctx, db, s.ShareId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithField("share_id", s.ShareId).
				WithError(err).
				Error("could not retrieve hr ids for share")
		}
		s.HRIdList = idList
	}
	return shares, nil
}

func GetSharesBaseDataByShareIds(
	ctx context.Context,
	db *sql.DB,
	shareIds []interface{},
) (string, []ShareBase, error) {
	tables, cols := getSharesQueryTablesAndCols()

	questionArr := make([]string, len(shareIds))
	for i := range questionArr {
		questionArr[i] = "?"
	}
	rows, err := mysqlWithLog.Query(
		ctx,
		db,
		"SELECT "+cols+" FROM "+tables+" WHERE s.share_id IN ("+strings.Join(
			questionArr,
			",",
		)+") ORDER BY s.date ASC;",
		shareIds...)
	if err != nil {
		return "", nil, err
	}
	defer rows.Close()

	acctId, shares, err := getSharesBaseDataFromRows(rows)
	if err != nil {
		return "", nil, err
	}
	for i := range shares {
		idList, err := GetHRIDList(ctx, db, shares[i].ShareId)
		if err != nil {
			logutils.DebugCtxLogger(ctx).
				WithField("share_id", shares[i].ShareId).
				WithError(err).
				Error("could not retrieve hr ids for share")
		}
		shares[i].HRIdList = idList
	}
	return acctId, shares, nil
}

func GetP2PTransferId(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (transferId string, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT transfer_id FROM provider_transfers WHERE share_id=?",
		[]interface{}{&transferId},
		shareId,
	)
	if err != nil {
		return "", err
	}
	return transferId, nil
}

func DeactivateShare(ctx context.Context, db *sql.DB, shareId string) (err error) {
	_, err = mysqlWithLog.Exec(ctx, db, "UPDATE shares set active=false where share_id=?", shareId)
	if err != nil {
		return err
	}
	return nil
}

func RetrieveShareData(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (pin string, recipient string, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT recipient, pin FROM shares WHERE share_id=?",
		[]interface{}{&recipient, &pin},
		shareId,
	)
	if err != nil {
		return
	}
	return
}

func RetrievePDFData(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (data resendPDFData, err error) {
	var expiry string
	var code string
	var jsonString sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT view_code, expiry_timestamp, pdf_json_string FROM view_shares WHERE share_id=?",
		[]interface{}{&code, &expiry, &jsonString},
		shareId,
	)
	if err != nil {
		return
	}
	returnJsonString := ""
	if jsonString.Valid {
		returnJsonString = jsonString.String
	}
	data = resendPDFData{code, expiry, returnJsonString}
	return
}

func RetrieveFaxPDFData(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) (faxNum string, faxJson string, err error) {
	var jsonString sql.NullString
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT faxnumber, fax_json_string FROM fax_requests WHERE share_id=?",
		[]interface{}{&faxNum, &jsonString},
		shareId,
	)
	if err != nil {
		return
	}
	returnJsonString := ""
	if jsonString.Valid {
		returnJsonString = jsonString.String
	}
	return faxNum, returnJsonString, nil
}

func ExtendViewShare(ctx context.Context, db *sql.DB, shareId string) (valid bool, err error) {
	var extendedExpiry sql.NullString
	var expiryTime string
	valid = true
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT expiry_timestamp, extended_expiry FROM view_shares WHERE share_id = ?",
		[]interface{}{&expiryTime, &extendedExpiry},
		shareId,
	)
	if err != nil {
		return false, err
	}
	t, _ := time.Parse("2006-01-02T15:04:05Z", expiryTime)
	if extendedExpiry.Valid {
		/* has been updated*/
		return false, nil
	} else {
		extendedExpiry := t.AddDate(0, 9, 0).Format("2006-01-02 15:04:05")
		_, err = mysqlWithLog.Exec(ctx, db, "UPDATE view_shares SET extended_expiry=?, expiry_timestamp=expiry_timestamp WHERE share_id=?", extendedExpiry, shareId)
		if err != nil {
			return false, err
		}
		return valid, err
	}
}

func IsShareActive(ctx context.Context, db *sql.DB, shareId string) (active bool, err error) {
	err = mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT active FROM shares WHERE share_id = ?",
		[]interface{}{&active},
		shareId,
	)
	if err != nil {
		return false, err
	}
	return active, nil
}

func GetAcctId(ctx context.Context, db *sql.DB, shareId string) string {
	var id sql.NullString //acct will  be null for p2p shares
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT account_id FROM shares WHERE share_id=?",
		[]interface{}{&id},
		shareId,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithError(err).
			Debug("couldn't get account id for share")
	}
	return id.String
}

// ContainsDeletedExams checks if the given share has any objects that have been deleted
func ContainsDeletedExams(ctx context.Context, db *sql.DB, shareID string) (bool, error) {
	var objID string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT object_id FROM share_objects2 WHERE is_deleted=1 AND share_id=? LIMIT 1",
		[]interface{}{&objID},
		shareID,
	)
	if err == sql.ErrNoRows {
		err = nil
	}

	return objID != "", err
}

func GetHRIDList(ctx context.Context, db *sql.DB, shareId string) ([]string, error) {
	rows, err := mysqlWithLog.Query(ctx, db, `SELECT record_id
	FROM share_healthrecords
	WHERE share_id = ?`, shareId)

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var hrIdList []string
	for rows.Next() {
		var id string
		err := rows.Scan(&id)
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("error scanning hr id")
			return nil, err
		}
		hrIdList = append(hrIdList, id)
	}
	return hrIdList, nil
}

// GetPatientHealthRecordProfileIdBySRecordId gets the profile ID for a record ID.
// this function assumes that all rows that have the given record ID will have the
// same profile ID so limit the result to the first row.
func GetPatientHealthRecordPatientIdByShareId(
	ctx context.Context,
	db *sql.DB,
	shareID string,
) (string, string, error) {
	var ptid sql.NullString
	var acctId string
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		"SELECT account_id, patient_id FROM shares WHERE share_id=? LIMIT 1",
		[]interface{}{&acctId, &ptid},
		shareID,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("share_id", shareID).
			WithError(err).
			Error("unable to get patient id for share")
		return "", "", err
	}

	if !ptid.Valid {
		return "", "", errors.New("null patient id for share")
	}

	return acctId, ptid.String, nil
}

func GetPatientLastNameByShareId(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) ([]string, error) {
	// Shares may have more than one last name (soemtimes different institutions have different name formats)
	var lastnames []string
	rows, err := mysqlWithLog.Query(ctx, db, `SELECT DISTINCT e.patient_name FROM share_objects2 so
	JOIN object_mappings om ON so.object_id = om.object_id
	JOIN exams e ON e.uuid = om.exam_uuid
	WHERE so.share_id = ?`, shareId)
	if err != nil {
		return lastnames, err
	}
	defer rows.Close()

	for rows.Next() {
		var name string
		err := rows.Scan(&name)
		if err != nil {
			return lastnames, err
		}
		_, lastname := dcmtools.ParseFirstLastName(ctx, name)
		lastnames = append(lastnames, lastname)
	}

	return lastnames, nil
}

func BelongsToAcct(
	ctx context.Context,
	db *sql.DB,
	acctId string,
	shareId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
		"shareId": shareId,
	})

	shareAcct := GetAcctId(ctx, db, shareId)

	if shareAcct != "" {
		return shareAcct == acctId
	}

	lg.Info("share acct not present in SQL") // expected for p2p shares
	return false
}

// Store this share so that provider service can pick it up and complete it after the transfer is complete
func StoreDelayedShare(
	ctx context.Context,
	db *sql.DB,
	share coreapi.Share,
	shareId string,
	acctId string,
	securityCode string,
	dob string,
	pin string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id":  acctId,
		"share_id": shareId,
	})

	// store share as a JSON in db
	b, err := json.Marshal(share)
	if err != nil {
		lg.WithError(err).Error("failed to marshal share into json for delayed share store")
		return err
	}
	shareJson := string(b)

	// store hr IDs as a JSON in db
	b2, err := json.Marshal(share.GetHRIds())
	if err != nil {
		lg.WithError(err).Error("failed to marshal hrids into json for delayed share store")
		return err
	}
	hrIdsJson := string(b2)

	// store the delayed share
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		lg.WithError(err).Error("unable to begin transaction")
		return err
	}
	_, err = mysqlWithLog.ExecTx(
		ctx,
		tx,
		`INSERT INTO v3transfer_delayed_share (share_id, account_id, view_code, dob, pin, method, mode, hr_ids_json, share_json, completed) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		shareId,
		acctId,
		securityCode,
		dob,
		pin,
		string(share.Method),
		string(share.Mode),
		hrIdsJson,
		shareJson,
		false,
	)
	if err != nil {
		lg.WithError(err).Error("could not store delayed share")
		err2 := tx.Rollback()
		if err2 != nil {
			lg.WithError(err2).Error("Couldn't rollback")
			return err2
		}
		return err
	}

	// store exam uuid <-> share_id mapping for lookup
	for _, exam := range share.ExamList {
		_, err := mysqlWithLog.ExecTx(
			ctx,
			tx,
			"INSERT IGNORE INTO v3transfer_delayed_share_examlist (share_id, uuid) VALUES (?, ?)",
			shareId,
			exam.UUID,
		)
		if err != nil {
			lg.WithField("uuid", exam.UUID).
				WithError(err).
				Error("failed to insert exam into v3transfer_delayed_share_examlist")
			err2 := tx.Rollback()
			if err2 != nil {
				lg.WithError(err2).Error("Couldn't rollback")
				return err2
			}
			return err
		}
	}

	err = tx.Commit()
	if err != nil {
		lg.WithError(err).Error("Couldn't commit transaction")
		return err
	}

	return nil
}

func IsShareDelayed(
	ctx context.Context,
	db *sql.DB,
	shareId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"share_id": shareId,
	})
	query := `
		SELECT 1
		FROM v3transfer_delayed_share
		WHERE completed = FALSE AND share_id = ?
		LIMIT 1
	`

	var exists int
	err := mysqlWithLog.QueryRowAndScan(
		ctx,
		db,
		query,
		[]interface{}{&exists},
		shareId,
	)
	if err != nil {
		lg.WithError(err).Error("error looking up delayed share")
		return false
	}

	return exists == 1
}
