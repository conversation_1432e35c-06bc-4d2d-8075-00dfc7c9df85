package mysql

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

// inserts a new entry into table shares
func InsertTestShare(
	t *testing.T,
	db *sql.DB,
	accountID string,
	patientID string,
	shareID string,
) {
	_, err := db.Exec(
		`INSERT INTO shares (account_id, patient_id, share_id) VALUES (?, ?, ?)`,
		accountID,
		patientID,
		shareID,
	)
	assert.NoError(t, err)
}

// deletes an entry from table shares
func DeleteShare(
	t *testing.T,
	db *sql.DB,
	accountID string,
	patientID string,
	shareID string,
) {
	_, err := db.Exec(
		`DELETE FROM shares WHERE account_id=? AND patient_id=? AND share_id=?`,
		accountID,
		patientID,
		shareID,
	)
	assert.NoError(t, err)
}
