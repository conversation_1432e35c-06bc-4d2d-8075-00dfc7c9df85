package pdfs

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"golang.org/x/text/language"
)

func TestCreateConsentPDFJsonString(t *testing.T) {
	testutils.SetWorkingDirToRoot(t)
	lang := language.MustParse("en-US")
	bundle := i18n.NewBundle(language.English)

	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}
	falseEnroll := false
	trueEnroll := true

	testCaseNames := []string{
		"custom_org_consent_no_delegate_ohip_enroll_false",
		"custom_org_consent_delegate_mrn_enroll_true",
		"no_custom_consent_no_delegate_ipn_recent_no_enroll",
		"no_custom_consent_delegate_ssn_no_enroll",
	}
	cases := []struct {
		name              string
		expectedpdf       string
		orgConsentText    string
		orgDelConsentText string
		request           models.Request
	}{
		{
			name:           testCaseNames[0],
			expectedpdf:    getExpectedJson(t, testCaseNames[0]),
			orgConsentText: "I, <b>_PAT_NAME_</b>, hereby waive all claims against <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories, their doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time. I authorize <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories to release my records to me as I have specified within this form.",
			request: models.Request{
				Ohip:      "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &falseEnroll,
				},
			},
		},
		{
			name:              testCaseNames[1],
			expectedpdf:       getExpectedJson(t, testCaseNames[1]),
			orgConsentText:    "I, <b>_PAT_NAME_</b>, hereby waive all claims against <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories, their doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time. I authorize <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories to release my records to me as I have specified within this form.",
			orgDelConsentText: "I_COMMA__DEL_NAME__COMMA_confirm that I am legally authorized to request and receive the medical imaging and records of <b>_PAT_NAME_</b>. On behalf of [Patient’s Full Name], I hereby waive all claims against <b>Grand River Hospital and/or St. Mary’s General Hospital</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.<br><br>I understand that the patient’s records will be made available, from <b>Grand River Hospital and/or St. Mary’s General Hospital</b>, to me via a secure third-party record storage platform, PocketHealth, and that I will be able to access, view, download, and share these records at my discretion.<br><br>I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion.<br><br>I understand that I will be able to opt out of this online access at any time.<br><br>I authorize <b>Grand River Hospital and/or St. Mary’s General Hospital</b> to release the patient's records to me, via PocketHealth, as I have specified within this form.",
			request: models.Request{
				Mrn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &trueEnroll,
					Delegate: &models.StudyRequestDelegate{
						FirstName: "Delly",
						LastName:  "Gate",
						Relation:  "Daughter",
						Address:   "123 Dellygate Lane",
						Phone:     "**********",
					},
				},
			},
		},
		{
			name:        testCaseNames[2],
			expectedpdf: getExpectedJson(t, testCaseNames[2]),
			request: models.Request{
				Ipn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					RecentExamDetails: &models.StudyRequestRecentExamDetails{
						Type: "Ultrasound",
						Site: "Ultrasound place",
						Date: "2020/04/04",
						Address: &models.StudyRequestRecentExamDetailsAddress{
							StreetNumber: "1",
							StreetName:   "Patient Street",
							PostalCode:   "M6N3H7",
						},
					},
				},
			},
		},
		{
			name:        testCaseNames[3],
			expectedpdf: getExpectedJson(t, testCaseNames[3]),
			request: models.Request{
				Ssn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					Delegate: &models.StudyRequestDelegate{
						FirstName: "Delly",
						LastName:  "Gate",
						Relation:  "Daughter",
						Address:   "123 Dellygate Lane",
						Phone:     "**********",
					},
				},
			},
		},
	}
	dt := time.Date(2021, 9, 17, 12, 0, 0, 0, time.UTC)
	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			pdfJson := CreateConsentPDFJsonString(
				context.Background(),
				bundle,
				lang,
				dt,
				test.orgConsentText,
				test.orgDelConsentText,
				111111,
				test.request,
				"testorg",
				"b64string",
				"b64signaturestring",
				false,
			)
			assert.JSONEq(t, test.expectedpdf, pdfJson)
		})
	}
}

func TestCreateUPHConsentPDFJsonString(t *testing.T) {
	testutils.SetWorkingDirToRoot(t)
	lang := language.MustParse("en-US")
	bundle := i18n.NewBundle(language.English)

	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}
	falseEnroll := false
	trueEnroll := true

	testCaseNames := []string{
		"custom_org_consent_no_delegate_ohip_enroll_false_UPH",
		"custom_org_consent_delegate_mrn_enroll_true_UPH",
		"no_custom_consent_no_delegate_ipn_recent_no_enroll_UPH",
		"no_custom_consent_delegate_ssn_no_enroll_UPH",
	}
	cases := []struct {
		name              string
		expectedpdf       string
		orgConsentText    string
		orgDelConsentText string
		request           models.Request
	}{
		{
			name:           testCaseNames[0],
			expectedpdf:    getExpectedJson(t, testCaseNames[0]),
			orgConsentText: "I, <b>_PAT_NAME_</b>, hereby waive all claims against <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories, their doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time. I authorize <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories to release my records to me as I have specified within this form.",
			request: models.Request{
				Ohip:      "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &falseEnroll,
				},
			},
		},
		{
			name:              testCaseNames[1],
			expectedpdf:       getExpectedJson(t, testCaseNames[1]),
			orgConsentText:    "I, <b>_PAT_NAME_</b>, hereby waive all claims against <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories, their doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time. I authorize <b>William Osler Health System</b> and all participating hospitals and independent health facilities contributing to the HDIRS East/West Provincial Diagnostic Imaging Repositories to release my records to me as I have specified within this form.",
			orgDelConsentText: "I_COMMA__DEL_NAME__COMMA_confirm that I am legally authorized to request and receive the medical imaging and records of <b>_PAT_NAME_</b>. On behalf of [Patient’s Full Name], I hereby waive all claims against <b>Grand River Hospital and/or St. Mary’s General Hospital</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.<br><br>I understand that the patient’s records will be made available, from <b>Grand River Hospital and/or St. Mary’s General Hospital</b>, to me via a secure third-party record storage platform, PocketHealth, and that I will be able to access, view, download, and share these records at my discretion.<br><br>I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion.<br><br>I understand that I will be able to opt out of this online access at any time.<br><br>I authorize <b>Grand River Hospital and/or St. Mary’s General Hospital</b> to release the patient's records to me, via PocketHealth, as I have specified within this form.",
			request: models.Request{
				Mrn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &trueEnroll,
					Delegate: &models.StudyRequestDelegate{
						FirstName: "Delly",
						LastName:  "Gate",
						Relation:  "Daughter",
						Address:   "123 Dellygate Lane",
						Phone:     "**********",
					},
				},
			},
		},
		{
			name:        testCaseNames[2],
			expectedpdf: getExpectedJson(t, testCaseNames[2]),
			request: models.Request{
				Ipn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					RecentExamDetails: &models.StudyRequestRecentExamDetails{
						Type: "Ultrasound",
						Site: "Ultrasound place",
						Date: "2020/04/04",
						Address: &models.StudyRequestRecentExamDetailsAddress{
							StreetNumber: "1",
							StreetName:   "Patient Street",
							PostalCode:   "M6N3H7",
						},
					},
				},
			},
		},
		{
			name:        testCaseNames[3],
			expectedpdf: getExpectedJson(t, testCaseNames[3]),
			request: models.Request{
				Ssn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					Delegate: &models.StudyRequestDelegate{
						FirstName: "Delly",
						LastName:  "Gate",
						Relation:  "Daughter",
						Address:   "123 Dellygate Lane",
						Phone:     "**********",
					},
				},
			},
		},
	}
	dt := time.Date(2021, 9, 17, 12, 0, 0, 0, time.UTC)
	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			pdfJson := CreateConsentPDFJsonString(
				context.Background(),
				bundle,
				lang,
				dt,
				test.orgConsentText,
				test.orgDelConsentText,
				111111,
				test.request,
				"testorg",
				"b64string",
				"b64signaturestring",
				true,
			)
			assert.JSONEq(t, test.expectedpdf, pdfJson)
		})
	}
}

func TestCreateNonUPHConsentPDFJsonString(t *testing.T) {
	testutils.SetWorkingDirToRoot(t)
	lang := language.MustParse("en-US")
	bundle := i18n.NewBundle(language.English)

	projectDir := "coreapi"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}
	falseEnroll := false
	trueEnroll := true

	testCaseNames := []string{
		"custom_org_consent_no_delegate_ohip_enroll_false_non_UPH",
		"custom_org_consent_delegate_mrn_enroll_true_non_UPH",
	}
	cases := []struct {
		name              string
		expectedpdf       string
		orgConsentText    string
		orgDelConsentText string
		request           models.Request
	}{
		{
			name:           testCaseNames[0],
			expectedpdf:    getExpectedJson(t, testCaseNames[0]),
			orgConsentText: "I, <b>_PAT_NAME_</b> acknowledge I am the patient or the patient's legal authorized personal representative, and I am willingly enrolling in PocketHeatlh web-based service for the purpose of obtaining digital images and/or reports from services received at Southern Illinois Healthcare (SIH) facilities. I understand that my digital images and/or reports will be made available to me via PocketHealth, a secure third-party digital image and/or reports storage platform, through which I am able to access, view, download, and share these digital images and/or reports at my discretion. I understand that based on my subscription enrollment choice in PocketHealth, future digital images and/or reports, if/when they are created, will also be added to my account and made available to access, view and download. I understand if I have opted for Connect Membership, I am solely responsible for any access permissions I have given to others which allows their ability to view, share and download my digital images and/or reports. I understand it is my responsibility to notify PocketHealth if I no longer want others to have these access permissions. I understand sharing my personal health information in an internet-based application delivery method may not be secure and expose my health information to others. When choosing this delivery method, you release SIH from any liability involving a potential or actual breach of your health information occurring based on your actions to download and/or share your protected health information. I, hereby waive all claims against Southern Illinois Hospital Services (SIHS), its directors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said digital images and/or reports. Signature of Acknowledgement By clicking and dragging using your mouse, please provide your signature below in acknowledgement of your consent.",
			request: models.Request{
				Ohip:      "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &falseEnroll,
				},
			},
		},
		{
			name:              testCaseNames[1],
			expectedpdf:       getExpectedJson(t, testCaseNames[1]),
			orgConsentText:    "I, <b>_PAT_NAME_</b> acknowledge I am the patient or the patient's legal authorized personal representative, and I am willingly enrolling in PocketHeatlh web-based service for the purpose of obtaining digital images and/or reports from services received at Southern Illinois Healthcare (SIH) facilities. I understand that my digital images and/or reports will be made available to me via PocketHealth, a secure third-party digital image and/or reports storage platform, through which I am able to access, view, download, and share these digital images and/or reports at my discretion. I understand that based on my subscription enrollment choice in PocketHealth, future digital images and/or reports, if/when they are created, will also be added to my account and made available to access, view and download. I understand if I have opted for Connect Membership, I am solely responsible for any access permissions I have given to others which allows their ability to view, share and download my digital images and/or reports. I understand it is my responsibility to notify PocketHealth if I no longer want others to have these access permissions. I understand sharing my personal health information in an internet-based application delivery method may not be secure and expose my health information to others. When choosing this delivery method, you release SIH from any liability involving a potential or actual breach of your health information occurring based on your actions to download and/or share your protected health information. I, hereby waive all claims against Southern Illinois Hospital Services (SIHS), its directors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said digital images and/or reports. Signature of Acknowledgement By clicking and dragging using your mouse, please provide your signature below in acknowledgement of your consent.",
			orgDelConsentText: "I_COMMA__DEL_NAME__COMMA_ acknowledge I am the patient or the patient’s legal authorized personal representative, and I am willingly enrolling in PocketHeatlh web-based service for the purpose of obtaining digital images and/or reports from services received at Southern Illinois Healthcare (SIH) facilities.<br><br>I understand that the patient's digital images and/or reports will be made available to me via PocketHealth, a secure third-party digital image and/or reports storage platform, through which I am able to access, view, download, and share these digital images and/or reports at the patient's discretion.<br><br>I understand that based on the patient's subscription enrollment choice in PocketHealth, future digital images and/or reports, if/when they are created, will also be added to the patient's account and made available to access, view and download.<br><br>I understand if I have opted for Connect Membership, I am solely responsible for any access permissions I have given to others which allows their ability to view, share and download the patient's digital images and/or reports.<br><br>I understand it is my responsibility to notify PocketHealth if I no longer want others to have these access permissions.<br><br>I understand sharing the patient's personal health information in an internet-based application delivery method may not be secure and expose the patient's health information to others.<br><br>When choosing this delivery method, you release SIH from any liability involving a potential or actual breach of your health information occurring based on your actions to download and/or share your protected health information.<br><br>I, hereby waive all claims against Southern Illinois Hospital Services (SIHS), its directors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said digital images and/or reports.<br><br><br><br>Signature of Acknowledgement<br><br><br><br>By clicking and dragging using your mouse, please provide your signature below in acknowledgement of your consent.",
			request: models.Request{
				Mrn:       "*********",
				FirstName: "Jane",
				LastName:  "Doe",
				Contents: models.StudyRequest{
					EnrollmentConsent: &trueEnroll,
					Delegate: &models.StudyRequestDelegate{
						FirstName: "Delly",
						LastName:  "Gate",
						Relation:  "Daughter",
						Address:   "123 Dellygate Lane",
						Phone:     "**********",
					},
				},
			},
		},
	}
	dt := time.Date(2023, 05, 26, 12, 0, 0, 0, time.UTC)
	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			pdfJson := CreateConsentPDFJsonString(
				context.Background(),
				bundle,
				lang,
				dt,
				test.orgConsentText,
				test.orgDelConsentText,
				111111,
				test.request,
				"testorg",
				"b64string",
				"b64signaturestring",
				true,
			)
			assert.JSONEq(t, test.expectedpdf, pdfJson)
		})
	}
}

func TestCreateEnrollmentConsentPDFJson(t *testing.T) {
	testutils.SetWorkingDirToRoot(t)
	lang := language.MustParse("en-US")
	bundle := i18n.NewBundle(language.English)

	caseNames := []string{
		"enroll_with_understands",
		"enroll_without_understands",
	}
	cases := []struct {
		name        string
		form        coreapi.ConsentFormData
		consent     coreapi.Consent
		expectedpdf string
	}{
		{
			name: caseNames[0],
			form: coreapi.ConsentFormData{
				ProviderName: "ToysRUs",
				New:          true,
				Opt:          "in",
				UnderstandItems: []string{
					"that the world is not flat",
					"that vaccines work",
				},
				ConsentText: "consentconsentconsentconsentconsent",
			},
			consent: coreapi.Consent{
				Opt:          "in",
				FullName:     "God Zilla",
				SignatureImg: "notarealimage",
			},
			expectedpdf: getExpectedJson(t, caseNames[0]),
		},
		{
			name: caseNames[1],
			form: coreapi.ConsentFormData{
				ProviderName:    "ToysRUs",
				New:             true,
				Opt:             "in",
				UnderstandItems: []string{},
				ConsentText:     "consentconsentconsentconsentconsent",
			},
			consent: coreapi.Consent{
				Opt:          "in",
				FullName:     "God Zilla",
				SignatureImg: "notarealimage",
			},
			expectedpdf: getExpectedJson(t, caseNames[1]),
		},
	}

	dt := time.Date(2021, 9, 16, 12, 0, 0, 0, time.UTC)
	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			pdfJson := CreateEnrollmentConsentPDFJson(
				context.Background(),
				bundle,
				lang,
				dt,
				test.form,
				test.consent,
			)
			assert.JSONEq(t, test.expectedpdf, pdfJson)
		})
	}

}

func getExpectedJson(t *testing.T, testcase string) string {
	bytes, err := ioutil.ReadFile(fmt.Sprintf("pkg/pdfs/testjson/expected_%s.json", testcase))
	if err != nil {
		t.Fatalf("could not read expected json: %v", err)
	}
	return string(bytes)
}

func TestConsentLangValidator(t *testing.T) {
	testStringPass := "I, SIHDELEGATE last,  acknowledge I am the patient or the patient's legal authorized personal representative, and I am willingly enrolling in PocketHeatlh web-based service for the purpose of obtaining digital images and/or reports from services received at Southern Illinois Healthcare (SIH) facilities.<br><br>I understand that the patient's digital images and/or reports will be made available to me via PocketHealth, a secure third-party digital image and/or reports storage platform, through which I am able to access, view, download, and share these digital images and/or reports at the patient''s discretion.<br><br>I understand that based on the patient''s subscription enrollment choice in PocketHealth, future digital images and/or reports, if/when they are created, will also be added to the patient''s account and made available to access, view and download.<br><br>I understand if I have opted for Connect Membership, I am solely responsible for any access permissions I have given to others which allows their ability to view, share and download the patient''s digital images and/or reports.<br><br>I understand it is my responsibility to notify PocketHealth if I no longer want others to have these access permissions.<br><br>I understand sharing the patient''s personal health information in an internet-based application delivery method may not be secure and expose the patient''s health information to others.<br><br>When choosing this delivery method, you release SIH from any liability involving a potential or actual breach of your health information occurring based on your actions to download and/or share your protected health information.<br><br>I, hereby waive all claims against Southern Illinois Hospital Services (SIHS), its directors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said digital images and/or reports.<br><br><br><br>Signature of Acknowledgement<br><br><br><br>By clicking and dragging using your mouse, please provide your signature below in acknowledgement of your consent."
	testStringPassMoreTags := "<ul> <li><b>This Consent and Release affects your legal rights, please read carefully.</b> <li>I, <b>_PAT_NAME_</b>, understand that I have voluntarily chosen to use the PocketHealth service, as a service provider directly engaged by me, to access my medical imaging records stored at Trillium Health Partners (“THP”) directly from my personal device and view, download, and share these records at my discretion.  I understand that the PocketHealth service is operated by PocketSix Technologies Inc. (\"PocketHealth\"), which is an independent, third-party who is not affiliated with THP and whose services are not endorsed by THP.  My use of the PocketHealth service is at my sole risk and discretion.  I may still access my medical imaging records directly through THP should I choose not to use the PocketHealth service. <li>I consent and hereby authorize THP to release my existing and future medical imaging records to PocketHealth.  My future medical imaging records at THP will automatically be released to PocketHealth so that I may add the new record to my PocketHealth account.  I understand that PocketHealth is not a service provider or an agent of THP (as such term is defined in the Personal Health Information Protection Act, 2004), and is therefore not acting on behalf of THP but is instead acting as a third party service provider voluntarily selected by me to provide me with these services.  I may withdraw my consent and opt out from receiving future records or from the PocketHealth service entirely <NAME_EMAIL> or calling 1-************. I acknowledge and agree that PocketHealth will implement my withdrawal request at the first reasonable opportunity and no longer than 24 hours of receipt of this notice. <li>I have had the opportunity to review and satisfy myself as to the sufficiency of the security and privacy controls maintained by PocketHealth in the operation of its services. <li>I hereby waive all claims against THP, its officers, directors, doctors, employees and agents (“THP Parties”) for all purposes whatsoever in connection with my voluntary use of the PocketHealth service including any release and disclosure of my medical imaging records to PocketHealth, and I hereby release and discharge the THP Parties from any liability in connection with the foregoing. </ul>"
	testStringFailClass := "I, <b data-matomo-mask> SIHDELEGATE last </b>, confirm that I am legally authorized to request and receive the medical imaging and records of SIHMAIN last. On behalf of SIHMAIN last, I hereby waive all claims against Bluewater Imaging and Edward Street Diagnostic, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.I understand that the patient's records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time.I authorize Bluewater Imaging and Edward Street Diagnostic to release the patient's records to me as I have specified within this form."
	testStringFailUnclosed := "I, <b> SIHDELEGATE last, confirm that I am legally authorized to request and receive the medical imaging and records of SIHMAIN last. On behalf of SIHMAIN last, I hereby waive all claims against Bluewater Imaging and Edward Street Diagnostic, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.I understand that the patient's records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time.I authorize Bluewater Imaging and Edward Street Diagnostic to release the patient's records to me as I have specified within this form."
	testStringFailUnaccounted := "I, <table></table> SIHDELEGATE last, confirm that I am legally authorized to request and receive the medical imaging and records of SIHMAIN last. On behalf of SIHMAIN last, I hereby waive all claims against Bluewater Imaging and Edward Street Diagnostic, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.I understand that the patient's records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time.I authorize Bluewater Imaging and Edward Street Diagnostic to release the patient's records to me as I have specified within this form."

	result, _ := ConsentLangValidator(testStringPass)
	assert.Truef(t, result, "expected pass failed")
	result, _ = ConsentLangValidator(testStringPassMoreTags)
	assert.Truef(t, result, "expected pass failed")
	result, _ = ConsentLangValidator(testStringFailClass)
	assert.Falsef(t, result, "expected fail html with class failed")
	result, _ = ConsentLangValidator(testStringFailUnclosed)
	assert.Falsef(t, result, "expected fail an html tag was not closed")
	result, _ = ConsentLangValidator(testStringFailUnaccounted)
	assert.Falsef(t, result, "expected fail html that is not accounted for in consent lang to text")

}

func TestConsentJSONValidator(t *testing.T) {
	testStringPass := `{"text": [{"text": "I, SIHDELEGATE last,  confirm that I am legally authorized to request and receive the medical imaging and records of SIHMAIN last"},{"text": ". On behalf of SIHMAIN last", "bold": true},{"text": ", I hereby waive all claims against "},{"text": "Bluewater Imaging and Edward Street Diagnostic", "bold": true},{"text": ", its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.I understand that the patient's records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion.I understand that I will be able to opt out of this online access at any time.I authorize "},{"text": "Bluewater Imaging and Edward Street Diagnostic", "bold": true},{"text": " to release the patient's records to me as I have specified within this form."}]}`
	testStringFail := `{"text": [{"text": "I, SIHDELEGATE last,  confirm that I am legally authorized to request and receive the medical imaging and records of SIHMAIN last"},{"text": ". On behalf of SIHMAIN last", "bold": true},{"text": ", I hereby waive all claims against "},{"text": "Bluewater Imaging and Edward Street Diagnostic", "bold": true},{"text": ", its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.I understand that the patient's records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion.I understand that I will be able to opt out of this online access at any time.I authorize "},{"text": "Bluewater Imaging and Edward Street Diagnostic", "bold": true},{"text": " to release the patient's records to me as I have specified within this form."}]`
	result, err := ConsentJSONValidator(testStringPass)
	if err != nil {
		t.Fatal(err)
	}
	assert.Truef(t, result, "nooo")
	result, _ = ConsentJSONValidator(testStringFail)
	assert.Falsef(t, result, "nooo")
}

func TestCreateSharePDFJsonString(t *testing.T) {
	caseNames := []string{
		"pdf_variables_escape_not_needed",
		"pdf_variables_escape_needed",
	}
	cases := []struct {
		name              string
		vars              SharePdfDynamicVariables
		expectedValidJSON bool
	}{
		{
			name: caseNames[0],
			vars: SharePdfDynamicVariables{
				FaxAttn:                     "fake fax attn",
				FaxSubject:                  "fake fax subject",
				FaxFrom:                     "fake fax from",
				FaxNote:                     "fake fax note",
				InstitutionLogoPathOrBase64: "fake Institution Logo Path Or Base 64",
				InstitutionName:             "fake institution name",
				PatientName:                 "fake patient name",
				DOB:                         "fake dob",
				SecurityCode:                "fake security code",
				RecordJson:                  "{\"modality\": \"fake modality\", \"description\": \"fake desc\", \"date\": \"fake date\"}",
				TotalRecord:                 "10",
				ExpiryDate:                  "fake expiry date",
				ExpiryTime:                  "fake expiry time",
				ValidityPeriod:              "5",
			},
			expectedValidJSON: true,
		},
		{
			name: caseNames[1],
			vars: SharePdfDynamicVariables{
				FaxAttn:                     "fake fax attn",
				FaxSubject:                  "fake fax subject",
				FaxFrom:                     "fake fax' from",
				FaxNote:                     "fake fax note",
				InstitutionLogoPathOrBase64: "fake Institution Logo Path Or Base 64",
				InstitutionName:             `fake \ institution name`,
				PatientName:                 `John"Doe"`,
				DOB:                         "fake dob",
				SecurityCode:                "fake security code",
				RecordJson:                  "{\"modality\": \"fake modality\", \"description\": \"fake desc\", \"date\": \"fake date\"}",
				TotalRecord:                 "0",
				ExpiryDate:                  "fake expiry date",
				ExpiryTime:                  "fake expiry time",
				ValidityPeriod:              "0",
			},
			expectedValidJSON: true,
		},
	}

	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			pdfJsonString := CreateSharePDFJsonString(test.vars)
			if !(json.Valid([]byte(pdfJsonString))) {
				t.Errorf("got %s", pdfJsonString)
			}
			assert.Equal(t, test.expectedValidJSON, json.Valid([]byte(pdfJsonString)))
		})
	}
}
