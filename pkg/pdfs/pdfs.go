package pdfs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"index/suffixarray"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/coreapi/pkg/util/script"
	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"golang.org/x/net/html"
	"golang.org/x/text/language"
)

type SharePdfDynamicVariables struct {
	FaxAttn                     string
	FaxSubject                  string
	FaxFrom                     string
	FaxNote                     string
	InstitutionLogoPathOrBase64 string
	InstitutionName             string
	PatientName                 string
	DOB                         string
	SecurityCode                string
	RecordJson                  string
	TotalRecord                 string
	ExpiryDate                  string
	ExpiryTime                  string
	ValidityPeriod              string
}

func CreateSharePDF(
	folderName string,
	providerOrProvider string,
	printOrFax string,
	acctId string,
	v SharePdfDynamicVariables,
	removeTmp bool,
	lang language.Tag,
) (string, []byte, error) {
	pdfJsonString := CreateSharePDFJsonString(v)
	pdfjson := []byte(pdfJsonString)
	file, err := generateSharePdf(
		pdfjson,
		folderName,
		providerOrProvider,
		printOrFax,
		removeTmp,
		lang,
	)
	if err != nil {
		err = NewPDFGenError(err)
	}

	return pdfJsonString, file, err
}

func CreateSharePDFJsonString(v SharePdfDynamicVariables) (pdfJsonString string) {
	pdfVariables := reflect.ValueOf(&v).Elem()
	for i := 0; i < pdfVariables.NumField(); i++ {
		// don't need to clean RecordJson since we add escapes in model_share.go
		if pdfVariables.Type().Field(i).Name != "RecordJson" {
			pdfVariable := pdfVariables.Field(i)
			escapedValue := stringhelpers.EscapeTextFieldForJSON(pdfVariable.String())
			pdfVariable.SetString(escapedValue)
		}
	}

	pdfJsonString = `{"faxAttn": "` + v.FaxAttn + `", "faxSubject": "` + v.FaxSubject + `", "faxFrom": "` + v.FaxFrom + `", "faxNote":"` + v.FaxNote + `", "institutionLogoUrl": "` + v.InstitutionLogoPathOrBase64 + `", "institutionName":"` + v.InstitutionName + `","patientName":"` + v.PatientName + `","dob":"` + v.DOB + `","securityCode":"` + v.SecurityCode + `", "examList":[` + v.RecordJson + `], "totalNum":` + v.TotalRecord + `, "expiryTime":"` + v.ExpiryTime + `", "expiryDate":"` + v.ExpiryDate + `", "validityPeriod":` + v.ValidityPeriod + `}`
	return pdfJsonString
}

func RecreateSharePDF(
	ctx context.Context,
	folderName string,
	providerOrProvider string,
	printOrFax string,
	acctId string,
	pdfJsonString string,
	removeTmp bool,
	lang language.Tag,
) ([]byte, error) {
	pdfjson := []byte(pdfJsonString)
	file, err := generateSharePdf(
		pdfjson,
		folderName,
		providerOrProvider,
		printOrFax,
		removeTmp,
		lang,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("can't recreate share pdf")
		err = NewPDFGenError(err)
	}
	return file, err
}

func generateSharePdf(
	pdfjson []byte,
	folderName string,
	providerOrProvider string,
	printOrFax string,
	removeTmp bool,
	lang language.Tag,
) (file []byte, err error) {
	tmpFileId := ksuid.New().String()
	path := "vault/tmp/prep/" + tmpFileId
	if filepath.Dir(path) != "vault/tmp/prep" {
		logrus.WithFields(logrus.Fields{
			"reason": "path traversal detected",
			"path":   path,
		}).Error("generate share PDF failed")
		return nil, errors.New(errmsg.ERR_BAD_PATH)
	}
	if _, err := os.Stat(path + "/" + folderName); err != nil {
		err := os.MkdirAll(path+"/"+folderName, 0700)
		if err != nil {
			return nil, errors.New(errmsg.ERR_OP_CONFLICT)
		}
	} else {
		if _, err := os.Stat(path); !os.IsNotExist(err) {
			err := os.RemoveAll(path + "/" + folderName)
			if err != nil {
				return nil, errors.New(errmsg.ERR_OP_CONFLICT)
			}
		}
		err = os.MkdirAll(path+"/"+folderName, 0700)
		if err != nil {
			return nil, errors.New(errmsg.ERR_OP_CONFLICT)
		}
	}

	err = ioutil.WriteFile(path+"/"+folderName+"/DESCRIPTOR", pdfjson, 0600)
	if err != nil {
		return nil, errors.New(errmsg.ERR_COPY_FAIL)
	}

	err, stdErrStr := script.RunNodeScript(
		"jsScripts/generateSharePage.js",
		[]string{
			path + "/" + folderName + "/DESCRIPTOR",
			providerOrProvider,
			printOrFax,
			lang.String(),
		},
	)

	if err != nil {
		err2 := os.RemoveAll(path)
		if err2 != nil {
			logrus.WithFields(logrus.Fields{
				"path":      path,
				"error_msg": err2,
			}).Error("os.RemoveAll failed")
		}
		return nil, errors.New(stdErrStr)
	}

	finalFilePath := path + "/" + folderName + "/MAIN.pdf"
	/* #nosec G304 */
	file, err = ioutil.ReadFile(finalFilePath)
	if err != nil {
		return nil, errors.New(errmsg.ERR_READ_FAIL)
	}

	if removeTmp {
		defer os.RemoveAll(path)
	}

	return file, nil
}

func CreateConsentPDFJsonString(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	dt time.Time,
	consentText string,
	delegateConsentText string,
	requestId int64,
	r models.Request,
	orgName string,
	orgLogoUrlOrBase64 string,
	signatureBase64 string,
	isUPH bool,
) (pdfJsonString string) {

	var enrollmentText, enrollHeader string
	if consentText == "" {
		consentText = localizedDefaultConsentText(
			ctx,
			i18nBundle,
			lang,
			orgName,
			r.Contents.EnrollmentConsent,
		)
	}

	var delegInfoJson string
	var delegName string
	var delegConfirmation string
	if r.Contents.Delegate != nil {
		//need table describing delegate and delegate consent text
		delegName = r.Contents.Delegate.FullName()
		delegInfoJson = localizedDelegateInfoJson(ctx, lang, i18nBundle, r.Contents.Delegate)
		consentText = delegateConsentText
		if consentText == "" {
			consentText = localizedDefaultDelegateConsentText(
				ctx,
				i18nBundle,
				lang,
				r.Contents.EnrollmentConsent,
				orgName,
			)
		}

		delegConfirmation = localizedDelegateConfirmation(ctx, i18nBundle, lang)
	}
	_, err := ConsentLangValidator(consentText)
	if err != nil {
		logrus.WithError(err).Error("Consent Lang Validator returned an error")
	}
	consentText = consentTextToPdfText(consentText, r.FirstName, r.LastName, delegName)

	if r.Contents.EnrollmentConsent != nil {
		enrollHeader = localizedEnrollHeaderJson(ctx, i18nBundle, lang)
		enrollmentText = localizedEnrollText(ctx, i18nBundle, lang, r.Contents.EnrollmentConsent)
	}

	examInfoJson := localizedExamInfoTable(ctx, i18nBundle, lang, r.Contents.RecentExamDetails)

	ptInfoJson := localizedPtTable(ctx, i18nBundle, lang, r)
	date := datetime.MediumLocaleDateStr(dt, lang)

	title := localizedConsentTitle(ctx, i18nBundle, lang)

	var pdfJson string

	type PdfJSONStruct struct {
		HeaderText1        string
		HeaderText2        string
		Title              string
		PtInfoJson         string
		DelegInfoJson      string
		ExamInfoJson       string
		ConsentText        string
		Date               string
		DelegConfirmation  string
		FooterText1        string
		FooterText2        string
		FooterText3        string
		FooterText4        string
		SignatureBase64    string
		OrgLogoUrlOrBase64 string

		RequestId      string
		EnrollHeader   string
		EnrollmentText string
	}

	var data PdfJSONStruct

	if isUPH {
		headerText1 := `The world's first patient-driven image sharing platform that lets patients`
		headerText2 := `access, share and take ownership over their own medical imaging.`

		footerText1 := `If you have any questions regarding this request for records,`
		footerText2 := `contact PocketHealth at 1-************ (toll-free).`

		footerText3 := `PocketHealth is used by over 600,000 patients and 550 hospitals/imaging`
		footerText4 := `clinics across North America. Learn more at `

		data = PdfJSONStruct{
			HeaderText1:        headerText1,
			HeaderText2:        headerText2,
			Title:              title,
			PtInfoJson:         ptInfoJson,
			DelegInfoJson:      delegInfoJson,
			ExamInfoJson:       examInfoJson,
			ConsentText:        consentText,
			Date:               date,
			DelegConfirmation:  delegConfirmation,
			FooterText1:        footerText1,
			FooterText2:        footerText2,
			FooterText3:        footerText3,
			FooterText4:        footerText4,
			SignatureBase64:    signatureBase64,
			OrgLogoUrlOrBase64: orgLogoUrlOrBase64,
		}

		pdfJson = `{ 
			"pageSize": "letter",
			"pageMargins": [ 44, 44, 44, 44 ],
			"header": {
				"columns": [
					{
						"image": "logoImage",
						"alignment": "left",
						"fit": [76, 24]
					},
					{
						"stack": [
							{"text": "{{.HeaderText1}}", "style": "headerText"},
							{"text": "{{.HeaderText2}}", "style": "headerText"}
						],
						"width": "*",
						"alignment": "right"
					}
				],
				"margin": [44, 24, 44, 0]
			},
			"content": [
				{
					"text": "{{.Title}}",
					"style": "title"
				},
				{{.PtInfoJson}}{{.DelegInfoJson}}{{.ExamInfoJson}}
				{"text": "Consent", "style": "subheader"},
				{"text": {{.ConsentText}}, "style": "consentText"},
				{
					"image": "signatureImage",
					"width": 100,
					"margin": [0, 40, 0, 0]
				},
				{"text": "{{.Date}}", "margin": [0, 16, 0, 0], "style": "consentText"},
				{"text": "{{.DelegConfirmation}}", "margin": [0, 4, 0, 0], "style": "consentText"}
			],
			"footer": {
				"stack": [
					{
						"canvas": [
							{
								"type": "line",
								"x1": 44, "y1": 0,
								"x2": 568, "y2": 0,
								"lineWidth": 1,
								"lineColor": "#CBD5E1"
							}
						]
					},
					{
						"columns": [
							{
								"stack": [
									{"text": "{{.FooterText1}}", "style": "footerTextLeft"},
									{"text": "{{.FooterText2}}", "style": "footerTextLeft"}
								],
								"alignment": "left",
								"margin": [ 44, 0, 0, 0 ]
							},
							{
								"stack": [
									{"text": "{{.FooterText3}}", "style": "footerTextRight"},
									{
										"text": [
											{ "text": "{{.FooterText4}}", "style": "footerTextRight" },
											{ "text": "www.pockethealth.com", "decoration": "underline", "style": "footerTextRight" }
										]
									}
								],
								"alignment": "right",
								"margin": [ 0, 0, 44, 0 ] 
							}
						],
						"margin": [0, 8, 0, 0]
					}
				]
			},
			"images": {
				"signatureImage": "{{.SignatureBase64}}",
				"logoImage": "{{.OrgLogoUrlOrBase64}}"
			},
	
			"styles": {
				"defaultStyle": {
					"columnGap": 20
				},
				"title": {
					"fontSize": 24,
					"bold": true,
					"margin": [0, 28, 0, 0]
				},
				"subheader": {
					"fontSize": 16,
					"bold": true,
					"margin": [0, 8, 0, 0]
				},
				"tableExample": {
					"margin": [0, 5, 0, 15]
				},
				"tableHeader": {
					"bold": true,
					"fontSize": 13,
					"color": "black"
				},
				"footerTextLeft": {
					"fontSize": 8,
					"color": "#0F172A"
				},
				"footerTextRight": {
					"fontSize": 8,
					"color": "#64748B"
				},
				"headerText": {
					"fontSize": 8,
					"color": "#64748B"
				},
				"consentText": {
					"fontSize": 12,
					"color": "#000000",
					"margin": [0, 8, 0, 0],
					"lineHeight": 1.5
				}
			}
		}`
	} else {

		data = PdfJSONStruct{
			Title:              title,
			RequestId:          strconv.FormatInt(requestId, 10),
			PtInfoJson:         ptInfoJson,
			DelegInfoJson:      delegInfoJson,
			ExamInfoJson:       examInfoJson,
			EnrollHeader:       enrollHeader,
			EnrollmentText:     enrollmentText,
			ConsentText:        consentText,
			Date:               date,
			DelegConfirmation:  delegConfirmation,
			SignatureBase64:    signatureBase64,
			OrgLogoUrlOrBase64: orgLogoUrlOrBase64,
		}

		pdfJson = `{ 
			"pageSize": "letter",
			"content": [
				{
					"image": "logoImage",
					"width": 100,
					"alignment": "center"
				},
			{
					"text": "{{.Title}} - ID:{{.RequestId}}",
					"style": "header",
					"alignment": "center"
				},
				{{.PtInfoJson}}{{.DelegInfoJson}}{{.ExamInfoJson}}{{.EnrollHeader}}{{.EnrollmentText}}
				{"text": "Consent","style": "subheader"},{{.ConsentText}},{
					"image": "signatureImage",
					"width": 100
				},
				{"text": "{{.Date}}"}, {"text": "{{.DelegConfirmation}}"}
			],
			"images": {
				"signatureImage": "{{.SignatureBase64}}",
				"logoImage": "{{.OrgLogoUrlOrBase64}}"
			},

			"styles": {
				"defaultStyle": {
					"columnGap": 20
				},
				"header": {
					"fontSize": 18,
					"bold": true,
					"margin": [0, 0, 0, 10]
				},
				"subheader": {
					"fontSize": 16,
					"bold": true,
					"margin": [0, 10, 0, 5]
				},
				"tableExample": {
					"margin": [0, 5, 0, 15]
				},
				"tableHeader": {
					"bold": true,
					"fontSize": 13,
					"color": "black"
				}
			}
		}`
	}
	tmpl, err := template.New("pdfJsonString").Parse(pdfJson)
	if err != nil {
		fmt.Println("Error parsing template:", err)
		return
	}
	var result bytes.Buffer
	err = tmpl.Execute(&result, data)
	if err != nil {
		fmt.Println("Error executing template:", err)
		return
	}
	resultstring := result.String()
	return resultstring
}

func localizedDelegateConfirmation(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
) string {
	return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateConfirmation",
			Other: "By submitting this request, I confirm that the information I have provided above is correct and complete and that I am legally authorized to make this request on behalf of the patient.",
		},
	}, lang)
}

func localizedConsentTitle(ctx context.Context, i18nBundle *i18n.Bundle, lang language.Tag) string {
	return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "ConsentTitle",
			Other: "Online Patient Imaging Request",
		},
	}, lang)
}

func localizedPtTable(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	r models.Request,
) string {

	header := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "PatientInfoConsentHeader",
			Other: "Patient Information",
		},
	}, lang)

	nameLbl := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "PatientNameConsent",
			Other: "Full Name",
		},
	}, lang)

	dobLbl := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "PatientDOBConsent",
			Other: "Date of Birth (mm/dd/yyyy)",
		},
	}, lang)

	emailLbl := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "PatientEmailConsent",
			Other: "Email",
		},
	}, lang)

	phoneLbl := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "PatientPhoneConsent",
			Other: "Phone Number",
		},
	}, lang)

	identifierJson := localizedIdJson(ctx, i18nBundle, lang, r)

	return fmt.Sprintf(`{
				"text": "%s",
				"style": "subheader"
			},
			{
				"style": "tableExample",
				"table": {
					"body": [
						[{ "text": "%s", "bold": true }, "%s"],
						[{ "text": "%s", "bold": true }, "%s"],%s
						[{ "text": "%s", "bold": true }, "%s"],
						[{ "text": "%s", "bold": true }, "%s"]
					]
				}
			},`, header, nameLbl, fmt.Sprintf("%s %s", r.FirstName, r.LastName), dobLbl, r.Dob, identifierJson, emailLbl, r.Email, phoneLbl, r.Tel)
}

func localizedEnrollHeaderJson(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
) string {

	header := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "FutureExamHeader",
			Other: "Future Exam Notifications",
		},
	}, lang)
	return fmt.Sprintf(`{
			"text": "%s",
			"style": "subheader"
			},`, header)
}

func localizedEnrollText(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	enrollConsent *bool,
) string {
	enrollText := ""
	if *enrollConsent {
		enrollText = phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "NotifyMeText",
				Other: "Please notify me automatically when new exams are available to add to my account.",
			},
		}, lang)

	} else {
		enrollText = phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "DontNotifyMeText",
				Other: "I do not wish to receive access to any future exams. I understand that I will need to submit a new request for any new imaging.",
			},
		}, lang)
	}

	return fmt.Sprintf(`{"text":"%s", "bold":true},`, enrollText)
}

func localizedDelegateInfoJson(
	ctx context.Context,
	lang language.Tag,
	i18nBundle *i18n.Bundle,
	del *models.StudyRequestDelegate,
) string {

	title := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentHeader",
			Other: "Delegate Information",
		},
	}, lang)
	fName := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentFirst",
			Other: "First Name",
		},
	}, lang)
	lName := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentLast",
			Other: "Last Name",
		},
	}, lang)
	relation := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentRelation",
			Other: "Relation to Patient",
		},
	}, lang)
	phone := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentPhone",
			Other: "Phone Number",
		},
	}, lang)
	address := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "DelegateInfoConsentAddr",
			Other: "Address",
		},
	}, lang)

	return fmt.Sprintf(`{ "text": "%s", "style": "subheader" },
				{
					"style": "tableExample",
					"table": {
						"body": [
							[{ "text": "%s", "bold": true }, "%s"],
							[{ "text": "%s", "bold": true }, "%s"],
							[{ "text": "%s", "bold": true }, "%s"],
							[{ "text": "%s", "bold": true }, "%s"],
							[{ "text": "%s", "bold": true }, "%s"]

						]
					}
				},`, title, fName, del.FirstName, lName, del.LastName, relation, del.Relation, phone, del.Phone, address, del.Address)
}

func localizedExamInfoTable(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	recentExamDetails *models.StudyRequestRecentExamDetails,
) string {
	var examInfoJson string
	examTable := ""
	if recentExamDetails != nil {
		if len(recentExamDetails.ExamMetadatas) > 0 {
			for _, examMetadata := range recentExamDetails.ExamMetadatas {
				examTypeTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
					DefaultMessage: &i18n.Message{
						ID:    "ExamTypeConsentRow",
						Other: "Exam Type",
					},
				}, lang)
				examTable += fmt.Sprintf(
					`[{ "text": "%s", "bold": true }, "%s"],`,
					examTypeTitle,
					examMetadata.Type,
				)
				examDateTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
					DefaultMessage: &i18n.Message{
						ID:    "ExamDateConsentRow",
						Other: "Exam Date",
					},
				}, lang)
				examTable += fmt.Sprintf(
					`[{ "text": "%s", "bold": true }, "%s"],`,
					examDateTitle,
					examMetadata.Year,
				)
			}
		}
		if recentExamDetails.Type != "" {
			examTypeTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
				DefaultMessage: &i18n.Message{
					ID:    "ExamTypeConsentRow",
					Other: "Exam Type",
				},
			}, lang)
			examTable += fmt.Sprintf(
				`[{ "text": "%s", "bold": true }, "%s"],`,
				examTypeTitle,
				recentExamDetails.Type,
			)
		}
		if recentExamDetails.Site != "" {
			examSiteTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
				DefaultMessage: &i18n.Message{
					ID:    "ExamSiteConsentRow",
					Other: "Exam Site",
				},
			}, lang)
			examTable += fmt.Sprintf(
				`[{ "text": "%s", "bold": true }, "%s"],`,
				examSiteTitle,
				recentExamDetails.Site,
			)
		}
		if recentExamDetails.Date != "" {
			examDateTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
				DefaultMessage: &i18n.Message{
					ID:    "ExamDateConsentRow",
					Other: "Exam Date",
				},
			}, lang)
			examTable += fmt.Sprintf(
				`[{ "text": "%s", "bold": true }, "%s"],`,
				examDateTitle,
				recentExamDetails.Date,
			)
		}
		if recentExamDetails.Address != nil {
			examAddrTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
				DefaultMessage: &i18n.Message{
					ID:    "ExamAddrConsentRow",
					Other: "Addr",
				},
			}, lang)
			examTable += fmt.Sprintf(
				`[{ "text": "%s", "bold": true }, "%s"],`,
				examAddrTitle,
				recentExamDetails.Address.String(),
			)
		}
	}
	if examTable != "" {
		examTableTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "ExamInfoTitle",
				Other: "Recent Exam Information",
			},
		}, lang)
		examTable = strings.TrimRight(examTable, ",")
		examInfoJson = fmt.Sprintf(`{"style": "tableExample","table": {"body": [%s]}},`, examTable)
		examInfoJson = fmt.Sprintf(
			`{"text": "%s","style": "subheader"},%s`,
			examTableTitle,
			examInfoJson,
		)
	}
	return examInfoJson
}

func localizedIdJson(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	r models.Request,
) string {
	format := `[{"text":"%s", "bold":true},"%s"],`
	if r.Ohip != "" {
		ohipNumberText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "OhipNumberLabel",
				Other: "OHIP Number",
			},
		}, lang)
		return fmt.Sprintf(format, ohipNumberText, r.Ohip)
	} else if r.Bcphn != "" {
		bcphnNumberText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "BCPHNNumberLabel",
				Other: "BC PHN",
			},
		}, lang)
		return fmt.Sprintf(format, bcphnNumberText, r.Bcphn)
	} else if r.Ssn != "" {
		ssnText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "SSNLabel",
				Other: "Social Security Number",
			},
		}, lang)
		return fmt.Sprintf(format, ssnText, r.Ssn)
	} else if r.Ipn != "" {
		ipnText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "IPNLabel",
				Other: "Insurance Policy Number",
			},
		}, lang)
		return fmt.Sprintf(format, ipnText, r.Ipn)
	} else if r.AltId != "" {
		altIDText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "altIDLabel",
				Other: "Other Health ID",
			},
		}, lang)
		return fmt.Sprintf(format, altIDText, r.AltId)
	} else if r.Mrn != "" {
		mrnText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "mrnLabel",
				Other: "MRN",
			},
		}, lang)
		return fmt.Sprintf(format, mrnText, r.Mrn)
	}
	return ""
}

func localizedDefaultConsentText(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	orgName string,
	enrollConsent *bool,
) string {
	//default consent text
	if enrollConsent != nil {
		return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "RequestConsentEnrollment",
				Other: "I, <b>_PAT_NAME_</b> hereby waive all claims against {{.OrgName}}, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.\nI understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.\nI understand that I will be able to opt out of this online access at any time. I authorize <b> {{.OrgName}}</b> to release my records to me as I have specified within this form.",
			},
			TemplateData: map[string]interface{}{
				"OrgName": orgName,
			},
		}, lang)
	} else {
		return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "RequestConsentNoEnroll",
				Other: "I, <b>_PAT_NAME_</b>, hereby waive all claims against <b>{{.OrgName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I authorize <b> {{.OrgName}}</b> to release my records to me as I have specified within this form.",
			},
			TemplateData: map[string]interface{}{
				"OrgName": orgName,
			},
		}, lang)
	}
}

func localizedDefaultDelegateConsentText(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	enrollConsent *bool,
	orgName string,
) string {
	if enrollConsent != nil {
		return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "DelegateConsentEnroll",
				Other: "I_COMMA__DEL_NAME__COMMA_confirm that I am legally authorized to request and receive the medical imaging and records of <b>_PAT_NAME_</b>. On behalf of <b>_PAT_NAME_</b>, I hereby waive all claims against <b>{{.OrgName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that the patient’s records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion.\n I understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion. I understand that I will be able to opt out of this online access at any time. I authorize <b>{{.OrgName}}</b> to release the patient’s records to me as I have specified within this form.",
			},
			TemplateData: map[string]interface{}{
				"OrgName": orgName,
			},
		}, lang)
	} else {
		return phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID:    "DelegateConsentEnroll",
				Other: "I_COMMA__DEL_NAME__COMMA_confirm that I am legally authorized to request and receive the medical imaging and records of <b>_PAT_NAME_</b>. On behalf of <b>_PAT_NAME_</b>, I hereby waive all claims against <b>{{.OrgName}}</b>, its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that the patient’s records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I authorize <b>{{.OrgName}}</b> to release the patient’s records to me as I have specified within this form.",
			},
			TemplateData: map[string]interface{}{
				"OrgName": orgName,
			},
		}, lang)
	}
}

func consentTextToPdfText(
	inputText string,
	patientFName string,
	patientLName string,
	delegFullName string,
) string {
	inputText = strings.ReplaceAll(inputText, "_PAT_NAME_", patientFName+" "+patientLName)
	inputText = strings.ReplaceAll(inputText, "_COMMA__DEL_NAME__COMMA_", ", "+delegFullName+", ")
	inputText = strings.ReplaceAll(inputText, "’", "'")
	inputText = strings.ReplaceAll(inputText, "\"", "\\\"")

	if strings.Contains(inputText, "<ul>") {
		/*remove <ul> </ul> tag*/
		inputText = inputText[4 : len(inputText)-5]
		re := regexp.MustCompile("(<li>)|(</li>)")
		inputTextArr := re.Split(inputText, -1)
		/*bullet list format*/
		returnValue := []string{}
		for _, sect := range inputTextArr {
			if sect == "" || sect == " " {
				continue
			}
			//var element = {text: []}
			element := []string{}
			re = regexp.MustCompile("(<b>)|(</b>)")
			splitText := re.Split(sect, -1)
			if len(splitText) > 1 {
				for j, subSect := range splitText {
					if !isOdd(j) {
						element = append(element, `{"text": "`+subSect+`"}`)
					} else {
						element = append(element, `{"text": "`+subSect+`", "bold":true}`)
					}
				}
			} else {
				element = append(element, `"`+splitText[0]+`"`)
			}

			returnValue = append(returnValue, `{"text":[`+strings.Join(element, ",")+`]}`)
		}
		return `{"ul":[` + strings.Join(returnValue, ",") + `]}`
	} else {
		/*paragraph format*/
		inputText = strings.ReplaceAll(inputText, "<br>", `\n`)
		inputText = strings.ReplaceAll(inputText, "<br></br>", `\n\n`)
		inputText = strings.ReplaceAll(inputText, "<div [A-Za-z0-9=\"']*>|</div>", "")
		inputText = strings.ReplaceAll(inputText, "<span [A-Za-z0-9=\"']*>|</span>", "")

		re := regexp.MustCompile("(<b>)|(</b>)")
		splitText := re.Split(inputText, -1)
		if len(splitText) > 1 {

			returnValue := make([]string, len(splitText))
			for i, sect := range splitText {
				if !isOdd(i) {
					returnValue[i] = `{"text": "` + sect + `"}`
				} else {
					returnValue[i] = `{"text": "` + sect + `", "bold": true}`
				}
			}
			return `{"text": [` + strings.Join(returnValue, ",") + `]}`
		} else {
			returnValue := `{"text": "` + inputText + `"}`
			return returnValue
		}
	}
}

func isOdd(i int) bool {
	return i%2 != 0
}

func CreateEnrollmentConsentPDFJson(
	ctx context.Context,
	i18nBundle *i18n.Bundle,
	lang language.Tag,
	dt time.Time,
	formData coreapi.ConsentFormData,
	consent coreapi.Consent,
) string {

	nameText := `{"text": "` + consent.FullName + `", "bold": true}`
	understandText := ""
	for i, item := range formData.UnderstandItems {
		understandText = understandText + fmt.Sprintf(
			`{ "text": "%d) %s\n", "style":"p"},`,
			i+1,
			item,
		)
	}
	waiveText := `{ "text": "` + formData.ConsentText + `\n", "style":"p"},`
	signatureText := `{ "image": "` + consent.SignatureImg + `","width": 150},`

	date := datetime.MediumLocaleDateStr(dt, lang)
	dateText := `{"text": "` + date + `"}`
	pageTitle := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "EnrollConsentPDFHeader",
			Other: "Consent for Record Release",
		},
	}, lang)
	iText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "I",
			Other: "I, ",
		},
	}, lang)
	understandIntroText := phlanguage.LocalizeString(ctx, i18nBundle, &i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "EnrollUnderstandIntroText",
			Other: ", understand the following:\\n",
		},
	}, lang)
	pdfJson := fmt.Sprintf(`{
		"pageSize": "letter",
		"content": [
		  { "image": "%s",
						"width": 200,
					"alignment": "center"
		  },

					{ "text": "%s",
						"style": "header",
				"alignment": "center"
					},
		  { "text": ["%s", %s,
			 "%s"], "style": "p" }, %s%s%s%s
		],

			  "styles": {
		  "defaultStyle": {
			"columnGap": 20
		  },
				  "header": {
					  "fontSize": 18,
			"bold": true,
					  "margin": [0, 0, 0, 40]
				  },
				  "subheader": {
					  "fontSize": 16,
					  "bold": true,
					  "margin": [0, 10, 0, 5]
				  },
				  "tableExample": {
					  "margin": [0, 5, 0, 15]
				  },
				  "tableHeader": {
					  "bold": true,
					  "fontSize": 13,
					  "color": "black"
		  },
		  "p": {
			"margin": [4, 0, 4, 20]
		  }
			  }
		  }`, GetBase64ConsentImage(), pageTitle, iText, nameText, understandIntroText, understandText, waiveText, signatureText, dateText)
	return pdfJson
}

func GetBase64ConsentImage() string {
	file, _ := os.Open("assets/pocketHealthLogo.txt")
	// We only read from this file, so we can ignore close errors.
	/* #nosec G307 */
	defer file.Close()

	bytes, _ := io.ReadAll(file)
	return string(bytes)
}

func GetBase64PocketHealthLogo() string {
	file, _ := os.Open("assets/pocketHealthLogo.txt")
	// We only read from this file, so we can ignore close errors.
	/* #nosec G307 */
	defer file.Close()

	bytes, _ := io.ReadAll(file)
	return string(bytes)
}

func ConsentLangValidator(consentText string) (bool, error) {
	/*
		check if every html tag is either as simple bold, line break, div or span ??
		check if the text itself is valid through other metrics
		check if string only contains alphanumeric characters allowing the needed
	*/
	consentText = strings.ReplaceAll(consentText, "<br>", `\n`)
	re := regexp.MustCompile("(<[a-z]*>)|(</[a-z]*>)")
	htmlTags := re.FindAllString(consentText, -1)
	splitText := re.Split(consentText, -1)
	if len(htmlTags) > 1 {
		for _, tag := range htmlTags {
			if !(strings.Contains(tag, "<b>") || strings.Contains(tag, "</b>")) &&
				!(strings.Contains(tag, "<span>") || strings.Contains(tag, "</span>")) &&
				!(strings.Contains(tag, "<div>") || strings.Contains(tag, "</div>")) &&
				!(strings.Contains(tag, "<ul>") || strings.Contains(tag, "</ul>")) &&
				!(strings.Contains(tag, "<li>") || strings.Contains(tag, "</li>")) &&
				!(strings.Contains(tag, "<li>")) {
				return false, errors.New("there exists a html tag in the consent lang that will be printed in plain text in the pdf")
			}
		}
	}
	_, err := html.Parse(strings.NewReader(consentText))
	if err != nil {
		return false, errors.New("invalid html")
	}
	if len(splitText) > 1 {
		for _, sect := range splitText {
			// text in between html tags should be strictly alphanumeric
			re = regexp.MustCompile(`([a-zA-z0-9\p{P}<>\\\/\ ]*)`)
			innerSplit := re.Split(sect, -1)
			if len(innerSplit) > 1 && innerSplit[0] != "" {
				return false, errors.New("there exists a some undesired characters that are not alphanum in consentlang")
			}
		}
	}
	openingTags := regexp.MustCompile("(<[a-z]*>)")
	closingTags := regexp.MustCompile("(</[a-z]*>)")
	listItemRE := regexp.MustCompile("(<li>)")
	closingListItemRE := regexp.MustCompile("(</li>)")
	temp := suffixarray.New([]byte(consentText))
	resultsOpening := temp.FindAllIndex(openingTags, -1)
	resultsClosing := temp.FindAllIndex(closingTags, -1)
	listItems := temp.FindAllIndex(listItemRE, -1)
	closingListItems := temp.FindAllIndex(closingListItemRE, -1)
	if len(resultsOpening) != len(resultsClosing)+(len(listItems)-len(closingListItems)) {
		return false, errors.New("there exists a HTML tag that was not closed")
	}
	return true, nil
}

func ConsentJSONValidator(consentJSON string) (bool, error) {
	/*
		check that there are no remaining html tags
		make sure the text is only alphanumeric but allow punc, and apostrophies
		check if the text itself is valid idk how tho
		check if the json is valid
	*/
	re := regexp.MustCompile("(<[a-z]*>)|(</[a-z]*>)")
	splitText := re.Split(consentJSON, -1)
	if len(splitText) > 1 {
		return false, errors.New("there exists a html tag in the json consent lang that will be printed in plain text in the pdf")
	}
	re = regexp.MustCompile(`([a-zA-z0-9\p{P}<>\/\ ]*)`)
	splitText = re.Split(consentJSON, -1)
	if len(splitText) > 1 && splitText[0] != "" {
		return false, errors.New("there exists a some undesired characters that are not alphanum in json consent")
	}
	if !json.Valid([]byte(consentJSON)) {
		return false, errors.New("the json produced by consent lang is not valid")
	}
	return true, nil
}

func CleanupTmpFiles(ctx context.Context, path string) {
	err := os.RemoveAll(path)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("os.RemoveAll failed")
	}
}
