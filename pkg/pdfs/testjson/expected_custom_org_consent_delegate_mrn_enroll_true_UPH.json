{"pageSize": "letter", "pageMargins": [44, 44, 44, 44], "header": {"columns": [{"image": "logoImage", "alignment": "left", "fit": [76, 24]}, {"stack": [{"text": "The world's first patient-driven image sharing platform that lets patients", "style": "headerText"}, {"text": "access, share and take ownership over their own medical imaging.", "style": "headerText"}], "width": "*", "alignment": "right"}], "margin": [44, 24, 44, 0]}, "content": [{"text": "Online Patient Imaging Request", "style": "title"}, {"text": "Patient Information", "style": "subheader"}, {"style": "tableExample", "table": {"body": [[{"text": "Full Name", "bold": true}, "<PERSON>"], [{"text": "Date of Birth (mm/dd/yyyy)", "bold": true}, ""], [{"text": "MRN", "bold": true}, "123456789"], [{"text": "Email", "bold": true}, ""], [{"text": "Phone Number", "bold": true}, ""]]}}, {"text": "Delegate Information", "style": "subheader"}, {"style": "tableExample", "table": {"body": [[{"text": "First Name", "bold": true}, "<PERSON><PERSON>"], [{"text": "Last Name", "bold": true}, "Gate"], [{"text": "Relation to Patient", "bold": true}, "Daughter"], [{"text": "Phone Number", "bold": true}, "**********"], [{"text": "Address", "bold": true}, "123 Dellygate Lane"]]}}, {"text": "Consent", "style": "subheader"}, {"text": {"text": [{"text": "I, <PERSON><PERSON>, confirm that I am legally authorized to request and receive the medical imaging and records of "}, {"text": "<PERSON>", "bold": true}, {"text": ". On behalf of [<PERSON><PERSON>'s Full Name], I hereby waive all claims against "}, {"text": "Grand River Hospital and/or St. Mary's General Hospital", "bold": true}, {"text": ", its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records.\n\nI understand that the patient's records will be made available, from "}, {"text": "Grand River Hospital and/or St. Mary's General Hospital", "bold": true}, {"text": ", to me via a secure third-party record storage platform, PocketHealth, and that I will be able to access, view, download, and share these records at my discretion.\n\nI understand that future records, if/when they are created will also be made available to access and add to my account, at my discretion.\n\nI understand that I will be able to opt out of this online access at any time.\n\nI authorize "}, {"text": "Grand River Hospital and/or St. Mary's General Hospital", "bold": true}, {"text": " to release the patient's records to me, via PocketHealth, as I have specified within this form."}]}, "style": "consentText"}, {"image": "signatureImage", "width": 100, "margin": [0, 40, 0, 0]}, {"text": "Sep 17, 2021", "margin": [0, 16, 0, 0], "style": "consentText"}, {"text": "By submitting this request, I confirm that the information I have provided above is correct and complete and that I am legally authorized to make this request on behalf of the patient.", "margin": [0, 4, 0, 0], "style": "consentText"}], "footer": {"stack": [{"canvas": [{"type": "line", "x1": 44, "y1": 0, "x2": 568, "y2": 0, "lineWidth": 1, "lineColor": "#CBD5E1"}]}, {"columns": [{"stack": [{"text": "If you have any questions regarding this request for records,", "style": "footerTextLeft"}, {"text": "contact PocketHealth at 1-************ (toll-free).", "style": "footerTextLeft"}], "alignment": "left", "margin": [44, 0, 0, 0]}, {"stack": [{"text": "PocketHealth is used by over 600,000 patients and 550 hospitals/imaging", "style": "footerTextRight"}, {"text": [{"text": "clinics across North America. Learn more at ", "style": "footerTextRight"}, {"text": "www.pockethealth.com", "decoration": "underline", "style": "footerTextRight"}]}], "alignment": "right", "margin": [0, 0, 44, 0]}], "margin": [0, 8, 0, 0]}]}, "images": {"signatureImage": "b64signaturestring", "logoImage": "b64<PERSON>ring"}, "styles": {"defaultStyle": {"columnGap": 20}, "title": {"fontSize": 24, "bold": true, "margin": [0, 28, 0, 0]}, "subheader": {"fontSize": 16, "bold": true, "margin": [0, 8, 0, 0]}, "tableExample": {"margin": [0, 5, 0, 15]}, "tableHeader": {"bold": true, "fontSize": 13, "color": "black"}, "footerTextLeft": {"fontSize": 8, "color": "#0F172A"}, "footerTextRight": {"fontSize": 8, "color": "#64748B"}, "headerText": {"fontSize": 8, "color": "#64748B"}, "consentText": {"fontSize": 12, "color": "#000000", "margin": [0, 8, 0, 0], "lineHeight": 1.5}}}