{"pageSize": "letter", "pageMargins": [44, 44, 44, 44], "header": {"columns": [{"image": "logoImage", "alignment": "left", "fit": [76, 24]}, {"stack": [{"text": "The world's first patient-driven image sharing platform that lets patients", "style": "headerText"}, {"text": "access, share and take ownership over their own medical imaging.", "style": "headerText"}], "width": "*", "alignment": "right"}], "margin": [44, 24, 44, 0]}, "content": [{"text": "Online Patient Imaging Request", "style": "title"}, {"text": "Patient Information", "style": "subheader"}, {"style": "tableExample", "table": {"body": [[{"text": "Full Name", "bold": true}, "<PERSON>"], [{"text": "Date of Birth (mm/dd/yyyy)", "bold": true}, ""], [{"text": "Insurance Policy Number", "bold": true}, "123456789"], [{"text": "Email", "bold": true}, ""], [{"text": "Phone Number", "bold": true}, ""]]}}, {"text": "Recent Exam Information", "style": "subheader"}, {"style": "tableExample", "table": {"body": [[{"text": "Exam Type", "bold": true}, "Ultrasound"], [{"text": "Exam Site", "bold": true}, "Ultrasound place"], [{"text": "Exam Date", "bold": true}, "2020/04/04"], [{"text": "Addr", "bold": true}, "1 Patient Street, M6N3H7"]]}}, {"text": "Consent", "style": "subheader"}, {"text": {"text": [{"text": "I, "}, {"text": "<PERSON>", "bold": true}, {"text": ", hereby waive all claims against "}, {"text": "testorg", "bold": true}, {"text": ", its doctors, employees and agents for all purposes whatsoever in connection with said communication and disclosure of information in the said records. I understand that my records will be made available to me via a secure third-party record storage platform, PocketHealth, through which I will be able to access, view, download, and share these records at my discretion. I authorize "}, {"text": " testorg", "bold": true}, {"text": " to release my records to me as I have specified within this form."}]}, "style": "consentText"}, {"image": "signatureImage", "width": 100, "margin": [0, 40, 0, 0]}, {"text": "Sep 17, 2021", "margin": [0, 16, 0, 0], "style": "consentText"}, {"text": "", "margin": [0, 4, 0, 0], "style": "consentText"}], "footer": {"stack": [{"canvas": [{"type": "line", "x1": 44, "y1": 0, "x2": 568, "y2": 0, "lineWidth": 1, "lineColor": "#CBD5E1"}]}, {"columns": [{"stack": [{"text": "If you have any questions regarding this request for records,", "style": "footerTextLeft"}, {"text": "contact PocketHealth at 1-************ (toll-free).", "style": "footerTextLeft"}], "alignment": "left", "margin": [44, 0, 0, 0]}, {"stack": [{"text": "PocketHealth is used by over 600,000 patients and 550 hospitals/imaging", "style": "footerTextRight"}, {"text": [{"text": "clinics across North America. Learn more at ", "style": "footerTextRight"}, {"text": "www.pockethealth.com", "decoration": "underline", "style": "footerTextRight"}]}], "alignment": "right", "margin": [0, 0, 44, 0]}], "margin": [0, 8, 0, 0]}]}, "images": {"signatureImage": "b64signaturestring", "logoImage": "b64<PERSON>ring"}, "styles": {"defaultStyle": {"columnGap": 20}, "title": {"fontSize": 24, "bold": true, "margin": [0, 28, 0, 0]}, "subheader": {"fontSize": 16, "bold": true, "margin": [0, 8, 0, 0]}, "tableExample": {"margin": [0, 5, 0, 15]}, "tableHeader": {"bold": true, "fontSize": 13, "color": "black"}, "footerTextLeft": {"fontSize": 8, "color": "#0F172A"}, "footerTextRight": {"fontSize": 8, "color": "#64748B"}, "headerText": {"fontSize": 8, "color": "#64748B"}, "consentText": {"fontSize": 12, "color": "#000000", "margin": [0, 8, 0, 0], "lineHeight": 1.5}}}