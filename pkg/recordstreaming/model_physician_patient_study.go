package recordstreaming

import (
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// PhysicianPatientStudy wraps the generated recordservice API
// PhysicianPatientStudy type.
type PhysicianPatientStudy generatedrecordservice.PhysicianPatientStudy

// ToShareMetadata converts a PhysicianPatientStudy to coreapi.ShareMetadata
// type.
func (study PhysicianPatientStudy) ToShareMetadata() coreapi.ShareMetadata {
	shareMetadata := coreapi.NewShareMetadata(
		study.DicomStudyTags.Value.StudyInstanceUID.Value, // shareID
		"",                         // phPatientID
		study.OrganizationID.Value, // legacyProviderID
		"",                         // orgName
		1,                          // examCount
		0,                          // healthRecordCount
		study.DicomPatientTags.Value.PatientName.Value,                // patientName
		study.DicomPatientTags.Value.PatientBirthDate.Value,           // patientDOB
		[]string{study.DicomStudyTags.Value.ReferringPhysician.Value}, // referringPhysicians
	)
	shareMetadata.PhysicianAccountID = study.PhysicianAccountID.Value
	permissionGroups := []coreapi.PermissionGroup{}
	for _, permissionGroup := range study.PermissionGroups.Value {
		permissionGroups = append(permissionGroups, coreapi.PermissionGroup{
			GroupID:   permissionGroup.GroupID.Value,
			GroupName: permissionGroup.GroupName.Value,
		})
	}
	shareMetadata.PermissionGroups = permissionGroups
	return shareMetadata
}

// ToExamRaw converts a PhysicianPatientStudy to coreapi.ExamRaw type.
func (study PhysicianPatientStudy) ToExamRaw() coreapi.ExamRaw {
	return coreapi.ExamRaw{
		ExamRawBasic: coreapi.ExamRawBasic{
			UUID:                    study.UUID.Value,
			PatientId:               study.PatientID.Value,
			ExamId:                  study.DicomStudyTags.Value.StudyInstanceUID.Value,
			TransferId:              study.TransferID.Value,
			DICOMPatientName:        study.DicomPatientTags.Value.PatientName.Value,
			DICOMBirthDate:          study.DicomPatientTags.Value.PatientBirthDate.Value,
			DICOMReferringPhysician: study.DicomStudyTags.Value.ReferringPhysician.Value,
			DICOMExamDate:           study.DicomStudyTags.Value.StudyDate.Value,
			Description:             study.DicomStudyTags.Value.StudyDescription.Value,
			PatientMrn:              study.DicomPatientTags.Value.PatientID.Value,
			Sex:                     study.DicomPatientTags.Value.PatientSex.Value,
			Phone:                   study.DicomPatientTags.Value.PatientTelephoneNumber.Value,
			BodyPart:                study.DicomStudyTags.Value.BodyPart.Value,
			Modality:                study.DicomStudyTags.Value.Modality.Value,
			Activated:               true, // always activated for a physician
		},
		Reports: StudyReports(
			study.Reports.Value,
		).ToReports(!study.ActivatedTimestamp.Value.IsZero()),
		OrgId: study.OrganizationID.Value,
	}
}

// PhysicianPatientStudies wraps a slice of the generated recordservice API
// PhysicianPatientStudy type.
type PhysicianPatientStudies []generatedrecordservice.PhysicianPatientStudy

// ToShareMetadata converts PhysicianPatientStudies to []coreapi.ShareMetadata
// type.
func (studies PhysicianPatientStudies) ToShareMetadata() []coreapi.ShareMetadata {
	result := []coreapi.ShareMetadata{}
	for _, study := range studies {
		result = append(result, PhysicianPatientStudy(study).ToShareMetadata())
	}
	return result
}

// StudyReport wraps the generated recordservice API StudyReport type.
type StudyReport generatedrecordservice.StudyReport

// ToReport converts a StudyReport to coreapi.Report type.
func (r StudyReport) ToReport(activated bool) coreapi.Report {
	return coreapi.Report{
		ReportId:    r.UUID.Value,
		Size:        r.FileSizeBytes.Value,
		Protocol:    r.Type.Value,
		Definitions: true, // always true now
		Activated:   activated,
	}
}

// StudyReports wraps a slice of the generated recordservice API StudyReport
// type.
type StudyReports []generatedrecordservice.StudyReport

func (rs StudyReports) ToReports(activated bool) []coreapi.Report {
	result := []coreapi.Report{}
	for _, report := range rs {
		result = append(result, StudyReport(report).ToReport(activated))
	}
	return result
}
