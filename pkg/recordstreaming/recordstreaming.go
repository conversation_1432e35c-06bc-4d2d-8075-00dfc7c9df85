package recordstreaming

import (
	"context"
	"database/sql"
	"errors"

	"github.com/sirupsen/logrus"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	eunitytokens "gitlab.com/pockethealth/coreapi/pkg/mysql/eunityTokens"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	sqlStudyPermissions "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RecordStreamingServicer interface {
	CreateEUnityAccessTokenForStudy(
		ctx context.Context,
		physicianAccountID string,
		studyUID string,
		providerID int64,
	) (string, error)
	GetShareMetadataForRecordStreamingStudies(
		ctx context.Context,
		physicianAccountID string,
	) ([]coreapi.ShareMetadata, error)
	GetShareForRecordStreamingStudiesForPhysician(
		ctx context.Context,
		physicianAccountID string,
		studyUID string,
		providerID int64,
	) (coreapi.Share, error)
	PhysicianCanAccessStudy(
		ctx context.Context,
		physicianAccountID string,
		studyUID string,
		providerID int64,
	) (bool, error)
}

type RecordStreamingService struct {
	db                  *sql.DB
	orgServiceClient    orgs.OrgService
	recordServiceClient recordservice.RecordServiceClientInterface
}

func NewRecordStreamingService(
	db *sql.DB,
	orgServiceClient orgs.OrgService,
	recordServiceClient recordservice.RecordServiceClientInterface,
) *RecordStreamingService {
	return &RecordStreamingService{
		db:                  db,
		orgServiceClient:    orgServiceClient,
		recordServiceClient: recordServiceClient,
	}
}

// CreateEUnityAccessTokenForStudy checks if a physician with a given physicianAccountID
// has access to the study with the given studyUID and providerID.
// If the physician has access, a eunity token is created and returned.
// That eunity token can then be used to access the study via eunity viewer.
// If the physician does not have access to the study,
// or if the study is not yet accessible, the func returns an error.
//
// It only considers studies that were shared via record streaming.
func (s *RecordStreamingService) CreateEUnityAccessTokenForStudy(
	ctx context.Context,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(
		logrus.Fields{
			"physician_account_id": physicianAccountID,
			"study_uid":            studyUID,
			"provider_id":          providerID,
		},
	)

	// verify that physician can access study
	hasAccess, err := s.PhysicianCanAccessStudy(
		ctx,
		physicianAccountID,
		studyUID,
		providerID,
	)
	if err != nil {
		return "", err
	}
	if !hasAccess {
		lg.WithError(err).Error("physician does not have access to record streaming study")
		return "", errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	recordStreamingStudyID := coreapi.NewRecordStreamingStudyID(studyUID, providerID)

	// create and return eunity token for study
	return eunitytokens.Create(ctx, s.db, recordStreamingStudyID.Key())
}

// GetShareMetadataForRecordStreamingStudies returns a list of ShareMetadata objects
// for studies that the physician account with the given physicianAccountId as access to.
// Key is the key of the patient summary.
//
// It only considers studies that were shared via record streaming.
func (s *RecordStreamingService) GetShareMetadataForRecordStreamingStudies(
	ctx context.Context,
	physicianAccountID string,
) ([]coreapi.ShareMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("physician_account_id", physicianAccountID)

	// Get studies for a physician from recordservice. This includes
	// studies accessible by shared access groups.
	response, err := s.recordServiceClient.GetV1PhysicianStudies(
		ctx,
		generatedrecordservice.GetV1PhysicianStudiesParams{
			AccountID: physicianAccountID,
		},
	)
	if err != nil {
		lg.WithError(err).
			Error("failed to get studies for physician via recordservice for record streaming studies")
		return []coreapi.ShareMetadata{}, err
	}
	studies, ok := response.(*generatedrecordservice.PhysicianPatientStudies)
	if !ok {
		return []coreapi.ShareMetadata{}, errors.New(
			"failed to type assert PhysicianPatientStudies",
		)
	}
	shareMetadata := PhysicianPatientStudies(*studies).ToShareMetadata()

	// Add provider names based on provider IDs from orgsvc.
	providerNameMap := map[int64]string{}
	for i, study := range shareMetadata {
		if providerName, ok := providerNameMap[study.LegacyProviderID]; ok {
			shareMetadata[i].OrgName = providerName
		}
		provider, err := s.orgServiceClient.GetProviderByLegacyId(ctx, study.LegacyProviderID)
		if err != nil {
			lg.WithError(err).
				Error("failed to get name for provider via orgsvc for record streaming studies for physician")
			return []coreapi.ShareMetadata{}, err
		}
		providerNameMap[study.LegacyProviderID] = provider.Name
		shareMetadata[i].OrgName = provider.Name
	}

	return shareMetadata, nil
}

// GetShareForRecordStreamingStudiesForPhysician returns a share object
// for a study that a physician with the given physicianAccountID
// has access to.
// If the physician does not have access to a study with the given studyUID+providerID pair,
// the function returns an error.
//
// It only considers studies that were shared via record streaming.
func (s *RecordStreamingService) GetShareForRecordStreamingStudiesForPhysician(
	ctx context.Context,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) (coreapi.Share, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"physician_account_id": physicianAccountID,
		"study_uid":            studyUID,
		"provider_id":          providerID,
	})

	// Get examUUID for study.
	examUUID, err := mysql.GetExamUUIDForRecordStreamingStudy(s.db, studyUID, providerID)
	if err != nil {
		lg.WithError(err).Error("failed to get exam uuid record streaming study")
		return coreapi.Share{}, err
	}

	// Get study for a physician from recordservice. This includes
	// permission checks and returns studies accessible by shared access
	// groups.
	response, err := s.recordServiceClient.GetV1PhysicianStudies(
		ctx,
		generatedrecordservice.GetV1PhysicianStudiesParams{
			AccountID:      physicianAccountID,
			UUID:           []string{examUUID},
			IncludeReports: generatedrecordservice.NewOptBool(true),
		},
	)
	if err != nil {
		lg.WithError(err).
			Error("failed to get study for physician via recordservice for record streaming study")
		return coreapi.Share{}, err
	}
	studies, ok := response.(*generatedrecordservice.PhysicianPatientStudies)
	if !ok {
		return coreapi.Share{}, errors.New(
			"failed to type assert PhysicianPatientStudies",
		)
	}
	if len(*studies) != 1 {
		return coreapi.Share{}, errors.New(
			"failed to get single study for examUUID",
		)
	}
	rawExam := PhysicianPatientStudy((*studies)[0]).ToExamRaw()

	// get clinic data of sending provider
	provider, err := s.orgServiceClient.GetProviderByLegacyId(ctx, providerID)
	if err != nil {
		// this should not cause request to fail
		lg.WithError(err).Warning("failed to get clinic data for record streaming study")
	}

	// set display data for raw exam
	rawExam = exams.SetRawExamDisplayData(ctx, s.db, rawExam, provider)

	// create eUnityToken for study
	recordStreamingStudyID := coreapi.NewRecordStreamingStudyID(studyUID, providerID)
	eUnityToken, err := eunitytokens.Create(ctx, s.db, recordStreamingStudyID.Key())
	if err != nil {
		lg.WithError(err).Error("failed to create eunity access token for record streaming study")
		return coreapi.Share{}, err
	}

	share := coreapi.Share{
		Active:         true,
		EUnityToken:    eUnityToken,
		ExamList:       []coreapi.Exam{rawExam.ToExam(ctx)},
		HealthRecords:  []coreapi.Record{},       // no health records are shared via record streaming yet
		Method:         coreapi.RECORD_STREAMING, // all record streaming studies have been shared via the same workflow
		ShareId:        studyUID,
		ShareInitiator: provider.Name, // name of provider who shared study
		ShareType:      "provider",    // record streaming is currently only available for provider shares
	}
	return share, nil
}

// PhysicianCanAccessStudy checks if a physician has permissions to
// access a record streaming study.
func (s *RecordStreamingService) PhysicianCanAccessStudy(
	ctx context.Context,
	physicianAccountID string,
	studyUID string,
	providerID int64,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"physician_account_id": physicianAccountID,
		"study_uid":            studyUID,
		"provider_id":          providerID,
	})
	uuid, err := sqlStudyPermissions.GetExamUUIDForRecordStreamingStudy(s.db, studyUID, providerID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		lg.WithError(err).
			Error("failed to get uuid for record streaming study")
		return false, errors.New("failed to verify study access")
	}
	// if no study index exists this is not a record streaming study
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}

	// check if physician has access to study via record streaming permissions
	response, err := s.recordServiceClient.V1PhysiciansAccountIDStudiesMatchGet(
		ctx,
		generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
			AccountID: physicianAccountID,
			UUID:      []string{uuid},
		})
	if err != nil {
		lg.WithError(err).
			Error("failed to verify study access via record streaming permissions")
		return false, errors.New("failed to verify study access")
	}
	hasAccess, ok := response.(*generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON)
	if !ok {
		return false, errors.New("failed to convert recordservice response")
	}

	return bool(*hasAccess), nil
}
