//go:build integration
// +build integration

package recordstreaming

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	phtestutil "gitlab.com/pockethealth/phutils/v10/pkg/testutils"

	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	eunitytokens "gitlab.com/pockethealth/coreapi/pkg/mysql/eunityTokens"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	object "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/physicianstudypermissions"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

func TestCreateEUnityAccessTokenForStudy(t *testing.T) {
	ctx := context.Background()
	db := testutils.SetupTestDB(t)

	t.Run(
		"should return error if access verification fails",
		func(t *testing.T) {
			recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
			service := NewRecordStreamingService(db, &orgs.OrgServiceMock{}, recordService)

			physicianAccountID := phtestutil.GenerateRandomString(t, 10)
			studyUID := phtestutil.GenerateRandomString(t, 10)
			providerID := phtestutil.GenerateRandomInt64(t)
			examUUID := phtestutil.GenerateRandomString(t, 20)

			recordService.EXPECT().V1PhysiciansAccountIDStudiesMatchGet(
				ctx,
				generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
					AccountID: physicianAccountID,
					UUID:      []string{examUUID},
				}).Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(false)), errors.New("dummy"))

			mysql.InsertStudyIndex(t, db, studyUID, providerID, examUUID)
			t.Cleanup(func() {
				mysql.DeleteStudyIndex(t, db, studyUID, providerID, examUUID)
			})

			result, err := service.CreateEUnityAccessTokenForStudy(
				ctx,
				physicianAccountID,
				studyUID,
				providerID,
			)
			t.Cleanup(func() {
				eunitytokens.DeleteToken(t, db, result)
			})
			assert.Error(t, err)
			assert.Equal(t, "", result)
		},
	)

	t.Run(
		"should return unauthorized if physician does not have access to study",
		func(t *testing.T) {
			recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
			service := NewRecordStreamingService(db, &orgs.OrgServiceMock{}, recordService)

			physicianAccountID := phtestutil.GenerateRandomString(t, 10)
			studyUID := phtestutil.GenerateRandomString(t, 10)
			providerID := phtestutil.GenerateRandomInt64(t)
			examUUID := phtestutil.GenerateRandomString(t, 20)

			recordService.EXPECT().V1PhysiciansAccountIDStudiesMatchGet(
				ctx,
				generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
					AccountID: physicianAccountID,
					UUID:      []string{examUUID},
				}).Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(false)), nil)

			mysql.InsertStudyIndex(t, db, studyUID, providerID, examUUID)
			t.Cleanup(func() {
				mysql.DeleteStudyIndex(t, db, studyUID, providerID, examUUID)
			})

			result, err := service.CreateEUnityAccessTokenForStudy(
				ctx,
				physicianAccountID,
				studyUID,
				providerID,
			)
			t.Cleanup(func() {
				eunitytokens.DeleteToken(t, db, result)
			})
			assert.Error(t, err)
			assert.EqualError(t, err, errormsgs.ERR_NOT_AUTHORIZED)
			assert.Equal(t, "", result)
		},
	)

	t.Run(
		"should create and return token if study exists and physician has access",
		func(t *testing.T) {
			recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
			service := NewRecordStreamingService(db, &orgs.OrgServiceMock{}, recordService)

			physicianAccountID := phtestutil.GenerateRandomString(t, 10)
			studyUID := phtestutil.GenerateRandomString(t, 10)
			providerID := phtestutil.GenerateRandomInt64(t)
			examUUID := phtestutil.GenerateRandomString(t, 20)

			recordService.EXPECT().V1PhysiciansAccountIDStudiesMatchGet(
				ctx,
				generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetParams{
					AccountID: physicianAccountID,
					UUID:      []string{examUUID},
				}).Return(lo.ToPtr(generatedrecordservice.V1PhysiciansAccountIDStudiesMatchGetOKApplicationJSON(true)), nil)

			mysql.InsertStudyIndex(t, db, studyUID, providerID, examUUID)
			t.Cleanup(func() {
				mysql.DeleteStudyIndex(t, db, studyUID, providerID, examUUID)
			})

			result, err := service.CreateEUnityAccessTokenForStudy(
				ctx,
				physicianAccountID,
				studyUID,
				providerID,
			)
			t.Cleanup(func() {
				eunitytokens.DeleteToken(t, db, result)
			})
			assert.NoError(t, err)
			assert.NotEqual(t, "", result)
		},
	)
}

func TestGetShareMetadataForRecordStreamingStudies(t *testing.T) {
	ctx := context.Background()
	db := testutils.SetupTestDB(t)

	recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
	service := NewRecordStreamingService(db, &orgs.OrgServiceMock{}, recordService)

	t.Run(
		"should return empty list if physician does not have access to any studies",
		func(t *testing.T) {
			physicianAccountID := phtestutil.GenerateRandomString(t, 10)
			recordService.EXPECT().GetV1PhysicianStudies(
				mock.Anything,
				generatedrecordservice.GetV1PhysicianStudiesParams{
					AccountID: physicianAccountID,
				},
			).Return(&generatedrecordservice.PhysicianPatientStudies{}, nil)

			result, err := service.GetShareMetadataForRecordStreamingStudies(
				ctx,
				physicianAccountID,
			)
			assert.NoError(t, err)
			assert.Equal(t, []coreapi.ShareMetadata{}, result)
		},
	)

	t.Run("returns error if no provider is found for providerID", func(t *testing.T) {
		physicianAccountID := phtestutil.GenerateRandomString(t, 10)
		providerID := int64(0)

		// set up study with provider id "0" to cause orgService to throw
		studyUID, examUUID, _ := SetupDatabaseEntry(
			t,
			db,
			physicianAccountID,
			providerID,
			"patientName",
			"2000/01/01",
		)

		t.Cleanup(func() {
			CleanupDatabaseEntry(t, db, physicianAccountID, providerID, studyUID, examUUID)
		})

		recordService.EXPECT().GetV1PhysicianStudies(
			mock.Anything,
			generatedrecordservice.GetV1PhysicianStudiesParams{
				AccountID: physicianAccountID,
			},
		).Return(&generatedrecordservice.PhysicianPatientStudies{
			{
				UUID:           generatedrecordservice.NewOptString(examUUID),
				OrganizationID: generatedrecordservice.NewOptInt64(providerID),
			},
		}, nil)

		result, err := service.GetShareMetadataForRecordStreamingStudies(ctx, physicianAccountID)
		assert.Error(t, err)
		assert.Equal(t, []coreapi.ShareMetadata{}, result)
	})

	t.Run(
		"returns list of share metadata objects for studies physician has access to",
		func(t *testing.T) {
			physicianAccountID := phtestutil.GenerateRandomString(t, 10)

			providerId1 := phtestutil.GenerateRandomInt64(t)
			providerId2 := phtestutil.GenerateRandomInt64(t)
			patientName1 := phtestutil.GenerateRandomString(t, 10)
			patientName2 := phtestutil.GenerateRandomString(t, 10)
			patientDOB1 := strconv.Itoa(
				phtestutil.GenerateRandomIntInRange(t, ********, ********),
			)
			patientDOB2 := strconv.Itoa(phtestutil.GenerateRandomIntInRange(t, ********, ********))

			// 2 studies for providerId1, patientName1, patientDOB1
			studyUID1, examUUID1, referringPhysician1 := SetupDatabaseEntry(
				t,
				db,
				physicianAccountID,
				providerId1,
				patientName1,
				patientDOB1,
			)
			studyUID2, examUUID2, referringPhysician2 := SetupDatabaseEntry(
				t,
				db,
				physicianAccountID,
				providerId1,
				patientName1,
				patientDOB1,
			)
			// 1 study for providerId1, patientName1, patientDOB2
			studyUID3, examUUID3, referringPhysician3 := SetupDatabaseEntry(
				t,
				db,
				physicianAccountID,
				providerId1,
				patientName1,
				patientDOB2,
			)
			// 1 study for providerId1, patientName2, patientDOB1
			studyUID4, examUUID4, referringPhysician4 := SetupDatabaseEntry(
				t,
				db,
				physicianAccountID,
				providerId1,
				patientName2,
				patientDOB1,
			)
			// 1 study for providerId2, patientName1, patientDOB1
			studyUID5, examUUID5, referringPhysician5 := SetupDatabaseEntry(
				t,
				db,
				physicianAccountID,
				providerId2,
				patientName1,
				patientDOB1,
			)

			t.Cleanup(func() {
				CleanupDatabaseEntry(t, db, physicianAccountID, providerId1, studyUID1, examUUID1)
				CleanupDatabaseEntry(t, db, physicianAccountID, providerId1, studyUID2, examUUID2)
				CleanupDatabaseEntry(t, db, physicianAccountID, providerId1, studyUID3, examUUID3)
				CleanupDatabaseEntry(t, db, physicianAccountID, providerId1, studyUID4, examUUID4)
				CleanupDatabaseEntry(t, db, physicianAccountID, providerId2, studyUID5, examUUID5)
			})

			expectedResult := []coreapi.ShareMetadata{
				coreapi.NewShareMetadata(
					studyUID1,
					"",
					providerId1,
					"Demo Medical Imaging",
					1,
					0,
					patientName1,
					patientDOB1,
					[]string{referringPhysician1},
				),
				coreapi.NewShareMetadata(
					studyUID2,
					"",
					providerId1,
					"Demo Medical Imaging",
					1,
					0,
					patientName1,
					patientDOB1,
					[]string{referringPhysician2},
				),
				coreapi.NewShareMetadata(
					studyUID3,
					"",
					providerId1,
					"Demo Medical Imaging",
					1,
					0,
					patientName1,
					patientDOB2,
					[]string{referringPhysician3},
				),
				coreapi.NewShareMetadata(
					studyUID4,
					"",
					providerId1,
					"Demo Medical Imaging",
					1,
					0,
					patientName2,
					patientDOB1,
					[]string{referringPhysician4},
				),
				coreapi.NewShareMetadata(
					studyUID5,
					"",
					providerId2,
					"Demo Medical Imaging",
					1,
					0,
					patientName1,
					patientDOB1,
					[]string{referringPhysician5},
				),
			}

			physicianStudiesResponse := createPhysicianStudiesResponse(expectedResult)
			recordService.EXPECT().GetV1PhysicianStudies(
				mock.Anything,
				generatedrecordservice.GetV1PhysicianStudiesParams{
					AccountID: physicianAccountID,
				},
			).Return(&physicianStudiesResponse, nil)

			result, err := service.GetShareMetadataForRecordStreamingStudies(
				ctx,
				physicianAccountID,
			)
			assert.NoError(t, err)
			assert.ElementsMatch(t, expectedResult, result)
		},
	)
}

func TestGetShareForRecordStreamingStudiesForPhysician(t *testing.T) {
	ctx := context.Background()
	db := testutils.SetupTestDB(t)

	physicianAccountID := phtestutil.GenerateRandomString(t, 10)
	study := coreapi.NewRecordStreamingStudyID(
		phtestutil.GenerateRandomString(t, 10),
		phtestutil.GenerateRandomInt64(t),
	)
	examUUID := phtestutil.GenerateRandomString(t, 10)
	objectID := phtestutil.GenerateRandomString(t, 10)
	size := phtestutil.GenerateRandomInt64(t)

	testExam := coreapi.ExamRawBasic{
		UUID:                    examUUID,
		ExamId:                  study.StudyUID,
		Activated:               true,
		DICOMReferringPhysician: phtestutil.GenerateRandomString(t, 10),
		Description:             phtestutil.GenerateRandomString(t, 10),
		DICOMPatientName:        phtestutil.GenerateRandomString(t, 10),
		DICOMBirthDate: fmt.Sprintf(
			"%v",
			phtestutil.GenerateRandomIntInRange(t, ********, ********),
		),
		DICOMExamDate: fmt.Sprintf(
			"%v",
			phtestutil.GenerateRandomIntInRange(t, ********, ********),
		),
	}

	testCases := []struct {
		name             string
		setupFunction    func(t *testing.T, db *sql.DB, recordService *mockrecordservice.MockRecordServiceClientInterface)
		cleanupFunction  func(t *testing.T, db *sql.DB)
		validateFunction func(t *testing.T, db *sql.DB, result coreapi.Share, err error)
	}{
		{
			name: "returns error if physician does not have permission to access study",
			setupFunction: func(t *testing.T, db *sql.DB, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				mysql.InsertStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				recordService.EXPECT().GetV1PhysicianStudies(
					ctx,
					generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID:      physicianAccountID,
						UUID:           []string{examUUID},
						IncludeReports: generatedrecordservice.NewOptBool(true),
					}).Return(&generatedrecordservice.PhysicianPatientStudies{}, nil)
			},
			cleanupFunction: func(t *testing.T, db *sql.DB) {
				mysql.DeleteStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				eunitytokens.DeleteTokenByShareID(
					t,
					db,
					study.Key(),
				) // fallback in case token gets added to store
			},
			validateFunction: func(t *testing.T, db *sql.DB, result coreapi.Share, err error) {
				assert.Error(t, err)
				assert.Equal(t, coreapi.Share{}, result)
				assert.False(t, eunitytokens.HasTokenForShareID(t, db, study.Key()))
			},
		},
		{
			name: "returns error if get physician studies fails",
			setupFunction: func(t *testing.T, db *sql.DB, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				mysql.InsertStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				recordService.EXPECT().GetV1PhysicianStudies(
					ctx,
					generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID:      physicianAccountID,
						UUID:           []string{examUUID},
						IncludeReports: generatedrecordservice.NewOptBool(true),
					}).Return(&generatedrecordservice.PhysicianPatientStudies{}, errors.New("dummy"))
			},
			cleanupFunction: func(t *testing.T, db *sql.DB) {
				mysql.DeleteStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				eunitytokens.DeleteTokenByShareID(
					t,
					db,
					study.Key(),
				) // fallback in case token gets added to store
			},
			validateFunction: func(t *testing.T, db *sql.DB, result coreapi.Share, err error) {
				assert.Error(t, err)
				assert.Equal(t, coreapi.Share{}, result)
				assert.False(t, eunitytokens.HasTokenForShareID(t, db, study.Key()))
			},
		},
		{
			name: "returns error if study does not have study index",
			cleanupFunction: func(t *testing.T, db *sql.DB) {
				eunitytokens.DeleteTokenByShareID(
					t,
					db,
					study.Key(),
				) // fallback in case token gets added to store
			},
			validateFunction: func(t *testing.T, db *sql.DB, result coreapi.Share, err error) {
				assert.Error(t, err)
				assert.Equal(t, coreapi.Share{}, result)
				assert.False(t, eunitytokens.HasTokenForShareID(t, db, study.Key()))
			},
		},
		{
			// checking correct error handling in error case
			// - exam should always exist if study index exists
			name: "returns error if no matching exam exists for study index entry",
			setupFunction: func(t *testing.T, db *sql.DB, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				// no unique study index to exam entry
			},
			cleanupFunction: func(t *testing.T, db *sql.DB) {
				eunitytokens.DeleteTokenByShareID(
					t,
					db,
					study.Key(),
				) // fallback in case token gets added to store
			},
			validateFunction: func(t *testing.T, db *sql.DB, result coreapi.Share, err error) {
				assert.Error(t, err)
				assert.Equal(t, coreapi.Share{}, result)
				assert.False(t, eunitytokens.HasTokenForShareID(t, db, study.Key()))
			},
		},
		{
			name: "returns share with data from exam and issuing clinic",
			setupFunction: func(t *testing.T, db *sql.DB, recordService *mockrecordservice.MockRecordServiceClientInterface) {
				recordService.EXPECT().GetV1PhysicianStudies(
					ctx,
					generatedrecordservice.GetV1PhysicianStudiesParams{
						AccountID:      physicianAccountID,
						UUID:           []string{examUUID},
						IncludeReports: generatedrecordservice.NewOptBool(true),
					}).Return(&generatedrecordservice.PhysicianPatientStudies{
					{
						UUID:           generatedrecordservice.NewOptString(examUUID),
						OrganizationID: generatedrecordservice.NewOptInt64(study.ProviderID),
						Reports: generatedrecordservice.NewOptNilStudyReportArray(
							[]generatedrecordservice.StudyReport{
								{
									UUID: generatedrecordservice.NewOptString(objectID),
								},
							},
						),
					},
				}, nil)
				mysql.InsertStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				exams.InsertTestExam(t, db, testExam, true)
				object.InsertObject(t, db, objectID, true)
				object.InsertObjectMapping(t, db, examUUID, objectID, size)
			},
			cleanupFunction: func(t *testing.T, db *sql.DB) {
				mysql.DeleteStudyIndex(t, db, study.StudyUID, study.ProviderID, examUUID)
				exams.DeleteExam(t, db, examUUID)
				object.DeleteObject(t, db, objectID)
				object.DeleteObjectMapping(t, db, examUUID, objectID)
				eunitytokens.DeleteTokenByShareID(
					t,
					db,
					study.Key(),
				) // fallback in case token gets added to store
			},
			validateFunction: func(t *testing.T, db *sql.DB, result coreapi.Share, err error) {
				assert.NoError(t, err)
				assert.Equal(t, true, result.Active)
				assert.NotEmpty(t, result.EUnityToken)
				assert.Equal(t, coreapi.RECORD_STREAMING, result.Method)
				assert.Equal(t, study.StudyUID, result.ShareId)
				assert.Equal(t, "provider", result.ShareType)
				if assert.Equal(t, 1, len(result.ExamList)) {
					resultExam := result.ExamList[0]
					assert.Equal(t, examUUID, resultExam.UUID)
					if assert.Equal(t, 1, len(resultExam.Reports)) {
						assert.Equal(t, objectID, resultExam.Reports[0].ReportId)
					}
				}
				assert.True(t, eunitytokens.HasTokenForShareID(t, db, study.Key()))
			},
		},
	}

	for _, testcase := range testCases {
		t.Run(testcase.name, func(t *testing.T) {
			recordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
			service := NewRecordStreamingService(db, &orgs.OrgServiceMock{}, recordService)

			if testcase.setupFunction != nil {
				testcase.setupFunction(t, db, recordService)
			}

			t.Cleanup(func() {
				if testcase.cleanupFunction != nil {
					testcase.cleanupFunction(t, db)
				}
			})

			result, err := service.GetShareForRecordStreamingStudiesForPhysician(
				ctx,
				physicianAccountID,
				study.StudyUID,
				study.ProviderID,
			)
			if testcase.validateFunction != nil {
				testcase.validateFunction(t, db, result, err)
			}
		})
	}
}

func SetupDatabaseEntry(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	providerID int64,
	patientName string,
	patientDOB string,
) (string, string, string) {
	studyUID := phtestutil.GenerateRandomString(t, 10)

	// add permission for physician to access study
	mysql.InsertStudyPermission(t, db, physicianAccountID, studyUID, providerID)
	// add test exam
	testExam := exams.CreateAndInsertTestExamWithNameAndDOB(t, db, patientName, patientDOB)
	// add study index to map to exam uuid
	mysql.InsertStudyIndex(t, db, studyUID, providerID, testExam.UUID)

	return studyUID, testExam.UUID, testExam.DICOMReferringPhysician
}

func CleanupDatabaseEntry(
	t *testing.T,
	db *sql.DB,
	physicianAccountID string,
	providerID int64,
	studyUID string,
	examUUID string,
) {
	mysql.DeleteStudyPermission(t, db, physicianAccountID, studyUID, providerID)
	mysql.DeleteStudyIndex(t, db, physicianAccountID, providerID, examUUID)
	exams.DeleteExam(t, db, examUUID)
}

func createPhysicianStudiesResponse(
	shareMetadata []coreapi.ShareMetadata,
) generatedrecordservice.PhysicianPatientStudies {
	result := generatedrecordservice.PhysicianPatientStudies{}
	for _, share := range shareMetadata {
		physicianPatientStudy := generatedrecordservice.PhysicianPatientStudy{
			OrganizationID: generatedrecordservice.NewOptInt64(share.LegacyProviderID),
			DicomStudyTags: generatedrecordservice.NewOptDicomStudyTags(
				generatedrecordservice.DicomStudyTags{
					StudyInstanceUID: generatedrecordservice.NewOptString(share.ShareID),
					ReferringPhysician: generatedrecordservice.NewOptNilString(
						share.ReferringPhysicians[0],
					),
				},
			),
			DicomPatientTags: generatedrecordservice.NewOptDicomPatientTags(
				generatedrecordservice.DicomPatientTags{
					PatientName:      generatedrecordservice.NewOptString(share.PatientName),
					PatientBirthDate: generatedrecordservice.NewOptString(share.PatientDOB),
				},
			),
			PermissionGroups: generatedrecordservice.NewOptNilPermissionGroupArray(
				[]generatedrecordservice.PermissionGroup{},
			),
		}
		result = append(result, physicianPatientStudy)
	}

	return result
}
