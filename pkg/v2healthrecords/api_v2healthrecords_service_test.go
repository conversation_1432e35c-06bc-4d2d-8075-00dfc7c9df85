//go:build integration
// +build integration

package v2healthrecords

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/DATA-DOG/go-sqlmock"
	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
)

func TestHtmlToPdfZipConversion(t *testing.T) {
	testHtml := "<html>\n" +
		"<body>\n" +
		"Hello world\n" +
		"</body>\n" +
		"</html>"

	t.Run("html response can be converted and zipped", func(t *testing.T) {
		t.Skip("This test just requires local manual verification, and doesn't have any assertions")
		testReader := strings.NewReader(testHtml)
		testReadCloser := io.NopCloser(testReader)
		zipCloser, err := convertHtmlToPdfZip(testReadCloser)
		assert.NoError(t, err)
		assert.NotNil(t, zipCloser)
		bytes, err := io.ReadAll(zipCloser)
		fmt.Println(string(bytes))
		os.WriteFile("/tmp/test.zip", bytes, 0644)
		// unzip /tmp/test.zip && open test.zip and compare manually
	})
}

func TestGetPatientAge(t *testing.T) {
	db, mockDB, err := sqlmock.New()
	require.NoError(t, err)
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	t.Run("no patient returned from acct svc", func(t *testing.T) {
		s := V2HealthRecordsApiService{
			sqldb:       db,
			acctSvc:     &accountservice.AcctSvcMock{GetPatientReturn: accountservice.Patient{}},
			examService: examServiceMock,
		}
		acctId := ksuid.New().String()
		ptId := ksuid.New().String()
		ptAge := 51

		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  //instanceUploadProgressPercent
		)

		study.PatientID = ptId
		study.DicomPatientTags.PatientBirthDate = time.Now().
			AddDate(-ptAge, 0, 0).
			Format("********")
		var b *bool
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, acctId, false, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)
		expectGetPatientFromDb(t, s, ptId, acctId, ptAge)
	})

	t.Run("pt returned from acctsvc with bad dob", func(t *testing.T) {
		acctId := ksuid.New().String()
		ptId := ksuid.New().String()
		ptAge := 51
		badAgePatient := accountservice.Patient{
			PatientId: ptId,
			AccountId: acctId,
			DOB:       "-1",
		}

		s := V2HealthRecordsApiService{
			sqldb:       db,
			acctSvc:     &accountservice.AcctSvcMock{GetPatientReturn: badAgePatient},
			examService: examServiceMock,
		}

		study := recordservice.FormatPatientStudy(
			true, // activated
			recordservice.FULL_AVAILABILITY,
			true, // hasReport
			100,  //instanceUploadProgressPercent
		)

		study.PatientID = ptId
		study.DicomPatientTags.PatientBirthDate = time.Now().
			AddDate(-ptAge, 0, 0).
			Format("********")
		var b *bool
		recordServiceMock.ExpectedCalls = nil
		recordServiceMock.EXPECT().
			GetStudies(mock.Anything, acctId, false, false, b, []string{}).
			Return(
				[]recordservice.PatientStudy{study}, nil,
			)

		expectGetPatientFromDb(t, s, ptId, acctId, ptAge)

	})

	t.Run("pt returned from acctsvc with good db", func(t *testing.T) {
		acctId := ksuid.New().String()
		ptId := ksuid.New().String()
		ptAge := 51

		then := time.Now().UTC().AddDate(-ptAge, 0, 0)

		goodAgePatient := accountservice.Patient{
			PatientId: ptId,
			AccountId: acctId,
			DOB:       then.Format(time.DateOnly),
		}

		s := V2HealthRecordsApiService{
			sqldb:       db,
			acctSvc:     &accountservice.AcctSvcMock{GetPatientReturn: goodAgePatient},
			examService: examServiceMock,
		}

		age, err := s.getPatientAge(context.TODO(), acctId, ptId)
		require.NoError(t, err)

		expectAgesToMatch(t, ptAge, age)
		require.NoError(t, mockDB.ExpectationsWereMet())
	})
}

func expectGetPatientFromDb(
	t *testing.T,
	s V2HealthRecordsApiService,
	ptId string,
	acctId string,
	ptAge int,
) {
	age, err := s.getPatientAge(context.TODO(), acctId, ptId)
	require.NoError(t, err)

	expectAgesToMatch(t, ptAge, age)
}

func expectAgesToMatch(t *testing.T, ptAge int, epochDob int64) {
	//Getting the current time in UTC
	currentTime, err := time.Parse(
		time.DateOnly,
		time.Now().UTC().AddDate(-ptAge, 0, 0).Format(time.DateOnly),
	)
	require.NoError(t, err)

	timeDifference := epochDob - currentTime.Unix()
	require.True(t, timeDifference == 0)
}
