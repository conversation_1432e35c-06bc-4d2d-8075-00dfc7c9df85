package v2healthrecords

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"github.com/samply/golang-fhir-models/fhir-models/fhir"
	"gitlab.com/pockethealth/coreapi/pkg/consentPdf"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/util/file"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	sqlUploadSessions "gitlab.com/pockethealth/coreapi/pkg/mysql/healthrecorduploadtokens"
	sqlHandlers "gitlab.com/pockethealth/coreapi/pkg/mysql/recordhandlers"
	"gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	sqlShareObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/shareobjects"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type V2HealthRecordsApiService struct {
	sqldb          *sql.DB
	hlthRecSvcUser hrs.HlthRecSvcUser
	acctSvc        accountservice.AccountService
	examService    exams.ExamServiceInterface

	// injuected functions (so we can mock them when testing)
	isAuthForFeature IsAuthForFeatureFunc
}

type IsAuthForFeatureFunc func(token string, featureId uint64) (bool, error)

func NewV2HealthRecordsApiService(
	db *sql.DB,
	hlthRecSvcUser hrs.HlthRecSvcUser,
	as accountservice.AccountService,
	examService exams.ExamServiceInterface,
) coreapi.V2HealthRecordsApiServicer {
	return &V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          as,
		isAuthForFeature: auth.IsAuthForFeature,
		examService:      examService,
	}
}

// custom middleware to verify upload token
func (s *V2HealthRecordsApiService) ValidateAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token := r.Header.Get("Authorization")
		acctId, patientId, sessionId, err := auth.DecodeHealthRecordsUploadToken(token)
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).
				WithField("reason", "couldn't decode token").
				Error("upload healthrecords unauthorized")
			httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}

		// check if token is valid
		valid, err := sqlUploadSessions.IsValidV2Session(
			r.Context(),
			s.sqldb,
			acctId,
			patientId,
			sessionId,
		)
		if err != nil || !valid {
			httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
			return
		}

		// pass request to the next middleware or final handler
		next.ServeHTTP(w, r)
	})
}

func (s *V2HealthRecordsApiService) PostVerifyToken(
	ctx context.Context,
	acctId string,
	patientId string,
	sessionId string,
) (interface{}, error) {
	valid, err := sqlUploadSessions.IsValidV2Session(ctx, s.sqldb, acctId, patientId, sessionId)

	if err != nil || !valid {
		if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			return coreapi.HRTokenValid{Valid: false}, nil
		} else {
			return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		}
	}

	return coreapi.HRTokenValid{Valid: true}, nil
}

func (s *V2HealthRecordsApiService) PostHealthRecords(
	ctx context.Context,
	acctId string,
	patientId string,
	sessionId string,
	reader *multipart.Reader,
	covidVaccine string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
	})

	upload, err := sqlUploadSessions.GetV2UploadSession(ctx, s.sqldb, patientId)
	if err != nil {
		lg.WithError(err).Error("could not get upload session")
		return nil, err
	}

	// check if patient belong to account
	// check if upload session belong to patient
	if !s.patientBelongToAccount(ctx, acctId, patientId) || upload.PatientId != patientId {
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	res, err := s.hlthRecSvcUser.PostRecords(ctx, lg, acctId, patientId, "", "", reader)
	if err != nil {
		lg.WithError(err).Error("failed to post to hrs")
		return nil, err
	}

	// if post was successful, set token to used in db
	err = sqlUploadSessions.UseV2Session(ctx, s.sqldb, acctId, patientId)
	if err != nil {
		lg.WithError(err).Error("could not set token to used")
		return nil, err
	}

	return res, nil
}

func (s *V2HealthRecordsApiService) GetHealthRecords(
	ctx context.Context,
	acctId string,
	patientId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
	})

	// check ownership of patient
	if !s.patientBelongToAccount(ctx, acctId, patientId) {
		lg.Warn("patient does not belong to account")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	res, err := s.hlthRecSvcUser.GetRecordsByAcctAndPatient(ctx, acctId, patientId)
	if err != nil {
		lg.WithError(err).Error("hrs get records failed")
		return nil, err
	}

	return res, nil
}

func (s *V2HealthRecordsApiService) PostHealthRecordsUpload(
	ctx context.Context,
	acctId string,
	patientId string,
	jwtToken string,
	covidVaccine string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
	})

	// covid vaccine upload should be available for all plan type
	if covidVaccine != "true" {
		isAuth, err := s.isAuthForFeature(jwtToken, uint64(planservice.HEALTH_RECORDS))
		if err != nil {
			lg.WithError(err).
				Error("unable to check if user has auth to access health records feature")
			return nil, err
		}
		if !isAuth {
			lg.Error("user has no access to health records feature")
			return nil, errors.New("user has no access to health records feature")
		}
	}

	sessionId, err := secure.GenerateRandomString(12)
	if err != nil {
		lg.WithError(err).Error("Failed to generate sessionId")
		return nil, err
	}

	// create the token
	// passing in profileId=0 for now, should remove
	token, err := auth.MakeHealthRecordUploadToken(patientId, acctId, sessionId)
	if err != nil {
		lg.WithError(err).Error("Failed to create HR token")
		return nil, err
	}

	// add upload session details to database
	err = sqlUploadSessions.InsertV2UploadSession(ctx, s.sqldb, acctId, patientId, sessionId)
	if err != nil {
		return nil, err
	}

	return coreapi.HRUploadSession{UploadSessionToken: token, RegionID: regions.GetRegionID()}, nil
}

func (s *V2HealthRecordsApiService) PutHealthRecord(
	ctx context.Context,
	acctId string,
	patientId string,
	recordId string,
	reader *multipart.Reader,
	token string,
	covidVaccine string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
		"record_id":  recordId,
	})

	// check for access to hr feature
	// covid vaccine is available for all accounts
	if covidVaccine != "true" {
		isAuth, err := s.isAuthForFeature(token, uint64(planservice.HEALTH_RECORDS))
		if err != nil {
			lg.WithError(err).
				Error("unable to check if user has auth to access health records feature")
			return nil, err
		}
		if !isAuth {
			lg.Error("user has no access to health records feature")
			return nil, errors.New("user has no access to health records feature")
		}
	}

	// check if record id and patient id belong to the account
	canAccess, err := s.canAccessHealthRecord(ctx, recordId, acctId, patientId, "")
	if err != nil {
		lg.WithError(err).Error("failed to check for access to health record")
		return nil, err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access health record")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	// passing in dummy values now, should remove post migration
	res, err := s.hlthRecSvcUser.PutRecord(
		ctx,
		lg,
		acctId,
		patientId,
		"",
		"",
		recordId,
		reader,
	)
	if err != nil {
		lg.WithError(err).Error("failed to put to hrs")
		return nil, err
	}

	return res, nil
}

func (s *V2HealthRecordsApiService) GetHealthRecordThumbnailById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
	shareId string,
) (io.ReadCloser, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
		"share_id":   shareId,
		"record_id":  recordId,
	})

	canAccess, err := s.canAccessHealthRecord(ctx, recordId, acctId, patientId, shareId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to health record")
		return nil, err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access health record")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	return s.hlthRecSvcUser.GetHealthRecordThumbnailById(ctx, lg, recordId)
}

func (s *V2HealthRecordsApiService) GetHealthRecordById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
	shareId string,
) (io.ReadCloser, string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
		"record_id":  recordId,
		"share_id":   shareId,
	})

	canAccess, err := s.canAccessHealthRecord(ctx, recordId, acctId, patientId, shareId)
	if err != nil {
		lg.WithError(err).Error("error checking for access to health record")
		return nil, "", err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access health record")
		return nil, "", errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}
	recordResponse, metadata, err := s.hlthRecSvcUser.GetHealthRecordById(ctx, lg, recordId)
	if err != nil {
		// We used to transparently just return s.hlthRecSvcUser.GetHealthRecordById(ctx, lg, recordId),
		// and so only want to process the body if no error, else just continue to transparently return the result
		return recordResponse, metadata, err
	}

	var structuredMetadata hrs.RecordResponse

	err = json.Unmarshal([]byte(metadata), &structuredMetadata)
	if err != nil {
		return recordResponse, metadata, err
	}

	if structuredMetadata.Record.FHIRResourceType == fhir.ResourceTypeRiskAssessment {
		zipBytes, err := convertHtmlToPdfZip(recordResponse)
		return zipBytes, metadata, err
	}

	return recordResponse, metadata, err
}

func (s *V2HealthRecordsApiService) DeleteHealthRecordById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
		"record_id":  recordId,
	})

	canAccess, err := s.canAccessHealthRecord(ctx, recordId, acctId, patientId, "")
	if err != nil {
		lg.WithError(err).Error("error checking for access to health record")
		return err
	}
	if !canAccess {
		lg.WithError(err).Error("not allowed to access health record")
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	return s.hlthRecSvcUser.DeleteRecordById(ctx, recordId)
}

func (s *V2HealthRecordsApiService) canAccessHealthRecord(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
	shareId string,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patientId":  patientId,
		"share_id":   shareId,
		"hr_id":      recordId,
	})

	found := false

	// check for valid share id first since if this is not there,
	// we can assume the passed in patient id is the actual pt
	// trying to view their own hr
	if shareId != "" {
		var err error
		found, err = shareobjects.HealthRecordShareExists(ctx, s.sqldb, shareId, recordId)
		if err != nil {
			return false, err
		}
	} else if canAccess, err := physicianCanAccessHealthRecord(ctx, s.sqldb, recordId, acctId); canAccess && err == nil {
		return true, nil
	} else {
		if !s.patientBelongToAccount(ctx, acctId, patientId) {
			lg.Warn("patient does not belong to account")
			return false, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		}

		// get list of records
		// passing in dummy values now, should remove post migration
		recs, err := s.hlthRecSvcUser.GetRecordsList(ctx, acctId, patientId)
		if err != nil {
			return false, err
		}

		for _, id := range recs {
			if recordId == id {
				found = true
				break
			}
		}
	}

	if !found {
		lg.Warn("hr id not found in account's records")
		return false, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	return true, nil
}

func (s *V2HealthRecordsApiService) patientBelongToAccount(
	ctx context.Context,
	acctId string,
	patientId string,
) bool {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": acctId,
		"patient_id": patientId,
	})

	// call acctsvc to get list of patients
	pts, err := s.acctSvc.GetPatients(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("failed to get account patients")
		lg.Warn("failed to verify patient ownership")
		return false
	}

	for _, pt := range pts {
		if pt.PatientId == patientId {
			return true
		}
	}

	return false
}

func (s *V2HealthRecordsApiService) SearchMyChartOrgs(ctx context.Context, query string) ([]coreapi.FHIROrg, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"query": query,
	})

	// call hrs to search the list of orgs
	resp, err := s.hlthRecSvcUser.SearchMyChartOrgs(ctx, query)
	if err != nil {
		lg.WithError(err).Error("failed to search mychart orgs")
		return nil, err
	}

	return resp, nil
}

func (s *V2HealthRecordsApiService) CreateMyChartIntegration(
	ctx context.Context,
	acctId string,
	ptId string,
	mc *coreapi.MyChartIntegrationRequest,
) error {
	lg := logutils.CtxLogger(ctx).WithField("patient_id", ptId)
	// check if pt belongs to account and forward request to HRS
	if !s.patientBelongToAccount(ctx, acctId, ptId) {
		lg.Warn("patient does not belong to account")
		return errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	return s.hlthRecSvcUser.CreateMyChartIntegration(ctx, acctId, ptId, mc)
}

func (s *V2HealthRecordsApiService) GenerateGailRiskResult(
	ctx context.Context,
	acctId string,
	ptId string,
	gr *coreapi.GailQuestionnairePatientResponses,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithField("patient_id", ptId)
	// check if pt belongs to account
	if !s.patientBelongToAccount(ctx, acctId, ptId) {
		lg.Warn("patient does not belong to account")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	// grab the patient's age
	age, err := s.getPatientAge(ctx, acctId, ptId)
	if err != nil {
		lg.WithError(err).Error("could not get a valid dob for patient")
		return nil, err
	}

	gr.Dob = &age
	// forward request to HRS
	return s.hlthRecSvcUser.GenerateGailRiskResult(ctx, acctId, ptId, gr)
}

func (s *V2HealthRecordsApiService) GetRecordsByType(
	ctx context.Context,
	acctId string,
	ptId string,
	resourceType string,
	limit int,
) ([]coreapi.Record, error) {
	lg := logutils.CtxLogger(ctx).WithField("patient_id", ptId)
	// check if pt belongs to account
	if !s.patientBelongToAccount(ctx, acctId, ptId) {
		lg.Warn("patient does not belong to account")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	return s.hlthRecSvcUser.GetRecordsByType(ctx, acctId, ptId, resourceType, limit)
}

func (s *V2HealthRecordsApiService) getPatientAge(ctx context.Context, acctId, ptId string) (int64, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"acct_id": acctId,
		"pt_id":   ptId,
	})
	// grab the patient's info
	pt, err := s.acctSvc.GetPatient(ctx, acctId, ptId)
	if err != nil {
		lg.WithError(err).Error("could not fetch patient")
		// continue anyway to fallback method
	}
	birthdate, err := time.Parse(time.DateOnly, pt.DOB)
	if err != nil || birthdate.IsZero() {
		// look up patient's latest exam to grab DOB
		// TODO: get DOB from account service instead of from studies
		dob, err := s.examService.GetPatientBirthDateByPatientID(ctx, acctId, ptId)
		if err != nil {
			lg.WithError(err).Error("could not get a valid dob for patient")
			return -1, err
		}

		birthdate, err = time.Parse("********", dob)
		if err != nil || birthdate.IsZero() {
			lg.WithError(err).Error("could not parse a valid dob for patient")
			return -1, errors.New("invalid dob")
		}
	}

	return birthdate.Unix(), nil
}

func age(year int, month time.Month, day int) int {
	ty, tm, td := time.Now().UTC().Date()
	today := time.Date(ty, tm, td, 0, 0, 0, 0, time.UTC)
	birthdate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	if today.Before(birthdate) {
		return 0
	}
	age := ty - year
	anniversary := birthdate.AddDate(age, 0, 0)
	if anniversary.After(today) {
		age--
	}
	return age
}

func physicianCanAccessHealthRecord(
	ctx context.Context,
	db *sql.DB,
	recordId string,
	acctId string,
) (bool, error) {
	// check if this account ID belongs to a physician
	physicianHandlerId, err := sqlHandlers.LookupHandlerId(
		ctx,
		db,
		coreapi.PhysicianAccount,
		acctId,
	)
	if err != nil {
		return false, nil
	}

	// accountId belongs to a physician
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"recordId":     recordId,
		"acctId":       acctId,
		"account_type": coreapi.PhysicianAccount,
	})

	// check if any shares exist that this object is in that the physician has access to
	hrShareIds, err := sqlShareObjects.GetHealthRecordShareIds(ctx, db, recordId)
	if err != nil {
		lg.WithError(err).Error("unauthorized physician access: health record not in share")
		return false, err
	}
	canView, err := sqlHandlers.HandlerCanViewAtLeastOneShare(
		ctx,
		db,
		physicianHandlerId,
		hrShareIds,
	)
	// physicians can access reports inside of shares they can view
	if err != nil {
		lg.WithError(err).Error("unauthorized physician access: unable to check if handler can view share")
		return false, err
	}

	return canView, nil
}

func convertHtmlToPdfZip(closer io.ReadCloser) (io.ReadCloser, error) {
	htmlBytes, err := io.ReadAll(closer)
	if err != nil {
		return nil, err
	}

	pdfBytes, err := consentPdf.HtmlToPdf(htmlBytes, 1.8)
	if err != nil {
		return nil, err
	}

	zipBytes, err := file.CreateFileZip("gail_report.pdf", pdfBytes)
	if err != nil {
		return nil, err
	}

	reader := bytes.NewReader(zipBytes)
	return io.NopCloser(reader), nil
}
