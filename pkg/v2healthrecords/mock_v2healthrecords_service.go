package v2healthrecords

import (
	"bytes"
	"context"
	"io"
	"mime/multipart"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

type MockV2HealthRecordsApiService struct {
	retErr error
}

func (s *MockV2HealthRecordsApiService) PostVerifyToken(
	ctx context.Context,
	acctId string,
	patientId string,
	sessionId string,
) (interface{}, error) {

	return coreapi.HRTokenValid{Valid: true}, nil
}

func (s *MockV2HealthRecordsApiService) PostHealthRecords(
	ctx context.Context,
	acctId string,
	patientId string,
	sessionId string,
	reader *multipart.Reader,
	covidVaccine string,
) (interface{}, error) {

	return nil, nil
}

func (s *MockV2HealthRecordsApiService) GetHealthRecords(
	ctx context.Context,
	acctId string,
	patientId string,
) (interface{}, error) {
	return nil, nil
}

func (s *MockV2HealthRecordsApiService) PostHealthRecordsUpload(
	ctx context.Context,
	acctId string,
	patientId string,
	jwtToken string,
	covidVaccine string,
) (interface{}, error) {
	return nil, nil
}

func (s *MockV2HealthRecordsApiService) PutHealthRecord(
	ctx context.Context,
	acctId string,
	patientId string,
	recordId string,
	reader *multipart.Reader,
	token string,
	covidVaccine string,
) (interface{}, error) {
	return nil, nil
}

func (s *MockV2HealthRecordsApiService) GetHealthRecordThumbnailById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
	shareId string,
) (io.ReadCloser, error) {

	return io.NopCloser(bytes.NewReader([]byte{})), nil
}

func (s *MockV2HealthRecordsApiService) GetHealthRecordById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
	shareId string,
) (io.ReadCloser, string, error) {
	return io.NopCloser(bytes.NewReader([]byte{})), "", nil
}

func (s *MockV2HealthRecordsApiService) DeleteHealthRecordById(
	ctx context.Context,
	recordId string,
	acctId string,
	patientId string,
) error {
	return nil
}

func (s *MockV2HealthRecordsApiService) CreateMyChartIntegration(ctx context.Context, acctId string, ptId string, mc *coreapi.MyChartIntegrationRequest) error {
	return s.retErr
}

func (s *MockV2HealthRecordsApiService) SearchMyChartOrgs(ctx context.Context, query string) ([]coreapi.FHIROrg, error) {
	return []coreapi.FHIROrg{}, nil
}

func (s *MockV2HealthRecordsApiService) GenerateGailRiskResult(ctx context.Context, acctId string, ptId string, mc *coreapi.GailQuestionnairePatientResponses) ([]byte, error) {
	return nil, s.retErr
}

func (s *MockV2HealthRecordsApiService) GetRecordsByType(ctx context.Context, acctId string, ptId string, resourceType string, limit int) ([]coreapi.Record, error) {
	return nil, s.retErr
}

// custom middleware to verify upload token
func (s *MockV2HealthRecordsApiService) ValidateAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		// pass request to the next middleware or final handler
		next.ServeHTTP(w, r)
	})
}
