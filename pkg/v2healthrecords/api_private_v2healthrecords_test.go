package v2healthrecords

import (
	"bytes"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
)

func TestPostMyChartIntegration(t *testing.T) {

	s := &MockV2HealthRecordsApiService{}
	controller := NewPrivateV2HealthRecordsApiController(s)
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	auth := "Bearer " + auth.MakeAccountAuthToken(
		"2G0MUc3o7CtYvHGVsgjuzIDZ5zw",
		"0.0.0.0",
	)
	t.Run("unexpected body", func(t *testing.T) {
		badBodyJson := []byte(`
		{
			"attribute":"not expected"
			"nested": {
				"definitely": "not expected"
			}
		}`)

		buf := bytes.NewBuffer(badBodyJson)
		path := "/v2/healthrecords/123/integrations/mychart"
		req, err := http.NewRequest("POST", path, buf)
		if err != nil {
			t.Fatal(err)
		}

		req.Header.Set("Authorization", auth)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusBadRequest {
			t.Fatalf(
				"handler returned wrong status code: got %v want %v",
				status,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("service unauthorized", func(t *testing.T) {
		goodbodyJson := []byte(`
		{
			"access":"abc123",
			"scopes": "scope1,scope2"
		}`)

		buf := bytes.NewBuffer(goodbodyJson)
		path := "/v2/healthrecords/123/integrations/mychart"
		req, err := http.NewRequest("POST", path, buf)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		s.retErr = errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusUnauthorized {
			t.Fatalf(
				"handler returned wrong status code: got %v want %v",
				status,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("service error", func(t *testing.T) {
		goodbodyJson := []byte(`
		{
			"access":"abc123",
			"scopes": "scope1,scope2"
		}`)

		buf := bytes.NewBuffer(goodbodyJson)
		path := "/v2/healthrecords/123/integrations/mychart"
		req, err := http.NewRequest("POST", path, buf)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Authorization", auth)
		rr := httptest.NewRecorder()
		s.retErr = errors.New("not authorization error")
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusInternalServerError {
			t.Fatalf(
				"handler returned wrong status code: got %v want %v",
				status,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("service ok", func(t *testing.T) {
		goodbodyJson := []byte(`
		{
			"access":"abc123",
			"scopes": "scope1,scope2"
		}`)

		buf := bytes.NewBuffer(goodbodyJson)
		path := "/v2/healthrecords/123/integrations/mychart"
		req, err := http.NewRequest("POST", path, buf)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Authorization", auth)
		rr := httptest.NewRecorder()
		s.retErr = nil
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Fatalf(
				"handler returned wrong status code: got %v want %v",
				status,
				http.StatusUnauthorized,
			)
		}
	})
}
