//go:build integration
// +build integration

package v2healthrecords

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/hrs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

// TODO: post profiles migration clean-up
// inserting dummy value for profile id for now - insert properly later
func setupShare(t *testing.T, db *sql.DB, recordId string, shareId string) {
	_, err := db.Exec(
		"INSERT INTO share_healthrecords (share_id, profile_id, record_id) VALUES (?,?,?)",
		shareId,
		0,
		recordId,
	)
	if err != nil {
		t.Fatalf("unable to set insert share: %s", err)
	}

	t.Cleanup(func() {
		db.Exec("DELETE FROM share_healthrecords WHERE share_id=?", shareId)
	})
}

func setupPhysicianShare(t *testing.T, db *sql.DB, recordId string, shareId string, physicianAcctId string) {
	setupShare(t, db, recordId, shareId)
	handlerId := "hrsintegtest_" + physicianAcctId

	_, err := db.Exec(
		`INSERT INTO record_handlers
		(id,
		handler_type,
		handler_type_id,
		upload,
		own,
		share,
		view)
		VALUES (?,?,?,?,?,?,?)`,
		handlerId,
		coreapi.PhysicianAccount,
		physicianAcctId,
		false,
		false,
		false,
		true,
	)
	if err != nil {
		t.Fatalf("unable to set insert physician: %s", err)
	}

	_, err = db.Exec(
		"INSERT INTO shares (account_id, share_id) VALUES ('abc123', ?)",
		shareId,
	)
	if err != nil {
		t.Fatalf("unable to set insert share for physician: %s", err)
	}

	_, err = db.Exec(
		"INSERT INTO record_handlers_share_map (handler_id, share_id) VALUES (?,?)",
		handlerId,
		shareId,
	)
	if err != nil {
		t.Fatalf("unable to set insert share physician mapping: %s", err)
	}

	t.Cleanup(func() {
		db.Exec("DELETE FROM record_handlers WHERE id=?", handlerId)
		db.Exec("DELETE FROM record_handlers_share_map WHERE handler_id=?", handlerId)
		db.Exec("DELETE FROM shares where share_id=?", shareId)
	})
}

func generateGoodReqBody(t *testing.T, recordTypeCode coreapi.RecordTypeCode) *multipart.Reader {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	records := `
		{
			"records":[
				{
					"name":"my lab report",
					"filename":"someotherfile",
					"source":{
						"type":"upload",
						"description":"Dr.K"
					},
					"recorddate":"********",
					"typeCode":` + strconv.Itoa(int(recordTypeCode)) + `,
					"tag": "LabReport"
				}
			]
		}
		`
	if !json.Valid([]byte(records)) {
		t.Fatal("bad json in test data")
	}
	var fw io.Writer
	var err error
	if fw, err = w.CreateFormField("records"); err != nil {
		t.Fatalf("failed to create test multipart record field: %q", err)
	}
	if _, err = io.WriteString(fw, records); err != nil {
		t.Fatalf("failed to write test multipart record field: %q", err)
	}
	if fw, err = w.CreateFormFile("someotherfile", "healthrecord.fmt"); err != nil {
		t.Fatalf("failed to create test multipart file: %q", err)
	}
	filebytes, err := ioutil.ReadFile("../../apptest/gotests/assets/test-image.png")
	if err != nil {
		t.Fatalf("error getting file for tests: %v", err)
	}
	if _, err = io.Copy(fw, bytes.NewReader(filebytes)); err != nil {
		t.Fatalf("failed to write test multipart file: %q", err)
	}
	w.Close()
	return multipart.NewReader(&b, w.Boundary())
}

func TestGetHealthRecords(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)

	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}
	// mock hlth rec svc request
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		body := ioutil.NopCloser(strings.NewReader(`
		[
			{
				"id": "0d066a9f-119e-42dd-8f51-c8b8ba38d1b2",
				"lastModified": "2021-05-27T14:07:12.737+00:00",
				"record": {
					"name": "my health record file",
					"source": {
						"type": "upload",
						"description": "Dr. T"
					},
					"recordDate": "2021-05-27T00:00:00+00:00",
					"typeCode": 11,
					"createdDate": "2021-05-27T14:07:11+00:00",
					"filenames": ["record.fmt"],
					"tag": "record"
				}
			},
			{
				"id": "c31293dd-9555-4d17-b37d-f80f8c4bfc80",
				"lastModified": "2021-05-26T20:53:06.748+00:00",
				"record": {
					"name": "my HR doc",
					"source": {
						"type": "upload",
						"description": "Dr.K"
					},
					"recordDate": "2020-04-04T00:00:00+00:00",
					"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
					"createdDate": "2021-05-26T20:53:06+00:00",
					"filenames": ["doc.doc"],
					"tag": "doc"
				}
			}
		]
		`))
		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       body,
		}

		if req.Method == "GET" && req.URL.Host == strings.Split(hlthRecSvcUser.URL, "?")[0] {
			// validate authorization
			token := req.Header.Get("Authorization")
			if token != apiKey {
				t.Errorf("bad authorization: expected %s, got %s", apiKey, token)
			}
		} else {
			t.Errorf("unexpected request to hlth rec svc: %s %s", req.Method, req.URL.String())
		}

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)
	// initialize service
	s := V2HealthRecordsApiService{
		sqldb:          db,
		hlthRecSvcUser: hlthRecSvcUser,
		acctSvc:        &accountservice.AcctSvcMock{},
	}

	t.Run("patient does not belong to account", func(t *testing.T) {
		// set up mock acctsvc
		s.acctSvc = &accountservice.AcctSvcMock{GetPatientsReturn: []accountservice.Patient{}}

		_, err := s.GetHealthRecords(context.Background(), "acct_id", "pt_id")
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("good request", func(t *testing.T) {
		// set up mock acctsvc
		s.acctSvc = &accountservice.AcctSvcMock{
			GetPatientsReturn: []accountservice.Patient{{PatientId: "patientId"}},
		}

		_, err := s.GetHealthRecords(context.Background(), "acctId", "patientId")
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
	})
}

func TestPostHealthRecordsUpload(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)
	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          &accountservice.AcctSvcMock{},
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("does not have health records feature", func(t *testing.T) {
		_, err := s.PostHealthRecordsUpload(
			context.Background(),
			"acctId",
			"patientId",
			"Free",
			"false",
		)
		expected := errors.New("user has no access to health records feature")
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("good req - not unlimited account but making covid vaccine upload", func(t *testing.T) {
		acctId := "acctId_integ_test"
		ptId := "patientId_integ_test"
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				ptId,
			)
		})
		res, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			ptId,
			"Free",
			"true",
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}
		val, ok := res.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}
	})

	t.Run("good request", func(t *testing.T) {
		acctId := "acctId_integ_test"
		ptId := "patientId_integ_test"
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				ptId,
			)
		})

		testRegionID := uint16(2)
		regions.SetRegionID(testRegionID)

		res, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			ptId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error: %v", err)
		}

		val, ok := res.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		if val.RegionID != testRegionID {
			t.Errorf("expected regionID to be: %d\t got: %d", testRegionID, val.RegionID)
		}

		actualAcctId, actualPtId, actualSID, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error %q", val)
		}

		if actualPtId != ptId {
			t.Errorf("expected patientID to be %s but got %s", ptId, actualPtId)
		}
		if actualAcctId != acctId {
			t.Errorf("expected acctId to be ")
		}

		var sessionId string
		var used bool
		err = db.QueryRow(
			"SELECT session_id, used FROM hr_v2_upload_sessions WHERE patient_id=? AND account_id=?",
			ptId,
			acctId,
		).Scan(&sessionId, &used)

		if used {
			t.Errorf("expected fase but got true")
		}
		if actualSID != sessionId {
			t.Errorf("expected session id to be %s but got %s", sessionId, actualSID)
		}
	})
}

func TestPostVerifyToken(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)
	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          &accountservice.AcctSvcMock{},
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("token already used", func(t *testing.T) {
		acctId := "acct_id"
		patientId := "patient_id"
		t.Cleanup(func() {
			db.Exec(
				"delete from hr_v2_upload_sessions where account_id=? and patient_id=?",
				acctId,
				patientId,
			)
		})

		// create token
		res, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		tokenRes, ok := res.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", tokenRes)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + tokenRes.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		db.Exec(
			"update hr_v2_upload_sessions set used=1 where patient_id=? and account_id=?",
			patientId,
			acctId,
		)

		res, err = s.PostVerifyToken(context.Background(), acctId, patientId, sessionId)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		validRes, ok := res.(coreapi.HRTokenValid)
		if !ok {
			t.Errorf("expected struct HRTokenValid but got %v", validRes)
		}

		if validRes.Valid {
			t.Errorf("expecxted valid to be false, but got true")
		}
	})

	t.Run("invalid session id", func(t *testing.T) {
		acctId := "acct_id"
		patientId := "patient_id"
		t.Cleanup(func() {
			db.Exec(
				"delete from hr_v2_upload_sessions where account_id=? and patient_id=?",
				acctId,
				patientId,
			)
		})

		// create token
		_, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		sessionId := "invalid_session_id"
		res, err := s.PostVerifyToken(context.Background(), acctId, patientId, sessionId)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		val, ok := res.(coreapi.HRTokenValid)
		if !ok {
			t.Errorf("expected struct HRTokenValid but got %v", val)
		}

		if val.Valid {
			t.Errorf("expected valid to be false but got true")
		}
	})

	t.Run("good token", func(t *testing.T) {
		acctId := "acct_id"
		patientId := "patient_id"
		t.Cleanup(func() {
			db.Exec(
				"delete from hr_v2_upload_sessions where account_id=? and patient_id=?",
				acctId,
				patientId,
			)
		})

		// create token
		res, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		tokenRes, ok := res.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", tokenRes)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + tokenRes.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		res, err = s.PostVerifyToken(context.Background(), acctId, patientId, sessionId)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		validRes, ok := res.(coreapi.HRTokenValid)
		if !ok {
			t.Errorf("expected struct HRTokenValid but got %v", validRes)
		}

		if !validRes.Valid {
			t.Errorf("expected valid to be false but got true")
		}
	})
}

func TestPostHealthRecordsDoRequest(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)
	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}

	t.Run("hrs return status 400, 401, 500", func(t *testing.T) {
		// set up service with mock
		// return 400
		doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			hlthRecSvcResp := http.Response{
				StatusCode: http.StatusBadRequest,
				Body:       ioutil.NopCloser(strings.NewReader(`Bad request body`)),
			}
			return &hlthRecSvcResp, nil
		}
		hlthRecSvcUser.SetDoRequest(doRequestMock)
		hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)
		s := V2HealthRecordsApiService{
			sqldb:            db,
			hlthRecSvcUser:   hlthRecSvcUser,
			acctSvc:          &accountservice.AcctSvcMock{},
			isAuthForFeature: mockIsAuthForFeature,
		}

		acctId := "acct_id"
		patientId := "patient_id"
		t.Cleanup(func() {
			db.Exec(
				"delete from hr_v2_upload_sessions where account_id=? and patient_id=?",
				acctId,
				patientId,
			)
		})

		res, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		val, ok := res.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error: %q", err)
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"false",
		)
		if err == nil {
			t.Errorf("expected error but got none")
		}

		// return 401
		doRequestMock = func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			hlthRecSvcResp := http.Response{
				StatusCode: http.StatusUnauthorized,
				Body:       ioutil.NopCloser(strings.NewReader(`Not authorized`)),
			}
			return &hlthRecSvcResp, nil
		}
		hlthRecSvcUser.SetDoRequest(doRequestMock)
		hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)
		s = V2HealthRecordsApiService{
			sqldb:          db,
			hlthRecSvcUser: hlthRecSvcUser,
			acctSvc:        &accountservice.AcctSvcMock{},
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"false",
		)
		if err == nil {
			t.Errorf("expected error but got none")
		}

		// return 500
		doRequestMock = func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
			hlthRecSvcResp := http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       ioutil.NopCloser(strings.NewReader(`Some err message`)),
			}
			return &hlthRecSvcResp, nil
		}
		hlthRecSvcUser.SetDoRequest(doRequestMock)
		hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)
		s = V2HealthRecordsApiService{
			sqldb:          db,
			hlthRecSvcUser: hlthRecSvcUser,
			acctSvc:        &accountservice.AcctSvcMock{},
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"false",
		)
		if err == nil {
			t.Errorf("expected error but got none")
		}
	})
}

func TestPutHealthRecordById(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)
	var err error
	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}
	// mock hlth rec svc request
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       ioutil.NopCloser(strings.NewReader(`{uploadSessionToken: ""}`)),
		}

		if req.Method == "PUT" &&
			req.URL.String() == "https://"+hlthRecSvcUser.URL+"/v1/records/mockRecordId" {
			// validate authorization
			token := req.Header.Get("Authorization")
			if token != apiKey {
				t.Errorf("bad authorization: expected %s, got %s", apiKey, token)
			}
			err := req.ParseMultipartForm(3 * 1024 * 1024 * 1024)
			if err != nil {
				t.Fatalf("could not parse request to hlth rec svc: %q", err)
			}
			metadataStr := req.FormValue("recordMetadata")
			if metadataStr == "" {
				t.Error("no metadata in request to hlth rec svc")
			}

			body := ioutil.NopCloser(strings.NewReader(`
			[
				{
					"id": "mockRecordId",
					"lastModified": "",
					"record": {
						"name": "my health record file",
						"source": {
							"type": "upload",
							"description": "Dr. T"
						},
						"recordDate": "2021-05-27T00:00:00+00:00",
						"type": 11,
						"createdDate": "2021-05-27T14:07:11+00:00",
						"filenames": ["record.fmt"],
						"tag": "record"
					}
				}
			]
			`))
			hlthRecSvcResp.Body = body
		} else if req.Method == "GET" && strings.Contains(req.URL.String(), "https://"+hlthRecSvcUser.URL+"/v1/records/list") {
			body := ioutil.NopCloser(strings.NewReader(`["mockRecordId"]`))
			hlthRecSvcResp.Body = body
		} else {
			t.Errorf("unexpected request to hlth rec svc: %s %s", req.Method, req.URL.String())
		}

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)

	// set up mock acctsvc
	acctId := "acct_id"
	patientId := "patient_id"
	acctsvc := &accountservice.AcctSvcMock{
		GetPatientsReturn: []accountservice.Patient{{PatientId: patientId}},
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          acctsvc,
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("invalid request body", func(t *testing.T) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `This is not JSON`

		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}
		if fw, err = w.CreateFormFile("someotherfile", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		if _, err = io.WriteString(fw, "test file contents"); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			multipartReader,
			"Unlimited",
			"false",
		)

		if err == nil {
			t.Error("expected error but got none")
		}
		expected := errors.New(errormsgs.ERR_INVALID_REQ_BODY)
		if err.Error() != expected.Error() {
			t.Errorf("expected error to be %s but got %s", expected, err)
		}
	})

	t.Run("bad request with too large an image", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image-3.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}

		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// Records with 51 filenames
		records := `
			{
				"records":[
					{
						"filenames":["../../apptest/gotests/assets/test-image-3.png"],
						"source":{
							"type":"upload",
							"description":"Dr.K"
						},
						"recorddate":"********",
						"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
						"tag": "LabReport"
					}
				]
			}
			`

		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}

		if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}

		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			multipartReader,
			"Unlimited",
			"false",
		)
		expected := errors.New(errormsgs.ERR_FILE_SIZE)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request with more than 50 images", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}

		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		records := `
			{
				"records":[
					{
						"filenames":["../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png"],
						"source":{
							"type":"upload",
							"description":"Dr.K"
						},
						"recorddate":"********",
						"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
						"tag": "LabReport"
					}
				]
			}
			`

		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}

		for i := 0; i < 51; i++ {
			if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
				t.Fatalf("failed to create test multipart file: %s", err)
			}
			_, err = io.Copy(fw, png)
			if err != nil {
				t.Fatalf("failed to write test multipart file: %s", err)
			}
		}

		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			multipartReader,
			"Unlimited",
			"false",
		)

		expected := errors.New(errormsgs.ERR_INVALID_REQ_BODY)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request - free user", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"Free",
			"false",
		)
		expected := errors.New("user has no access to health records feature")
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("good request - free user but editting covid vaccine", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"Free",
			"true",
		)
		if err != nil {
			t.Errorf("got error when expected none: %s", err)
		}
	})

	t.Run("good request", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("got error when expected none: %s", err)
		}
	})

	t.Run("good request with multiple images", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()
		png2, err := os.OpenFile("../../apptest/gotests/assets/test-image-2.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png2.Close()
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `
			{
				"records":[
					{
						"name": "my lab report",
						"filenames":["file1", "file2"],
						"source":{
							"type":"upload",
							"description":"Dr.K"
						},
						"recorddate":"********",
						"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
						"tag": "LabReport"
					}
				]
			}
			`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}
		if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}
		if fw, err = w.CreateFormFile("file2", "healthrecord-2.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png2)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}
		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		_, err = s.PutHealthRecord(
			context.Background(),
			acctId,
			patientId,
			"mockRecordId",
			multipartReader,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("got error when expected none: %s", err)
		}
	})
}

func TestPostHealthRecords(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)
	var err error
	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}
	// mock hlth rec svc request
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       ioutil.NopCloser(strings.NewReader(`{uploadSessionToken: ""}`)),
		}

		if req.Method == "POST" && req.URL.String() == "https://"+hlthRecSvcUser.URL+"/v1/records" {
			// validate authorization
			token := req.Header.Get("Authorization")
			if token != apiKey {
				t.Errorf("bad authorization: expected %s, got %s", apiKey, token)
			}
			err := req.ParseMultipartForm(3 * 1024 * 1024 * 1024)
			if err != nil {
				t.Fatalf("could not parse request to hlth rec svc: %q", err)
			}
			metadataStr := req.FormValue("recordMetadata")
			if metadataStr == "" {
				t.Error("no metadata in request to hlth rec svc")
			}

			body := ioutil.NopCloser(strings.NewReader(`
			[
				{
					"id": "0d066a9f-119e-42dd-8f51-c8b8ba38d1b2",
					"lastModified": "",
					"record": {
						"name": "my health record file",
						"source": {
							"type": "upload",
							"description": "Dr. T"
						},
						"recordDate": "2021-05-27T00:00:00+00:00",
						"type": 11,
						"createdDate": "2021-05-27T14:07:11+00:00",
						"filenames": ["record.fmt"],
						"tag": "record"
					}
				},
				{
					"id": "c31293dd-9555-4d17-b37d-f80f8c4bfc80",
					"lastModified": "",
					"record": {
						"name": "my HR doc",
						"source": {
							"type": "upload",
							"description": "Dr.K"
						},
						"recordDate": "2020-04-04T00:00:00+00:00",
						"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
						"createdDate": "2021-05-26T20:53:06+00:00",
						"filenames": ["doc.doc"],
						"tag": "doc"
					}
				}
			]
			`))
			hlthRecSvcResp.Body = body
		} else {
			t.Errorf("unexpected request to hlth rec svc: %s %s", req.Method, req.URL.String())
		}

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)

	// set up mock acctsvc
	acctId := "acct_id"
	patientId := "patient_id"
	acctsvc := &accountservice.AcctSvcMock{
		GetPatientsReturn: []accountservice.Patient{{PatientId: patientId}},
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          acctsvc,
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("invalid request body", func(t *testing.T) {
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				patientId,
			)
		})
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `This is not JSON`
		var fw io.Writer

		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}
		if fw, err = w.CreateFormFile("someotherfile", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		if _, err = io.WriteString(fw, "test file contents"); err != nil {
			t.Fatalf("failed to write test multipart file: %q", err)
		}
		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		// create upload token
		response, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		val, ok := response.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			multipartReader,
			"false",
		)
		if err == nil {
			t.Error("expected error but got none")
		}
		expected := errors.New(errormsgs.ERR_INVALID_REQ_BODY)
		if err.Error() != expected.Error() {
			t.Errorf("expected error to be %s but it was %s", expected, err)
		}
	})

	t.Run("good request", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()
		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				patientId,
			)
		})

		// create upload token
		response, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		val, ok := response.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			generateGoodReqBody(t, coreapi.VACCINATION_CODE),
			"false",
		)
		if err != nil {
			t.Errorf("got error when expected none: %s", err)
		}
	})

	t.Run("good request with multiple images", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png.Close()
		png2, err := os.OpenFile("../../apptest/gotests/assets/test-image-2.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		defer png2.Close()

		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				patientId,
			)
		})

		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		records := `
		{
			"records":[
				{
					"name": "my lab report",
					"filenames":["file1", "file2"],
					"source":{
						"type":"upload",
						"description":"Dr.K"
					},
					"recorddate":"********",
					"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
					"tag": "LabReport"
				}
			]
		}
		`
		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}
		if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}
		if fw, err = w.CreateFormFile("file2", "healthrecord-2.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png2)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}
		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		// create upload token
		response, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		val, ok := response.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			multipartReader,
			"false",
		)
		if err != nil {
			t.Errorf("got error when expected none: %s", err)
		}
	})

	t.Run("bad request with more than 50 images", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}

		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				patientId,
			)
		})

		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// Records with 51 filenames
		records := `
		{
			"records":[
				{
					"filenames":["../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png", "../../apptest/gotests/assets/test-image.png"],
					"source":{
						"type":"upload",
						"description":"Dr.K"
					},
					"recorddate":"********",
					"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
					"tag": "LabReport"
				}
			]
		}
		`

		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}

		for i := 0; i < 51; i++ {
			if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
				t.Fatalf("failed to create test multipart file: %s", err)
			}
			_, err = io.Copy(fw, png)
			if err != nil {
				t.Fatalf("failed to write test multipart file: %s", err)
			}
		}

		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		response, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		val, ok := response.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			multipartReader,
			"false",
		)
		expected := errors.New(errormsgs.ERR_INVALID_REQ_BODY)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request with too large an image", func(t *testing.T) {
		png, err := os.OpenFile("../../apptest/gotests/assets/test-image-3.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}

		t.Cleanup(func() {
			db.Exec(
				"DELETE FROM hr_v2_upload_sessions WHERE account_id=? AND patient_id=?",
				acctId,
				patientId,
			)
		})

		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// Records with 51 filenames
		records := `
		{
			"records":[
				{
					"filenames":["../../apptest/gotests/assets/test-image-3.png"],
					"source":{
						"type":"upload",
						"description":"Dr.K"
					},
					"recorddate":"********",
					"typeCode": ` + strconv.Itoa(int(coreapi.LAB_RESULT_CODE)) + `,
					"tag": "LabReport"
				}
			]
		}
		`

		if !json.Valid([]byte(records)) {
			t.Fatal("bad json in test data")
		}
		var fw io.Writer
		if fw, err = w.CreateFormField("records"); err != nil {
			t.Fatalf("failed to create test multipart record field: %s", err)
		}
		if _, err = io.WriteString(fw, records); err != nil {
			t.Fatalf("failed to write test multipart record field: %s", err)
		}

		if fw, err = w.CreateFormFile("file1", "healthrecord.fmt"); err != nil {
			t.Fatalf("failed to create test multipart file: %s", err)
		}
		_, err = io.Copy(fw, png)
		if err != nil {
			t.Fatalf("failed to write test multipart file: %s", err)
		}

		w.Close()
		multipartReader := multipart.NewReader(&b, w.Boundary())

		response, err := s.PostHealthRecordsUpload(
			context.Background(),
			acctId,
			patientId,
			"Unlimited",
			"false",
		)
		val, ok := response.(coreapi.HRUploadSession)
		if !ok {
			t.Errorf("expected struct HRUploadSession but got %q", val)
		}

		_, _, sessionId, err := auth.DecodeHealthRecordsUploadToken(
			"Bearer " + val.UploadSessionToken,
		)
		if err != nil {
			t.Errorf("expected none but got error")
		}

		_, err = s.PostHealthRecords(
			context.Background(),
			acctId,
			patientId,
			sessionId,
			multipartReader,
			"false",
		)
		expected := errors.New(errormsgs.ERR_FILE_SIZE)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})
}

func TestGetHealthRecordThumbnailById(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)

	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}

	// mock hlth rec svc request
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		// validate authorization
		token := req.Header.Get("Authorization")
		if token != apiKey {
			t.Fatalf("bad authorization: expected %s, got %s", apiKey, token)
		}

		if strings.HasSuffix(req.URL.Path, "/records/list") {
			b := ioutil.NopCloser(strings.NewReader(`
					[
						"fake-fhir-id",
						"c31293dd-9555-4d17-b37d-f80f8c4bfc80",
						"0d066a9f-119e-42dd-8f51-c8b8ba38d1b2"
					]
					`))

			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       b,
			}, nil
		}

		png, err := os.OpenFile("../../apptest/gotests/assets/test-image.png", os.O_RDONLY, 0o600)
		if err != nil {
			t.Fatalf("test setup failed: could not open png to send request: %v", err)
		}
		body := ioutil.NopCloser(png)

		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       body,
		}

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)

	// set up mock acctsvc
	acctId := "acct_id"
	patientId := "patient_id"
	acctsvc := &accountservice.AcctSvcMock{
		GetPatientsReturn: []accountservice.Patient{{PatientId: patientId}},
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          acctsvc,
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("bad request: patient does not own record", func(t *testing.T) {
		_, err := s.GetHealthRecordThumbnailById(
			context.Background(),
			"some-other-fake-fhir-id",
			acctId,
			patientId,
			"",
		)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request: record not part of share", func(t *testing.T) {
		sid := "fake-share-id"
		fid := "fake-fhir-id"
		setupShare(t, db, "some-other-"+fid, sid)

		_, err := s.GetHealthRecordThumbnailById(
			context.Background(),
			fid,
			acctId,
			patientId,
			sid,
		)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request: profile does not belong to user", func(t *testing.T) {
		_, err := s.GetHealthRecordThumbnailById(
			context.Background(),
			"fake-fhir-id",
			acctId,
			"bad_patient_id",
			"",
		)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("good request: getting a share to view", func(t *testing.T) {
		sid := "fake-share-id"
		fid := "fake-fhir-id"
		setupShare(t, db, fid, sid)

		_, err := s.GetHealthRecordThumbnailById(
			context.Background(),
			fid,
			acctId,
			patientId,
			sid,
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
	})

	t.Run("good request: viewing your own health record", func(t *testing.T) {
		_, err := s.GetHealthRecordThumbnailById(
			context.Background(),
			"fake-fhir-id",
			acctId,
			patientId,
			"",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
	})
}

func TestGetHealthRecordById(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)

	db := testutils.SetupTestDB(t)

	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}

	// mock hlth rec svc request
	m := `
	{
			"id": "0d066a9f-119e-42dd-8f51-c8b8ba38d1b2",
			"lastModified": "2021-05-27T14:07:12.737+00:00",
			"record": {
				"source": {
					"type": "upload",
					"description": "Dr. T"
				},
				"recordDate": "2021-05-27T00:00:00+00:00",
				"typeCode": 11,
				"createdDate": "2021-05-27T14:07:11+00:00",
				"filenames": ["record.fmt"],
				"tag": "record"
			}
		}
	`
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		// validate authorization
		token := req.Header.Get("Authorization")
		if token != apiKey {
			t.Fatalf("bad authorization: expected %s, got %s", apiKey, token)
		}

		if strings.HasSuffix(req.URL.Path, "/records/list") {
			b := ioutil.NopCloser(strings.NewReader(`
			[
				"fake-fhir-id",
				"c31293dd-9555-4d17-b37d-f80f8c4bfc80",
				"0d066a9f-119e-42dd-8f51-c8b8ba38d1b2"
			]
			`))

			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       b,
			}, nil
		}

		zipFile, err := os.OpenFile(
			"../../apptest/gotests/assets/test-image.zip",
			os.O_RDONLY,
			0o600,
		)
		if err != nil {
			t.Fatalf("test setup failed: could not open zip to send request: %v", err)
		}
		body := ioutil.NopCloser(zipFile)

		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       body,
			Header:     make(http.Header, 1),
		}
		hlthRecSvcResp.Header.Set("PocketHealth-API-Result", m)

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	hlthRecSvcUser.SetUploadLimits(5, 15, 500, 10000)

	// set up mock acctsvc
	acctId := "acct_id"
	patientId := "patient_id"
	acctsvc := &accountservice.AcctSvcMock{
		GetPatientsReturn: []accountservice.Patient{{PatientId: patientId}},
	}

	s := V2HealthRecordsApiService{
		sqldb:            db,
		hlthRecSvcUser:   hlthRecSvcUser,
		acctSvc:          acctsvc,
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run("bad request: profile does not own record", func(t *testing.T) {
		_, _, err := s.GetHealthRecordById(
			context.Background(),
			"some-other-fake-fhir-id",
			acctId,
			patientId,
			"",
		)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request: record not part of share", func(t *testing.T) {
		sid := "fake-share-id"
		fid := "fake-fhir-id"
		setupShare(t, db, "some-other-"+fid, sid)

		_, _, err := s.GetHealthRecordById(context.Background(), fid, acctId, patientId, sid)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("bad request: profile does not belong to user", func(t *testing.T) {
		_, _, err := s.GetHealthRecordById(
			context.Background(),
			"fake-fhir-id",
			acctId,
			"fake_patinetid",
			"",
		)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)

		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("good request: getting a share to view", func(t *testing.T) {
		sid := "fake-share-id"
		fid := "fake-fhir-id"
		setupShare(t, db, fid, sid)

		_, metadata, err := s.GetHealthRecordById(
			context.Background(),
			fid,
			acctId,
			patientId,
			sid,
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}

		if metadata != m {
			t.Fatalf("expected returned metadata to be %v but was %v", m, metadata)
		}
	})

	t.Run("good request: viewing your own health record", func(t *testing.T) {
		_, metadata, err := s.GetHealthRecordById(
			context.Background(),
			"fake-fhir-id",
			acctId,
			patientId,
			"",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}

		if metadata != m {
			t.Fatalf("expected returned metadata to be %v but was %v", m, metadata)
		}
	})

	t.Run(
		"good request: profile does not own record, but is a physician that can view it after its shared",
		func(t *testing.T) {
			sid := "fake-phys-share-id"
			fid := "fake-phys-fhir-id"
			phid := strconv.FormatInt(time.Now().UnixNano(), 10)

			sid2 := "fake-phys-share-id2"
			fid2 := "fake-phys-fhir-id2"
			phid2 := "2_" + phid
			// no access to the share
			_, _, err := s.GetHealthRecordById(
				context.Background(),
				fid,
				phid,
				patientId,
				"",
			)
			expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)
			if err == nil {
				t.Errorf("expected error but got none")
			} else if err.Error() != expected.Error() {
				t.Errorf("expected error to be %q but it was %q", expected, err)
			}

			// share with physician
			setupPhysicianShare(t, db, fid, sid, phid)
			setupPhysicianShare(t, db, fid2, sid2, phid2)

			// now physician should have access
			_, metadata, err := s.GetHealthRecordById(
				context.Background(),
				fid,
				phid,
				patientId,
				"",
			)
			if err != nil {
				t.Fatalf("got error when expected none: %q", err.Error())
			}
			if metadata != m {
				t.Fatalf("expected returned metadata to be %v but was %v", m, metadata)
			}

			// other physicians won't have access
			_, metadata, err = s.GetHealthRecordById(
				context.Background(),
				fid,
				phid2,
				patientId,
				"",
			)
			expected = errors.New(errormsgs.ERR_NOT_AUTHORIZED)
			if err == nil {
				t.Errorf("expected error but got none")
			} else if err.Error() != expected.Error() {
				t.Errorf("expected error to be %q but it was %q", expected, err)
			}
		},
	)
}

func TestGetHealthRecordByType(t *testing.T) {
	apiKey, _ := secure.GenerateRandomString(32)
	testPatientId := "2aPdBynGaIA7meKPg3Ck6JU9MvY"
	testAccountId := "3aPdBynGaIA7meKPg3Ck6JU9MvY"
	db := testutils.SetupTestDB(t)

	hlthRecSvcUser := hrs.HlthRecSvcUser{
		URL:    "test.healthrecord.pocket.health",
		ApiKey: apiKey,
	}
	// mock hlth rec svc request
	doRequestMock := func(_ context.Context, _ *http.Client, req *http.Request) (*http.Response, error) {
		body := ioutil.NopCloser(strings.NewReader("[]"))
		hlthRecSvcResp := http.Response{
			StatusCode: http.StatusOK,
			Body:       body,
		}

		if req.Method == "GET" && req.URL.Host == strings.Split(hlthRecSvcUser.URL, "?")[0] {
			// validate authorization
			token := req.Header.Get("Authorization")
			if token != apiKey {
				t.Errorf("bad authorization: expected %s, got %s", apiKey, token)
			}
		} else {
			t.Errorf("unexpected request to hlth rec svc: %s %s", req.Method, req.URL.String())
		}

		return &hlthRecSvcResp, nil
	}

	hlthRecSvcUser.SetDoRequest(doRequestMock)
	// initialize service
	s := V2HealthRecordsApiService{
		sqldb:          db,
		hlthRecSvcUser: hlthRecSvcUser,
		acctSvc:        &accountservice.AcctSvcMock{},
	}

	t.Run("patient does not belong to account", func(t *testing.T) {
		// set up mock acctsvc
		s.acctSvc = &accountservice.AcctSvcMock{GetPatientsReturn: []accountservice.Patient{}}

		_, err := s.GetRecordsByType(context.Background(), testAccountId, testPatientId, "RiskAssessment", 1)
		expected := errors.New(errormsgs.ERR_NOT_AUTHORIZED)
		if err == nil {
			t.Errorf("expected error but got none")
		} else if err.Error() != expected.Error() {
			t.Errorf("expected error to be %q but it was %q", expected, err)
		}
	})

	t.Run("When patients belong to an account, a request is made to HRS", func(t *testing.T) {
		// set up mock acctsvc
		s.acctSvc = &accountservice.AcctSvcMock{
			GetPatientsReturn: []accountservice.Patient{{PatientId: testPatientId}},
		}

		records, err := s.GetRecordsByType(context.Background(), testAccountId, testPatientId, "RiskAssessment", 1)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		if len(records) != 0 {
			t.Error("Somehow theres not 0 records")
		}
	})
}

func mockIsAuthForFeature(token string, featureId uint64) (bool, error) {
	if featureId != uint64(planservice.HEALTH_RECORDS) {
		return false, errors.New("should only check HEALTH_RECORDS feature here")
	}
	if token == "Unlimited" {
		return true, nil
	} else {
		return false, nil
	}
}
