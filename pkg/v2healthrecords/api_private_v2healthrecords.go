package v2healthrecords

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type PrivateV2HealthRecordsApiController struct {
	service     coreapi.V2HealthRecordsApiServicer
	rateLimiter coreapi.RateLimiter
}

func NewPrivateV2HealthRecordsApiController(
	s coreapi.V2HealthRecordsApiServicer,
) coreapi.PrivateV2HealthrecordsApiRouter {
	return &PrivateV2HealthRecordsApiController{
		service: s,
	}
}

func (c *PrivateV2HealthRecordsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetHealthRecords",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{patientId}",
			HandlerFunc: c.GetHealthRecords,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 200},
		},
		{
			Name:        "PostHealthRecordsUpload",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{patientId}/upload",
			HandlerFunc: c.PostHealthRecordsUpload,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 25},
		},
		{
			Name:        "PutHealthRecords",
			Method:      strings.ToUpper("Put"),
			Pattern:     "/{patientId}/records/{recordId}",
			HandlerFunc: c.PutHealthRecord,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 10},
		},
		{
			Name:        "GetHealthRecordThumbnailById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{patientId}/records/{recordId}/thumbnail",
			HandlerFunc: c.GetHealthRecordThumbnailById,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 200},
		},
		{
			Name:        "GetHealthRecordById",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{patientId}/records/{recordId}",
			HandlerFunc: c.GetHealthRecordById,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 200},
		},
		{
			Name:        "DeleteHealthRecordById",
			Method:      strings.ToUpper("Delete"),
			Pattern:     "/{patientId}/records/{recordId}",
			HandlerFunc: c.DeleteHealthRecordById,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 10},
		},
		{
			Name:        "CreateMyChartIntegration",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{patientId}/integrations/mychart",
			HandlerFunc: c.CreateMyChartIntegration,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 10},
		},
		{
			Name:        "SearchMyChartOrgs",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/integrations/mychart/search",
			HandlerFunc: c.SearchMyChartOrgs,
		},
		{
			Name:        "GenerateGailRiskResult",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{patientId}/questionnaire/gail",
			HandlerFunc: c.GenerateGailRiskResult,
		},
		{
			Name:        "GetRecordsByType",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{patientId}/record/type/{recordType}",
			HandlerFunc: c.GetRecordsByType,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 200},
		},
	}
}

func (c *PrivateV2HealthRecordsApiController) GetPathPrefix() string {
	return "/v2/healthrecords"
}

func (c *PrivateV2HealthRecordsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all healthrecords paths require authentication
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func (c *PrivateV2HealthRecordsApiController) GetHealthRecords(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]

	res, err := c.service.GetHealthRecords(r.Context(), acctId, patientId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *PrivateV2HealthRecordsApiController) PostHealthRecordsUpload(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	covidVaccine := r.URL.Query().Get("covidVaccine")

	res, err := c.service.PostHealthRecordsUpload(
		r.Context(),
		acctId,
		patientId,
		token,
		covidVaccine,
	)

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == "user has no access to health records feature" {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusForbidden)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}

		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *PrivateV2HealthRecordsApiController) PutHealthRecord(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	recordId := params["recordId"]
	covidVaccine := r.URL.Query().Get("covidVaccine")

	reader, err := r.MultipartReader()
	if err != nil {
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	res, err := c.service.PutHealthRecord(
		r.Context(),
		acctId,
		patientId,
		recordId,
		reader,
		token,
		covidVaccine,
	)

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else if err.Error() == "user has no access to health records feature" {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusForbidden)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *PrivateV2HealthRecordsApiController) GetHealthRecordThumbnailById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil || (claims.AccountID == "" && claims.ShareID == "") {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	recordId := params["recordId"]

	res, err := c.service.GetHealthRecordThumbnailById(
		r.Context(),
		recordId,
		claims.AccountID,
		patientId,
		claims.ShareID,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	defer func() {
		err := res.Close()
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).
				WithError(err).
				Error("error closing HR thumbnail response body")
		}
	}()
	w.Header().Set("Content-Type", "image/png")
	_, err = io.Copy(w, res)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("error copying HR thumbnail response body")
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

func (c *PrivateV2HealthRecordsApiController) GetHealthRecordById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := r.Header.Get("Authorization")
	claims, err := auth.DecodeBearerToken(token)
	if err != nil || (claims.AccountID == "" && claims.ShareID == "") {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	recordId := params["recordId"]

	res, metadata, err := c.service.GetHealthRecordById(
		r.Context(),
		recordId,
		claims.AccountID,
		patientId,
		claims.ShareID,
	)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_FOUND, http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	defer func() {
		err := res.Close()
		if err != nil {
			logutils.DebugCtxLogger(r.Context()).
				WithError(err).
				Error("error closing HR response body")
		}
	}()

	w.Header().Set("Content-Type", "application/zip")
	w.Header().Set("PocketHealth-API-Result", metadata)
	_, err = io.Copy(w, res)
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("could not copy HRS record response")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *PrivateV2HealthRecordsApiController) DeleteHealthRecordById(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	recordId := params["recordId"]

	err = c.service.DeleteHealthRecordById(r.Context(), recordId, acctId, patientId)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
		} else if err.Error() == errmsg.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), http.StatusText(http.StatusOK), nil, w)
}

func (c *PrivateV2HealthRecordsApiController) CreateMyChartIntegration(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]

	mc := &coreapi.MyChartIntegrationRequest{}
	if err := json.NewDecoder(r.Body).Decode(&mc); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	err = c.service.CreateMyChartIntegration(r.Context(), acctId, patientId, mc)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), http.StatusText(http.StatusOK), nil, w)
}

func (c *PrivateV2HealthRecordsApiController) SearchMyChartOrgs(
	w http.ResponseWriter,
	r *http.Request,
) {
	queryParams := r.URL.Query()
	query := queryParams.Get("query")

	if query == "" {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
	}

	result, err := c.service.SearchMyChartOrgs(r.Context(), query)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *PrivateV2HealthRecordsApiController) GenerateGailRiskResult(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]

	gr := &coreapi.GailQuestionnairePatientResponses{}
	if err := json.NewDecoder(r.Body).Decode(&gr); err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	respBytes, err := c.service.GenerateGailRiskResult(r.Context(), acctId, patientId, gr)
	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
		} else if err.Error() == errmsg.ERR_UNPROCESSABLE_ENTITY {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnprocessableEntity),
				http.StatusUnprocessableEntity,
			)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	buf := bytes.NewBuffer(respBytes)
	_, err = io.Copy(w, buf)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
	w.WriteHeader(http.StatusOK)
}

func (c *PrivateV2HealthRecordsApiController) GetRecordsByType(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	queryParams := r.URL.Query()

	acctId, err := auth.DecodeAccountToken(r.Header.Get("Authorization"))
	if err != nil {
		httperror.ErrorWithLog(w, r, errmsg.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}
	patientId := params["patientId"]
	recordType := params["recordType"]

	limit := 1
	limitQuery := queryParams.Get("limit")
	if limitQuery != "" {
		limit, err = strconv.Atoi(limitQuery)
		if err != nil {
			httperror.ErrorWithLog(w, r, errmsg.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		}
	}
	records, err := c.service.GetRecordsByType(r.Context(), acctId, patientId, recordType, limit)

	if err != nil {
		if err.Error() == errmsg.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
		} else if err.Error() == errmsg.ERR_UNPROCESSABLE_ENTITY {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnprocessableEntity),
				http.StatusUnprocessableEntity,
			)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), records, nil, w)
	w.WriteHeader(http.StatusOK)
}
