package v2healthrecords

import (
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type V2UploadHealthRecordsApiController struct {
	service     coreapi.V2HealthRecordsApiServicer
	rateLimiter coreapi.RateLimiter
}

func NewV2UploadHealthRecordsApiController(
	s coreapi.V2HealthRecordsApiServicer,
) coreapi.V2UploadHealthRecordsApiRouter {
	return &V2UploadHealthRecordsApiController{
		service: s,
		rateLimiter: ratelimit.NewRateLimiter(
			5,  // period in minutes
			25, // max hits per period
		),
	}
}

func (c *V2UploadHealthRecordsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostVerifyToken",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/verify",
			HandlerFunc: c.PostVerifyToken,
		},
		{
			Name:        "PostHealthRecords",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{patientId}",
			HandlerFunc: c.PostHealthRecords,
		},
	}
}

func (c *V2UploadHealthRecordsApiController) GetPathPrefix() string {
	return "/v2/healthrecords"
}

// special auth function only to be used in this controller to authenticate upload token
func (c *V2UploadHealthRecordsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{c.service.ValidateAuth, c.rateLimiter}
}

func (c *V2UploadHealthRecordsApiController) PostVerifyToken(
	w http.ResponseWriter,
	r *http.Request,
) {
	acctId, patientId, sessionId, err := auth.DecodeHealthRecordsUploadToken(
		r.Header.Get("Authorization"),
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	res, err := c.service.PostVerifyToken(r.Context(), acctId, patientId, sessionId)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), res, nil, w)
}

func (c *V2UploadHealthRecordsApiController) PostHealthRecords(
	w http.ResponseWriter,
	r *http.Request,
) {
	acctId, tokenPatientId, sessionId, err := auth.DecodeHealthRecordsUploadToken(
		r.Header.Get("Authorization"),
	)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	}
	covidVaccine := r.URL.Query().Get("covidVaccine")

	patientId := mux.Vars(r)["patientId"]
	// compare patient id in params and upload token
	if patientId != tokenPatientId {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		return
	}

	reader, err := r.MultipartReader()
	if err != nil {
		logutils.DebugCtxLogger(r.Context()).
			WithError(err).
			Error("failed to read multipart form from req")
		httperror.ErrorWithLog(
			w,
			r,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
		return
	}

	result, err := c.service.PostHealthRecords(
		r.Context(),
		acctId,
		patientId,
		sessionId,
		reader,
		covidVaccine,
	)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_AUTHORIZED {
			httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_AUTHORIZED, http.StatusUnauthorized)
		} else if err.Error() == errormsgs.ERR_INVALID_REQ_BODY {
			httperror.ErrorWithLog(w, r, errormsgs.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		} else {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
