package checkout

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v8/pkg/amplitude_util"
)

type PublicCheckoutApiController struct {
	service         coreapi.CheckoutApiServicer
	ampCookieHeader string
	lt              lockouttracker.LockoutTracker
}

func NewPublicCheckoutApiController(
	s coreapi.CheckoutApiServicer,
	ampCookieHeader string,
	lockout lockouttracker.LockoutTracker,
) coreapi.PublicCheckoutApiRouter {
	return &PublicCheckoutApiController{service: s, lt: lockout, ampCookieHeader: ampCookieHeader}
}

func (c *PublicCheckoutApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "PostActivateStudies",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{account_id}/studies/activate",
			HandlerFunc: c.PostActivateStudies,
		},
		{
			Name:        "GetMetadata",
			Method:      http.MethodGet,
			Pattern:     "/{providerId}/metadata",
			HandlerFunc: c.GetMetadata,
		},
	}
}

func (c *PublicCheckoutApiController) GetPathPrefix() string {
	return "/v1/checkout"
}

func (c *PublicCheckoutApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{}
}

func (c *PublicCheckoutApiController) PostActivateStudies(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	lg := logutils.CtxLogger(ctx)

	accountID := mux.Vars(r)["account_id"]
	if accountID == "" {
		lg.Error("accountID cannot be empty")
		httperror.ErrorWithLog(w, r, errmsg.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		return
	}

	clientAddr := strings.Split(r.RemoteAddr, ":")
	IP := clientAddr[0]

	if c.lt.IsLocked(r.Context(), accountID) || c.lt.IsLocked(r.Context(), IP) {
		httperror.ErrorWithLog(w, r, errmsg.ERR_TOO_MANY_ATTEMPTS, http.StatusForbidden)
		return
	}

	deviceID := amplitude_util.GetAmplitudeDeviceID(r, c.ampCookieHeader)

	var request coreapi.ActivationKey
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		lg.WithError(err).Info("error reading request body")
		httperror.ErrorWithLog(w, r, errmsg.ERR_JSON_UNMARSHAL, http.StatusBadRequest)
		return
	}

	if request.DateOfBirth == "" {
		lg.Error("date of birth cannot be empty")
		httperror.ErrorWithLog(w, r, errmsg.ERR_INVALID_REQ_BODY, http.StatusBadRequest)
		return
	}

	result, err := c.service.PostActivateStudies(ctx, accountID, request, IP, deviceID)

	if err != nil {
		var status int
		if err.Error() == errmsg.ERR_NO_RECORDS {
			status = http.StatusNotAcceptable
			c.lt.IncrementAttempts(r.Context(), accountID)
			c.lt.IncrementAttempts(r.Context(), IP)
		} else {
			status = http.StatusInternalServerError
		}

		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}
	c.lt.Reset(r.Context(), accountID)
	c.lt.Reset(r.Context(), IP)
	status := http.StatusOK
	coreapi.EncodeJSONResponse(ctx, result, &status, w)
}

func (c *PublicCheckoutApiController) GetMetadata(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	providerId, err := strconv.Atoi(params["providerId"])
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusBadRequest)
		return
	}

	lg := logutils.DebugCtxLogger(r.Context()).WithField("provider_id", providerId)
	result, err := c.service.GetMetadata(r.Context(), int64(providerId))
	if err != nil {
		lg.WithError(err).Error("could not get checkout metadata")
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
