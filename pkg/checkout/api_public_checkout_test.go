package checkout

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	mockcoreapi "gitlab.com/pockethealth/coreapi/generated/mocks/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/lockouttracker"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

type ControllerTestData struct {
	name                 string
	method               string
	url                  string
	body                 string // body as json string
	expectedStatus       int
	skipAuthorizedHeader bool
	serviceSetupFunc     func(service *mockcoreapi.MockCheckoutApiServicer)
}

func TestPostActivateStudies(t *testing.T) {
	validBody := coreapi.ActivationKey{
		DateOfBirth: "1999/01/01",
	}
	validBodyBytes, _ := json.Marshal(validBody)
	validBodyString := string(validBodyBytes)

	invalidBody := coreapi.ActivationKey{}
	invalidBodyBytes, _ := json.Marshal(invalidBody)
	invalidBodyString := string(invalidBodyBytes)
	testcases := []ControllerTestData{
		{
			name:           "PostActivateStudies - bad request - missing accountID",
			method:         http.MethodPost,
			url:            "/v1/checkout/ /studies/activate",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "PostActivateStudies - bad request - missing request payload",
			method:         http.MethodPost,
			url:            "/v1/checkout/mock-account-id/studies/activate",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "PostActivateStudies - bad request - missing dob in payload",
			method:         http.MethodPost,
			url:            "/v1/checkout/mock-account-id/studies/activate",
			body:           invalidBodyString,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "PostActivateStudies - valid request",
			method:         http.MethodPost,
			url:            "/v1/checkout/mock-account-id/studies/activate",
			body:           validBodyString,
			expectedStatus: http.StatusOK,
			serviceSetupFunc: func(service *mockcoreapi.MockCheckoutApiServicer) {
				service.EXPECT().
					PostActivateStudies(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(coreapi.ActivateStudyResponse{}, nil)
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestGetMetadata(t *testing.T) {
	testcases := []ControllerTestData{
		{
			name:           "GetMetadata - valid request",
			method:         http.MethodGet,
			url:            "/v1/checkout/48/metadata",
			expectedStatus: http.StatusOK,
			serviceSetupFunc: func(service *mockcoreapi.MockCheckoutApiServicer) {
				service.EXPECT().
					GetMetadata(mock.Anything, mock.Anything).
					Return(&models.CheckoutMetadata{}, nil)
			},
		},
		{
			name:           "GetMetadata - bad provider id",
			method:         http.MethodGet,
			url:            "/v1/checkout/bad-provider-id/metadata",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "GetMetadata - fail to retrieve metadata",
			method:         http.MethodGet,
			url:            "/v1/checkout/48/metadata",
			expectedStatus: http.StatusInternalServerError,
			serviceSetupFunc: func(service *mockcoreapi.MockCheckoutApiServicer) {
				service.EXPECT().
					GetMetadata(mock.Anything, mock.Anything).
					Return(nil, errors.New(errormsgs.ERR_NOT_FOUND))
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func setupController(t *testing.T) (coreapi.Router, *mockcoreapi.MockCheckoutApiServicer) {
	service := mockcoreapi.NewMockCheckoutApiServicer(t)
	lt := lockouttracker.NewMockLockoutTracker()
	controller := NewPublicCheckoutApiController(service, "", lt)

	return controller, service
}

func runControllerTest(t *testing.T, data ControllerTestData) {
	// init controller and services
	controller, service := setupController(t)

	if data.serviceSetupFunc != nil {
		data.serviceSetupFunc(service)
	}

	// set up controller
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// create request body
	buf := bytes.NewBuffer([]byte(data.body))

	// create request
	request, err := http.NewRequest(data.method, data.url, buf)
	if err != nil {
		t.Fatal(err)
	}

	// set up response recorder
	rr := httptest.NewRecorder()

	// process request
	router.ServeHTTP(rr, request)

	// check status code
	assert.Equal(t, data.expectedStatus, rr.Code)
}
