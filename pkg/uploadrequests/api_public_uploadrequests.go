package uploadrequests

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/go-faster/jx"
	"github.com/gorilla/mux"
	"github.com/oapi-codegen/nullable"
	"github.com/oapi-codegen/runtime/types"
	ogenjson "github.com/ogen-go/ogen/json"
	"github.com/samber/lo"

	"gitlab.com/pockethealth/coreapi/generated/api"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/util/typeconverter"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/recordretrievalservice"
)

const (
	codeInternalServerError = "INTERNAL_SERVER_ERROR"
	codeBadRequest          = "BAD_REQUEST_BODY"
	tokenCookieName         = "session_token"
)

type contextKey int

const (
	contextKeyAuthToken contextKey = iota
)

type PublicUploadRequestsAPIController struct {
	RecordRetrievalService recordretrievalservice.ClientWithResponsesInterface
	AllowCrossSite         bool
}

func (c *PublicUploadRequestsAPIController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "CreateUploadRequest",
			Method:      http.MethodPost,
			Pattern:     "",
			HandlerFunc: postHandler(c.createUploadRequest),
		},
		{
			Name:        "GetUploadRequest",
			Method:      http.MethodGet,
			Pattern:     "",
			HandlerFunc: noInputHandler(c.getUploadRequest),
		},
		{
			Name:        "AuthenticateUploadRequest",
			Method:      http.MethodPost,
			Pattern:     "/authenticate",
			HandlerFunc: postHandler(c.authenticateUploadRequest),
		},
		{
			Name:        "DeclineUploadRequest",
			Method:      http.MethodPost,
			Pattern:     "/decline",
			HandlerFunc: postHandler(c.declineUploadRequest),
		},
		{
			Name:        "SubmitUploadRequest",
			Method:      http.MethodPost,
			Pattern:     "/submit",
			HandlerFunc: noInputHandler(c.submitUploadRequest),
		},
		{
			Name:        "UploadInstance",
			Method:      http.MethodPost,
			Pattern:     "/instances",
			HandlerFunc: c.uploadInstanceHandler,
		},
		{
			Name:        "ListStudies",
			Method:      http.MethodGet,
			Pattern:     "/studies",
			HandlerFunc: noInputHandler(c.listStudies),
		},
		{
			Name:        "UpdateStudy",
			Method:      http.MethodPatch,
			Pattern:     "/studies/{id}",
			HandlerFunc: c.updateStudyHandler,
		},
		{
			Name:        "DeleteStudy",
			Method:      http.MethodDelete,
			Pattern:     "/studies/{id}",
			HandlerFunc: c.deleteStudyHandler,
		},
		{
			Name:        "GetProviderDetails",
			Method:      http.MethodGet,
			Pattern:     "/providers/{subdomain}",
			HandlerFunc: c.getProviderDetailsHandler,
		},
	}
}

func (c *PublicUploadRequestsAPIController) GetPathPrefix() string {
	return "/v1/upload-request"
}

func (s *PublicUploadRequestsAPIController) GetMiddleware() []func(http.Handler) http.Handler {
	return []func(http.Handler) http.Handler{
		authCapturingMiddleware,
	}
}

func (c *PublicUploadRequestsAPIController) cookie(value string) *http.Cookie {
	sameSiteMode := http.SameSiteDefaultMode
	if c.AllowCrossSite {
		sameSiteMode = http.SameSiteNoneMode
	}

	return &http.Cookie{
		Name:     tokenCookieName,
		Path:     "/v1/upload-request",
		Value:    value,
		Secure:   true,
		HttpOnly: true,
		SameSite: sameSiteMode,
	}
}

func (c *PublicUploadRequestsAPIController) setCookie(w http.ResponseWriter, token string) {
	cookie := c.cookie(token)
	http.SetCookie(w, cookie)
}

func (c *PublicUploadRequestsAPIController) clearCookie(w http.ResponseWriter) {
	cookie := c.cookie("goodbye")
	cookie.Expires = time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)
	http.SetCookie(w, cookie)
}

// returns the contents of the auth token if it's on the context, or ""
func authToken(ctx context.Context) string {
	token, _ := ctx.Value(contextKeyAuthToken).(string)
	return token
}

func authCapturingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		cookie, err := r.Cookie(tokenCookieName)
		if err == nil {
			ctx = context.WithValue(ctx, contextKeyAuthToken, cookie.Value)
		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func withAuthHeader() recordretrievalservice.RequestEditorFn {
	return func(ctx context.Context, req *http.Request) error {
		if token := authToken(ctx); token != "" {
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
		}

		return nil
	}
}

func writeBadJSON(ctx context.Context, w http.ResponseWriter, err error) {
	resp := api.ErrorResponse{
		Code:    codeBadRequest,
		Message: fmt.Sprintf("failed to parse JSON body: %s", err.Error()),
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusBadRequest)
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		logutils.Errorx(ctx, "failed to write response", err)
	}
}

func encodeResponse(ctx context.Context, w http.ResponseWriter, status int, resp ogenjson.Marshaler) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	e := &jx.Encoder{}
	resp.Encode(e)
	if _, err := e.WriteTo(w); err != nil {
		logutils.Errorx(ctx, "failed to write response", err)
	}
}

type errorResponseWithCode struct {
	status int
	resp   *api.ErrorResponse
}

// errorResponseFromBody parses an error response from the body of a recordretrievalservice call,
// and reformats it as an api.ErrorResponse.
// This is helpful because every non-2XX response from recordretrievalservice should have the same
// error body, so we don't have to special case every error.
// It's a little wasteful since we'll end up parsing the body twice, but saves a lot of boilerplate
// in the handler code.
func errorResponseFromBody(ctx context.Context, status int, body []byte) *errorResponseWithCode {
	resp := &recordretrievalservice.ErrorResponse{}
	if err := json.Unmarshal(body, resp); err != nil {
		logutils.Errorx(ctx, "error response from recordretrievalservice could not be decoded as ErrorResponse", "response_body", string(body), err)
		return &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}

	return &errorResponseWithCode{
		status: status,
		resp: &api.ErrorResponse{
			Message: resp.Message,
			Code:    resp.Code,
		},
	}
}

func postHandler[I any, O ogenjson.Marshaler](handler func(context.Context, *I, http.ResponseWriter) (O, *errorResponseWithCode)) func(w http.ResponseWriter, r *http.Request) {
	var _ nullable.Nullable[string] // TODO: remove when something uses nullable for real, for some reason go is not picking this up as required if it's not imported here
	return func(w http.ResponseWriter, r *http.Request) {
		var req I
		buf, err := io.ReadAll(r.Body)
		if err != nil {
			logutils.Errorx(r.Context(), "failed to read request body", err)
			writeBadJSON(r.Context(), w, err)
			return
		}

		if err := json.Unmarshal(buf, &req); err != nil {
			logutils.Errorx(r.Context(), "failed to parse request body", err)
			writeBadJSON(r.Context(), w, err)
			return
		}

		resp, apiErr := handler(r.Context(), &req, w)
		if apiErr != nil {
			encodeResponse(r.Context(), w, apiErr.status, apiErr.resp)
			return
		}

		encodeResponse(r.Context(), w, http.StatusOK, resp)
	}
}

func noInputHandler[O ogenjson.Marshaler](handler func(context.Context, http.ResponseWriter) (O, *errorResponseWithCode)) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		resp, apiErr := handler(r.Context(), w)
		if apiErr != nil {
			encodeResponse(r.Context(), w, apiErr.status, apiErr.resp)
			return
		}

		encodeResponse(r.Context(), w, http.StatusOK, resp)
	}
}

func (c *PublicUploadRequestsAPIController) uploadInstanceHandler(w http.ResponseWriter, r *http.Request) {
	resp, apiErr := c.uploadInstance(r.Context(), r.Header.Get("Content-Type"), r.Body)
	if apiErr != nil {
		encodeResponse(r.Context(), w, apiErr.status, apiErr.resp)
		return
	}

	encodeResponse(r.Context(), w, http.StatusOK, resp)
}

func (c *PublicUploadRequestsAPIController) uploadInstance(ctx context.Context, contentType string, body io.Reader) (*api.UploadRequestUploadInstanceV1OK, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderUploadInstanceV1WithBodyWithResponse(ctx, contentType, body, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	if resp.StatusCode() == http.StatusOK {
		return &api.UploadRequestUploadInstanceV1OK{}, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) declineUploadRequest(ctx context.Context, req *api.DeclineUploadRequestBody, _ http.ResponseWriter) (*api.DeclineUploadRequestV1OK, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderDeclineUploadRequestV1WithResponse(ctx, recordretrievalservice.UploaderDeclineUploadRequestV1JSONRequestBody{
		DeclineReason: req.DeclineReason,
		SecurityCode:  req.SecurityCode,
		BirthDate:     types.Date{Time: req.BirthDate},
	}, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	if resp.StatusCode() == http.StatusOK {
		return &api.DeclineUploadRequestV1OK{}, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) getUploadRequest(ctx context.Context, w http.ResponseWriter) (*api.GetUploadRequestResponse, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderGetUploadRequestV1WithResponse(ctx, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	if resp.StatusCode() == http.StatusOK {
		apiResp := &api.GetUploadRequestResponse{
			ProviderDestinationID:    typeconverter.ToOptInt64(resp.JSON200.UploadRequest.DestinationID),
			ProviderDestinationSetBy: toOptSetBy(resp.JSON200.UploadRequest.DestinationSetBy),
			RequestType:              api.UploadRequestType(resp.JSON200.UploadRequest.RequestType),
			State:                    api.UploadRequestState(resp.JSON200.UploadRequest.State),
			ReferenceCode:            resp.JSON200.UploadRequest.ReferenceCode,
			PatientName:              resp.JSON200.UploadRequest.Patient.Name,
			PatientBirthDate:         resp.JSON200.UploadRequest.Patient.BirthDate.Time,
		}

		if uploader := resp.JSON200.UploadRequest.Uploader; uploader != nil {
			apiResp.UploaderType = api.NewOptUploadRequestUploaderType(api.UploadRequestUploaderType(uploader.UploaderType))
			apiResp.UploaderContactName = api.NewOptString(uploader.ContactName)
			apiResp.UploaderContactInformation = api.NewOptString(uploader.ContactInformation)
			apiResp.UploaderContactMethod = api.NewOptUploadRequestUploaderContactMethod(api.UploadRequestUploaderContactMethod(uploader.ContactMethod))
		}

		return apiResp, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func toOptSetBy(sb *recordretrievalservice.UploadRequestDestinationSetBy) api.OptUploadRequestDestinationSetBy {
	if sb == nil {
		return api.OptUploadRequestDestinationSetBy{}
	}

	return api.NewOptUploadRequestDestinationSetBy(api.UploadRequestDestinationSetBy(*sb))
}

func (c *PublicUploadRequestsAPIController) submitUploadRequest(ctx context.Context, w http.ResponseWriter) (*api.SubmitUploadRequestV1OK, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderSubmitUploadRequestV1WithResponse(ctx, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	if resp.StatusCode() == http.StatusOK {
		c.clearCookie(w)

		return &api.SubmitUploadRequestV1OK{}, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) authenticateUploadRequest(ctx context.Context, req *api.AuthenticateUploadRequestBody, w http.ResponseWriter) (*api.AuthenticateUploadRequestV1OK, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderAuthenticateV1WithResponse(ctx, recordretrievalservice.UploaderAuthenticateV1JSONRequestBody{
		SecurityCode: req.SecurityCode,
		BirthDate:    types.Date{Time: req.BirthDate},
	})
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}

	if resp.StatusCode() == http.StatusOK {
		c.setCookie(w, resp.JSON200.Token)

		return &api.AuthenticateUploadRequestV1OK{}, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) createUploadRequest(ctx context.Context, req *api.CreateUploadRequestBody, w http.ResponseWriter) (*api.CreateUploadRequestV1OK, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderCreateUploadRequestV1WithResponse(ctx, recordretrievalservice.UploaderCreateUploadRequestV1JSONRequestBody{
		ProviderID:            req.ProviderID,
		ProviderDestinationID: req.ProviderDestinationID,
		PatientName:           req.PatientName,
		PatientBirthDate:      types.Date{Time: req.PatientBirthDate},
		UploaderType:          recordretrievalservice.UploadRequestUploaderType(req.UploaderType),
		ContactInformation:    req.ContactInformation,
		ContactMethod:         recordretrievalservice.UploadRequestUploaderContactMethod(req.ContactMethod),
		ContactName:           req.ContactName,
	})
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}

	if resp.StatusCode() == http.StatusOK {
		c.setCookie(w, resp.JSON200.Token)

		return &api.CreateUploadRequestV1OK{}, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) listStudies(ctx context.Context, w http.ResponseWriter) (*api.UploadRequestStudiesResponse, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderListStudiesV1WithResponse(ctx, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	if resp.StatusCode() == http.StatusOK {
		apiResp := &api.UploadRequestStudiesResponse{}

		// Convert the list of studies to this api's format.
		if studies := resp.JSON200.Studies; studies != nil {
			for _, rrsStudy := range studies {
				study, err := recordRetrievalToCoreAPIStudy(rrsStudy)
				if err != nil {
					logutils.Errorx(ctx, "failure converting study to api format", err)
					return nil, &errorResponseWithCode{
						status: http.StatusInternalServerError,
						resp: &api.ErrorResponse{
							Message: "an internal server error occurred",
							Code:    codeInternalServerError,
						},
					}
				}

				apiResp.Studies = append(apiResp.Studies, *study)
			}
		}

		return apiResp, nil
	}

	return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
}

func (c *PublicUploadRequestsAPIController) updateStudyHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var req api.SubmitStudyNotesBody
	buf, err := io.ReadAll(r.Body)
	if err != nil {
		logutils.Errorx(ctx, "failed to read request body", err)
		writeBadJSON(ctx, w, err)
		return
	}

	if err := json.Unmarshal(buf, &req); err != nil {
		logutils.Errorx(ctx, "failed to parse request body", err)
		writeBadJSON(ctx, w, err)
		return
	}

	studyID := mux.Vars(r)["id"]

	resp, apiErr := c.updateStudy(ctx, studyID, &req)
	if apiErr != nil {
		encodeResponse(ctx, w, apiErr.status, apiErr.resp)
		return
	}
	encodeResponse(ctx, w, http.StatusOK, resp)
}

func (c *PublicUploadRequestsAPIController) updateStudy(ctx context.Context, studyID string, req *api.SubmitStudyNotesBody) (*api.UploadRequestStudyResponse, *errorResponseWithCode) {
	uploaderNotes := nullable.NewNullNullable[string]()
	if !req.UploaderNotes.IsNull() {
		uploaderNotes.Set(req.UploaderNotes.Value)
	}

	resp, err := c.RecordRetrievalService.UploaderUpdateStudyV1WithResponse(
		ctx, studyID,
		recordretrievalservice.UploaderUpdateStudyV1JSONRequestBody{
			UploaderNotes: uploaderNotes,
		},
		withAuthHeader(),
	)
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
	}

	// Convert study to API format
	study, err := recordRetrievalToCoreAPIStudy(resp.JSON200.Study)
	if err != nil {
		logutils.Errorx(ctx, "failure converting study to api format", err)
		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}
	return &api.UploadRequestStudyResponse{
		Study: *study,
	}, nil
}

func (c *PublicUploadRequestsAPIController) deleteStudyHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	studyID := mux.Vars(r)["id"]

	resp, err := c.RecordRetrievalService.UploaderDeleteStudyV1WithResponse(ctx, studyID, withAuthHeader())
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		encodeResponse(ctx, w, http.StatusInternalServerError, &api.ErrorResponse{
			Message: "an internal server error occurred",
			Code:    codeInternalServerError,
		})
		return
	}

	if resp.StatusCode() != http.StatusNoContent {
		encodeResponse(ctx, w, resp.StatusCode(), errorResponseFromBody(ctx, resp.StatusCode(), resp.Body).resp)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (c *PublicUploadRequestsAPIController) getProviderDetailsHandler(w http.ResponseWriter, r *http.Request) {
	resp, apiErr := c.getProviderDetails(r.Context(), &api.GetProviderDetailsV1Params{
		Subdomain: mux.Vars(r)["subdomain"],
	})
	if apiErr != nil {
		encodeResponse(r.Context(), w, apiErr.status, apiErr.resp)
		return
	}

	encodeResponse(r.Context(), w, http.StatusOK, resp)
}

func (c *PublicUploadRequestsAPIController) getProviderDetails(ctx context.Context, req *api.GetProviderDetailsV1Params) (*api.GetProviderDetailsResponse, *errorResponseWithCode) {
	resp, err := c.RecordRetrievalService.UploaderGetProviderDetailsV1WithResponse(ctx, req.Subdomain)
	if err != nil {
		logutils.Errorx(ctx, "failure calling record retrieval service", err)

		return nil, &errorResponseWithCode{
			status: http.StatusInternalServerError,
			resp: &api.ErrorResponse{
				Message: "an internal server error occurred",
				Code:    codeInternalServerError,
			},
		}
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, errorResponseFromBody(ctx, resp.StatusCode(), resp.Body)
	}

	return &api.GetProviderDetailsResponse{
		ID:   resp.JSON200.ID,
		Name: resp.JSON200.Name,
		Destinations: lo.Map(resp.JSON200.Destinations, func(s recordretrievalservice.UploadRequestDestinationSummary, _ int) api.ProviderDestination {
			return api.ProviderDestination{
				ID:    s.ID,
				Label: s.Label,
			}
		}),
		Logo: api.ProviderLogo{
			Path:   resp.JSON200.Logo.Path,
			Width:  resp.JSON200.Logo.Width,
			Height: resp.JSON200.Logo.Height,
		},
	}, nil
}

func recordRetrievalToCoreAPIStudy(study recordretrievalservice.ExternalUploadRequestStudy) (*api.UploadRequestStudy, error) {
	respID, err := strconv.ParseInt(study.ID, 10, 64)
	if err != nil {
		return nil, err
	}
	return &api.UploadRequestStudy{
		ID:               respID,
		StudyInstanceUID: api.DicomUID(study.StudyInstanceUID),
		StudyDescription: typeconverter.ToOptString(study.StudyDescription),
		PatientName:      typeconverter.ToOptString(study.PatientName),
		PatientBirthDate: typeconverter.ToOptString(study.PatientBirthDate),
		Modality:         typeconverter.ToOptString(study.Modality),
		AccessionNumber:  typeconverter.ToOptString(study.AccessionNumber),
		StudyDate:        typeconverter.ToOptString(study.StudyDate),
		UploaderNotes:    typeconverter.ToOptString(study.UploaderNotes),
		InstanceCount:    study.InstanceCount,
	}, nil
}
