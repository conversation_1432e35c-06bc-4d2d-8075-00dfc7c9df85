/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package v2patients

import (
	"context"
	"database/sql"
	"errors"

	"github.com/segmentio/ksuid"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	notifs "gitlab.com/pockethealth/coreapi/pkg/mysql/notifications"
	phiProfiles "gitlab.com/pockethealth/coreapi/pkg/mysql/phi_profiles"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/util/phiHelpers"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// V2PatientsApiService is a service that implents the logic for the V2PatientsApiServicer
// This service should implement the business logic for every endpoint for the patients API.
// Include any external packages or services that will be required by this service.
type V2PatientsApiService struct {
	sqldb         *sql.DB
	acctsvcClient accountservice.AccountService

	//inject functions
	isAuthForFeature IsAuthForFeatureFunc
}

type IsAuthForFeatureFunc func(token string, featureId uint64) (bool, error)

// NewV2PatientsApiService creates a default api service
func NewV2PatientsApiService(
	db *sql.DB,
	acctsvc accountservice.AccountService,
) coreapi.V2PatientsApiServicer {
	return &V2PatientsApiService{
		sqldb:            db,
		acctsvcClient:    acctsvc,
		isAuthForFeature: auth.IsAuthForFeature,
	}
}

// DeletePatientSubscriptionMember - Remove family member
func (s *V2PatientsApiService) DeletePatient(
	ctx context.Context,
	patientId string,
	acctId string,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"patient_id": patientId, "account_id": acctId})
	// Delete patient in acctsvc
	err := s.acctsvcClient.DeletePatient(ctx, acctId, patientId)
	if err != nil {
		lg.WithError(err).Error("delete patient failed in acctsvc")
		return err
	}

	return err
}

// PostPatient adds an account patient.
// This assumes that the patient is created in-app through the UI.
func (s *V2PatientsApiService) PostPatient(
	ctx context.Context,
	acctId string,
	token string,
	pt models.PhiPatient,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", acctId)
	isAuth, err := s.isAuthForFeature(token, uint64(planservice.FAMILY_MEMBERS))
	if err != nil {
		lg.WithError(err).
			Error("unable to check if patient has auth to access family members feature")
		return "", err
	}

	if !isAuth {
		// check that account has no family members
		p, err := s.acctsvcClient.GetPatients(ctx, acctId)
		if len(p) > 0 || err != nil {
			lg.Error("account has no access to family member feature")
			return "", errors.New("account has no access to family member feature")
		}
	}

	p := accountservice.Patient{
		Email:       pt.Email,
		FirstName:   pt.FirstName,
		LastName:    pt.LastName,
		DOB:         pt.DOB,
		Phone:       pt.Phone,
		AltLastName: pt.AltLastName,
		Sex:         pt.Sex,
		Country:     pt.Country,
		Subdivision: pt.Subdivision,
		PostalCode:  pt.PostalCode,
		Source:      accountservice.PATIENT_CREATION_UI,
	}

	// add patient
	pId, err := s.acctsvcClient.GetOrCreatePatient(ctx, acctId, p, true)
	if err != nil {
		lg.WithError(err).Error("add patient failed in acctsvc")
		return "", err
	}

	res := phiHelpers.GetPhiInfoFromPatient(pId, &pt)
	if len(res) > 0 {
		err = phiProfiles.AddPhiProfiles(ctx, s.sqldb, res)
		if err != nil {
			lg.WithError(err).Error("failed to write phi_profile")
		}
	}

	return pId, err
}

// PatchPatient - Edit account patient
func (s *V2PatientsApiService) PatchPatient(
	ctx context.Context,
	patientId string,
	acctId string,
	pt models.PhiPatient,
) error {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"patient_id": patientId, "account_id": acctId})
	rb := accountservice.Patient{
		FirstName:   pt.FirstName,
		LastName:    pt.LastName,
		DOB:         pt.DOB,
		Phone:       pt.Phone,
		Email:       pt.Email,
		AltLastName: pt.AltLastName,
		Sex:         pt.Sex,
		Country:     pt.Country,
		Subdivision: pt.Subdivision,
		PostalCode:  pt.PostalCode,
	}

	// first update patient in acctsvc
	err := s.acctsvcClient.UpdatePatient(ctx, acctId, patientId, rb)
	if err != nil {
		lg.WithError(err).Error("update patient failed in acctsvc")
		return err
	}

	//TODO: allow updating phi profile so a patient can have multiple identifiers
	res := phiHelpers.GetPhiInfoFromPatient(patientId, &pt)
	if len(res) > 0 {
		err = phiProfiles.DeletePHIProfile(ctx, s.sqldb, patientId)
		if err != nil {
			lg.WithError(err).Error("unable to delete existing phiprofile")
		}
		err = phiProfiles.AddPhiProfiles(ctx, s.sqldb, res)
		if err != nil {
			lg.WithError(err).Error("failed to add phi_profile")
			return err
		}
	}

	return nil
}

// GetPatients - Get list of account patients
func (s *V2PatientsApiService) GetPatients(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{"account_id": acctId})
	// Get all patients for acctid from acctsvc
	p, err := s.acctsvcClient.GetPatients(ctx, acctId)
	if err != nil {
		lg.WithError(err).Error("could not get patients")
		return nil, err
	}

	pts := make([]models.PhiPatient, len(p))
	pIds := make([]interface{}, 0)
	for i := range p {
		pIds = append(pIds, p[i].PatientId)
	}

	// Get Phi profiles
	phiP, err := phiProfiles.GetPhiProfiles(ctx, s.sqldb, pIds)
	if err != nil {
		lg.WithError(err).Error("failed to fetch patient phi profiles")
		return nil, err
	}

	for i, patient := range p {
		pts[i] = models.PhiPatient{
			PatientId:      patient.PatientId,
			FirstName:      patient.FirstName,
			LastName:       patient.LastName,
			AltLastName:    patient.AltLastName,
			DOB:            patient.DOB,
			Phone:          patient.Phone,
			Email:          patient.Email,
			Sex:            patient.Sex,
			Country:        patient.Country,
			Subdivision:    patient.Subdivision,
			PostalCode:     patient.PostalCode,
			IsAccountOwner: *patient.IsAccountOwner,
		}
		for _, phi := range phiP {
			if pts[i].PatientId == phi.PatientId {
				switch phi.PhiType {
				case coreapi.OHIP:
					pts[i].Ohip = phi.PhiValue
				case coreapi.OHIP_VC:
					pts[i].Ohipvc = phi.PhiValue
				case coreapi.BCPHN:
					pts[i].Bcphn = phi.PhiValue
				case coreapi.SSN:
					pts[i].Ssn = phi.PhiValue
				case coreapi.IPN:
					pts[i].Ipn = phi.PhiValue
				case coreapi.ALT_H_ID:
					pts[i].AltId = phi.PhiValue
				default:
					lg.WithError(err).Errorf("no phi present for patient: %s", pts[i].PatientId)
				}
			}
		}
	}

	return pts, nil
}

// GetPatient - Get patient profile by id
func (s *V2PatientsApiService) GetPatient(
	ctx context.Context,
	acctId string,
	patientId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).
		WithFields(logrus.Fields{"account_id": acctId, "patient_id": patientId})
	// Get all patients for acctid from acctsvc
	p, err := s.acctsvcClient.GetPatient(ctx, acctId, patientId)
	if err != nil {
		lg.WithError(err).Error("could not get patient")
		return nil, err
	}

	var pId = []interface{}{patientId}
	// Get Phi profiles
	phiP, err := phiProfiles.GetPhiProfiles(ctx, s.sqldb, pId)
	if err != nil {
		lg.WithError(err).Error("failed to fetch patient phi profiles")
		return nil, err
	}

	pt := models.PhiPatient{
		PatientId:      p.PatientId,
		FirstName:      p.FirstName,
		LastName:       p.LastName,
		AltLastName:    p.AltLastName,
		DOB:            p.DOB,
		Phone:          p.Phone,
		Email:          p.Email,
		Sex:            p.Sex,
		Country:        p.Country,
		Subdivision:    p.Subdivision,
		PostalCode:     p.PostalCode,
		IsAccountOwner: *p.IsAccountOwner,
	}

	for _, phi := range phiP {
		if pt.PatientId == phi.PatientId {
			switch phi.PhiType {
			case coreapi.OHIP:
				pt.Ohip = phi.PhiValue
			case coreapi.OHIP_VC:
				pt.Ohipvc = phi.PhiValue
			case coreapi.BCPHN:
				pt.Bcphn = phi.PhiValue
			case coreapi.SSN:
				pt.Ssn = phi.PhiValue
			case coreapi.IPN:
				pt.Ipn = phi.PhiValue
			case coreapi.ALT_H_ID:
				pt.AltId = phi.PhiValue
			default:
				lg.WithError(err).Errorf("no phi present for patient: %s", pt.PatientId)
			}
		}
	}

	return pt, nil
}

func (s *V2PatientsApiService) GetAllPatientProfileValidations(
	ctx context.Context, accountId string,
) (map[string]accountservice.PatientProfileValidation, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", accountId)

	result, err := s.acctsvcClient.GetAllPatientProfileValidationsForAccount(ctx, accountId)
	if err != nil {
		lg.WithError(err).Error("Could not fetch all patient information validations")
		return nil, err
	}

	return result, nil
}

func (s *V2PatientsApiService) GetPatientProfileValidation(
	ctx context.Context, accountId, patientId string,
) (accountservice.PatientProfileValidation, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{"account_id": accountId, "patient_id": patientId})

	result, err := s.acctsvcClient.GetAccountPatientProfileValidation(ctx, accountId, patientId)
	if err != nil {
		lg.WithError(err).Error("Could not fetch patient information validations")
		if err.Error() != errormsgs.ERR_NOT_FOUND || err.Error() != errormsgs.ERR_FORBIDDEN {
			return accountservice.PatientProfileValidation{}, err
		}

		return accountservice.PatientProfileValidation{}, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	return result, nil
}

func (s *V2PatientsApiService) CreateNotificationForIncompletePatientsOnAccount(
	ctx context.Context, accountId string,
) (bool, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", accountId)

	orders, err := s.acctsvcClient.GetOrders(ctx, accountId, nil)
	if err != nil {
		lg.WithError(err).Error("Could not fetch orders")
		return false, err
	}

	// We only want to send the notification if they have chosen a plan
	if len(orders) == 0 {
		return false, nil
	}

	notificationsCreated := false
	result, err := s.acctsvcClient.GetAllPatientProfileValidationsForAccount(ctx, accountId)
	if err != nil {
		lg.WithError(err).Error("Could not fetch all patient information validations")
		return false, err
	}

	invalidProfiles := false
	for _, patient := range result {
		if !patient.Valid {
			invalidProfiles = true
			break
		}
	}

	if invalidProfiles {
		count, err := notifs.CountNotificationsForAccountByType(
			ctx, s.sqldb, accountId, coreapi.NotificationPatientInfoIncomplete,
		)
		if err != nil {
			lg.WithError(err).Errorf(
				"Could not get the %s notification count for account", coreapi.NotificationPatientInfoIncomplete,
			)
			return false, err
		}

		if count > 0 {
			return false, nil
		}

		_, err = notifs.CreateAccountNotification(
			ctx,
			s.sqldb,
			ksuid.New().String(),
			accountId,
			coreapi.NotificationPatientInfoIncomplete,
			"",
			nil,
		)
		if err != nil {
			lg.WithError(err).Error("failed to create patient eligibility program notification")
			return false, err
		}

		notificationsCreated = true
	}

	return notificationsCreated, nil
}
