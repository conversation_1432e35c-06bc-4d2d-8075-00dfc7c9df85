//go:build integration
// +build integration

package v2patients

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

func setupService(t *testing.T, db *sql.DB) coreapi.V2PatientsApiServicer {
	t.Helper()

	cfg := testutils.LoadTestConfigFromEnvVar(t)
	qaAcctSvcUrl := cfg.AcctSvcUrl
	acctServiceUser := "apptest_rw"
	acctSvcApiKey := "CIqm6Y9zoBAfDt0tDXpK4f60gd54VNt7qwlLZRrgfVICfZHqHFPAnvP9JAapWZLY"

	httpClient := httpclient.NewHTTPClient(
		&http.Client{},
		nil,
	)
	accountSrvc := accountservice.NewHTTPAccountServiceClient(qaAcctSvcUrl, acctServiceUser, acctSvcApiKey, httpClient)
	return NewV2PatientsApiService(db, accountSrvc)
}

func TestV2PatientsService(t *testing.T) {
	db := testutils.SetupTestDB(t)
	var err error

	mockIsAuthForFeature := func(token string, featureId uint64) (bool, error) {
		if featureId != uint64(planservice.FAMILY_MEMBERS) {
			return false, errors.New("should only check FAMILY_MEMBERS feature here")
		}
		if token == "Unlimited" {
			return true, nil
		} else {
			return false, nil
		}
	}

	rand := strconv.FormatInt(time.Now().UnixNano(), 10)
	patientEmail := "test_email_" + rand + "@example.com"
	acctId := "tAId" + rand
	pId := "tPid" + rand

	mockPatient := accountservice.Patient{
		FirstName:      "testFName",
		LastName:       "testLname",
		DOB:            "1998-01-01",
		PatientId:      pId,
		Phone:          "*********",
		Email:          patientEmail,
		Sex:            "O",
		Country:        "CA",
		Subdivision:    "ON",
		IsAccountOwner: &[]bool{true}[0],
	}

	mockPatients := make([]accountservice.Patient, 2)
	mockPatients = append(mockPatients, mockPatient)
	// mockPatients = append(mockPatients, mockPatient2)

	service := &V2PatientsApiService{
		sqldb: db,
		acctsvcClient: &accountservice.AcctSvcMock{
			GetPatientReturn:  mockPatient,
			GetPatientsReturn: mockPatients,
		},
		isAuthForFeature: mockIsAuthForFeature,
	}
	noPatientsService := &V2PatientsApiService{
		sqldb: db,
		acctsvcClient: &accountservice.AcctSvcMock{
			GetPatientReturn:  accountservice.Patient{},
			GetPatientsReturn: []accountservice.Patient{},
		},
		isAuthForFeature: mockIsAuthForFeature,
	}

	t.Run(
		"Post patient with Free subscription but no patients in account", func(t *testing.T) {
			ctx := context.Background()
			newPt := models.PhiPatient{
				FirstName:   "First",
				LastName:    "Last",
				DOB:         "1997-02-02",
				Ohip:        "**********",
				Ohipvc:      "AA",
				AltId:       "",
				Phone:       "**********",
				Email:       patientEmail,
				Sex:         "O",
				Country:     "CA",
				Subdivision: "ON",
				Ipn:         "",
				Ssn:         "",
			}
			patientId, err := noPatientsService.PostPatient(ctx, acctId, "Free", newPt)
			if err != nil {
				t.Fatalf("unable to update the profile: %s", err)
			}
			t.Cleanup(func() {
				db.Exec("DELETE FROM phi_profiles WHERE patientId = ?", patientId)
			})

			// verify phi_profiles has been populated with ohip type and value
			var pId2, phiType, phiVal string
			err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", patientId, coreapi.OHIP).
				Scan(&pId2, &phiType, &phiVal)
			require.NoError(t, err, "error finding user in phi profiles")
			assert.Equal(t, patientId, pId2)
			assert.Equal(t, string(coreapi.OHIP), phiType)
			assert.Equal(t, "**********", phiVal)

			//also verify if a new row exists in phi_profiles for ohip vc and value
			var pId3, phiType2, phiVal2 string
			err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", patientId, coreapi.OHIP_VC).
				Scan(&pId3, &phiType2, &phiVal2)
			require.NoError(t, err, "failed to find patient in phi_profiles")
			assert.Equal(t, patientId, pId3)
			assert.Equal(t, string(coreapi.OHIP_VC), phiType2)
			assert.Equal(t, "AA", phiVal2)
		},
	)
	t.Run(
		"Post patient with Free subscription with patients in account", func(t *testing.T) {
			ctx := context.Background()
			newPt := models.PhiPatient{
				FirstName:   "First",
				LastName:    "Last",
				DOB:         "1997-02-02",
				Ohip:        "**********",
				Ohipvc:      "AA",
				AltId:       "",
				Phone:       "**********",
				Email:       patientEmail,
				Sex:         "O",
				Country:     "CA",
				Subdivision: "ON",
				Ipn:         "",
				Ssn:         "",
			}
			patientId, err := service.PostPatient(ctx, acctId, "Free", newPt)
			require.Error(t, err, "should have had a no family member feature access error but didn't happen")
			t.Cleanup(func() {
				db.Exec("DELETE FROM phi_profiles WHERE patientId = ?", patientId)
			})

		},
	)

	t.Run("Post patient with OHIP and OHIP VC", func(t *testing.T) {
		ctx := context.Background()
		newPt := models.PhiPatient{
			FirstName:   "First",
			LastName:    "Last",
			DOB:         "1997-02-02",
			Ohip:        "**********",
			Ohipvc:      "AA",
			AltId:       "",
			Phone:       "**********",
			Email:       patientEmail,
			Sex:         "O",
			Country:     "CA",
			Subdivision: "ON",
			Ipn:         "",
			Ssn:         "",
		}
		patientId, err := service.PostPatient(ctx, acctId, "Unlimited", newPt)
		require.NoError(t, err, "unable to update profile")

		t.Cleanup(func() {
			db.Exec("DELETE FROM phi_profiles WHERE patientId = ?", patientId)
		})

		// verify phi_profiles has been populated with ohip type and value
		var pId2, phiType, phiVal string
		err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", patientId, coreapi.OHIP).
			Scan(&pId2, &phiType, &phiVal)
		require.NoError(t, err, "error finding user in phi profiles")
		assert.Equal(t, patientId, pId2)
		assert.Equal(t, string(coreapi.OHIP), phiType)
		assert.Equal(t, "**********", phiVal)

		//also verify if a new row exists in phi_profiles for ohip vc and value
		var pId3, phiType2, phiVal2 string
		err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", patientId, coreapi.OHIP_VC).
			Scan(&pId3, &phiType2, &phiVal2)
		require.NoError(t, err, "failed to find patient in phi_profiles")
		assert.Equal(t, patientId, pId3)
		assert.Equal(t, string(coreapi.OHIP_VC), phiType2)
		assert.Equal(t, "AA", phiVal2)

	})

	t.Run(
		"Patch subscription edit patient profile, change firstName, OHIP and OHIP VC",
		func(t *testing.T) {
			ctx := context.Background()

			if err != nil {
				t.Fatalf("unable to setup test patient subscription: %s", err)
			}

			updatePt := models.PhiPatient{
				FirstName:   "FirstEdited",
				LastName:    "Last",
				DOB:         "1997-02-02",
				Ohip:        "666666666",
				Ohipvc:      "BB",
				AltId:       "",
				Phone:       "**********",
				Email:       patientEmail,
				Sex:         "O",
				Country:     "CA",
				Subdivision: "ON",
				Ipn:         "",
				Ssn:         "",
			}
			err = service.PatchPatient(ctx, pId, acctId, updatePt)
			require.NoError(t, err)

			//also verify phi_profiles has been populated with ohip type and value
			var pId2, phiType, phiVal string
			err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", pId, coreapi.OHIP).
				Scan(&pId2, &phiType, &phiVal)
			require.NoError(t, err, "failed to find user in phi_profiles table")
			assert.Equal(t, pId, pId2)
			assert.Equal(t, string(coreapi.OHIP), phiType)
			assert.Equal(t, "666666666", phiVal)

			//also verify if a new row exists in phi_profiles for ohip vc and value
			var pId3, phiType2, phiVal2 string
			err = db.QueryRow("SELECT patient_id, phi_type, phi_value FROM phi_profiles where patient_id = ? AND phi_type =? LIMIT 1", pId, coreapi.OHIP_VC).
				Scan(&pId3, &phiType2, &phiVal2)
			require.NoError(t, err, "failed to find patient in phi_profiles")
			assert.Equal(t, pId, pId3)
			assert.Equal(t, string(coreapi.OHIP_VC), phiType2)
			assert.Equal(t, "BB", phiVal2)
		},
	)

	t.Run("GET a single profile for a given acctId, patientId including OHIP", func(t *testing.T) {
		ctx := context.Background()

		_, err = db.Exec(
			"INSERT INTO phi_profiles(id, patient_id, phi_type, phi_value) VALUES (?, ?, ?, ?)",
			"phId"+rand,
			pId,
			coreapi.OHIP,
			"**********",
		)
		if err != nil {
			t.Fatalf("unable to setup test patient subscription: %s", err)
		}

		res, err := service.GetPatient(ctx, acctId, pId)
		if err != nil {
			t.Fatalf("unable to get the patient profile: %s", err)
		}

		//cast to struct
		v := res.(models.PhiPatient)
		if v.FirstName != "testFName" {
			t.Fatalf("getting wrong first name, expect 'testFName' but got %s", v.FirstName)
		}

		if v.LastName != "testLname" {
			t.Fatalf("getting wrong last name, expect 'testLname' but got %s", v.LastName)
		}

		if v.DOB != "1998-01-01" {
			t.Fatalf("getting wrong DOB, expect '1998-01-01' but got %s", v.DOB)
		}

		if v.Ohip != "**********" {
			t.Fatalf("getting wrong ohip value, expect '**********' but got %s", v.Ohip)
		}
	})
}

func TestCreateNotificationForIncompletePatientsOnAccount(t *testing.T) {
	db := testutils.SetupTestDB(t)
	srvc := setupService(t, db)

	t.Run("when an account has a patient with missing profile information", func(t *testing.T) {
		accountId := "2vs0w0J0KkwULTszsTgHeE5ICaG"           // incomplete with Unlimited
		accountIdNoOrders := "2cGzADVm48Qtchm4ehnrso7vUln"   // incomplete without orders
		accountIdFirstLogin := "2vs3b7pc4fcpa9m2bAfnXrsPAYd" // simulate account on first login

		t.Cleanup(func() {
			db.Exec("DELETE FROM user_notifications where account_id = ? AND name = ?", accountId, coreapi.NotificationPatientInfoIncomplete)
		})

		t.Run("and the account does not have any notifications", func(t *testing.T) {
			// Make sure that this account has no PATIENT_INFO_INCOMPLETE notifications
			_, err := db.Exec("DELETE FROM user_notifications where account_id = ? AND name = ?", accountId, coreapi.NotificationPatientInfoIncomplete)
			require.NoError(t, err)
			row := db.QueryRow("SELECT count(*) from user_notifications where account_id = ? AND name = ?", accountId, coreapi.NotificationPatientInfoIncomplete)
			var count int
			err = row.Scan(&count)
			require.NoError(t, err)
			require.Zero(t, count)

			created, err := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), accountId)
			assert.NoError(t, err)
			assert.True(t, created)
		})

		t.Run("and the account already has a PATIENT_INFO_INCOMPLETE notification", func(t *testing.T) {
			// Make sure that this account has at least one PATIENT_INFO_INCOMPLETE notification
			_, err := db.Exec("INSERT INTO user_notifications (id, account_id, name) VALUES (?,?,?)", ksuid.New().String(), accountId, coreapi.NotificationPatientInfoIncomplete)
			require.NoError(t, err)
			row := db.QueryRow("SELECT count(*) from user_notifications where account_id = ? AND name = ?", accountId, coreapi.NotificationPatientInfoIncomplete)
			var count int
			err = row.Scan(&count)
			require.NoError(t, err)
			require.NotZero(t, count)

			created, err := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), accountId)
			assert.NoError(t, err)
			assert.False(t, created)
		})

		t.Run("and the account has no orders", func(t *testing.T) {
			// Make sure that this account has no PATIENT_INFO_INCOMPLETE notifications
			_, err := db.Exec("DELETE FROM user_notifications where account_id = ? AND name = ?", accountIdNoOrders, coreapi.NotificationPatientInfoIncomplete)
			require.NoError(t, err)
			row := db.QueryRow("SELECT count(*) from user_notifications where account_id = ? AND name = ?", accountIdNoOrders, coreapi.NotificationPatientInfoIncomplete)
			var count int
			err = row.Scan(&count)
			require.NoError(t, err)
			require.Zero(t, count)

			created, err := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), accountIdNoOrders)
			assert.NoError(t, err)
			assert.False(t, created)
		})

		t.Run("and the account is in a default state (also has no orders)", func(t *testing.T) {
			// Make sure that this account has no PATIENT_INFO_INCOMPLETE notifications
			_, err := db.Exec("DELETE FROM user_notifications where account_id = ? AND name = ?", accountIdFirstLogin, coreapi.NotificationPatientInfoIncomplete)
			require.NoError(t, err)
			row := db.QueryRow("SELECT count(*) from user_notifications where account_id = ? AND name = ?", accountIdFirstLogin, coreapi.NotificationPatientInfoIncomplete)
			var count int
			err = row.Scan(&count)
			require.NoError(t, err)
			require.Zero(t, count)

			created, err := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), accountIdFirstLogin)
			assert.NoError(t, err)
			assert.False(t, created)
		})
	})

	t.Run("when an account does not have a patient with missing profile information", func(t *testing.T) {
		accountId := "2b5TNo2af1JqtTJiRmd5m4L2t3o"

		created, err := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), accountId)
		assert.NoError(t, err)
		assert.False(t, created)
	})

	t.Run("when the account id is not valid", func(t *testing.T) {
		created, _ := srvc.CreateNotificationForIncompletePatientsOnAccount(context.TODO(), ksuid.New().String())
		assert.False(t, created)
	})
}
