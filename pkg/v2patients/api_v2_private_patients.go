/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package v2patients

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// A PrivateSubscriptionsApiController binds http requests to an api service and writes the service results to the http response
type V2PrivatePatientsApiController struct {
	service coreapi.V2PatientsApiServicer
}

// NewV2PrivatePatientsApiController creates a default api controller
func NewV2PrivatePatientsApiController(
	s coreapi.V2PatientsApiServicer,
) coreapi.V2PrivatePatientsApiRouter {
	return &V2PrivatePatientsApiController{service: s}
}

// Routes returns all of the api route for the PrivateSubscriptionsApiController
func (c *V2PrivatePatientsApiController) Routes() coreapi.Routes {
	return coreapi.Routes{

		{
			Name:        "DeletePatient",
			Method:      strings.ToUpper("Delete"),
			Pattern:     "/{patientId}",
			HandlerFunc: c.DeletePatient,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 15},
		},
		{
			Name:        "PostPatient",
			Method:      strings.ToUpper("Post"),
			Pattern:     "",
			HandlerFunc: c.PostPatient,
			RateLimit:   ratelimit.RateLimitConfig{PeriodMinutes: 5, MaxRequests: 15},
		},
		{
			Name:        "GetPatients",
			Method:      strings.ToUpper("Get"),
			Pattern:     "",
			HandlerFunc: c.GetPatients,
		},
		{
			Name:        "GetAllPatientProfileValidations",
			Method:      http.MethodGet,
			Pattern:     "/valid",
			HandlerFunc: c.GetAllPatientProfileValidations,
		},
		{
			Name:        "PostIncompletePatientInfoAccount",
			Method:      http.MethodPost,
			Pattern:     "/incomplete/notification",
			HandlerFunc: c.PostIncompletePatientInfoNotification,
		},
		{
			Name:        "GetPatient",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{patientId}",
			HandlerFunc: c.GetPatient,
		},
		{
			Name:        "PatchPatient",
			Method:      strings.ToUpper("Patch"),
			Pattern:     "/{patientId}",
			HandlerFunc: c.PatchPatient,
		},
		{
			Name:        "GetPatientProfileValidation",
			Method:      http.MethodGet,
			Pattern:     "/{patientId}/valid",
			HandlerFunc: c.GetPatientProfileValidation,
		},
	}
}

func (c *V2PrivatePatientsApiController) GetPathPrefix() string {
	return "/v2/patients"
}

func (c *V2PrivatePatientsApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	//all PrivateSubscriptions paths require auth
	return []func(next http.Handler) http.Handler{auth.ValidateAuth}
}

func getAccountIdFromToken(r *http.Request) (string, error) {
	token := r.Header.Get("Authorization")
	acctId, err := auth.DecodeAccountToken(token)
	if err != nil {
		logutils.CtxLogger(r.Context()).WithError(err).Error("error decoding token")
	}
	return acctId, err
}

// DeletePatient - Remove patient profile
func (c *V2PrivatePatientsApiController) DeletePatient(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	patientId := params["patientId"]

	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	err = c.service.DeletePatient(r.Context(), patientId, acctId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// PostPatient - Add a patient
func (c *V2PrivatePatientsApiController) PostPatient(
	w http.ResponseWriter,
	r *http.Request,
) {
	err := r.ParseForm()
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	pt := &models.PhiPatient{}
	if err := json.NewDecoder(r.Body).Decode(&pt); err != nil {
		logutils.CtxLogger(r.Context()).WithError(err).Error("error unmarchaling json")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	if !pt.IsValid(r.Context()) {
		logutils.CtxLogger(r.Context()).WithError(err).Error("invalid profile")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	pId, err := c.service.PostPatient(
		r.Context(),
		acctId,
		r.Header.Get("Authorization"),
		*pt,
	)
	if err != nil {
		if err.Error() == "account has no access to family member feature" {
			httperror.ErrorWithLog(w, r, http.StatusText(http.StatusForbidden), http.StatusForbidden)
		} else {
			httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), pId, nil, w)
}

// PatchPatient - Edit account patient
func (c *V2PrivatePatientsApiController) PatchPatient(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	patientId := params["patientId"]

	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	pt := &models.PhiPatient{}
	if err := json.NewDecoder(r.Body).Decode(&pt); err != nil {
		logutils.CtxLogger(r.Context()).WithError(err).Error("error unmarchaling json")
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	err = c.service.PatchPatient(r.Context(), patientId, acctId, *pt)
	if err != nil {
		var statusCode int
		if err.Error() == errormsgs.ERR_INVALID_REQ_BODY || err.Error() == http.StatusText(http.StatusBadRequest) {
			statusCode = http.StatusBadRequest
		} else {
			statusCode = http.StatusInternalServerError
		}

		httperror.ErrorWithLog(w, r, err.Error(), statusCode)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (c *V2PrivatePatientsApiController) GetPatients(
	w http.ResponseWriter,
	r *http.Request,
) {

	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetPatients(r.Context(), acctId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}
func (c *V2PrivatePatientsApiController) GetPatient(
	w http.ResponseWriter,
	r *http.Request,
) {

	acctId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	params := mux.Vars(r)
	patientId := params["patientId"]

	result, err := c.service.GetPatient(r.Context(), acctId, patientId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *V2PrivatePatientsApiController) GetAllPatientProfileValidations(w http.ResponseWriter, r *http.Request) {
	accountId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	result, err := c.service.GetAllPatientProfileValidations(r.Context(), accountId)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *V2PrivatePatientsApiController) GetPatientProfileValidation(w http.ResponseWriter, r *http.Request) {
	accountId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	params := mux.Vars(r)
	patientId := params["patientId"]

	result, err := c.service.GetPatientProfileValidation(r.Context(), accountId, patientId)
	if err != nil {
		errStatus := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			errStatus = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), errStatus)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

func (c *V2PrivatePatientsApiController) PostIncompletePatientInfoNotification(w http.ResponseWriter, r *http.Request) {
	accountId, err := getAccountIdFromToken(r)
	if err != nil {
		httperror.ErrorWithLog(w, r, err.Error(), http.StatusUnauthorized)
		return
	}

	created, err := c.service.CreateNotificationForIncompletePatientsOnAccount(r.Context(), accountId)
	if err != nil {
		status := http.StatusInternalServerError
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			status = http.StatusNotFound
		}
		httperror.ErrorWithLog(w, r, err.Error(), status)
		return
	}

	status := http.StatusOK
	if created {
		status = http.StatusCreated
	}

	coreapi.EncodeJSONResponse(r.Context(), nil, &status, w)
}
