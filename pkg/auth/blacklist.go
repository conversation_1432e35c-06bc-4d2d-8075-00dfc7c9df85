package auth

import (
	"context"
	"database/sql"
	"time"

	"github.com/sirupsen/logrus"
	sqlTokens "gitlab.com/pockethealth/coreapi/pkg/mysql/tokens"
)

// map of tokens to expiry
var tokenBlacklist map[string]int64
var db *sql.DB

func InitializeBlacklist(sqldb *sql.DB) {
	db = sqldb
	tokenBlacklist = make(map[string]int64)
	loadPersistedBlacklist()
	removeExpiredTokens()
}

// load blacklist from db
func loadPersistedBlacklist() {
	if db != nil {
		tokenBlacklist, _ = sqlTokens.LoadBlacklist(db)
	}
}

// Add a token to the blacklist that has not expired but is invalid
// Also take this opportunity to go through and delete tokens from the list that have now expired
func AddToBlacklist(ctx context.Context, token string) error {
	claims, err := DecodeBearerToken(token)
	if err != nil {
		return err
	}
	tokenBlacklist[token] = claims.RegisteredClaims.ExpiresAt.Unix()
	if db != nil {
		err = sqlTokens.AddToken(ctx, db, token, claims.RegisteredClaims.ExpiresAt.Unix())
		if err != nil {
			return err
		}
	}

	removeExpiredTokens()
	return nil
}

// check for existence of token in blacklist
func IsInBlacklist(ctx context.Context, token string) (found bool) {
	//check cache
	_, found = tokenBlacklist[token]
	if !found && db != nil {
		//check db
		found, expiry := sqlTokens.IsInBlacklist(ctx, db, token)
		if found { //add to local cache
			tokenBlacklist[token] = expiry
		}
		return found
	}
	return found
}

// remove all blacklist entries that have expired
func removeExpiredTokens() {
	for token, expiry := range tokenBlacklist {
		if expiry < time.Now().Unix() {
			delete(tokenBlacklist, token)
		}
	}

	if db != nil { //if not unit testing
		err := sqlTokens.RemoveExpiredTokens(db)
		if err != nil {
			logrus.WithError(err).Error("failed to remove expired tokens")
		}
	}
}
