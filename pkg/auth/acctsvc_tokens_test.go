package auth

import (
	"crypto/ecdsa"
	"reflect"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v4"

	"gitlab.com/pockethealth/coreapi/pkg/auth/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/testutil"
)

func Test_adaptAcctsTokenToUserToken(t *testing.T) {
	privKey := testutil.DecodeECPrivateKeyPEM(t, hardcodedPrivKeyPEM)
	acctSvcJWTPublicKey = privKey.Public().(*ecdsa.PublicKey)

	t.Run("good acct jwt (generated)", func(t *testing.T) {
		// setup
		claims := accountservice.GlobalClaims{
			Users: map[uint16]uint64{
				1: 42,
			},
			AcctType:   "Patient",
			MainRegion: 1,
			IPAddr:     "127.0.0.1",
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    "acctsvc",
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 438000)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
			},
		}
		jwtStr, err := jwt.NewWithClaims(jwt.SigningMethodES256, claims).SignedString(privKey)
		if err != nil {
			t.Fatal("test setup error - bad priv key:", err)
		}

		input := jwtStr
		t.Log(input)
		// execute
		got, err := adaptAcctsTokenToUserToken(input)
		if err != nil {
			t.Error("unexpected error: ", err)
		}

		// check
		want := Claims{
			IP: "127.0.0.1",
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: claims.ExpiresAt,
				IssuedAt:  claims.IssuedAt,
			},
			AccountType: models.ACCOUNT_TYPE_PATIENT,
		}

		if !reflect.DeepEqual(got, want) {
			t.Errorf(
				"adaptAcctsTokenToUserToken() unexpected result.  got: %+v. want: %+v",
				got,
				want,
			)
		}
	})

	t.Run("bad jwt", func(t *testing.T) {
		// setup
		input := "not.a.jwt"

		// execute
		got, err := adaptAcctsTokenToUserToken(input)

		// check
		if err == nil {
			t.Error("expected error but got none")
		}
		want := Claims{}
		if !reflect.DeepEqual(got, want) {
			t.Errorf(
				"adaptAcctsTokenToUserToken() unexpected result.  got: %+v. want: %+v",
				got,
				want,
			)
		}
	})
}

var hardcodedPrivKeyPEM = `
***********************************************************************************************************************************************************************************************************************************`
