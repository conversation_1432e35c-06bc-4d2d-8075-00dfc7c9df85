package auth

import (
	"errors"

	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
	"gopkg.in/square/go-jose.v2"
)

var ErrorJWEDecrypt = errors.New("unable to decrypt jwe")

// EncryptJWETwoPart<PERSON>ey encrypts the provided jwt token using a secret key
// and a user-provided key formatted as {userKey}{secretKey}
func EncryptJWETwoPartKey(jwtToken []byte, userKey string) (string, error) {

	compositeKey, err := createCompositeKey([]byte(userKey), secretKey)
	if err != nil {
		return "", err
	}

	rcpt := jose.Recipient{
		Algorithm: jose.DIRECT,
		Key:       compositeKey,
	}

	enc, err := jose.NewEncrypter(jose.A256CBC_HS512, rcpt, nil)
	if err != nil {
		return "", err
	}
	jweToken, err := enc.Encrypt(jwtToken)
	if err != nil {
		return "", err
	}

	return jweToken.CompactSerialize()
}

// DecryptJWETwoPart<PERSON>ey decrypts the provided jwe token using a key of the format {userKey}{secretKey}
func DecryptJWETwoPartKey(jweToken string, userKey string) ([]byte, error) {
	jwe, err := jose.ParseEncrypted(jweToken)
	if err != nil {
		return nil, errors.New("unable to parsed jwe")
	}

	compositeKey, err := createCompositeKey([]byte(userKey), secretKey)
	if err != nil {
		return nil, err
	}

	decrypted, err := jwe.Decrypt(compositeKey)
	if err != nil {
		return nil, ErrorJWEDecrypt
	}
	return decrypted, nil
}

// createCompositeKey creates a composite key composed of two parts
// the key gets padded or truncated to make it 64 bytes long
func createCompositeKey(keyPart1, keyPart2 []byte) ([]byte, error) {
	// the content-encryption algorithm requires a (512/8)=64 byte key
	compositeKey := append([]byte(keyPart1), keyPart2...)
	if len(compositeKey) < 64 {
		// pad key
		var err error
		compositeKey, err = secure.PKCS7Pad(compositeKey, 64)
		if err != nil {
			return nil, err
		}
	} else if len(compositeKey) > 64 {
		// truncate the key
		compositeKey = compositeKey[:64]
	}

	return compositeKey, nil
}
