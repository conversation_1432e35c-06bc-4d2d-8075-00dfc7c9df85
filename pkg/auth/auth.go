package auth

import (
	"context"
	"net/http"
	"strings"

	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type authMethodKeyT int

const (
	authMethodKey authMethodKeyT = iota
)

type authMethod int

const (
	bearerTokenAuthMethod authMethod = iota
	signedRequestAuthMethod
)

func IsValidatedWithBearerToken(ctx context.Context) bool {
	method := ctx.Value(authMethodKey)
	if method := method.(authMethod); method == bearerTokenAuthMethod {
		return true
	}
	return false
}

func IsValidatedWithSignedRequest(ctx context.Context) bool {
	method := ctx.Value(authMethodKey)
	if method := method.(authMethod); method == signedRequestAuthMethod {
		return true
	}
	return false
}

// auth middleware - called on authenticated routes before the handler is called
func ValidateAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		r, isValid := ValidateToken(w, r)
		if !isValid {
			return
		}
		// Pass down the request to the next middleware (or final handler)
		next.ServeHTTP(w, recordAuthMethod(r, bearerTokenAuthMethod))
	})
}

// This middleware supports both JWT bearer tokens and the X-PH-Signature header (signed with
// accountservice's private key)
func ValidateDualAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		r, _, authorized := getAndValidateBearerClaims(r)
		if authorized {
			r = recordAuthMethod(r, bearerTokenAuthMethod)
		} else {
			authorized = ValidateAcctSvcSignature(r, r.Header.Get("X-PH-Signature"))
			if authorized {
				r = recordAuthMethod(r, signedRequestAuthMethod)
			}
		}
		if !authorized {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
			return
		}

		// Pass down the request to the next middleware (or final handler)
		next.ServeHTTP(w, r)
	})
}

func ValidatePHSignature(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authorized := ValidateAcctSvcSignature(r, r.Header.Get("X-PH-Signature"))
		if authorized {
			r = recordAuthMethod(r, signedRequestAuthMethod)
		} else {
			httperror.ErrorWithLog(
				w,
				r,
				http.StatusText(http.StatusUnauthorized),
				http.StatusUnauthorized,
			)
			return
		}

		// Pass down the request to the next middleware (or final handler)
		next.ServeHTTP(w, r)
	})
}

// ValidateToken() checks that the token is valid and not blacklisted. It writes an HTTP
// Unauthorized response for an invalid token
func ValidateToken(w http.ResponseWriter, r *http.Request) (*http.Request, bool) {
	r, claims, authorized := getAndValidateBearerClaims(r)
	if !authorized {
		httperror.ErrorWithLogFields(
			w, r, "Not authorized", http.StatusUnauthorized,
			map[string]interface{}{
				"claims_ip": claims.IP,
			},
		)
		return r, false
	}
	return r, true
}

// auth middleware - validate auth from outbound service
func ValidateObsAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		valid := ValidateObsToken(w, r)
		if !valid {
			return
		}

		// Pass down the request to the next middleware (or final handler)
		next.ServeHTTP(w, r)
	})
}

func ValidateObsToken(w http.ResponseWriter, r *http.Request) bool {
	token := r.Header.Get("Authorization")
	// check that the token is valid by decoding it
	reqId, err := DecodeIncompleteReqEmailToken(token)
	if err != nil {
		httperror.ErrorWithLogFields(
			w, r, "Not authorized", http.StatusUnauthorized,
			map[string]interface{}{
				"claims_req_id": reqId,
			},
		)
		return false
	}

	return true
}

// getAndValidateBearerClaims() checks that the token is valid by decoding it, and ensuring it is not
// blacklisted. It does not write an HTTP response for an invalid token.
// If the token is valid, claims get added to the request's context. An updated request is returned
func getAndValidateBearerClaims(r *http.Request) (*http.Request, Claims, bool) {
	token := r.Header.Get("Authorization")
	claims, err := DecodeBearerToken(token)
	if err != nil {
		logutils.Errorx(r.Context(), "failed to decode token", err)
		return r, Claims{}, false
	}
	return enrichContext(r, claims), claims, !IsInBlacklist(r.Context(), token)
}

func recordAuthMethod(r *http.Request, method authMethod) *http.Request {
	ctx := r.Context()
	ctx = context.WithValue(ctx, authMethodKey, method)
	return r.WithContext(ctx)
}

// enrichContext adds values from claims and request to context, including
// IP - the request's remote address
// Subject - the subject as set in claims
// AccountID - the caller's account ID as set in claims
// AccountType - the caller's account type as set in claims, e.g. "Patient" or "Physician"
// The values can be retrieved from context using auth.<Value>OrEmpty or auth.<Value>
func enrichContext(r *http.Request, claims Claims) *http.Request {
	ctx := r.Context()
	// grab first ip from request, if any
	// NOTE: this only works if an IPResolverMiddleware is in place, see routers.go
	// otherwise this will be the IP of a load balancer, not the original caller's IP
	ips := strings.Split(r.RemoteAddr, ":")
	ip := ""
	if len(ips) > 0 {
		ip = ips[0]
	}
	ctx = auth.WithIP(ctx, ip)
	ctx = auth.WithSubject(ctx, claims.Subject)
	ctx = auth.WithAccountID(ctx, claims.AccountID)
	ctx = auth.WithAccountType(ctx, string(claims.AccountType))
	return r.WithContext(ctx)
}
