package auth

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

const (
	localClaimIp = "127.0.0.1"
)

func TestDecodeBearerToken(t *testing.T) {
	secretKey = []byte("mySecretKey1")
	t.Run("valid signed auth token 1", func(t *testing.T) {
		inputTok := "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2QR4gH_tc4APRxbt06iroyLcZwnwjq7507SAOVMVfnc"
		want := "23ZIHzTbIX9TNHdGaR4ZYU9P15V"
		if got, err := DecodeAccountToken(inputTok); err != nil {
			t.Errorf("Unexpected error: %+v", err)
		} else {
			if got != want {
				t.Errorf("got %q wanted %q\n", got, want)
			}
		}
	})

	t.Run("invalid signature should have error", func(t *testing.T) {
		//input token signed with HMACSHA512 instead of HMACSHA256
		inputTok := "bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.*******************************************************.7doFutIeK5P_UJ8LViA3iKsLLuTfJ98r1bZ6PEkjffq7hfhaL1jMEwpnb1YsBvuuftVqbSDXqDH6LtIhhzeO2g"
		if got, err := DecodeAccountToken(inputTok); err == nil {
			t.Errorf("Should have had error but one didn't happen.  got = %+v", got)
		}
	})

	t.Run("invalid token format should have error", func(t *testing.T) {
		inputTok := "bearer eyJhbGciOiJIUcCI6IkpXVCJ9.*******************************************************.n4lS88jpcC54jBy9kYKHdRLrCwAwj8voBcoesMa-5rc"
		if got, err := DecodeAccountToken(inputTok); err == nil {
			t.Errorf("Should have had error but one didn't happen.  got = %+v", got)
		}
	})

	t.Run("invalid token format (without bearer) should have error", func(t *testing.T) {
		inputTok := "eyJhbGciOiJIUcCI6IkpXVCJ9.*******************************************************.n4lS88jpcC54jBy9kYKHdRLrCwAwj8voBcoesMa-5rc"
		if got, err := DecodeAccountToken(inputTok); err == nil {
			t.Errorf("Should have had error but one didn't happen.  got = %+v", got)
		}
	})

}

func TestTokenExpiry(t *testing.T) {
	secretKey = []byte("mySecretKey1")

	t.Run("expired token should have error", func(t *testing.T) {
		UserTokenExpiryHours = 0
		inputTok := MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)
		time.Sleep(time.Second * 1) //token should be expired after this

		got, err := DecodeAccountToken("Bearer " + inputTok)
		if err == nil {
			t.Errorf("Should have had error but one didn't happen.  got = %+v", got)
		} else if !strings.Contains(err.Error(), "expired") {
			t.Errorf("Token should be that it's expired. Error: %q", err.Error())
		}
	})

	t.Run("non expired token should have no error", func(t *testing.T) {
		UserTokenExpiryHours = 1
		inputTok := MakeAccountAuthToken("24IBqyml0oDV2EYaO57NJLcRb7l", localClaimIp)

		got, err := DecodeAccountToken("Bearer " + inputTok)
		if err != nil {
			t.Errorf("Should have no error. got = %+v, Error: %q", got, err.Error())
		}
	})
}

func TestSetRegionRouterPublicKey(t *testing.T) {
	t.Run("expired token should have error", func(t *testing.T) {
		pub := `-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaolFpG78PddlbXbBZYUeBeuXdiLr
hHnAW7uw1Et5yOgTUwljxDJja5fLqLcz3U5h+iOGs1g5Chm5M1WDYb9JDA==
-----END PUBLIC KEY-----`

		err := SetRegionRouterPublicKey([]byte(pub))
		if err != nil {
			t.Error("error setting rr public key", err)
		}
	})
}

func TestSetAcctServPublicKey(t *testing.T) {
	t.Run("valid key should set", func(t *testing.T) {
		pub := `-----BEGIN PUBLIC KEY-----
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERCF9OF18tDXQhM+NOcGTkfU0f4p12SVO
UFySZ27cysKBIKpqJAN1/9MmyDaFfGqM9NrhfVk0vnf0xgBdlLx9Iq6vwoHrWh8o
eoX0+zIZ/oBpCmYKCmy95QzPpwyUmuMt
-----END PUBLIC KEY-----`

		err := SetAcctSvcPublicKey([]byte(pub))
		if err != nil {
			t.Error("error setting acct svc public key", err)
		}
	})
}

func TestShareDLTokenCreation(t *testing.T) {
	t.Run("non share dl token should have claim DL = false", func(t *testing.T) {
		token, err := MakeShareAuthToken("12345", "123.456.12.12")
		assert.NoError(t, err)

		claims, err := DecodeToken(token)
		assert.NoError(t, err)
		assert.False(t, claims.DL)
	})

	t.Run("share dl token should have claim DL = true", func(t *testing.T) {
		token, err := MakeShareDLToken("12345")
		assert.NoError(t, err)

		claims, err := DecodeToken(token)
		assert.NoError(t, err)
		assert.True(t, claims.DL)
	})

	t.Run("share dl token should not have account id", func(t *testing.T) {
		token, err := MakeShareDLToken("12345")
		assert.NoError(t, err)

		claims, err := DecodeToken(token)
		assert.NoError(t, err)
		assert.Empty(t, claims.AccountID)
	})

	t.Run("share dl token for record streaming should not have account id", func(t *testing.T) {
		token, err := MakeShareDLRecordStreamingToken("12345", "accountID")
		assert.NoError(t, err)

		claims, err := DecodeToken(token)
		assert.NoError(t, err)
		assert.NotEmpty(t, claims.AccountID)
	})
}
