package accountservice

import "github.com/golang-jwt/jwt/v4"

type GlobalClaims struct {
	AcctID     string            `json:"aid"`
	AcctType   string            `json:"type"` // type of account, e.g. 'Patient' or 'Physician'
	Users      map[uint16]uint64 `json:"uu"`   //regionID -> userID
	MainRegion uint16            `json:"mr"`
	IPAddr     string            `json:"ip"`
	Plan       string            `json:"p"`
	OrderID    string            `json:"oid"`
	PlanID     uint64            `json:"pid"`
	Features   []uint64          `json:"f"`
	jwt.RegisteredClaims
}
