package auth

import (
	"errors"
	"fmt"
	"strings"

	"github.com/golang-jwt/jwt/v4"

	"gitlab.com/pockethealth/coreapi/pkg/auth/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

func adaptAcctsTokenToUserToken(jwtString string) (Claims, error) {
	acctClaims, err := ParseAcctsToken(jwtString)
	if err != nil {
		return Claims{}, err
	}

	orderId := acctClaims.OrderID
	return Claims{
		CurrentOrderID: orderId,
		AccountID:      acctClaims.AcctID,
		AccountType:    models.AccountType(acctClaims.AcctType),
		IP:             acctClaims.IPAddr,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: acctClaims.ExpiresAt,
			IssuedAt:  acctClaims.IssuedAt,
		},
		Features: acctClaims.Features,
		PlanID:   acctClaims.PlanID,
	}, nil
}

func ParseAcctsToken(jwtString string) (accountservice.GlobalClaims, error) {
	tokenparts := strings.Split(jwtString, " ")
	if len(tokenparts) > 1 {
		//remove "Bearer "
		jwtString = jwtString[7:]
	}
	var acctClaims accountservice.GlobalClaims
	token, err := jwt.ParseWithClaims(
		jwtString,
		&acctClaims,
		func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodECDSA); !ok {
				return nil, fmt.Errorf(
					"unexpected acctsvc signing method: %+v",
					token.Header["alg"],
				)
			}
			return acctSvcJWTPublicKey, nil
		},
	)

	if err != nil {
		return accountservice.GlobalClaims{}, err
	}

	if !token.Valid {
		return accountservice.GlobalClaims{}, errors.New("token not valid")
	}
	return acctClaims, nil
}
