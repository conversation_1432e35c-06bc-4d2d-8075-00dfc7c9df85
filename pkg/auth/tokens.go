package auth

import (
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/sirupsen/logrus"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/signature"
)

var signingMethod = jwt.SigningMethodHS256

type Claims struct {
	AccountID             string
	AccountType           models.AccountType `json:"type"`
	CurrentOrderID        string             `json:"oid"`
	ProfileID             int                `json:"p"`
	PatientID             string             `json:"pt"`
	IP                    string             `json:"ip"`
	ShareID               string             `json:"s"`
	TransferID            string
	ImageID               string
	SessionID             string
	SubscriptionType      string
	DL                    bool                     `json:"dl"` // will default to false.
	IncompleteRequestID   string                   `json:"incomplete_request_id"`
	IncompleteRequestData models.IncompleteRequest `json:"incomplete_request_data"`
	Features              []uint64
	PlanID                uint64
	ConsentID             string
	ChallengeAccountID    string
	jwt.RegisteredClaims
}

func (c *Claims) Verify() bool {
	return true
}

var (
	secretKey             []byte
	regionRouterPublicKey *ecdsa.PublicKey
	acctSvcPublicKey      *ecdsa.PublicKey
	acctSvcJWTPublicKey   *ecdsa.PublicKey
	regionID              uint16
	UserTokenExpiryHours  = 1 // not const because modified in a test
)

const (
	ShareTokenExpiryHours    = 2
	DLTokenExpiryMinutes     = 20
	HRUploadTokenExpiryHours = 1
)

const verifiedConsentTokenExpiryMinutes = 10

func SetSecretKey(key []byte) {
	secretKey = key
}

func SetRegionRouterPublicKey(pubkey []byte) error {
	pub, err := parsePublicKey(pubkey)
	if err != nil {
		return err
	}
	regionRouterPublicKey = pub
	return nil
}

func SetAcctSvcPublicKey(pubkey []byte) error {
	pub, err := parsePublicKey(pubkey)
	if err != nil {
		return err
	}
	acctSvcPublicKey = pub
	return nil
}

func SetAcctSvcJWTPublicKey(pubkey []byte) error {
	pub, err := parsePublicKey(pubkey)
	if err != nil {
		return err
	}
	acctSvcJWTPublicKey = pub
	return nil
}

func parsePublicKey(pubkey []byte) (*ecdsa.PublicKey, error) {
	block, _ := pem.Decode(pubkey)
	if block == nil {
		return nil, errors.New("failed to decode PEM public key")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, errors.New("failed to parse public key")
	}
	switch pub := pub.(type) {
	case *ecdsa.PublicKey:
		return pub, nil
	default:
		return nil, errors.New("unsupported public key type; expected ecdsa.PublicKey")
	}
}

func SetRegionID(id uint16) {
	regionID = id
}

// for testing purpose to generate a token that contains accountID
func MakeAccountAuthToken(accountID string, ip string) string {
	claims := getClaimsForAccountToken(accountID, ip, models.ACCOUNT_TYPE_PATIENT)
	jwt, _ := generateTokenFromClaims(claims)
	return jwt
}

// for testing purpose to generate a token that contains accountID and account type physician
func MakePhysicianAccountAuthToken(accountID string, ip string) string {
	claims := getClaimsForAccountToken(accountID, ip, models.ACCOUNT_TYPE_PHYSICIAN)
	jwt, _ := generateTokenFromClaims(claims)
	return jwt
}

// Determine if the jwtv4 token has been created since <duration> ago
func IsFreshAcctsToken(token string, duration time.Duration) (bool, error) {
	claims, err := ParseAcctsToken(token)
	if err != nil || claims.IssuedAt.Unix() == 0 {
		return false, errors.New("cannot verify token age")
	}

	if (time.Now().Unix() - claims.IssuedAt.Unix()) > int64(duration.Seconds()) {
		return false, nil
	}
	return true, nil
}

func MakeShareAuthToken(shareID string, ip string) (string, error) {
	claims := Claims{
		ShareID: shareID,
		IP:      ip,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(ShareTokenExpiryHours)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeChallengeUnlockToken(transferId string, acctId string, ip string) (string, error) {
	claims := Claims{
		ChallengeAccountID: acctId,
		TransferID:         transferId,
		IP:                 ip,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(UserTokenExpiryHours)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeImageAuthToken(imageId string) (string, error) {
	claims := Claims{
		ImageID: imageId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(UserTokenExpiryHours)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeShareDLRecordStreamingToken(shareID string, accountID string) (string, error) {
	return makeShareDLTokenBase(shareID, accountID)
}

func MakeShareDLToken(shareID string) (string, error) {
	return makeShareDLTokenBase(shareID, "")
}

// TODO: post profiles migration clean-up
// deprecate profileId, no longer need to encode into token
func MakeHealthRecordUploadToken(
	patientId string,
	accountId string,
	sessionId string,
) (string, error) {
	claims := Claims{
		AccountID: accountId,
		ProfileID: 0, // deprecated
		PatientID: patientId,
		SessionID: sessionId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(HRUploadTokenExpiryHours)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeIncompleteReqEmailAuthToken(incompleteRequestID string) (string, error) {
	claims := Claims{
		IncompleteRequestID: incompleteRequestID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(7 * 24)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeIncomplReqDataToken(data models.IncompleteRequest) (string, error) {
	claims := Claims{
		IncompleteRequestData: data,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(7 * 24)),
		},
	}
	return generateTokenFromClaims(claims)
}

func MakeVerifiedConsentToken(consentId string, ip string) (string, error) {
	claims := Claims{
		IP:        ip,
		ConsentID: consentId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(
				datetime.GetNowPlusXMinutes(verifiedConsentTokenExpiryMinutes),
			),
			IssuedAt: jwt.NewNumericDate(time.Now()),
		},
	}
	return generateTokenFromClaims(claims)
}

// DecodeBearerToken
func DecodeBearerToken(tokenHeader string) (Claims, error) {
	// token should be in format "Bearer <token>"
	var token string
	headerList := strings.Split(tokenHeader, " ")
	if len(headerList) != 2 {
		err := errors.New("bad token format")
		logrus.
			WithField("tokenHeader", tokenHeader).
			WithError(err)
		return Claims{}, err
	} else {
		token = headerList[1]
	}

	var issuer string
	claims := jwt.RegisteredClaims{}
	parsedTok, _, err := new(jwt.Parser).ParseUnverified(token, &claims)
	if err != nil {
		return Claims{}, err
	}
	if stdClaims, ok := parsedTok.Claims.(*jwt.RegisteredClaims); ok {
		issuer = stdClaims.Issuer
	} else {
		return Claims{}, err
	}

	switch issuer {
	case "acctsvc":
		claims, err := adaptAcctsTokenToUserToken(token)
		if err != nil {
			logrus.
				WithField("claims", claims).
				WithError(err).
				Debug("DecodeBearerToken() used acctsvc JWT path")
		}
		return claims, err
	}

	return DecodeToken(token)
}

func DecodeToken(token string) (Claims, error) {
	parseFunc := func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			err := fmt.Errorf("unexpected signing method: %+v", token.Header["alg"])
			logrus.
				WithError(err).
				Debug("DecodeToken() faile")
			return nil, err
		}
		return secretKey, nil
	}

	claims := Claims{}
	if token, err := jwt.ParseWithClaims(token, &claims, parseFunc); err != nil {
		return claims, err
	} else {
		if token.Valid {
			return claims, nil
		} else {
			return claims, errors.New("token not valid")
		}
	}
}

func DecodeAccountToken(tokenHeader string) (accountId string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", err
	}

	if claims.AccountID == "" {
		return "", errors.New("token invalid or not issued by acctsvc")
	}

	return claims.AccountID, nil
}

func DecodeShareViewerToken(tokenHeader string) (shareId string, ip string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", "", err
	}
	if claims.ShareID == "" {
		return "", "", errors.New("not a valid share token")
	}

	return claims.ShareID, claims.IP, nil
}

func DecodeChallengeUnlockToken(
	tokenHeader string,
) (transferId string, accountID string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", "", err
	}

	if claims.ChallengeAccountID == "" {
		return "", "", errors.New("not a valid transfer token")
	}

	return claims.TransferID, claims.ChallengeAccountID, nil
}

func DecodeHealthRecordsUploadToken(
	tokenHeader string,
) (acctId string, patientId string, sessionId string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", "", "", err
	}
	// TODO: post profiles migration clean-up
	// no longer need to check profile id
	if (claims.ProfileID == 0 && claims.PatientID == "") || claims.SessionID == "" ||
		claims.AccountID == "" {
		return "", "", "", errors.New("not a valid health records upload token")
	}

	return claims.AccountID, claims.PatientID, claims.SessionID, nil
}

func DecodeIncompleteReqEmailToken(tokenHeader string) (incompleteRequestId string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", err
	}
	if claims.IncompleteRequestID == "" {
		return "", errors.New("not a valid incomplete request email token")
	}

	return claims.IncompleteRequestID, nil
}

func DecodeIncomplReqDataToken(token string) (models.IncompleteRequest, error) {
	claims, err := DecodeToken(token)
	return claims.IncompleteRequestData, err
}

func DecodeVerifiedConsentToken(tokenHeader string) (consentId string, err error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return "", err
	}
	if claims.ConsentID == "" {
		return "", errors.New("not a valid verified consent token")
	}

	return claims.ConsentID, nil
}

// ValidateRRSignature - Validate payload signature with RegionRouter public key
func ValidateRRSignature(payload []byte, signatureStr string) bool {
	return validateSignature(payload, signatureStr, regionRouterPublicKey)
}

// ValidateAcctSvcSignature - Validate payload signature with AcctSvc public key
func ValidateAcctSvcSignature(r *http.Request, signatureStr string) bool {
	return httpclient.VerifyAutoSignedRequest(r, acctSvcPublicKey)
}

func validateSignature(payload []byte, signatureStr string, publicKey *ecdsa.PublicKey) bool {
	return signature.VerifyBase64Enc(publicKey, payload, signatureStr)
}

func IsAuthForFeature(tokenHeader string, featureId uint64) (bool, error) {
	claims, err := DecodeBearerToken(tokenHeader)
	if err != nil {
		return false, err
	}
	for _, f := range claims.Features {
		if f == featureId {
			return true, nil
		}
	}

	return false, nil
}

func makeShareDLTokenBase(shareID string, accountID string) (string, error) {
	claims := Claims{
		ShareID: shareID,
		DL:      true,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXMinutes(DLTokenExpiryMinutes)),
		},
	}
	if accountID != "" {
		claims.AccountID = accountID
	}
	return generateTokenFromClaims(claims)
}

func getClaimsForAccountToken(
	accountID string,
	ip string,
	accountType models.AccountType,
) Claims {
	return Claims{
		AccountID:   accountID,
		AccountType: accountType,
		IP:          ip,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(datetime.GetNowPlusXHours(UserTokenExpiryHours)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}
}

func generateTokenFromClaims(claims Claims) (string, error) {
	tok := jwt.NewWithClaims(signingMethod, claims)
	return tok.SignedString(secretKey)
}
