package refer

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/util/httperror"
)

// A ReferApiController binds http requests to an api service and writes the service results to the http response
type PublicReferApiController struct {
	referService coreapi.ReferApiServicer
}

// NewReferApiController creates a default api controller
func NewPublicReferApiController(s coreapi.ReferApiServicer) coreapi.PublicReferApiRouter {
	return &PublicReferApiController{referService: s}
}

// Routes returns all of the api route for the ReferApiController
func (c *PublicReferApiController) Routes() coreapi.Routes {
	return coreapi.Routes{
		{
			Name:        "GetEmailReferThrottleByToken",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{token}",
			HandlerFunc: c.GetEmailReferThrottleByToken,
		},
		{
			Name:        "PostEmailRefer",
			Method:      strings.ToUpper("Post"),
			Pattern:     "/{token}",
			HandlerFunc: c.PostEmailRefer,
		},
	}
}

func (c *PublicReferApiController) GetPathPrefix() string {
	return "/v1/refer"
}

func (c *PublicReferApiController) GetMiddleware() []func(next http.Handler) http.Handler {
	return []func(next http.Handler) http.Handler{} //no middleware for Refer, return empty list
}

// GetEmailRreferThrottleByToken - Get a provider
func (c *PublicReferApiController) GetEmailReferThrottleByToken(
	w http.ResponseWriter,
	r *http.Request,
) {
	params := mux.Vars(r)
	token := params["token"]

	if token == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		return
	}

	//get email count by acct
	result, err := c.referService.GetEmailReferThrottleByToken(r.Context(), token)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	coreapi.EncodeJSONResponse(r.Context(), result, nil, w)
}

// PostEmailRefer - Get consent data for a consentid
func (c *PublicReferApiController) PostEmailRefer(w http.ResponseWriter, r *http.Request) {

	params := mux.Vars(r)
	token := params["token"]

	if token == "" {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_BAD_QUERY_PARAM, http.StatusBadRequest)
		return
	}

	referral := coreapi.PublicReferral{}
	err := json.NewDecoder(r.Body).Decode(&referral)
	if err != nil {
		httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_FOUND, http.StatusNotFound)
		return
	}

	// send refer email
	err = c.referService.PostEmailRefer(r.Context(), token, referral)
	if err != nil {
		if err.Error() == errormsgs.ERR_NOT_FOUND {
			httperror.ErrorWithLog(w, r, errormsgs.ERR_NOT_FOUND, http.StatusNotFound)
			return
		}
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

}
