//go:build integration
// +build integration

package refer

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/cio_email"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	cioEmail "gitlab.com/pockethealth/phutils/v10/pkg/email"

	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/coreapi/pkg/util/languageproviders"
	"golang.org/x/text/language"

	_ "github.com/go-sql-driver/mysql"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/users"
	"gitlab.com/pockethealth/coreapi/pkg/util/emailToken"
	phlanguage "gitlab.com/pockethealth/phutils/v10/pkg/language"
)

func setupRefer(t *testing.T, db *sql.DB) (string, string) {
	acctId := "139"
	randString := strconv.Itoa(rand.Int())
	email := fmt.Sprintf("<EMAIL>", randString)
	validEmailToken := "mzI36w5QjR8UnPc904ma37GA1Ap7gV1BrsAhfw9lb0MP5tQB6b1cuUjUamKTu0nFjxsm7C2JiF0Susbzue7nFt4Ufg=="
	var res sql.Result
	var err error
	res, err = db.Exec(
		`INSERT INTO email_token_log (email_token, account_id, email, email_type, timestamp) VALUES (?, ?, ?, ?, ?)`,
		validEmailToken,
		acctId,
		email,
		"",
		time.Now(),
	)
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}

	logId, err := res.LastInsertId()
	if err != nil {
		t.Fatalf("unable to set up test data: %q", err.Error())
	}
	t.Cleanup(func() {
		db.Exec(`DELETE FROM email_token_log WHERE account_id=? and id >=?`, acctId, logId)
	})

	return email, validEmailToken
}

func TestRefers(t *testing.T) {
	db := testutils.SetupTestDB(t)

	// Mock (empty) variables for user service
	i18nBundle := i18n.NewBundle(language.English)
	mockProvider := phlanguage.LanguageTagProvider{
		GetLanguageTag: func(ctx context.Context, id interface{}) language.Tag { return language.English },
	}
	mockLangProviders := languageproviders.LanguageTagProviders{
		AccountId: mockProvider,
		ClinicId:  mockProvider,
		OrgId:     mockProvider,
	}
	mailer, mockclient := cioEmail.NewMock()
	mockEmailSender := cio_email.ConfigureMail(mailer, map[string]string{"friend_referral": "21"})
	mockSupportedLanguages := map[string]string{"en": "English (United States)"}
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	roiServiceMock := &roiservice.RoiServiceMock{}
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)

	userService := users.NewUsersApiService(
		db,
		azureUtils.ContainerClient{},
		mockEmailSender,
		"qa.pocket.health",
		i18nBundle,
		mockLangProviders,
		mockSupportedLanguages,
		accountServiceMock,
		orgServiceMock,
		planServiceMock,
		nil,
		false,
		nil,
		nil,
		providersServiceMock,
		nil,
		nil,
		nil,
		examService,
		nil,
		nil,
		roiServiceMock,
	)

	emailTokenKey := "1234567890abcdef"
	service := NewReferApiService(
		db,
		userService,
		&accountservice.AcctSvcMock{},
		1000,
		emailToken.EmailToken{Key: emailTokenKey},
	)

	t.Run("valid emailt token", func(t *testing.T) {
		_, validEmailToken := setupRefer(t, db)

		ctx := context.Background()
		emailThrottle, err := service.GetEmailReferThrottleByToken(ctx, validEmailToken)
		if err != nil {
			t.Error("unable to get email throttle:", err.Error())
		}

		if emailThrottle == nil {
			t.Error("expected an email throttle")
		}
	})

	t.Run("invalid emailt token", func(t *testing.T) {
		invalidEmailToken := "this-is-invalid-token"

		setupRefer(t, db)
		ctx := context.Background()
		emailThrottle, err := service.GetEmailReferThrottleByToken(ctx, invalidEmailToken)

		if err == nil {
			t.Error("valid fake token is not failed:")
		}

		if emailThrottle != nil {
			t.Error("expected an nil email throttle")
		}
	})

	t.Run("send email refer", func(t *testing.T) {
		setupRefer(t, db)
		email, validEmailToken := setupRefer(t, db)
		ctx := context.Background()
		referral := coreapi.PublicReferral{
			Emails: []string{email},
		}
		err := service.PostEmailRefer(ctx, validEmailToken, referral)
		if err != nil {
			t.Error("unable to send email:", err.Error())
		}

		logs := mockclient.CallLogs.Get("SendEmail")
		require.True(t, logs.CalledOnce())
	})
}
