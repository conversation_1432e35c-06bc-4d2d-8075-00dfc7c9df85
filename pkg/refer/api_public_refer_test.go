package refer

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

func TestEmailRefer(t *testing.T) {

	t.Run("check email refer throttle by token", func(t *testing.T) {

		service := NewMockReferApiService()

		controller := NewPublicReferApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/refer/token1", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

	t.Run("check email refer throttle by empty token", func(t *testing.T) {

		service := NewMockReferApiService()

		controller := NewPublicReferApiController(service)
		router, err := coreapi.NewRouter(controller)
		if err != nil {
			t.Fatal(err)
		}
		req, err := http.NewRequest("GET", "/v1/refer/", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusNotFound
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})

}
