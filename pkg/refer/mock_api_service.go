package refer

import (
	"context"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// MockReferApiService is a service that implents the logic for the ReferApiServicer for testing
type MockReferApiService struct {
}

func NewMockReferApiService() coreapi.ReferApiServicer {
	return &MockReferApiService{}
}

// Get email refer throttle by token
func (s *MockReferApiService) GetEmailReferThrottleByToken(
	ctx context.Context,
	token string,
) (interface{}, error) {
	return &coreapi.PublicReferEmailThrottle{ThrottleNumber: 5, AvailableNumber: 2}, nil
}

// send out email referral for public access user
func (s *MockReferApiService) PostEmailRefer(
	ctx context.Context,
	token string,
	referral coreapi.PublicReferral,
) error {
	return nil
}
