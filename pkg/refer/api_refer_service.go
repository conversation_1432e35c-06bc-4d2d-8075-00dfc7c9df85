package refer

import (
	"context"
	"database/sql"
	"errors"
	"strconv"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	mysql "gitlab.com/pockethealth/coreapi/pkg/mysql/refer"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/emailToken"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// ReferApiService is a service that implents the logic for the ReferApiServicer
// This service should implement the business logic for every endpoint for the ReferApi API.
// Include any external packages or services that will be required by this service.
type ReferApiService struct {
	sqldb               *sql.DB
	userService         coreapi.UsersApiServicer
	acctServiceClient   accountservice.AccountService
	emailThrottlePerday int
	emailToken          emailToken.EmailToken
}

// NewReferApiService creates a default api service
func NewReferApiService(
	db *sql.DB,
	u coreapi.UsersApiServicer,
	as accountservice.AccountService,
	emailThrottlePerday int,
	emailToken emailToken.EmailToken,
) coreapi.ReferApiServicer {
	return &ReferApiService{
		sqldb:               db,
		userService:         u,
		acctServiceClient:   as,
		emailThrottlePerday: emailThrottlePerday,
		emailToken:          emailToken,
	}
}

// GetProviderById - Get a provider
func (s *ReferApiService) GetEmailReferThrottleByToken(
	ctx context.Context,
	token string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("token", token)

	referEmailToken, err := s.emailToken.ParseEmailToken(token)
	if err != nil {
		lg.WithError(err).Error("ParseEmailToken is failed")
		return nil, err
	}

	// Find out how many refer emails have been sent out in the past 24 hours with the specific token.
	timestamp := time.Now().AddDate(0, 0, -1)
	var count int
	count, err = mysql.GetEmailTokenCountByAccountId(
		ctx,
		s.sqldb,
		referEmailToken.AccountId,
		token,
		timestamp,
	)

	if err != nil {
		lg.WithError(err).
			WithField("account_id", referEmailToken.AccountId).
			Error("mysql.GetEmailTokenCountByAccountId failed")
		return nil, err
	}

	//get remain avaialble number
	if count >= s.emailThrottlePerday {
		count = 0
	} else {
		count = s.emailThrottlePerday - count
	}

	return &coreapi.PublicReferEmailThrottle{
		ThrottleNumber:  s.emailThrottlePerday,
		AvailableNumber: count,
	}, nil
}

// send out email referral for public access user
func (s *ReferApiService) PostEmailRefer(
	ctx context.Context,
	token string,
	referral coreapi.PublicReferral,
) error {
	lg := logutils.DebugCtxLogger(ctx).WithField("token", token)

	//get email throlle by token
	result, err := s.GetEmailReferThrottleByToken(ctx, token)
	emailThrottle := result.(*coreapi.PublicReferEmailThrottle)
	if err != nil {
		lg.WithError(err).Error("GetEmailReferThrottleByToken is failed")
		return err
	}

	if emailThrottle.AvailableNumber < 1 {
		err = errors.New(
			"you've reached the daily referral limit of " + strconv.Itoa(
				emailThrottle.ThrottleNumber,
			) + ", Please login to refer more friends or wait until the next day.",
		)
		lg.WithError(err).Error("limit hit")
		return err
	}

	// verify email refer availble number for today
	if len(referral.Emails) > emailThrottle.AvailableNumber {
		err = errors.New(
			"you're reaching the daily referral limit of " + strconv.Itoa(
				emailThrottle.ThrottleNumber,
			),
		)

		lg.WithError(err).Error("The count of emails is exceed the throlttle!")
		return err
	}

	referEmailToken, err := s.emailToken.ParseEmailToken(token)
	if err != nil {
		lg.WithError(err).Error("ParseEmailToken is failed")
		return err
	}

	for _, email := range referral.Emails {
		// send out email
		err := s.userService.PostFriendReferral(
			ctx,
			referEmailToken.AccountId,
			email,
		)
		if err != nil {
			lg.WithError(err).
				WithField("account_id", referEmailToken.AccountId).
				Error("send friend referral failed")
		} else {
			//insert email token log if email sent out

			//find out email type
			emailType := "ReferAFriend"
			//insert email refer log
			err = mysql.InsertEmailTokenLog(ctx, s.sqldb, token, referEmailToken.AccountId, email, emailType, time.Now())
			if err != nil {
				lg.WithError(err).WithField("AccountId", referEmailToken.AccountId).Error("insert email token failed")
			}
		}
	}
	return nil
}
