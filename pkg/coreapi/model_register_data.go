/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"fmt"
	"time"
)

type RegisterData struct {
	Email string `json:"email"`

	Password string `json:"password"`

	//the below are not required for registration thru the request form or transfer
	FirstName   string    `json:"first_name,omitempty"`
	LastName    string    `json:"last_name,omitempty"`
	DOB         time.Time `json:"dob,omitempty"` //expect ISO8601
	RedirectUrl string    `json:"redirect_url,omitempty"`

	// below is for physician accounts
	ShareId string `json:"share_id,omitempty"`
}

func (r *RegisterData) Valid() error {
	if r.Email == "" || r.Password == "" {
		return fmt.Errorf("missing email and/or password")
	}
	return nil
}
