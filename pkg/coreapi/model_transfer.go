/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type Transfer struct {
	PaymentStatus PaymentStatus `json:"paymentStatus,omitempty"`

	FeeInfo FeeInfo `json:"feeInfo,omitempty"`

	ProviderName string `json:"providerName,omitempty"`

	ProviderId int64 `json:"providerId,omitempty"`

	Exams []TransferExamSummary `json:"exams,omitempty"`

	OrgId int64 `json:"orgId,omitempty"`

	Base64Logo string `json:"base64Logo,omitempty"`

	OrgName string `json:"orgName,omitempty"`
}

type DeleteE2E struct {
	RequestId     int    `json:"requestId"`
	EnrollmentId  int    `json:"enrollmentId"`
	TransferId    string `json:"transferId"`
	UserId        int    `json:"userId"`
	AccountId     string `json:"accountId"`
	DeleteAccount bool   `json:"deleteAccount"`
}
