package coreapi

import (
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"slices"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/util/typeconverter"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

// PhysicianPatientSummary holds detailed information about each patient
// that has records that a physician has access to.
// Summaries are split up by origin.
// Records shared by a patient are grouped by the pockethealth patient ID.
// The summary object contains a pockethealth patient ID, but no provider ID, org name or share IDs
// Records shared by a provider, via record streaming or default workflow,
// are grouped by providerID, patient name and DOB.
// The summary object contains a provider ID, org name and list of share IDs, but no pockethealth patient ID
type PhysicianPatientSummary struct {
	ID string `json:"id"` // unique identifier of the physician patient summary object - based on patientID of patient shares, provider id and patient data for provider shares

	PatientID string `json:"patientId,omitempty"` // id of pockethealth account owner if share was initiated by a patient

	ProviderID int64    `json:"providerId,omitempty"` // legacy id of provider if share was initiated by a provider
	OrgName    string   `json:"orgName,omitempty"`    // name of provider if share was initiated by a provider
	ShareIDs   []string `json:"shareIds,omitempty"`   // list of share ids for patient if shares were initiated by a provider

	FirstName   string             `json:"firstName"`
	LastName    string             `json:"lastName"`
	PatientName models.PatientName `json:"patientName"`

	DOB          string `json:"dob"`
	RecordNumber int    `json:"recordNum"`

	ReferringPhysicians []models.PatientName `json:"referringPhysicians"` // list of referring physician names of records for this patient

	// PhysicianAccountID of the physician that is associated with the study.
	PhysicianAccountID string `json:"physicianAccountId,omitempty"`
	// PermissionGroups associated with the study.
	PermissionGroups []PermissionGroup `json:"permissionGroups,omitempty"`
}

// NewPhysicianPatientSummary creates a new basic PhysicianPatientSummary object
// for a physician share, a patient share or a record streaming study based on a ShareMetadata object.
// PhysicianPatientSummary objects have an ID that corresponds to the PatientID
// for patient shares. For provider shares, the id consists of the provider id, patient name and dob.
// PhysicianPatientSummary objects for patient shares have a PatientID
// that corresponds to the id of the owner of the PocketHealth patient account
// that initiated the share. They have no ProviderId, OrgName or ShareIDs
// PhysicianPatientSummary objects for physician shares have a ProviderId and OrgName
// that corresponcs to the name of the provider that initiated the share
// and a list of ShareIDs. They do not have a PatientID.
func NewPhysicianPatientSummary(
	ctx context.Context,
	shareMetadata ShareMetadata,
) (summary PhysicianPatientSummary, err error) {
	firstName, lastName := dcmtools.ParseFirstLastName(ctx, shareMetadata.PatientName)
	summary = PhysicianPatientSummary{
		FirstName: firstName,
		LastName:  lastName,
		PatientName: models.PatientName{
			DicomName:          shareMetadata.PatientName,
			FirstAndMiddleName: firstName,
			LastName:           lastName,
		},
		DOB:                 dcmtools.ParseDob(shareMetadata.PatientDOB, "/"),
		RecordNumber:        shareMetadata.ExamCount + shareMetadata.HealthRecordCount,
		ReferringPhysicians: []models.PatientName{},
	}

	summaryID, err := shareMetadata.SummaryID(ctx)
	if err != nil {
		return PhysicianPatientSummary{}, err
	}
	summary.ID = summaryID

	if shareMetadata.PhPatientID != "" {
		summary.PatientID = shareMetadata.PhPatientID
	} else if shareMetadata.LegacyProviderID != 0 {
		summary.ProviderID = shareMetadata.LegacyProviderID
		summary.OrgName = shareMetadata.OrgName
		summary.ShareIDs = []string{shareMetadata.ShareID}
	}

	summary.ReferringPhysicians = getReferringPhysicianNames(ctx, shareMetadata.ReferringPhysicians)
	summary.PhysicianAccountID = shareMetadata.PhysicianAccountID
	summary.PermissionGroups = shareMetadata.PermissionGroups
	return summary, nil
}

// GroupShareMetadata takes a list of shareMetadata and groups them into
// PhysicianPatientSummary objects.
// Entries are grouped by their PhysicianPatientSummary id.
func GroupShareMetadata(
	ctx context.Context,
	metadata []ShareMetadata,
) ([]PhysicianPatientSummary, error) {
	patientSummaryMap := make(map[string]PhysicianPatientSummary)

	for _, shareMetadata := range metadata {
		summaryID, err := shareMetadata.SummaryID(ctx)
		if err != nil {
			return []PhysicianPatientSummary{}, err
		}

		if value, hasKey := patientSummaryMap[summaryID]; !hasKey {
			// add patient summary for key
			patientSummaryMap[summaryID], err = NewPhysicianPatientSummary(ctx, shareMetadata)
			if err != nil {
				return []PhysicianPatientSummary{}, err
			}
		} else {
			// add share metadata to patient summary
			value.add(ctx, shareMetadata)
			patientSummaryMap[summaryID] = value
		}
	}
	return typeconverter.MapToList(patientSummaryMap), nil
}

// add merges a ShareMetadata object with a PhysicianPatientSummary summary
// by adding their record numbers, share IDs and referring physicians.
// Referring physicians are only added if they are valid (non-empty)
// This function can be used to group record metadata by patient.
// This function is NOT idempotent! Share ID and referring physician lists are unique,
// but record numbers would be incremented repeatedly.
func (o *PhysicianPatientSummary) add(ctx context.Context, metadata ShareMetadata) {
	o.RecordNumber += metadata.ExamCount + metadata.HealthRecordCount
	if metadata.PhPatientID == "" && !slices.Contains(o.ShareIDs, metadata.ShareID) {
		o.ShareIDs = append(o.ShareIDs, metadata.ShareID)
	}
	for _, referringPhysicianName := range getReferringPhysicianNames(ctx, metadata.ReferringPhysicians) {
		if err := referringPhysicianName.Validate(); err == nil &&
			!slices.Contains(o.ReferringPhysicians, referringPhysicianName) {
			o.ReferringPhysicians = append(o.ReferringPhysicians, referringPhysicianName)
		}
	}
}

// PatientSummaryIdHash takes a patient's name and date of birth as stored in their respective DICOM tags
// and a provider id and converts them into an identifier for the patient.
// For obvuscation, the id being returned is hashed.
// NOTE: This identifier is used to identify patients without a pockethealth account,
// in place of the account's id. It is used e.g. to group provider shares by patient.
// For shares made by a patient with a pockethealth account, USE ACCOUNT ID INSTEAD
// Since different providers might use different MRNs per patient, shares are not grouped across providers.
func PatientSummaryIdHash(
	patientName string,
	patientDOB string,
	providerID int64,
) (string, error) {
	dob := dcmtools.ParseDob(patientDOB, "/")
	caser := cases.Title(language.English)
	name := caser.String(patientName)

	idString := fmt.Sprintf("%v_%s_%s", providerID, name, dob)
	h := sha256.New()
	_, err := h.Write([]byte(idString))
	if err != nil {
		return "", errors.New("failed to create hash for patient summary id")
	}
	return fmt.Sprintf("%x", h.Sum(nil)), nil
}

// ShareMetadata is used as a helper object to compute patient summary objects for physician accounts.
// It contains information on a single share (or record streaming study) that a physician has access to
// and can be grouped into a PhysicianPatientSummary.
// It either contains a PhPatientID AND ShareType "PS"
// OR
// it contains a LegacyProviderID, OrgName AND ShareType "P2P"
type ShareMetadata struct {
	ShareID           string
	ShareType         string
	PhPatientID       string
	LegacyProviderID  int64
	OrgName           string
	ExamCount         int
	HealthRecordCount int
	PatientName       string
	// TODO (mp): patient name in DICOM name format
	PatientDICOMName    models.PatientName
	PatientDOB          string
	ReferringPhysicians []string
	PhysicianAccountID  string
	PermissionGroups    []PermissionGroup
}

// PermissionGroup holds information about a group that a study is associated
// with.
type PermissionGroup struct {
	GroupID   int64  `json:"groupId"`
	GroupName string `json:"groupName"`
}

// NewShareMetadata returns a new ShareMetadata object
// For patient shares (phPatientID != nil),
// it sets ShareType "PS" and sets no legacyProviderID or orgName
// For provider shares (phPatientID == nil AND legacyProviderID != 0),
// it sets ShareType "P2P" and sets no phPatientID
func NewShareMetadata(
	shareID string,
	phPatientID string,
	legacyProviderID int64,
	orgName string,
	examCount int,
	healthRecordCount int,
	patientName string,
	patientDOB string,
	referringPhysicians []string,
) ShareMetadata {
	var shareType string
	if phPatientID != "" {
		shareType = "PS"
		// ensure that no legacy provider ID or org name are set for patient share
		legacyProviderID = 0
		orgName = ""
	} else if legacyProviderID != 0 {
		shareType = "P2P"
		// ensure that no pockethealth patient id is set for provider share
		phPatientID = ""
	}

	return ShareMetadata{
		ShareID:             shareID,
		ShareType:           shareType,
		PhPatientID:         phPatientID,
		LegacyProviderID:    legacyProviderID,
		OrgName:             orgName,
		ExamCount:           examCount,
		HealthRecordCount:   healthRecordCount,
		PatientName:         patientName,
		PatientDOB:          patientDOB,
		ReferringPhysicians: referringPhysicians,
		PermissionGroups:    []PermissionGroup{},
	}
}

// SummaryId returns an ID that can be used for grouping ShareMetadata objects into PhysicianPatientSummaries
// SummaryId is equal to PhPatientID for patient shares
// For provider shares or record streaming studies, SummaryId is based on the patient name, dob and the legacy provider id
func (o *ShareMetadata) SummaryID(ctx context.Context) (string, error) {
	if o.PhPatientID != "" {
		return o.PhPatientID, nil
	} else if o.LegacyProviderID != 0 {
		id, err := PatientSummaryIdHash(o.PatientName, o.PatientDOB, o.LegacyProviderID)
		if err != nil {
			return "", err
		}
		return id, nil
	}
	return "", errors.New("patient id and legacy provider id cannot both be empty")
}

// getReferringPhysicianNames returns a list of PatientName objects
// for a given list of referringPhysicians strings.
// PatientName objects are only returned for valid (non-empty and non-duplicate) strings.
func getReferringPhysicianNames(
	ctx context.Context,
	referringPhysicians []string,
) []models.PatientName {
	referringPhysicianNames := make([]models.PatientName, 0)
	for _, referringPhysician := range referringPhysicians {
		referringPhysicianName := models.NewPatientName(ctx, referringPhysician)
		if err := referringPhysicianName.Validate(); err == nil &&
			!slices.Contains(referringPhysicianNames, referringPhysicianName) {
			referringPhysicianNames = append(
				referringPhysicianNames,
				referringPhysicianName,
			)
		}
	}
	return referringPhysicianNames
}
