// From hlth rec svc

package coreapi

import "github.com/samply/golang-fhir-models/fhir-models/fhir"

type Record struct {
	FHIRId string `json:"id"`

	Name string `json:"name"`

	ProfileId int `json:"profileId"`

	PatientId string `json:"patientId"`

	Source RecordSource `json:"source"`

	RecordDate string `json:"recordDate"`

	Description string `json:"description"`

	TypeCode RecordTypeCode `json:"typeCode,omitempty"`

	CreatedDate string `json:"createdDate,omitempty"`

	Filenames []string `json:"filenames"`

	Tag string `json:"tag"`

	DeletedAt string `json:"deletedAt,omitempty"`

	FHIRResourceType fhir.ResourceType `json:"fhir_resource_type"`

	ResourceData interface{} `json:"data"` //some json specific to the fhir resource type
}
