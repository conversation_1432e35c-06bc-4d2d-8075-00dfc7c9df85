package coreapi

import (
	"context"
	"encoding/json"
	"testing"
)

func TestModelExam(t *testing.T) {
	t.Run("ExamRaw type should not be marshalled", func(t *testing.T) {
		ex := ExamRaw{}
		bytes, err := json.Marshal(ex)
		if err == nil {
			t.<PERSON>al("expected marshalling to produce an error but none was")
		}
		if bytes != nil {
			t.<PERSON>al("expected marshalling to produce nil bytes, but got non-nil")
		}
	})

	t.Run("ExamRaw can be adapted to an Exam", func(t *testing.T) {
		exRaw := ExamRaw{
			ExamRawBasic: ExamRawBasic{
				UUID:       "some-id",
				PatientMrn: "some-mrn",
				Phone:      "a phone number",
			},
		}
		ex := exRaw.ToExam(context.Background())
		bytes, err := json.Marshal(ex)
		if err != nil {
			t.Fatalf("marshalling exam type failed: %s", err)
		}
		expected := `{"uuid":"some-id","patientName":{},"reportDelay":0,"referringPhysician":"","size":0,"orgId":0,"facilityFunded":false,"allow_pt_png_dls":false,"patientId":"","unlockStatus":""}`
		if string(bytes) != expected {
			t.Fatalf("expected %s but got %s", expected, string(bytes))
		}
	})

	t.Run(
		"Sensitive fields should be formatted and included in Exam when activated",
		func(t *testing.T) {
			exRaw := ExamRaw{
				ExamRawBasic: ExamRawBasic{
					UUID:                    "some-id",
					PatientMrn:              "some-mrn",
					Phone:                   "**********",
					DICOMExamDate:           "20241126",
					DICOMPatientName:        "TIMMINSON^TIM",
					DICOMBirthDate:          "19000102",
					DICOMReferringPhysician: "JIMMINSON^JIM",
					Description:             "posterior bowflex swoliosis mitosis",
					Sex:                     "F",
					BodyPart:                "toe",
					Modality:                "CT",
					Activated:               true,
				},
			}
			ex := exRaw.ToExam(context.Background())
			bytes, err := json.Marshal(ex)
			if err != nil {
				t.Fatalf("marshalling exam type failed: %s", err)
			}
			expected := `{"uuid":"some-id","patientName":{"dicomName":"TIMMINSON^TIM","firstAndMiddleName":"Tim","lastName":"Timminson"},"examType":"CT Scan","examDate":"Nov 26, 2024","activated":true,"description":"posterior bowflex swoliosis mitosis","dob":"1900/01/02","sex":"F","bodypart":"toe","reportDelay":0,"referringPhysician":"Dr. Jim Jimminson","size":0,"orgId":0,"facilityFunded":false,"modality":"CT","allow_pt_png_dls":false,"patientId":"","unlockStatus":""}`
			if string(bytes) != expected {
				t.Fatalf("expected %s but got %s", expected, string(bytes))
			}
		},
	)

	t.Run("Sensitive fields should not be included Exam when not activated", func(t *testing.T) {
		exRaw := ExamRaw{
			ExamRawBasic: ExamRawBasic{
				UUID:                    "some-id",
				PatientMrn:              "some-mrn",
				Phone:                   "**********",
				DICOMExamDate:           "20241126",
				DICOMPatientName:        "TIMMINSON^TIM",
				DICOMBirthDate:          "19000102",
				DICOMReferringPhysician: "JIMMINSON^JIM",
				Description:             "posterior bowflex swoliosis mitosis",
				Sex:                     "F",
				BodyPart:                "toe",
				Modality:                "CT",
			},
		}
		ex := exRaw.ToExam(context.Background())
		bytes, err := json.Marshal(ex)
		if err != nil {
			t.Fatalf("marshalling exam type failed: %s", err)
		}
		expected := `{"uuid":"some-id","patientName":{},"examType":"CT Scan","examDate":"Nov 26, 2024","reportDelay":0,"referringPhysician":"","size":0,"orgId":0,"facilityFunded":false,"modality":"CT","allow_pt_png_dls":false,"patientId":"","unlockStatus":""}`
		if string(bytes) != expected {
			t.Fatalf("expected %s but got %s", expected, string(bytes))
		}
	})
}
