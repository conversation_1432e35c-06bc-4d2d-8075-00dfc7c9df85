package coreapi

type PaymentIntentRequest struct {
	PlanId uint64 `json:"planId"`

	ProviderId int64 `json:"providerId,omitempty"`

	PaymentType string `json:"paymentType"`

	DisableAutoRenew bool `json:"disableAutoRenew,omitempty"`

	DiscountName string `json:"discount_name,omitempty"`

	AccountID string `json:"accountId"`
}

type PaymentIntentResponse struct {
	OrderId string `json:"orderId"`

	ClientSecret string `json:"clientSecret"`
}

type PaymentSuccessRequest struct {
	OrderId string `json:"orderId"`

	PaymentIntentId string `json:"paymentIntentId"`

	PaymentMethod string `json:"paymentMethod"`

	AccountID string `json:"accountId"`

	Source string `json:"source"`
}

func (paymentIntentInfo *PaymentIntentRequest) Valid() bool {
	if paymentIntentInfo.PlanId == 0 ||
		paymentIntentInfo.PaymentType == "" ||
		paymentIntentInfo.AccountID == "" {
		return false
	}
	return true
}

func (paymentSuccessRequest *PaymentSuccessRequest) Valid() bool {
	if paymentSuccessRequest.OrderId == "" ||
		paymentSuccessRequest.PaymentIntentId == "" ||
		paymentSuccessRequest.PaymentMethod == "" ||
		paymentSuccessRequest.AccountID == "" {
		return false
	}
	return true
}
