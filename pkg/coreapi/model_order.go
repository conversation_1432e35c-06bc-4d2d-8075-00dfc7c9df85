package coreapi

import "time"

type Order struct {
	OrderId       string      `json:"order_id"`
	PlanId        uint64      `json:"plan_id"`
	AutoRenew     bool        `json:"is_auto_renewing"`
	CreatedAt     time.Time   `json:"created_at"`
	ExpiresAt     time.Time   `json:"expires_at"`
	PaymentMethod PaymentCard `json:"payment_method"`
}

type PaymentCard struct {
	LastFour string `json:"lastFour,omitempty"`

	Brand string `json:"brand,omitempty"`

	ExpiryYear uint16 `json:"expiryYear,omitempty"`

	ExpiryMonth uint8 `json:"expiryMonth,omitempty"`
}
