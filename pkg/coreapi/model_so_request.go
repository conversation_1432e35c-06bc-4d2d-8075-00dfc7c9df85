package coreapi

type SORequest struct {
	PriorExams      []string                 `json:"prior_exams"`
	PatientDetail   SORequestPatientDetail   `json:"request_patient_detail"`
	ReferringDetail SORequestReferringDetail `json:"request_referring_detail"`
}

type SORequestPatientDetail struct {
	PatientName        string `json:"patient_name"`
	DoB                string `json:"dob"`
	Ohip               string `json:"ohip"`
	OhipVersionCode    string `json:"ohip_version_code"`
	OhipExpirationDate string `json:"ohip_expiration_date"`
	Phone              string `json:"phone"`
}
type SORequestReferringPhysician struct {
	Cpso      uint   `json:"cpso"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Address   string `json:"address"`
	Phone     string `json:"phone"`
	PhoneExt  string `json:"phone_ext"`
	Fax       string `json:"fax"`
}
type SORequestReferringDetail struct {
	Reason             string                      `json:"reason"`
	Questions          []string                    `json:"questions"`
	ReferringPhysician SORequestReferringPhysician `json:"referring_physician"`
}
