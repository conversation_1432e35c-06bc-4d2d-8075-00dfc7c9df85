package coreapi

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/coreapi/pkg/models"
)

func TestGroupShareMetadata(t *testing.T) {
	ctx := context.Background()
	testcases := []struct {
		name           string
		input          []ShareMetadata
		expectedOutput []PhysicianPatientSummary
	}{
		{
			name: "should not group share metadata for patient shares with different patient id",
			input: []ShareMetadata{
				// same name and dob, but different patient id
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
				{
					ShareID:           "shareId 2",
					ExamCount:         2,
					HealthRecordCount: 3,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 2",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:        "patient id 1",
					PatientID: "patient id 1",
					FirstName: "1",
					LastName:  "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        3,
					ReferringPhysicians: []models.PatientName{},
				},
				{
					ID:        "patient id 2",
					PatientID: "patient id 2",
					FirstName: "1",
					LastName:  "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        5,
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should group share metadata for patient shares with same patient data",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
				{
					ShareID:           "shareId 1",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:        "patient id 1",
					PatientID: "patient id 1",
					FirstName: "1",
					LastName:  "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        8,
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should group share metadata for patient shares with different name but same account id",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
				{
					ShareID:           "shareId 1",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^2",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:        "patient id 1",
					PatientID: "patient id 1",
					FirstName: "1", // should use name of first patient in metadata list for summary
					LastName:  "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        8,
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should group share metadata for patient shares with different dob but same account id",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
				{
					ShareID:           "shareId 1",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					PhPatientID:       "patient id 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:        "patient id 1",
					PatientID: "patient id 1",
					FirstName: "1",
					LastName:  "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11", // should use dob of first patient in metadata list for summary
					RecordNumber:        8,
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should group share metadata for provider shares with same patient data",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
				{
					ShareID:           "shareId 2",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        8,
					ShareIDs:            []string{"shareId 1", "shareId 2"},
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should not add identical share id to list when merging provider share data",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
				{
					ShareID:           "shareId 1",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        8,
					ShareIDs:            []string{"shareId 1"},
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should not group share metadata for provider shares with different name",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
				{
					ShareID:           "shareId 2",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^2",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        3,
					ShareIDs:            []string{"shareId 1"},
					ReferringPhysicians: []models.PatientName{},
				},
				{
					ID:         getPatientId(t, "Patient^2", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "2",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^2",
						FirstAndMiddleName: "2",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        5,
					ShareIDs:            []string{"shareId 2"},
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should not group share metadata for provider shares with different dob",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
				{
					ShareID:           "shareId 2",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        3,
					ShareIDs:            []string{"shareId 1"},
					ReferringPhysicians: []models.PatientName{},
				},
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "2222/22/22",
					RecordNumber:        5,
					ShareIDs:            []string{"shareId 2"},
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should not group share metadata for provider shares with different provider id",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
				},
				{
					ShareID:           "shareId 2",
					ExamCount:         1,
					HealthRecordCount: 4,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  2,
					OrgName:           "provider 2",
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        3,
					ShareIDs:            []string{"shareId 1"},
					ReferringPhysicians: []models.PatientName{},
				},
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(2)),
					ProviderID: 2,
					OrgName:    "provider 2",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:                 "1111/11/11",
					RecordNumber:        5,
					ShareIDs:            []string{"shareId 2"},
					ReferringPhysicians: []models.PatientName{},
				},
			},
		},
		{
			name: "should add all unique and valid referring physicians on grouping",
			input: []ShareMetadata{
				{
					ShareID:           "shareId 1",
					ExamCount:         3,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
					ReferringPhysicians: []string{
						"Valid Name",
						"Duplicate Name",
						"",
					},
				},
				{
					ShareID:           "shareId 1",
					ExamCount:         1,
					HealthRecordCount: 0,
					PatientName:       "Patient^1",
					PatientDOB:        "********",
					LegacyProviderID:  1,
					OrgName:           "provider 1",
					ReferringPhysicians: []string{
						"Duplicate Name",
						"",
						"Unique Name",
					},
				},
			},
			expectedOutput: []PhysicianPatientSummary{
				{
					ID:         getPatientId(t, "Patient^1", "********", int64(1)),
					ProviderID: 1,
					OrgName:    "provider 1",
					FirstName:  "1",
					LastName:   "Patient",
					PatientName: models.PatientName{
						DicomName:          "Patient^1",
						FirstAndMiddleName: "1",
						LastName:           "Patient",
					},
					DOB:          "1111/11/11",
					RecordNumber: 4,
					ShareIDs:     []string{"shareId 1"},
					ReferringPhysicians: []models.PatientName{
						models.NewPatientName(ctx, "Valid Name"),
						models.NewPatientName(ctx, "Duplicate Name"),
						models.NewPatientName(ctx, "Unique Name"),
					},
				},
			},
		},
	}

	for _, testcase := range testcases {
		t.Run(testcase.name, func(t *testing.T) {
			result, err := GroupShareMetadata(context.Background(), testcase.input)
			assert.NoError(t, err)
			assert.ElementsMatch(t, testcase.expectedOutput, result)
		})
	}
}

func getPatientId(
	t *testing.T,
	patientName string,
	dob string,
	providerID int64,
) string {
	patientId, err := PatientSummaryIdHash(patientName, dob, providerID)
	assert.NoError(t, err)
	return patientId
}
