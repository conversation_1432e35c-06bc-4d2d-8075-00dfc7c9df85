package coreapi

import (
	"encoding/json"
	"testing"
)

func TestShareIsValid(t *testing.T) {

	ex1 := Exam{
		ExamId: "exam1",
		Dob:    "1994/03/20",
		Sex:    "Female",
	}

	ex2 := Exam{
		ExamId: "exam2",
		Dob:    "1994/03/20",
		Sex:    "Female",
	}

	ex3 := Exam{
		ExamId: "exam2",
		Dob:    "1994/04/20",
		Sex:    "Female",
	}

	ex4 := Exam{
		ExamId: "exam2",
		Dob:    "1994/03/20",
		Sex:    "Male",
		ExamType: "AA",
		Description: "FakeDescription",
		ExamDate: "2002/10/01",
	}

	ex5 := Exam{
		ExamId: "exam5",
		ExamType: `DX\\KO`,
		Description:`"DENSITOMETRY "HIGH RISK (1/YR)"`,
		ExamDate: "2002/10/01",
	}

	hr1 := Record{
		ProfileId: 100,
		TypeCode:  1, //coreapi.ALLERGY_CODE causes cycle import
	}

	hr2 := Record{
		ProfileId: 99,
		TypeCode:  1, //coreapi.ALLERGY_CODE causes cycle import
	}

	hr3 := Record{
		Name: `John "Jimothy" Doe`,
		Description: "a very very long description",
	}

	// happy cases
	t.Run("valid fax share", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "9057777777",
			Mode:      "all",
		}

		err := share.IsValid()

		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
	})

	t.Run("valid print share", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_PRINT,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
	})

	t.Run("valid email share", func(t *testing.T) {
		share := Share{
			Method:    EMAIL,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "<EMAIL>",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
	})

	//unhappy cases
	t.Run("empty exam list", func(t *testing.T) {
		share := Share{
			Method:    EMAIL,
			ExamList:  []Exam{},
			Recipient: "<EMAIL>",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got empty exam list error but got none")
		}
	})

	t.Run("bad share method", func(t *testing.T) {
		share := Share{
			Method:    "thisisnotasharemethod",
			ExamList:  []Exam{ex1, ex2},
			Recipient: "<EMAIL>",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got bad share method error but got none")
		}
	})

	t.Run("empty email recipient", func(t *testing.T) {
		share := Share{
			Method:    EMAIL,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got missing recipient error but got none")
		}
	})

	t.Run("empty fax recipient", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got missing recipient error but got none")
		}
	})

	t.Run("invalid fax recipient", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2},
			Recipient: "777",
			Mode:      "multiple",
		}

		err := share.IsValid()
		expectedErr := "invalid fax number"
		if err.Error() != expectedErr {
			t.Fatalf("should have got %s error but got %s", expectedErr, err.Error())
		}
	})

	t.Run("exam with different DOB", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2, ex3},
			Recipient: "",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got multiple patients error but got none")
		}
	})

	t.Run("exam with different sex", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2, ex4},
			Recipient: "",
			Mode:      "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got multiple patients error but got none")
		}
	})

	t.Run("health record with different profileId", func(t *testing.T) {
		share := Share{
			Method:        ACCESS_PAGE_PRINT,
			HealthRecords: []Record{hr1, hr2},
			Recipient:     "",
			Mode:          "multiple",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have gotten multiple patients error but got none")
		} else {
			if err.Error() != "only 1 patient's health records can be included in a single share" {
				t.Fatalf("should have gotten multiple patients error but got: %s", err.Error())
			}
		}

	})

	t.Run("invalid share mode", func(t *testing.T) {
		share := Share{
			Method:    ACCESS_PAGE_FAX,
			ExamList:  []Exam{ex1, ex2, ex4},
			Recipient: "",
			Mode:      "thisisnotasharemode",
		}

		err := share.IsValid()

		if err == nil {
			t.Fatalf("should have got invalid share mode error but got none")
		}
	})

	t.Run("record summary json properly escaped", func(t *testing.T) {
		share := Share{
			ExamList: []Exam{ex4, ex5},
			HealthRecords: []Record{hr3},
		}
		
		actualJson := share.GetRecordSummaryJson()
		isJson := json.Valid([]byte("[" + actualJson + "]"))
		if !isJson {
			t.Fatalf("should be json")
		}
	})

}
