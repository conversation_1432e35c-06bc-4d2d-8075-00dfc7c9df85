/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type ImageMetadata struct {
	Activated bool `json:"activated" bson:"activated"`

	ReferringPhysician string `json:"referringPhysician,omitempty" bson:"referring_physician"`

	Type string `json:"type,omitempty" bson:"type"`

	BodyPart string `json:"bodyPart,omitempty" bson:"body_part"`

	Description string `json:"description,omitempty" bson:"description"`

	ClinicName string `json:"clinicName,omitempty"`

	ClinicId int64 `json:"clinicId,omitempty" bson:"clinic_id"`

	Date string `json:"date,omitempty" bson:"date"`

	ExamId string `json:"examId,omitempty" bson:"uid"`

	OrgName string `json:"orgName,omitempty"`
}
