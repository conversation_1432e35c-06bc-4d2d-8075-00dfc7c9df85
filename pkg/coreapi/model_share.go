/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"errors"
	"strings"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/util/stringhelpers"
)

// Share - Object to describe a share.
type Share struct {

	// This should not be populated on POST
	ShareId string `json:"shareId,omitempty"`

	// share status - only applies to faxes. Not populated on POST.
	Status FaxStatus `json:"status,omitempty"`

	ExamList []Exam `json:"examList,omitempty"`

	// Populate with fax number for fax shares and email address for email shares. Blank for print shares.
	Recipient string `json:"recipient,omitempty"`

	Method ShareMethod `json:"method,omitempty"`

	Mode ShareMode `json:"mode,omitempty"`

	// Not populated on POST
	Expiry string `json:"expiry,omitempty"`

	// Not populated on POST
	Active bool `json:"active"`

	Date string `json:"date,omitempty"`

	EUnityToken string `json:"eunityToken,omitempty"`

	CCUser bool `json:"ccUser,omitempty"`

	ExtendedExpiry string `json:"extendedExpiry,omitempty"`

	HealthRecords []Record `json:"healthRecords"`

	HRPatient HRPatient `json:"hrPatient"`

	ContainsDeletedExam bool `json:"containsDeletedExam,omitempty"`

	// account id of sender, if sender is patient; provider name, if sender is provider
	ShareInitiator string `json:"shareInitiator,omitempty"`
	// 'patient' if sender is patient, 'provider' if sender is provider
	ShareType string `json:"shareType,omitempty"`
}

func (s *Share) IsValid() error {
	//validate share method
	//TODO: potentially make more robust since GO enums are not strictly enforced like other languages
	switch s.Method {
	case EMAIL, ACCESS_PAGE_FAX, ACCESS_PAGE_PRINT, ZIP, ISO:
		//do nothing
	default:
		return errors.New("invalid share method")
	}

	//validate recipient based on share method
	switch s.Method {
	case EMAIL: //TODO: validate actual email
		if s.Recipient == "" {
			return errors.New("recipient must be populated for email or fax shares")
		}
	case ACCESS_PAGE_FAX:
		if len(s.Recipient) != 10 {
			return errors.New("invalid fax number")
		}
	}

	//validate share mode
	switch s.Mode {
	case ALL, MULTIPLE:
		//do nothing
	default:
		return errors.New("invalid share mode")
	}

	//validate non empty exam list
	examCount := len(s.ExamList)
	hrCount := len(s.HealthRecords)
	if examCount+hrCount == 0 {
		return errors.New("must share non empty record list")
	}

	//validate that all exams (if more than one) are for the same patient
	//based off DOB and sex
	if examCount > 1 {
		DOB := s.ExamList[0].Dob
		sex := s.ExamList[0].Sex
		for _, e := range s.ExamList {
			if e.Dob != DOB || e.Sex != sex {
				return errors.New("only 1 patient's exams can be included in a single share")
			}
		}
	}

	//validate that all hrs are for the same patient based on profileId
	if hrCount > 1 {
		profileId := s.HealthRecords[0].ProfileId
		for _, hr := range s.HealthRecords {
			if profileId != hr.ProfileId {
				return errors.New(
					"only 1 patient's health records can be included in a single share",
				)
			}
		}
	}

	return nil
}

func (s *Share) GetRecordSummaryJson() string {
	var examListJson string
	//create concise json for the first three exams/records in the list to display in the pdf
	for i, ex := range s.ExamList {
		if i < 3 {
			examListJson += "{\"modality\": \"" + stringhelpers.EscapeTextFieldForJSON(ex.ExamType) + "\", \"description\": \"" + stringhelpers.EscapeTextFieldForJSON(ex.Description) + "\", \"date\": \"" + stringhelpers.EscapeTextFieldForJSON(ex.ExamDate) + "\"}"
			examListJson += ", "
		}
	}
	if len(s.ExamList) < 3 && len(s.HealthRecords) > 0 {
		for i, hr := range s.HealthRecords {
			if i < 3-len(s.ExamList) {
				truncatedName := hr.Name
				if len(hr.Description) > 15 {
					truncatedName = hr.Name[:12] + "..."
				}
				date, _ := time.Parse("2006-01-02T15:04:05-07:00", hr.RecordDate)
				examListJson += "{\"modality\": \"" + hr.TypeCode.String() + "\", \"description\": \"" + stringhelpers.EscapeTextFieldForJSON(truncatedName) + "\", \"date\": \"" + date.Format(
					"Jan 2, 2006",
				) + "\"}"
				examListJson += ", "
			}
		}
	}
	//strip trailing comma/spaces
	return strings.Trim(examListJson, ", ")
}

func (s *Share) GetExamIds() []string {
	examIds := make([]string, len(s.ExamList))
	for i, ex := range s.ExamList {
		examIds[i] = ex.UUID
	}
	return examIds
}

func (s *Share) GetHRIds() []string {
	if len(s.HealthRecords) > 0 {
		hrIds := make([]string, len(s.HealthRecords))
		for i, hr := range s.HealthRecords {
			hrIds[i] = hr.FHIRId
		}
		return hrIds
	}
	return []string{}
}

func (s *Share) GetPatientId() string {
	if len(s.HealthRecords) > 0 {
		return s.HealthRecords[0].PatientId
	}
	return ""
}

type PhysicianShareInfo struct {
	ShareId        string `json:"shareId"`
	Expiry         string `json:"expiry,omitempty"`
	ExtendedExpiry string `json:"extendedExpiry,omitempty"`
	EUnityToken    string `json:"eunityToken,omitempty"`
}

type PatientShareInfo struct {
	Exams                    []Exam                        `json:"exams"`
	ExamShareInfoMap         map[string]PhysicianShareInfo `json:"examShareInfoMap"`
	HealthRecords            []Record                      `json:"hrs"`
	HealthRecordShareInfoMap map[string]PhysicianShareInfo `json:"hrShareInfoMap"`
}
