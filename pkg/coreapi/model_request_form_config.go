/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import "gitlab.com/pockethealth/coreapi/pkg/services/orgs"

type RequestFormConfig struct {
	SingleDate bool `json:"singleDate,omitempty"`

	RecentAndComment bool `json:"recentAndComment,omitempty"`

	Mrn string `json:"mrn,omitempty"`

	DelegateConsent string `json:"delegateConsent,omitempty"`

	ExamType string `json:"examType,omitempty"`

	PatientConsent string `json:"patientConsent,omitempty"`

	ExamSite string `json:"examSite,omitempty"`

	ExamSiteMrnPrefix string `json:"examSiteMrnPrefix,omitempty"`

	ModalTypes []string `json:"modalTypes,omitempty"`

	Provider Provider `json:"provider,omitempty"`

	Payment bool `json:"payment,omitempty"`

	AltID string `json:"altID,omitempty"`

	Multilocations bool `json:"multilocations,omitempty"`

	Delegate bool `json:"delegate,omitempty"`

	Reports bool `json:"reports,omitempty"`

	RecordText string `json:"recordText,omitempty"`

	PaymentText string `json:"paymentText,omitempty"`

	EnrollConsent bool `json:"enrollConsent,omitempty"`

	Legal bool `json:"legal,omitempty"`

	LegalSurcharge string `json:"legalSurcharge,omitempty"`

	Ohip bool `json:"ohip,omitempty"`

	Ipn bool `json:"ipn,omitempty"`

	Ssn bool `json:"ssn,omitempty"`

	Bcphn bool `json:"bcphn,omitempty"`

	AffiliatedText string `json:"affiliatedText,omitempty"`

	MinConsentAge int32 `json:"minConsentAge,omitempty"`

	ExamDate bool `json:"examDate,omitempty"`

	PatientAddress bool `json:"patientAddress,omitempty"`

	EnrollOpt bool `json:"enrollOpt,omitempty"`

	Base64Logo string `json:"base64Logo,omitempty"`

	DelegateAuthInstruction string `json:"delegateAuthInstruction,omitempty"`

	SurveyId int32 `json:"surveyId,omitempty"`

	ShowLoginLink bool `json:"showLoginLink"`

	ShowZohoLiveChat bool `json:"showZohoLiveChat"`

	RequireDelegateReview bool `json:"requireDelegateReview"`

	IsUPH bool `json:"isUPH"`

	FormResponseV2 orgs.FormResponse `json:"formResponseV2"`
}

func (rfc *RequestFormConfig) RequiresHealthID() bool {
	return rfc.Ohip || rfc.Bcphn || (rfc.Mrn != "" && rfc.Mrn != "0") || rfc.Ipn || rfc.Ssn ||
		(rfc.AltID != "" && rfc.AltID != "0")
}
