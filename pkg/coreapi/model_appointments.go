package coreapi

type AppointmentDetails struct {
	AppointmentDate string                           `json:"appointment_date"`
	Appointments    []Appointment                    `json:"appointments"`
	Clinic          ClinicInfo                       `json:"clinic"`
	PatientName     string                           `json:"patient_name"`
	PatientStatus   AppointmentReminderPatientStatus `json:"patient_status"`
	ReminderID      string                           `json:"reminder_id"`
}

type Appointment struct {
	ID                  int      `json:"id,omitempty"`
	Time                string   `json:"time"`
	Modality            string   `json:"modality"`
	Procedure           string   `json:"procedure"`
	Description         string   `json:"description"`
	PrepLink            string   `json:"prep_link,omitempty"`
	PrepInstructionText string   `json:"prep_instruction_text,omitempty"`
	PrepInstructionUrls []string `json:"prep_instruction_urls,omitempty"`
}

type ClinicInfo struct {
	Name           string `json:"name,omitempty"`
	ShortName      string `json:"clinic_name_short,omitempty"`
	Address        string `json:"address,omitempty"`
	AddressDetails string `json:"address_details,omitempty"`
	ShortAddress   string `json:"address_short,omitempty"`
	Phone          string `json:"phone,omitempty"`
	Email          string `json:"email,omitempty"`
	URL            string `json:"url,omitempty"`
}
