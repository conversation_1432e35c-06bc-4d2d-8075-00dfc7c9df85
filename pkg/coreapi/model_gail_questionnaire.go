package coreapi

type Ethnicity string //as defined by the Gail <PERSON>reast Cancer Risk Assessment

const (
	WHITE                 Ethnicity = "Wh"
	AFRICANAMERICAN       Ethnicity = "AA"
	HISPANIC              Ethnicity = "H"
	NORTHAMERICAN_UNKNOWN Ethnicity = "NA"
	ASIAN                 Ethnicity = "A"
)

type SubEthnicity string //as defined by the Gail Breast Cancer Risk Assessment

const (
	HISPANIC_AMERICAN              SubEthnicity = "HU"
	HISPANIC_AMERICAN_FOREIGN_BORN SubEthnicity = "HF"
	CHINESE_AMERICAN               SubEthnicity = "Ch"
	JAPANESE_AMERICAN              SubEthnicity = "Ja"
	FILIPINO_AMERICAN              SubEthnicity = "Fi"
	HAWAIIAN_AMERICAN              SubEthnicity = "Hw"
	OTHER_PACIFIC_ISLANDER         SubEthnicity = "oP"
	OTHER_ASIAN                    SubEthnicity = "oA"
)

type OneOrMore int

const (
	NONE           OneOrMore = 0
	ONE            OneOrMore = 1
	MORE           OneOrMore = 2
	UNKNOWN_AMOUNT OneOrMore = 99
)

type YesNoUnknown int

const (
	NO      YesNoUnknown = 0
	YES     YesNoUnknown = 1
	UNKNOWN YesNoUnknown = 99
)

type BreastBiopsyFactor struct {
	Exists              *YesNoUnknown `json:"exists,omitempty"`
	BenignBiopsies      *OneOrMore    `json:"benign_biopsies,omitempty"`
	AtypicalHyperplasia *OneOrMore    `json:"atypical_hyperplasia,omitempty"`
}

type GailQuestionnairePatientResponses struct {
	EligibilityCheck     GailRiskEligibility `json:"eligibility_check"`
	Dob                  *int64              `json:"dob,omitempty"`
	Ethnicity            *Ethnicity          `json:"ethnicity,omitempty"`
	SubEthnicity         *SubEthnicity       `json:"sub_ethnicity,omitempty"`
	AgeFirstMenstruation *int                `json:"age_men,omitempty"`
	AgeFirstBirth        *int                `json:"age_birth,omitempty"`
	FirstDegreeRelatives *OneOrMore          `json:"first_degree_relatives,omitempty"`
	BreastBiopsyFactor   *BreastBiopsyFactor `json:"breast_biopsy,omitempty"`
}

type GailRiskEligibility struct {
	BreastCancerHistory      YesNoUnknown `json:"breast_cancer_history"`
	HasCurrentCondition      string       `json:"has_current_condition"`
	HodgkinLymphomaRadiation YesNoUnknown `json:"has_hodgkin_lymphoma"`
	HasGeneMutations         YesNoUnknown `json:"has_gene_mutations"`
	GeneticRiskBreastCancer  YesNoUnknown `json:"genetic_risk_breast_cancer"`
}

type GailRiskScore struct {
	FiveYearRisk float64 `json:"risk_five_years"`
	LifetimeRisk float64 `json:"risk_lifetime"`
}
