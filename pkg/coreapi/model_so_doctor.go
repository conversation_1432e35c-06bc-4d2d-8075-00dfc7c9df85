package coreapi

type SOCPSODoctor struct {
	Id                  int    `json:"id"`
	FirstName           string `json:"first_name,omitempty"`
	LastName            string `json:"last_name"`
	Phone               string `json:"phone,omitempty"`
	PhoneExt            string `json:"phone_ext,omitempty"`
	Fax                 string `json:"fax,omitempty"`
	Address1            string `json:"address_1,omitempty"`
	Address2            string `json:"address_2,omitempty"`
	Address3            string `json:"address_3,omitempty"`
	Address4            string `json:"address_4,omitempty"`
	Address5            string `json:"address_5,omitempty"`
	CPSO                int    `json:"cpso"`
	AdditionalLocations string `json:"additional_locations,omitempty"`
	Specialization      string `json:"specialization,omitempty"`
}
