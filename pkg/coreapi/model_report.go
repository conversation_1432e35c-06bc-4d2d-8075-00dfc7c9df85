/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

type Report struct {
	ReportId string `json:"reportId,omitempty" bson:"_id"`

	ClinicId int64 `json:"clinicId,omitempty" bson:"clinic_id"`

	ClinicName string `json:"clinicName,omitempty"`

	OrgName string `json:"orgName,omitempty"`

	ParsedTime string `json:"parsedTime,omitempty"`

	UploadTime string `json:"uploadTime,omitempty"`

	BsonUploadTime time.Time `json:"-" bson:"upload_time"`

	Activated bool `json:"activated" bson:"activated"`

	ReferringPhysician string `json:"referringPhysician,omitempty"`

	Size int64 `json:"size,omitempty"`

	InactivatedTransferId string `json:"inactivatedTransferId,omitempty"`

	PatientName models.PatientName `json:"patientName,omitempty"`

	Dob string `json:"dob,omitempty"`

	Sex string `json:"sex,omitempty"`

	Protocol string `json:"protocol,omitempty"`

	Definitions bool `json:"definitions"`
}

type FollowUpReport struct {
	FollowUpExists bool `json:"followUpExists"`
}

func (r *Report) GetFullName() string {
	return r.PatientName.GetFullName()
}

func (r *Report) GetLastCommaFirstName() string {
	return r.PatientName.GetLastCommaFirst()
}
