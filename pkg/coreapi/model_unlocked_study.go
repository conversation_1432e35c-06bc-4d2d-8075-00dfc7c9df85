package coreapi

type UnlockedStudy struct {
	UUID        string `json:"uuid"`
	Description string `json:"description,omitempty"`
	ExamDate    string `json:"examDate,omitempty"`
	HasReport   bool   `json:"hasReport"`
	ExamType    string `json:"examType,omitempty"`
	ExamId      string `json:"examId,omitempty"`
	PatientId   string `json:"patientId"`
	OrgId       int64  `json:"orgId"`
	OrgName     string `json:"orgName"`
}
