/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type Provider struct {
	ProviderId int64 `json:"providerId,omitempty"`

	OrgId int64 `json:"orgId,omitempty"`

	Name string `json:"name,omitempty"`

	OrgName string `json:"orgName,omitempty"`

	ClinicName string `json:"clinicName,omitempty"`

	Address string `json:"address,omitempty"`

	FeeAmount float32 `json:"feeAmount,omitempty"`

	TaxPercent float32 `json:"taxPercent,omitempty"`

	TaxName string `json:"taxName,omitempty"`

	Region string `json:"region,omitempty"`

	Url string `json:"urlName,omitempty"`

	AltUrls []string `json:"altUrls,omitempty"`
}
