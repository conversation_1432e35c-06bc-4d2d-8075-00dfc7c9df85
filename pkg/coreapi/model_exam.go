/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"context"
	"errors"
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	orgservice "gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v8/pkg/logutils"
)

const (
	ExamDateFormat  = "Jan 02, 2006"
	DICOMDateFormat = "20060102"
)
const ReferringPhysicianNamePrefix = "Dr. "

// raw exam object with only exam table fields
type ExamRawBasic struct {
	UUID          string
	ExamId        string
	DICOMExamDate string

	TransferId              string
	TransferStatus          V3TransferStep
	DICOMPatientName        string
	PatientMrn              string
	Activated               bool
	Description             string
	DICOMBirthDate          string
	Sex                     string
	DICOMReferringPhysician string
	PatientId               string
	Phone                   string
	BodyPart                string
	AccountId               string
	Modality                string
}

func (e *ExamRawBasic) GetFormattedPatientName(ctx context.Context) models.PatientName {
	patientName := models.PatientName{}
	firstAndMiddleName, lastName := dcmtools.ParseFirstLastName(ctx, e.DICOMPatientName)
	patientName.DicomName = e.DICOMPatientName
	patientName.FirstAndMiddleName = firstAndMiddleName
	patientName.LastName = lastName
	return patientName
}

func (e *ExamRawBasic) GetFormattedPatientDOB() string {
	if e.DICOMBirthDate == "" {
		return ""
	}
	return dcmtools.ParseDob(e.DICOMBirthDate, "/")
}

func (e *ExamRawBasic) GetFormattedReferringPhysicianName(ctx context.Context) string {
	if e.DICOMReferringPhysician == "" {
		return ""
	}
	return ReferringPhysicianNamePrefix + dcmtools.ParseName(ctx, e.DICOMReferringPhysician)
}

// raw exam object for reading from the database
type ExamRaw struct {
	ExamRawBasic

	Series  []Series
	Reports []Report

	ExamType            string
	Provider            string //clinic name
	OrgName             string //provider name
	TransferDate        string
	ReportDelay         int
	Size                int
	OrgId               int64 //legacy provider ID
	FacilityFunded      bool
	AllowPtPngDownloads bool
	AttributeOrderId    string
	AttributedAt        time.Time
	UnlockStatus        models.UnlockStatus
	ProviderId          string // provider unique identifier
}

func (e ExamRaw) MarshalJSON() ([]byte, error) {
	return nil, errors.New("ExamRaw struct cannot be marshalled. Use the Exam type instead")
}

// exam response object
type Exam struct {
	Series  []Series `json:"series,omitempty"         bson:"series"`
	Reports []Report `json:"reports,omitempty"`
	UUID    string   `json:"uuid"`
	// Not populated on POST
	ExamId      string             `json:"examId,omitempty"         bson:"_id"`
	PatientName models.PatientName `json:"patientName,omitempty"`
	// parsed modality
	ExamType string `json:"examType,omitempty"`
	Provider string `json:"provider,omitempty"` //clinic name
	OrgName  string `json:"orgName,omitempty"`
	ExamDate string `json:"examDate,omitempty"`
	// Not populated on POST
	Activated           bool                `json:"activated,omitempty"`
	Description         string              `json:"description,omitempty"`
	Dob                 string              `json:"dob,omitempty"            bson:"dob"`
	Sex                 string              `json:"sex,omitempty"            bson:"sex"`
	TransferId          string              `json:"transferId,omitempty"`
	TransferStatus      V3TransferStep      `json:"transferStatus,omitempty"`
	TransferDate        string              `json:"transferDate,omitempty"`
	BodyPart            string              `json:"bodypart,omitempty"`
	ReportDelay         int                 `json:"reportDelay"`
	ReferringPhysician  string              `json:"referringPhysician"`
	Size                int                 `json:"size"`
	OrgId               int64               `json:"orgId"`
	FacilityFunded      bool                `json:"facilityFunded"`
	Modality            string              `json:"modality,omitempty"`
	AllowPtPngDownloads bool                `json:"allow_pt_png_dls"`
	PatientId           string              `json:"patientId"`
	UnlockStatus        models.UnlockStatus `json:"unlockStatus"`
}

func (e *ExamRaw) SetProviderMetadata(provider orgservice.Provider) {
	e.OrgName = provider.Name
	e.Provider = provider.Name
	e.AllowPtPngDownloads = provider.AllowDownload
	e.FacilityFunded = provider.GetIsFacilityFunded()
	e.ReportDelay = -1
	if provider.SendReports {
		e.ReportDelay = provider.ReportSendTime
	}
}

func (e *ExamRaw) ToExam(ctx context.Context) Exam {
	patientName := models.PatientName{}
	referringPhysician := ""
	dob := ""
	description := ""
	sex := ""
	bodyPart := ""
	examDate := ""

	if e.DICOMExamDate != "" {
		date, err := time.Parse(DICOMDateFormat, e.DICOMExamDate)
		if err == nil {
			examDate = date.Format(ExamDateFormat)
		} else {
			logutils.DebugCtxLogger(ctx).WithField("exam_date", e.DICOMExamDate).Warn("could not parse exam date")
		}
	}

	examType := e.ExamType
	if examType == "" {
		examType = dcmtools.ParseType(e.Modality)
	}

	// only populate sensitive patient info if activated
	if e.Activated {
		formattedDOB := e.ExamRawBasic.GetFormattedPatientDOB()
		if formattedDOB != "" {
			dob = formattedDOB
		}
		formattedReferringPhysicianName := e.ExamRawBasic.GetFormattedReferringPhysicianName(ctx)
		if formattedReferringPhysicianName != "" {
			referringPhysician = formattedReferringPhysicianName
		}
		description = e.Description
		patientName = e.ExamRawBasic.GetFormattedPatientName(ctx)

		sex = e.Sex
		bodyPart = e.BodyPart
	}

	return Exam{
		Series:              e.Series,
		Reports:             e.Reports,
		UUID:                e.UUID,
		ExamId:              e.ExamId,
		PatientName:         patientName,
		ExamType:            examType,
		Provider:            e.Provider,
		OrgName:             e.OrgName,
		ExamDate:            examDate,
		Activated:           e.Activated,
		Description:         description,
		Dob:                 dob,
		Sex:                 sex,
		TransferId:          e.TransferId,
		TransferStatus:      e.TransferStatus,
		TransferDate:        e.TransferDate,
		BodyPart:            bodyPart,
		ReportDelay:         e.ReportDelay,
		ReferringPhysician:  referringPhysician,
		Size:                e.Size,
		OrgId:               e.OrgId,
		FacilityFunded:      e.FacilityFunded,
		Modality:            e.Modality,
		AllowPtPngDownloads: e.AllowPtPngDownloads,
		PatientId:           e.PatientId,
		UnlockStatus:        e.UnlockStatus,
	}
}

func RawToExams(ctx context.Context, e []ExamRaw) []Exam {
	exams := make([]Exam, len(e))
	for i, raw := range e {
		exams[i] = raw.ToExam(ctx)
	}
	return exams
}

func (e *Exam) GetFullName() string {
	return e.PatientName.GetFullName()
}

func (e *Exam) GetLastCommaFirstName() string {
	return e.PatientName.GetLastCommaFirst()
}

type V3TransferStep string

const (
	V3_TRANSFER_INIT     V3TransferStep = "Initialized"
	V3_TRANSFER_PREFETCH V3TransferStep = "Prefetched"
	V3_TRANSFER_FULL     V3TransferStep = "FullTransfered"
)
