/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

// ShareCredentials - Credentials for validating a share view - only one pair should be used: shareId and pin or viewcode and dob.
type ShareCredentials struct {
	ShareId string `json:"shareId,omitempty"`

	Pin string `json:"pin,omitempty"`

	Viewcode string `json:"viewcode,omitempty"`

	Dob string `json:"dob,omitempty"`

	LastName string `json:"lastName,omitempty"`
}
