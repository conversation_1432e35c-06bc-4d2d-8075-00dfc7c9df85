package coreapi

type PhiType string

// List of PhiTypes
const (
	OHIP     PhiType = "OHIP"
	OHIP_VC  PhiType = "OHIP_VC"
	BCPHN    PhiType = "BCPHN"
	SSN      PhiType = "SSN"
	IPN      PhiType = "IPN"
	ALT_H_ID PhiType = "ALT_H_ID"
)

type PhiProfile struct {
	Id string `json:"Id,omitempty"`

	PatientId string `json:"patientId,omitempty"`

	PhiType PhiType `json:"PhiType,omitempty"`

	PhiValue string `json:"PhiValue,omitempty"`
}
