/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type RejectVerifyDetails struct {
	RecentExamDate    string   `json:"recentExamDate"`
	HasOlderExams     bool     `json:"hasOlderExams"`
	ProviderConfirmed bool     `json:"providerConfirmed"`
	ProviderId        uint     `json:"providerId,omitempty"`
	ProviderText      string   `json:"providerText,omitempty"` // if user manually enters provider name
	ExamTypes         []string `json:"examTypes,omitempty"`
	OtherExamType     string   `json:"otherExamType,omitempty"`
	Comments          string   `json:"comments,omitempty"`
}
