package coreapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	errmsg "gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// EncodeJSONResponse uses the json encoder to write an interface to the http response with an optional status code
func EncodeJSONResponse(ctx context.Context, i interface{}, status *int, w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	if status != nil {
		w.WriteHeader(*status)
	} else {
		w.WriteHeader(http.StatusOK)
	}

	err := json.NewEncoder(w).Encode(i)
	if err != nil {
		logutils.CtxLogger(ctx).Error("could not encode response")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

func EncodeJSONResponseNoHTMLEscape(
	ctx context.Context,
	i interface{},
	status *int,
	w http.ResponseWriter,
) {
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	if status != nil {
		w.WriteHeader(*status)
	} else {
		w.WriteHeader(http.StatusOK)
	}

	encoder := json.NewEncoder(w)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(i)
	if err != nil {
		logutils.CtxLogger(ctx).Error("could not encode response")
		http.Error(
			w,
			http.StatusText(http.StatusInternalServerError),
			http.StatusInternalServerError,
		)
	}
}

// ParseIntParameter parses a string parameter to an int64
func ParseIntParameter(param string) (int64, error) {
	return strconv.ParseInt(param, 10, 64)
}

// ParseUIntParameter parses a string parameter to a uint
func ParseUIntParameter(param string) (uint, error) {
	intParam, err := strconv.ParseUint(param, 10, 32)
	return uint(intParam), err
}

func ParseQueryParamBool(
	r *http.Request,
	paramName string,
) (exists bool, paramBool bool, err error) {
	query := r.URL.Query()
	if paramStr := query.Get(paramName); paramStr != "" {
		paramBool, err = strconv.ParseBool(paramStr)
		if err != nil {
			return true, false, errors.New(errmsg.ERR_BAD_QUERY_PARAM + ": " + paramName)

		}
		return true, paramBool, nil
	}
	return false, false, nil
}

func ParseQueryParamInt64(r *http.Request, paramName string) (bool, int64, error) {
	query := r.URL.Query()
	if paramStr := query.Get(paramName); paramStr != "" {
		paramInt, err := strconv.ParseInt(paramStr, 10, 64)
		if err != nil {
			return true, 0, errors.New(errmsg.ERR_BAD_QUERY_PARAM + ": " + paramName)
		}
		return true, paramInt, nil
	}
	return false, 0, nil
}

func ParseQueryParamInt(r *http.Request, paramName string) (bool, int, error) {
	hasParam, value, err := ParseQueryParamInt64(r, paramName)
	return hasParam, int(value), err
}

func ConvertBoolParamString(param string) (bool, error) {
	if param == "" {
		return false, nil
	}
	result, err := strconv.ParseBool(param)
	if err != nil {
		return false, err
	}
	return result, nil
}

// ValidateSecureURL checks if the provided string is a valid URL
// and ensures that its scheme is HTTPS.
// It returns trimmedURL if the URL is valid and secure, otherwise an error.
func ValidateSecureURL(urlString string) (string, error) {
	// Trim leading/trailing whitespace
	trimmedURL := strings.TrimSpace(urlString)

	if trimmedURL == "" {
		return "", fmt.Errorf("URL cannot be empty")
	}

	// Parse the URL
	parsedURL, err := url.ParseRequestURI(trimmedURL)
	if err != nil {
		// This error means the URL is malformed (e.g., invalid characters, missing scheme)
		return "", fmt.Errorf("invalid URL format: %w", err)
	}

	// Check if the scheme is present (url.ParseRequestURI should ensure this, but for sanity)
	if parsedURL.Scheme == "" {
		return "", fmt.Errorf("URL scheme is missing")
	}

	// Check if the scheme is HTTPS
	if strings.ToLower(parsedURL.Scheme) != "https" {
		return "", fmt.Errorf("URL scheme must be HTTPS, got: %s", parsedURL.Scheme)
	}

	// Ensure Host is present
	if parsedURL.Host == "" {
		return "", fmt.Errorf("URL host is missing")
	}

	return trimmedURL, nil
}
