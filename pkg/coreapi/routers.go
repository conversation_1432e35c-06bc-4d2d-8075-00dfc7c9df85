/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"fmt"
	"net/http"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/coreapi/pkg/ratelimit"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/phnet"
)

// A Route defines the parameters for an api endpoint
type Route struct {
	Name        string
	Method      string
	Pattern     string
	HandlerFunc http.HandlerFunc
	RateLimit   ratelimit.RateLimitConfig //optional - only if the route need a route level rate limit
}

// Routes are a collection of defined api endpoints
type Routes []Route

// Router defines the required methods for retrieving api routes
type Router interface {
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

// list of IP ranges of proxies that traffic could flow from(e.g. app gateway);
// used for resolving client IP addresses
var proxyIPRanges []string

// NewRouter creates a new router for any number of api routers
// and adds each api router's associated middleware
func NewRouter(routers ...Router) (*mux.Router, error) {
	mRouter := mux.NewRouter().StrictSlash(false)

	for _, api := range routers {
		//create a subrouter for each subgroup of paths (ie, '/v1/images*')
		subRouter := mRouter.PathPrefix(api.GetPathPrefix()).Subrouter().StrictSlash(false)
		//apply middleware to that subgroup
		for _, middleware := range api.GetMiddleware() {
			subRouter.Use(middleware)
		}

		//add paths to subrouter
		for _, route := range api.Routes() {
			if route.RateLimit.MaxRequests > 0 {
				rl := ratelimit.NewRateLimiter(
					route.RateLimit.PeriodMinutes,
					route.RateLimit.MaxRequests,
				)
				subRouter.
					Methods(route.Method, http.MethodOptions).
					Path(route.Pattern).
					Name(route.Name).
					Handler(rl(route.HandlerFunc))
			} else {
				subRouter.
					Methods(route.Method, http.MethodOptions).
					Path(route.Pattern).
					Name(route.Name).
					Handler(route.HandlerFunc)
			}
		}
	}

	mRouter.HandleFunc("/ping", HandlePing).Methods("GET")

	//add api-wide middleware in the order it is to be applied
	mRouter.Use(SecureHeaders)
	mRouter.Use(correlationIDHeader)
	mRouter.Use(phnet.CreateIPResolverMiddleware(proxyIPRanges))
	mRouter.Use(genericRespMiddleware)
	walkErr := mRouter.Walk(
		func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
			tmpl, err := route.GetPathTemplate()
			if err != nil {
				return err
			}
			fmt.Printf("route: %s, handler: %v\n", tmpl, route.GetHandler())
			return nil
		},
	)
	if walkErr != nil {
		return nil, walkErr
	}
	return mRouter, nil
}

func HandlePing(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
}

// register the IP ranges of recognized proxies so that we can resolve true
// client IP's for requests that come through those proxies(e.g. appgateway)
func SetProxyIPRanges(ipRanges []string) {
	proxyIPRanges = ipRanges
}

func SecureHeaders(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Add("X-Frame-Options", "DENY")
		w.Header().Add("Content-Security-Policy", "frame-ancestors 'none'")
		w.Header().Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		w.Header().Add("X-Xss-protection", "1; mode=block")

		w.Header().Add("Referrer-Policy", "same-origin")

		w.Header().Add("Cache-Control", "no-cache, no-store, must-revalidate")
		w.Header().Add("Pragma", "no-cache")
		w.Header().Add("Expires", "0")

		next.ServeHTTP(w, r)
	})
}

func correlationIDHeader(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cID, _ := r.Context().Value(logutils.CorrelationIdContextKey).(string)
		w.Header().Add("X-Correlation-ID", cID)

		next.ServeHTTP(w, r)
	})
}

// middleware for enabling overriding of response messages, it replaces the
// response writer with a wrapper that replaces response messages based on
// status codes
func genericRespMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// wrap response write with custom one
		customResponseWriter := NewCustomResponseWriter(w)

		// pass the custom respwriter along
		next.ServeHTTP(customResponseWriter, r)
	})
}

// CustomResponseWriter is a wrapper around http.ResponseWriter that allows modification of the response body.
type CustomResponseWriter struct {
	// original response writer that we're wrapping
	originalResp http.ResponseWriter
	// true when the response body has been altered
	altered bool
}

func NewCustomResponseWriter(w http.ResponseWriter) *CustomResponseWriter {
	return &CustomResponseWriter{originalResp: w}
}

func (c *CustomResponseWriter) WriteHeader(statusCode int) {
	switch statusCode / 100 {
	case 5: // return generic messages on all 5xx codes
		c.altered = true
		http.Error(c.originalResp, http.StatusText(statusCode), statusCode)
	default:
		c.originalResp.WriteHeader(statusCode)
		return
	}
}

func (c *CustomResponseWriter) Write(b []byte) (int, error) {
	// if the response hasn't been altered, return as is
	if !c.altered {
		return c.originalResp.Write(b)
	}

	return 0, nil
}

func (c *CustomResponseWriter) Header() http.Header {
	return c.originalResp.Header()
}
