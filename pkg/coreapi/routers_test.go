package coreapi

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gorilla/mux"
	"gitlab.com/pockethealth/phutils/v10/pkg/phnet"
)

func TestPhnetIpResolverMiddleware(t *testing.T) {

	t.Run("client ip resolved when request has app gateway IP(public or private)", func(t *testing.T) {
		appGwPublicIP := "**************"
		appGwPublicCIDR := appGwPublicIP + "/32"
		appGwPubRemoteAddr := appGwPublicIP + ":3245"
		appGwPrivateIP := "**********"
		appGwPrivateCIDR := "**********/24"
		appGwPrivRemoteAddr := appGwPrivateIP + ":3245"

		wantClientIP := "*************" // will be in X-Forwarded-For header
		gotClientIP := ""

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			gotClientIP = strings.Split(r.RemoteAddr, ":")[0]
			w.WriteHeader(http.StatusOK)
		})

		// setup mux router
		router := mux.NewRouter().StrictSlash(false)
		router.Path("/").Methods("GET").Handler(handler)
		// setup the ipresolver using appgw IP CIDRs
		router.Use(phnet.CreateIPResolverMiddleware([]string{appGwPublicCIDR, appGwPrivateCIDR}))

		// test when request has app gateway public IP
		req := httptest.NewRequest("GET", "/", nil)
		req.RemoteAddr = appGwPubRemoteAddr
		req.Header.Add("X-Forwarded-For", wantClientIP)

		rec := httptest.NewRecorder()

		// send request!
		router.ServeHTTP(rec, req)

		if wantClientIP != gotClientIP {
			t.Errorf("expected resolved IP to be: %s, got: %s", wantClientIP, gotClientIP)
		}

		// test when request has app gateway private IP
		req.RemoteAddr = appGwPrivRemoteAddr
		rec = httptest.NewRecorder()
		router.ServeHTTP(rec, req)

		if wantClientIP != gotClientIP {
			t.Errorf("expected resolved IP to be: %s, got: %s", wantClientIP, gotClientIP)
		}
	})

	t.Run("remote address left unchanged if source IP doesn't match any defined proxy IPs", func(t *testing.T) {
		testRemoteIP := "*************"
		testRemoteAddr := testRemoteIP + ":644"
		fakeForwardedIP := "************"

		appGwPublicCIDR := "**************/32"
		appGwPrivateCIDR := "**********/24"

		gotClientIP := ""

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			gotClientIP = strings.Split(r.RemoteAddr, ":")[0]
			w.WriteHeader(http.StatusOK)
		})

		// setup mux router
		router := mux.NewRouter().StrictSlash(false)
		router.Path("/").Methods("GET").Handler(handler)
		// setup the ipresolver using appgw IP CIDRs
		router.Use(phnet.CreateIPResolverMiddleware([]string{appGwPublicCIDR, appGwPrivateCIDR}))

		// imagine a malicious actor tries to impersonate a different IP address
		req := httptest.NewRequest("GET", "/", nil)
		req.RemoteAddr = testRemoteAddr
		req.Header.Add("X-Forwarded-For", fakeForwardedIP)

		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)

		if gotClientIP != testRemoteIP {
			t.Errorf("expected remote addr to remain: %s, got: %s", testRemoteIP, gotClientIP)
		}
	})
}

func TestGenericRespMiddleware(t *testing.T) {
	t.Run("genericRespMiddleware replaces the response message with a generic msg on 500 code", func(t *testing.T) {
		customMsg := "hi sir how are you"
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(customMsg))
		})

		// setup mux router
		router := mux.NewRouter().StrictSlash(false)
		router.Path("/").Methods("GET").Handler(handler)
		router.Use(genericRespMiddleware)

		req := httptest.NewRequest("GET", "/", nil)
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)
		gotBody, err := io.ReadAll(rec.Body)
		if err != nil {
			t.Fatal("error reading response body:", err)
		}

		gotMsg := string(gotBody)

		expectedMsg := http.StatusText(http.StatusInternalServerError) + "\n"
		if gotMsg != expectedMsg {
			t.Errorf("expected middleware to alter response msg to: %s, got: %s", expectedMsg, gotMsg)
		}
	})

	t.Run("genericRespMiddleware ignores response message when code is non-5xx error code", func(t *testing.T) {
		customMsg := "konichiwa"
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(customMsg))
		})

		// setup mux router
		router := mux.NewRouter().StrictSlash(false)
		router.Path("/").Methods("GET").Handler(handler)
		router.Use(genericRespMiddleware)

		req := httptest.NewRequest("GET", "/", nil)
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)
		gotBody, err := io.ReadAll(rec.Body)
		if err != nil {
			t.Fatal("error reading response body:", err)
		}

		gotMsg := string(gotBody)
		if gotMsg != customMsg {
			t.Errorf("expected custom msg: %s,\t got: %s", customMsg, gotMsg)
		}
	})

	t.Run("genericRespMiddleware ignores response message on success code", func(t *testing.T) {
		customMsg := "à bientôt"
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(customMsg))
		})

		// setup mux router
		router := mux.NewRouter().StrictSlash(false)
		router.Path("/").Methods("GET").Handler(handler)
		router.Use(genericRespMiddleware)

		req := httptest.NewRequest("GET", "/", nil)
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)
		gotBody, err := io.ReadAll(rec.Body)
		if err != nil {
			t.Fatal("error reading response body:", err)
		}

		gotMsg := string(gotBody)
		if gotMsg != customMsg {
			t.Errorf("expected custom msg: %s,\t got: %s", customMsg, gotMsg)
		}
	})
}
