package coreapi

import "gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"

type ExamInsightsEligibility struct {
	Rho       bool       `json:"rho"`
	Followups []FollowUp `json:"followups"`
	Report    bool       `json:"report"`
}

type FollowUp struct {
	ReportId    string                              `json:"report_id"`
	HasFollowup bool                                `json:"has_followup"`
	Occurrences []reportinsights.FollowUpOccurrence `json:"occurrences,omitempty"`
}
