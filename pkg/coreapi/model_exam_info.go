/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type TransferExamSummary struct {
	UUID string `json:"uuid"`

	ExamId string `json:"examId,omitempty"`

	Type string `json:"type,omitempty"`

	ExamDate string `json:"examDate,omitempty"`

	Description string `json:"description,omitempty"`

	Provider string `json:"provider,omitempty"`

	ReferringPhysician string `json:"referringPhysician,omitempty"`

	ReportIncluded bool `json:"reportIncluded"`

	OrgName string `json:"orgName,omitempty"`
}
