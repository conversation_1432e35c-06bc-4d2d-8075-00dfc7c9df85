package coreapi

import "time"

type PaymentTransactionDiscount struct {
	DiscountName string `json:"discount_name"`
	Pct          uint   `json:"pct"`
	Amount       uint   `json:"amount"`
}

type DiscountDetails struct {
	DiscountName string `json:"discount_name"`

	// Discount % to be subtracted. This value should be between 0 and 1
	Pct *float32 `json:"pct,omitempty"`

	// Discount dollar value, counted in cents
	Amount *int32 `json:"amount,omitempty"`

	// How many times can discount be applied
	NumRecur *int32 `json:"num_recur,omitempty"`

	// How many more billing cycles discount applies for
	RecurRemain int32 `json:"recur_remain,omitempty"`

	// The external representation of this coupon for a given provider
	ExtID *string `json:"ext_id,omitempty"`

	// The provider for this instance of the coupon
	Provider *string `json:"provider,omitempty"`

	// When the discount code expires
	ExpiresAt *time.Time `json:"expires_at,omitempty"`

	// When the discount code was created
	CreatedAt *time.Time `json:"created_at,omitempty"`

	// When the discount code was last modified
	UpdatedAt *time.Time `json:"updated_at,omitempty"`

	// If the specific discount code is active
	Active *bool `json:"active,omitempty"`

	// The ID of the discount in the discount_codes table
	ID *int `json:"id,omitempty"`

	PlanIDs *[]int `json:"plans,omitempty"`
}
