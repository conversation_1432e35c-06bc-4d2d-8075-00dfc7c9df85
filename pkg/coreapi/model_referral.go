package coreapi

type Referral struct {
	Email string `json:"email,omitempty"`
}

func (r *Referral) GetEmail() string {
	return r.Email
}

type PublicReferral struct {
	Emails []string `json:"emails,omitempty"`
}

type PublicReferEmailThrottle struct {
	ThrottleNumber  int `json:"email_throttle_number"`
	AvailableNumber int `json:"available_number"`
}

type PublicReferEmailToken struct {
	AccountId string `json:"accountId,omitempty"`
	Timestamp string `json:"timestamp,omitempty"`
}
