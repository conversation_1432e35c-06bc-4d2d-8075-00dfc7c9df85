/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"regexp"

	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/util/datetime"
)

type UserProfile struct {
	FirstName string `json:"firstName,omitempty"`

	LastName string `json:"lastName,omitempty"`

	AltLastName string

	DOB string `json:"DOB,omitempty"` // format: 01/02/2006

	Ohip string `json:"ohip,omitempty"`

	Ohipvc string `json:"ohipvc,omitempty"`

	AltId string `json:"altId,omitempty"`

	Phone string `json:"phone,omitempty"`

	Email string `json:"email,omitempty"`

	ProfileId uint `json:"profileId,omitempty"`

	Mrn string `json:"mrn,omitempty"`

	Ipn string `json:"ipn,omitempty"`

	Ssn string `json:"ssn,omitempty"`

	Sex string `json:"sex,omitempty"`

	PatientId string `json:"patientId,omitempty"`

	IsAccountOwner bool `json:"isAccountOwner,omitempty"`
}

func (up *UserProfile) IsValid() bool {
	//need non empty first & last name
	if up.FirstName == "" || up.LastName == "" {
		return false
	}

	//need some type of id
	if up.Ohip == "" && up.AltId == "" && up.Mrn == "" && up.Ipn == "" && up.Ssn == "" {
		return false
	}

	//dob format mm/dd/yyyy
	var rxPat = regexp.MustCompile(`^[01][0-9]/[0123][0-9]/[0-9]{4}$`)
	return rxPat.MatchString(up.DOB)
}

func (up *UserProfile) ConvertToAcctSvcPt() accountservice.Patient {
	return accountservice.Patient{
		Email:          up.Email,
		FirstName:      up.FirstName,
		LastName:       up.LastName,
		DOB:            datetime.ConvertMMDDYYYYToISO8061(up.DOB),
		Phone:          up.Phone,
		IsAccountOwner: &up.IsAccountOwner,
	}
}
