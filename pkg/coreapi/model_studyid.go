package coreapi

import "fmt"

// RecordStreamingStudyID is a wrapper object for the
// two identifiers needed to uniquely identify a
// study that was shared via record streaming:
// studyUID (DICOM identifier of study) and providerID
type RecordStreamingStudyID struct {
	StudyUID   string `json:"studyUID"`
	ProviderID int64  `json:"providerID"`
}

func (r *RecordStreamingStudyID) IsValid() bool {
	return r.ProviderID != 0 && r.StudyUID != ""
}

// Key returns a single unique key for a record streaming study.
// The key has the format <providerID>_<studyUID>.
// It is used to store and access XML metadata for a study
func (r *RecordStreamingStudyID) Key() string {
	return fmt.Sprintf("%v_%v", r.ProviderID, r.StudyUID)
}

func NewRecordStreamingStudyID(studyUID string, providerID int64) RecordStreamingStudyID {
	return RecordStreamingStudyID{
		StudyUID:   studyUID,
		ProviderID: providerID,
	}
}

// StudyIndexEntry is an entry of the table
// unique_study_index.
// It contains a studyUid (DICOM identifier) of a study that was shared via record streaming,
// a providerID of the provider who shared the study
// and the examUuid of the study (internal identifier)
type StudyIndexEntry struct {
	StudyUID   string `json:"studyUID"`
	ProviderID int64  `json:"providerID"`
	ExamID     string `json:"examID"`
}

func (r *StudyIndexEntry) IsValid() bool {
	return r.ProviderID != 0 && r.StudyUID != "" && r.ExamID != ""
}
