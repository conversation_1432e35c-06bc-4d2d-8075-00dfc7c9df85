package coreapi

type Enrollment struct {
	Id        int64  `json:"id,omitempty"`
	OrgId     int64  `json:"orgId,omitempty"`
	Mrn       string `json:"mrn,omitempty"`
	Email     string `json:"email,omitempty"`
	AuditUser string `json:"auditUser,omitempty"`
	Timestamp string `json:"timestamp,omitempty"`
	RequestId int64  `json:"requestId,omitempty"`
	AccountId string `json:"accountId,omitempty"`
	PatientId string `json:"patientId,omitempty"`
	ProviderName string `json:"providerName,omitempty"`
}
