package coreapi

import (
	"testing"
)

func TestUserProfileIsValid(t *testing.T) {

	t.Run("valid profile with otherID", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "<PERSON>",
			LastName:  "<PERSON>",
			DOB:       "01/01/1972",
			AltId:     "someid",
		}

		if !profile.IsValid() {
			t.<PERSON>("user profile was invalid when valid was expected")
		}
	})

	t.Run("valid profile with ohip", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/1972",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if !profile.IsValid() {
			t.<PERSON>("user profile was invalid when valid was expected")
		}
	})

	t.Run("valid profile with mrn", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/1972",
			Mrn:       "7285927",
		}

		if !profile.IsValid() {
			t.<PERSON><PERSON>("user profile was invalid when valid was expected")
		}
	})

	t.Run("valid profile with ipn", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/1972",
			Ipn:       "847502849",
		}

		if !profile.IsValid() {
			t.Errorf("user profile was invalid when valid was expected")
		}
	})

	t.Run("valid profile with ssn", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/1972",
			Ssn:       "847502849",
		}

		if !profile.IsValid() {
			t.Errorf("user profile was invalid when valid was expected")
		}
	})

	t.Run("empty lname", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "",
			DOB:       "01/01/1972",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

	t.Run("empty fname", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "",
			LastName:  "Lauper",
			DOB:       "01/01/1972",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

	t.Run("empty names", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "",
			LastName:  "",
			DOB:       "01/01/1972",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

	t.Run("bad dob", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01011972",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

	t.Run("bad dob2", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/19725",
			Ohip:      "98123783272",
			Ohipvc:    "SD",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

	t.Run("no id", func(t *testing.T) {
		profile := UserProfile{
			FirstName: "Cyndi",
			LastName:  "Lauper",
			DOB:       "01/01/19725",
		}

		if profile.IsValid() {
			t.Errorf("user profile was valid when invalid was expected")
		}
	})

}
