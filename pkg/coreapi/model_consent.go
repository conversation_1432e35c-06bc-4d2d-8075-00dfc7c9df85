/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"time"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

type Consent struct {
	Opt            string                `json:"opt,omitempty"`
	FullName       string                `json:"fullName,omitempty"`
	SignatureImg   string                `json:"signatureImg,omitempty"`
	Email          string                `json:"email,omitempty"`
	PaymentDetails models.PaymentDetails `json:"paymentDetails"`
}

type ConsentData struct {
	ProviderId            int64
	OptIn                 bool
	OptOut                bool
	IsEnrolled            bool
	IsAppointmentReminder bool
}

type ConsentAppointmentData struct {
	// id of temp customer.io account, if consent link was sent out in appointment reminder
	CioTempAccountId string `json:"cio_temp_account_id,omitempty"`

	AppointmentCadence int `json:"appointment_cadence"`

	MessageType string `json:"message_type"` // e.g. 'Email', 'SMS', other message types can be added as needed
}

type ConsentUserData struct {
	FirstName string
	LastName  string
	Email     string
	Dob       time.Time
	Phone     string
}

type VerifiedConsent struct {
	Token      string `json:"token"`
	IsEnrolled bool   `json:"is_enrolled"`
}

type ConsentEmailVerification struct {
	Token string `json:"token"`
	Email string `json:"email"`
}

type ConsentType struct {
	Verified bool `json:"verified"`
}
