package coreapi

import (
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
)

type ProviderDisplayInfo struct {
	Name             string
	OrgName          string
	PtPngDownload    bool //allow patients in-app to downloads pngs of their exam images
	ClinicId         int64
	OrgId            int64
	ProviderClinicId string //the new clinic ID from orgsvc clinics table
	ProviderId       string //the new provider ID from orgsvc providers table
	ProviderTaxName  string
	ProviderPlans    []orgs.ProviderPlan
}
