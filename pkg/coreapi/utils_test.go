package coreapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateSecureURL(t *testing.T) {

	t.Run("should pass on valid url with no query params", func(t *testing.T) {
		_, err := ValidateSecureURL("https://www.example.com")
		assert.NoError(t, err)
	})

	t.Run("should pass on valid url with query params", func(t *testing.T) {
		_, err := ValidateSecureURL("https://example.com/path?query=value")
		assert.NoError(t, err)
	})

	t.Run("should fail on url with unsecured scheme - https", func(t *testing.T) {
		_, err := ValidateSecureURL("http://www.example.com")
		assert.Error(t, err)
	})

	t.Run("should fail on url with unsecured scheme - ftp ", func(t *testing.T) {
		_, err := ValidateSecureURL("ftp://example.com")
		assert.Error(t, err)
	})

	t.Run("should fail on url with no scheme", func(t *testing.T) {
		_, err := ValidateSecureURL("www.example.com")
		assert.Error(t, err)
	})

	t.Run("should fail on url with no host", func(t *testing.T) {
		_, err := ValidateSecureURL("https://")
		assert.Error(t, err)
	})

	t.Run("should fail on malformed url", func(t *testing.T) {
		_, err := ValidateSecureURL("https:/example.com")
		assert.Error(t, err)
	})

	t.Run("should fail on empty url", func(t *testing.T) {
		_, err := ValidateSecureURL("")
		assert.Error(t, err)
	})

	t.Run("should pass on valid url with spaces around", func(t *testing.T) {
		_, err := ValidateSecureURL("  https://spaced.com  ")
		assert.NoError(t, err)
	})

	t.Run("should pass on valid uppercase url", func(t *testing.T) {
		_, err := ValidateSecureURL("HTTPS://UPPERCASE.COM")
		assert.NoError(t, err)
	})

	t.Run("should fail with invalid url format", func(t *testing.T) {
		_, err := ValidateSecureURL("invalid-url-format")
		assert.Error(t, err)
	})

	t.Run("should fail with malformed url format with no scheme", func(t *testing.T) {
		_, err := ValidateSecureURL("://missingscheme.com")
		assert.Error(t, err)
	})
}
