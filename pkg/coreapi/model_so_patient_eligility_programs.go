package coreapi

type SORequestPatientEligibilityProgram struct {
	ProgramName ProgramName `json:"program_name"`
}

type SORequestUpdateUncompletedEligibilityPrograms struct {
	IsCompleted bool `json:"is_completed"`
}

type SORequestCreatePatientEligibilityProgram struct {
	PatientId        string      `json:"patient_id"`
	ProgramName      ProgramName `json:"program_name"`
	IsCompleted      bool        `json:"is_completed"`
	WithNotification bool        `json:"with_notification,omitempty"`
}

type ProgramName string

const (
	ProgramObsp              ProgramName = "obsp"
	ProgramBreastRiskScoring ProgramName = "breast_risk_scoring"
)

type PatientEligibilityProgram struct {
	Id          string      `json:"id"`
	AccountId   string      `json:"account_id"`
	PatientId   string      `json:"patient_id"`
	ProgramName ProgramName `json:"program_name"`
}
