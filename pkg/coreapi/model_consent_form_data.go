/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

type ConsentFormData struct {
	ProviderName    string   `json:"providerName"`
	ProviderId      int64    `json:"providerId"`
	ProviderPlanIds []uint64 `json:"providerPlanIds"`
	New             bool     `json:"new"`
	Opt             string   `json:"opt"`
	UnderstandItems []string `json:"understandItems"`
	ConsentText     string   `json:"consentText"`
	// id of temp customer.io account, if consent link was sent out in appointment reminder
	AppointmentReminderId string `json:"appointmentReminderId,omitempty"`
	// source of consent link
	ConsentSource string `json:"consentSource"`
}
