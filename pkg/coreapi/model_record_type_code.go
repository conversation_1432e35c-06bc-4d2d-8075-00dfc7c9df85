// From hlth rec svc

package coreapi

type RecordTypeCode int

// List of RecordTypeCode
const (
	ALLERGY_CODE        RecordTypeCode = 1
	CONDITION_CODE      RecordTypeCode = 2
	VACCINATION_CODE    RecordTypeCode = 3
	LAB_RESULT_CODE     RecordTypeCode = 4
	MEDICAL_TEST_CODE   RecordTypeCode = 5
	PRESCRIPTION_CODE   RecordTypeCode = 6
	PROCEDURE_CODE      RecordTypeCode = 7
	REFERRAL_CODE       RecordTypeCode = 8
	TREATMENT_PLAN_CODE RecordTypeCode = 9
	OTHER_CODE          RecordTypeCode = 10
)

func (c RecordTypeCode) String() string {
	switch c {
	case ALLERGY_CODE:
		return "ALLERGY"
	case CONDITION_CODE:
		return "CONDITION"
	case VACCINATION_CODE:
		return "VACCINATION"
	case LAB_RESULT_CODE:
		return "LAB_RESULT"
	case MEDICAL_TEST_CODE:
		return "MEDICAL_TEST"
	case PRESCRIPTION_CODE:
		return "PRESCRIPTION"
	case PROCEDURE_CODE:
		return "PROCEDURE"
	case REFERRAL_CODE:
		return "REFERRAL"
	case TREATMENT_PLAN_CODE:
		return "TREATMENT_PLAN"
	default:
		return "OTHER"
	}
}
