package coreapi

// String enum for patient status types for appointment reminders
type AppointmentReminderPatientStatus string

// List of patient status types
const (
	PATIENT_CONFIRMED AppointmentReminderPatientStatus = "confirmed"
	PATIENT_CANCELLED AppointmentReminderPatientStatus = "cancelled"
)

type AppointmentReminderPatientStatusUpdate struct {
	ReminderId    string                           `json:"reminder_id"`
	PatientStatus AppointmentReminderPatientStatus `json:"patient_status"`
	ProviderId    string                           `json:"provider_id"`
}

// This is the same as above but with the provider id as an int to pass down to further services
type AppointmentReminderPatientStatusUpdateRequest struct {
	ReminderId    string                           `json:"reminder_id"`
	PatientStatus AppointmentReminderPatientStatus `json:"patient_status"`
	ProviderId    int64                            `json:"provider_id"`
}

func (a AppointmentReminderPatientStatusUpdate) ToRequestBody(providerId int64) AppointmentReminderPatientStatusUpdateRequest {
	return AppointmentReminderPatientStatusUpdateRequest{
		ReminderId:    a.<PERSON>minderId,
		PatientStatus: a.PatientStatus,
		ProviderId:    providerId,
	}
}
