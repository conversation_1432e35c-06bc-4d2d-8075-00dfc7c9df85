package coreapi

import (
	"context"
	"io"
	"mime/multipart"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
)

type PrivateV2TransfersApiRouter interface {
	PostTransfer(http.ResponseWriter, *http.Request)
	PostTransferImages(http.ResponseWriter, *http.Request)
	PostTransferReportDCM(http.ResponseWriter, *http.Request)
	DeleteTransfer(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2TransfersApiServicer interface {
	PostTransfer(
		context.Context,
		string,
		string,
		string,
		string,
		[]UploadImage,
	) (interface{}, error)
	PostTransferImages(context.Context, string, string, string, string, *multipart.Reader) error
	PostTransferReportDCM(
		context.Context,
		string,
		string,
		string,
		string,
		UploadFileMetadata,
		[]*multipart.FileHeader,
	) error
	DeleteTransfer(context.Context, string, string, string, string) error
}
type PublicV2UsersApiRouter interface {
	PostUsersSetup(http.ResponseWriter, *http.Request)
	PostUsersVerify(http.ResponseWriter, *http.Request)
	PostUsersLogin(http.ResponseWriter, *http.Request)
	PostUsersRefresh(http.ResponseWriter, *http.Request)
	PostUserResetPasswordInit(w http.ResponseWriter, r *http.Request)
	PostUserPasswordReset(w http.ResponseWriter, r *http.Request)
	PostLockAccount(w http.ResponseWriter, r *http.Request)
	PostVerificationCode(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateV2UsersApiRouter interface {
	PostUsersEmailUpdateInit(http.ResponseWriter, *http.Request)
	GetAccountState(w http.ResponseWriter, r *http.Request)
	GetAccountEnrolmentProviders(w http.ResponseWriter, r *http.Request)
	GetEnrolmentByAccountId(w http.ResponseWriter, r *http.Request)
	GetUserExamLookup(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2UsersApiServicer interface {
	PostUsersSetup(
		context.Context,
		accountservice.Verification,
	) (accountservice.PasswordSetup, error)
	PostUsersEmailUpdateInit(context.Context, string, EmailUpdate) error
	PostUsersVerify(
		context.Context,
		accountservice.Verification,
	) (accountservice.VerifyEmailResponse, error)
	PutUsersEmail(context.Context, accountservice.UsersEmailUpdate) error
	PostUsersLogin(context.Context, string, string, string) (interface{}, string, error)
	PostUsersRefresh(context.Context, string, string) (interface{}, error)
	PostUserResetPasswordInit(context.Context, string) error
	PostUserPasswordReset(context.Context, PasswordResetInfo) error
	PostLockAccount(context.Context, string) error
	PatchDeactivateAccount(context.Context, string, string) error
	PatchSetAccountOwner(context.Context, string, models.SetAccountOwnerRequest) error
	PostVerificationCode(context.Context, accountservice.VerificationCode, string) (string, error)
	PostEmailVerification(context.Context, string) (accountservice.EmailVerification, error)
	PostVerifyDateOfBirth(context.Context, accountservice.DateOfBirthVerification) (bool, error)
	GetAccountState(context.Context, string) (AccountState, error)
	GetAccountEnrolmentProviders(context.Context, string) ([]int64, error)
	GetEnrolmentByAccountId(context.Context, string) ([]Enrollment, error)
	GetUserExamLookup(
		ctx context.Context,
		ssoToken string,
		acctId string,
		accession string,
	) (string, error)
}

type PrivateV2SharesApiRouter interface {
	GetShareExamsByshareId(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2SharesApiServicer interface {
	GetShareExamsByShareId(context.Context, string, string) (map[string]interface{}, error)
}

type PublicV2ProvidersApiRouter interface {
	GetPlans(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2ProvidersApiServicer interface {
	GetPlans(context.Context, uint, string, string) ([]planservice.PlanV1, error)
}

type PrivateV2OrdersApiRouter interface {
	PostOrder(http.ResponseWriter, *http.Request)
	GetOrders(http.ResponseWriter, *http.Request)
	PutOrderPaymentDetails(http.ResponseWriter, *http.Request)
	PostOrderActionReason(w http.ResponseWriter, r *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2OrdersApiServicer interface {
	PostOrder(
		context.Context,
		string,
		OrderRequest,
		string,
	) (accountservice.CreateOrderResponse, error)
	GetOrders(context.Context, string, map[string]bool) ([]Order, error)
	PutOrderPaymentDetails(context.Context, string, string, uint16, models.PaymentDetails) error
	PostOrderActionReason(context.Context, string, string, accountservice.OrderActionReason) error
}

type PublicV2PlanApiRouter interface {
	GetPlans(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2PlanApiServicer interface {
	GetPlans(context.Context, bool, string, []uint, uint64) (interface{}, error)
	GetPlanById(context.Context, int32) (*planservice.PlanV2, error)
}

type PublicV2FeatureApiRouter interface {
	AuthorizeFeature(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2FeatureApiServicer interface {
	AuthorizeFeature(context.Context, planservice.FeatureId, string, uint) (interface{}, error)
}

type V2PatientsApiServicer interface {
	DeletePatient(context.Context, string, string) error
	PostPatient(context.Context, string, string, models.PhiPatient) (string, error)
	PatchPatient(context.Context, string, string, models.PhiPatient) error
	GetPatients(context.Context, string) (interface{}, error)
	GetPatient(context.Context, string, string) (interface{}, error)
	GetAllPatientProfileValidations(
		ctx context.Context,
		accountId string,
	) (map[string]accountservice.PatientProfileValidation, error)
	GetPatientProfileValidation(
		ctx context.Context,
		accountId, patientId string,
	) (accountservice.PatientProfileValidation, error)
	CreateNotificationForIncompletePatientsOnAccount(
		ctx context.Context,
		accountId string,
	) (bool, error)
}

type V2PrivatePatientsApiRouter interface {
	DeletePatient(http.ResponseWriter, *http.Request)
	PostPatient(http.ResponseWriter, *http.Request)
	PatchPatient(http.ResponseWriter, *http.Request)
	GetPatients(http.ResponseWriter, *http.Request)
	GetPatient(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
	GetAllPatientProfileValidations(w http.ResponseWriter, r *http.Request)
	GetPatientProfileValidation(w http.ResponseWriter, r *http.Request)
	PostIncompletePatientInfoNotification(w http.ResponseWriter, r *http.Request)
}

type PrivateV2HealthrecordsApiRouter interface {
	GetHealthRecords(http.ResponseWriter, *http.Request)
	PostHealthRecordsUpload(http.ResponseWriter, *http.Request)
	PutHealthRecord(http.ResponseWriter, *http.Request)
	GetHealthRecordThumbnailById(http.ResponseWriter, *http.Request)
	GetHealthRecordById(http.ResponseWriter, *http.Request)
	DeleteHealthRecordById(http.ResponseWriter, *http.Request)
	CreateMyChartIntegration(http.ResponseWriter, *http.Request)
	GenerateGailRiskResult(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2UploadHealthRecordsApiRouter interface {
	PostVerifyToken(http.ResponseWriter, *http.Request)
	PostHealthRecords(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type V2HealthRecordsApiServicer interface {
	ValidateAuth(http.Handler) http.Handler
	PostVerifyToken(context.Context, string, string, string) (interface{}, error)
	PostHealthRecords(
		context.Context,
		string,
		string,
		string,
		*multipart.Reader,
		string,
	) (interface{}, error)
	GetHealthRecords(context.Context, string, string) (interface{}, error)
	PostHealthRecordsUpload(context.Context, string, string, string, string) (interface{}, error)
	PutHealthRecord(
		context.Context,
		string,
		string,
		string,
		*multipart.Reader,
		string,
		string,
	) (interface{}, error)
	GetHealthRecordThumbnailById(
		context.Context,
		string,
		string,
		string,
		string,
	) (io.ReadCloser, error)
	GetHealthRecordById(
		context.Context,
		string,
		string,
		string,
		string,
	) (io.ReadCloser, string, error)
	DeleteHealthRecordById(context.Context, string, string, string) error
	CreateMyChartIntegration(context.Context, string, string, *MyChartIntegrationRequest) error
	SearchMyChartOrgs(context.Context, string) ([]FHIROrg, error)
	GenerateGailRiskResult(
		context.Context,
		string,
		string,
		*GailQuestionnairePatientResponses,
	) ([]byte, error)
	GetRecordsByType(
		ctx context.Context,
		accountId string,
		phPatientId string,
		recordType string,
		limit int,
	) ([]Record, error) // ToDo: Eventually add method for GAIL/new stuff, add pagination if ever needed
}
