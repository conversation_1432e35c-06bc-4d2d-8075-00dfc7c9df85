package coreapi

import (
	"time"
)

type NotificationType string

// List of Notification types - the column for this value is `name`
const (
	NotificationMultiRegion           NotificationType = "MULTI_REGION_USER"
	NotificationObsp                  NotificationType = "OBSP"
	NotificationBreastRiskScoring     NotificationType = "BREAST_RISK_SCORING"
	NotificationPatientInfoIncomplete NotificationType = "PATIENT_INFO_INCOMPLETE"
	NotificationCaregiving            NotificationType = "CAREGIVING"
	NotificationAppointmentPrep       NotificationType = "APPOINTMENT_PREP"
	NotificationRho                   NotificationType = "RHO"
	NotificationImageReader           NotificationType = "IMAGE_READER"
	NotificationReportExplanations    NotificationType = "REPORT_EXPLANATIONS"
	NotificationLifeLabsExperiment    NotificationType = "LIFELABS_EXPERIMENT"
)

func (notif NotificationType) Valid() bool {
	for _, value := range []NotificationType{
		NotificationMultiRegion,
		NotificationObsp,
		NotificationBreastRiskScoring,
		NotificationPatientInfoIncomplete,
		NotificationCaregiving,
		NotificationAppointmentPrep,
		NotificationRho,
		NotificationImageReader,
		NotificationReportExplanations,
		NotificationLifeLabsExperiment,
	} {
		if notif == value {
			return true
		}
	}
	return false
}

type Notification struct {
	Id          string            `json:"id"`
	Type        NotificationType  `json:"type"`
	Read        bool              `json:"read"`
	PatientId   string            `json:"patient_id"`
	CreatedDate time.Time         `json:"created_date"`
	Data        *NotificationData `json:"data,omitempty"`
}

type NotificationData struct {
	ExamUuid string `json:"exam_uuid,omitempty"`
}
