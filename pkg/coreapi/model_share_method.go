/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

// ShareMethod : String enum for share method. AccessPagePrint, AccessPageFax, or Email
type ShareMethod string

// List of ShareMethod
const (
	EMAIL             ShareMethod = "Email"
	ACCESS_PAGE_PRINT ShareMethod = "AccessPagePrint"
	ACCESS_PAGE_FAX   ShareMethod = "AccessPageFax"
	ISO               ShareMethod = "ISO"
	ZIP               ShareMethod = "ZIP"
	RECORD_STREAMING  ShareMethod = "RecordStreaming"
)

func (s *ShareMethod) IsOfflineShare() bool {
	return *s == ISO || *s == ZIP
}
