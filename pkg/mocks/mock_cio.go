package mocks

type MockCIOProducerData struct {
	CallLogs []map[string]interface{}
}

// UpdateUserAttributes produces/updates an user attribute with the given `metadata`. An `updated_at`
// timestamp is always automatically generated.
func (p *MockCIOProducerData) UpdateUserAttributes(
	acctId string,
	email string,
	metadata map[string]interface{},
) error {
	p.CallLogs = append(p.CallLogs, map[string]interface{}{
		"method":   "UpdateUserAttributes",
		"acctId":   acctId,
		"email":    email,
		"metadata": metadata,
	})
	return nil
}

// Produce produces/updates an user attribute and an event with the given `eventName` and `eventAttributes`.
func (p *MockCIOProducerData) Produce(
	acctId string,
	email string,
	eventName string,
	userAttributes map[string]interface{},
	eventAttributes map[string]interface{},
) error {
	p.CallLogs = append(p.CallLogs, map[string]interface{}{
		"method":          "Produce",
		"acctId":          acctId,
		"email":           email,
		"eventName":       eventName,
		"userAttributes":  userAttributes,
		"eventAttributes": eventAttributes,
	})
	return nil
}

// Produce an event with the given `eventName` and `eventAttributes`.
func (p *MockCIOProducerData) ProduceEvent(
	acctId string,
	eventName string,
	eventAttributes map[string]interface{},
) error {
	p.CallLogs = append(p.CallLogs, map[string]interface{}{
		"method":          "ProduceEvent",
		"acctId":          acctId,
		"eventName":       eventName,
		"eventAttributes": eventAttributes,
	})
	return nil
}
