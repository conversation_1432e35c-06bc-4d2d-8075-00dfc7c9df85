#!/bin/bash

set -e

# dir of this script; borrowed from S.O.:
# https://stackoverflow.com/questions/59895/how-to-get-the-source-directory-of-a-bash-script-from-within-the-script-itself
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

source "$SCRIPT_DIR/config.dev.sh"

echo "setting vars for loading apptest sql"
echo "-- $SCRIPT_DIR/init_apptest.sql"
MYSQL_PWD=sqlrootpw mysql -h "$sql_host" --port "$sql_port" --user "root" --database pockethealth < "$SCRIPT_DIR/init_apptest.sql"

echo "adding sql test data with schema"
for file in $(ls "$SCRIPT_DIR"/sql/*.sql | grep -v ".zip" ); do
	echo "-- $file"
	MYSQL_PWD=$sql_pw mysql -h "$sql_host" --port "$sql_port" --user "$sql_user" --database pockethealth < "$file"
done

echo "adding goose migrations"
goose -dir ../migrations mysql 'devuser:devpw@tcp(127.0.0.1:3306)/pockethealth?parseTime=true'  up

echo "adding sql test data only"
for file in $(ls "$SCRIPT_DIR/sql/dataonly" | grep -v ".zip"); do
	echo "-- $file"
	MYSQL_PWD=$sql_pw mysql -h "$sql_host" --port "$sql_port" --user "$sql_user" --database pockethealth < "sql/dataonly/$file"
done

echo "resetting vars for loading apptest sql"
echo "-- $SCRIPT_DIR/finish_apptest.sql"
MYSQL_PWD=sqlrootpw mysql -h "$sql_host" --port "$sql_port" --user "root" --database pockethealth < "$SCRIPT_DIR/finish_apptest.sql"
