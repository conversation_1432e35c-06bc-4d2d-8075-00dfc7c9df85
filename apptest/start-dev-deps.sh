#!/bin/bash

set -e

# dir of this script; borrowed from S.O.:
# https://stackoverflow.com/questions/59895/how-to-get-the-source-directory-of-a-bash-script-from-within-the-script-itself
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
source "tmp/config.sh"

docker compose -f "$SCRIPT_DIR/../docker-compose.yml" up -d mysql

echo "Waiting for MySQL to start up..."
iter=0
SECONDS=0
while [ $iter -lt 10 ] && ! mysqladmin ping -h"$sql_host" -u "$sql_user" -p'$sql_pw' --silent; do
    sleep 5
    iter=$(($iter + 1))
done

echo "MySQL took $SECONDS sec"
mysqladmin ping -h"$sql_host" -u "$sql_user" -p"$sql_pw"
if [ $? -ne 0 ]; then
    echo "MySQL setup did not work"
    exit 1
fi
