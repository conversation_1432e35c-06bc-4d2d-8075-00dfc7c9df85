package apptest

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"testing"
	"time"
)

func TestRegisterInvalidPassword(t *testing.T) {

	t.Run("existing email invalid password", func(t *testing.T) {

		client := &http.Client{}
		email := "<EMAIL>"

		// make post /users request
		req, err := http.NewRequest(
			"POST",
			host+"/v1/users",
			strings.NewReader(
				fmt.Sprintf(
					`{"email": %q, "password": "password", "dob":"2020-02-02T09:00:00Z", "first_name":"J", "last_name":"<PERSON>"}`,
					email,
				),
			),
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.<PERSON>rror())
		}
		req.Header.Add("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			t.<PERSON>rf("got error when expected none: %q", err.<PERSON>rror())
		}

		respBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatal("couldn't read response body", err)
		}

		if resp.StatusCode != 400 {
			t.Errorf("expected status code %d got %d", 400, resp.StatusCode)
		}

		if !strings.Contains(string(respBody), "dictionary word:2") {
			t.Error("expected body to contains 'dictionary word:2' but body was", string(respBody))
		}
	})

	t.Run("new email invalid password", func(t *testing.T) {

		client := &http.Client{}
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		email := "kelvin" + rand + "@pocket.health"

		// make post /users request
		req, err := http.NewRequest(
			"POST",
			host+"/v1/users",
			strings.NewReader(
				fmt.Sprintf(
					`{"email": %q, "password": "password", "dob":"2020-02-02T09:00:00Z", "first_name":"J", "last_name":"J"}`,
					email,
				),
			),
		)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		req.Header.Add("Content-Type", "application/json")
		q := req.URL.Query()
		req.URL.RawQuery = q.Encode()

		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}

		respBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatal("couldn't read response body", err)
		}

		if resp.StatusCode != 400 {
			t.Errorf("expected status code %d got %d", 400, resp.StatusCode)
		}

		if !strings.Contains(string(respBody), "dictionary word:2") {
			t.Error("expected body to contain 'dictionary word:2' but body was", string(respBody))
		}
	})
}
