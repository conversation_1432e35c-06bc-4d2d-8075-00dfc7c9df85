package apptest

import (
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetExams(t *testing.T) {

	token := LoginAndGetToken("<EMAIL>", "test1234")
	t.Run("unauthorized get exams", func(t *testing.T) {
		client := &http.Client{}
		req, _ := http.NewRequest("GET", host+"/v1/users/exams", nil)
		resp, err := client.Do(req)

		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("authorized get exams, activated param true", func(t *testing.T) {
		client := &http.Client{}
		req, _ := http.NewRequest("GET", host+"/v1/users/exams?activated=true", nil)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)

		if err != nil {
			t.Fatalf("could not get resp body: %v", err)
		}
		var jsonList []json.RawMessage
		err = json.Unmarshal(body, &jsonList)
		require.NoError(t, err)

		for _, sh := range jsonList {
			var examJson map[string]json.RawMessage
			err = json.Unmarshal(sh, &examJson)
			if err != nil {
				t.Errorf("Expected exam objects to be unmarshalable json")
			}
			var examId string
			var reportDelay int
			var activated bool
			err1 := json.Unmarshal(examJson["examId"], &examId)
			if err1 != nil {
				t.Errorf("Expected exam json to contain examId")
			}
			err3 := json.Unmarshal(examJson["reportDelay"], &reportDelay)
			if err3 != nil {
				t.Errorf("Expected exam json to contain reportDelay")
			}
			err4 := json.Unmarshal(examJson["activated"], &activated)
			if err4 != nil {
				t.Errorf("Expected exam json to contain activated")
			}

		}

	})

	t.Run("authorized get exams, activated param false", func(t *testing.T) {
		client := &http.Client{}
		req, _ := http.NewRequest("GET", host+"/v1/users/exams?activated=false", nil)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not get resp body: %v", err)
		}
		var jsonList []json.RawMessage
		err = json.Unmarshal(body, &jsonList)
		require.NoError(t, err)

		for _, sh := range jsonList {
			var examJson map[string]json.RawMessage
			err = json.Unmarshal(sh, &examJson)
			if err != nil {
				t.Errorf("Expected exam objects to be unmarshalable json")
			}
			var examId, extype, phone, mrn string
			var ptName, seriesList, reports []json.RawMessage
			err1 := json.Unmarshal(examJson["examId"], &examId)
			if err1 != nil {
				t.Errorf("Expected exam json to contain examId")
			}
			err1 = json.Unmarshal(examJson["examType"], &extype)
			if err1 != nil {
				t.Errorf("Expected exam json to contain examType")
			}
			err2 := json.Unmarshal(examJson["series"], &seriesList)
			if err2 == nil {
				t.Errorf("Expected exam json to not contain series list")
			}
			err3 := json.Unmarshal(examJson["reports"], &reports)
			if err3 == nil {
				t.Errorf("Expected exam json to not contain reports list")
			}
			err4 := json.Unmarshal(examJson["patientName"], &ptName)
			if err4 == nil {
				t.Errorf("Expected exam json to not contain patientName")
			}
			err5 := json.Unmarshal(examJson["phone"], &phone)
			if err5 == nil {
				t.Errorf("Expected exam json to not contain phone")
			}
			err6 := json.Unmarshal(examJson["patientMrn"], &mrn)
			if err6 == nil {
				t.Errorf("Expected exam json to not contain phone")
			}
		}
	})

	t.Run("authorized get exams, invalid activated param ", func(t *testing.T) {
		client := &http.Client{}
		req, _ := http.NewRequest("GET", host+"/v1/users/exams?activated=fals", nil)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusBadRequest {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusBadRequest,
			)
		}
	})

	t.Run("authorized get exams, activated param not set", func(t *testing.T) {
		client := &http.Client{}
		req, _ := http.NewRequest("GET", host+"/v1/users/exams", nil)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonList []json.RawMessage
		err = json.Unmarshal(body, &jsonList)
		require.NoError(t, err)

		for _, sh := range jsonList {
			var examJson map[string]json.RawMessage
			err = json.Unmarshal(sh, &examJson)
			if err != nil {
				t.Errorf("Expected exam objects to be unmarshalable json")
			}
			var examId string
			var reportDelay int
			var activated bool
			err1 := json.Unmarshal(examJson["examId"], &examId)
			if err1 != nil {
				t.Errorf("Expected exam json to contain examId")
			}
			err3 := json.Unmarshal(examJson["reportDelay"], &reportDelay)
			if err3 != nil {
				t.Errorf("Expected exam json to contain reportDelay")
			}
			err4 := json.Unmarshal(examJson["activated"], &activated)
			if err4 != nil {
				t.Errorf("Expected exam json to contain activated")
			}

		}
	})

}
