package apptest

import (
	"net/http"
	"testing"
)

func TestUnauthorizedSubscription(t *testing.T) {

	t.Run("put auto renew subscription", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest("PUT", host+"/v1/users/subscription/toggleautorenew", nil)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.E<PERSON>rf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}
