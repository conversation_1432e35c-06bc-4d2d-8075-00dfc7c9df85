package apptest

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func validateEmailShare(t *testing.T, pin string, id string) (token string, err error) {
	validateCreds := fmt.Sprintf(`{"shareId": %q, "pin": %q}`, id, pin)
	shareReq, err := http.NewRequest(
		"POST",
		host+"/v1/shares/validate",
		strings.NewReader(validateCreds),
	)
	client := &http.Client{}
	resp, err := client.Do(shareReq)
	if err != nil {
		return "", fmt.Errorf("got error when expected none: %q", err.Error())
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf(
			"got status code %d when expected : %d",
			resp.StatusCode,
			http.StatusOK,
		)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("could not get resp body: %v", err)
	}

	var validateJson map[string]json.RawMessage
	err = json.Unmarshal(body, &validateJson)
	if err != nil {
		return "", fmt.Errorf("Expected share validate objects to be unmarshalable json")
	}
	err1 := json.Unmarshal(validateJson["token"], &token)
	if err1 != nil {
		return "", fmt.Errorf("Expected validate share json to contain token.")
	}
	return token, nil
}

func createNewShare(t *testing.T, shareRecipient string, shareMethod string, token string) (string, error) {
	jsonBytes := generateJson(shareMethod, shareRecipient)
	_, shareId, err := postNewShare(t, token, jsonBytes)
	require.NoError(t, err)
	t.Cleanup(func() {
		teardownPostShare(shareId)
	})

	//POST /shares does some asynchronous work. To ensure it is complete before validating any results, sleep here.

	time.Sleep(3 * time.Second)
	return shareId, err
}

func generateJson(method string, recipient string) []byte {
	return []byte(fmt.Sprintf(
		`{
				"examList":[
					{"uuid":"2qLzNzIQtwHHzrtWeN7FSJ5UURT","examId":"1.2.840.10008.114051.752239.**********.450643366.**********","patientName":{"dicomName":"SMITH^PAUL^MICHAEL","firstAndMiddleName":"Paul Michael","lastName":"Smith"},
						"examType":"Ultrasound","examDate":"Nov 07, 2020","activated":true,"description":"TRANSESOPHAGEL ECHO","dob":"1964/02/12","sex":"M","transferId":"2qLzNzV3GTJL24YeVqUjffaOfxO","bodypart":"Unknown","reportDelay":0,
						"referringPhysician":"Dr.  Sarah Brown","size":0,"orgId":0,"facilityFunded":false,"modality":"US","allow_pt_png_dls":false,"patientId":"2qLzSZaU8Yze7DOJrLTRY08VnDw","unlockStatus":"fullAccess"},
					{"uuid":"2qM0BUsRMvUQUxaN59xMAMPxvky","examId":"1.2.840.10008.114051.752239.**********.450643366.**********","patientName":{"dicomName":"SMITH^PAUL^MICHAEL","firstAndMiddleName":"Paul Michael","lastName":"Smith"},
						"examType":"Ultrasound","examDate":"Nov 07, 2020","activated":true,"description":"TRANSESOPHAGEL ECHO","dob":"1964/02/12","sex":"M","transferId":"2qM0BT16ZNJ4h4jE2IdNkvk3YId","bodypart":"Unknown","reportDelay":0,
						"referringPhysician":"Dr.  Sarah Brown","size":0,"orgId":0,"facilityFunded":false,"modality":"US","allow_pt_png_dls":false,"patientId":"2qLzSZaU8Yze7DOJrLTRY08VnDw","unlockStatus":"fullAccess"}],
				"healthRecords":[{"source":{"type":"UPLOAD","description":"old man yells"},"recordDate":"1988-05-01T00:00:00+00:00","name":"docker","description":"","typeCode":7,"createdDate":"2024-12-17T17:40:08+00:00",
					"filenames":["old-man-yells-at-docker.png"],"urls":["https://phdevcentralcabloblrs.blob.core.windows.net/healthrecords/637e0329-0c8c-4d58-a6cc-aa653a6f1c31"],"tag":"","fhir_resource_type":"DocumentReference","data":null,"patientId":"2qLzSZaU8Yze7DOJrLTRY08VnDw","id":"802abd9c-67ce-40d8-8d3b-03c1d5c079ce"}],
				"recipient":"%s",
				"method":"%s",
				"mode":"multiple","date":"Tue Dec 17 2024 12:44:20 GMT-0500 (Eastern Standard Time)","ccUser":false}`,
		recipient, method,
	))
}

func TestShareDownload(t *testing.T) {
	t.Run("invalid account token", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			fmt.Sprintf("%s/v1/shares/%s", host, ShareNotBelongToAccount),
			nil,
		)
		require.NoError(t, err)
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()
		require.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("with share token", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		//find share pin
		var sharePin string
		err = sqldb.QueryRow("SELECT pin FROM shares WHERE share_id=?", shareId).
			Scan(&sharePin)
		require.NoError(t, err)

		shareToken, err := validateEmailShare(t, sharePin, shareId)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId, nil)
		require.NoError(t, err)

		req.Header.Set("Authorization", "Bearer "+shareToken)
		resp, err := client.Do(req)
		require.NoError(t, err)

		defer resp.Body.Close()
		require.Equal(t, http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err, "could not read GET /shares/{id} body")
		var jsonMap map[string]json.RawMessage
		err = json.Unmarshal(body, &jsonMap)
		require.NoError(t, err, "expected response from GET /shares/{shareId} to be a json object")
		validateJsonContainsExpectedFields(t, jsonMap)

	})

	t.Run("set account token, accept not set", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId, nil)
		require.NoError(t, err, "could not create request")
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()
		require.Equal(t, http.StatusOK, resp.StatusCode, "unexpected status code for GETting new share")

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err, "could not read resp body for GET /shares/{id}")
		var jsonMap map[string]json.RawMessage
		err = json.Unmarshal(body, &jsonMap)
		require.NoError(t, err, "expect shares response to be in json format")
		validateJsonContainsExpectedFields(t, jsonMap)
	})

	t.Run("valid account token, accept json", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId, nil)
		req.Header.Set("Authorization", token)
		req.Header.Set("accept", "application/json")
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		if !strings.Contains(resp.Header.Get("Content-type"), "application/json") {
			t.Errorf(
				"got content-type %s when expected application/json",
				resp.Header.Get("Content-type"),
			)
		}
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		err = json.Unmarshal(body, &jsonMap)
		if err != nil {
			t.Errorf("expected response from GET /shares/{shareId} to be a json object")
		}

		validateJsonContainsExpectedFields(t, jsonMap)
	})

	t.Run("valid account token, accept zip", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId, nil)
		req.Header.Set("Authorization", token)
		req.Header.Set("accept", "application/zip")
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		if resp.Header.Get("Content-type") != "application/zip" {
			t.Errorf(
				"got content-type %s when expected application/zip",
				resp.Header.Get("Content-type"),
			)
		}
	})

	t.Run("valid account token, accept octet", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId, nil)
		req.Header.Set("Authorization", token)
		req.Header.Set("accept", "application/octet-stream")
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		if resp.Header.Get("Content-type") != "application/octet-stream" {
			t.Errorf(
				"got content-type %s when expected application/octet-stream",
				resp.Header.Get("Content-type"),
			)
		}
		respFile := "shareResp.iso"
		f, err := os.Create(respFile)
		if err != nil {
			t.Fatalf("couldn't create response file to copy response to: %v", err)
		}
		numBytes, err := io.Copy(f, resp.Body)
		if err != nil {
			t.Fatalf("couldn't copy share body: %v", err)
		}
		t.Cleanup(func() {
			os.Remove(respFile)
		})
		lenStr := resp.Header.Get("Content-Length")
		if strconv.Itoa(int(numBytes)) != lenStr {
			t.Fatalf("content length header (%v) does not match body size (%v)", lenStr, numBytes)
		}

	})

	t.Run("valid account token, more than 30 exams", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		mockExamIds := "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31"
		req, err := http.NewRequest(
			"GET",
			host+"/v1/shares/"+shareId+"?eids="+mockExamIds,
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusBadRequest {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusBadRequest,
			)
		}
	})

	t.Run("invalid shareId, share not exist", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			host+"/v1/shares/643sUuYe67MXw9gp81DZ5vTnhJA1IyfNI0cKjnLiw=",
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}

	})
}

func TestHealthRecordShareDownload(t *testing.T) {
	t.Run("invalid account token", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			host+"/v1/shares/643sUuYe67MXw_9gOp81DZ5vTnhJA1IyfNI0cKjnLiw=/healthrecords",
			nil,
		)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("with share token", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		//find share pin
		var sharePin string
		err = sqldb.QueryRow("SELECT pin FROM shares WHERE share_id=?", shareId).
			Scan(&sharePin)
		if err != nil {
			t.Fatal(err)
		}

		shareToken, err := validateEmailShare(t, sharePin, shareId)
		if err != nil {
			t.Fatal(err)
		}

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId+"/healthrecords", nil)

		req.Header.Set("Authorization", "Bearer "+shareToken)
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		if resp.Header.Get("Content-type") != "application/zip" {
			t.Errorf(
				"got content-type %s when expected application/zip",
				resp.Header.Get("Content-type"),
			)
		}
		if cd := resp.Header.Get("Content-Disposition"); !strings.Contains(cd, "HEALTHRECORDS_") {
			t.Errorf(
				"got content-disposition %s but expected it to include the filename %s",
				cd,
				"HEALTHRECORDS_",
			)
		}

	})

	t.Run("with account token", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares/"+shareId+"/healthrecords", nil)
		req.Header.Set("Authorization", token)
		req.Header.Set("accept", "application/zip")
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		if resp.Header.Get("Content-type") != "application/zip" {
			t.Errorf(
				"got content-type %s when expected application/zip",
				resp.Header.Get("Content-type"),
			)
		}
		if cd := resp.Header.Get("Content-Disposition"); !strings.Contains(cd, "HEALTHRECORDS_") {
			t.Errorf(
				"got content-disposition %s but expected it to include the filename %s",
				cd,
				"HEALTHRECORDS_",
			)
		}

	})

	t.Run("valid account token, more than 30 records", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
		shareId, err := createNewShare(t, "<EMAIL>", "Email", token)
		require.NoError(t, err)

		client := &http.Client{}
		mockRecordIDs := "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31"
		req, err := http.NewRequest(
			"GET",
			host+"/v1/shares/"+shareId+"/healthrecords?hrids="+mockRecordIDs,
			nil,
		)
		require.NoError(t, err)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusBadRequest {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusBadRequest,
			)
		}

	})

	t.Run("invalid shareId, share not exist", func(t *testing.T) {
		token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)

		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			host+"/v1/shares/643sUuYe67MXw9gp81DZ5vTnhJA1IyfNI0cKjnLiw=/healthrecords",
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func validateJsonContainsExpectedFields(t *testing.T, jsonMap map[string]json.RawMessage) {
	var shareId, eunityToken, date string
	var active bool
	err1 := json.Unmarshal(jsonMap["shareId"], &shareId)
	require.NoError(t, err1, "Expected share json to contain shareId")
	err2 := json.Unmarshal(jsonMap["active"], &active)
	require.NoError(t, err2, "Expected share json to contain activated")
	err3 := json.Unmarshal(jsonMap["date"], &date)
	require.NoError(t, err3, "Expected share json to contain date")
	err4 := json.Unmarshal(jsonMap["eunityToken"], &eunityToken)
	require.NoError(t, err4, "Expected share json to contain eunitytoken")

}
