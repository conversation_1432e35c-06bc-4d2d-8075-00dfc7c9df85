package apptest

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestProviders(t *testing.T) {

	t.Run("Get consent without auth", func(t *testing.T) {
		resp, err := http.Get(
			host + "/v1/providers/consents/39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=",
		)

		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("Post consent without auth", func(t *testing.T) {
		requestBody := "{\"opt\":\"in\",\"fullName\":\"Patient One\",\"signatureImg\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==\"}"
		resp, err := http.Post(
			host+"/v1/providers/consents/39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=",
			"application/json",
			bytes.NewBufferString(requestBody),
		)

		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("Post/Get consent", func(t *testing.T) {
		token := GetVerifiedConsentToken("39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=", "********")

		client := &http.Client{}
		// post consent, consent already has an account
		requestBody := "{\"opt\":\"in\",\"fullName\":\"Patient One\",\"signatureImg\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==\"}"
		req, err := http.NewRequest(
			http.MethodPost,
			host+"/v1/providers/consents/39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=",
			bytes.NewBufferString(requestBody),
		)
		require.NoError(t, err)

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", token)

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		// get consent
		getRequest, err := http.NewRequest(
			http.MethodGet,
			host+"/v1/providers/consents/39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=",
			nil,
		)
		require.NoError(t, err)

		getRequest.Header.Set("Content-Type", "application/json")
		getRequest.Header.Set("Authorization", token)

		resp, err = client.Do(getRequest)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)

		assert.NotEmpty(
			t,
			string(jsonMap["providerName"]),
			"expected provider name as 'Demo Corp', but got none",
		)
		assert.NotEmpty(
			t,
			string(jsonMap["consentText"]),
			"expected consent text, but got empty",
		)
	})

	t.Run("Get consent with mismatched token", func(t *testing.T) {
		token := GetVerifiedConsentToken("39AWKIzRVng91wCG8wZcnQHPafzIA8lGhuJ4gQ0q5FY=", "********")

		// get different consent
		client := &http.Client{}
		getRequest, err := http.NewRequest(
			http.MethodGet,
			host+"/v1/providers/consents/BJOokBZk7VPuAx1gZfhnX1N0zQcw2dJ36jjT81ecH2w=",
			nil,
		)
		require.NoError(t, err)

		getRequest.Header.Set("Content-Type", "application/json")
		getRequest.Header.Set("Authorization", token)

		resp, err := client.Do(getRequest)

		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})
}

func TestGetPlans(t *testing.T) {
	t.Run("get plans without email", func(t *testing.T) {
		resp, err := http.Get(host + "/v2/providers/50/plans")
		require.NoError(t, err)

		gotBody, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(gotBody, &jsonMap)

		assert.NotContains(t, string(gotBody), "free flex", "unexpected free plans")
		assert.NotContains(t, string(gotBody), "free unlimited", "unexpected free plans")

		if !strings.Contains(string(gotBody), "flex CA") &&
			!strings.Contains(string(gotBody), "flex US") &&
			!strings.Contains(string(gotBody), "Basic") {
			t.Errorf("expected flex plan")
		}
	})

	t.Run("get plans with email no token, shuold be regular plans", func(t *testing.T) {
		resp, err := http.Get(
			host + "/v2/providers/50/plans?email=<EMAIL>",
		)
		require.NoError(t, err)

		gotBody, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(gotBody, &jsonMap)

		assert.NotContains(t, string(gotBody), "free flex", "unexpected free plans")
		assert.NotContains(t, string(gotBody), "free unlimited", "unexpected free plans")

		if !strings.Contains(string(gotBody), "flex CA") &&
			!strings.Contains(string(gotBody), "flex US") &&
			!strings.Contains(string(gotBody), "Basic") {
			t.Errorf("expected basic plan")
		}
	})

	// TODO: Bhavik fix later when cleanup functions can be created for deleting a token
	// t.Run("get plans with email with token, shuold be 1 plan along with token", func(t *testing.T) {
	// 	// create token
	// 	requestBody := "{\"email\": \"<EMAIL>\",\"provider_id\": -1,\"plan_ids\": [18],\"request_source\": \"supportpanel\"}"
	// 	req, err := http.NewRequest(
	// 		"POST",
	// 		"https://appgateway.cactrl.qa.pocket.health/plansvc/v1/plans/token/create",
	// 		bytes.NewBufferString(requestBody),
	// 	)
	// 	if err != nil {
	// 		t.Fatalf("failed to create token request")
	// 	}
	// 	req.Header.Set("Content-Type", "application/json")
	// 	req.SetBasicAuth("core-qa", "oB7Ud0WOsXH2UgZ+0S/CvoBRk8gWtGXNeDfC7ez0e4w=")
	// 	client := &http.Client{}
	// 	_, err = client.Do(req)
	// 	if err != nil {
	// 		t.Fatalf("failed to create token")
	// 	}

	// 	resp, err := http.Get(host + "/v2/providers/50/plans?email=<EMAIL>")
	// 	if err != nil {
	// 		t.Fatalf("failed to cerate request")
	// 	}
	// 	gotBody, err := io.ReadAll(resp.Body)
	// 	if err != nil {
	// 		t.Fatalf("failed to read resp")

	// 	}
	// 	var jsonMap map[string]json.RawMessage
	// 	json.Unmarshal(gotBody, &jsonMap)

	// 	if string(jsonMap["token"]) == "" {
	// 		t.Errorf("expected token")
	// 	}

	// 	if string(jsonMap["plans"]) == "" {
	// 		t.Errorf("expected plans")
	// 	}

	// 	if !strings.Contains(string(gotBody), "free flex") && !strings.Contains(string(gotBody), "free unlimited") {
	// 		t.Errorf("expected free plans")
	// 	}

	// 	if strings.Contains(string(gotBody), "Basic") {
	// 		t.Errorf("unexpected basic plan")
	// 	}
	// })
}
