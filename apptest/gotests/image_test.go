package apptest

import (
	"net/http"
	"testing"
)

func TestImagesMetadata(t *testing.T) {
	t.Run("get image no authorization", func(t *testing.T) {
		_ = LoginAndGetToken("<EMAIL>", "test12@34")

		req, err := http.NewRequest(
			"GET",
			host+"/v1/images/ORGHD2TZAJLhsDyS_Tn9gQDKktOEGQuQsO4WchOSguI=/metadata",
			nil,
		)
		if err != nil {
			t.<PERSON>rrorf("unable to generate request with error: %q", err.Error())
		}

		client := &http.Client{}
		res, err := client.Do(req)
		if err != nil {
			t.<PERSON><PERSON><PERSON>("unable to perform request with error: %q", err.<PERSON>rror())
		}
		if res.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				res.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func TestImages(t *testing.T) {
	t.Run("get image no authorization", func(t *testing.T) {
		_ = LoginAndGetToken("<EMAIL>", "test12@34")

		req, err := http.NewRequest(
			"GET",
			host+"/v1/images/ORGHD2TZAJLhsDyS_Tn9gQDKktOEGQuQsO4WchOSguI=",
			nil,
		)
		if err != nil {
			t.Errorf("unable to generate request with error: %q", err.Error())
		}

		client := &http.Client{}
		res, err := client.Do(req)
		if err != nil {
			t.Errorf("unable to perform request with error: %q", err.Error())
		}
		if res.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				res.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})

}
