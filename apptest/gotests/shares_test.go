package apptest

import (
	"bytes"
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// this is meant to be a constant, but slices can't be constants
// exam uuids that are in the account above
var ACCOUNT_EXAM_UUIDS = []string{
	"2qLzNzIQtwHHzrtWeN7FSJ5UURT",
	"2qM0BUsRMvUQUxaN59xMAMPxvky",
	"2qRpkUrxOD0tXGMOH3NAa35cj3z",
}

// tests for creating a new share
func TestNewFaxShare(t *testing.T) {
	t.Run("fax share", func(t *testing.T) {
		postAndCheckNewShare(t, "**********", "AccessPageFax", true, false, "application/pdf")
	})
	t.Run("fax share with more than 1 exam", func(t *testing.T) {
		postAndCheckNewMultiShare(t, "**********", "AccessPageFax", true, false, "application/pdf")
	})
}

func TestNewPrintShare(t *testing.T) {
	t.Run("print share", func(t *testing.T) {
		shareId, _ := postAndCheckNewShare(t, "", "AccessPagePrint", true, false, "application/pdf")
		validatePDFShare(t, shareId)
	})

	t.Run("print share with more than 1 exam", func(t *testing.T) {
		shareId, _ := postAndCheckNewMultiShare(t, "", "AccessPagePrint", true, false, "application/pdf")
		validatePDFShare(t, shareId)
	})

	t.Run("print share - hr only", func(t *testing.T) {
		shareId, _ := postAndCheckNewShare(t, "", "AccessPagePrint", false, true, "application/pdf")
		validatePDFShare(t, shareId)
	})
}

func TestNewEmailShare(t *testing.T) {
	t.Run("email share", func(t *testing.T) {

		shareId, _ := postAndCheckNewShare(t, "<EMAIL>", "Email", true, false, "")

		//check that the new share is accessible to a share viewer
		var pin sql.NullString
		err := sqldb.QueryRow("SELECT pin FROM shares where share_id = ?", shareId).Scan(&pin)
		require.NoErrorf(t, err, "expected to find share %s in the view_shares table", shareId)
		require.Truef(t, pin.Valid, "expected valid pin credentials for the new share %s", shareId)

		validateCreds := fmt.Sprintf(`{"shareId": %q, "pin": %q}`, shareId, pin.String)
		shareReq, err := http.NewRequest(
			"POST",
			host+"/v1/shares/validate",
			strings.NewReader(validateCreds),
		)
		require.NoError(t, err)
		client := &http.Client{}
		res, err := client.Do(shareReq)
		require.NoError(t, err)
		require.Equalf(t, http.StatusOK, res.StatusCode, "unexpected validate result share %s", shareId)

	})

	t.Run("email share with more than 1 exam", func(t *testing.T) {

		shareId, _ := postAndCheckNewMultiShare(t, "<EMAIL>", "Email", true, false, "")

		//check that the new share is accessible to a share viewer
		var pin sql.NullString
		err := sqldb.QueryRow("SELECT pin FROM shares where share_id = ?", shareId).Scan(&pin)
		require.NoErrorf(t, err, "expected to find share %s in the view_shares table", shareId)
		require.Truef(t, pin.Valid, "expected valid pin credentials for the new share %s", shareId)

		validateCreds := fmt.Sprintf(`{"shareId": %q, "pin": %q}`, shareId, pin.String)
		shareReq, err := http.NewRequest(
			"POST",
			host+"/v1/shares/validate",
			strings.NewReader(validateCreds),
		)
		require.NoError(t, err)
		client := &http.Client{}
		res, err := client.Do(shareReq)
		require.NoError(t, err)
		require.Equalf(t, http.StatusOK, res.StatusCode, "unexpected validate result share %s", shareId)

	})
}

func TestNewDLShare(t *testing.T) {
	t.Run("download share", func(t *testing.T) {
		shareId, body := postAndCheckNewShare(t, "", "ZIP", true, false, "text/plain; charset=utf-8")

		//download shares won't show up in get shares, but can check the format of the response of the new share
		bodyShareId := string(body)

		if !strings.HasPrefix(bodyShareId, "offline") {
			t.Fatalf(
				"expected new download share to respond with a body with offline share Id, instead got %s",
				shareId,
			)
		}
	})

	t.Run("download share with more than 1 exam", func(t *testing.T) {
		shareId, body := postAndCheckNewMultiShare(t, "", "ZIP", true, false, "text/plain; charset=utf-8")

		//download shares won't show up in get shares, but can check the format of the response of the new share
		bodyShareId := string(body)

		if !strings.HasPrefix(bodyShareId, "offline") {
			t.Fatalf(
				"expected new download share to respond with a body with offline share Id, instead got %s",
				shareId,
			)
		}
	})
}

func teardownPostShare(shareId string) {
	//teardown: delete share
	sqldb.Exec("DELETE FROM shares where share_id = ?", shareId)
	sqldb.Exec("DELETE FROM view_shares where share_id = ?", shareId)
	sqldb.Exec("DELETE FROM share_objects2 where share_id = ?", shareId)
	sqldb.Exec("DELETE FROM share_analytics where share_id = ?", shareId)
	sqldb.Exec("DELETE FROM share_healthrecords where share_id = ?", shareId)
}

func newExam(UUID string) string {
	return `{
			  "uuid": "` + UUID + `",
			  "examId": "1.2.840.10008.114051.752239.**********.450643366.**********",
			  "patientName": {
				"dicomName": "SMITH^PAUL^MICHAEL",
				"firstAndMiddleName": "Paul Michael",
				"lastName": "Smith"
			  },
			  "examType": "Ultrasound",
			  "examDate": "Nov 07, 2020",
			  "activated": true,
			  "description": "TRANSESOPHAGEL ECHO",
			  "dob": "1964/02/12",
			  "sex": "M",
			  "transferId": "2qLzNzV3GTJL24YeVqUjffaOfxO",
			  "bodypart": "Unknown",
			  "reportDelay": 0,
			  "referringPhysician": "Dr.  Sarah Brown",
			  "size": 0,
			  "orgId": 0,
			  "facilityFunded": false,
			  "modality": "US",
			  "allow_pt_png_dls": false,
			  "patientId": "2qLzSZaU8Yze7DOJrLTRY08VnDw",
			  "unlockStatus": "fullAccess"
			}`
}

func newShareJson(method string, recipient string, imaging bool, hr bool, UUIDs []string) []byte {
	jsonStr := `{`
	var examStrings []string
	for _, UUID := range UUIDs {
		examStrings = append(examStrings, newExam(UUID))
	}
	if imaging {
		jsonStr += `
		"examList": [` + strings.Join(examStrings, ",") + `],`
	}
	if hr {
		jsonStr += `"healthRecords":[{"source":{"type":"UPLOAD","description":"old man yells"},
					"recordDate":"1988-05-01T00:00:00+00:00","name":"docker","description":"","typeCode":7,
					"createdDate":"2024-12-17T17:40:08+00:00",
					"filenames":["old-man-yells-at-docker.png"],
					"urls":["https://phdevcentralcabloblrs.blob.core.windows.net/healthrecords/637e0329-0c8c-4d58-a6cc-aa653a6f1c31"],
					"tag":"","fhir_resource_type":"DocumentReference",
					"data":null,"patientId":"2qLzSZaU8Yze7DOJrLTRY08VnDw","id":"802abd9c-67ce-40d8-8d3b-03c1d5c079ce"}],`
	}
	jsonStr = jsonStr + `
		"method": "` + method + `",
		"recipient":"` + recipient + `",
		"mode": "multiple"
	  }`
	return ([]byte)(jsonStr)
}

func postAndCheckNewShare(
	t *testing.T,
	shareRecipient string,
	shareMethod string,
	includeImaging bool,
	includeHR bool,
	expectedContentType string,
) (string, []byte) {

	jsonBytes := newShareJson(
		shareMethod, shareRecipient, includeImaging, includeHR, ACCOUNT_EXAM_UUIDS[:1],
	)
	token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
	res, shareId, err := postNewShare(t, token, jsonBytes)
	require.NoError(t, err)
	t.Cleanup(func() {
		teardownPostShare(shareId)
	})

	body, err := io.ReadAll(res.Body)
	require.NoError(t, err)

	require.Equal(t, expectedContentType, res.Header.Get("Content-type"), "wrong content type")

	isDevFax := shareMethod == "AccessPageFax" && env == "dev"    //there's no actual faxing mechanism in dev, this will always fail
	isOffline := (shareMethod == "ZIP") || (shareMethod == "ISO") //offline shares don't show up in GET /shares

	if !isDevFax && !isOffline {
		foundShareJson, err := getSharesCheck(t, shareId, token, shareRecipient, shareMethod)
		require.NoError(t, err)
		require.NotNilf(t, foundShareJson, "could not find new share %s", shareId)
	}

	return shareId, body
}

func postAndCheckNewMultiShare(
	t *testing.T,
	shareRecipient string,
	shareMethod string,
	includeImaging bool,
	includeHR bool,
	expectedContentType string,
) (string, []byte) {

	jsonBytes := newShareJson(shareMethod, shareRecipient, includeImaging, includeHR,
		ACCOUNT_EXAM_UUIDS,
	)
	token := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
	res, shareId, err := postNewShare(t, token, jsonBytes)
	require.NoError(t, err)
	t.Cleanup(func() {
		teardownPostShare(shareId)
	})

	body, err := io.ReadAll(res.Body)
	require.NoError(t, err)

	require.Equal(t, expectedContentType, res.Header.Get("Content-type"), "wrong content type")

	isDevFax := shareMethod == "AccessPageFax" && env == "dev"    //there's no actual faxing mechanism in dev, this will always fail
	isOffline := (shareMethod == "ZIP") || (shareMethod == "ISO") //offline shares don't show up in GET /shares

	if !isDevFax && !isOffline {
		foundShareJson, err := getSharesCheck(t, shareId, token, shareRecipient, shareMethod)
		require.NoError(t, err)
		require.NotNilf(t, foundShareJson, "could not find new share %s", shareId)
	}

	return shareId, body
}

func validatePDFShare(t *testing.T, shareId string) {
	// check that the new share is accessible to a share viewer
	var viewcode, dob sql.NullString
	err := sqldb.QueryRow("SELECT view_code, dob FROM view_shares where share_id = ?", shareId).
		Scan(&viewcode, &dob)
	require.NoErrorf(t, err, "expected to find the share %s in the view_shares table", shareId)
	require.Truef(t, viewcode.Valid, "expected valid viewcode for share %s", shareId)
	require.Truef(t, dob.Valid, "expected valid dob cred for share %s", shareId)

	validateCreds := fmt.Sprintf(`{"viewcode": %q, "dob": %q}`, viewcode.String, dob.String)
	shareReq, err := http.NewRequest(
		"POST",
		host+"/v1/shares/validate",
		strings.NewReader(validateCreds),
	)
	require.NoError(t, err)
	client := &http.Client{}
	res, err := client.Do(shareReq)
	require.NoError(t, err)
	require.Equalf(t, http.StatusOK, res.StatusCode, "unexpected share validate result for share %s", shareId)

	// check if hash of viewcode+dob to region mapping correctly stored on regionrouter
	viewCodeIdType := int16(1)
	expectedViewCodeRegion := uint16(1)
	viewcodeDOB := viewcode.String + dob.String

	hash := sha256.New()
	_, err = io.WriteString(hash, viewcodeDOB)
	if err != nil {
		t.Fatal("error hashing viewcode+DOB")
	}
	hashString := base64.URLEncoding.EncodeToString(hash.Sum(nil))

	gotRegion, err := getViewCodeRegion(viewCodeIdType, hashString)
	if err != nil {
		t.Fatal(
			"error querying regionrouter for viewcode+dob hash to region mapping, got:",
			err,
		)
	}
	if gotRegion != expectedViewCodeRegion {
		t.Fatalf(
			"incorrect viewcode to region mapping, expected region %d got %d",
			expectedViewCodeRegion,
			gotRegion,
		)
	}
}

func postNewShare(
	t *testing.T,
	token string,
	jsonBytes []byte,
) (*http.Response, string, error) {
	req, err := http.NewRequest("POST", host+"/v1/shares", bytes.NewBuffer(jsonBytes))
	require.NoError(t, err)

	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, res.StatusCode)

	//expect share ID in header
	apiHeaderStr := res.Header.Get("PocketHealth-API-Result")
	type shareIDResult struct {
		ID string `json:"id"`
	}

	idRes := shareIDResult{}
	err = json.Unmarshal([]byte(apiHeaderStr), &idRes)
	require.NoError(t, err)

	return res, idRes.ID, nil
}

func getSharesCheck(
	t *testing.T,
	id string,
	token string,
	shareRecipient string,
	shareMethod string,
) (foundShareJson map[string]json.RawMessage, err error) {

	//poll a couple times, because POST /shares does some async background work
	//after the API call returns

	numChecks := 5
	waitBetween := 1 * time.Second

out:
	for i := 0; i < numChecks; i++ {
		//call GET /shares to verify the new share is present
		req, err := http.NewRequest("GET", host+"/v1/shares", nil)
		require.NoError(t, err)
		req.Header.Set("Authorization", token)

		// set limit to 10 so as to get a limited number of shares
		// if many many pipeslines run at once this could be a problem
		queryParam := url.Values{}
		queryParam.Add("limit", "10")
		req.URL.RawQuery = queryParam.Encode()

		client := &http.Client{}
		res, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, res.StatusCode)

		body, err := io.ReadAll(res.Body)
		require.NoError(t, err)
		var jsonList []json.RawMessage
		err = json.Unmarshal(body, &jsonList)
		require.NoError(t, err, "expected response from GET /shares to be in format of a list of json objects")

		expectedRecipient := shareRecipient
		if shareMethod == "AccessPageFax" || shareMethod == "AccessPagePrint" {
			expectedRecipient = "pdf"
		}
		for _, sh := range jsonList {
			var shareJson map[string]json.RawMessage
			err = json.Unmarshal(sh, &shareJson)
			require.NoError(t, err)
			var resShareId, recipient, method string
			err := json.Unmarshal(shareJson["shareId"], &resShareId)
			require.NoError(t, err)
			err2 := json.Unmarshal(shareJson["method"], &method)
			require.NoError(t, err2)
			err3 := json.Unmarshal(shareJson["recipient"], &recipient)
			require.NoError(t, err3)

			if resShareId == id && method == shareMethod && recipient == expectedRecipient {
				foundShareJson = shareJson
				break out
			}
		}

		time.Sleep(waitBetween)
	}

	if shareMethod == "AccessPageFax" {
		var status string
		err = json.Unmarshal(foundShareJson["status"], &status)
		require.NoError(t, err, "Expected fax share json to contain 'status' member")
		require.Equal(t, "delivered", status, "new share was not faxed")
	}

	return foundShareJson, nil
}

// get the region mapped to the hash of viewcode+DOB in region router
func getViewCodeRegion(idType int16, viewcodeDOBHash string) (uint16, error) {
	ctx := context.Background()
	rrHost := "https://appgateway.cactrl.qa.pocket.health/regionrouter"
	rrAPIKey := "RPUvzrjqHvDrzy1fhfLQ7cNcEzjSGnGNjU3ux7EepwDlF9e2/j9p6/9oQ916KLC4HGhFe42qC+EjrclDrXDLvw=="
	target := fmt.Sprintf("%s/v2/sid/%d/%s", rrHost, idType, viewcodeDOBHash)

	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return 0, fmt.Errorf("unable to generate request")
	}
	req.Header.Set("Authorization", "Bearer "+rrAPIKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return 0, fmt.Errorf("problem connecting to server")
	}

	if res.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("got status code %d when expected : %d", res.StatusCode, http.StatusOK)
	}

	gotRegion, err := io.ReadAll(res.Body)
	if err != nil {
		return 0, fmt.Errorf("problem reading response body")
	}

	var gotRegionInt uint16
	err = json.Unmarshal(gotRegion, &gotRegionInt)
	if err != nil {
		return 0, fmt.Errorf("can't unmarshal response: %s", string(gotRegion))
	}

	return gotRegionInt, nil
}

// Test Share management
func TestGetShare(t *testing.T) {
	token_connect := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)

	t.Run("get shares-all", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares", nil)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap []map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		if len(jsonMap) != 0 {
			if string(jsonMap[0]["shareId"]) == "" {
				t.Errorf("got empty share id for the first share")
			}
		}
	})

	t.Run("get shares-pagination", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares?offset=10&limit=20", nil)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap []map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		if len(jsonMap) > 20 {
			t.Errorf("expected to get 20 shares but got %d", len(jsonMap))
		}
	})

	t.Run("get shares-not authorized", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest("GET", host+"/v1/shares?offset=10&limit=20", nil)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("get share exams", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			fmt.Sprintf("%s/v2/shares/%s/exams", host, EamilShareWithExams),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		if string(jsonMap["exams"]) == "" {
			t.Errorf("expected exams to not be empty, but got empty")
		}
	})

	t.Run("get share exams-NOT match", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"GET",
			fmt.Sprintf("%s/v2/shares/%s/exams", host, ShareNotBelongToAccount),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusInternalServerError {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusInternalServerError,
			)
		}
	})
}

func TestReshare(t *testing.T) {
	token_connect := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
	t.Run("Reshare-email", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			fmt.Sprintf("%s/v1/shares/%s/reshare", host, ExtendEmailShare),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
	})

	t.Run("Reshare-print", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			fmt.Sprintf("%s/v1/shares/%s/reshare", host, ResharePrintShare),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		// check the response body (the pdf)
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		if len(body) == 0 {
			t.Errorf("got empty pdf string by resharing the share pdf share")
		}
	})

	t.Run("Reshare-NOT match", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			fmt.Sprintf("%s/v1/shares/%s/reshare", host, ShareNotBelongToAccount),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func TestRevokeShare(t *testing.T) {
	token_connect := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
	t.Run("revoke a share", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/revoke", host, RevokeShare),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		// Try to validate the revoked share (should be failed)
		var pin sql.NullString
		err = sqldb.QueryRow("SELECT pin FROM shares where share_id = ?", RevokeShare).Scan(&pin)
		if err != nil {
			t.Errorf("expected to find the share in the view_shares table")
		}

		if !pin.Valid {
			t.Fatalf("expected valid pin credentials for the new share")
		}
		validateCreds := fmt.Sprintf(`{"shareId": %q, "pin": %q}`, RevokeShare, pin.String)
		shareReq, err := http.NewRequest(
			"POST",
			host+"/v1/shares/validate",
			strings.NewReader(validateCreds),
		)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		res, err := client.Do(shareReq)
		if err != nil {
			t.Fatalf("could not do req: %v", err)
		}

		if res.StatusCode != http.StatusForbidden {
			t.Fatalf(
				"share validate: got status code %d when expected : %d",
				res.StatusCode,
				http.StatusForbidden,
			)
		}

		// Recall the test revoke
		sqldb.Exec("UPDATE shares SET active=true where share_id = ?", RevokeShare)
	})

	t.Run("revoke a share-NOT match", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/revoke", host, ShareNotBelongToAccount),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func TestExtendShare(t *testing.T) {
	token_connect := LoginAndGetToken(SharesAccountEmail, SharesAccountPassword)
	t.Run("extend a share-email share", func(t *testing.T) {
		var pin string
		err := sqldb.QueryRow(
			"SELECT pin FROM shares where share_id = ?", ExtendEmailShare,
		).
			Scan(&pin)
		if err != nil {
			t.Errorf("expected to find the share in the shares table")
		}
		token := GetShareToken(ExtendEmailShare, pin, "", "")

		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/extend", host, ExtendEmailShare),
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusInternalServerError {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusInternalServerError,
			)
		}
	})

	t.Run("extend a share-NOT match", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/extend", host, ShareNotBelongToAccount),
			nil,
		)
		req.Header.Set("Authorization", token_connect)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})

	t.Run("extend a share-pdf share-Expired", func(t *testing.T) {
		var code, dob string
		err := sqldb.QueryRow(
			"SELECT view_code, dob FROM view_shares where share_id = ?",
			ExpiredPdfShare,
		).
			Scan(&code, &dob)
		if err != nil {
			t.Errorf("expected to find the share in the view_shares table")
		}
		token := GetShareToken("", "", code, dob)
		if token != "Bearer " {
			t.Errorf("expected empty token since expired, but got %s", token)
		}
	})

	t.Run("extend a share-pdf share", func(t *testing.T) {
		var code, dob, original_expire string
		var original_extended sql.NullString
		err := sqldb.QueryRow(
			"SELECT view_code, dob, extended_expiry, expiry_timestamp FROM view_shares where share_id = ?",
			ExtendPdfShare,
		).
			Scan(&code, &dob, &original_extended, &original_expire)
		if err != nil {
			t.Errorf("expected to find the share in the view_shares table")
		}

		// Update expiry time to make it not expired and not revoked
		sqldb.Exec(
			"UPDATE view_shares SET extended_expiry=NULL, expiry_timestamp=NOW()+INTERVAL 2 DAY where share_id = ?",
			ExtendPdfShare,
		)
		sqldb.Exec(
			"UPDATE shares SET active=true where share_id = ?",
			ExtendPdfShare,
		)
		token := GetShareToken("", "", code, dob)
		if token == "" || token == "Bearer " {
			t.Errorf("expected to get valid share token but got %s", token)
			return
		}

		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/extend", host, ExtendPdfShare),
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		// Check share validation for the extended share

		validateCreds := fmt.Sprintf(`{"viewcode": %q, "dob": %q}`, code, dob)
		shareReq, err := http.NewRequest(
			"POST",
			host+"/v1/shares/validate",
			strings.NewReader(validateCreds),
		)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		res, err := client.Do(shareReq)
		if err != nil {
			t.Fatalf("could not do req: %v", err)
		}
		if res.StatusCode != http.StatusOK {
			t.Fatalf(
				"share validate: got status code %d when expected : %d",
				res.StatusCode,
				http.StatusOK,
			)
		}

		body, err := io.ReadAll(res.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		if string(jsonMap["requiresNameAuth"]) != "true" {
			t.Fatalf("extended share got requiresNameAuth false, expected true")
		}

		// Set the sql data to original data
		if original_extended.Valid {
			sqldb.Exec(
				"UPDATE view_shares SET extended_expiry=?, expiry_timestamp=? where share_id = ?",
				original_extended.String,
				original_expire,
				ExtendPdfShare,
			)
		} else {
			sqldb.Exec(
				"UPDATE view_shares SET extended_expiry=?, expiry_timestamp=NULL where share_id = ?",
				original_extended.String, original_expire, ExtendPdfShare,
			)
		}
	})

	t.Run("extend a share-pdf share-Extended", func(t *testing.T) {
		var code, dob, original_expire string
		var original_extended sql.NullString
		err := sqldb.QueryRow(
			"SELECT view_code, dob, extended_expiry, expiry_timestamp FROM view_shares where share_id = ?",
			ExtendedPdfShare,
		).
			Scan(&code, &dob, &original_extended, &original_expire)
		if err != nil {
			t.Errorf("expected to find the share in the view_shares table")
		}

		// Update expiry time to make it not expired
		sqldb.Exec(
			"UPDATE view_shares SET extended_expiry= NOW()+INTERVAL 7 DAY, expiry_timestamp=NOW()+INTERVAL 2 DAY where share_id = ?",
			ExtendedPdfShare,
		)
		token := GetShareToken("", "", code, dob)
		if token == "" || token == "Bearer " {
			t.Errorf("got error getting share token, expected none")
			return
		}

		client := &http.Client{}
		req, err := http.NewRequest(
			"PUT",
			fmt.Sprintf("%s/v1/shares/%s/extend", host, ExtendedPdfShare),
			nil,
		)
		req.Header.Set("Authorization", token)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusForbidden {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusForbidden)
		}

		// Set the sql data to original data
		if original_extended.Valid {
			sqldb.Exec(
				"UPDATE view_shares SET extended_expiry=?, expiry_timestamp=? where share_id = ?",
				original_extended.String,
				original_expire,
				ExtendedPdfShare,
			)
		} else {
			sqldb.Exec(
				"UPDATE view_shares SET extended_expiry=?, expiry_timestamp=NULL where share_id = ?",
				original_extended.String, original_expire, ExtendedPdfShare,
			)
		}
	})
}
