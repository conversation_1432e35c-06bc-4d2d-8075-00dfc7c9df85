package apptest

import (
	"bytes"
	"net/http"
	"testing"
)

func TestResetPassword(t *testing.T) {

	t.Run("reset account password initial and reset-Not exist", func(t *testing.T) {
		// reset initial
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/resetpassword/init",
			bytes.NewBufferString(`"<EMAIL>"`),
		)
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("got status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

	})

}
