{"sqlConnString": "core-qa-apptest:hZ1NSfeCkgG1!@tcp(phqa0.mysql.database.azure.com:3306)/pockethealth?parseTime=true", "azure_storage_account": "phdevcentralcabloblrs", "azure_storage_key": "****************************************************************************************", "acct_svc_url": "https://account.global.qa.pocket.health", "rr_url": "https://rr.global.qa.pocket.health", "plans_url": "https://plansvc.global.qa.pocket.health", "fax_url": "fax.qa.pocket.health", "orgsvc_url": "https://appgateway.cactrl.qa.pocket.health/orgsvc", "orgsvc_uname": "core-dev", "orgsvc_api_key": "NQrfbT7uYvv5lNkwv3D87w1yfv7ccYNfwZySsmdkeB6gIhkOkOuGty0gMr2T739y", "azure_reportinsight_storage_container": "reportinsightsdata", "azure_pprof_autoprofiler_storage_container": "coreapi-profiles", "exam_insights_srvc_url": "https://examinsights.cactrl.qa.pocket.health", "exam_insights_srvc_api_key": "2Muk2LMwr2Vfz2Ua7HDwr8LPyr9Yxt6VMru7HKbi5QMvu6Tzq6ZWqm5Cvd3S", "exam_insights_srvc_api_key_sec_name": "core-dev", "gateway_auth_url": "https://gatewayauth.global.qa.pocket.health", "report_insights_url": "https://ri.cactrl.qa.pocket.health"}