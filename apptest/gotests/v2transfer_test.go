package apptest

import (
	"bytes"
	"encoding/json"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestV2Transfers(t *testing.T) {
	t.Run("when multiple images and reports, should be successful", func(t *testing.T) {
		//first login
		client := &http.Client{}
		loginReq, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test1234"}`),
		)
		loginResp, err := client.Do(loginReq)
		require.NoError(t, err, "got login error when expected none")

		require.Equal(t, http.StatusOK, loginResp.StatusCode)

		defer loginResp.Body.Close()

		body, err := ioutil.ReadAll(loginResp.Body)
		require.NoError(t, err, "could not read resp body")

		var loginJsonMap map[string]json.RawMessage
		json.Unmarshal(body, &loginJsonMap)
		token := string(loginJsonMap["token"])
		if token == "" {
			t.Fatalf("expected token in response body")
		}

		token = "Bearer " + strings.Trim(token, "\"")

		// init transfer
		initJson := []byte(`[
			{
				"partname": "ultrasound.dcm",
				"study_id": "10023",
				"series_id": "10002",
				"instance_id": "10002",
				"sha1": "vpvgRZluFcUCj4bKfTNRI9bywqw="
			},
			{
				"partname": "ultrasound-2.dcm",
				"study_id": "10023",
				"series_id": "10003",
				"instance_id": "10003",
				"sha1": "xiXRmZVp38DQuF30cWHbEEs4lLY="
			}
		]`)

		req, err := http.NewRequest("POST", host+"/v2/transfers", bytes.NewBuffer(initJson))
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		req.Header.Set("Authorization", token)

		resp, err := client.Do(req)
		require.NoError(t, err, "got error when expected none")
		require.Equal(t, http.StatusOK, resp.StatusCode)

		// read response
		resBody, err := ioutil.ReadAll(resp.Body)
		require.NoError(t, err, "could not read resp body")

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(resBody, &jsonMap)
		transferId := strings.Trim(string(jsonMap["transferId"]), "\"")
		uploadSessionId := strings.Trim(string(jsonMap["uploadSessionId"]), "\"")
		if transferId == "" {
			t.Fatalf("expected non-empty transferId")
		}
		if uploadSessionId == "" {
			t.Fatalf("expected non-empty uploadSessionId")
		}

		// upload report dcm
		reportFile, err := os.Open("./assets/dcm_report.dcm")
		if err != nil {
			t.Fatalf("error when opening file: %q", err.Error())
		}
		defer reportFile.Close()
		reportMeta := map[string]string{
			"instanceNumber": "10001",
			"studyDate":      "2020-05-02 00:00:00",
			"protocolName":   "Musculoskeletal",
			"modality":       "SR",
			"patientName":    "BASRA^MANVEER",
		}
		reportMetaStr, _ := json.Marshal(reportMeta)
		values := map[string]io.Reader{
			"metadata": strings.NewReader(string(reportMetaStr)),
			"file":     reportFile,
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			if x, ok := r.(*os.File); ok {
				fw, _ = w.CreateFormFile(key, x.Name())
			} else {
				fw, _ = w.CreateFormField(key)
			}
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err = http.NewRequest("POST", host+"/v2/transfers/"+transferId+"/reportdcm", &b)
		require.NoError(t, err, "got error when expected none")

		req.Header.Set("Authorization", token)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.Header.Set("Upload-Session-Id", uploadSessionId)

		resp, err = client.Do(req)
		require.NoError(t, err)

		require.Equal(t, http.StatusOK, resp.StatusCode)

		// upload image files
		imageFile1, err := os.Open("./assets/ultrasound.dcm")
		require.NoError(t, err, "error when opening file")

		defer imageFile1.Close()
		imageFile2, err := os.Open("./assets/ultrasound-2.dcm")
		require.NoError(t, err, "error when opening file")
		defer imageFile2.Close()

		// Prepare a form
		w = multipart.NewWriter(&b)
		imgField1, _ := w.CreateFormFile("file", "ultrasound.dcm")
		io.Copy(imgField1, imageFile1)
		imgField2, _ := w.CreateFormFile("file", "ultrasound-2.dcm")
		io.Copy(imgField2, imageFile2)
		// Don't forget to close the multipart writer.
		w.Close()

		req, err = http.NewRequest("POST", host+"/v2/transfers/"+transferId+"/images", &b)
		require.NoError(t, err)

		req.Header.Set("Authorization", token)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.Header.Set("Upload-Session-Id", uploadSessionId)

		resp, err = client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		t.Cleanup(func() {
			sqldb.Exec(
				"DELETE FROM view_metadata WHERE object_id IN (SELECT object_id FROM objects WHERE scan_id=?)",
				transferId,
			)
			sqldb.Exec(
				"DELETE FROM object_mappings WHERE series_uid IN (SELECT series_uid FROM series WHERE exam_uuid IN (SELECT `uuid` FROM exams WHERE transfer_id=?))",
				transferId,
			)
			sqldb.Exec(
				"DELETE FROM series WHERE exam_uuid IN (SELECT `uuid` FROM exams WHERE transfer_id=?)",
				transferId,
			)
			sqldb.Exec("DELETE FROM objects WHERE scan_id=?", transferId)
			sqldb.Exec("DELETE FROM exams WHERE transfer_id=?", transferId)
			sqldb.Exec("DELETE FROM scans WHERE scan_id=?", transferId)
		})

		// this test uses QA Prov Svc, so there's no way to validate the upload by getting a user's exams
		// unless we're also talking to the QA dbs
		if env == "qa" {

			uploadFound := false
			attempt := 0

			for !uploadFound && attempt < 10 {
				time.Sleep(10 * time.Second)

				attempt += 1
				t.Logf("waiting for transfer to finalize, attempt %d...", attempt)

				// Get exams to make sure our uploaded record is there
				req, _ = http.NewRequest("GET", host+"/v1/users/exams", nil)
				req.Header.Set("Authorization", token)
				resp, _ = client.Do(req)
				assert.Equal(t, http.StatusOK, resp.StatusCode)

				resBody, err = ioutil.ReadAll(resp.Body)
				if err != nil {
					t.Errorf("could not read resp body: %v", err)
					continue
				}

				var jsonMapSlice []map[string]json.RawMessage
				json.Unmarshal(resBody, &jsonMapSlice)

				// check exams for transfer id of self-upload
				for _, m := range jsonMapSlice {
					foundId := strings.Trim(string(m["transferId"]), "\"")
					if foundId == transferId {
						uploadFound = true
					}
				}
			}

			if !uploadFound {
				t.Error("expected to find uploaded exams in user exams, found none")
			}
		}
	})
	t.Run("not unlimited/flex user", func(t *testing.T) {
		//first login
		client := &http.Client{}
		loginReq, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test1234!"}`),
		)
		loginResp, err := client.Do(loginReq)
		if err != nil {
			t.Errorf("got login error when expected none: %q", err.Error())
		}
		if loginResp.StatusCode != http.StatusOK {
			t.Errorf(
				"got login status code %d when expected : %d",
				loginResp.StatusCode,
				http.StatusOK,
			)
		}
		defer loginResp.Body.Close()
		body, err := ioutil.ReadAll(loginResp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var loginJsonMap map[string]json.RawMessage
		json.Unmarshal(body, &loginJsonMap)
		token := string(loginJsonMap["token"])
		if token == "" {
			t.Errorf("expected token in response body")
		}

		token = "Bearer " + strings.Trim(token, "\"")

		// init transfer
		initJson := []byte(`[
			{
				"partname": "ultrasound.dcm",
				"study_id": "10023",
				"series_id": "10002",
				"instance_id": "10002",
				"sha1": "vpvgRZluFcUCj4bKfTNRI9bywqw="
			},
			{
				"partname": "ultrasound-2.dcm",
				"study_id": "10023",
				"series_id": "10003",
				"instance_id": "10003",
				"sha1": "xiXRmZVp38DQuF30cWHbEEs4lLY="
			}
		]`)

		req, err := http.NewRequest("POST", host+"/v2/transfers", bytes.NewBuffer(initJson))
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		req.Header.Set("Authorization", token)

		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusForbidden {
			t.Errorf("got status code %d when expected : %d", resp.StatusCode, http.StatusForbidden)
		}
	})
}
