package apptest

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
)

func TestGetReport(t *testing.T) {

	if env == "qa" {
		cases := []struct {
			name     string
			reportId string
			accept   string
		}{
			//TODO: expand coverage for all report types and purchased/unpurchased
			//and check binary returned
			{
				name:     "encap PDF blank(png)",
				reportId: "4PqjZFpNvtQx5t-oNI6B34Ev5m8NEHMp3wl5MD5uQRo=",
				accept:   "",
			},
			{
				name:     "encap PDF explicit png",
				reportId: "4PqjZFpNvtQx5t-oNI6B34Ev5m8NEHMp3wl5MD5uQRo=",
				accept:   "image/png",
			},
			{
				name:     "encap PDF pdf",
				reportId: "4PqjZFpNvtQx5t-oNI6B34Ev5m8NEHMp3wl5MD5uQRo=",
				accept:   "application/pdf",
			},
			{
				name:     "encap PDF html",
				reportId: "4PqjZFpNvtQx5t-oNI6B34Ev5m8NEHMp3wl5MD5uQRo=",
				accept:   "text/html",
			},
		}

		token := LoginAndGetToken("<EMAIL>", "test1234")
		for _, c := range cases {
			t.Run(c.name, func(t *testing.T) {

				req, err := http.NewRequest(
					"GET",
					fmt.Sprintf("%s/v1/reports/%s", host, c.reportId),
					nil,
				)
				if err != nil {
					t.Fatalf("unable to generate request with error: %v", err)
				}
				req.Header.Set("Authorization", token)
				req.Header.Set("Accept", c.accept)
				client := &http.Client{}
				res, err := client.Do(req)
				if err != nil {
					t.Fatalf("unable to perform request with error: %v", err)
				}
				if res.StatusCode != http.StatusOK {
					t.Fatalf("got status code %d when expected : %d", res.StatusCode, http.StatusOK)
				}
				defer res.Body.Close()
				//check response
				_, err = ioutil.ReadAll(res.Body)
				if err != nil {
					t.Fatalf("could not read response: %v", err)
				}
			})
		}
	}
}

func TestGetTaggedReport(t *testing.T) {
	if env == "qa" {

		cases := []struct {
			name            string
			reportId        string
			numDefsExpected int
		}{
			//TODO: expand for other report types
			{
				name:     "encap PDF",
				reportId: "4PqjZFpNvtQx5t-oNI6B34Ev5m8NEHMp3wl5MD5uQRo=",
			},
		}

		token := LoginAndGetToken("<EMAIL>", "test1234")
		for _, c := range cases {
			t.Run(c.name, func(t *testing.T) {
	
				req, err := http.NewRequest(
					"GET",
					fmt.Sprintf("%s/v1/reports/%s/taggedhtml", host, c.reportId),
					nil,
				)
				if err != nil {
					t.Fatalf("unable to generate request with error: %v", err)
				}
				req.Header.Set("Authorization", token)
	
				client := &http.Client{}
				res, err := client.Do(req)
				if err != nil {
					t.Fatalf("unable to perform request with error: %v", err)
				}
				if res.StatusCode != http.StatusOK {
					t.Fatalf("got status code %d when expected : %d", res.StatusCode, http.StatusOK)
				}
				defer res.Body.Close()
				//check response
				body, err := ioutil.ReadAll(res.Body)
				if err != nil {
					t.Fatalf("could not read response: %v", err)
				}
				var jsonMap map[string]json.RawMessage
				err = json.Unmarshal(body, &jsonMap)
				if err != nil {
					t.Fatalf("could not unmarshal response: %v", err)
				}
				var ok bool
				if _, ok = jsonMap["defs"]; !ok {
					t.Fatalf("expected definition dict to be present in response, but wasn't")
				}
				var s map[string]string
				err = json.Unmarshal(jsonMap["defs"], &s)
				if err != nil {
					t.Fatalf("expected defs to be a string:string map, got error unmarshaling: %v", err)
				}
	
				if _, ok = jsonMap["report"]; !ok {
					t.Fatalf("expected report to be present in response, but wasn't")
				}
				var report string
				err = json.Unmarshal(jsonMap["report"], &report)
				if err != nil {
					t.Fatalf("expected report to be a string, got error unmarshaling: %v", err)
				}
			})
		}
	}
}
