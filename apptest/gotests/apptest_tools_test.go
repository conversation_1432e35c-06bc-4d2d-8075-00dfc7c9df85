package apptest

import (
	"crypto/tls"
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

var sqldb *sql.DB
var host string
var env string

type ApptestEnvConfig struct {
	SqlConnString string `json:"sqlConnString"`
}

func init() {
	flag.StringVar(&host, "host", "", "api host")
	flag.StringVar(&env, "env", "", "test env")
}

func TestMain(m *testing.M) {
	flag.Parse()
	doSetup()
	os.Exit(m.Run())
}

func doSetup() {
	log.Println(host)
	log.Println(env)
	if strings.Contains(host, "localhost") {
		//ignore certificate errors for local-running containers, which use self signed cert
		http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{
			InsecureSkipVerify: true,
		}
	}

	filename := "config." + env + ".json"
	configJSON, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Fatal("Unable to read config: ", err)
	}

	var envConfig ApptestEnvConfig
	err = json.Unmarshal(configJSON, &envConfig)
	if err != nil {
		log.Fatal("Unable to parse config")
	}

	sqldb, err = sql.Open("mysql", envConfig.SqlConnString)
	if err != nil {
		log.Fatal("unable to perform sql.Open")
	}
	sqldb.SetMaxIdleConns(0)
	sqldb.SetConnMaxLifetime(time.Minute)
	err = sqldb.Ping()
	if err != nil {
		log.Fatalf("unable to ping db, %q", err)
	}

}

func LoginAndGetToken(email string, password string) string {
	//login (assume it works, login tests cover it with error checking)
	client := &http.Client{}
	req, err := http.NewRequest(
		"POST",
		host+"/v2/users/login",
		strings.NewReader(fmt.Sprintf(`{"email": %q, "password": %q}`, email, password)),
	)

	resp, err := client.Do(req)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)
	var jsonMap map[string]json.RawMessage
	json.Unmarshal(body, &jsonMap)
	return "Bearer " + strings.Trim(string(jsonMap["token"]), "\"")
}

func GetShareToken(shareId string, pin string, view_code string, dob string) string {
	if shareId != "" && pin != "" {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v1/shares/validate",
			strings.NewReader(fmt.Sprintf(`{"shareId": %q, "pin": %q}`, shareId, pin)),
		)
		resp, err := client.Do(req)
		if err != nil {
			return ""
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			return ""
		}
		body, _ := ioutil.ReadAll(resp.Body)
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		return "Bearer " + strings.Trim(string(jsonMap["token"]), "\"")
	}
	client := &http.Client{}
	req, err := http.NewRequest(
		"POST",
		host+"/v1/shares/validate",
		strings.NewReader(fmt.Sprintf(`{"viewcode": %q, "dob": %q}`, view_code, dob)),
	)
	resp, err := client.Do(req)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)
	var jsonMap map[string]json.RawMessage
	json.Unmarshal(body, &jsonMap)
	return "Bearer " + strings.Trim(string(jsonMap["token"]), "\"")
}

func GetVerifiedConsentToken(consentId string, dob string) string {
	resp, err := http.Post(
		host+fmt.Sprintf("/v1/providers/consents/%s/verify", consentId),
		"application/json",
		strings.NewReader(fmt.Sprintf(`{"dateOfBirth": %q}`, dob)),
	)
	if err != nil {
		return ""
	}

	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	var jsonMap map[string]json.RawMessage
	json.Unmarshal(body, &jsonMap)
	return "Bearer " + strings.Trim(string(jsonMap["token"]), "\"")
}

// Get imageIds list in the transfer from sqldb
func SqlGetImagesFromTransfer(scanId string) (objectIdList []string, err error) {
	rows, err := sqldb.Query(
		"SELECT object_id FROM objects WHERE is_report=0 and scan_id=?",
		scanId,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		var objectId string
		err = rows.Scan(&objectId)
		if err != nil {
			return nil, err
		}
		objectIdList = append(objectIdList, objectId)
	}
	return objectIdList, nil
}
