package apptest

import (
	"bytes"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/coreapi/pkg/util/testutil"

	"gitlab.com/pockethealth/coreapi/pkg/util/secure"
)

func TestRequest(t *testing.T) {

	t.Run("good request without delegate", func(t *testing.T) {

		//generate random ssn
		ohip, _ := secure.GenerateRandomDigits(10)
		client := &http.Client{}
		values := map[string]io.Reader{
			"inApp": strings.NewReader(`true`),
			"requestBody": strings.NewReader(
				`{"firstName":"Grace","lastName":"Cochrane","dob":"12/12/1999","email":"<EMAIL>","tel":"**********","orgId":9,"providerId":13,"contents":{"mode":"daterange","allStudies":true,"enrollment_consent":true},"ohip":"` + ohip + `"}`,
			),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,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`,
			),
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err := http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		//expect a consentPDF and request ID in the body
		//check for the minimum of the provider and patientconsent text
		if string(jsonMap["consentPdf"]) == "" {
			t.Fatalf("expected consent pdf in response body")
		}

		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		if requestId == "" {
			t.Fatalf("expected request id in response body")
		}

		t.Cleanup(func() {
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
		})

		//look up request Id
		rows, err := sqldb.Query("SELECT * from requests where id=?", requestId)
		if err != nil {
			t.Fatalf("expected request to exist in the requests table")
		}
		defer rows.Close()
	})

	t.Run("good request with delegate as a family member", func(t *testing.T) {
		//generate random ohip
		ohip, _ := secure.GenerateRandomDigits(10)
		client := &http.Client{}
		values := map[string]io.Reader{
			"inApp": strings.NewReader(`true`),
			"requestBody": strings.NewReader(
				`{"firstName":"Grace","lastName":"Cochrane","dob":"12/12/1999","email":"<EMAIL>","tel":"**********","orgId":9,"providerId":13,"contents":{"mode":"daterange","allStudies":true,"enrollment_consent":true, "delegate":{"firstName": "Grace", "lastName": "Cochrane", "relation": "Sibling", "relationType": "family-member"}},"ohip":"` + ohip + `"}`,
			),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,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`,
			),
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// add delegate photo id
		f, err := os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		if err != nil {
			t.Fatalf("test setup failed: could not open file to send request: %v", err)
		}
		fw, err := testutil.CreateFormFileWithType(
			w,
			"delegatePhotoId[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)

		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add auth document
		f, err = os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)

		fw, err = testutil.CreateFormFileWithType(
			w,
			"delegForm[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)

		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add form data
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err := http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		//expect a consentPDF and request ID in the body
		//check for the minimum of the provider and patientconsent text
		if string(jsonMap["consentPdf"]) == "" {
			t.Fatalf("expected consent pdf in response body")
		}

		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		require.NotEmpty(t, requestId)

		//look up request Id
		rows, err := sqldb.Query("SELECT * from requests where id=?", requestId)
		if err != nil {
			t.Fatalf("expected request to exist in the requests table")
		}
		t.Cleanup(func() {
			rows.Close()
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
		})

		var status sql.NullString

		err = sqldb.QueryRow("SELECT scan_id FROM requests where id=?", requestId).Scan(&status)
		if err != nil {
			t.Fatalf("expected scan_id to exist in the requests table")
		}

	})

	t.Run("good request with delegate which isn't a family member and provider PX delegate review is set to true", func(t *testing.T) {
		//generate random ohip
		ohip, _ := secure.GenerateRandomDigits(10)
		client := &http.Client{}
		values := map[string]io.Reader{
			"inApp": strings.NewReader(`true`),
			"requestBody": strings.NewReader(
				`{"firstName":"Grace","lastName":"Cochrane","dob":"12/12/1999","email":"<EMAIL>","tel":"**********","orgId":9,"providerId":13,"contents":{"mode":"daterange","allStudies":true,"enrollment_consent":true, "delegate":{"firstName": "Grace", "lastName": "Cochrane", "relation": "Guardian", "relationType": "public-guardian"}},"ohip":"` + ohip + `"}`,
			),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,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`,
			),
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// add delegate photo id
		f, err := os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)
		fw, err := testutil.CreateFormFileWithType(
			w,
			"delegatePhotoId[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)

		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add auth document
		f, err = os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)

		fw, err = testutil.CreateFormFileWithType(
			w,
			"delegForm[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)
		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add form data
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err := http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		//expect a consentPDF and request ID in the body
		//check for the minimum of the provider and patientconsent text
		if string(jsonMap["consentPdf"]) == "" {
			t.Fatalf("expected consent pdf in response body")
		}

		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		require.NotEmpty(t, requestId)

		//look up request Id
		rows, err := sqldb.Query("SELECT * from requests where id=?", requestId)
		if err != nil {
			t.Fatalf("expected request to exist in the requests table")
		}
		t.Cleanup(func() {
			rows.Close()
		})

		var status string

		err = sqldb.QueryRow("SELECT scan_id FROM requests where id=?", requestId).Scan(&status)
		if err != nil {
			t.Fatalf("expected scan_id to exist in the requests table")
		}

		if status != "DELEGATE_PENDING_AUTH" {
			t.Fatalf("expected status to be DELEGATE_PENDING_AUTH but got %s", status)
		}

		t.Cleanup(func() {
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
		})

	})

	t.Run("good request with delegate which isn't a family member and provider PX delegate review is set to false", func(t *testing.T) {
		//generate random ohip
		ohip, _ := secure.GenerateRandomDigits(10)
		client := &http.Client{}
		values := map[string]io.Reader{
			"inApp": strings.NewReader(`true`),
			"requestBody": strings.NewReader(
				`{"firstName":"Grace","lastName":"Cochrane","dob":"12/12/1999","email":"<EMAIL>","tel":"**********","orgId":200152,"providerId":200192,"contents":{"mode":"daterange","allStudies":true,"enrollment_consent":true, "delegate":{"firstName": "Grace", "lastName": "Cochrane", "relation": "Guardian", "relationType": "public-guardian"}},"ohip":"` + ohip + `"}`,
			),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,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`,
			),
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// add delegate photo id
		f, err := os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)
		fw, err := testutil.CreateFormFileWithType(
			w,
			"delegatePhotoId[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)

		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add auth document
		f, err = os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)

		fw, err = testutil.CreateFormFileWithType(
			w,
			"delegForm[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)
		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add form data
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err := http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		//expect a consentPDF and request ID in the body
		//check for the minimum of the provider and patientconsent text
		if string(jsonMap["consentPdf"]) == "" {
			t.Fatalf("expected consent pdf in response body")
		}

		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		require.NotEmpty(t, requestId)

		//look up request Id
		rows, err := sqldb.Query("SELECT * from requests where scan_id IS NULL AND id=?", requestId)
		if err != nil {
			t.Fatalf("expected request to exist in the requests table for ID: %s", requestId)
		}
		t.Cleanup(func() {
			rows.Close()
		})

		t.Cleanup(func() {
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
		})

	})

	t.Run("good request with delegate for custom delegate flow", func(t *testing.T) {
		//generate random ohip
		ohip, _ := secure.GenerateRandomDigits(10)
		client := &http.Client{}
		values := map[string]io.Reader{
			"inApp": strings.NewReader(`true`),
			"requestBody": strings.NewReader(
				`{"firstName":"Grace","lastName":"Cochrane","dob":"12/12/1999","email":"<EMAIL>","tel":"**********","orgId":9,"providerId":13,"contents":{"mode":"daterange","allStudies":true,"enrollment_consent":true, "delegate":{"firstName": "Grace", "lastName": "Cochrane", "relation": "Attorney"}},"ohip":"` + ohip + `"}`,
			),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAABJlBMVEX////oIzcAAADpIzjlIjbnIzblIjjrIzbtIzT7+/v4+PjsIznrIzPqIzrkIjXrIzUABADw7+8VAADW1NTx8fHj4uLwJDm3tLX2JTffITjPzc3p6OjcIDYfAACgnp/HxcWJhocEoeEFn+RnYGJST0+XEya2GTCjFCbaIDvPHjYoAAnTHjOfFCYcAAURFRRhYWF8Dh9pCxiNioqcmJh1cnNHBhFCQUFSVFMALUIASGgEVHILHS06MzQAao8Fj8YANkYcEw0AIjEKgLIGltMxJyQAo9YADBoFkMkBYYcAc5goJScjICAADBM4AAs0DBCEChotOjihFy8AJSJva2wiGhpdDBMAFRExAAzCHTY6AAASISBwWF5gAA9OABIoLCuADCE1EhN4EiN/b0JTAAAJDklEQVR4nO3be3faRhYAcGs0D5mRRiCZkQTi4RgJC2HzhrR108dmu904mLiObeINsP7+X2IHSDZuT5vdPVsfIp/7s33g8NfcM4977yDv7QEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACPbb/gujW3sL/rcTyaQm1w1GgcDWqFXY/kkbj9MXr+1ddffXNWL+96LI+iXD/75tsXh4fPXnz3ffVg16N5DNWXP7w4fPbs8PDwx2/G7q5H8wjKQ/TtMxWh+vvL85fVXQ/nERQ76MfDv25+Dr/+vr/r4fzJ9lV+cMc/vVDzpxbpMxVhPZcr5HY9rD9R0SsVvbPnf1vvwvU6/do58qp17wmFWBw2jhqnP/z87d83fv4OvVq+mlSfUIS52vAcrb1Gr396/RqhPELjvvt0aptCaXB0Pr2P5sdxcyPu9qYn46FX3PXI/iRl7+rVRZQEXDMMn1q+tKUuwmTWejMsPYWFuu/2z27boWQch1gnzKAaNYRPMA3bF2eD7Kf+/VJ92ZtbtrBps9e75Jp5HPVmAda4FrBk9ctVLeMF3IF39bZLfcaEbDv5/NSi+sxBaIYtxrjmy2ZrXM3ibvz3IbnvNSoRNSiVBPfUAepQY36rXq9T3SY2F6Y1795kbzfmXK+23V77tcZFFBgEYybIKI/yt5Y/X2eN65Sr/ahpuibS3s1Vbccj/h/lSldnL682/VFpWIm5Tyin0hYqQqcrRbyOMMKcrCPk3MJ0hY6yc97k3JLrDs67l+8aKp8Xh3ezUJhCrVGu8fbopJdI83iK0GjOmaCarutM17m1uOlnpSUuVI+WjX800Fz27ryDQvX0PbeZITVsasKUx4HGBTF4EBpqeRJNJ7pOiCZIslgOMrIVvcao2VwgFIvrpXdQ6lRig+gU29KwsPADaZq+FUpBMRaYMUJ0CxPdkCxGWWmJB3erwDba03g+7dSK9fMetqiaolWobZHti649wIhpW12UkXXqNZzF5dxKwhWql2oNlEibGloXdaX2h3TKmYxbY2/Xg/9v5LwGqlRuE9/ooXG9vlxIKqhJ7tEl++MIrYAJpvfyVxmYxFyt8c8oSZsU07S9uFmi2CIMy7CHVp+bQ4sJbKRo6X35zVR56LS5ERwHgeQsiJ13qTo4MfsPERIbq40pF6j/5d8Tu51pnERTNPdtYWDdSlQlQ1Uf0c3fS04ZebBUVbJQKZJsIySEURqjzpdfnxYGr962kBNZaXMuLCENwW09kCRCi3WSoA8jJGFzbn2cQ8o5T5wsJIyCVx87MZWxg6btNPRtihnR7CZyuOA0eJAksJ1Upun2fXrdpcI4XtxVv/yNqArRyTT0xWVlubxx7hMuVANBeHKiihxTHaufIhSkjUbbCPVggWLVF3fz/SzUNaXlgor4YjLwhkvU1NUmNFU66KF7SzDb+hQhS0foUm6WrWocVb4UVjt/lYUI3Ynz/v2rjpfbKzVQLENGbErVMq3MuYntB6u0mb8I8TZYI0YLKWSMJl/+UbPO+cOr+qCkOqdSo5LoJtUFszR8jXopUyerIBoLpSnEfIRmgmnMtIRJYnSrJjt13lS//Hyh5Mrb86LUaFFsCssyVYOfOCgiockkt6hhhjxZh2xpVGWTMO72UCslMnTynaNqBgqbj0qTCsO6yngqQu5fIrTivqShCsqkSRe15lK1/YzNeye31yco5oE1G71vLbMxjRtu4yS0sKmrMFS2N+YXaBqnzDBMP2y+Q6OYU82Sgl2jbuoHcWr7wpJ+XGlkYS9uuR01MUKoqs02WGCyZIQqi26z2ZwtHLRIuKQ65oKqCshux4YVmOExNcJ34+xc2RwMUcQJZqYpdEOoEK32qLX57gKNLgPV8psa5lSu8s4ITdN5Yq1QUyTTbDRRG/vV5bUw+YNilFlJO4qiyziUtth8okoZEY2Q00ycKY1QN+mhenZWqdqIKDECKR4UMsLwfd2igWDG5gOOTd+K0UjlwoofIefibaeWhaz/Qa6qEjmj/FOEtoEFEcSWDG/jZlQIoSKkqnJN7vPjYdXNUIB7e7UxmnP88E6GqCZCV0EJ/KGywcxgKlfejxBqnZz3y1kovR8o1E9HiXxQbutYNYkB5yqwDzuTaJgG68u5Wft+ep6dQ+ajWgPN1FbT8ccwCdZspmFCPgWt69JO44TapHuXvQj3vXMUMdU2UFP7DJUwfYPQURYuan5LrdNKHBiGEJ+LEEsrwGyemUvhXynWT1FkceOzc6iqcWrQBcrm43zu8BRdx8Fnrks3kyjDCJ1l9PEatzrOO7O5OlHWx8rvbkOTqG7KGWeoq/i1XKk/Rqi3asdJSn1h0MAQzCaMGrbanoyJII1X0/zYy+Qa3Sp4w8kdQpXpqHccCKpJaTKVG0k8a8bNqNsbOafjupfx5xXKpVq907hDbU1Prlcp0YVNk1H+5pebu+V4UvdKGY9vo1D2OiiSejN/m5qqOTSs63yj3h9UvafzZHthiGaSpKgVUI3qIlzc9cvlg9xTCW9v85BwZFiJ46SUGEJPW6f1kvuEHjLN5dyr/EzTwxZKBZZqH7byryZHfc99Cntw78D1qvWjs/w9x6yF5sxU1TeOo6i7cJadQbaawt9Trg064+Xb29Ft5FNtoc4bk3KbyYASkUSj846X1WS/te8OxnejWZxSGoQq0UdoZJqWYIIRw2SqLI9UPZPpWSz231RUCmTCNn1BNCtBLRVdYGiUSF3lf1teokkW24qPDgbnFwlXZRrDjFFM9PQ23/QptSyqsobOLFXIXZ9meRLdDlpxZlGM1yExtTCv0X1IuXpnmOrXsG3Zu8vyfwnVziqJYbD1Ixd8fWUa95zT00q3GSeB0CxMw3Q+O2lk6RLxt0pj1Ay39zTEDONe5W5SH3be3KDpordade97709OO5ntnNYK/aWzuIznyTyOZ6N1+isVyq43qHcmy03dfTVUn+x6lP+XcvVqiRByEMrfjY8+3fkW3VKtViu5xQw3hh/kDrzqcKLmqt73ihneb5+3Xyxm6CsXAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdu5fkJ8Nb3oo1YgAAAAASUVORK5CYII=`,
			),
		}
		// Prepare a form
		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		// add auth document
		f, err := os.OpenFile("assets/samplejpeg.jpeg", os.O_RDONLY, 0600)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not open file to send request: %v", err),
		)
		fw, err := testutil.CreateFormFileWithType(
			w,
			"delegForm[0]",
			"samplejpeg.jpeg",
			"image/jpeg",
		)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not create form data header: %s", err),
		)

		_, err = io.Copy(fw, f)
		require.NoError(
			t,
			err,
			fmt.Sprintf("test setup failed: could not write file to form: %v", err),
		)

		// add form data
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			// Add other fields
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		// Don't forget to close the multipart writer.
		w.Close()

		req, err := http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		//expect a consentPDF and request ID in the body
		//check for the minimum of the provider and patientconsent text
		if string(jsonMap["consentPdf"]) == "" {
			t.Fatalf("expected consent pdf in response body")
		}

		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		require.NotEmpty(t, requestId)

		//look up request Id
		rows, err := sqldb.Query("SELECT * from requests where id=?", requestId)
		if err != nil {
			t.Fatalf("expected request to exist in the requests table")
		}
		t.Cleanup(func() {
			rows.Close()
		})
		var status sql.NullString

		err = sqldb.QueryRow("SELECT scan_id FROM requests where id=?", requestId).Scan(&status)
		if err != nil {
			t.Fatalf("expected scan_id to exist in the requests table")
		}

		t.Cleanup(func() {
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
		})
	})
}

func TestIncompleteRequest(t *testing.T) {
	t.Run("start and complete request, no drop off", func(t *testing.T) {
		client := &http.Client{}

		// setup incomplete req body
		ohip, err := secure.GenerateRandomDigits(10)
		rand := strconv.FormatInt(time.Now().UnixNano(), 10)
		email := "coreApiApptest" + rand + "@pocket.health"
		reqBodyStep1 := []byte(`{
			"requestData": {
				"firstName": "Grace",
				"lastName": "Cochrane",
				"dob": "12/12/1999",
				"email": "` + email + `",
				"tel": "**********",
				"orgId": 9,
				"providerId": 13,
				"ohip": "` + ohip + `"
			},
			"lastCompletedStep": "PATIENT_INFO"
		}`)

		// create incomplete req
		req, err := http.NewRequest(
			"POST",
			host+"/v1/requests/incomplete",
			bytes.NewBuffer(reqBodyStep1),
		)
		deviceID := "james123"
		ampCookie := base64.StdEncoding.EncodeToString(
			[]byte(fmt.Sprintf("{\"DeviceID\": \"%s\"}", deviceID)),
		)
		// TODO: load configs in apptest to avoid hardcoding this
		ampCookieHeader := "AMP_06c2865596"
		req.AddCookie(&http.Cookie{Name: ampCookieHeader, Value: ampCookie})
		require.NoError(t, err)

		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		// read response
		resBody, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(resBody, &jsonMap)
		incompleteRequestId := strings.Trim(string(jsonMap["incompleteRequestId"]), "\"")
		assert.NotEmpty(t, incompleteRequestId)

		// setup complete req body
		reqBodyComplete := `{
			"firstName": "Grace",
			"lastName": "Cochrane",
			"dob": "12/12/1999",
			"email": "` + email + `",
			"tel": "**********",
			"orgId": 9,
			"providerId": 13,
			"ohip": "` + ohip + `",
			"contents": {
				"mode": "daterange",
				"allStudies": true,
				"enrollment_consent": true
			}
		}`
		values := map[string]io.Reader{
			"requestBody":  strings.NewReader(reqBodyComplete),
			"paymentToken": strings.NewReader(`{"token":"","paymentProvider":""}`),
			"signatureImg": strings.NewReader(
				`data:image/png;base64,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`,
			),
		}
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		for key, r := range values {
			var fw io.Writer
			if x, ok := r.(io.Closer); ok {
				defer x.Close()
			}
			fw, _ = w.CreateFormField(key)
			io.Copy(fw, r)
		}
		w.Close()

		// create complete req
		req, err = http.NewRequest("POST", host+"/v2/requests/create", &b)
		require.NoError(t, err)
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err = client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, resp.StatusCode)

		// read response
		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		json.Unmarshal(body, &jsonMap)
		requestId := strings.Trim(string(jsonMap["requestId"]), "\"")
		assert.NotEmpty(t, requestId)

		t.Cleanup(func() {
			// requests have to be deleted first,
			// since incomplete_requests has a foreign key with requests
			sqldb.Exec("DELETE FROM requests WHERE id=?", requestId)
			sqldb.Exec("DELETE FROM incomplete_requests WHERE id=?", incompleteRequestId)
		})
	})
}
