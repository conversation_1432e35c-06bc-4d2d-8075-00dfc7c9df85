package apptest

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"testing"

	"gitlab.com/pockethealth/coreapi/pkg/models"
)

func TestPatients(t *testing.T) {

	var createdPtId string

	t.Run(
		"Create patient", func(t *testing.T) {
			token := LoginAndGetToken("<EMAIL>", "test1234!")
			client := &http.Client{}
			req, err := http.NewRequest(
				"POST",
				host+"/v2/patients",
				bytes.NewBufferString(
					`{
				"firstName": "Test",
				"lastName": "User",
				"DOB": "1992-10-10",
				"ohip": "1231-231-232",
				"phone": "**********",
				"email": "<EMAIL>",
				"postalCode": "A1A 1A1"
			}`,
				),
			)
			req.Header.Set("Authorization", token)
			if err != nil {
				t.<PERSON><PERSON><PERSON>("got error when expected none: %q", err.Error())
			}

			resp, err := client.Do(req)
			if err != nil {
				t.<PERSON>rrorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()

			if resp.StatusCode >= http.StatusBadRequest {
				t.Errorf(
					"got status code %d when expected : %d",
					resp.StatusCode,
					http.StatusOK,
				)
			}

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			var jsonList string
			err = json.Unmarshal(body, &jsonList)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			createdPtId = jsonList
		},
	)

	t.Run(
		"Update patient", func(t *testing.T) {
			token := LoginAndGetToken("<EMAIL>", "test1234!")
			client := &http.Client{}
			req, err := http.NewRequest(
				"PATCH",
				host+"/v2/patients/"+createdPtId,
				bytes.NewBufferString(
					`{
				"firstName": "Test",
				"lastName": "Update",
				"DOB": "1992-10-10",
				"ohip": "1231-231-232",
				"phone": "**********",
				"email": "<EMAIL>",
				"postalCode": "A1A 1A1"
			}`,
				),
			)
			req.Header.Set("Authorization", token)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf(
					"got status code %d when expected : %d",
					resp.StatusCode,
					http.StatusOK,
				)
			}

		},
	)

	t.Run(
		"Get patient", func(t *testing.T) {
			token := LoginAndGetToken("<EMAIL>", "test1234!")
			client := &http.Client{}
			req, err := http.NewRequest(
				"GET",
				host+"/v2/patients/"+createdPtId,
				nil,
			)

			req.Header.Set("Authorization", token)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf(
					"got status code %d when expected : %d",
					resp.StatusCode,
					http.StatusOK,
				)
			}

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			pt := &models.PhiPatient{}
			err = json.Unmarshal(body, &pt)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			if pt.FirstName != "Test" {
				t.Errorf("got first name %s when expected : %s", pt.FirstName, "Test")
			}

			if pt.LastName != "Update" {
				t.Errorf("got last name %s when expected : %s", pt.LastName, "Update")
			}

			if pt.Ohip != "1231-231-232" {
				t.Errorf("got ohip %s when expected : %s", pt.Ohip, "1231-231-232")
			}

			if pt.PostalCode != "A1A 1A1" {
				t.Errorf("got postal code %s when expected : %s", pt.PostalCode, "A1A 1A1")
			}

		},
	)

	t.Run(
		"Delete patient", func(t *testing.T) {
			token := LoginAndGetToken("<EMAIL>", "test1234!")
			client := &http.Client{}
			req, err := http.NewRequest(
				"DELETE",
				host+"/v2/patients/"+createdPtId,
				nil,
			)

			req.Header.Set("Authorization", token)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf(
					"got status code %d when expected : %d",
					resp.StatusCode,
					http.StatusOK,
				)
			}

		},
	)

	t.Run(
		"Get all patients", func(t *testing.T) {
			token := LoginAndGetToken("<EMAIL>", "test1234!")
			client := &http.Client{}
			req, err := http.NewRequest(
				"GET",
				host+"/v2/patients",
				nil,
			)

			req.Header.Set("Authorization", token)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf(
					"got status code %d when expected : %d",
					resp.StatusCode,
					http.StatusOK,
				)
			}

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			var jsonList []json.RawMessage
			err = json.Unmarshal(body, &jsonList)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}

			if len(jsonList) == 0 {
				t.Errorf("got no patients when expected some")
			}

		},
	)
}
