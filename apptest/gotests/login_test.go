package apptest

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"

	"github.com/segmentio/ksuid"
	"github.com/stretchr/testify/require"
)

func TestLogin(t *testing.T) {

	t.Run("valid login", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test12@34"}`),
		)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, resp.StatusCode)

		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		require.NoError(t, err, "couldn't read response body")

		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		if string(jsonMap["token"]) == "" {
			t.Errorf("expected token in response body")
		}

	})

	t.Run("invalid password login", func(t *testing.T) {
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "testbad"}`),
		)
		require.NoError(t, err)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("invalid user login", func(t *testing.T) {
		client := &http.Client{}
		notauseremailstring := fmt.Sprintf("%<EMAIL>", ksuid.New().String())
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(fmt.Sprintf(`{"email": "%s", "password": "test1234"}`, notauseremailstring)),
		)
		require.NoError(t, err)
		resp, err := client.Do(req)
		require.NoError(t, err)
		require.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run(
		"invalid credentials error should match account does not exist error",
		func(t *testing.T) {
			client := &http.Client{}
			req, err := http.NewRequest(
				"POST",
				host+"/v2/users/login",
				strings.NewReader(`{"email": "<EMAIL>", "password": "testbad"}`),
			)
			require.NoError(t, err)
			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("could not read resp body: %v", err)
			}
			var badPasswordError string
			json.Unmarshal(body, &badPasswordError)

			req, err = http.NewRequest(
				"POST",
				host+"/v2/users/login",
				strings.NewReader(
					`{"email": "<EMAIL>", "password": "test"}`,
				),
			)
			if err != nil {
				t.Fatalf("could not create req: %v", err)
			}
			resp, err = client.Do(req)
			if err != nil {
				t.Errorf("got error when expected none: %q", err.Error())
			}
			defer resp.Body.Close()
			body, err = ioutil.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("could not read resp body: %v", err)
			}
			var accountDoesNotExistError string
			json.Unmarshal(body, &accountDoesNotExistError)

			if badPasswordError != accountDoesNotExistError {
				t.Errorf(
					"Error messages do not match: %s, %s",
					badPasswordError,
					accountDoesNotExistError,
				)
			}
		},
	)
}

func TestLogout(t *testing.T) {

	t.Run("valid logout", func(t *testing.T) {
		//first login
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test12@34"}`),
		)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got login error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got login status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		token := string(jsonMap["token"])
		if token == "" {
			t.Errorf("expected token in response body")
		}

		token = "Bearer " + strings.Trim(token, "\"")

		//logout with token
		req, err = http.NewRequest("DELETE", host+"/v1/users/logout", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		req.Header.Set("Authorization", token)
		resp, err = client.Do(req)
		if err != nil {
			t.Errorf("got logout error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got logout status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}

		//teardown: remove token from blacklist. tests run fast enough that it's possible to generate the same token in a future test.
		sqldb.Exec("DELETE FROM token_blacklist where token = ?", token)

	})

	t.Run("invalid logout", func(t *testing.T) {
		//logout without a token
		client := &http.Client{}
		req, err := http.NewRequest("DELETE", host+"/v1/users/logout", nil)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got logout error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"got logout status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}
	})
}

func TestBlacklist(t *testing.T) {

	t.Run("logout then reuse token", func(t *testing.T) {
		//first login
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test1234"}`),
		)
		resp, err := client.Do(req)
		if err != nil {
			t.Errorf("got login error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf("got login status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		token := string(jsonMap["token"])
		if token == "" {
			t.Errorf("expected token in response body")
		}

		token = "Bearer " + strings.Trim(token, "\"")
		//logout with token
		req, err = http.NewRequest("DELETE", host+"/v1/users/logout", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		req.Header.Set("Authorization", token)
		resp, err = client.Do(req)
		if err != nil {
			t.Errorf("got logout1 error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf(
				"got logout1 status code %d when expected : %d",
				resp.StatusCode,
				http.StatusOK,
			)
		}

		//try to logout with same token again
		resp, err = client.Do(req)
		if err != nil {
			t.Errorf("got error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusUnauthorized {
			t.Errorf(
				"logout2 - got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}

		//teardown: remove token from blacklist. tests run fast enough that it's possible to generate the same token in a future test.
		sqldb.Exec("DELETE FROM token_blacklist where token = ?", token)
	})
}

func TestAcctSvcJWTCompat(t *testing.T) {
	t.Run("login then logout using AS JWT token", func(t *testing.T) {
		//first login
		client := &http.Client{}
		req, err := http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test1234"}`),
		)
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("got login error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Fatalf("got login status code %d when expected : %d", resp.StatusCode, http.StatusOK)
		}
		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		var jsonMap map[string]json.RawMessage
		json.Unmarshal(body, &jsonMap)
		token := string(jsonMap["token"])
		if token == "" {
			t.Fatalf("expected token in response body")
		}

		token = strings.Trim(token, "\"")

		cookies := resp.Cookies()
		if len(cookies) != 1 && cookies[0].Name != "refresh_token" {
			t.Fatalf("expected refresh token in cookie")
		}

		token = "Bearer " + token
		//get new token with refresh
		req, err = http.NewRequest("POST", host+"/v2/users/refresh", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		req.Header.Set("Authorization", token)
		req.AddCookie(cookies[0])
		resp, err = client.Do(req)
		if err != nil {
			t.Fatalf("got refresh error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Fatalf(
				"got refresh status code %d when expected : %d",
				resp.StatusCode,
				http.StatusOK,
			)
		}

		defer resp.Body.Close()
		body, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("could not read resp body: %v", err)
		}
		json.Unmarshal(body, &jsonMap)
		token = string(jsonMap["token"])
		if token == "" {
			t.Fatalf("expected token in response body")
		}

		token = strings.Trim(token, "\"")
		token = "Bearer " + token
		//logout with refreshed token
		req, err = http.NewRequest("DELETE", host+"/v1/users/logout", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		req.Header.Set("Authorization", token)
		resp, err = client.Do(req)
		if err != nil {
			t.Fatalf("got logout1 error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusOK {
			t.Fatalf(
				"got logout1 status code %d when expected : %d",
				resp.StatusCode,
				http.StatusOK,
			)
		}
		cookies = resp.Cookies() // prep cookies for testing refresh token later

		//try to logout with same token again
		resp, err = client.Do(req)
		if err != nil {
			t.Fatalf("got error when expected none: %q", err.Error())
		}
		if resp.StatusCode != http.StatusUnauthorized {
			t.Fatalf(
				"logout2 - got status code %d when expected : %d",
				resp.StatusCode,
				http.StatusUnauthorized,
			)
		}

		// try to refresh token again with prev response cookies
		req, err = http.NewRequest("POST", host+"/v2/users/refresh", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		req.AddCookie(cookies[0])
		resp, err = client.Do(req)
		if err != nil {
			t.Fatalf("got refresh error when expected none: %q", err.Error())
		}
		if resp.StatusCode == http.StatusOK {
			t.Fatalf(
				"got refresh status code %d when expected : %d",
				resp.StatusCode,
				http.StatusInternalServerError,
			)
		}

		//login again to reset acctsvc's lockout tracker
		req, err = http.NewRequest(
			"POST",
			host+"/v2/users/login",
			strings.NewReader(`{"email": "<EMAIL>", "password": "test1234"}`),
		)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}
		client.Do(req)

		//teardown: remove token from blacklist. tests run fast enough that it's possible to generate the same token in a future test.
		sqldb.Exec("DELETE FROM token_blacklist where token = ?", token)
	})
}
