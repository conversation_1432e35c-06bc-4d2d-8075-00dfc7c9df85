CREATE TABLE IF NOT EXISTS `appointment_reminders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reminder_id` varchar(255) NOT NULL,
  `org_id` int(11) NOT NULL,
  `patient_name` varchar(255) NOT NULL,
  `mrn` varchar(255) NOT NULL,
  `dob` datetime NOT NULL,
  `gender` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `mobile` varchar(255) NOT NULL,
  `clinic_name` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `appointment_cadence` int(11) NOT NULL,
  `step_id` int(11) DEFAULT NULL,
  `event_sent` timestamp NOT NULL,
  `reminder_sent` timestamp NULL DEFAULT NULL,
  `message_status` varchar(255) DEFAULT NULL,
  `clinic_info_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `appointment_reminders_reminderid_index` (`reminder_id`),
  KEY `fk_clinic_info_idx` (`clinic_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
