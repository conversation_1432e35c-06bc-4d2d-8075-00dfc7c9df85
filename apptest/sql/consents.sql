CREATE TABLE IF NOT EXISTS `consents` (
  `id` varchar(255) NOT NULL,
  `org_id` bigint(20) DEFAULT NULL,
  `mrn` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `opt_in` tinyint(1) DEFAULT NULL,
  `opt_out` tinyint(1) DEFAULT NULL,
  `enrolled` tinyint(1) DEFAULT NULL,
  `request_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `response_timestamp` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ip` varchar(45) DEFAULT NULL,
  `pre_appointment` tinyint(1) DEFAULT NULL,
  `post_appointment` tinyint(1) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

