CREATE TABLE IF NOT EXISTS `exams` (
  `uuid` varchar(100) NOT NULL,
  `exam_uid` varchar(100) DEFAULT NULL,
  `transfer_id` varchar(100) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `activated` tinyint(1) NOT NULL DEFAULT '0',
  `referring_physician` text,
  `description` text,
  `date` char(8) DEFAULT NULL,
  `patient_name` text,
  `patient_mrn` varchar(255) DEFAULT NULL,
  `dob` char(8) DEFAULT NULL,
  `sex` char(1) DEFAULT NULL,
  `phone` varchar(64) DEFAULT NULL,
  `accession_number` varchar(64) DEFAULT NULL,
  `body_part` varchar(255) DEFAULT NULL,
  `modality` varchar(10) DEFAULT NULL,
  `has_report` tinyint(1) DEFAULT NULL,
  `access_revoked_at` timestamp NULL DEFAULT NULL,
  `access_restored_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `uuid` (`uuid`),
  <PERSON><PERSON><PERSON> `user_id` (`user_id`),
  KEY `transfer_id` (`transfer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;