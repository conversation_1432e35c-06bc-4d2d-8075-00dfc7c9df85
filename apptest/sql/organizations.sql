CREATE TABLE IF NOT EXISTS `organizations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` text,
  `admin_user_id` int(11) DEFAULT NULL,
  `admin_email` varchar(255) DEFAULT NULL,
  `container_id` varchar(256) DEFAULT NULL,
  `patient_fee` int(11) DEFAULT NULL,
  `url_name` text NOT NULL,
  `security_question` text,
  `pdf_path` text,
  `mht_path` text,
  `dcm_pdf_reports` tinyint(1) DEFAULT '0',
  `request_email_template` text,
  `plan_id` text,
  `synonyms` text,
  `tax_percentage` float NOT NULL DEFAULT '0',
  `tax_name` char(5) DEFAULT NULL,
  `region` char(2) NOT NULL DEFAULT 'CA',
  `offline_modal_title` text,
  `offline_modal_html` text,
  `send_reports` tinyint(1) DEFAULT '0',
  `report_send_time` int(2) DEFAULT NULL,
  `enrollment_org` tinyint(1) DEFAULT NULL,
  `min_report_date` varchar(8) DEFAULT NULL,
  `free_trial` tinyint(1) DEFAULT '0',
  `regex` text,
  `mrn_text` text,
  `post_release_report_delay_days` int(11) NOT NULL DEFAULT '0',
  `require_delegate_review` tinyint(1) DEFAULT NULL,
  `default_language` varchar(20) DEFAULT NULL,
  `alt_url_names` text,
  `report_definitions` tinyint(1) DEFAULT '0',
  `pt_png_download` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49780 DEFAULT CHARSET=latin1;

