CREATE TABLE IF NOT EXISTS  `appointment_data` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `time` varchar(255) NOT NULL,
  `modality` varchar(255) NOT NULL,
  `appointment_procedure` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `prep_link` varchar(255) DEFAULT NULL,
  `appointment_reminder_id` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_idx` (`appointment_reminder_id`),
  KEY `idx_appointment_reminder_id` (`appointment_reminder_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;