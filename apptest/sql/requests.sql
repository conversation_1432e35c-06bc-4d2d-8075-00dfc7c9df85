CREATE TABLE IF NOT EXISTS `requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `clinic_id` bigint(20) NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `tel` varchar(20) DEFAULT NULL,
  `ohip` char(20) DEFAULT NULL,
  `ohip_vc` char(2) DEFAULT NULL,
  `mrn` varchar(255) DEFAULT NULL,
  `alt_h_id` varchar(255) DEFAULT NULL,
  `dob` char(10) DEFAULT NULL,
  `contents` text,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `scan_id` varchar(100) DEFAULT NULL,
  `enrolled` tinyint(1) NOT NULL DEFAULT '0',
  `ssn` char(9) DEFAULT NULL,
  `ipn` char(255) DEFAULT NULL,
  `alt_last_name` varchar(255) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `order_id` char(28) DEFAULT NULL,
  `rejection_reason` tinyint(3) DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `email` (`email`),
  KEY `idx_clinic_id` (`clinic_id`),
  KEY `idx_scan_id` (`scan_id`),
  KEY `idx_clinic_scan_enrolled` (`clinic_id`,`scan_id`,`enrolled`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
