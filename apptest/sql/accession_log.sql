CREATE TABLE IF NOT EXISTS `accession_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint NOT NULL,
  `transfer_id` varchar(100) NOT NULL,
  `accession` varchar(255) NOT NULL,
  `imaging_uploaded` tinyint(1) DEFAULT NULL,
  `report_uploaded` tinyint(1) DEFAULT NULL,
  `issue` text,
  `suppress` tinyint(1) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_accession_report` (`suppress`,`org_id`,`accession`,`report_uploaded`),
  KEY `idx_imaging` (`suppress`,`org_id`,`accession`,`imaging_uploaded`),
  KEY `idx_iss_ts_orgid_report` (`issue`(32),`timestamp`,`org_id`,`report_uploaded`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;