CREATE TABLE IF NOT EXISTS `incomplete_requests` (
  `id` varchar(100) NOT NULL DEFAULT '',
  `request_id` bigint(20) DEFAULT NULL,
  `created_timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_timestamp` D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expiry_timestamp` DATETIME DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `clinic_id` bigint(20) NOT NULL,
  `data_token` text,
  `last_completed_step` varchar(255) DEFAULT NULL,
  `emails_sent` INT DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
