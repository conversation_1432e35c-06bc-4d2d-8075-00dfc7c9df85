CREATE TABLE IF NOT EXISTS  `forms` (
  `organization_id` bigint(20) NOT NULL,
  `logo` text NOT NULL,
  `base64` mediumtext NOT NULL,
  `alt_h_id` tinyint(1) NOT NULL,
  `mrn` text,
  `single_date` tinyint(1) NOT NULL,
  `multi_locations` tinyint(1) NOT NULL,
  `delegate` tinyint(1) NOT NULL,
  `reports` tinyint(1) NOT NULL,
  `modalities` text,
  `record_txt` text NOT NULL,
  `payment` tinyint(1) NOT NULL,
  `pay_txt` tinyint(1) NOT NULL,
  `hub_clinic` bigint(20) DEFAULT NULL,
  `opener_text` text NOT NULL,
  `portal_text` text,
  `timeframe` text NOT NULL,
  `patient_consent_lang` text,
  `delegate_consent_lang` text,
  `legal_surcharge` text,
  `enroll_consent` tinyint(1) DEFAULT NULL,
  `legal` tinyint(1) DEFAULT NULL,
  `detail_request` tinyint(1) NOT NULL DEFAULT '0',
  `ohip` tinyint(1) NOT NULL DEFAULT '0',
  `ssn` tinyint(1) NOT NULL DEFAULT '0',
  `ipn` tinyint(1) NOT NULL DEFAULT '0',
  `fullfilment_timeframe_form` text,
  `bc_phn` tinyint(1) NOT NULL,
  `fullfilment_timeframe_email` text,
  `affiliated_provider` text,
  `min_consent_age` int(11) NOT NULL DEFAULT '0',
  `require_delegate_review` tinyint(1) DEFAULT NULL,
  `exam_type` varchar(255) DEFAULT NULL,
  `exam_site` varchar(255) DEFAULT NULL,
  `exam_site_mrn_prefix` text,
  `exam_date` tinyint(1) NOT NULL DEFAULT '0',
  `patient_address` tinyint(1) NOT NULL DEFAULT '0',
  `enrollment_optin` tinyint(1) NOT NULL DEFAULT '0',
  `delegate_auth_instructions` text NOT NULL,
  `survey_id` int(11) DEFAULT NULL,
  `show_login_link` tinyint(4) NOT NULL DEFAULT '0',
  `show_zoho_live_chat` tinyint(1) NOT NULL DEFAULT '0',
  `is_uph` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`organization_id`),
  CONSTRAINT `forms_ibfk_1` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
