CREATE TABLE IF NOT EXISTS `import_request` (
  `request_id` varchar(255) NOT NULL,
  `import_patient_id` int DEFAULT NULL,
  `import_source_id` varchar(255) NOT NULL,
  `pin` varchar(255) DEFAULT NULL,
  `order_json` text NOT NULL,
  `consented` timestamp NULL DEFAULT NULL,
  `image_uploaded` timestamp NULL DEFAULT NULL,
  `image_downloaded` timestamp NULL DEFAULT NULL,
  `report_uploaded` timestamp NULL DEFAULT NULL,
  `report_downloaded` timestamp NULL DEFAULT NULL,
  `request_completed` timestamp NULL DEFAULT NULL,
  `rejected` timestamp NULL DEFAULT NULL,
  `initialized` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `solicited` tinyint DEFAULT NULL,
  `skip_consent` tinyint DEFAULT '0',
  `reminder_expired` tinyint DEFAULT NULL,
  `last_reminder` timestamp NULL DEFAULT NULL,
  `nopriors` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
