CREATE TABLE IF NOT EXISTS `scans` (
  `scan_id` varchar(100) NOT NULL DEFAULT '',
  `origin_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `modality` varchar(10) DEFAULT NULL,
  `description` text,
  `institution` text,
  `patient_id` varchar(8) DEFAULT NULL,
  `patient_name` text,
  `uploaded` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `inactive` tinyint(1) DEFAULT '0',
  `expiry_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`scan_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_origin_id` (`origin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
