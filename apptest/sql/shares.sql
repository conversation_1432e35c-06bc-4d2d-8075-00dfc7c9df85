CREATE TABLE IF NOT EXISTS `shares` (
  `user_id` int(11) DEFAULT NULL,
  `account_id` char(28) DEFAULT NULL,
  `share_id` varchar(100) NOT NULL DEFAULT '',
  `scan_id` text,
  `recipient` varchar(255) DEFAULT NULL,
  `pin` varchar(255) DEFAULT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`share_id`),
  KEY `user_id` (`user_id`),
  KEY `account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
