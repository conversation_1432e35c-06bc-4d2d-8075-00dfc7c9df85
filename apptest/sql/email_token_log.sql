CREATE TABLE IF NOT EXISTS `email_token_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email_token` varchar(255) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_type` varchar(255) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `account_id` char(28) DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `email_token` (`email_token`) USING BTREE,
  KEY `user_id_idx` (`user_id`),
  KEY `account_id` (`account_id`),
  CONSTRAINT `user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=10184 DEFAULT CHARSET=latin1;

