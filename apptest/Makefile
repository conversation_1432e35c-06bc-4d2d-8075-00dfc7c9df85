.PHONY: setup_dev_deps apptest_dev clean apptest_local entry

PH_TEST_ENV ?= dev

entry:
	@if [ "$(PH_TEST_ENV)" = "dev" ]; then \
		$(MAKE) apptest_dev; \
	elif [ "$(PH_TEST_ENV)" = "dev-qa" ]; then \
		$(MAKE) apptest_qa_local; \
	else \
		$(MAKE) apptest; \
	fi;

# start set up local dependencies and run app tests.  dependencies will be
# torn down if everything successful.
apptest_dev: export PH_TEST_ENV=dev
apptest_dev:
	cd .. && $(MAKE) run_docker_dev_nt
	$(MAKE) apptest_local
# start up local container connected to QA services and dbs and run apptests
# torn down if everything successful.
apptest_qa_local: export PH_TEST_ENV=dev-qa
apptest_qa_local:
	cd .. && $(MAKE) run_docker_qa_nt
	$(MAKE) apptest_local

apptest_local:
	sleep 5
	mkdir -p tmp
	go test ./... -v -host=https://localhost -env=$(PH_TEST_ENV) -timeout="20m" || ./stop-dev-deps.sh


apptest:
	go env -w GOPRIVATE=gitlab.com/pockethealth/*
	go test ./... -v -host=https://$(shell if [ -z "$(API_HOST)" ]; then echo -n "core.qa.pocket.health"; else echo -n "$(API_HOST)"; fi) -env=$(PH_TEST_ENV)

sql/Dump.sql: sql/Dump.sql.zip
	unzip -o $< -d sql

# start and set up local dependencies (can use for local development)
setup_dev_deps: sql/Dump.sql
	mkdir -p tmp
	cp -r config.$(PH_TEST_ENV).sh tmp/config.sh
	./start-dev-deps.sh
	./setup-test-data.sh

# stop running containers and teardown local dependencies
kill_dev_deps:
	docker stop $$(docker ps | grep azure | cut -d " " -f1)
	$(MAKE) clean

# resets the state of app tests and tears down all local dependencies
clean:
	rm -rf tmp
	./stop-dev-deps.sh