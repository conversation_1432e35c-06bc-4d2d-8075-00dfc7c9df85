-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS fhir_patient_refresh_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_id CHAR(28) NOT NULL,
    org_id INT NOT NULL,
    mrn VARCHAR(255) NOT NULL,
    fhir_id VARCHAR(255) NOT NULL,
    token_type VARCHAR(255) NOT NULL,
    scope TEXT NOT NULL,
    access_token TEXT NOT NULL,
    access_expiry TIMESTAMP,
    refresh_token TEXT,
    refresh_expiry TIMESTAMP,
    token_endpoint_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_account_id (account_id),
    INDEX idx_org_id_patient_id_is_active (org_id, mrn, is_active),
    INDEX idx_fhir_id_is_active (fhir_id, is_active),
    INDEX idx_refresh_expiry_is_active (refresh_expiry, is_active),
    UNIQUE KEY idx_account_org_mrn_fhir (account_id, org_id, mrn, fhir_id)
);
-- +goose StatementEnd
-- +goose Down
-- +goose StatementBegin
DROP TABLE fhir_patient_refresh_tokens;
-- +goose StatementEnd