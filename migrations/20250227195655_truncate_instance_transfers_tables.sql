-- Stargate has been working on instance transfers for a while,
-- but we haven't shipped to production. With some recent product
-- changes, we need to make breaking changes to our database schemas.
-- Rather than trying to migrate a bunch of fake data around, we're
-- going to delete all the data and restart. It's much faster and
-- gives us a clean production database to start with.

-- +goose Up
DELETE FROM pockethealth.instance_transfer_logs;
DELETE FROM pockethealth.instance_transfer_destinations;
DELETE FROM pockethealth.instance_transfers;
DELETE FROM pockethealth.instance_uploads;
