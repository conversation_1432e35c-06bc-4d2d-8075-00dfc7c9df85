-- This SQL migration was generated by the generate_dicom_tag_tables script
-- +goose Up
-- +goose StatementBegin
ALTER TABLE `dicoms`
MODIFY COLUMN `id` VARBINARY(27),
<PERSON>ANGE COLUMN`StudyInstanceUID` `study_instance_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.study_instance_uid') STORED NOT NULL COMMENT "(0020,000d)",
CHANGE COLUMN`SeriesInstanceUID` `series_instance_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.series_instance_uid') STORED NOT NULL COMMENT "(0020,000e)",
CHANGE COLUMN`SOPInstanceUID` `sop_instance_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.sop_instance_uid') STORED NOT NULL COMMENT "(0008,0018)",
<PERSON><PERSON><PERSON> COLUMN`SOPClassUID` `sop_class_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.sop_class_uid') STORED NULL COMMENT "(0008,0016)",
CHANGE COLUMN`TransferSyntaxUID` `transfer_syntax_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.transfer_syntax_uid') STORED NULL COMMENT "(0002,0010)",
CHANGE COLUMN`IssuerOfPatientID` `issuer_of_patient_id` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.issuer_of_patient_id') STORED NULL COMMENT "(0010,0021)",
CHANGE COLUMN`PatientID` `patient_id` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.patient_id') STORED NULL COMMENT "(0010,0020)",
CHANGE COLUMN`PatientName` `patient_name` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.patient_name') STORED NULL COMMENT "(0010,0010)",
CHANGE COLUMN`PatientBirthDate` `patient_birth_date` CHAR(8) GENERATED ALWAYS AS (tags->>'$.patient_birth_date') STORED NULL COMMENT "(0010,0030)",
CHANGE COLUMN`PatientSex` `patient_sex` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.patient_sex') STORED NULL COMMENT "(0010,0040)",
CHANGE COLUMN`AccessionNumber` `accession_number` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.accession_number') STORED NULL COMMENT "(0008,0050)",
CHANGE COLUMN`StudyID` `study_id` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.study_id') STORED NULL COMMENT "(0020,0010)",
CHANGE COLUMN`StudyDescription` `study_description` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.study_description') STORED NULL COMMENT "(0008,1030)",
CHANGE COLUMN`StudyDate` `study_date` CHAR(8) GENERATED ALWAYS AS (tags->>'$.study_date') STORED NULL COMMENT "(0008,0020)",
CHANGE COLUMN`StudyTime` `study_time` TIME GENERATED ALWAYS AS (tags->>'$.study_time') STORED NULL COMMENT "(0008,0030)",
CHANGE COLUMN`SeriesDescription` `series_description` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.series_description') STORED NULL COMMENT "(0008,103e)",
CHANGE COLUMN`SeriesDate` `series_date` CHAR(8) GENERATED ALWAYS AS (tags->>'$.series_date') STORED NULL COMMENT "(0008,0021)",
CHANGE COLUMN`SeriesTime` `series_time` TIME GENERATED ALWAYS AS (tags->>'$.series_time') STORED NULL COMMENT "(0008,0031)",
CHANGE COLUMN`SeriesNumber` `series_number` INT GENERATED ALWAYS AS (tags->>'$.series_number') STORED NULL COMMENT "(0020,0011)",
CHANGE COLUMN`NumberOfStudyRelatedSeries` `number_of_study_related_series` INT GENERATED ALWAYS AS (tags->>'$.number_of_study_related_series') STORED NULL COMMENT "(0020,1206)",
CHANGE COLUMN`NumberOfStudyRelatedInstances` `number_of_study_related_instances` INT GENERATED ALWAYS AS (tags->>'$.number_of_study_related_instances') STORED NULL COMMENT "(0020,1208)",
CHANGE COLUMN`NumberOfSeriesRelatedInstances` `number_of_series_related_instances` INT GENERATED ALWAYS AS (tags->>'$.number_of_series_related_instances') STORED NULL COMMENT "(0020,1209)",
CHANGE COLUMN`Modality` `modality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.modality') STORED NULL COMMENT "(0008,0060)",
CHANGE COLUMN`ProtocolName` `protocol_name` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.protocol_name') STORED NULL COMMENT "(0018,1030)",
CHANGE COLUMN`InstanceNumber` `instance_number` INT GENERATED ALWAYS AS (tags->>'$.instance_number') STORED NULL COMMENT "(0020,0013)",
CHANGE COLUMN`ReferringPhysicianName` `referring_physician_name` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.referring_physician_name') STORED NULL COMMENT "(0008,0090)",
CHANGE COLUMN`PatientTelephoneNumbers` `patient_telephone_numbers` JSON GENERATED ALWAYS AS (tags->>'$.patient_telephone_numbers') STORED NULL COMMENT "(0010,2154)",
CHANGE COLUMN`BodyPartExamined` `body_part_examined` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.body_part_examined') STORED NULL COMMENT "(0018,0015)",
CHANGE COLUMN`Laterality` `laterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.laterality') STORED NULL COMMENT "(0020,0060)",
CHANGE COLUMN`ContentDate` `content_date` CHAR(8) GENERATED ALWAYS AS (tags->>'$.content_date') STORED NULL COMMENT "(0008,0023)",
CHANGE COLUMN`ContentTime` `content_time` TIME GENERATED ALWAYS AS (tags->>'$.content_time') STORED NULL COMMENT "(0008,0033)",
CHANGE COLUMN`AcquisitionDate` `acquisition_date` CHAR(8) GENERATED ALWAYS AS (tags->>'$.acquisition_date') STORED NULL COMMENT "(0008,0022)",
CHANGE COLUMN`AcquisitionTime` `acquisition_time` TIME GENERATED ALWAYS AS (tags->>'$.acquisition_time') STORED NULL COMMENT "(0008,0032)",
CHANGE COLUMN`ImageType` `image_type` JSON GENERATED ALWAYS AS (tags->>'$.image_type') STORED NULL COMMENT "(0008,0008)",
CHANGE COLUMN`ViewPosition` `view_position` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.view_position') STORED NULL COMMENT "(0018,5101)",
CHANGE COLUMN`NumberOfFrames` `number_of_frames` INT GENERATED ALWAYS AS (tags->>'$.number_of_frames') STORED NULL COMMENT "(0028,0008)",
CHANGE COLUMN`Rows` `rows` SMALLINT GENERATED ALWAYS AS (tags->>'$.rows') STORED NULL COMMENT "(0028,0010)",
CHANGE COLUMN`Columns` `columns` SMALLINT GENERATED ALWAYS AS (tags->>'$.columns') STORED NULL COMMENT "(0028,0011)",
CHANGE COLUMN`PixelSpacing` `pixel_spacing` JSON GENERATED ALWAYS AS (tags->>'$.pixel_spacing') STORED NULL COMMENT "(0028,0030)",
CHANGE COLUMN`ImageLaterality` `image_laterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.image_laterality') STORED NULL COMMENT "(0020,0062)",
CHANGE COLUMN`ImagePositionPatient` `image_position_patient` JSON GENERATED ALWAYS AS (tags->>'$.image_position_patient') STORED NULL COMMENT "(0020,0032)",
CHANGE COLUMN`ImageOrientationPatient` `image_orientation_patient` JSON GENERATED ALWAYS AS (tags->>'$.image_orientation_patient') STORED NULL COMMENT "(0020,0037)",
CHANGE COLUMN`FrameOfReferenceUID` `frame_of_reference_uid` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.frame_of_reference_uid') STORED NULL COMMENT "(0020,0052)",
CHANGE COLUMN`ConceptNameCodeSequence` `concept_name_code_sequence` TEXT GENERATED ALWAYS AS (tags->>'$.concept_name_code_sequence') STORED NULL COMMENT "(0040,a043)",
CHANGE COLUMN`CompletionFlag` `completion_flag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.completion_flag') STORED NULL COMMENT "(0040,a491)",
CHANGE COLUMN`VerificationFlag` `verification_flag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.verification_flag') STORED NULL COMMENT "(0040,a493)",
CHANGE COLUMN`InstitutionName` `institution_name` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.institution_name') STORED NULL COMMENT "(0008,0080)",
CHANGE COLUMN`Manufacturer` `manufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.manufacturer') STORED NULL COMMENT "(0008,0070)",
CHANGE COLUMN`SecondaryCaptureDeviceManufacturer` `secondary_capture_device_manufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.secondary_capture_device_manufacturer') STORED NULL COMMENT "(0018,1016)",
CHANGE COLUMN`SecondaryCaptureDeviceManufacturerModelName` `secondary_capture_device_manufacturer_model_name` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.secondary_capture_device_manufacturer_model_name') STORED NULL COMMENT "(0018,1018)";
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE `dicoms`
CHANGE COLUMN`study_instance_uid` `StudyInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.StudyInstanceUID') STORED NOT NULL COMMENT "(0020,000d)",
CHANGE COLUMN`series_instance_uid` `SeriesInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SeriesInstanceUID') STORED NOT NULL COMMENT "(0020,000e)",
CHANGE COLUMN`sop_instance_uid` `SOPInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SOPInstanceUID') STORED NOT NULL COMMENT "(0008,0018)",
CHANGE COLUMN`sop_class_uid` `SOPClassUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SOPClassUID') STORED NULL COMMENT "(0008,0016)",
CHANGE COLUMN`transfer_syntax_uid` `TransferSyntaxUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.TransferSyntaxUID') STORED NULL COMMENT "(0002,0010)",
CHANGE COLUMN`issuer_of_patient_id` `IssuerOfPatientID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.IssuerOfPatientID') STORED NULL COMMENT "(0010,0021)",
CHANGE COLUMN`patient_id` `PatientID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.PatientID') STORED NULL COMMENT "(0010,0020)",
CHANGE COLUMN`patient_name` `PatientName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.PatientName') STORED NULL COMMENT "(0010,0010)",
CHANGE COLUMN`patient_birth_date` `PatientBirthDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.PatientBirthDate') STORED NULL COMMENT "(0010,0030)",
CHANGE COLUMN`patient_sex` `PatientSex` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.PatientSex') STORED NULL COMMENT "(0010,0040)",
CHANGE COLUMN`accession_number` `AccessionNumber` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.AccessionNumber') STORED NULL COMMENT "(0008,0050)",
CHANGE COLUMN`study_id` `StudyID` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.StudyID') STORED NULL COMMENT "(0020,0010)",
CHANGE COLUMN`study_description` `StudyDescription` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.StudyDescription') STORED NULL COMMENT "(0008,1030)",
CHANGE COLUMN`study_date` `StudyDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.StudyDate') STORED NULL COMMENT "(0008,0020)",
CHANGE COLUMN`study_time` `StudyTime` TIME GENERATED ALWAYS AS (tags->>'$.StudyTime') STORED NULL COMMENT "(0008,0030)",
CHANGE COLUMN`series_description` `SeriesDescription` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SeriesDescription') STORED NULL COMMENT "(0008,103e)",
CHANGE COLUMN`series_date` `SeriesDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.SeriesDate') STORED NULL COMMENT "(0008,0021)",
CHANGE COLUMN`series_time` `SeriesTime` TIME GENERATED ALWAYS AS (tags->>'$.SeriesTime') STORED NULL COMMENT "(0008,0031)",
CHANGE COLUMN`series_number` `SeriesNumber` INT GENERATED ALWAYS AS (tags->>'$.SeriesNumber') STORED NULL COMMENT "(0020,0011)",
CHANGE COLUMN`number_of_study_related_series` `NumberOfStudyRelatedSeries` INT GENERATED ALWAYS AS (tags->>'$.NumberOfStudyRelatedSeries') STORED NULL COMMENT "(0020,1206)",
CHANGE COLUMN`number_of_study_related_instances` `NumberOfStudyRelatedInstances` INT GENERATED ALWAYS AS (tags->>'$.NumberOfStudyRelatedInstances') STORED NULL COMMENT "(0020,1208)",
CHANGE COLUMN`number_of_series_related_instances` `NumberOfSeriesRelatedInstances` INT GENERATED ALWAYS AS (tags->>'$.NumberOfSeriesRelatedInstances') STORED NULL COMMENT "(0020,1209)",
CHANGE COLUMN`modality` `Modality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.Modality') STORED NULL COMMENT "(0008,0060)",
CHANGE COLUMN`protocol_name` `ProtocolName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.ProtocolName') STORED NULL COMMENT "(0018,1030)",
CHANGE COLUMN`instance_number` `InstanceNumber` INT GENERATED ALWAYS AS (tags->>'$.InstanceNumber') STORED NULL COMMENT "(0020,0013)",
CHANGE COLUMN`referring_physician_name` `ReferringPhysicianName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.ReferringPhysicianName') STORED NULL COMMENT "(0008,0090)",
CHANGE COLUMN`patient_telephone_numbers` `PatientTelephoneNumbers` JSON GENERATED ALWAYS AS (tags->>'$.PatientTelephoneNumbers') STORED NULL COMMENT "(0010,2154)",
CHANGE COLUMN`body_part_examined` `BodyPartExamined` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.BodyPartExamined') STORED NULL COMMENT "(0018,0015)",
CHANGE COLUMN`laterality` `Laterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.Laterality') STORED NULL COMMENT "(0020,0060)",
CHANGE COLUMN`content_date` `ContentDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.ContentDate') STORED NULL COMMENT "(0008,0023)",
CHANGE COLUMN`content_time` `ContentTime` TIME GENERATED ALWAYS AS (tags->>'$.ContentTime') STORED NULL COMMENT "(0008,0033)",
CHANGE COLUMN`acquisition_date` `AcquisitionDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.AcquisitionDate') STORED NULL COMMENT "(0008,0022)",
CHANGE COLUMN`acquisition_time` `AcquisitionTime` TIME GENERATED ALWAYS AS (tags->>'$.AcquisitionTime') STORED NULL COMMENT "(0008,0032)",
CHANGE COLUMN`image_type` `ImageType` JSON GENERATED ALWAYS AS (tags->>'$.ImageType') STORED NULL COMMENT "(0008,0008)",
CHANGE COLUMN`view_position` `ViewPosition` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.ViewPosition') STORED NULL COMMENT "(0018,5101)",
CHANGE COLUMN`number_of_frames` `NumberOfFrames` INT GENERATED ALWAYS AS (tags->>'$.NumberOfFrames') STORED NULL COMMENT "(0028,0008)",
CHANGE COLUMN`rows` `Rows` SMALLINT GENERATED ALWAYS AS (tags->>'$.Rows') STORED NULL COMMENT "(0028,0010)",
CHANGE COLUMN`columns` `Columns` SMALLINT GENERATED ALWAYS AS (tags->>'$.Columns') STORED NULL COMMENT "(0028,0011)",
CHANGE COLUMN`pixel_spacing` `PixelSpacing` JSON GENERATED ALWAYS AS (tags->>'$.PixelSpacing') STORED NULL COMMENT "(0028,0030)",
CHANGE COLUMN`image_laterality` `ImageLaterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.ImageLaterality') STORED NULL COMMENT "(0020,0062)",
CHANGE COLUMN`image_position_patient` `ImagePositionPatient` JSON GENERATED ALWAYS AS (tags->>'$.ImagePositionPatient') STORED NULL COMMENT "(0020,0032)",
CHANGE COLUMN`image_orientation_patient` `ImageOrientationPatient` JSON GENERATED ALWAYS AS (tags->>'$.ImageOrientationPatient') STORED NULL COMMENT "(0020,0037)",
CHANGE COLUMN`frame_of_reference_uid` `FrameOfReferenceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.FrameOfReferenceUID') STORED NULL COMMENT "(0020,0052)",
CHANGE COLUMN`concept_name_code_sequence` `ConceptNameCodeSequence` TEXT GENERATED ALWAYS AS (tags->>'$.ConceptNameCodeSequence') STORED NULL COMMENT "(0040,a043)",
CHANGE COLUMN`completion_flag` `CompletionFlag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.CompletionFlag') STORED NULL COMMENT "(0040,a491)",
CHANGE COLUMN`verification_flag` `VerificationFlag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.VerificationFlag') STORED NULL COMMENT "(0040,a493)",
CHANGE COLUMN`institution_name` `InstitutionName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.InstitutionName') STORED NULL COMMENT "(0008,0080)",
CHANGE COLUMN`manufacturer` `Manufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.Manufacturer') STORED NULL COMMENT "(0008,0070)",
CHANGE COLUMN`secondary_capture_device_manufacturer` `SecondaryCaptureDeviceManufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SecondaryCaptureDeviceManufacturer') STORED NULL COMMENT "(0018,1016)",
CHANGE COLUMN`secondary_capture_device_manufacturer_model_name` `SecondaryCaptureDeviceManufacturerModelName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SecondaryCaptureDeviceManufacturerModelName') STORED NULL COMMENT "(0018,1018)";
-- +goose StatementEnd