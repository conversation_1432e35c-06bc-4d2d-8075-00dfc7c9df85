-- +goose Up
-- +goose StatementBegin
ALTER TABLE user_notifications
ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
-- +goose StatementEnd

-- Explicitly ignore the default value for existing rows, as we want to understand at an immediate glance which rows
-- -- were created prior to this statement
-- +goose StatementBegin
UPDATE user_notifications
SET updated_at = NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE user_notifcations
DROP COLUMN updated_at;
-- +goose StatementEnd
