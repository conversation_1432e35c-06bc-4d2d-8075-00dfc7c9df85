-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `host_system_snapshot` (
	id bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
	host_id bigint NOT NULL, 
	num_cores int,
	free_mem_kb float,
	total_mem_kb float,
	computer_name text,
	cpu_percentage float,
	gateway_account text,
	num_logical_processors int,
	num_physical_processors int,
	timestamp timestamp NOT NULL DEFAULT (UTC_TIMESTAMP)

) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `host_system_process_snapshot` (
	id bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
	system_snapshot_id bigint NOT NULL,
	p_id int,
	name text,
	mem_kb float,
	version text,
	file_path text,
	file_size float,
	handle_count int,
	thread_count int,
	cpu_percentage float,
	last_write_time_utc text,
	total_folder_bytes float,
	total_folder_files int,
	timestamp timestamp NOT NULL DEFAULT (UTC_TIMESTAMP)

) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `host_system_snapshot`;
-- +goose StatementEnd
-- +goose StatementBegin
DROP TABLE IF EXISTS `host_system_process_snapshot`;
-- +goose StatementEnd
