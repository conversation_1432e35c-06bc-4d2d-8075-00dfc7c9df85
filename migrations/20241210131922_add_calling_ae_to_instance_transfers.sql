-- +goose Up
-- Default to empty string to set NULL values to empty string (there is no case where either a null value or empty string would be normally allowed by the dicom tag specification)
ALTER TABLE `instance_transfers` CHANGE COLUMN `application_entity_title` `called_ae` VARCHAR(255) NOT NULL DEFAULT '';
ALTER TABLE `instance_transfers` ADD COLUMN `calling_ae` VARCHAR(255) NOT NULL DEFAULT '';
-- Then remove the default to prevent NULL values from being inserted
ALTER TABLE `instance_transfers` ALTER COLUMN `called_ae` DROP DEFAULT;
ALTER TABLE `instance_transfers` ALTER COLUMN `calling_ae` DROP DEFAULT;
ALTER TABLE `instance_transfer_destinations` DROP COLUMN `application_entity_title`;

-- +goose Down
ALTER TABLE `instance_transfer_destinations` ADD COLUMN `application_entity_title` TEXT NULL DEFAULT NULL;
ALTER TABLE `instance_transfers` DROP COLUMN `calling_ae`;
ALTER TABLE `instance_transfers` <PERSON>ANGE COLUMN `called_ae` `application_entity_title` TEXT NULL DEFAULT NULL;
