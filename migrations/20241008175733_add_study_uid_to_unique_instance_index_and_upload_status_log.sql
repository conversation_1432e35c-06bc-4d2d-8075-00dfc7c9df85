-- +goose Up

-- Add study_uid column only if it does not exist
-- +goose StatementBegin
SELECT COUNT(*)
    INTO @exist
    FROM information_schema.columns
    WHERE table_schema = 'pockethealth'
    AND table_name = 'unique_instance_index'
    AND column_name = 'study_uid'
    LIMIT 1;
-- +goose StatementEnd
-- +goose StatementBegin
SET @query = IF(@exist <= 0, 'ALTER TABLE pockethealth.`unique_instance_index` ADD COLUMN `study_uid` varchar(255)',
    'select \'Column exists\' status');
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE stmt from @query;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE stmt;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE stmt;
-- +goose StatementEnd

-- Add index on provider_id, study_uid, and instance_uid only if it does not exist
-- +goose StatementBegin
SELECT COUNT(*)
	INTO @exist
    FROM information_schema.statistics
    WHERE table_schema = 'pockethealth'
    AND table_name = 'unique_instance_index'
    AND index_name = 'idx_provider_study_instance'
    LIMIT 1;
-- +goose StatementEnd
-- +goose StatementBegin
SET @query = IF(@exist <= 0, 'ALTER TABLE pockethealth.`unique_instance_index` ADD UNIQUE INDEX `idx_provider_study_instance` (`provider_id`, `study_uid`, `instance_uid`), ALGORITHM=INPLACE, LOCK=NONE',
    'select \'Index exists\' status');
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE stmt from @query;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE stmt;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE stmt;
-- +goose StatementEnd

-- Add study_uid column only if it does not exist
-- +goose StatementBegin
SELECT COUNT(*)
    INTO @exist
    FROM information_schema.columns
    WHERE table_schema = 'pockethealth'
    AND table_name = 'upload_status_log'
    AND column_name = 'study_uid'
    LIMIT 1;
-- +goose StatementEnd
-- +goose StatementBegin
SET @query = IF(@exist <= 0, 'ALTER TABLE pockethealth.`upload_status_log` ADD COLUMN `study_uid` varchar(255)',
    'select \'Column Exists\' status');
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE stmt from @query;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE stmt;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE stmt;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE pockethealth.unique_instance_index
DROP COLUMN `study_uid`,
DROP INDEX `idx_provider_study_instance`;
-- +goose StatementEnd

-- +goose StatementBegin
ALTER TABLE pockethealth.upload_status_log DROP COLUMN `study_uid`;
-- +goose StatementEnd
