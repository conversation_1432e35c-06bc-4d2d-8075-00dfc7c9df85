-- +goose Up
-- Update deleted_at to use timestamp instead of datetime
ALTER TABLE `instance_transfers` MODIFY `deleted_at` TIMESTAMP(6) DEFAULT NULL;
-- Index for deleted_at to improve data cleanup efficiency
CREATE INDEX `idx_deleted_at_id` ON `instance_transfers` (`deleted_at`, `id`);

-- +goose Down
ALTER TABLE `instance_transfers` DROP INDEX `idx_deleted_at_id`;
ALTER TABLE `instance_transfers` MODIFY `deleted_at` DATETIME DEFAULT NULL;
