-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `study_instance_upload_status` (
    `study_uid` VARCHAR(64) NOT NULL,
    `legacy_provider_id` BIGINT NOT NULL, 
    `instance_count` INT NOT NULL,
    `instances_processed` INT DEFAULT 0,
    `instances_uploaded` INT DEFAULT 0,
    `created_at_utc` DATETIME NOT NULL DEFAULT (UTC_TIMESTAMP) COMMENT "Using UTC_TIMESTAMP instead of CURRENT_TIMESTAMP until all servers use UTC timezone", 
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "Only CURRENT_TIMESTAMP can be used for auto-update timestamps",
    UNIQUE (`legacy_provider_id`, `study_uid`) 
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `study_instance_upload_status`;
-- +goose StatementEnd
