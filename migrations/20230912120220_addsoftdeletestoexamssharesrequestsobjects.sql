-- insertion statement from: https://stackoverflow.com/a/31989541

-- +goose Up
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'exams')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'is_deleted')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.exams ADD is_deleted TINYINT(1) NOT NULL DEFAULT FALSE;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'exams')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'deleted_at')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.exams ADD deleted_at TIMESTAMP;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'shares')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'is_deleted')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.shares ADD is_deleted TINYINT(1) NOT NULL DEFAULT FALSE;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'shares')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'deleted_at')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.shares ADD deleted_at TIMESTAMP;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'requests')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'is_deleted')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.requests ADD is_deleted TINYINT(1) NOT NULL DEFAULT FALSE;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'requests')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'deleted_at')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.requests ADD deleted_at TIMESTAMP;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'objects')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'is_deleted')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.objects ADD is_deleted TINYINT(1);"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (table_name = 'objects')
      AND (table_schema = 'pockethealth')
      AND (column_name = 'deleted_at')
  ) > 0,
  "SELECT 1",
  "ALTER TABLE pockethealth.objects ADD deleted_at TIMESTAMP;"
));
-- +goose StatementEnd
-- +goose StatementBegin
PREPARE alterIfNotExists FROM @preparedStatement;
-- +goose StatementEnd
-- +goose StatementBegin
EXECUTE alterIfNotExists;
-- +goose StatementEnd
-- +goose StatementBegin
DEALLOCATE PREPARE alterIfNotExists;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE pockethealth.exams DROP COLUMN is_deleted
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.exams DROP COLUMN deleted_at
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.shares DROP COLUMN is_deleted
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.shares DROP COLUMN deleted_at
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.requests DROP COLUMN is_deleted
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.requests DROP COLUMN deleted_at
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.objects DROP COLUMN is_deleted
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.objects DROP COLUMN deleted_at
-- +goose StatementEnd