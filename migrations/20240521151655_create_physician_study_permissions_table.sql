-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `physician_study_permissions` (
    physician_account_id char(28) NOT NULL,
    study_uid VARCHAR(255) NOT NULL,
    provider_id VARCHAR(255) NOT NULL, 
    created_timestamp_utc DATETIME NOT NULL DEFAULT (UTC_TIMESTAMP) COMMENT "Using UTC_TIMESTAMP instead of CURRENT_TIMESTAMP until all servers use UTC timezone", 
    updated_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "Only CURRENT_TIMESTAMP can be used for auto-update timestamps", 
    INDEX idx_provider (provider_id, study_uid),
    INDEX idx_physician (physician_account_id),
    UNIQUE (`physician_account_id`, `provider_id`, `study_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `physician_study_permissions`;
-- +goose StatementEnd
