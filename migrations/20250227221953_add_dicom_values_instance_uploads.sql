-- +goose Up
-- +goose StatementBegin
ALTER TABLE instance_uploads
  ADD COLUMN accession_number VARCHAR(16) GENERATED ALWAYS AS (dicom_dataset->>'$.accession_number') STORED NULL COMMENT "(0008,0050)",
  ADD COLUMN issuer_of_patient_id VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.issuer_of_patient_id') STORED NULL COMMENT "(0010,0021)",
  ADD COLUMN patient_birth_date CHAR(8) GENERATED ALWAYS AS (dicom_dataset->>'$.patient_birth_date') STORED NULL COMMENT "(0010,0030)",
  ADD COLUMN patient_id VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.patient_id') STORED NULL COMMENT "(0010,0020)",
  ADD COLUMN series_instance_uid VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.series_instance_uid') STORED NOT NULL COMMENT "(0020,000e)",
  ADD COLUMN series_number INT GENERATED ALWAYS AS (dicom_dataset->>'$.series_number') STORED NULL COMMENT "(0020,0011)",
  ADD COLUMN sop_class_uid VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.sop_class_uid') STORED NULL COMMENT "(0008,0016)",
  ADD COLUMN sop_instance_uid VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.sop_instance_uid') STORED NOT NULL COMMENT "(0008,0018)",
  ADD COLUMN study_date CHAR(8) GENERATED ALWAYS AS (dicom_dataset->>'$.study_date') STORED NULL COMMENT "(0008,0020)",
  ADD COLUMN study_description VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.study_description') STORED NULL COMMENT "(0008,1030)",
  ADD COLUMN study_instance_uid VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.study_instance_uid') STORED NOT NULL COMMENT "(0020,000d)",
  ADD COLUMN study_time TIME GENERATED ALWAYS AS (dicom_dataset->>'$.study_time') STORED NULL COMMENT "(0008,0030)",
  ADD COLUMN transfer_syntax_uid VARCHAR(64) GENERATED ALWAYS AS (dicom_dataset->>'$.transfer_syntax_uid') STORED NULL COMMENT "(0002,0010)";
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE instance_uploads
  DROP COLUMN accession_number,
  DROP COLUMN issuer_of_patient_id,
  DROP COLUMN patient_birth_date,
  DROP COLUMN patient_id,
  DROP COLUMN series_instance_uid,
  DROP COLUMN series_number,
  DROP COLUMN sop_class_uid,
  DROP COLUMN sop_instance_uid,
  DROP COLUMN study_date,
  DROP COLUMN study_description,
  DROP COLUMN study_instance_uid,
  DROP COLUMN study_time,
  DROP COLUMN transfer_syntax_uid;
-- +goose StatementEnd
