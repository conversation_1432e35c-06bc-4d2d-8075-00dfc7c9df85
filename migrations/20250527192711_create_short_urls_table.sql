-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `short_urls` (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    slug VARCHAR(10) CHARACTER SET ascii COLLATE ascii_bin NOT NULL,
    original_url TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE INDEX uniq_slug (slug)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS short_urls;
-- +goose StatementEnd