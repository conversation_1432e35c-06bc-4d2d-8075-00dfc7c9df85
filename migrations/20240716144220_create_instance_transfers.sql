-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `instance_transfers` (
    `id` VARBINARY(27) NOT NULL UNIQUE PRIMARY KEY,
    `organization_id` BIGINT DEFAULT NULL,
    `gateway_user_id` INT default NULL,
    `study_instance_uid` VARCHAR(64) NOT NULL,
    `series_instance_uid` VARCHAR(64) NOT NULL,
    `sop_instance_uid` VARCHAR(64) NOT NULL,
    `dicom_tags` JSON NOT NULL,
    `instance_upload_id` VARBINARY(27) NOT NULL,
    CONSTRAINT `fk_instance_upload_id` FOREIGN KEY (`instance_upload_id`) REFERENCES `instance_uploads`(`id`),
    `deleted_at` DATETIME DEFAULT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `instance_transfers`;
-- +goose StatementEnd
