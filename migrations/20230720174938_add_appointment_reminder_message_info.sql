-- +goose Up
-- +goose StatementBegin
ALTER TABLE `pockethealth`.`appointment_reminders` 
DROP COLUMN `message_status`,
DROP COLUMN `reminder_sent`,
ADD COLUMN `cio_message_id` VARCHAR(255) NULL DEFAULT NULL,
ADD COLUMN `message_sent` TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN `message_delivered` TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN `message_failed` TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN `failure_message` VARCHAR(500) NULL DEFAULT NULL,
ADD COLUMN `failure_type` VARCHAR(255) NULL DEFAULT NULL,
ADD COLUMN `patient_status` VARCHAR(255) NULL DEFAULT NULL,
ADD COLUMN `patient_timestamp` TIMESTAMP NULL DEFAULT NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE `pockethealth`.`appointment_reminders` 
ADD COLUMN `message_status` varchar(255) DEFAULT NULL,
ADD COLUMN `reminder_sent` timestamp NULL DEFAULT NULL,
DROP COLUMN `cio_message_id` TIMESTAMP NULL,
DROP COLUMN `message_sent` TIMESTAMP NULL,
DROP COLUMN `message_delivered` TIMESTAMP NULL,
DROP COLUMN `message_failed` TIMESTAMP NULL,
DROP COLUMN `failure_message` VARCHAR(500) NULL,
DROP COLUMN `failure_type` VARCHAR(255) NULL,
DROP COLUMN `patient_status` VARCHAR(255) NULL,
DROP COLUMN `patient_timestamp` TIMESTAMP NULL;
-- +goose StatementEnd