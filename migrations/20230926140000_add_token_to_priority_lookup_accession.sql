-- +goose Up
ALTER TABLE `pockethealth`.`priority_lookup_accession` ADD COLUMN `token` VARCHAR(100) UNIQUE FIRST;
ALTER TABLE `pockethealth`.`priority_lookup_accession`
ADD CONSTRAINT `sso_token_ibfk_1`
FOREI<PERSON>N KEY (`token`) REFERENCES `pockethealth`.`sso_tokens` (`token`)
ON DELETE CASCADE;

-- +goose Down
ALTER TABLE `pockethealth`.`priority_lookup_accession` DROP FOREIGN KEY `sso_token_ibfk_1`;
ALTER TABLE `pockethealth`.`priority_lookup_accession` DROP COLUMN `token`;
