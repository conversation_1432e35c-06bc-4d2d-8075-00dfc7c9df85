-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `appointment_reminder_follow_ups` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`reminder_id` varchar(255) NOT NULL,
	`cio_message_id` varchar(255) DEFAULT NULL,
	`cio_temp_account_id` varchar(255) DEFAULT NULL,
	`message_sent` TIMESTAMP NULL DEFAULT NULL,
	`message_delivered` TIMESTAMP NULL DEFAULT NULL,
	`message_failed` TIMESTAMP NULL DEFAULT NULL,
	`failure_message` VARCHAR(500) NULL DEFAULT NULL,
	`failure_type` VARCHAR(255) NULL DEFAULT NULL,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS appointment_reminder_follow_ups;
-- +goose StatementEnd
