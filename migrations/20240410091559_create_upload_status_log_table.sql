-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `upload_status_log` (
    provider_id BIGINT(20) NOT NULL,
    dicom_level VARCHAR(255) NOT NULL, 
    entity_id VARCHAR(255) NOT NULL, 
    details VARCHAR(255) NOT NULL,
    created_timestamp_utc DATETIME NOT NULL DEFAULT (UTC_TIMESTAMP) COMMENT "Using UTC_TIMESTAMP instead of CURRENT_TIMESTAMP until all servers use UTC timezone",
    INDEX idx_entity (provider_id, dicom_level, entity_id)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `upload_status_log`
-- +goose StatementEnd
