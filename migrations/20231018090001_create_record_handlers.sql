-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `record_handlers`(
	`id` char(28) NOT NULL,
	`handler_type` varchar(20) NOT NULL,
	`handler_type_id` varchar(100) NOT NULL,
	`upload` tinyint(1) NOT NULL DEFAULT '0',
	`own` tinyint(1) NOT NULL DEFAULT '0',
	`share` tinyint(1) NOT NULL DEFAULT '0',
	`view` tinyint(1) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`),
	UNIQUE (`handler_type`, `handler_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `record_handlers`;
-- +goose StatementEnd
