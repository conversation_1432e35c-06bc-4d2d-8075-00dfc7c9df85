-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `record_handlers_share_map`(
	`id` int NOT NULL AUTO_INCREMENT,
	`handler_id` varchar(28) NOT NULL,
	`share_id` varchar(100) NOT NULL,
	`is_deleted` tinyint(1) NOT NULL DEFAULT '0',
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`),
  <PERSON>EY `idx_handler_id` (`handler_id`),
  KEY `idx_share_id` (`share_id`),
  UNIQUE (`handler_id`, `share_id`),
  CONSTRAINT `record_handlers_id_ibfk_1` FOREIGN KEY (`handler_id`) REFERENCES `record_handlers`(`id`) ON DELETE CASCADE,
  CONSTRAINT `shares_share_id_ibfk_2` F<PERSON><PERSON><PERSON><PERSON> KEY (`share_id`) REFERENCES `shares`(`share_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `record_handlers_share_map`;
-- +goose StatementEnd
