-- +goose Up
ALTER TABLE requests ADD COLUMN account_id char(28), ADD COLUMN  patient_id char(28);
CREATE INDEX acct_idx ON requests (account_id);
CREATE INDEX pt_idx ON requests (patient_id);
ALTER TABLE enrollments ADD COLUMN account_id char(28), ADD COLUMN  patient_id char(28);
CREATE INDEX acct_idx ON enrollments (account_id);
CREATE INDEX pt_idx ON enrollments (patient_id);
ALTER TABLE consents ADD COLUMN account_id char(28), ADD COLUMN  patient_id char(28);
CREATE INDEX acct_idx ON consents (account_id);
CREATE INDEX pt_idx ON consents (patient_id);
ALTER TABLE exams ADD COLUMN account_id char(28), ADD COLUMN  patient_id char(28);
CREATE INDEX acct_idx ON exams (account_id);
CREATE INDEX pt_idx ON exams (patient_id);
ALTER TABLE scans modify column expiry_start TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP; 
ALTER TABLE scans ADD COLUMN account_id char(28), ADD COLUMN  ph_patient_id char(28);
CREATE INDEX acct_idx ON scans (account_id);
CREATE INDEX pt_idx ON scans (ph_patient_id);

-- +goose Down
ALTER TABLE requests DROP COLUMN account_id, DROP COLUMN patient_id ;
ALTER TABLE enrollments DROP COLUMN account_id, DROP COLUMN patient_id ;
ALTER TABLE consents DROP COLUMN account_id, DROP COLUMN patient_id ;
ALTER TABLE exams DROP COLUMN account_id, DROP COLUMN patient_id ;
ALTER TABLE scans DROP COLUMN account_id, DROP COLUMN ph_patient_id ;