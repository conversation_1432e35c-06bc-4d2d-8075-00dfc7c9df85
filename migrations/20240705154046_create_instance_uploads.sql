-- +goose Up
CREATE TABLE IF NOT EXISTS `instance_uploads` (
    `id` VARBINARY(27) NOT NULL UNIQUE PRIMARY KEY,
    `provider_id` VARBINARY(27) DEFAULT NULL,
    `gateway_id` BIGINT DEFAULT NULL,
    `file_name` TEXT NOT NULL,
    `file_size_bytes` BIGINT UNSIGNED NOT NULL,
    `checksum_sha256` VARBINARY(64) NOT NULL,
    `blob_container` VARCHAR(64) NOT NULL,
    `blob_name` VARCHAR(1024) NOT NULL,
    `status` ENUM('created', 'uploaded', 'linked', 'deleted') DEFAULT 'created',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE INDEX `idx_created_at_status` ON `instance_uploads`(
    `created_at`,
    `status`,
    `id`,
    `blob_container`,
    `blob_name`
);

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS instance_uploads;
-- +goose StatementEnd
