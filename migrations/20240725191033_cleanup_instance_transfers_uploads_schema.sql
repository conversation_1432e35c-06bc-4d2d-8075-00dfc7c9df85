-- +goose Up
-- Make a few columns not null
-- Remove provider_id column
-- Remove "uploaded" from the status enum
-- Make checksum char
-- Make created/updated times timestamp
-- Increase timestamp precision

-- Instance Transfers:
ALTER TABLE `instance_transfers` MODIFY `organization_id` BIGINT NOT NULL;
ALTER TABLE `instance_transfers` MODIFY `gateway_user_id` INT NOT NULL;
ALTER TABLE `instance_transfers` MODIFY `created_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6);
ALTER TABLE `instance_transfers` MODIFY `updated_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6);
-- Instance Uploads:
ALTER TABLE `instance_uploads` MODIFY `organization_id` BIGINT NOT NULL;
ALTER TABLE `instance_uploads` MODIFY `gateway_user_id` INT NOT NULL;
ALTER TABLE `instance_uploads` MODIFY `status` enum('created','linked','deleted') NOT NULL DEFAULT 'created';
ALTER TABLE `instance_uploads` DROP COLUMN `provider_id`;
ALTER TABLE `instance_uploads` MODIFY `checksum_sha256` CHAR(64) NOT NULL;
ALTER TABLE `instance_uploads` MODIFY `created_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6);
ALTER TABLE `instance_uploads` MODIFY `updated_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6);
-- Instance Transfer Destinations:
ALTER TABLE `instance_transfer_destinations` MODIFY `organization_id` BIGINT NOT NULL;
ALTER TABLE `instance_transfer_destinations` MODIFY `gateway_user_id` INT NOT NULL;
ALTER TABLE `instance_transfer_destinations` MODIFY `created_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6);
ALTER TABLE `instance_transfer_destinations` MODIFY `updated_at` TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6);

-- +goose Down
-- Instance Transfer Destinations:
ALTER TABLE `instance_transfer_destinations` MODIFY `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE `instance_transfer_destinations` MODIFY `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `instance_transfer_destinations` MODIFY `gateway_user_id` INT DEFAULT NULL;
ALTER TABLE `instance_transfer_destinations` MODIFY `organization_id` BIGINT DEFAULT NULL;
-- Instance Uploads:
ALTER TABLE `instance_uploads` MODIFY `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE `instance_uploads` MODIFY `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `instance_uploads` MODIFY `checksum_sha256` VARBINARY(64) NOT NULL;
ALTER TABLE `instance_uploads` ADD COLUMN `provider_id` VARBINARY(27) DEFAULT NULL;
ALTER TABLE `instance_uploads` MODIFY `status` enum('created','uploaded','linked','deleted') DEFAULT 'created';
ALTER TABLE `instance_uploads` MODIFY `gateway_user_id` INT DEFAULT NULL;
ALTER TABLE `instance_uploads` MODIFY `organization_id` BIGINT DEFAULT NULL;
-- Instance Transfers:
ALTER TABLE `instance_transfers` MODIFY `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE `instance_transfers` MODIFY `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `instance_transfers` MODIFY `gateway_user_id` INT DEFAULT NULL;
ALTER TABLE `instance_transfers` MODIFY `organization_id` BIGINT DEFAULT NULL;