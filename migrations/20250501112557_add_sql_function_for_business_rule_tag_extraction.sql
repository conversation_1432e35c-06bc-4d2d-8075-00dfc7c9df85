-- +goose Up
-- +goose StatementBegin
DROP FUNCTION IF EXISTS json_extract_case_insensitive;
-- +goose StatementEnd
-- +goose StatementBegin
CREATE FUNCTION json_extract_case_insensitive(json_doc JSON, path VARCHAR(255))
R<PERSON><PERSON>NS TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci
DETERMINISTIC
READS SQL DATA
BEGIN
  RETURN JSON_UNQUOTE(COALESCE(JSON_EXTRACT(json_doc, path), ''));
END;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP FUNCTION IF EXISTS json_extract_case_insensitive;
-- +goose StatementEnd