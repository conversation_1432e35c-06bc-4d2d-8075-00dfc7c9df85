-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `instance_transfer_destinations` (
    `id` VARBINARY(27) NOT NULL UNIQUE PRIMARY KEY,
    `organization_id` BIGINT DEFAULT NULL,
    `gateway_user_id` INT default NULL,
    `aet` TEXT NOT NULL,
    `instance_transfer_id` VARBINARY(27) NOT NULL,
    CONSTRAINT `fk_instance_transfer_id` FOREIGN KEY (`instance_transfer_id`) REFERENCES `instance_transfers`(`id`),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `instance_transfer_destinations`;
-- +goose StatementEnd
