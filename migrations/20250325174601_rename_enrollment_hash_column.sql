-- +goose Up
-- +goose StatementBegin
ALTER TABLE pockethealth.enrollments
  DROP INDEX deduplication_hash_index,
  DROP COLUMN deduplication_hash,
  ADD COLUMN lookup_hash CHAR(64) NULL COMMENT 'SHA-256 hash to lookup duplicate enrolments',
  ADD INDEX lookup_hash_index (lookup_hash);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE pockethealth.enrollments
DROP INDEX lookup_hash_index,
DROP COLUMN lookup_hash,
ADD COLUMN deduplication_hash CHAR(64) NULL COMMENT 'SHA-256 hash to lookup duplicate enrolments',
ADD INDEX deduplication_hash_index (deduplication_hash);
-- +goose StatementEnd
