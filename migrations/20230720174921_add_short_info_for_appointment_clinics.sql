-- +goose Up
-- +goose StatementBegin
ALTER TABLE pockethealth.appointment_clinic_info
ADD COLUMN `short_name` varchar(255) DEFAULT NULL,
ADD COLUMN `short_address` varchar(255) DEFAULT NULL,

DROP INDEX `unique_clinic_info` ,
ADD UNIQUE INDEX `unique_clinic_info` (`name` ASC, `address` ASC, `address_details` ASC, `phone` ASC, `email` ASC, `url` ASC, `short_name` ASC, `short_address` ASC);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE pockethealth.appointment_clinic_info
DROP COLUMN `short_name` varchar(255) DEFAULT NULL,
DROP COLUMN `short_address` varchar(255) DEFAULT NULL,

-- id is needed in clinic index to avoid issues with duplicate entries
DROP INDEX `unique_clinic_info` ,
ADD UNIQUE INDEX `unique_clinic_info` (`id` ASC, `name` ASC, `address` ASC, `address_details` AS<PERSON>, `phone` ASC, `email` ASC, `url` ASC);
-- +goose StatementEnd