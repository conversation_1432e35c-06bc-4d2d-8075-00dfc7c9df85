-- +goose Up
ALTER TABLE enrollments 
ADD COLUMN application_entity_id BIGINT NULL COMMENT 'Application Entity ID, DICOM namespace identifier',
ALGORITHM=INSTANT;

ALTER TABLE unique_study_index 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

ALTER TABLE unique_report_index 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

ALTER TABLE unique_instance_index 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALG<PERSON><PERSON>HM=INSTANT;

ALTER TABLE study_instance_upload_status 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

ALTER TABLE dicoms 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

ALTER TABLE patient_account_study_permissions 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

ALTER TABLE physician_study_permissions 
ADD COLUMN source_type VARCHAR(255) NULL COMMENT 'DICOM namespace source type',
ADD COLUMN source_id VARCHAR(255) NULL COMMENT 'DICOM namespace source identifier',
ALGORITHM=INSTANT;

-- +goose Down
ALTER TABLE physician_study_permissions DROP COLUMN source_type, DROP COLUMN source_id;
ALTER TABLE patient_account_study_permissions DROP COLUMN source_type, DROP COLUMN source_id;

ALTER TABLE dicoms DROP COLUMN source_type, DROP COLUMN source_id;
ALTER TABLE study_instance_upload_status DROP COLUMN source_type, DROP COLUMN source_id;

ALTER TABLE unique_instance_index DROP COLUMN source_type, DROP COLUMN source_id;
ALTER TABLE unique_report_index DROP COLUMN source_type, DROP COLUMN source_id;
ALTER TABLE unique_study_index DROP COLUMN source_type, DROP COLUMN source_id;

ALTER TABLE enrollments DROP COLUMN application_entity_id;
