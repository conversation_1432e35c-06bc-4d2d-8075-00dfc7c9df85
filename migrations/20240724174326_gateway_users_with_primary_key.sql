-- +goose Up
CREATE TABLE gateway_users_new_20240723 (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  user_type VARCHAR(4) NOT NULL DEFAULT 'gw',
  user_name VARCHAR(64) NOT NULL,
  pw_hash CHAR(64) NOT NULL,
  enabled TINYINT(1) NOT NULL DEFAULT 1,
  user_id INT NULL,
  org_id BIGINT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_user_name ON gateway_users_new_20240723 (user_name);
CREATE INDEX idx_user_name_enabled ON gateway_users_new_20240723 (user_name, enabled);

-- +goose Down
DROP INDEX idx_user_name ON gateway_users_new_20240723;
DROP INDEX idx_user_name_enabled ON gateway_users_new_20240723;

DROP TABLE gateway_users_new_20240723;
