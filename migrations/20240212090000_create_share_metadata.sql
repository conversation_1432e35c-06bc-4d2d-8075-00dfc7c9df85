-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `share_metadata` (
    share_id VARCHAR(100) PRIMARY KEY,
    exam_count INT DEFAULT 0,
    hrs_count INT DEFAULT 0,
    patient_name VARCHAR(64),
    patient_dob CHAR(8),
    legacy_provider_id BIGINT,
    org_name TEXT,
    ph_patient_id CHAR(28),
    share_type VARCHAR(10),
    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_patient (patient_name, ph_patient_id),
    INDEX idx_legacy_provider (legacy_provider_id, patient_dob, patient_name)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `share_metadata`;
-- +goose StatementEnd
