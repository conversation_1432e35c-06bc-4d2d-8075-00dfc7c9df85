-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `v3transfer_status` (
  `transfer_id` varchar(255) NOT NULL,
  `part_name` varchar(255) NOT NULL,
  `object_id` varchar(255) DEFAULT NULL,
  `study_id` varchar(255) NOT NULL,
  `series_id` varchar(255) NOT NULL,
  `instance_id` varchar(255) DEFAULT NULL,
  `sha1` varchar(28) DEFAULT NULL,
  `size` bigint(20) default 0,
  `status` varchar(255) NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transfer_id`,`part_name`),
  UNIQUE KEY `v3transfer_status_objectid` (`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS v3transfer_status;
-- +goose StatementEnd