-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `hl7_events` (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    `provider_id` BIGINT NOT NULL COMMENT "legacy provider ID, also known as org_id",
    `message` JSON NOT NULL COMMENT "standardized HL7 JSON payload",
    `created_timestamp_utc` DATETIME NOT NULL DEFAULT (UTC_TIMESTAMP) COMMENT "Using UTC_TIMESTAMP instead of CURRENT_TIMESTAMP until all servers use UTC timezone"
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS hl7_events;
-- +goose StatementEnd
