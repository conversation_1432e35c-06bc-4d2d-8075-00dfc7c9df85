-- +goose Up
-- +goose StatementBegin
CREATE VIEW appointment_reminder_follow_up_overview AS
SELECT
    arf.*,
    ar.mobile,
    ar.org_id,
    ar.consent_id,
    aci.name AS clinic_name,
    aci.address AS clinic_address
FROM
    appointment_reminder_follow_ups arf
LEFT JOIN
    appointment_reminders ar ON arf.reminder_id = ar.reminder_id
LEFT JOIN
    appointment_clinic_info aci ON ar.clinic_info_id = aci.id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP view appointment_reminder_follow_up_overview;
-- +goose StatementEnd
