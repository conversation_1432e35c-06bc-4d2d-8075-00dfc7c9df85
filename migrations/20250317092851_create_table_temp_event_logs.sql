-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `temp_audit_event_logs` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `inserted_at` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT 'time of data insertion',
  `produced_at` TIMESTAMP(6) NOT NULL COMMENT 'time of log creation as UNIX timestamp in milliseconds',
  `service` ENUM(
    'accountservice',
    'coreapi',
    'providersservice', 
    'recordservice'
  ) NOT NULL COMMENT 'service that triggered the log',
  `client_id` VARBINARY(27) NOT NULL COMMENT 'ksuid of client during its lifespan',
  `message_id` INT UNSIGNED NOT NULL COMMENT 'id of message sent by this client',
  `subject` VARCHAR(50) NOT NULL COMMENT 'subject that triggered workflow for the log',
  `correlation_id` VARBINARY(27) NOT NULL COMMENT 'id of workflow that triggered event',
  `source_ip` VARCHAR(50) NOT NULL COMMENT 'ip address of incoming request that triggered this workflow',
  `event_type` ENUM(
    'PHYSICIANHUB_STUDY_VIEW', 
    'PHYSICIANHUB_REPORT_VIEW', 
    'PHYSICIANHUB_PACS_SEARCH', 
    'PHYSICIANHUB_STUDY_RETRIEVE', 
    'PHYSICIANHUB_STUDY_AUTOROUTE'
  ) NOT NULL COMMENT 'type of event',
  `event_data` json NOT NULL COMMENT 'additional log data, specific to event_type',
  INDEX idx_timestamp (`produced_at`, `message_id`, `client_id`),
  INDEX idx_event_type (`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `temp_audit_event_logs`;
-- +goose StatementEnd

