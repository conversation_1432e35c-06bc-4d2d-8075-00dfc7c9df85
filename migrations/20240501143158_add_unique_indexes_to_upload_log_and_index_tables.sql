-- +goose Up
-- +goose StatementBegin
ALTER TABLE pockethealth.unique_study_index ADD CONSTRAINT unique_key UNIQUE (provider_id, study_uid);
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.unique_instance_index ADD CONSTRAINT unique_key UNIQUE (provider_id, instance_uid);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE pockethealth.unique_study_index DROP CONSTRAINT unique_key;
-- +goose StatementEnd
-- +goose StatementBegin
ALTER TABLE pockethealth.unique_instance_index DROP CONSTRAINT unique_key;
-- +goose StatementEnd
