-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS study_replication_workflows (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `provider_id` BIGINT NOT NULL,
	`name` VARCHAR(200) NOT NULL,
    `status` VARCHAR(64) NOT NULL,
    `created_at` TIMESTAMP NOT NULL,

    `started_at` TIMESTAMP,
    `ended_at` TIMESTAMP,

    `target_study_window_start` TIMESTAMP NOT NULL,
    `target_study_window_end` TIMESTAMP,

    `batch_window_checkpoint` TIMESTAMP NOT NULL,
    `batch_window_length_ms` BIGINT NOT NULL,
    `batch_size` INT NOT NULL,

    `poll_frequency_ms` INT NOT NULL,

	INDEX (`provider_id`, `name`),
    UNIQUE (`provider_id`, `name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS study_replication_workflows;
-- +goose StatementEnd
