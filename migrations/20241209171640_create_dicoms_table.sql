-- This SQL migration was generated by the generate_dicom_tag_tables script
-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `dicoms` (
`id` CHAR(27) NOT NULL UNIQUE PRIMARY KEY COMMENT "ksuid",
`provider_id` BIGINT NOT NULL COMMENT "legacy provider ID, also known as org_id",
`tags` JSON NOT NULL COMMENT "human readable dicom json tags",
`StudyInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.StudyInstanceUID') STORED NOT NULL COMMENT "(0020,000d)",
`SeriesInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SeriesInstanceUID') STORED NOT NULL COMMENT "(0020,000e)",
`SOPInstanceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SOPInstanceUID') STORED NOT NULL COMMENT "(0008,0018)",
`SOP<PERSON>lassUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SOPClassUID') STORED NULL COMMENT "(0008,0016)",
`TransferSyntaxUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.TransferSyntaxUID') STORED NULL COMMENT "0002,0010)",
`IssuerOfPatientID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.IssuerOfPatientID') STORED NULL COMMENT "(0010,0021)",
`PatientID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.PatientID') STORED NULL COMMENT "(0010,0020)",
`PatientName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.PatientName') STORED NULL COMMENT "(0010,0010)",
`PatientBirthDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.PatientBirthDate') STORED NULL COMMENT "(0010,0030)",
`PatientSex` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.PatientSex') STORED NULL COMMENT "(0010,0040)",
`AccessionNumber` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.AccessionNumber') STORED NULL COMMENT "(0008,0050)",
`StudyID` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.StudyID') STORED NULL COMMENT "(0020,0010)",
`StudyDescription` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.StudyDescription') STORED NULL COMMENT "(0008,1030)",
`StudyDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.StudyDate') STORED NULL COMMENT "(0008,0020)",
`StudyTime` TIME GENERATED ALWAYS AS (tags->>'$.StudyTime') STORED NULL COMMENT "(0008,0030)",
`SeriesDescription` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SeriesDescription') STORED NULL COMMENT "(0008,103e)",
`SeriesDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.SeriesDate') STORED NULL COMMENT "(0008,0021)",
`SeriesTime` TIME GENERATED ALWAYS AS (tags->>'$.SeriesTime') STORED NULL COMMENT "(0008,0031)",
`SeriesNumber` INT GENERATED ALWAYS AS (tags->>'$.SeriesNumber') STORED NULL COMMENT "(0020,0011)",
`NumberOfStudyRelatedSeries` INT GENERATED ALWAYS AS (tags->>'$.NumberOfStudyRelatedSeries') STORED NULL COMMENT "(0020,1206)",
`NumberOfStudyRelatedInstances` INT GENERATED ALWAYS AS (tags->>'$.NumberOfStudyRelatedInstances') STORED NULL COMMENT "(0020,1208)",
`NumberOfSeriesRelatedInstances` INT GENERATED ALWAYS AS (tags->>'$.NumberOfSeriesRelatedInstances') STORED NULL COMMENT "(0020,1209)",
`Modality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.Modality') STORED NULL COMMENT "(0008,0060)",
`ProtocolName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.ProtocolName') STORED NULL COMMENT "(0018,1030)",
`InstanceNumber` INT GENERATED ALWAYS AS (tags->>'$.InstanceNumber') STORED NULL COMMENT "(0020,0013)",
`ReferringPhysicianName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.ReferringPhysicianName') STORED NULL COMMENT "(0008,0090)",
`PatientTelephoneNumbers` JSON GENERATED ALWAYS AS (tags->>'$.PatientTelephoneNumbers') STORED NULL COMMENT "(0010,2154)",
`BodyPartExamined` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.BodyPartExamined') STORED NULL COMMENT "(0018,0015)",
`Laterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.Laterality') STORED NULL COMMENT "(0020,0060)",
`ContentDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.ContentDate') STORED NULL COMMENT "(0008,0023)",
`ContentTime` TIME GENERATED ALWAYS AS (tags->>'$.ContentTime') STORED NULL COMMENT "(0008,0033)",
`AcquisitionDate` CHAR(8) GENERATED ALWAYS AS (tags->>'$.AcquisitionDate') STORED NULL COMMENT "(0008,0022)",
`AcquisitionTime` TIME GENERATED ALWAYS AS (tags->>'$.AcquisitionTime') STORED NULL COMMENT "(0008,0032)",
`ImageType` JSON GENERATED ALWAYS AS (tags->>'$.ImageType') STORED NULL COMMENT "(0008,0008)",
`ViewPosition` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.ViewPosition') STORED NULL COMMENT "(0018,5101)",
`NumberOfFrames` INT GENERATED ALWAYS AS (tags->>'$.NumberOfFrames') STORED NULL COMMENT "(0028,0008)",
`Rows` SMALLINT GENERATED ALWAYS AS (tags->>'$.Rows') STORED NULL COMMENT "(0028,0010)",
`Columns` SMALLINT GENERATED ALWAYS AS (tags->>'$.Columns') STORED NULL COMMENT "(0028,0011)",
`PixelSpacing` JSON GENERATED ALWAYS AS (tags->>'$.PixelSpacing') STORED NULL COMMENT "(0028,0030)",
`ImageLaterality` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.ImageLaterality') STORED NULL COMMENT "(0020,0062)",
`ImagePositionPatient` JSON GENERATED ALWAYS AS (tags->>'$.ImagePositionPatient') STORED NULL COMMENT "(0020,0032)",
`ImageOrientationPatient` JSON GENERATED ALWAYS AS (tags->>'$.ImageOrientationPatient') STORED NULL COMMENT "(0020,0037)",
`FrameOfReferenceUID` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.FrameOfReferenceUID') STORED NULL COMMENT "(0020,0052)",
`ConceptNameCodeSequence` TEXT GENERATED ALWAYS AS (tags->>'$.ConceptNameCodeSequence') STORED NULL COMMENT "(0040,a043)",
`CompletionFlag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.CompletionFlag') STORED NULL COMMENT "(0040,a491)",
`VerificationFlag` VARCHAR(16) GENERATED ALWAYS AS (tags->>'$.VerificationFlag') STORED NULL COMMENT "(0040,a493)",
`InstitutionName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.InstitutionName') STORED NULL COMMENT "(0008,0080)",
`Manufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.Manufacturer') STORED NULL COMMENT "(0008,0070)",
`SecondaryCaptureDeviceManufacturer` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SecondaryCaptureDeviceManufacturer') STORED NULL COMMENT "(0018,1016)",
`SecondaryCaptureDeviceManufacturerModelName` VARCHAR(64) GENERATED ALWAYS AS (tags->>'$.SecondaryCaptureDeviceManufacturerModelName') STORED NULL COMMENT "(0018,1018)",
INDEX idx_provider_study (provider_id, StudyInstanceUID),
INDEX idx_provider_series (provider_id, SeriesInstanceUID),
UNIQUE INDEX idx_provider_study_instance (provider_id, StudyInstanceUID, SOPInstanceUID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `dicoms`;
-- +goose StatementEnd