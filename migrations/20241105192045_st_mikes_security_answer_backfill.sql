-- +goose Up
-- +goose StatementBegin
update pockethealth.scans s
 join pockethealth.exams e on s.scan_id=e.transfer_id
 set s.patient_id = e.dob
 where s.origin_id=18987 and e.activated = 0
 and s.uploaded <= "2024-10-29 16:05:00"
 and s.source="PS"
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
update pockethealth.scans s
 join pockethealth.exams e on s.scan_id=e.transfer_id
 join accounts.accounts a on a.id=e.account_id
 join pockethealth.requests r on r.account_id=a.id
 set s.patient_id = RIGHT(r.ohip, 4)
 where s.origin_id=18987 and e.activated = 0
 and s.uploaded <= "2024-10-29 16:05:00"
 and s.source="PS"
-- +goose StatementEnd