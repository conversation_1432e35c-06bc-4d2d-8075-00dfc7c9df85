-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `pockethealth`.`priority_lookup_accession` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `enrollment_id` INT NOT NULL,
    `account_id` CHAR(28) NOT NULL,
    `accession` VARCHAR(64) NOT NULL,
    `requested_timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `completed_timestamp` TIMESTAMP NULL,
    UNIQUE `unique_account_enrollment_accession` (`account_id`, `enrollment_id`, `accession`),
    INDEX `idx_multi` (`account_id`, `accession`, `completed_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `pockethealth`.`priority_lookup_accession`;
-- +goose StatementEnd
