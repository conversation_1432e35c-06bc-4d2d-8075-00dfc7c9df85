-- +goose Up
CREATE TABLE `v2transfer_status2` (
  `transfer_id` varchar(255) NOT NULL,
  `part_name` varchar(255) NOT NULL,
  `object_id` varchar(255) DEFAULT NULL,
  `study_id` varchar(255) NOT NULL,
  `series_id` varchar(255) NOT NULL,
  `instance_id` varchar(255) DEFAULT NULL,
  `sha1` varchar(28) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transfer_id`,`part_name`),
  UNIQUE KEY `v2transfer_status_objectid` (`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE UNIQUE INDEX v2transfer_status2_objectid
ON v2transfer_status2(object_id);

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS v2transfer_status2;
-- +goose StatementEnd
