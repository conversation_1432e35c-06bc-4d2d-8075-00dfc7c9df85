package main

//go:generate go run github.com/ogen-go/ogen/cmd/ogen@v1.6.0 --clean --target generated/api --package api --config api/ogen.yaml api/openAPI3spec.yaml
//go:generate go run github.com/ogen-go/ogen/cmd/ogen@v1.6.0 --clean --target generated/services/recordservice --package recordservice --config pkg/services/ogen.yaml pkg/services/recordservice/openapi.yaml
//go:generate go run go.uber.org/mock/mockgen@v0.5.0 -source=pkg/services/orgs/orgservice.go -destination=pkg/services/orgs/mock/orgservice_mock.go -package=mock_orgservice
//go:generate go run go.uber.org/mock/mockgen@v0.5.0 -source=pkg/services/plans/plans.go -destination=pkg/services/plans/mock/planservice_mock.go -package=mock_planservice
//go:generate go run go.uber.org/mock/mockgen@v0.5.0 -source=pkg/services/accountservice/accountservice.go -destination=pkg/services/accountservice/mock/accountservice_mock.go -package=mock_accountservice

// Mock after re-generating the APIs (mocking requires the types!)
//go:generate go run github.com/vektra/mockery/v2@v2.52.2
