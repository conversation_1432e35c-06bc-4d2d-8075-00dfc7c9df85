# Email Translation Guide
People with Lokalise access: <PERSON><PERSON><PERSON> <PERSON> <PERSON>
## For Engineers
Whenever you add commits that create or modify a template or subject line, notify someone with Lokalise access so they can update the translations.
### Adding Email templates
Add new email templates to the `tmpl/locale/en` directory. Don't make copies for the other languages; those will be exported from Lokalise once the email has been translated. Our services will fall back to the English version until the translations are ready.

### Modifying templates with existing translations
When modifying existing templates, make your changes to the `tmpl/locale/en` version, then determine whether or not the now out-of-date alternate language versions of the template can still be used.
1. Is it possible to continue sending the old versions? If so leave the other templates as-is. If necessary, add code to detect parser errors and retry with the old fields.
1. Is it not possible to support the old version, and the merge can wait until translations are available? Send the updated English template to someone with Lokalise access, then wait for the translated templates and include them in your commit.
1. Is it not possible to support the old version, and the merge can't wait? Delete all alternate language versions of that template. Our language code will detect the missing translations and fall back to using the `tmpl/locale/en` version until the translations are added.

Option 1 is preferred in almost all cases. Option 3 is bad for user experience and should be avoided whenever possible.
### Subject lines
Subject lines should be written in code in the form of LocalizeConfig objects. Refer to existing code or our [language README](https://gitlab.com/pockethealth/phutils/-/blob/master/pkg/language/README.md) for examples. Existing subject lines can be modified in-place. Do not modify the `locales/*.json` files.
### Writing code to send an email
The steps to sending a translated email are:
1. Determine which language to use.
1. Parse the template corresponding to that language.
1. Replace the subject line with its translated counterpart.

The [phutils language package](https://gitlab.com/pockethealth/phutils/-/tree/master/pkg/language) provides tools to simplify all 3 steps. Refer to its [README](https://gitlab.com/pockethealth/phutils/-/blob/master/pkg/language/README.md) for a detailed usage guide.

## For translation managers

### Subject lines
The steps to translating subject lines are:
1. Extract the latest subject lines from code.
1. Import to Lokalise
1. Translate
1. Export from Lokalise
#### Extracting
Run `make i18n_extract`. It will generate a new `active.en.json` and `translate.es-US.json`. 

#### Importing
Upload `locales/translate.es-US.json` to Lokalise (set source language to English). Lokalise will create new translation units from the file. You can delete `translate.es-US.json` and `active.en.json` after the upload.

#### Exporting
Download the Spanish messages in JSON format as `active.es-US.json`. Commit `active.es-US.json` to source control.

### Emails
Email templates can be uploaded directly to Lokalise.

#### Importing
Upload `tmpl/locale/en/*.html` files to Lokalise.

#### Exporting
After translation is finished, download the files in HTML format and save in `tmpl/locale/es-US`.

Email templates require special attention after export. If there are any `{` or `}` characters inside HTML attributes Lokalise tends to replace them with `%7B` and `%7D` during the export. Change any `%7B` to `{` and `%7D` to `}` before committing the files.