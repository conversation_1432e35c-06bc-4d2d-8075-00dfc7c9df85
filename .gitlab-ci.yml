include:
  - template: "Workflows/MergeRequest-Pipelines.gitlab-ci.yml"

  - local: .gitlab/ci_templates/deploy-test-env.yaml
    inputs:
      namespace: coreapi
      identity-name: "qa-coreapi-podid"
      federated-credential-prefix: "fid-coreapi"
      region: cacentral
      cluster-name: qa-aks-2
      cluster-resource-group: pockethealth_test
      ingress-url: core-$CI_COMMIT_SHORT_SHA.cactrl.qa.pocket.health
      service-account-name: sa-coreapi

  - local: .gitlab/ci_templates/deploy-qa.yaml
  - local: .gitlab/ci_templates/deploy-prod.yaml
    inputs:
      namespace: coreapi
      region: cacentral
      cluster-name: prod-aks-2
      cluster-resource-group: pockethealth_backend_prod
      db-hostname: ph-sql-main3-cactrl-prod.mysql.database.azure.com
      ci-runner-tags: envprod
      url: https://core.pocket.health/ping

  - local: .gitlab/ci_templates/deploy-prod.yaml
    inputs:
      namespace: coreapi
      region: uswest
      cluster-name: aks-backend-prod-uswest
      cluster-resource-group: rg-phbackend-prod-uswest
      db-hostname: ph-sql-main1-prod-uswest.mysql.database.azure.com
      ci-runner-tags: envprod-uswest
      url: https://core.uswest.prod.pocket.health/ping

  - project: pockethealth/gitlab-templates
    ref: main
    file: jobs/not-dependabot.yaml

  - project: pockethealth/gitlab-templates
    ref: main
    file: jobs/push-to-acr.yaml
    inputs:
      image-name: coreapi
      image-target: ca
      image-placeholder: phcrcacentral0.azurecr.io/coreapi@__DOCKER_DIGEST_PLACEHOLDER__

  - local: .gitlab/ci_templates/s2sauth-test-setup.yaml

stages:
  - preimage
  - upload
  - deploy
  - teardown

build_dist:
  tags:
    - k8s-infra-dm
  stage: preimage
  # to the next person to attempt the bookworm upgrade: you'll have to resolve dcmtk not enjoying the version of libtiff that bookworm has, somehow.
  image: "phcrcacentral0.azurecr.io/golang:1.24-bullseye"
  before_script:
    # --- jfrog xray scan ---
    # Download JFrog CLI
    - curl -fL https://install-cli.jfrog.io | sh
    # configure artifactory server
    - jf config add ph-jfrog --url="https://phtest.jfrog.io/" --access-token=$JFROG_TOKEN
    - jf config show
    - export PATH=$PATH:$(go env GOPATH)/bin
  script:
    #lokalise has a bug when exporting html templates that escapes {{}} (which we need for variable substitution) with %7B%7B%7D%7D. Until they fix, use this job to keep an eye on it.
    - cd tmpl/locale
    - sh -c 'ERRS=0;for folder in $(ls);do for file in $(ls $folder);do echo "Checking $folder/$file" && (cat $folder/$file | grep '%7B%7B.*%7D%7D');if [ "$?" -eq "0" ];then echo "found escaped substitution var" && ERRS=1;fi;done;done;exit $ERRS'
    - cd ../../
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
    # npm used for redocly
    - apt-get update -qq
    - apt-get install npm -qq
    # redocly/cli is used to bundle the decomposed openapi files into the single-file generated/spec/openapi.yaml
    - npm install --silent @redocly/cli -g
    - make build
    - make dist
    - echo -n "" > dist/build_info.txt
    - echo "Git commit = $CI_COMMIT_SHA" >> dist/build_info.txt
    - echo "Git branch = $CI_COMMIT_BRANCH" >> dist/build_info.txt
    - echo "Git tag = $CI_COMMIT_TAG" >> dist/build_info.txt
    - echo "GitlabCI pipeline ID = $CI_PIPELINE_ID" >> dist/build_info.txt
    # scan go deps -- if scan fails, don't fail build job yet because
    # we still want docker image scan in the next CICD job to run.
    # save a fail to indicate this failure, so it can be checked later
    - jf audit . --go --watches=backend-std-watch --fail=false || ([ $? -eq 3 ] && { echo -e "\e[31m!! WARNING !!! Found vulnerabilities in packages that fail the security policy.  The CICD job will fail after Docker image scan.\e[0m" ; touch _go_deps_sec_failed  ; })
  retry:
    max: 2
    when:
      - runner_system_failure #tells the job to retry - adding to avoid docker daemon connect errors
      - script_failure
  artifacts:
    paths:
      - dist
      - deployments

generated_files_diff:
  tags:
    - k8s-infra-dm
  stage: preimage
  image: "phcrcacentral0.azurecr.io/golang:1.24-bookworm"
  before_script:
    - make clean-gen
    - go install github.com/golang/mock/mockgen@latest
    - export PATH=$PATH:$(go env GOPATH)/bin
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
  script:
    # npm used for redocly
    - apt-get update -qq
    - apt-get install nodejs npm -qq
    # redocly/cli is used to bundle the decomposed openapi files into the single-file generated/spec/openapi.yaml
    - npm install --silent @redocly/cli@1.29.0 -g
    - npm version
    - make generate
    - |
      uncommitted_files=$(git status --porcelain)
      if [ -n "$uncommitted_files" ]; then
        echo "Uncommitted generated files detected:"
        echo "$uncommitted_files"
        exit 1
      else
        echo "No uncommitted generated files found."
      fi

unit_integ_test:
  rules:
    - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
      when: never
    - when: always
  tags:
    - k8s-infra-dm
  needs:
    - s2sauth-test-setup
  dependencies:
    - s2sauth-test-setup
  stage: preimage
  image: "phcrcacentral0.azurecr.io/golang:1.24-bookworm"
  before_script:
    - apt-get update && apt-get install -y xpdf #for report conversion tests
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
    - "curl -sfL https://raw.githubusercontent.com/securego/gosec/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.14.0"
    - go install github.com/jstemmer/go-junit-report/v2@latest
    # colourize test output
    - go install github.com/rakyll/gotest@latest
    - export PATH="$PATH:$CI_PROJECT_DIR/testbin"
    # get kubelogin executable from s2sauth-test-setup artifact
    - cp $CI_PROJECT_DIR/kubelogin /usr/local/bin/kubelogin
  script:
    - make integ PH_IT_TEST_ENV=qa 2>&1 | tee output.txt
    - cat output.txt | go-junit-report -set-exit-code > report.xml
    # code coverage report
    - go get github.com/boumenot/gocover-cobertura
    - GOFLAGS="-tags=integration" go run github.com/boumenot/gocover-cobertura < coverage/integration_unit_filtered.cov > integration_unit.xml
    # code coverage percentage
    - go tool cover -func coverage/integration_unit_filtered.cov
  coverage: '/total:\s+\(statements\)\s+(\d+(?:\.\d+)?%)/'
  retry:
    max: 2
    when:
      - runner_system_failure #tells the job to retry - adding to avoid docker daemon connect errors
      - script_failure
  artifacts:
    reports:
      junit: report.xml
      coverage_report:
        coverage_format: cobertura
        path: integration_unit.xml

qa_jit_apptest:
  rules:
    - if: $CI_MERGE_REQUEST_ID
  stage: deploy
  tags:
    - k8s-infra-dm
  needs:
    - deploy_qa_test
  dependencies:
    - deploy_qa_test
  image: "phcrcacentral0.azurecr.io/golang:1.24-bookworm"
  variables:
    API_HOST: "core-$CI_COMMIT_SHORT_SHA.cactrl.qa.pocket.health"
  before_script:
    - apt-get update && apt-get install -y unzip
  script:
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
    - go install github.com/jstemmer/go-junit-report/v2@latest
    - cd apptest && make PH_TEST_ENV=qa 2>&1 | tee output.txt
    - cat output.txt | go-junit-report -set-exit-code > report.xml
  retry:
    max: 2
    when:
      - runner_system_failure
  artifacts:
    when: always
    paths:
      - apptest
    reports:
      junit: ./apptest/report.xml

qa_env_apptest:
  rules:
    - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
      when: always
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: always
  stage: deploy
  environment:
    name: "QA"
    action: verify
  tags:
    - k8s-infra-dm
  needs:
    - qa_env_deploy
  dependencies:
    - qa_env_deploy
  image: "phcrcacentral0.azurecr.io/golang:1.24-bookworm"
  before_script:
    - apt-get update && apt-get install -y unzip
  script:
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
    - go install github.com/jstemmer/go-junit-report/v2@latest
    - cd apptest && make PH_TEST_ENV=qa 2>&1 | tee output.txt
    - cat output.txt | go-junit-report -set-exit-code > report.xml
  retry:
    max: 2
    when:
      - runner_system_failure
  artifacts:
    when: always
    paths:
      - apptest
    reports:
      junit: ./apptest/report.xml
